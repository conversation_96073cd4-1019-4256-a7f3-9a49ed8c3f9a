variables:
  # 项目部署唯一标识
  APP_NAME: j2
  # 项目sonar名称
  SONAR_NAME: j2


include:
  - project: 'devops/argo-ci'
    ref: master
    file: 'templates/.gitlab-********************maven.yml'
  - project: 'devops/argo-ci'
    ref: master
    file: 'templates/java/gitlab-ci-template-java.yml'

build-online:
  before_script:
    - cp ~/argo-deployment/tools/common-configmap/bases/opentelemetry/opentelemetry-javaagent.jar .

build-test:
  before_script:
    - cp ~/argo-deployment/tools/common-configmap/bases/opentelemetry/opentelemetry-javaagent.jar .