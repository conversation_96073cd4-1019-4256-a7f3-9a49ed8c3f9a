FROM taqu-onlinie-registry-registry-vpc.cn-zhangjiakou.cr.aliyuncs.com/taqu/jdk:latest

# 项目名称, 请自行替换
ENV APP_NAME=j2

# 根据项目修改打包
COPY target/tq-account-1.0.0.jar /data/html/${APP_NAME}.jar
COPY scripts/java_busy_threads.sh /data/html/java_busy_threads.sh

# java 启动参数, 请谨慎变更
CMD java -Dcsp.sentinel.dashboard.server=mp-sentinel-dashboard.soa.internal.taqu.cn -Dproject.name=tq-account -server -Xmx8g -Xms8g -Xmn5g -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=4096m -Xss1m -XX:+PrintGC -XX:+PrintGCDetails \
    -XX:+UnlockDiagnosticVMOptions -XX:InitiatingHeapOccupancyPercent=35 -XX:MinMetaspaceFreeRatio=50 -XX:MaxMetaspaceFreeRatio=80 \
    -XX:+PrintTenuringDistribution -XX:+PrintGCDateStamps -XX:+ExplicitGCInvokesConcurrentAndUnloadsClasses -XX:CMSInitiatingOccupancyFraction=70 \
    -XX:+UseCMSInitiatingOccupancyOnly -Xloggc:/data/logs/${POD_NAME}-gc-%p-%t.log -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -XX:+PrintHeapAtGC \
    -XX:+PrintGCApplicationConcurrentTime -XX:+PrintGCApplicationStoppedTime -XX:PrintFLSStatistics=1 -XX:-OmitStackTraceInFastThrow \
    -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs/${POD_NAME}-oom-%p-%t.hprof -XX:CICompilerCount=3 \
    -Djava.security.egd=file:/dev/urandom -jar /data/html/${APP_NAME}.jar \
    --server.port=8080 --spring.profiles.active=prod --cli --server.address=0.0.0.0
