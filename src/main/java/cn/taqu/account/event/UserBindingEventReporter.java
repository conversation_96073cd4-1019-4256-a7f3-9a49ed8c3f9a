package cn.taqu.account.event;

import cn.taqu.account.dto.UserBindingEventDTO;
import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Producer;
import org.springframework.stereotype.Service;

import static cn.taqu.account.event.EventConst.ACCOUNT_BEHAVIOR_PULSAR_TOPIC;
import static cn.taqu.account.event.EventConst.USER_BINDING_EVENT_PULSAR_TOPIC;

/**
 * <AUTHOR>
 * @date 2025/1/3 下午2:58
 */
@Slf4j
@Service
public class UserBindingEventReporter implements PulsarFactory.ConfigListener {

    public volatile Producer<String> producer;

    public UserBindingEventReporter(PulsarFactory factory) {
        this.producer = factory.producer(USER_BINDING_EVENT_PULSAR_TOPIC);
        factory.addListener(this);
    }

    @Override
    public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
        Producer<String> origin = producer;
        this.producer = factory.producer(USER_BINDING_EVENT_PULSAR_TOPIC);
        origin.closeAsync();
    }

    public void report(String accountUuid, UserBindingEventDTO event) {
        try {
            producer.newMessage()
                .key(accountUuid)
                .eventTime(System.currentTimeMillis())
                .value(JsonUtils.objectToString(event))
                .sendAsync().whenComplete((id, e) -> {
                    if (e != null) {
                        log.warn("Pulsar推送失败，topic = " + USER_BINDING_EVENT_PULSAR_TOPIC, e);
                    } else {
                        log.info("Pulsar推送{}成功 {}", USER_BINDING_EVENT_PULSAR_TOPIC, accountUuid);
                    }
                });
        } catch (Exception e) {
            log.warn("Pulsar推送失败，topic = " + USER_BINDING_EVENT_PULSAR_TOPIC, e);
        }
    }
}
