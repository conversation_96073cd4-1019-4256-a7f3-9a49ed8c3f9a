package cn.taqu.account.event;

import cn.taqu.account.etcd.PulsarFactory;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.unmarshaller.JsonUnmashaller;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Producer;
import org.springframework.stereotype.Service;

import java.util.Map;

import static cn.taqu.account.constant.CommConst.AVATAR_HOST;
import static cn.taqu.account.constant.CommConst.HTTP;
import static cn.taqu.account.event.EventConst.AVATAR_GUIDE_CYCLE_TOPIC;

/**
 * 头像指导巡检
 *
 * <AUTHOR>
 * @date 2025/1/3 下午2:58
 */
@Slf4j
@Service
public class AvatarGuideCycleReporter implements PulsarFactory.ConfigListener {

    public volatile Producer<String> producer;

    public AvatarGuideCycleReporter(PulsarFactory factory) {
        this.producer = factory.producer(EventConst.resolveTopic(AVATAR_GUIDE_CYCLE_TOPIC));
        factory.addListener(this);
    }

    @Override
    public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
        Producer<String> origin = producer;
        this.producer = factory.producer(EventConst.resolveTopic(AVATAR_GUIDE_CYCLE_TOPIC));
        origin.closeAsync();
    }

    public void report(String accountUuid, String url) {
        if (!url.startsWith(HTTP)) {
            url = AVATAR_HOST + url;
        }

        Map<String, String> data = Maps.newHashMap();
        data.put("uuid", accountUuid);
        data.put("url", url);

        try {
            String val = JSON.toJSONString(data);
            producer.newMessage()
                    .key(accountUuid)
                    .eventTime(System.currentTimeMillis())
                    .value(val)
                    .sendAsync().whenComplete((id, e) -> {
                        log.info("Pulsar推送, topic = {}, val:{}", EventConst.resolveTopic(AVATAR_GUIDE_CYCLE_TOPIC), val);
                        if (e != null) {
                            log.warn("Pulsar推送失败，topic = {}, val:{}", EventConst.resolveTopic(AVATAR_GUIDE_CYCLE_TOPIC), val, e);
                        }
                    });
        } catch (Exception e) {
            log.warn("Pulsar推送失败，topic = " + EventConst.resolveTopic(AVATAR_GUIDE_CYCLE_TOPIC), e);
        }
    }
}
