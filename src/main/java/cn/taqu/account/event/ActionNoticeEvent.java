package cn.taqu.account.event;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 行为通知事件
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class ActionNoticeEvent {

    /**
     * 用户uuid
     */
    private String uuid;

    /**
     * 用户uuid
     */
    private Integer gender;

    /**
     * 行为枚举
     *
     * @see cn.taqu.account.constant.ActionEventEnum
     */
    private String action;

}
