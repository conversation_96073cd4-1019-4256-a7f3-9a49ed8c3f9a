package cn.taqu.account.event;

import cn.taqu.account.utils.EnvUtil;

/**
 * <AUTHOR> Wu.D.J
 */
public interface EventConst {

    /**
     * 文档：https://o15vj1m4ie.feishu.cn/wiki/QtZNwekuriG5Q4ks7nHcmDTUnqc
     * 用户系统推数据变更推kafka，需要的业务自己订阅
     */
    String EVENT_TRACKING_TOPIC = "j2_account_behavior_tracking";

    /**
     * 行为事件队列
     *
     * 文档：https://o15vj1m4ie.feishu.cn/wiki/PMewwxt6wi4bhZkk71ec1VDQnPg
     * php需要消费
     */
    String EVENT_ACTION_NOTICE_TOPIC = "mp_account_action_notice";

    String ADMP_USER_REG_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/admp_user_register";

    String ACCOUNT_BEHAVIOR_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/account_behavior_tracking";

    String USER_BINDING_EVENT_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/user_binding_event";

    String EVENT_NOTIFY_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/mp_taqu_event_notify";

    String ACCOUNT_AVATAR_EVENT_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/account_avatar_event";

    String ACCOUNT_PHOTO_AUDIT_EVENT_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/account_photo_audit_event";

    String ACCOUNT_PHOTO_NUM_EVENT_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/account_photo_num_event";

    String ACCOUNT_MANUFACTURER_CALL_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/account_manufacturer_call";

    String ACCOUNT_USER_REAL_AVATAR_UPDATE = "persistent://middle_tenant/taqu_ns/j2_user_real_avatar_update";

    String AVATAR_GUIDE_CYCLE_TOPIC = "persistent://middle_tenant/mp_ns/j2_avatar_guide_cycle";

    static String resolveTopic(String topic) {
        if (EnvUtil.isGray()) {
            topic += "_gray";
        }
        return topic;
    }

}
