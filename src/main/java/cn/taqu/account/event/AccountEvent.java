package cn.taqu.account.event;

/**
 * <AUTHOR> Wu.D.J
 */
public enum AccountEvent {

    /**
     * 实名认证
     */
    REAL_NAME_CERT,

    /**
     * 真人认证(审核前)
     */
    REAL_PERSON_CERT,
    
    /**
     * 真人认证(审核后) 只要上传头像，真人比对通过且审核通过后都会推送
     */
    REAL_PERSON_CERT_COMPLETE,

    /**
     * 真人活体认证
     */
    LIVE_FACE_CERT,

    /**
     * 上传头像
     */
    AVATAR_UPLOAD,

    /**
     * 更新基础资料
     */
    BASE_INFO_UPDATE,
}
