package cn.taqu.account.event;

import cn.taqu.account.constant.ActionEventEnum;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.utils.KafkaSinkUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 行为上报
 *
 * <AUTHOR>
 * @date 2025/2/26 09:55
 */
@Slf4j
@Service
public class ActionNoticeReport {

    @Resource
    private KafkaSinkUtil kafkaSinkUtil;
    @Resource
    private AccountBaseInfoManager accountBaseInfoManager;

    /**
     * 上报
     *
     * @param accountUuid
     * @param eventEnum
     */
    public void report(String accountUuid, ActionEventEnum eventEnum) {
        Integer gender = accountBaseInfoManager.getGender(accountUuid);
        kafkaSinkUtil.pushToOnlineBusinessKafka(true, EventConst.EVENT_ACTION_NOTICE_TOPIC, new ActionNoticeEvent(accountUuid, gender, eventEnum.getCode()));
    }
}
