package cn.taqu.account.event;

import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2023/6/1 5:23 PM
 */
@Slf4j
@Component
public class EventTrackReporter {
    
    @Autowired
    private KafkaTemplate<String, String> onlineKafkaTemplate;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    @Lazy
    private AccountsInfoService accountsInfoService;

    /**
     * 埋点上报
     *
     * @param accountUuid
     * @param content
     * @param dataWrapper
     * @param <T>
     */
    public <T> void report(String token, String accountUuid, String eventId, T content, BiConsumer<String, T> dataWrapper) {
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        EventTrackingServerDTO.Event<T> event = new EventTrackingServerDTO.Event<>();
        event.setEventTs(System.currentTimeMillis());
        event.setEventId(eventId);
        event.setContent(content);
        threadPoolTaskExecutor.submitListenable(() -> {
            Integer gender = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);
            if (dataWrapper != null && content != null) {
                dataWrapper.accept(accountUuid, content);
            }
            EventTrackingServerDTO dto = new EventTrackingServerDTO();
            dto.setToken(token);
            dto.setAppcode(String.valueOf(Optional.ofNullable(baseParams.getAppcode()).orElse(1)));
            dto.setCloned(String.valueOf(baseParams.getCloned()));
            dto.setGender(gender.toString());
            dto.setData(new EventTrackingServerDTO.Data(accountUuid, Collections.singletonList(event)));
            String json = JsonUtils.objectToString(dto);
            onlineKafkaTemplate.send("event_tracking_server", json);
            return json;
        }).addCallback(t -> log.info("埋点上报:{}", t), e -> log.warn("[j2]埋点上报异常", e));
    }

    public <T> void report(String accountUuid, String eventId, T content) {
        report(accountUuid, accountUuid, eventId, content, null);
    }
    
    public <T> void report(String token, String accountUuid, String eventId, T content) {
        if(StringUtils.isBlank(token)) {
            report(accountUuid, accountUuid, eventId, content, null);
        }else {
            report(token, accountUuid, eventId, content, null);
        }
    }

}
