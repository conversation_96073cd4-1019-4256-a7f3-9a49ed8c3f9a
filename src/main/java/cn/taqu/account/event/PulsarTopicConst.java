package cn.taqu.account.event;

import cn.taqu.account.utils.EnvUtil;
import org.apache.commons.lang3.BooleanUtils;

/**
 * pulsar统一收口
 *
 * <AUTHOR>
 * @date 2025/6/10 11:35
 */
public interface PulsarTopicConst {

    /**
     * 行为事件队列
     * <p>
     * 文档：https://o15vj1m4ie.feishu.cn/wiki/PMewwxt6wi4bhZkk71ec1VDQnPg
     * php需要消费
     */
    String ACTION_NOTICE_TOPIC = "persistent://middle_tenant/mp_ns/mp_account_action_notice";

    String ADMP_USER_REG_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/admp_user_register";

    String ACCOUNT_BEHAVIOR_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/account_behavior_tracking";

    String EVENT_NOTIFY_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/mp_taqu_event_notify";

    String ACCOUNT_AVATAR_EVENT_PULSAR_TOPIC = "persistent://middle_tenant/mp_ns/account_avatar_event";

    String RISK_CERTIFICATION_LOG = "persistent://middle_tenant/risk_ns/risk_certification_log";

    static String resolveTopic(String topic) {
        if (EnvUtil.isGray()) {
            topic += "_gray";
        }
        return topic;
    }

    /**
     * 测试环境隔离（使用场景：风控）
     *
     * @param topic
     * @param isolaTest
     * @return
     */
    static String resolveTopic(String topic, Boolean isolaTest) {
        if (BooleanUtils.isTrue(isolaTest)) {
            String env = EnvUtil.getEnv();
            if (env.startsWith("test")) {
                topic += "_test";
                return topic;
            }
        }

        return resolveTopic(topic);
    }

}
