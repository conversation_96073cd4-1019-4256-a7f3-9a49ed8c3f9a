package cn.taqu.account.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户标签变更
 *
 * <AUTHOR>
 * @date 2024/10/17 1:44 下午
 */
@Data
public class UserLabelEvent {

    /**
     * 标签id
     */
    @JsonProperty("tag_id")
    private Long id;

    /**
     * 标签分类
     */
    @JsonProperty("tag_type")
    private Integer type;

    /**
     * 标签内容
     */
    @JsonProperty("tag_content")
    private String content;

    /**
     * 标签操作时间
     */
    @JsonProperty("creat_time")
    private Long createTime;

    /**
     * 变更类型
     */
    @JsonProperty("change_type")
    private String changeType;

}
