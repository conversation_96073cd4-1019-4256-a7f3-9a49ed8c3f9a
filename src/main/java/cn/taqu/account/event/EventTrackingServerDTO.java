package cn.taqu.account.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 天玑埋点上报（https://o15vj1m4ie.feishu.cn/wiki/wikcnHuFwaE2vZErF5JMLYAXvdg）
 *
 * <AUTHOR>
 * @date 2023/5/15
 */
@Getter
@Setter
public class EventTrackingServerDTO {

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 应用标识
     */
    private String appcode;

    /**
     * 应用分身ID
     */
    private String cloned;

    /**
     * 性别 1:男; 2:女;
     */
    private String gender;

    /**
     * data数据
     */
    private Data data;

    /**
     * data数据
     */
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Data {

        private String uuid;

        private List<Event<?>> events;
    }

    /**
     * 埋点事件数据
     */
    @Getter
    @Setter
    public static class Event<C> {

        /**
         * 事件时间(毫秒)
         */
        @JsonProperty("event_ts")
        private Long eventTs;

        /**
         * 事件ID
         */
        @JsonProperty("event_id")
        private String eventId;

        /**
         * 事件属性
         */
        private C content;
    }

}
