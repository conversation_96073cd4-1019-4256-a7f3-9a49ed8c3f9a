package cn.taqu.account.event;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 提现绑定支付宝
 *
 * <AUTHOR>
 * 2024年11月12日下午11:39:01
 */
@Data
public class WithdrawBindAilipayEvent {

    /**
     * 绑定状态 成功/失败
     */
    @JsonProperty("status")
    private String status;

    /**
     * 失败原因
     */
    @JsonProperty("fail_reason")
    private String failReason;

    public enum Status{
        SUCCESS("成功"),
        FAIL("失败");

        /**
         * @param string
         */
        Status(String text) {
            this.text = text;
        }

        private String text;

        public String getText() {
            return text;
        }
    }

    public enum FailReason{
        /**
         * 支付宝校验超时
         */
        ALIPAY_SDK_CHECK_TIMEOUT("支付宝校验超时"),
        /**
         * 支付宝校验未通过
         */
        ALIPAY_SDK_CHECK_FAIL("支付宝校验未通过"),
        /**
         * 未绑定手机号
         */
        NO_MOBILE("未绑定手机号"),
        /**
         * 验证码校验失败
         */
        CHECK_VCODE_FAIL("验证码校验失败"),
        /**
         * 用户未实名认证
         */
        NO_ACCOUNT_CERTIFICATION("用户未实名认证"),
        /**
         * 新旧支付宝账号一致
         */
        NO_CHANGE("新旧支付宝账号一致"),
        /**
         * 风控支付宝账号限制
         */
        RISK("风控支付宝账号限制"),
        /**
         * 实名信息不一致
         */
        ALIPAY_ACCOUNT_CHECK_FAIL("实名信息不一致"),
        /**
         * 绑定失败，请稍后重试
         */
        SYSTEM_ERROR("绑定失败"),
        /**
         * 当前支付宝已绑定过其他平台实名账号
         */
        HAS_BOUND_OTHERS("该支付宝已绑定平台其它实名账号"),

        ;

        /**
         * @param string
         */
        FailReason(String text) {
            this.text = text;
        }

        private String text;

        public String getText() {
            return text;
        }
    }


}
