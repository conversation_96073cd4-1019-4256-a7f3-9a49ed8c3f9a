package cn.taqu.account.event;

import cn.taqu.account.etcd.PulsarFactory;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Producer;
import org.springframework.stereotype.Service;

import java.util.Map;

import static cn.taqu.account.event.PulsarTopicConst.RISK_CERTIFICATION_LOG;

/**
 * 用户认证相关
 *
 * <AUTHOR>
 * @date 2025/6/10 下午2:58
 */
@Slf4j
@Service
public class AccountCertEvent implements PulsarFactory.ConfigListener {

    public volatile Producer<String> producer;

    public static String TOPIC;

    static {
        TOPIC = PulsarTopicConst.resolveTopic(RISK_CERTIFICATION_LOG);
    }

    public AccountCertEvent(PulsarFactory factory) {
        this.producer = factory.producer(TOPIC);
        factory.addListener(this);
    }

    @Override
    public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
        Producer<String> origin = producer;
        this.producer = factory.producer(TOPIC);
        origin.closeAsync();
    }

    public void report(String accountUuid, Map<String, Object> event) {
        String value = JSON.toJSONString(event);
        try {
            producer.newMessage()
                    .key(accountUuid)
                    .eventTime(System.currentTimeMillis())
                    .value(value)
                    .sendAsync().whenComplete((id, e) -> {
                        if (e != null) {
                            log.warn("Pulsar推送失败，topic = {}, value:{}", TOPIC, value, e);
                        } else {
                            log.info("Pulsar推送成功，topic = {}, value:{}", TOPIC, value);
                        }
                    });
        } catch (Exception e) {
            log.warn("Pulsar推送失败，topic = {}, value:{}", TOPIC, value, e);
        }
    }
}

