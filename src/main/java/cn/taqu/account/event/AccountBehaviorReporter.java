package cn.taqu.account.event;

import cn.taqu.account.etcd.PulsarFactory;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Producer;
import org.springframework.stereotype.Service;

import static cn.taqu.account.event.EventConst.ACCOUNT_BEHAVIOR_PULSAR_TOPIC;

/**
 * <AUTHOR>
 * @date 2025/1/3 下午2:58
 */
@Slf4j
@Service
public class AccountBehaviorReporter implements PulsarFactory.ConfigListener {

    public volatile Producer<String> producer;

    public AccountBehaviorReporter(PulsarFactory factory) {
        this.producer = factory.producer(EventConst.resolveTopic(ACCOUNT_BEHAVIOR_PULSAR_TOPIC));
        factory.addListener(this);
    }

    @Override
    public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
        Producer<String> origin = producer;
        this.producer = factory.producer(EventConst.resolveTopic(ACCOUNT_BEHAVIOR_PULSAR_TOPIC));
        origin.closeAsync();
    }

    public void report(String accountUuid, EventTrackingDO tracking) {
        try {
            producer.newMessage()
                .key(accountUuid)
                .eventTime(System.currentTimeMillis())
                .value(JSON.toJSONString(tracking))
                .sendAsync().whenComplete((id, e) -> {
                    if (e != null) {
                        log.warn("Pulsar推送失败，topic = " + EventConst.resolveTopic(ACCOUNT_BEHAVIOR_PULSAR_TOPIC), e);
                    }
                });
        } catch (Exception e) {
            log.warn("Pulsar推送失败，topic = " + EventConst.resolveTopic(ACCOUNT_BEHAVIOR_PULSAR_TOPIC), e);
        }
    }
}
