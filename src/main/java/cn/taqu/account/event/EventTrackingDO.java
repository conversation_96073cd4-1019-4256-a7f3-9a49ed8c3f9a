package cn.taqu.account.event;

import java.util.Map;

import org.apache.commons.collections.MapUtils;

import com.alibaba.fastjson.annotation.JSONField;

import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.SpringContextHolder;
import lombok.Data;

/**
 * <AUTHOR> Wu.D.J
 */
@Data
public class EventTrackingDO {

    private Integer appcode;

    private Integer cloned;

    @JSONField(name = "account_uuid")
    private String accountUuid;

    private String token;

    private Integer gender;

    @J<PERSON><PERSON>ield(name = "platform_id")
    private Integer platformId;

    @JSONField(name = "event_id")
    private AccountEvent eventId;

    @JSONField(name = "event_time_sec")
    private Long eventTimeSec;

    @JSONField(name = "create_time")
    private Long createTime;

    public static EventTrackingDO create(String accountUuid, AccountEvent event) {
        SoaBaseParams param = SoaBaseParams.fromThread();

        EventTrackingDO eventTrackingDO = new EventTrackingDO();
        eventTrackingDO.setAccountUuid(accountUuid);
        eventTrackingDO.setEventId(event);
        eventTrackingDO.setAppcode(param.getAppcode());
        eventTrackingDO.setCloned(param.getCloned());
        eventTrackingDO.setToken(param.getToken());
        eventTrackingDO.setGender(param.getGender());
        eventTrackingDO.setPlatformId(param.getPlatform_id());
        eventTrackingDO.setCreateTime(getUserCreateTime(accountUuid));
        eventTrackingDO.setEventTimeSec(System.currentTimeMillis() / 1000);
        return eventTrackingDO;
    }

    public static EventTrackingDO create(String accountUuid, AccountEvent event, Integer appcode, Integer cloned) {
        SoaBaseParams param = SoaBaseParams.fromThread();

        EventTrackingDO eventTrackingDO = new EventTrackingDO();
        eventTrackingDO.setAccountUuid(accountUuid);
        eventTrackingDO.setEventId(event);
        eventTrackingDO.setAppcode(appcode);
        eventTrackingDO.setCloned(cloned);
        eventTrackingDO.setToken(param.getToken());
        eventTrackingDO.setGender(param.getGender());
        eventTrackingDO.setPlatformId(param.getPlatform_id());
        eventTrackingDO.setCreateTime(getUserCreateTime(accountUuid));
        eventTrackingDO.setEventTimeSec(System.currentTimeMillis() / 1000);
        return eventTrackingDO;
    }
    
    /**
     * 异步调用时头参数不准确，使用查询结果
     * 
     * @param accountUuid
     * @param event
     * @return
     */
    public static EventTrackingDO createWithQuery(String accountUuid, AccountEvent event) {
        Map<String, Object> userInfo = getUserInfo(accountUuid);
        EventTrackingDO eventTrackingDO = new EventTrackingDO();
        eventTrackingDO.setAccountUuid(accountUuid);
        eventTrackingDO.setEventId(event);
        eventTrackingDO.setAppcode(MapUtils.getInteger(userInfo, "login_appcode", 1));
        eventTrackingDO.setCloned(MapUtils.getInteger(userInfo, "login_cloned", 1));
        eventTrackingDO.setToken(null);
        eventTrackingDO.setGender(MapUtils.getInteger(userInfo, "sex_type"));
        eventTrackingDO.setPlatformId(MapUtils.getInteger(userInfo, "platform_id"));
        eventTrackingDO.setCreateTime(MapUtils.getLong(userInfo, "create_time"));
        eventTrackingDO.setEventTimeSec(System.currentTimeMillis() / 1000);
        return eventTrackingDO;
    }

    private static Long getUserCreateTime(String uuid) {
        Map<String, Object> uuidInfos = SpringContextHolder.getBean(AccountsInfoService.class).getInfoByUuid(
                new String[]{uuid},
                new String[]{"create_time"},
                "1", false, false).get(uuid);

        if (uuidInfos != null && uuidInfos.containsKey("create_time")) {
            return MapUtils.getLong(uuidInfos, "create_time");
        }
        return null;
    }
    
    private static Map<String, Object> getUserInfo(String uuid) {
        Map<String, Object> uuidInfos = SpringContextHolder.getBean(AccountsInfoService.class).getInfoByUuid(
            new String[]{uuid},
            new String[]{"login_appcode", "login_cloned", "sex_type", "platform_id", "create_time"},
            "1", false, false).get(uuid);
        return uuidInfos;
    }
}
