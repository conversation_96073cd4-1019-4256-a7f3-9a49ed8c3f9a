package cn.taqu.account.soa.handler;

import cn.taqu.account.soa.AccountFollowSoaService;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static cn.taqu.account.utils.SoaHeaderBuilder.buildHeadersFromSoaParams;

/**
 * 用户粉丝数获取
 *
 * <AUTHOR>
 * @date 2025/04/08
 */
@Slf4j
@Service
public class AccountFollowServiceImpl implements AccountFollowSoaService {

    public static String URL;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public Response getFollowNum(String uuid) {
        try {
            Map<String, Object> req = new HashMap<>();
            req.put("account_uuid", uuid);
            req.put("read_time", Instant.now().getEpochSecond());
            try {
                HttpHeaders headers = buildHeadersFromSoaParams();
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(req, headers);
                return restTemplate.postForObject(URL + "/v1/AccountFollow/getFollowNum", entity, Response.class);
            } catch (Exception e) {
                log.warn("AccountFollow error,", JsonUtils.objectToString(req), e);
                return null;
            }
        } catch (Exception e) {
            log.error("获取用户粉丝数异常", e);
        }
        return null;
    }

    @Data
    public static class Response {
        /**
         * 关注数量
         */
        private String num;

        /**
         * 粉丝数量
         */
        @JsonProperty("fans_num")
        private String fansNum;
    }

}
