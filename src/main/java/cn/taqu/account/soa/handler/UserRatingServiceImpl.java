package cn.taqu.account.soa.handler;

import cn.taqu.account.soa.UserRatingService;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户等级
 *
 * <AUTHOR>
 * @date 2025/04/08
 */
@Slf4j
@Service
public class UserRatingServiceImpl implements UserRatingService {

    private static final SoaServer SERVER = new SoaServer("/soa/go/taqu-user-rating");

    @Override
    public boolean isSpecialUser(String uuid) {
        Map<Object, Object> map = new HashMap<>();
        map.put("account_uuid", uuid);
        map.put("province", "");
        map.put("leve_city", "");
        map.put("country", "");
        map.put("mc_version", "2");
        map.put("is_ver", "0");

        SoaClient client = SoaClientFactory.create(SERVER);
        SoaResponse response = client.call("/Rating/localInfo", map);
        if (response.fail()) {
            log.warn("获取-1级失败, {}, {}", uuid, response.getMsg());
            return false;
        }

        boolean equals = StringUtils.equals(response.getData(), "-1");
        if (equals) {
            log.info("-1级用户:{}", uuid);
        }
        return equals;
    }


}
