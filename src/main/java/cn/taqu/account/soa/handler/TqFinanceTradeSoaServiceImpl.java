package cn.taqu.account.soa.handler;

import cn.taqu.account.soa.TqFinanceTradeSoaService;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 业财查询
 *
 * <AUTHOR>
 * @date 2025/4/15 14:48
 */
@Slf4j
@Service
public class TqFinanceTradeSoaServiceImpl implements TqFinanceTradeSoaService {

    public static final SoaServer FINANCE_TRADE = new SoaServer("/soa/php/live-trade");

    @Override
    public Boolean existRevenue(String accountUuid) {
        //Map<String, Object> params = Maps.newHashMapWithExpectedSize(1);
        //params.put("account_uuid", accountUuid);
        SoaResponse response = SoaClientFactory.create(FINANCE_TRADE).call("finance", "getAccountLeftReward", accountUuid);
        if (response.fail()) {
            log.warn("收益接口请求失败,finance/getAccountLeftReward, accountUuid:{},{},{}", accountUuid, response.getCode(), response.getData());
            return false;
        }
        IncomeDTO data = JsonUtils.stringToObject(response.getData(), IncomeDTO.class);
        if (data == null) {
            return false;
        }
        if (data.getPersonalReward() != null && data.getPersonalReward().leftIncomeScore.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (data.getHasConsortia() == 0) {
            return false;
        }
        return data.getConsortiaReward().stream().anyMatch(item -> item.getLeftIncomeScore().compareTo(BigDecimal.ZERO) > 0);
    }

    @Data
    public static class IncomeDTO {

        @JsonProperty("consortia_reward")
        private List<ConsortiaReward> consortiaReward;

        @JsonProperty("personal_reward")
        private ConsortiaReward personalReward;

        @JsonProperty("has_consortia")
        private Integer hasConsortia;
    }

    @Data
    public static class ConsortiaReward {
        private String scene;
        @JsonProperty("scene_name")
        private String sceneName;
        @JsonProperty("left_income_score")
        private BigDecimal leftIncomeScore;
        @JsonProperty("income_unit")
        private String incomeUnit;
        @JsonProperty("income_equal")
        private BigDecimal incomeEqual;
    }

}
