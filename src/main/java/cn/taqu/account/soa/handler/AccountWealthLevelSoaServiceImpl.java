package cn.taqu.account.soa.handler;

import cn.taqu.account.soa.AccountWealthLevelSoaService;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/5 下午1:46
 */
@Slf4j
@Service
public class AccountWealthLevelSoaServiceImpl implements AccountWealthLevelSoaService {

    private static final SoaServer SERVER = new SoaServer("/soa/go/taqu-wealth-level");

    @Override
    public Map<String, Integer> batchGet(Collection<String> uuids) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("account_uuids", uuids);

        SoaClient client = SoaClientFactory.create(SERVER);
        SoaResponse response = client.call("/Level/batchGetAccountLevel", map);
        if (response.fail()) {
            String msg = "获取用户财富等级失败" + response.getMsg();
            log.warn(msg);
            return uuids.stream().collect(Collectors.toMap(Function.identity(), k -> 0));
        }
        return JsonUtils.stringToObject(response.getData(), new TypeReference<Map<String, Integer>>() {});
    }
}
