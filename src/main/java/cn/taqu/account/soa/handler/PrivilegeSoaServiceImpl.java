package cn.taqu.account.soa.handler;

import cn.taqu.account.soa.PrivilegeSoaService;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/15 11:50
 */
@Slf4j
@Service
public class PrivilegeSoaServiceImpl implements PrivilegeSoaService {

    @Override
    public Boolean isVip(String uuid) {
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.PHP.PRIVILEGE_SYSTEM).call("Vip", "isAccountVip", uuid);
        if (soaResponse.fail()) {
            log.warn("会员接口请求失败,Vip/existBagBalance, isAccountVip:{},{},{}", uuid, soaResponse.getCode(), soaResponse.getData());
            return false;
        }

        String data = soaResponse.getData();
        return JsonUtils.stringToObject2(data, Boolean.class);
    }

}
