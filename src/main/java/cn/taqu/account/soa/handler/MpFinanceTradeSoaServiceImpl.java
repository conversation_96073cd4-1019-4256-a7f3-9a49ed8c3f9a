package cn.taqu.account.soa.handler;

import cn.taqu.account.soa.MpFinanceTradeSoaService;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 业财查询
 *
 * <AUTHOR>
 * @date 2025/4/15 14:48
 */
@Slf4j
@Service
public class MpFinanceTradeSoaServiceImpl implements MpFinanceTradeSoaService {

    public static final SoaServer FINANCE_TRADE = new SoaServer("/soa/java/financeTrade");

    @Override
    public Boolean existBagBalance(String accountUuid) {
        Map<String, String> map = new HashMap<>();
        map.put("account_uuid", accountUuid);
        SoaResponse response = SoaClientFactory.create(FINANCE_TRADE).call("bagBalance", "existBagBalance", map);
        if (response.fail()) {
            log.warn("业财接口请求失败,bagBalance/existBagBalance, accountUuid:{},{},{}", accountUuid, response.getCode(), response.getData());
            return false;
        }
        return JsonUtils.stringToObject2(response.getData(), Boolean.class);
    }

    @Override
    public Boolean existBalance(String accountUuid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(1);
        params.put("account_uuid", accountUuid);
        SoaResponse response = SoaClientFactory.create(FINANCE_TRADE).call("balance", "getBalance", params);
        if (response.fail()) {
            log.warn("业财接口请求失败,balance/getBalance, accountUuid:{},{},{}", accountUuid, response.getCode(), response.getData());
            return false;
        }
        UserBalanceDTO data = JsonUtils.stringToObject(response.getData(), UserBalanceDTO.class);
        Long balance = Optional.ofNullable(data).map(UserBalanceDTO::getAmount).map(Long::parseLong).orElse(0L);
        return balance > 0;
    }

    @Data
    public static class UserBalanceDTO {

        /**
         * 用户uuid
         */
        @JsonProperty("account_uuid")
        private String accountUuid;

        /**
         * 总金额
         */
        private String amount;

        /**
         * 现金金额
         */
        @JsonProperty("cost_amount")
        private String costAmount;

        /**
         * 补贴金额
         */
        @JsonProperty("subsidy_amount")
        private String subsidyAmount;
    }

}
