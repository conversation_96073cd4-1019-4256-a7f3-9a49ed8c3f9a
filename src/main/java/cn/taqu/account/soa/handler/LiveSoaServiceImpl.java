package cn.taqu.account.soa.handler;

import cn.taqu.account.common.CommonEnableStatus;
import cn.taqu.account.soa.LiveSoaService;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/15 11:50
 */
@Slf4j
@Service
public class LiveSoaServiceImpl implements LiveSoaService {

    public static final SoaServer LIVE_API = new SoaServer("/soa/php/live-api");

    @Override
    public Boolean isMallVip(String uuid) {

        SoaResponse soaResponse = SoaClientFactory.create(LIVE_API).call("ProductMall", "accountVipInfo", uuid);
        if (soaResponse.fail()) {
            log.warn("商城会员接口请求失败,ProductMall/isMallVip:{},{},{}", uuid, soaResponse.getCode(), soaResponse.getData());
            return false;
        }

        String data = soaResponse.getData();
        AccountVipInfoDTO vipInfo = JsonUtils.stringToObject2(data, AccountVipInfoDTO.class);
        return Objects.nonNull(vipInfo) && vipInfo.getStatus().equals(CommonEnableStatus.ENABLE.getStatus());
    }

    @Data
    public static class AccountVipInfoDTO {

        /**
         * 会员状态 1生效中 0无效
         */
        private Integer status;

        /**
         * 会员到期时间
         */
        private Integer expire_time;

        /**
         * 用户分级
         */
        private Integer rating_num;

    }
}
