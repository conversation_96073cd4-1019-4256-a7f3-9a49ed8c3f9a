package cn.taqu.account.soa;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * 财富等级接口
 * <AUTHOR>
 * @date 2025/6/5 下午1:43
 */
public interface AccountWealthLevelSoaService {

    default Integer get(String uuid) {
        Map<String, Integer> map = batchGet(Collections.singleton(uuid));
        return map.getOrDefault(uuid, 0);
    }

    Map<String, Integer> batchGet(Collection<String> uuids);
}
