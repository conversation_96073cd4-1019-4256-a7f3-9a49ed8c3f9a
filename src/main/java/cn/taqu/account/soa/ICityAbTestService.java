package cn.taqu.account.soa;

import cn.taqu.account.service.CityAbTestService;

/**
 * <AUTHOR>
 * @date 2025/1/12 15:48
 */
public interface ICityAbTestService {

    /**
     * 获取城市实验【自己】
     *
     * @param uuid
     * @param expCode
     * @return
     * @link {https://api.admin.internal.taqu.cn/docs/api/api-1eprsl9h126pc}
     */
    CityAbTestService.Response getUserExpCache(String uuid, String expCode);

    /**
     * 获取城市实验【他人】
     *
     * @param uuid
     * @param expCode
     * @return
     * @link {https://api.admin.internal.taqu.cn/docs/api/api-1epl7ukfuvm3f}
     */
    CityAbTestService.Response getTargetExpCache(String uuid, String expCode, String cityId, Integer appcode, Integer cloned, Integer platformId, Long appVersion);

}
