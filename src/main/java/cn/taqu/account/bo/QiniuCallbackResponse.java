package cn.taqu.account.bo;

/**
 * 回调接口返回值，七牛会将之转发给客户端作为响应
 */
public class QiniuCallbackResponse {
    /**
     * 上传的目标空间名
     */
    private String bucket;
    /**
     * 文件保存在空间中的资源名
     */
    private String key;
    /**
     * 上传资源的后缀名，通过自动检测的 mimeType 或者$(fname)的后缀来获取。
     */
    private String ext;
    /**
     * 上传的原始文件名
     */
    private String name;
    /**
     * 资源尺寸，单位为字节
     */
    private String size;
    /**
     * 图片宽度
     */
    private String w;
    /**
     * 图片高度
     */
    private String h;

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getW() {
        return w;
    }

    public void setW(String w) {
        this.w = w;
    }

    public String getH() {
        return h;
    }

    public void setH(String h) {
        this.h = h;
    }
}
