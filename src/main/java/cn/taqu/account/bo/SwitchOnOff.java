package cn.taqu.account.bo;

import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.lang3.StringUtils;

import cn.taqu.core.protocol.SoaBaseParams;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * 2024年7月12日下午2:22:16
 */
@Slf4j
public class SwitchOnOff {

    /**
     * 线上开关
     */
    private boolean switchOnline;
    
    /**
     * 灰度开关
     */
    private boolean switchGray;
    
    /**
     * 线上环境默认值，用于放量使用
     */
    private Boolean onlineDefault;
    
    /**
     * 总比例数
     */
    private int partTotal;
    
    /**
     * 允许部分
     */
    private int partAllow;

    /**
     * 1. 线上只有配置true，才是开启
     * 2. 灰度没配和线上一致
     * 3. 灰度有配，只有配置true，才是开启
     * 
     * @param switchOnline
     * @param switchGray
     * @param onlineDefault 
     * @param partTotal
     * @param partAllow
     */
    public SwitchOnOff(String switchOnline, String switchGray, Boolean onlineDefault, int partTotal, int partAllow) {
        super();
        
        if(Objects.equals(switchOnline, "true")) {
            this.switchOnline = true;
        }else {
            this.switchOnline = false;
        }
        
        if(StringUtils.isBlank(switchGray)) {
            this.switchGray = this.switchOnline;
        }else if(Objects.equals(switchGray, "true")) {
            this.switchGray = true;
        }else {
            this.switchGray = false;
        }
        
        this.onlineDefault = onlineDefault;
        this.partTotal = partTotal;
        this.partAllow = partAllow;
        
    }
    
    /**
     * 1. 线上只有配置true，才是开启
     * 2. 灰度没配和线上一致
     * 3. 灰度有配，只有配置true，才是开启
     * 
     * @param switchOnline
     * @param switchGray
     */
    public SwitchOnOff(String switchOnline, String switchGray) {
        super();
        
        if(Objects.equals(switchOnline, "true")) {
            this.switchOnline = true;
        }else {
            this.switchOnline = false;
        }
        
        if(StringUtils.isBlank(switchGray)) {
            this.switchGray = this.switchOnline;
        }else if(Objects.equals(switchGray, "true")) {
            this.switchGray = true;
        }else {
            this.switchGray = false;
        }
    }
    
    /**
     * 开关是否开启，会根据环境判断
     * 
     * @param writeLog 是否记录日志
     * @return
     */
    public boolean isOn(boolean writeLog) {
        String env = SoaBaseParams.fromThread().getEnv();
        
        if(StringUtils.isNotBlank(env) && (env.equals("gray") || env.equals("1"))){
            return switchGray;
        }else {
            // 判断流控配置存在
            if(onlineDefault != null && partTotal != 0 && partAllow != 0) {
                // 0 ~ partTotal, 不包含partTotal
                int randomInt = ThreadLocalRandom.current().nextInt(partTotal);
                // 如果小于 配置的比例，则使用线上配置，否知使用默认值
                if(randomInt < partAllow){
                    if(writeLog) {
                        log.info("放量流量");
                    }
                    return switchOnline;
                }else {
                    if(writeLog) {
                        log.info("默认流量");
                    }
                    return onlineDefault;
                }
            }
            
            return switchOnline;
        }
    }
   
    
}
