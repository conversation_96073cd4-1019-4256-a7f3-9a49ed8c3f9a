/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.client;

import cn.taqu.account.client.api.tengxunyun.TengxunyunApi;
import cn.taqu.account.client.api.tengxunyun.TengxunyunVoucher;
import cn.taqu.account.client.api.tengxunyun.TengxunyunWBappid;
import cn.taqu.account.common.ClonedEnum;
import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.common.PlatformEnum;
import cn.taqu.account.common.ShanLianEnum;
import cn.taqu.account.config.biz.TencentCloudWBAppConfig;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.core.common.client.HttpClient;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LocalConfUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;


/**
 *
 * 腾讯云</br>
 *
 * 重要：开发、测试、自动化环境不要使用线上appId去获取accessToken，否则会导致线上配置失效
 *
 * <AUTHOR>
 * @date 2021/06/08
 */
@Component
public class TengxunyunApiClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(TengxunyunApiClient.class);


	public static String baseUri = "https://idasc.webank.com";

	private HttpClient httpClient;

	private TengxunyunApi tengxunyunApi;

	@Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

	public TengxunyunApiClient(){
		super();
	}

	public TengxunyunApiClient(String baseUri){
		this.httpClient = new HttpClient(baseUri, 4 * 1000, 4 * 1000, 4 * 1000);
		this.tengxunyunApi = new TengxunyunApi(this.httpClient);
	}

	@PostConstruct
	public void init() {
		this.httpClient = new HttpClient(baseUri, 4 * 1000, 4 * 1000, 4 * 1000);
		this.tengxunyunApi = new TengxunyunApi(this.httpClient);
	}

	public TengxunyunApi getTengxunyunApi() {
		return tengxunyunApi;
	}

	// 一共7个包，14个配置 都列出来，有些可能没用，具体看配置
	/** 他趣-正式 */
	private final static TengxunyunWBappid TAQU_ONLINE = new TengxunyunWBappid("IDAorNmr","1fzGugrcn4Z1EJpUCreIuVQHtQlhO518pjTAXpZ15r5f0NNc304jBdoAWCd2ZDZn");
	/** 他趣-正式 测试*/
	private final static TengxunyunWBappid TAQU_ONLINE_BAK = new TengxunyunWBappid("TIDA6pnm","TSudDukGwj7hWes0h4evCZEb0PEBlEPTUBedORVyQTWUzLxWOxxN3dmiYT3JTRw4");
	/** 他趣-测试 */
	private final static TengxunyunWBappid TAQU_TEST = new TengxunyunWBappid("IDAuMtBF","aDTjZkWFXfr013jyn0jicjoeXetRwxcRR9euYq1Hjy7dz0UQi0DRk6Sc2ZPQK5Dk");
	/** 他趣-测试 测试*/
	private final static TengxunyunWBappid TAQU_TEST_BAK = new TengxunyunWBappid("TIDAiTLj","rXmpymTi304dvepYofe2H6Fv1Nph7jnVjqyTJZdZezxUMr4YKCGHLXPEzQ4ttULU");
	/** 他趣-内测 ios bate*/
	private final static TengxunyunWBappid TAQU_BETA = new TengxunyunWBappid("IDAqbyri","PjMvJ1dz1dwh0gtkm1LJmNop029vnGQh6PsaJFvsXyEqVKU8paS8TqR470scl9Ut");
	/** 他趣-内测 ios bate 测试*/
	private final static TengxunyunWBappid TAQU_BETA_BAK = new TengxunyunWBappid("TIDAn7KU","jDwAGvgMJhU5aiaVjHd26uCVBMooftN37q15jMu3ORNx834yQfm8Z3BKanGvMMSV");
	// 旧的配配配置没上线使用过，先注释，使用ios申请的配置
//	/** 配配-正式 */
//	private final static TengxunyunWBappid PEIPEI_ONLINE = new TengxunyunWBappid("IDAVRHv2","dRRZbhvxjhWIbrX8cfRGMwXgMdRy9FjfZJQBCFWWLNX0h4YoljGhufBwd3YCb1ge");
//	/** 配配-正式 测试*/
//	private final static TengxunyunWBappid PEIPEI_ONLINE_BAK = new TengxunyunWBappid("TIDATHgW","KxE3Z2xtMWIcin6t4FrpUAHhmumRmJ6mXmOleQuj7cRk7oauRsEVJC57svYJyiey");
//	/** 配配-内测 */
//	private final static TengxunyunWBappid PEIPEI_GRAY = new TengxunyunWBappid("IDALrov8","MmK8m1gFRzpKpMwZ3T2TqJc8xpiY25n0WNIPpzEZWf1Bb1qRQBYVcLxnV1UfzVtw");
//	/** 配配-内测 测试*/
//	private final static TengxunyunWBappid PEIPEI_GRAY_BAK = new TengxunyunWBappid("TIDAC2rH","odIcPoLWtCylFY46N6N1RNcKiA6Z8jCmzTQ6XrBSWOeHmk5zpRY0Q0gBKSDg2WuE");
	/** 配配-正式 */
	private final static TengxunyunWBappid PEIPEI_ONLINE = new TengxunyunWBappid("IDAcHFKQ","oqpElZvxRIyNORvuyRpdKAVZiulMeBhDa1XgIGcmOv0HhCm3nK0V3MkVlstHLf68");
	/** 配配-正式 测试*/
	private final static TengxunyunWBappid PEIPEI_ONLINE_BAK = new TengxunyunWBappid("TIDAeyWw","uffxDxwqPnBk9o4zDFaRvocbtyXDi5WGmWliZ2XSHpJ5Ihaa1JMaALoEd1MraqMh");
	/** 配配-测试 */
	private final static TengxunyunWBappid PEIPEI_TEST = new TengxunyunWBappid("IDAeG64h","wFsKgEUbfVwGMq7g5jRzwPuPAZTyTGABpNBakFbXyC0rgmLffeIsHmEXsVKWkX8o");
	/** 配配-测试 测试*/
	private final static TengxunyunWBappid PEIPEI_TEST_BAK = new TengxunyunWBappid("TIDAaQDK","odIcPoLWtCylFY46N6N1RNcKiA6Z8jCmzTQ6XrBSWOeHmk5zpRY0Q0gBKSDg2WuE");

	/** 他趣-Plus */
	private final static TengxunyunWBappid TAQU_ONLINE_PLUS = new TengxunyunWBappid("IDAILy6d","5lEu1U9GtnUiYXTtpVLcoqG6lJpuXEovrtUfQRPXzOAdBdk9Cnjnpu31p6DA45XR");
	/** 他趣-Plus 测试*/
	private final static TengxunyunWBappid TAQU_ONLINE_PLUS_BAK = new TengxunyunWBappid("TIDAAnDQ","dIom95UP21KpskQpYZk6Gfb9PNSelslfrx8RifCptJ5Uumv0WdMjk6q2x70nVPHx");
	/** 他趣-Plus测试 */
	private final static TengxunyunWBappid TAQU_TEST_PLUS = new TengxunyunWBappid("IDAcwdkU","au4uKp5PW9wMyHcMSPd17ZtXpPK2PORRqt378cyLdXhbuf2uFr6H29Jro5sI1tl3");
	/** 他趣交友 */
	private final static TengxunyunWBappid TAQU_ONLINE_F = new TengxunyunWBappid("IDA78Vag","MPEfQGuhw9IWgyt99AVwuQ3dJYRwzrgae60zLzMX9ydp8R01M8j0sTBdDgI6n3uu");
	/** 他趣交友 测试*/
	private final static TengxunyunWBappid TAQU_ONLINE_F_BAK = new TengxunyunWBappid("TIDA8kya","6R677btHQyK0aEPDvG6pHgH9PCoIhCx96ZJPg0QoH4ToVCuaivMvpljC9EU1uKpJ");

    /**
     * 闪恋 - 线上
     */
    private final static TengxunyunWBappid SHANLIAN_ONLINE = new TengxunyunWBappid("IDAZgMMG","ji7CpJcptPZWXLUGY378cK1OrqiYfcG2s16Lp7qoSUAO80f4VvEkoe8nhnKGKdMi");

    /**
     * 闪恋 - 测试
     */
    private final static TengxunyunWBappid SHANLIAN_TEST = new TengxunyunWBappid("TIDAgnFt","b3Bbky9vvG47VIsCg85uUXN7dTZh2OWWZZrjkdkGnO5k3c8AA2hW1wfy7bwuOa3y");


    // 因为ios和安卓包名不同，划分不同，所以分别配置
    /** ios 他趣 线上 默认值 */
    private final static TengxunyunWBappid IOS_ONLINE_TQ = TAQU_ONLINE;
    /** ios 他趣 内测 */
    private final static TengxunyunWBappid IOS_BETA_TQ = TAQU_BETA;
    /** ios 他趣 测试 */
    private final static TengxunyunWBappid IOS_TEST_TQ = TAQU_TEST;
    /** ios 配配 线上 */
    private final static TengxunyunWBappid IOS_ONLINE_PP = PEIPEI_ONLINE;
    /** ios 配配 测试 */
    private final static TengxunyunWBappid IOS_TEST_PP = PEIPEI_TEST;
    /** ios 他趣plus 线上 */
    private final static TengxunyunWBappid IOS_ONLINE_TQ_PLUS = TAQU_ONLINE_PLUS;
    /** ios 他趣plus 测试 */
    private final static TengxunyunWBappid IOS_TEST_TQ_PLUS = TAQU_TEST_PLUS;
    /** ios 闪恋 线上 */
    private final static TengxunyunWBappid IOS_ONLINE_SL = SHANLIAN_ONLINE;
    /** ios 闪恋 测试 */
    private final static TengxunyunWBappid IOS_TEST_SL = SHANLIAN_TEST;

    /** 安卓 他趣   */
    private final static TengxunyunWBappid ANDROID_ONLINE_TQ = TAQU_ONLINE;
    /** 安卓 他趣 测试   */
    private final static TengxunyunWBappid ANDROID_TEST_TQ = TAQU_TEST;
    /** 安卓 配配   */
    private final static TengxunyunWBappid ANDROID_ONLINE_PP = PEIPEI_ONLINE;
    /** 安卓 配配 测试   */
    private final static TengxunyunWBappid ANDROID_TEST_PP = PEIPEI_TEST;
    /** 安卓 他趣交友 */
    private final static TengxunyunWBappid ANDROID_ONLINE_TQ_F = TAQU_ONLINE_F;
    /** 安卓 闪恋 线上   */
    private final static TengxunyunWBappid ANDROID_ONLINE_SL = SHANLIAN_ONLINE;
    /** 安卓 闪恋 测试   */
    private final static TengxunyunWBappid ANDROID_TEST_SL = SHANLIAN_TEST;

    /**
     * 测试环境
     */
    private final static List<String> ENV_TEST_LIST = Lists.newArrayList("test","test1","test2","test3","test4","test5","test6","test7","test8","test9","test10");
    /**
     * 线上、灰度
     */
    private final static List<String> ENV_PROD_LIST = Lists.newArrayList("online","gray");

    /**
     * 环境对应的appid 测试
     */
    private final static List<TengxunyunWBappid> ENV_APPID_TEST_LIST = Lists.newArrayList(TAQU_TEST, PEIPEI_TEST, TAQU_TEST_PLUS);

    /**
     * 环境对应的appid 线上、灰度
     */
    private final static List<TengxunyunWBappid> ENV_APPID_PROD_LIST = Lists.newArrayList(TAQU_ONLINE, TAQU_BETA, PEIPEI_ONLINE, TAQU_ONLINE_PLUS, TAQU_ONLINE_F);

    /**
     * 该环境下不允许使用的配置
     */
    private static List<String> EXCLUDE_APPID_LIST = Lists.newArrayList();

    public static void updateExcludeAppidList(String json) {
        LOGGER.info("更新排除appidList,【{}】", json);
        List<String> list = JsonUtils.stringToObject(json, new TypeReference<List<String>>() {});
        if(!CollectionUtils.isEmpty(list)) {
            EXCLUDE_APPID_LIST = list;
        }
    }

    /**
     * 生成AccessToken和signTicket</br></br>
     * 第三方规则：</br>
     * 1、accessToken有效2小时、signTicket有效1小时，腾讯云建议两个绑定一起获取</br>
     * 2、accessToken获取新的后，旧的仍然保持到有效期结束。</br>
     * 3、signTicket归属accessToken，同一个accessToken创建新的signTicket后，旧的只保留1分钟有效。不同accessToken的signTicket不互相影响 </br>
     * </br>
     * 根据腾讯云机制制定获取逻辑：</br>
     * 只根据缓存环境来处理accessToken，默认只处理两个大环境:1-线上、灰度，2-测试环境；其他环境使用再去获取；配置存储redis中；</br>
     * </br>
     */
    public void generateAccessTokenAndSignTicket() {
        String localEnv = LocalConfUtil.getLocalEnv();
        if(ENV_PROD_LIST.contains(localEnv)) {
            for (TengxunyunWBappid WBappid : ENV_APPID_PROD_LIST) {
                try {
                    generateTengxunyunVoucher(WBappid);
                } catch (Exception e) {
                    LOGGER.error("生成腾讯云凭证异常",e);
                    continue;
                }
            }
        }else if(ENV_TEST_LIST.contains(localEnv)) {
            for (TengxunyunWBappid WBappid : ENV_APPID_TEST_LIST) {
                try {
                    generateTengxunyunVoucher(WBappid);
                } catch (Exception e) {
                    LOGGER.error("生成腾讯云凭证异常",e);
                    continue;
                }
            }
        }

    }

    /**
     * 获取配置
     *
     * @param platformId  平台
     * @param cloned
     * @param localEnv 服务器配置环境
     * @param reqEnv 请求环境 1-灰度，0-线上 //无用
     * @param channel
     * @return
     */
    public static TengxunyunWBappid getConfig(Integer platformId, Integer cloned, String localEnv, String reqEnv, String channel) {
        LOGGER.info("{},{},{},{}", platformId, cloned, localEnv, reqEnv);
        // 区分ios/安卓
        if(Objects.equals(platformId, PlatformEnum.ANDROID.getValue())) {
            // 安卓
            if(Objects.equals(cloned, ClonedEnum.TAQU.getCode()) && ENV_PROD_LIST.contains(localEnv)) {
                return ANDROID_ONLINE_TQ;
            }else if(Objects.equals(cloned, ClonedEnum.TAQU.getCode()) && ENV_TEST_LIST.contains(localEnv)) {
                return ANDROID_TEST_TQ;
            }else if((Objects.equals(cloned, ClonedEnum.PEIPEI.getCode()) || Objects.equals(cloned, ClonedEnum.QIALIAO.getCode())) && ENV_TEST_LIST.contains(localEnv)) {
                return ANDROID_TEST_PP;
            }else if ((Objects.equals(cloned, ClonedEnum.PEIPEI.getCode()) || Objects.equals(cloned, ClonedEnum.QIALIAO.getCode())) && ENV_PROD_LIST.contains(localEnv)) {
                return ANDROID_ONLINE_PP;
            } else if (Objects.equals(cloned, ClonedEnum.TAQU_FRIEND.getCode()) && ENV_PROD_LIST.contains(localEnv)) {
                return ANDROID_ONLINE_TQ_F;
            } else if (ClonedGroupEnum.YOUNG_GROUP.contains(cloned)) {
                // 这期开始把配置迁移到etcd，用魔法值配置,2025-04-02
                TencentCloudWBAppConfig.Conf conf = TencentCloudWBAppConfig.getConf(cloned);
                return new TengxunyunWBappid(conf.getId(), conf.getSecret());
            } else if (ShanLianEnum.isShanLianApp(cloned) && ENV_TEST_LIST.contains(localEnv)) {
                return new TengxunyunWBappid(
                        ShanLianEnum.getInstance(cloned).getTx_appid_test(),
                        ShanLianEnum.getInstance(cloned).getTx_secret_test()
                );
            } else if (ShanLianEnum.isShanLianApp(cloned) && ENV_PROD_LIST.contains(localEnv)) {
                return new TengxunyunWBappid(
                        ShanLianEnum.getInstance(cloned).getTx_appid_online(),
                        ShanLianEnum.getInstance(cloned).getTx_secret_online()
                );
            } else {
                return ANDROID_ONLINE_TQ;
            }
        }else if(Objects.equals(platformId, PlatformEnum.IPHONE.getValue()) || Objects.equals(platformId, PlatformEnum.IPAD.getValue())) {
            // ios
            if(Objects.equals(cloned, ClonedEnum.TAQU.getCode()) && ENV_PROD_LIST.contains(localEnv) ) {
                // 线上环境 他趣 需要区分普通包和beta包
                if(Objects.equals(channel, "beta")) {
                    // 内测包
                    return IOS_BETA_TQ;
                }else {
                    return IOS_ONLINE_TQ;
                }
            }else if(Objects.equals(cloned, ClonedEnum.TAQU.getCode()) && ENV_TEST_LIST.contains(localEnv)) {
                return IOS_TEST_TQ;
            }else if(Objects.equals(cloned, ClonedEnum.TAQU_PLUS.getCode()) && ENV_TEST_LIST.contains(localEnv)) {
                return IOS_TEST_TQ_PLUS;
            }else if((Objects.equals(cloned, ClonedEnum.PEIPEI.getCode()) || Objects.equals(cloned, ClonedEnum.QIALIAO.getCode())) && ENV_PROD_LIST.contains(localEnv)) {
                return IOS_ONLINE_PP;
            }else if ((Objects.equals(cloned, ClonedEnum.PEIPEI.getCode()) || Objects.equals(cloned, ClonedEnum.QIALIAO.getCode())) && ENV_TEST_LIST.contains(localEnv)) {
                return IOS_TEST_PP;
            } else if (Objects.equals(cloned, ClonedEnum.TAQU_PLUS.getCode()) && ENV_PROD_LIST.contains(localEnv)) {
                return IOS_ONLINE_TQ_PLUS;
            } else if (ClonedGroupEnum.YOUNG_GROUP.contains(cloned)) {
                // 这期开始把配置迁移到etcd，用魔法值配置,2025-04-02
                TencentCloudWBAppConfig.Conf conf = TencentCloudWBAppConfig.getConf(cloned);
                return new TengxunyunWBappid(conf.getId(), conf.getSecret());
            } else if (ShanLianEnum.isShanLianApp(cloned) && ENV_TEST_LIST.contains(localEnv)) {
                return new TengxunyunWBappid(
                        ShanLianEnum.getInstance(cloned).getTx_appid_test(),
                        ShanLianEnum.getInstance(cloned).getTx_secret_test()
                );
            } else if (ShanLianEnum.isShanLianApp(cloned) && ENV_PROD_LIST.contains(localEnv)) {
                return new TengxunyunWBappid(
                        ShanLianEnum.getInstance(cloned).getTx_appid_online(),
                        ShanLianEnum.getInstance(cloned).getTx_secret_online()
                );
            } else {
                return IOS_ONLINE_TQ;
            }
        }else {
            return null;
        }
    }

    /**
     * 生成并写入缓存
     *
     * @param WBappid
     * @return
     */
    private TengxunyunVoucher generateTengxunyunVoucher(TengxunyunWBappid WBappid) {
        TengxunyunVoucher tengxunyunVoucher = new TengxunyunVoucher();
        tengxunyunVoucher = getAccessToken(tengxunyunVoucher, WBappid.getAppid(), WBappid.getSecret());
        tengxunyunVoucher = getSignTicket(tengxunyunVoucher, WBappid.getAppid());

        ValueOperations<String, String> opsForValue = accountStringRedisTemplate.opsForValue();
        String redisKey = RedisKeyConstant.TENGXUNYUN_VOUCHER.setArg(WBappid.getAppid());
        opsForValue.set(redisKey, JsonUtils.objectToString(tengxunyunVoucher));

        return tengxunyunVoucher;
    }

    /**
     * 获取凭证
     *
     * @param WBappid
     * @return
     */
    public TengxunyunVoucher getTengxunyunVoucher(TengxunyunWBappid WBappid) {
        TengxunyunVoucher tengxunyunVoucher = null;
        ValueOperations<String, String> opsForValue = accountStringRedisTemplate.opsForValue();
        String redisKey = RedisKeyConstant.TENGXUNYUN_VOUCHER.setArg(WBappid.getAppid());
        String redisVal = opsForValue.get(redisKey);
        if(StringUtils.isNotBlank(redisVal)) {
            tengxunyunVoucher = JsonUtils.stringToObject(redisVal, new TypeReference<TengxunyunVoucher>() {});
        }

        // 为空算异常情况
        if(tengxunyunVoucher == null) {
            LOGGER.warn("获取时生成凭证,appid={}", WBappid.getAppid());
            return generateTengxunyunVoucher(WBappid);
        }
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        // 值不全，超时，算异常
        if(StringUtils.isBlank(tengxunyunVoucher.getAccessToken()) || StringUtils.isBlank(tengxunyunVoucher.getSignTicket()) ||
            tengxunyunVoucher.getAccessTokenExpireTime() == null || tengxunyunVoucher.getAccessTokenExpireTime() < currentTimeSeconds ||
            tengxunyunVoucher.getSignTicketExpireTime() == null || tengxunyunVoucher.getSignTicketExpireTime() < currentTimeSeconds) {
            LOGGER.warn("凭证异常,appid={},tengxunyunVoucher={}", WBappid.getAppid(), redisVal );
            return generateTengxunyunVoucher(WBappid);
        }

        return tengxunyunVoucher;
    }

    /**
     * 获取AccessToken
     *
     * @param tengxunyunVoucher
     * @param appid
     * @param secret
     * @return
     */
    private TengxunyunVoucher getAccessToken(TengxunyunVoucher tengxunyunVoucher, String appid, String secret) {
        // 2021.09.29 限制非线上环境不允许请求线上appid配置
        if(EXCLUDE_APPID_LIST.contains(appid)) {
            LOGGER.error("该配置不允许再当前环境使用，appid={}",appid);
            throw new ServiceException("generate_tengxunyun_voucher_error", "获取腾讯云accessToken失败");
        }

        String responseJson = tengxunyunApi.oauth2AccessToken(appid, secret);
        Map<String, Object> map = JsonUtils.stringToObject(responseJson, new TypeReference<Map<String, Object>>() {});
        String code = MapUtils.getString(map, "code");
        if(Objects.equals(code, "0")) {
            tengxunyunVoucher.setAccessToken(MapUtils.getString(map, "access_token", ""));
            tengxunyunVoucher.setAccessTokenExpireIn(MapUtils.getLong(map, "expire_in"));
            tengxunyunVoucher.setAccessTokenExpireTime(DateUtil.toSecond(DateUtil.string14ToDate(MapUtils.getString(map, "expire_time"))));
            return tengxunyunVoucher;
        }else {
            LOGGER.error("获取腾讯云accessToken失败，code={}，msg={}，bizSeqNo={}", code, MapUtils.getString(map, "msg"), MapUtils.getString(map, "bizSeqNo"));
            throw new ServiceException("generate_tengxunyun_voucher_error", "获取腾讯云accessToken失败");
        }
    }

    /**
     * 获取SignTicket，必须要有AccessToken
     *
     * @param tengxunyunVoucher
//     * @param string
     * @return
     */
    private TengxunyunVoucher getSignTicket(TengxunyunVoucher tengxunyunVoucher, String appid) {
        String responseJson = tengxunyunApi.oauth2ApiTicketSign(appid, tengxunyunVoucher.getAccessToken());
        Map<String, Object> map = JsonUtils.stringToObject(responseJson, new TypeReference<Map<String, Object>>() {});
        String code = MapUtils.getString(map, "code");
        if(Objects.equals(code, "0")) {
            List<Map<String, Object>> list = (List<Map<String, Object>>)MapUtils.getObject(map, "tickets");
            Map<String, Object> ticket = list.get(0);
            tengxunyunVoucher.setSignTicket(MapUtils.getString(ticket, "value", ""));
            tengxunyunVoucher.setSignTicketExpireIn(MapUtils.getLong(ticket, "expire_in"));
            tengxunyunVoucher.setSignTicketExpireTime(DateUtil.toSecond(DateUtil.string14ToDate(MapUtils.getString(ticket, "expire_time"))));
            return tengxunyunVoucher;
        }else {
            LOGGER.error("获取腾讯云signTicket失败，code={}，msg={}，bizSeqNo={}", code, MapUtils.getString(map, "msg"), MapUtils.getString(map, "bizSeqNo"));
            throw new ServiceException("generate_tengxunyun_voucher_error", "获取腾讯云signTicket失败");
        }
    }

    public String getNonceTicket(String appid, String accessToken, String accountUuid) {
        String oauth2ApiTicket =
            tengxunyunApi.oauth2ApiTicketNonce(appid, accessToken, accountUuid);
        // LOGGER.info("腾讯实名oauth2ApiTicket【{}】", oauth2ApiTicket);
        Map<String, Object> map =
            JsonUtils.stringToObject(oauth2ApiTicket, new TypeReference<Map<String, Object>>() {});
        String code = MapUtils.getString(map, "code");
        if(Objects.equals(code, "0")) {
            List<Map<String, Object>> object = (List<Map<String, Object>>)MapUtils.getObject(map, "tickets");
            String apiTicketNonce = (String)object.get(0).get("value");
            return apiTicketNonce;
        }else {
            LOGGER.error("获取腾讯云nonceTicket失败，code={}，msg={}", code, MapUtils.getString(map, "msg"));
            throw new ServiceException("generate_tengxunyun_voucher_error", "获取腾讯云nonceTicket失败");
        }
    }

    public String getFaceid(String appid, String orderNo, String certName, String certNo, String accountUuid, String sign, String nonce) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("webankAppId", appid);
        params.put("orderNo", orderNo);
        params.put("name", certName);
        params.put("idNo", certNo);
        params.put("userId", accountUuid);
        params.put("sourcePhotoStr", "");
        params.put("sourcePhotoType", 2);
        params.put("version", "1.0.0");
        params.put("sign", sign);
        params.put("nonce", nonce);

        String faceidJson = doPostByJson("https://idasc.webank.com/api/server/getfaceid?orderNo="+orderNo, null, JsonUtils.objectToString(params));
//        String faceidJson = tengxunyunApi.serverGetfaceid(appid, orderNo, certName, certNo, accountUuid, sign, nonce);

//        {"code":"********","msg":"姓名或身份证不合法","bizSeqNo":"21062520001184436316124619491055","result":{"bizSeqNo":"21062520001184436316124619491055","transactionTime":"**************","success":false},"transactionTime":"**************"}
//        {"code":"0","msg":"请求成功","bizSeqNo":"21062520001184438416134917885607","result":{"bizSeqNo":"21062520001184438416134917885607","transactionTime":"**************","orderNo":"dd31a68a4f014c43b9c487464ae90887","faceId":"wb0929b758bee1568a5c4939850d9da0","success":false},"transactionTime":"**************"}
        Map<String, Object> map =
            JsonUtils.stringToObject(faceidJson, new TypeReference<Map<String, Object>>() {});
        String code = MapUtils.getString(map, "code");
        if (!Objects.equals(code, "0")) {
            LOGGER.warn("腾讯云获取faceId失败，code={}, bizSeqNo={}, msg={}, appid={}, orderNo={}, certName={}, certNo={}, accountUuid={}, sign={}",
                code, MapUtils.getString(map, "bizSeqNo",""), MapUtils.getString(map, "msg",""), appid, orderNo, certName, certNo, accountUuid, sign);
            LOGGER.warn("腾讯云获取faceId失败2， appid={}, orderNo={}, certName={}, certNo={}, accountUuid={}, sign={}, json={}",
                appid, orderNo, certName, certNo, accountUuid, sign, JsonUtils.objectToString2(map));
            if(StringUtils.isNotEmpty(code) && code.equals("********")){
                throw new ServiceException(CodeStatus.IDENTITY_INVALID);
            }else{
                throw new ServiceException(CodeStatus.CERTIFY_FAIL);
            }
        }

        Map<String, Object> result = (Map<String, Object>)MapUtils.getObject(map, "result");
        return MapUtils.getString(result, "faceId");
    }

    /**
     * 获取sign，腾讯云提供方法
     *
     * @param values
     * @param ticket
     * @return
     */
    public static String sign(List<String> values, String ticket) {
        if (values == null) {
            throw new NullPointerException("values is null");
        }
        values.removeAll(Collections.singleton(null));// remove null
        values.add(ticket);
        java.util.Collections.sort(values);
        StringBuilder sb = new StringBuilder();
        for (String s : values) {
            sb.append(s);
        }
        return Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
    }

    /**
     * 获取sign，腾讯云提供方法
     *
//     * @param values
     * @param ticket
     * @return
     */
    public static String sign(String appid, String nonce, String accountUuid, String ticket) {
        List<String> values = Lists.newArrayList();
        values.add("1.0.0");
        values.add(appid);
        values.add(nonce);
        values.add(accountUuid);
        return sign(values, ticket);
    }

    /**
     * 订单号前缀
     */
    private static String ORDERNO_PREFIX = "TXY";

    /**
     * 腾讯云订单号随机算法
     * 前缀 + 毫秒时间戳 + 16位随机数
     *
     * @return
     */
    public static String generateOrderNo() {
        return new StringBuffer().append(ORDERNO_PREFIX).append(System.currentTimeMillis()).append(RandomStringUtils.randomAlphanumeric(16)).toString();
    }

    /**
     * 腾讯云nonce算法
     * 随机生成32位
     *
     * @return
     */
    public static String generateNonce() {
        return RandomStringUtils.randomAlphanumeric(32);
    }

    /**
     * post请求，由于腾讯云有接口只能这样请求
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static String doPostByJson(String url,Map<String,String> headers, String json) {
        // 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try{
            //创建httpPost请求
            HttpPost httpPost = new HttpPost(url);
            //给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(json,"utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            // 添加httpHeaders
            if (headers != null && headers.size() > 0) {
                for (String key : headers.keySet()) {
                    httpPost.addHeader(key, headers.get(key));
                }
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 获取响应消息
            resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
        }
        catch(Exception e)
        {
            e.printStackTrace();
        }
        finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return resultString;
    }

}
