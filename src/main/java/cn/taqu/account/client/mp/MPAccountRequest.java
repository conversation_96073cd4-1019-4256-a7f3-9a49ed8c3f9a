package cn.taqu.account.client.mp;

import cn.hutool.json.JSONUtil;
import cn.taqu.core.common.client.*;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LocalConfUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.taqu.mp.account.constant.URLNamespace;
import com.taqu.mp.account.core.MPRequest;
import com.taqu.mp.account.core.MPResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
public class MPAccountRequest implements MPRequest<ServiceException> {

    /**
     * 是否开启账号中台请求
     */
    private static Boolean isOpenRequest;

    private static final List<String> GRAY = Arrays.asList("gray", "1");
    private static final List<String> ONLINE = Arrays.asList("online", "0");

    private static Map<String, Boolean> switchMap = new HashMap<>();
    /*
    {
       "gray": true,
        "online": false
    }
    * */

    public static void initConfig(String raw) {
        isOpenRequest = Boolean.parseBoolean(raw);
        log.info("isOpenRequest: {}", isOpenRequest);
    }

    public static void initSwitchConfig(String raw) {
        log.info("mp request switchConfig: {}, env: {}", raw, LocalConfUtil.getLocalEnv());
        switchMap = JsonUtils.stringToObject(raw, new TypeReference<Map<String, Boolean>>() {
        });
    }

    public static Boolean isRequestMP() {
        if (GRAY.contains(LocalConfUtil.getLocalEnv())) {
            return switchMap.getOrDefault("gray", Boolean.FALSE);
        }
        if (ONLINE.contains(LocalConfUtil.getLocalEnv())) {
            return switchMap.getOrDefault("online", Boolean.FALSE) && isOpenRequest;
        }
        return isOpenRequest;
    }

    @Override
    public ServiceException catchRequestException(String message) {
        return new ServiceException(message);
    }

    @Override
    public MPResponse call(String service, String method, Object... form) {
        SoaClient client = SoaClientFactory.create(SoaServer.JAVA.MP_ACCOUNT_WEB);
        if (service.equals(URLNamespace.Service.ACCOUNT) && method.equals(URLNamespace.Method.LOGIN)) {
            SoaResponse soaResponse = client.call(getParam(), service, method, form);
            return new MPAccountResponse(soaResponse);
        }
        if (service.equals(URLNamespace.Service.ACCOUNT) && method.equals(URLNamespace.Method.REG)) {
            client.setTryLimit(1);
            SoaResponse soaResponse = client.call(getParam(), service, method, form);
            return new MPAccountResponse(soaResponse);
        }
        if (service.equals(URLNamespace.Service.ACCOUNT) && method.equals(URLNamespace.Method.DE_REG)) {
            SoaResponse soaResponse = client.call(getParam(), service, method, form);
            return new MPAccountResponse(soaResponse);
        }
        if (service.equals(URLNamespace.Service.MP_USER) && method.equals(URLNamespace.Method.GET_ACCOUNT_BIND_INFO)) {
            SoaResponse soaResponse = client.call(getParam(3), service, method, form);
            return new MPAccountResponse(soaResponse);
        }
        SoaResponse soaResponse = client.call(getParamBase(), service, method, form);
        return new MPAccountResponse(soaResponse);
    }

    public static ConnectParam getParam() {
        return new ConnectParam(15 * 1000, -1, 1000);
    }

    public static ConnectParam getParam(int connect) {
        return new ConnectParam(15 * 1000, -1, connect * 1000);
    }
    
    public static ConnectParam getParamBase() {
        return new ConnectParam(5 * 1000, 5 * 1000, 1000);
    }

    @Override
    public void close() throws IOException {
    }

    private boolean isAppleLogin(String service, String method, Object... form) {
        return service.equals(URLNamespace.Service.ACCOUNT) &&
                method.equals(URLNamespace.Method.LOGIN) &&
                form.length > 5 && ((int) form[2]) == 5;
    }

    private boolean isAppleDeReg(String service, String method, Object... form) {
        return false;
    }
}
