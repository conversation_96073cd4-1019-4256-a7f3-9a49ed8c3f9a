package cn.taqu.account.client.mp;

import cn.taqu.core.common.client.SoaResponse;
import com.taqu.mp.account.core.MPResponse;

/**
 * <AUTHOR> Wu.D.J
 */
public class MPAccountResponse implements MPResponse {

    private final SoaResponse soaResponse;

    public MPAccountResponse(SoaResponse soaResponse) {
        this.soaResponse = soaResponse;
    }

    @Override
    public String getCode() {
        return soaResponse.getCode();
    }

    @Override
    public String getData() {
        return soaResponse.getData();
    }

    @Override
    public String getMsg() {
        return soaResponse.getMsg();
    }
}
