package cn.taqu.account.client.api.tengxunyun;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 活体结果-result
 * 
 * <AUTHOR>
 * 2023年11月20日下午3:08:59
 */
@Getter
@Setter
@ToString
public class TxSyncResponseResult{
    
    private String bizSeqNo;
    private String transactionTime;
    private String orderNo;
 // 没用到，暂时示不处理
//    private String video;
    /**
     * Base64 String 活体检测时的照片，Base 64 位编码
     */
    private String photo;
    private String liveRate;
    private String similarity;
    private String occurredTime;
    private Boolean success;
    
    public TxSyncResponseResult() {
        super();
    }

    public TxSyncResponseResult(String bizSeqNo, String transactionTime, String orderNo, String photo, String liveRate,
        String similarity, String occurredTime, Boolean success) {
        super();
        this.bizSeqNo = bizSeqNo;
        this.transactionTime = transactionTime;
        this.orderNo = orderNo;
        this.photo = photo;
        this.liveRate = liveRate;
        this.similarity = similarity;
        this.occurredTime = occurredTime;
        this.success = success;
    }
    
}