package cn.taqu.account.client.api.tengxunyun;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 活体结果-result
 * 
 * <AUTHOR>
 * 2023年11月20日下午3:08:59
 */
@Getter
@Setter
@ToString
public class TxGetLiveResultResponseResult{
    
    private String bizSeqNo;
    private String transactionTime;
    private String orderNo;
    private String webankAppId;
    /**
     * Base64 String 活体检测时的照片，Base 64 位编码
     */
    private String photo;
    private String liveRate;
    private String occurredTime;
    // 没用到，暂时示不处理
    //private String photoCrop;
    private Boolean success;
    
    public TxGetLiveResultResponseResult() {
        super();
    }

    public TxGetLiveResultResponseResult(String bizSeqNo, String transactionTime, String orderNo, String webankAppId,
        String photo, String liveRate, String occurredTime, Boolean success) {
        super();
        this.bizSeqNo = bizSeqNo;
        this.transactionTime = transactionTime;
        this.orderNo = orderNo;
        this.webankAppId = webankAppId;
        this.photo = photo;
        this.liveRate = liveRate;
        this.occurredTime = occurredTime;
        this.success = success;
    }
    
    
}