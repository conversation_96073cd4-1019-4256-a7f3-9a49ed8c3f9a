package cn.taqu.account.client.api.tengxunyun;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 活体结果
 * 
 * <AUTHOR>
 * 2023年11月20日下午3:08:40
 */
@Getter
@Setter
@ToString
public class TxSyncResponse{
    
    private String code;
    private String msg;
    private String bizSeqNo;
    private TxGetLiveResultResponseResult result;
    private String transactionTime;
    private String app_id;
    private String order_no;
    
    public TxSyncResponse() {
        super();
    }

    public TxSyncResponse(String code, String msg, String bizSeqNo, TxGetLiveResultResponseResult result,
        String transactionTime, String app_id, String order_no) {
        super();
        this.code = code;
        this.msg = msg;
        this.bizSeqNo = bizSeqNo;
        this.result = result;
        this.transactionTime = transactionTime;
        this.app_id = app_id;
        this.order_no = order_no;
    }
 
    /**
     * 请求是否成功
     * 
     * @return
     */
    public boolean isSuccess() {
        boolean result = false;
        if("0".equals(code)){
            result = true;
        }
        return result;
    }
}