/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.client.api.tengxunyun;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.taqu.account.client.api.Api;
import cn.taqu.core.common.client.HttpClient;


/**
 * 腾讯云
 *
 * <AUTHOR>
 * @date 2021/05/27
 */
public class TengxunyunApi extends Api{
	private static final Logger LOGGER = LoggerFactory.getLogger(TengxunyunApi.class);

	/**
	 * @param client
	 */
	public TengxunyunApi(HttpClient client) {
		super(client);
	}


	/**
	 * 获取 Access Token 有效2小时
	 * https://cloud.tencent.com/document/product/1007/37304
	 *
	 * @param appid
	 * @param secret
	 * @param grantType
	 * @param version
	 * @return
	 */
    public String oauth2AccessToken(String appid, String secret) {
		String uri = "/api/oauth2/access_token";
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("app_id", appid);
		params.put("secret", secret);
		params.put("grant_type", "client_credential");
		params.put("version", "1.0.0");
		return this.getWithRetry(uri, params, 3);
	}

    /**
     * 获取 NONCE ticket
     * https://cloud.tencent.com/document/product/1007/37306
     *
     * @param appid
     * @param AccessToken
     * @param version
     * @param userId
     * @return
     */
    public String oauth2ApiTicketNonce(String appid, String AccessToken, String userId) {
        String uri = "/api/oauth2/api_ticket";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("app_id", appid);
        params.put("access_token", AccessToken);
        params.put("type", "NONCE");
        params.put("version", "1.0.0");
        params.put("user_id", userId);
        return this.getWithRetry(uri, params, 3);
    }

    /**
     * 获取 SIGN ticket 有效1小时
     * https://cloud.tencent.com/document/product/1007/37306
     *
     * @param appid
     * @param AccessToken
     * @param version
     * @param userId
     * @return
     */
    public String oauth2ApiTicketSign(String appid, String AccessToken) {
        String uri = "/api/oauth2/api_ticket";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("app_id", appid);
        params.put("access_token", AccessToken);
        params.put("type", "SIGN");
        params.put("version", "1.0.0");
        return this.getWithRetry(uri, params, 3);
    }

    /**
     * 身份认证查询接口
     * @param appid
     * @param version
     * @param nonce
     * @param orderNo
     * @param sign
     * @return
     */
//    public String serverSync(String appid, String version, String nonce, String orderNo, String sign) {
//        return serverSync(appid, version, nonce, orderNo, sign, "");
//    }

    /**
     * 身份认证查询接口
     * PS: 如果getFile传了 123可能会对服务器产生压力
     * @param appid
     * @param version
     * @param nonce
     * @param orderNo
     * @param sign
     * @param getFile 1-返回视频和照片 2-返回照片 3-返回视频 其他-不返回
     * @return
     */
    public String serverSync(String appid, String version, String nonce, String orderNo, String sign, String getFile) {
        String uri = "/api/server/sync";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("app_id", appid);
        params.put("version", version);
        params.put("nonce", nonce);
        params.put("order_no", orderNo);
        params.put("sign", sign);
        params.put("get_file", getFile);
        return this.getWithRetry(uri, params, 3);
    }

    /**
     * 身份认证查询接口
     * @param appid
     * @param version
     * @param nonce
     * @param orderNo
     * @param sign
     * @return
     */
    public String serverGetLiveResult(String appid, String version, String nonce, String orderNo, String sign) {
        String uri = "/api/server/getLiveResult";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("app_id", appid);
        params.put("version", version);
        params.put("nonce", nonce);
        params.put("order_no", orderNo);
        params.put("sign", sign);
        params.put("photoFlag", 1);
        params.put("get_file", 2);
        return this.getWithRetry(uri, params, 3);
    }

    /**
     * 获取faceId</br>
     * 
     * https://cloud.tencent.com/document/product/1007/35866
     * 
     * @param appid
     * @param orderNo
     * @param name
     * @param idNo
     * @param userId
     * @param sign
     * @param nonce
     * @return
     */
    public String serverGetfaceid(String appid, String orderNo, String name, String idNo, String userId, String sign, String nonce) {
        String uri = "/api/server/getfaceid?orderNo=" + orderNo;
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("webankAppId", appid);
        params.put("orderNo", orderNo);
        params.put("name", name);
        params.put("idNo", idNo);
        params.put("userId", userId);
        params.put("sourcePhotoStr", "");
        params.put("sourcePhotoType", 2);
        params.put("version", "1.0.0");
        params.put("sign", sign);
        params.put("nonce", nonce);
        
        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("Content-Type", "application/json; charset=UTF-8");

        return this.postWithRetry(uri, params, headerMap, 3);
    }

    /**
     * get请求
     *
     * @param uri
     * @param params
     * @param maxRetryTimes
     * @return
     */
	private String getWithRetry(String uri, Map<String, Object> params, int maxRetryTimes) {
		int tryTimes = 0;
		while(true) {
			try {
				return this.getClient().get(uri, params);
			} catch (Exception e) {
				if(++tryTimes>maxRetryTimes) {
					throw e;
				}
				LOGGER.warn("第 " + tryTimes +  "次请求腾讯云[" + uri + "]接口失败", e);
			}
		}
	}

	/**
	 * post请求
	 *
	 * @param uri
	 * @param params
	 * @param headerMap
	 * @param maxRetryTimes
	 * @return
	 */
	private String postWithRetry(String uri, Map<String, Object> params, Map<String, Object> headerMap, int maxRetryTimes) {
	    int tryTimes = 0;
	    while(true) {
	        try {
	            return this.getClient().post(uri, params, headerMap);
	        } catch (Exception e) {
	            if(++tryTimes>maxRetryTimes) {
	                throw e;
	            }
	            LOGGER.warn("第 " + tryTimes +  "次请求腾讯云[" + uri + "]接口失败", e);
	        }
	    }
	}

}
