package cn.taqu.account.client.api.tengxunyun;

import lombok.Getter;
import lombok.Setter;

/**
 * 腾讯云凭证
 * 
 * <AUTHOR>
 * @date 2021/06/08
 */
@Getter
@Setter
public class TengxunyunVoucher{
    /**
     * accessToken
     */
    private String accessToken = "";
    /**
     * accessToken 过期时间 最终时间
     */
    private Long accessTokenExpireTime;
    /**
     * accessToken 有效时长: 秒
     */
    private Long accessTokenExpireIn;
    
    /**
     * signTicket
     */
    private String signTicket = "";
    /**
     * signTicket 过期时间 最终时间
     */
    private Long signTicketExpireTime;
    /**
     * signTicket 有效时长: 秒
     */
    private Long signTicketExpireIn;
    
    
}