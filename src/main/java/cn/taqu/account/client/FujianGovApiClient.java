/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.client;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import lombok.Data;

/**
 * 福建省功能更数据资源统一开放平台 https://data.fujian.gov.cn/
 * 
 * <AUTHOR>
 * 2024年5月11日上午9:39:33
 */
@Component
public class FujianGovApiClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(FujianGovApiClient.class);


	@Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

	public FujianGovApiClient(){
		super();
	}
	
	private static BaseConfig baseConfig;
	
	private static CheckResidenceInfoConfig checkResidenceInfoConfig;
	
	public static void initConfig(String value){
	    baseConfig = JsonUtils.stringToObject(value, new TypeReference<BaseConfig>() {});
	    LOGGER.info("初始化FujianGovApiClient.initConfig={}", value);
	}
	
	public static void initCheckResidenceInfoConfig(String value){
	    checkResidenceInfoConfig = JsonUtils.stringToObject(value, new TypeReference<CheckResidenceInfoConfig>() {});
	    LOGGER.info("初始化FujianGovApiClient.initCheckResidenceInfoConfig={}", value);
	}
	
	@Data
	public static class BaseConfig{
	    private String clientId;
	    private String secret;
	}
	
	@Data
	public static class CheckResidenceInfoConfig{
	    private String url;
	    private String sirc_query_cause;
	    private String sirc_approve_name;
	    private String sirc_approve_id;
	    private String sirc_ip;
	    private String sirc_mac_address;
	    private String sirc_approve_phone;
	}

    public CheckResidenceInfoResponse checkResidenceInfo(String certName, String certNo) {
        CheckResidenceInfoResponse checkResidenceInfoResponse = new CheckResidenceInfoResponse();
        String orderNo = generateOrderNo();
        checkResidenceInfoResponse.setOrderNo(orderNo);
        
        String serviceUrl = checkResidenceInfoConfig.url; // 具体服务的调用地址, 若是GET请求, 请求参数放在URL后面
        List formParams = new ArrayList<>();
        formParams.add(new BasicNameValuePair("sirc_query_cause", checkResidenceInfoConfig.sirc_query_cause));
        formParams.add(new BasicNameValuePair("sirc_approve_name", checkResidenceInfoConfig.sirc_approve_name));
        formParams.add(new BasicNameValuePair("sirc_approve_id", checkResidenceInfoConfig.sirc_approve_id));
        formParams.add(new BasicNameValuePair("sirc_ip", checkResidenceInfoConfig.sirc_ip));
        formParams.add(new BasicNameValuePair("sirc_mac_address", checkResidenceInfoConfig.sirc_mac_address));
        formParams.add(new BasicNameValuePair("sirc_approve_phone", checkResidenceInfoConfig.sirc_approve_phone));
        formParams.add(new BasicNameValuePair("XM", certName));
        formParams.add(new BasicNameValuePair("GMSFHM", certNo));
        
        int timeout = 3000; // 服务调用延时
        String clientId = baseConfig.clientId; // 替换为用户的client_id
        String timestamp = String.valueOf(Instant.now().toEpochMilli());
        String nonce = orderNo;
        String secret = baseConfig.secret; // 替换为用户的client_secret
        String stringToSign = clientId + timestamp + nonce;
        String sign = null;
        
        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            byte[] keyBytes = secret.getBytes("UTF-8");
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
            sign = Base64.encodeBase64String(hmacSha256.doFinal(stringToSign.getBytes("UTF-8")));
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("",e);
            throw new ServiceException(SysCodeStatus.ERROR);
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("",e);
            throw new ServiceException(SysCodeStatus.ERROR);
        } catch (InvalidKeyException e) {
            LOGGER.error("",e);
            throw new ServiceException(SysCodeStatus.ERROR);
        }

        HttpClientBuilder httpClientBuilder = HttpClients.custom().useSystemProperties();
        CloseableHttpClient httpClient = httpClientBuilder.build();

        // 根据服务的具体请求方法构造合适的请求方法对象，此处以POST方法为例说明
        HttpPost requestMethod = new HttpPost(serviceUrl);
        RequestConfig config = RequestConfig.custom().setConnectTimeout(timeout).build();
        requestMethod.setConfig(config);
        requestMethod.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        requestMethod.addHeader("X-Client-Id", clientId);
        requestMethod.addHeader("X-Timestamp", timestamp);
        requestMethod.addHeader("X-Nonce", nonce);
        requestMethod.addHeader("X-Signature", sign);
        // 创建请求体中的表单参数（如果需要）
//        List formParams = new ArrayList<>();
        
        // 设置请求体的表单参数（如果需要）
        UrlEncodedFormEntity entity;
        try {
            entity = new UrlEncodedFormEntity(formParams, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("",e);
            throw new ServiceException(SysCodeStatus.ERROR);
        }
        requestMethod.setEntity(entity);
        CloseableHttpResponse response;
        try {
            response = httpClient.execute(requestMethod);
        } catch (IOException e) {
            LOGGER.error("",e);
            throw new ServiceException(SysCodeStatus.ERROR);
        }
        // 处理请求结果
        HttpEntity responseEntity = response.getEntity();
        String responseBody;
        try {
            responseBody = EntityUtils.toString(responseEntity, "UTF-8");
            LOGGER.info("港澳台居住证校验结果：{}", responseBody);
            FujianGovApiResponse fujianGovApiResponse = JsonUtils.stringToObject(responseBody, new TypeReference<FujianGovApiResponse>() {});
            // 处理结果 
            if(fujianGovApiResponse.fail()) {
                LOGGER.warn("认证失败，{}", responseBody);
                checkResidenceInfoResponse.setSuccess(false);
                throw new ServiceException(CodeStatus.IDENTITY_FAIL);
            }else {
                Object data = fujianGovApiResponse.getData();
                // 核查结果:0:否;1:是，在有效期内;2:是，超出有效期:3:"被核查人公民身份号码"有在福建省港澳台居住证信息里(不确定是否超出有效期)，但"被核查人姓名"与被核查人公民身份号码"不匹配!
                String dataStr = String.valueOf(data);
                
                if(Objects.equals(dataStr, "0")) {
                    // 0:否;
                    LOGGER.warn("认证失败，{}", responseBody);
                    checkResidenceInfoResponse.setSuccess(false);
                    throw new ServiceException(CodeStatus.IDENTITY_FAIL);
                } else if(Objects.equals(dataStr, "1")) {
                    // 1:是，在有效期内
                    checkResidenceInfoResponse.setSuccess(true);
                } else if(Objects.equals(dataStr, "2")) {
                    // 2:是，超出有效期
                    LOGGER.warn("认证失败，{}", responseBody);
                    checkResidenceInfoResponse.setSuccess(true);
                    throw new ServiceException(CodeStatus.IDENTITY_FAIL);
                } else if(Objects.equals(dataStr, "3")) {
                    // 3:"被核查人公民身份号码"有在福建省港澳台居住证信息里(不确定是否超出有效期)，但"被核查人姓名"与被核查人公民身份号码"不匹配
                    LOGGER.warn("认证失败，{}", responseBody);
                    checkResidenceInfoResponse.setSuccess(false);
                    throw new ServiceException(CodeStatus.IDENTITY_FAIL);
                }else {
                    // 未定义的结果
                    LOGGER.warn("认证失败，未定义结果，{}", responseBody);
                    checkResidenceInfoResponse.setSuccess(false);
                    throw new ServiceException(CodeStatus.IDENTITY_FAIL);
                }
                
            }
            
        } catch (ParseException | IOException e) {
            LOGGER.error("",e);
            throw new ServiceException(SysCodeStatus.ERROR);
        }
        
        return checkResidenceInfoResponse;
    }

    /**
     * 订单号前缀
     */
    private static String ORDERNO_PREFIX = "FJGOV";

    /**
     * 腾讯云订单号随机算法
     * 前缀 + 毫秒时间戳 + 14位随机数
     *
     * @return
     */
    public static String generateOrderNo() {
        return new StringBuffer().append(ORDERNO_PREFIX).append(System.currentTimeMillis()).append(RandomStringUtils.randomAlphanumeric(14)).toString();
    }
    
    public static class CheckResidenceInfoResponse{
        private boolean success;
        private String orderNo;
        public boolean isSuccess() {
            return success;
        }
        public void setSuccess(boolean success) {
            this.success = success;
        }
        public String getOrderNo() {
            return orderNo;
        }
        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }
    }
    
    public static class FujianGovApiResponse{
        private String code;
        private String msg;
        private Object data;
        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
        public String getMsg() {
            return msg;
        }
        public void setMsg(String msg) {
            this.msg = msg;
        }
        public Object getData() {
            return data;
        }
        public void setData(Object data) {
            this.data = data;
        }
        
        public boolean success() {
            return "200".equals(this.code);
        }
        
        public boolean fail() {
            return !success();
        }
    }
}
