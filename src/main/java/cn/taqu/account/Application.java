package cn.taqu.account;

import cn.taqu.account.client.mp.MPAccountRequest;
import cn.taqu.account.monitor.TaquMetricesServlet;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.configuration.FilterConfiguration;
import cn.taqu.core.configuration.properties.ServiceProperties;
import cn.taqu.core.etcd.EtcdListener;
import cn.taqu.core.etcd.EtcdPropertiesFactoryBean;
import cn.taqu.sentinel.annotation.EnableSentinelNacosSource;
import com.taqu.mp.account.client.MPAccountClient;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.spring.autoconfigure.MeterRegistryCustomizer;
import org.apache.coyote.http11.Http11NioProtocol;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.boot.context.embedded.EmbeddedServletContainerCustomizer;
import org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ErrorPage;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.sql.DataSource;

@EnableSentinelNacosSource
@EnableAsync
@ServletComponentScan
@ComponentScan(
    value = "cn.taqu",
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
        @ComponentScan.Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = FilterConfiguration.class),
    }
)
@SpringBootApplication
@EnableConfigurationProperties(ServiceProperties.class)
@ConfigurationProperties
public class Application {
    private Logger logger = LoggerFactory.getLogger(Application.class);

    @Profile(value = "dev")
    @Bean(name = "etcdPropertiesFactoryBean")
    public EtcdPropertiesFactoryBean etcdPropertiesFactoryBeanDev(@Qualifier("accountEtcdListener") EtcdListener listener) {
        logger.info("------dev------ :accountEtcdListener...");
        EtcdPropertiesFactoryBean etcdPropertiesFactoryBean = new EtcdPropertiesFactoryBean();
        etcdPropertiesFactoryBean.setListener(listener);
        etcdPropertiesFactoryBean.setIgnoreResourceNotFound(true);
        etcdPropertiesFactoryBean.setLocations(new ClassPathResource("application.properties"),
                new ClassPathResource("application-dev.properties"));
        return etcdPropertiesFactoryBean;
    }

    @Profile(value = "test")
    @Bean(name = "etcdPropertiesFactoryBean")
    public EtcdPropertiesFactoryBean etcdPropertiesFactoryBeanTest(@Qualifier("accountEtcdListener") EtcdListener listener) {
        logger.info("------test------ :accountEtcdListener...");
        EtcdPropertiesFactoryBean etcdPropertiesFactoryBean = new EtcdPropertiesFactoryBean();
        etcdPropertiesFactoryBean.setListener(listener);
        etcdPropertiesFactoryBean.setIgnoreResourceNotFound(true);
        etcdPropertiesFactoryBean.setLocations(new ClassPathResource("application.properties"),
                new ClassPathResource("application-test.properties"));
        return etcdPropertiesFactoryBean;
    }

    @Profile(value = "prod")
    @Bean(name = "etcdPropertiesFactoryBean")
    public EtcdPropertiesFactoryBean etcdPropertiesFactoryBeanProd(@Qualifier("accountEtcdListener") EtcdListener listener) {
        logger.info("------prod------ :accountEtcdListener...");
        EtcdPropertiesFactoryBean etcdPropertiesFactoryBean = new EtcdPropertiesFactoryBean();
        etcdPropertiesFactoryBean.setListener(listener);
        etcdPropertiesFactoryBean.setIgnoreResourceNotFound(true);
        etcdPropertiesFactoryBean.setLocations(new ClassPathResource("application.properties"),
                new ClassPathResource("application-prod.properties"));
        return etcdPropertiesFactoryBean;
    }

    @Bean(name = "primaryJdbcTemplate")
    @Primary
    public JdbcTemplate primaryJdbcTemplate(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(dataSource);
        return jdbcTemplate;
    }

    @Bean
    public EmbeddedServletContainerCustomizer containerCustomizer() {
             return container -> {
                TomcatEmbeddedServletContainerFactory tomcatFactory = (TomcatEmbeddedServletContainerFactory)container;
                tomcatFactory.addConnectorCustomizers(connector -> {
                    Http11NioProtocol protocol = (Http11NioProtocol)connector.getProtocolHandler();
                    protocol.setConnectionTimeout(20000);
                });

                container.addErrorPages(new ErrorPage(HttpStatus.NOT_FOUND, "/error/404"));
                container.addErrorPages(new ErrorPage(HttpStatus.BAD_REQUEST, "/error/400"));
                container.addErrorPages(new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR, "/error/500"));
                container.addErrorPages(new ErrorPage(Throwable.class, "/error/500"));
            };
    }

    /**
     * 线程、JVM、CPU、内存等基础参数监控
     * @return
     */
    @Bean
    MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config().commonTags("application", "j2");
    }

    public static void main(String[] args) {
        ConfigurableApplicationContext run = SpringApplication.run(Application.class, args);
        String digest = EncryptUtil.sm3("李春兰");
        System.out.println(digest);
    }

    @Bean
    public ServletRegistrationBean MetricsServlet(){
        return new ServletRegistrationBean(new TaquMetricesServlet(), "/metrics");
    }

    @Bean
    public MPAccountClient mpAccountClient() {
        MPAccountRequest mpAccountRequest = new MPAccountRequest();
        return new MPAccountClient(mpAccountRequest, Boolean.TRUE); // QQ渠道采用旧版的方式
    }

}
