package cn.taqu.account.special;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;

import cn.taqu.core.utils.JsonUtils;

/**
 * 对于一些特殊账号处理
 * 
 * <AUTHOR>
 * @date 2021/09/10
 */
public class SpecialAccountHandler {
    private static Logger LOGGER = LoggerFactory.getLogger(SpecialAccountHandler.class);
    
    /**
     * 审核人员uuid 列表 2021.09.10
     */
    private static final List<String> AUDIT_ACCOUNT_LIST = Lists.newArrayList();

    /**
     * 是否开启特殊审核人员
     */
    private static boolean IS_OPEN_AUDIT_ACCOUNT = false;
    
    /**
     * 审核人员 手机号列表
     */
    private static final List<String> AUDIT_MOBILE_LIST = Lists.newArrayList();
    
    /**
     * 审核人员 手机号可用验证码
     */
    private static final List<String> AUDIT_MOBILE_CODE_LIST = Lists.newArrayList();    
    /**
     * 更新审核人员列表
     * 
     * @param accountUuids
     */
    public static void updateAuditAccountList(String json) {
        if (StringUtils.isBlank(json)) {
            LOGGER.warn("未设置特殊审核人员");
            return;
        }
        
        Map<String, Object> map = JsonUtils.stringToObject(json, new TypeReference<Map<String, Object>>(){});
        String accountUuids = MapUtils.getString(map, "auditAccountList", "");
        IS_OPEN_AUDIT_ACCOUNT = MapUtils.getBoolean(map, "isOpenAuditAccount", false);
        String accountMobile = MapUtils.getString(map, "auditMobileList", "");
        String accountMobileCode = MapUtils.getString(map, "auditMobileCodeList", "");
        AUDIT_ACCOUNT_LIST.clear();
        AUDIT_ACCOUNT_LIST.addAll(Splitter.on(",").splitToList(accountUuids));
        AUDIT_MOBILE_LIST.clear();
        AUDIT_MOBILE_LIST.addAll(Splitter.on(",").splitToList(accountMobile));
        AUDIT_MOBILE_CODE_LIST.clear();
        AUDIT_MOBILE_CODE_LIST.addAll(Splitter.on(",").splitToList(accountMobileCode));
    }
    
    /**
     * 是否是审核人员账号
     * 
     * @return true - 是审核账号   false - 非审核账号
     */
    public static boolean isAuditAccount(String accountUuid) {
        if (IS_OPEN_AUDIT_ACCOUNT && AUDIT_ACCOUNT_LIST.contains(accountUuid)) {
            return true;
        }
        return false;
    }
    
    /**
     * 是否是审核人员手机号
     * 
     * @return true - 是审核账号   false - 非审核账号
     */
    public static boolean isAuditMobile(String mobile, String code) {
        if (IS_OPEN_AUDIT_ACCOUNT && AUDIT_MOBILE_LIST.contains(mobile) && AUDIT_MOBILE_CODE_LIST.contains(code)) {
            return true;
        }
        return false;
    }
    
    
}
