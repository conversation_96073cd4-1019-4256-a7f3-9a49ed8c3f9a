package cn.taqu.account.cron;

import cn.taqu.account.common.AliyunLiveFaceDetectStatus;
import cn.taqu.account.common.AllureSceneEnum;
import cn.taqu.account.common.PhotoOrignCheckEnum;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RiskSafeIdConstant;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dao.AliyunLiveFaceDetectDao;
import cn.taqu.account.dto.ImgVectorDetectDto;
import cn.taqu.account.dto.ImgVectorDetectRespDto;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.AccountsLife;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.AliyunLiveFaceDetect;
import cn.taqu.account.service.*;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.Identities;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户真人认证处理
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-07 09:48
 */
@Component
@Lazy(false)
@Slf4j
public class AccountRealPersonCertificationTask {

    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;
    @Autowired
    private AccountsPhotoDao accountsPhotoDao;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsLifeService accountsLifeService;

    @Scheduled(fixedDelay = 2000)
    public void processAccountLocationInfo1() {
        this.getAndProcess();
    }

    public void getAndProcess(){
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                MqResponse mqResponse = mqClient.pop(CommConst.MQ_ACCOUNT_REAL_PERSON_CERTIFICATION);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取用户位置信息数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }

                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                // 用户uuid
                String accountUuid = MapUtils.getString(map, "account_uuid");
                // 业务处理
                handler(accountUuid);
                // 我的生活照片状态处理
                accountLifeHandler(accountUuid);
                SpringContextHolder.getBean(AllureService.class).refreshAllure(accountUuid);
                SpringContextHolder.getBean(AllureService.class).refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
                mqResponse = mqClient.remove(CommConst.MQ_ACCOUNT_REAL_PERSON_CERTIFICATION, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除用户位置信息:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.error("从TQMQ获取到的用户位置信息:" + msgBody + "处理失败", e);
        }
    }

    /**
     * 业务处理
     * TODO 处理逻辑有点怪，有空再优化
     * @param accountUuid
     */
    private void handler(String accountUuid) {
        log.info("开始处理account_real_person_certification,accountUuid={}", accountUuid);
        AliyunLiveFaceDetect liveFaceDetect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        if(liveFaceDetect == null){
            return;
        }
        // 查找用户 create_time 用于判断用户是否存在
        Object createTimeObj = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "create_time");
        Long createTime = null;
        if(null != createTimeObj){
            try{
                createTime = Long.parseLong(createTimeObj + "");
            }catch (Exception e){
                log.warn("uuid={} info cache create_time save err. {}", accountUuid, createTimeObj);
            }
        }
        // 缓存查不到 取一次数据库
        if(createTimeObj == null){
            Long createTimeDb = accountsManager.getCreateTimeByUuid(accountUuid);
            if(createTimeDb != null){
                createTime = createTimeDb;
            }else{
                log.warn("doAccountPhotoCheck 用户不存在 uuid={}", accountUuid);
                return;
            }
        }

        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuid(accountUuid);
        // 相册
        List<AccountsPhoto> reCheckPhotos = new ArrayList<>();
        for (AccountsPhoto photo : photoList) {
            if(photo.getSeq() == 1){
                continue;
            }
            Integer verifyStatus = photo.getVerify_status();
            if(verifyStatus == 1){
                accountsPhotoDao.updateReviewCoverStatus(photo.getId());
                // 移除key，重新审核
                String content = PhotoOrignCheckEnum.REAL_PERSON_COVER.getValue() + "|" + photo.getId() + "|" + AvatarHandleService.getAvatarOfSavePhoto(photo.getPhoto_url());
                String pushReviewMd5 = Md5Util.encryptSHA1(content);
                accountStringRedisTemplate.delete(pushReviewMd5);
                // todo ？风控向量上报，感觉直接在审核回调的地方上报就好了
//                ImgVectorDetectDto dto = new ImgVectorDetectDto();
//                dto.setSafeId(RiskSafeIdConstant.REAL_PERSON_COVER);
//                dto.setBizId(Identities.uuid2());
//                dto.setImageUrl(Collections.singletonList(avatarHandleService.getAvatarByVersion(photo, null)));
//                dto.setSenderUuid(uuid);
//                RiskSafeService.imageVectorAdd(dto);

//                SpringContextHolder.getBean(AccountsPhotoService.class).imgVectorDetect(photo, true);
                reCheckPhotos.add(photo);
            }else{
                if(photo.getSeq() != 1 && AccountsPhotoService.PHOTO_UNCHECK_STATUS_ARR.contains(photo.getStatus())){
                    accountsPhotoDao.updateCoverOnlyAutoCheck(photo.getId());
                }
            }
        }
        log.info("真人后.相册复审.uuid={}.photoList={}", accountUuid, JSON.toJSONString(photoList));
        for (AccountsPhoto reCheckPhoto : reCheckPhotos) {
            log.info("真人后.相册复审.uuid={}.photoId={}", accountUuid, reCheckPhoto.getId());
        }
        accountsService.checkAccountEmptyFacePhoto(accountUuid);
    }

    public void accountLifeHandler(String uuid) {
        try {
            List<AccountsLife> list = accountsLifeService.getAccountsLifeByAccountUuid(uuid, false);
            list = list.stream()
                .filter(l -> AccountsLifeService.REAL_PERSON_SEQ.contains(l.getSeq()))
                .filter(l -> AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus().equals(l.getVerifyStatus()))
                .collect(Collectors.toList());
            if (list.isEmpty()) {
                log.info("真人认证后，跳过我的生活真人照片比对{}", uuid);
                return;
            }
            for (AccountsLife life : list) {
                Integer status = accountsLifeService.detectPhoto(uuid, life.getPhotoUrl());
                if (AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus().equals(status)) {
                    continue;
                }
                life.setVerifyStatus(status);
                accountsLifeService.updateAccountLife(life);
            }
        } catch (Exception e) {
            log.warn("真人认证后，我的生活真人照片比对处理失败", e);
        }

    }
}
