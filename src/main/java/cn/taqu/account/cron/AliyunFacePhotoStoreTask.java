package cn.taqu.account.cron;

import cn.taqu.account.common.*;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RiskCertificationTypeEnum;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dao.AliyunLiveFaceDetectDao;
import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.AliyunFacePhotoCompareLog;
import cn.taqu.account.model.AliyunLiveFaceDetect;
import cn.taqu.account.service.*;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 业务不一定是阿里云，早期命名原因
 * 
 * <AUTHOR>
 * 2023年10月27日上午11:23:57
 */
@Component
@Lazy(false)
public class AliyunFacePhotoStoreTask {

    public static final String MQ_NAME = "aliyun_facephoto_store_queue";

//    private static String ALIYUN_TMP_PHOTO_DOMAIN = "aliyuncs.com";

//    @Autowired
//    private AliyunFacePhotoCompareLogService aliyunFacePhotoCompareLogService;
    @Autowired
    private AliyunLiveFaceDetectService aliyunLiveFaceDetectService;
    @Autowired
    private AccountsPhotoDao accountsPhotoDao;
    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;
    @Autowired
    private BuryService buryService;
    @Autowired
    private AccountsInfoService accountsInfoService;

    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunFacePhotoStoreTask.class);

    /**
     * 保存人脸活体认证成功的底图和进行相册所有图片的人脸比对
     */
    @Scheduled(fixedDelay = 2000)
    public void process() {
        if (TaskHelper.podStopping()) {
            return;
        }
        int i = 0;
        while (i++ <= 1000) {
            if (TaskHelper.podStopping()) {
                return;
            }
            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).pop(MQ_NAME);
            if (mqResponse.fail()) {
                LOGGER.error("从aliyun_facephoto_store_queue获取人脸活体底图信息失败， {}-{}", mqResponse.getCode(), mqResponse.getMsg());
                break;
            }
            String msgBody = mqResponse.getMsg_body();
            if (StringUtils.isBlank(msgBody)) {
                break;
            }
            LOGGER.info("获取人脸底图信息={}", msgBody);
            Map<String, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<String, Object>>() {
            });
            // 长事务
//            aliyunLiveFaceDetectService.processSaveAndCompare(map);
            // 短事务（第三方调用移出方法外）
            handle(map);
            SpringContextHolder.getBean(AllureService.class).refreshAllure(MapUtils.getString(map, "accountUuid"));
            SpringContextHolder.getBean(AllureService.class).refreshAllureV2(MapUtils.getString(map, "accountUuid"), AllureSceneEnum.CARD, null);
            mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).remove(MQ_NAME, mqResponse.getMsg_id());
            if (mqResponse.fail()) {
                LOGGER.error("从mq删除bbs获取帖子通过的信息失败={}-{}", mqResponse.getCode(), mqResponse.getMsg());
                break;
            }
        }
    }

    /**
     * 短事务处理
     *
     * @param map
     */
    private void handle(Map<String, Object> map) {
        // 获取人脸底图信息={}
        // 图片转存
        String accountUuid = MapUtils.getString(map, "accountUuid");
        Long detectId = MapUtils.getLong(map, "detectId");
        // 2024.07.11 不带host
        String basePhotoUrl = MapUtils.getString(map, "aliyunTempPhotoUrl");

        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.findById(detectId);
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"profile_verify_status", "face_certification"}, "1", false, false).get(accountUuid);
        String auditSuccess = String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        Boolean isFaceDetect = auditSuccess.equals(infoMap.get("profile_verify_status")) || auditSuccess.equals(infoMap.get("face_certification"));
        if (null == detect || !isFaceDetect) {
            return;
        }
        // [https://sdata-tmp3.oss-cn-zhangjiakou.aliyuncs.com/x/sdata-tmp/0f8a/27191/46639555_76098674_860f615a881141f3866c0750b5c5e0fc.jpg?Expires=**********&OSSAccessKeyId=LTAIa7UVTbz8M9A4&Signature=mHbwBBzNU1mHe3PWlaOYIwOitxA%3D
//        String qiniuPhotoUrl = "";
        // 如果aliyunTempPhotoUrl 包含 avatar.jiaoliuqu.com 这种情况是客户端走腾讯云，上传的图片是七牛云上的图片，不需要重传
        // 2024.07.11 优化只处理去域名，上线时队列可能有旧数据
        String qiniuPhotoUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(basePhotoUrl);
//            qiniuPhotoUrl = aliyunLiveFaceDetectService.getCustomImageUrl(aliyunTempPhotoUrl);
            //如果七牛云转存底图失败，则把认证的图片设置为底图，保证后续也可以成功进行图片比对认证
//            if (null == qiniuPhotoUrl) {
//                qiniuPhotoUrl = detect.getVerify_photo_url();
//            }
//        }
        LOGGER.info("AliyunFacePhotoStoreTask:handle,uuid={},detectId=[{}],aliyunTempPhotoUrl=[{}],qiniuUrl=[{}]", accountUuid, detectId, basePhotoUrl, qiniuPhotoUrl);

        //该用户相册与底图的认证，需保存认证的日志
        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuid(accountUuid);
        if (CollectionUtils.isEmpty(photoList)) {
            return;
        }
        List<Long> successIds = Lists.newArrayList();

        JSONArray updatePhotoList = new JSONArray();
        List<AliyunFacePhotoCompareLog> addFacePhotoCompareLogList = new ArrayList<>();
        String fullQiniuPhotoUrl = ToolsService.getPrivateBasePic(qiniuPhotoUrl);
        AtomicLong successVerifyPhotoCount = new AtomicLong();
        photoList.stream().forEach(photo -> {
            Float similarityScore = 0.0f;
            boolean similarity = false;
            CompareFaceDto dto = new CompareFaceDto();
            String photoUrl = ToolsService.addPhotoUrlPreDomain(photo.getPhoto_url());
            String verifyPhotoUrl = detect.getVerify_photo_url();

            if (photoUrl.equals(verifyPhotoUrl) || verifyPhotoUrl.contains(photoUrl)) {
                // 阿里活体和图片对比的结果可能不一致，所以此处只要活体成功，就默认对比通过。否则会出现活体提示成功，但是图片对比失败的情况。
                similarityScore = AliyunLiveFaceDetectService.DEFAULT_SIMILARITY_SCORE;
                similarity = true;
            } else {
                
                // 2024.07.04 对比前底图获取是有图片地址去比较
                String privateBasePic = ToolsService.getPrivateBasePic(fullQiniuPhotoUrl);
                dto = aliyunLiveFaceDetectService.comparePic(photoUrl, privateBasePic, accountUuid, RiskCertificationTypeEnum.UNKNOWN);
                similarity = dto.isSimilarity();
                similarityScore = dto.getScore();
            }

            if (similarity) {
                photo.setVerify_status(AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus());
                photo.setError_msg("");
                if (AliyunLiveFaceDetectService.PROFILE_SEQ_NUM.equals(photo.getSeq())) {
                    aliyunLiveFaceDetectService.setProfileVerifyStatustCache(photo, AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus(), null);
                }
                successVerifyPhotoCount.getAndIncrement();
                successIds.add(photo.getId());
            } else {
                photo.setVerify_status(AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus());
                //默认的检测错误提示
                String errorMsg = CodeStatus.ALIYUN_VERIFY_FALI_MSG.getReasonPhrase();
                if (null != similarityScore && AliyunLiveFaceDetectService.NO_FACE_ERROR_CODE.equals(dto.getCode())) {
                    errorMsg = CodeStatus.ALIYUN_NON_FACE_DETECT.getReasonPhrase();
                }
                photo.setError_msg(errorMsg);
                if (AliyunLiveFaceDetectService.PROFILE_SEQ_NUM.equals(photo.getSeq())) {
                    aliyunLiveFaceDetectService.setProfileVerifyStatustCache(photo, AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus(), errorMsg);
                }
            }

            JSONObject photoJson = new JSONObject();
            photoJson.put("id", photo.getId());
            photoJson.put("verifyStatus", photo.getVerify_status());
            photoJson.put("errorMsg", photo.getError_msg());
            photoJson.put("photoUrl", photo.getPhoto_url());
            updatePhotoList.add(photoJson);

            AliyunFacePhotoCompareLog log = new AliyunFacePhotoCompareLog();
            log.setAccount_uuid(photo.getAccount_uuid());
            log.setAccounts_photo_id(photo.getId());
            log.setBase_photo_url(qiniuPhotoUrl);
            log.setSimilarity_score(similarityScore);
            log.setStatus(photo.getVerify_status());
            //直接保存即可，PHOTO_URL_PRE_DOMAIN+这边的url即可直接得到绝对路径的图片地址
            log.setVerify_photo_url(photo.getPhoto_url());
            log.setCreate_time(DateUtil.currentTimeSeconds());
            log.setError_msg(photo.getError_msg());
            log.setAccount_gender(LiveFaceDetectGenderEnum.DEFAULT.getValue());
            log.setBase_photo_gender(LiveFaceDetectGenderEnum.DEFAULT.getValue());
            log.setGender_compare_result(LiveFaceDetectGenderCompareResultEnum.DEFAULT.getValue());
            //log.setUpdate_time(DateUtil.currentTimeSeconds());
            addFacePhotoCompareLogList.add(log);
        });
        // 更新呢列表 photoUpdateList
        long t1 = System.currentTimeMillis();
        aliyunLiveFaceDetectService.processSaveAndCompareV2(accountUuid, photoList, qiniuPhotoUrl, detectId, successVerifyPhotoCount.get(), updatePhotoList, addFacePhotoCompareLogList);
        long t2 = System.currentTimeMillis();

        LOGGER.info("AliyunFacePhotoStoreTask:handle，qiniuImg=[{}],图片url=[{}],successVerifyPhotoCount=[{}],processSaveAndCompareV2 cost time=[ {} ms]", qiniuPhotoUrl, fullQiniuPhotoUrl, successVerifyPhotoCount, (t2 - t1));
        buryService.pushRealPersonCertification(accountUuid);
    }

}
