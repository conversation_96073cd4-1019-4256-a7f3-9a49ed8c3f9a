package cn.taqu.account.cron;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.service.AccountBizDataService;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;

/**
 * 业务数据消费定时任务
 *
 * <AUTHOR>
 * @date 2021/03/08
 */
@Component
@Lazy(false)
public class AccountBizDataTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountBizDataTask.class);

    @Autowired
    private AccountBizDataService accountBizDataService;


    @Scheduled(fixedDelay = 1000)
    public void processReview1() {
        this.doTask();
    }
    @Scheduled(fixedDelay = 1000)
    public void processReview2() {
        this.doTask();
    }
    @Scheduled(fixedDelay = 1000)
    public void processReview3() {
        this.doTask();
    }

	private void doTask() {
	    for (int i = 0; i < 1000; i++) {
            if(TaskHelper.podStopping()){
                return;
            }
            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).pop(CommConst.MQ_ACCOUNT_BIZ_DATA);
            if (mqResponse.fail()) {
                LOGGER.error("从{}获取数据失败，{}-{}", CommConst.MQ_ACCOUNT_BIZ_DATA, mqResponse.getCode(), mqResponse.getMsg());
                break;
            }
            String msgBody = mqResponse.getMsg_body();
            if(StringUtils.isBlank(msgBody)) {
                break;
            }
            accountBizDataService.processMsgBody(msgBody);

            mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).remove(CommConst.MQ_ACCOUNT_BIZ_DATA, mqResponse.getMsg_id());
            if (mqResponse.fail()) {
                LOGGER.error("从{}删除数据{}失败，{}-{}",CommConst.MQ_ACCOUNT_BIZ_DATA, mqResponse.getMsg_id(), mqResponse.getCode(), mqResponse.getMsg());
                break;
            }
        }
	}

}
