package cn.taqu.account.cron;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import cn.taqu.account.service.AccountsPhotoService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Component
public class SingletonTask {

    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private AccountsPhotoService accountsPhotoService;

//    @Scheduled(cron = "0 59 15 05 07 ?")
    public void task2() {
//        String localEnv = LocalConfUtil.getLocalEnv();
//        int year = DateUtil.year(new Date());
//        if ("gray".equals(localEnv) && year == 2023) {
//            List<String> uuids = Arrays.asList(
//                    "bgiidfcdaadghihc",
//                    "bgiidfdjgaiebgbc",
//                    "bgiidfeeedjifbcj",
//                    "bgiidffbedjjgjad",
//                    "bgiidgieiaicjbih",
//                    "bgiidgijbffighcc",
//                    "bgiidgjcacfigaeb",
//                    "bgiidgjeiegeadgg",
//                    "bgiidgjghaadgfjd",
//                    "bgiidgjijfhijije",
//                    "bgiicigjeeijgdfh",
//                    "bgiicihbabfecfgb",
//                    "bgiicihcdgdbchdb",
//                    "bgiicihdfjdcdjai",
//                    "bgiicjaecdibgdic",
//                    "bgiidadjgghefjga",
//                    "bgiidaeifhgecijj",
//                    "bgiidafahchhdbfa",
//                    "bgiidafdbfdgfedi",
//                    "bgiidafejhdgfghj");
//
//            for (String uuid : uuids) {
//                String photoUrl = uuid + ".png";
//
//                accountsPhotoService.savePhoto(uuid, photoUrl);
//
//                String redisKey = RedisKeyConstant.ACCOUNT_PHOTO.setArg(uuid);
//                accountStringRedisTemplate.delete(redisKey);
//
//                Map<String, String> cacheInfo = new HashMap<>();
//                cacheInfo.put("avatar", photoUrl);
//                cacheInfo.put("avatar_origin", photoUrl);
//                accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), cacheInfo);
//            }
//        }
    }
}
