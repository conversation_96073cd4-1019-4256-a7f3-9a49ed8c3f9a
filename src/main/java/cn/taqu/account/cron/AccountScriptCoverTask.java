package cn.taqu.account.cron;

import cn.taqu.account.common.AliyunLiveFaceDetectStatus;
import cn.taqu.account.common.ScriptRunStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dto.TencentImgQualityDTO;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.service.AccountsService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.account.service.MessageService;
import cn.taqu.account.thread.TencentImgQualityCall;
import cn.taqu.account.thread.ThirdPartFactory;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-30 11:41
 */
@Slf4j
@Component
@Lazy(false)
public class AccountScriptCoverTask {

    private static String TASK_NAME = "account_script_avatar_task";

    @Autowired
    private AccountsPhotoDao accountsPhotoDao;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private MessageService messageService;
    @Autowired
    private TaskHelper taskHelper;
    @Autowired
    private AccountsService accountsService;

    @Scheduled(fixedDelay = 1000, initialDelay = 200)
    public void processAccountScriptCover1() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 1, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(1);
        }
    }
    @Scheduled(fixedDelay = 1000, initialDelay = 210)
    public void processAccountScriptCover11() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 11, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(11);
        }
    }
    @Scheduled(fixedDelay = 1000, initialDelay = 220)
    public void processAccountScriptCover21() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 21, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(21);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 400)
    public void processAccountScriptCover2() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 2, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(2);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 410)
    public void processAccountScriptCover12() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 12, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(12);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 420)
    public void processAccountScriptCover22() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 22, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(22);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 600)
    public void processAccountScriptCover3() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 3, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(3);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 610)
    public void processAccountScriptCover13() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 13, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(13);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 620)
    public void processAccountScriptCover23() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 23, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(23);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 800)
    public void processAccountScriptCover4() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 4, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(4);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 810)
    public void processAccountScriptCover14() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 14, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(14);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 820)
    public void processAccountScriptCover24() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 24, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(24);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1000)
    public void processAccountScriptCover5() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 5, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(5);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1010)
    public void processAccountScriptCover15() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 15, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(15);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1020)
    public void processAccountScriptCover25() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 25, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(25);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1100)
    public void processAccountScriptCover6() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 6, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(6);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1110)
    public void processAccountScriptCover16() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 16, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(16);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1120)
    public void processAccountScriptCover26() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 26, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(26);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1300)
    public void processAccountScriptCover7() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 7, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(7);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1310)
    public void processAccountScriptCover17() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 17, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(17);
        }
    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1320)
//    public void processAccountScriptCover27() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 27, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(27);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1500)
//    public void processAccountScriptCover8() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 8, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(8);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1510)
//    public void processAccountScriptCover18() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 18, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(18);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1520)
//    public void processAccountScriptCover28() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 28, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(28);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1700)
//    public void processAccountScriptCover9() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 9, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(9);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1710)
//    public void processAccountScriptCover19() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 19, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(19);
//        }
//    }
//    @Scheduled(fixedDelay = 1000, initialDelay = 1720)
//    public void processAccountScriptCover29() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 29, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(29);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1900)
//    public void processAccountScriptCover10() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 10, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(10);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1910)
//    public void processAccountScriptCover20() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 20, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(20);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1920)
//    public void processAccountScriptCover30() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 30, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(30);
//        }
//    }

    public void getAndProcess(Integer index) {
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            for (int j = 0; j < 1000; j++) {
                if (TaskHelper.podStopping()) {
                    return;
                }
                MqResponse mqResponse = mqClient.pop(CommConst.MQ_SRCIPT_COVER);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取用户头像质量检测失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }

                // {"account_uuid":"t0nhoul565i"}
                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                // 用户uuid
                String accountUuid = MapUtils.getString(map, "account_uuid");
                handler(accountUuid);

                mqResponse = mqClient.remove(CommConst.MQ_SRCIPT_COVER, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除用户头像质量检测:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.error("从TQMQ获取用户头像质量检测:" + msgBody + "处理失败", e);
        } finally {
            taskHelper.expireTask(TASK_NAME + index, 0L, TimeUnit.SECONDS);
        }
    }

    public void handler(String accountUuid) {
//        // 已经跑过
        Object scriptQuality = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "cover_script_quality");
        if(Objects.equals(scriptQuality, ScriptRunStatus.DONE.getStatus())){
            log.info("已经跑过相册uuid={}, {},{}", accountUuid, scriptQuality, ScriptRunStatus.DONE.getStatus());
            return;
        }

        List<String> deletePhotoUrl = new ArrayList<>();
        List<AccountsPhoto> accountsAllPhoto = accountsPhotoDao.findByUuid(accountUuid);
        if(CollectionUtils.isEmpty(accountsAllPhoto)){
            log.warn("活跃用户重扫相册质量.相册数据异常.uuid={}", accountUuid);
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "cover_script_quality", "1");
            return;
        }
        accountsAllPhoto.remove(0);

        try {
            for (AccountsPhoto cover : accountsAllPhoto) {
                if (Objects.equals(cover.getVerify_status(), AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus())) {
                    String coverUrl = cover.getPhoto_url();
                    coverUrl = AvatarHandleService.getAvatarSrcPhotoUrl(coverUrl);
                    Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, coverUrl));
                    if(imgQualityFuture == null){
                        return;
                    }
                    TencentImgQualityDTO tencentImgQualityDTO = imgQualityFuture.get(5, TimeUnit.SECONDS);
                    if(tencentImgQualityDTO == null){
                        return;
                    }
                    Boolean imgQualityPass = tencentImgQualityDTO.isImgQualityPass();
                    if (!imgQualityPass) {
                        deletePhotoUrl.add(cover.getPhoto_url());
                    }
                }
            }
            if(deletePhotoUrl.size() > 0){
                accountsPhotoService.removeUnClearCover(accountUuid, deletePhotoUrl);
                sendSystemMsg(accountUuid);
                log.info("活跃用户重扫相册质量.uuid={}.清理相册url={}", accountUuid, JSON.toJSONString(deletePhotoUrl));
//                // 真人情况
//                if(realPersonCertification.equals("1")){
//                    log.info("活跃用户重扫相册质量.uuid={}.真人.清理相册url={}", accountUuid, JSON.toJSONString(deletePhotoUrl));
//                }else{
//                    // 非真人情况
//                    log.info("活跃用户重扫相册质量.uuid={}.非真人.清理相册url={}", accountUuid, JSON.toJSONString(deletePhotoUrl));
//                }
                accountsService.checkAccountEmptyFacePhoto(accountUuid);
            }else{
                log.info("活跃用户重扫相册质量.uuid={}.无需清理相册", accountUuid);
            }
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "cover_script_quality", "1");
        } catch (Exception e) {
            log.warn("图片质量检测失败,accountUuid={}", accountUuid, e);
        }
    }

    /**
     * 推送小米商户
     * @param accountUuid
     */
    private void sendSystemMsg(String accountUuid) {

        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info="您的相册内容比较模糊，已被清除，可以%s重新上传";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "点击这里");
        contentReplace.put("r", "m=mine&a=album");
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "您的相册内容比较模糊，已被清除");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);

        messageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
    }
}
