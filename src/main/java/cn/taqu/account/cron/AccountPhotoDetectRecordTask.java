package cn.taqu.account.cron;

import cn.taqu.account.constant.AccountPhotoDetectType;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.dto.ShumeiImgCheckResponseDTO;
import cn.taqu.account.dto.TencentImgQualityDTO;
import cn.taqu.account.model.AccountsPhotoImgCheckLog;
import cn.taqu.account.model.AccountsPhotoImgCompareLog;
import cn.taqu.account.model.AccountsPhotoImgQualityLog;
import cn.taqu.account.service.*;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.Md5Util;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 记录图片检测日志
 */
@Component
@Lazy(false)
@Slf4j
public class AccountPhotoDetectRecordTask {

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private AccountsPhotoImgCheckLogService accountsPhotoImgCheckLogService;
    @Autowired
    private AccountsPhotoImgQualityLogService accountsPhotoImgQualityLogService;
    @Autowired
    private AccountsPhotoImgCompareLogService accountsPhotoImgCompareLogService;

    @Scheduled(fixedDelay = 1000, initialDelay = 200)
    public void processAccountLocationInfo1() {
        this.getAndProcess();
    }

    public void getAndProcess(){
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            for (int i = 0; i < 1000; i++) {
                if(TaskHelper.podStopping()){
                    return;
                }
                MqResponse mqResponse = mqClient.pop(CommConst.MQ_ACCOUNT_PHOTO_DETECT_RECORD);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取图片检测结果数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }

                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                // 用户uuid
                String accountUuid = MapUtils.getString(map, "account_uuid");
                String type = MapUtils.getString(map, "type", "");
                Object data = MapUtils.getObject(map, "data", null);
                handler(type, accountUuid, data);

                mqResponse = mqClient.remove(CommConst.MQ_ACCOUNT_PHOTO_DETECT_RECORD, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除获取图片检测结果信息:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.error("从TQMQ获取图片检测结果信息:" + msgBody + "处理失败", e);
        }
    }

    /**
     * 业务处理
     * @param type
     * @param data
     */
    private void handler(String type, String accountUuid, Object data) {

        switch (type){
            case AccountPhotoDetectType.IMG_CHECK:
                handleImgCheck(accountUuid, data);
                break;
            case AccountPhotoDetectType.IMG_QUALITY:
                handleImgQuality(accountUuid, data);
                break;
            case AccountPhotoDetectType.IMG_COMPARE:
                handleImgCompare(accountUuid, data);
                break;
            default:
                log.warn("保存图片检测结果.无法解析数据type={}.uuid={}.data={}", type, accountUuid, JSON.toJSONString(data));
                break;
        }
    }

    /**
     * 处理图片违规
     * @param accountUuid
     * @param data
     */
    private void handleImgCheck(String accountUuid, Object data){
        ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = JSON.parseObject(JSON.toJSONString(data), ShumeiImgCheckResponseDTO.class);
        AccountsPhotoImgCheckLog accountsPhotoImgCheckLog = new AccountsPhotoImgCheckLog();
        accountsPhotoImgCheckLog.setAccountUuid(accountUuid).setPhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(shumeiImgCheckResponseDTO.getPhotoUrl()))
                .setRiskLevel(shumeiImgCheckResponseDTO.getRiskLevel()).setRiskDescription(shumeiImgCheckResponseDTO.getRiskDescription())
                .setThirdOrderNo(shumeiImgCheckResponseDTO.getRequestId()).setResponseStr(shumeiImgCheckResponseDTO.getResponseStr())
                .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
        accountsPhotoImgCheckLogService.merge(accountsPhotoImgCheckLog);
        // 保存到缓存7天
        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO_IMG_CHECK_RESULT.setArg(Md5Util.encryptSHA1(accountUuid + shumeiImgCheckResponseDTO.getPhotoUrl()));
        accountStringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(shumeiImgCheckResponseDTO),7, TimeUnit.DAYS);
    }

    /**
     * 处理图片质量
     * @param accountUuid
     * @param data
     */
    private void handleImgQuality(String accountUuid, Object data){
        TencentImgQualityDTO tencentImgQualityDTO = JSON.parseObject(JSON.toJSONString(data), TencentImgQualityDTO.class);
        AccountsPhotoImgQualityLog imgQualityLog = new AccountsPhotoImgQualityLog();
        imgQualityLog.setAccountUuid(accountUuid).setPhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(tencentImgQualityDTO.getPhotoUrl())).setLongImage(tencentImgQualityDTO.getLongImage())
                .setBlackAndWhite(tencentImgQualityDTO.getBlackAndWhite()).setSmallImage(tencentImgQualityDTO.getSmallImage()).setBigImage(tencentImgQualityDTO.getBigImage())
                .setPureImage(tencentImgQualityDTO.getPureImage()).setClarityScore(tencentImgQualityDTO.getClarityScore()).setAestheticScore(tencentImgQualityDTO.getAestheticScore())
                .setThirdOrderNo(tencentImgQualityDTO.getRequestId()).setResponseStr(JSON.toJSONString(imgQualityLog))
                .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
        accountsPhotoImgQualityLogService.merge(imgQualityLog);

        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO_IMG_QUALITY_RESULT.setArg(Md5Util.encryptSHA1(accountUuid + tencentImgQualityDTO.getPhotoUrl()));
        accountStringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(tencentImgQualityDTO),7, TimeUnit.DAYS);
    }

    /**
     * 处理图片比对日志
     * @param accountUuid
     * @param data
     */
    private void handleImgCompare(String accountUuid, Object data){
        CompareFaceDto compareFaceDto = JSON.parseObject(JSON.toJSONString(data), CompareFaceDto.class);

        AccountsPhotoImgCompareLog photoImgCompareLog = new AccountsPhotoImgCompareLog();
        photoImgCompareLog.setAccountUuid(accountUuid).setBasePhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(compareFaceDto.getBasePhotoUrl())).setVerifyPhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(compareFaceDto.getVerifyPhotoUrl()))
                .setFaceModelVersion(compareFaceDto.getFaceModelVersion()).setSocre(compareFaceDto.getScore())
                .setThirdOrderNo(compareFaceDto.getRequestId()).setResponseStr(JSON.toJSONString(compareFaceDto))
                .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
        accountsPhotoImgCompareLogService.merge(photoImgCompareLog);

        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO_IMG_COMPARE_RESULT.setArg(Md5Util.encryptSHA1(accountUuid + compareFaceDto.getBasePhotoUrl() + compareFaceDto.getVerifyPhotoUrl()));
        accountStringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(compareFaceDto),7, TimeUnit.DAYS);
    }
}
