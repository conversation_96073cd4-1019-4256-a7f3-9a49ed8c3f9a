package cn.taqu.account.cron;

import cn.taqu.account.ApplicationCloseListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class TaskHelper {
	private static final Logger LOGGER = LoggerFactory.getLogger(TaskHelper.class);
	@Qualifier("accountStringRedisTemplate")
	@Autowired
	private StringRedisTemplate accountStringRedisTemplate;

	/**
	 * 判断指定任务是否有在运行
	 * @param taskKey 任务key
	 * @param taskTime 下一次任务支行时间时间
	 * @param timeUnit taskTime的单位
	 * @return true:任务已经运行; false:任务没有运行
	 */
	public boolean isTaskRun(String taskKey, Long taskTime, TimeUnit timeUnit) {
		try {
			boolean isRun = accountStringRedisTemplate.opsForValue().increment(taskKey, 1L) > 1;
			if(taskTime!=null) {
				Long ttl = accountStringRedisTemplate.getExpire(taskKey);
				if(ttl > timeUnit.toSeconds(taskTime)) {
					accountStringRedisTemplate.delete(taskKey);
				} else if(ttl != null && ttl == -1) {
					this.expireTask(taskKey, taskTime, timeUnit);
				}
			}
			if(isRun) {
				LOGGER.debug("任务{}已经在运行中，不再继续运行", taskKey);
				return true;
			}
			return false;
		} catch (Exception e) {
			LOGGER.error("check task run exception", e);
			//抛出异常时返回true
			return true;
		}
	}

	/**
	 * 设置任务过期时间
	 * @param taskKey 任务key
	 * @param taskTime 下一次任务支行时间时间
	 * @param timeUnit taskTime的单位
	 */
	public void expireTask(String taskKey, Long taskTime, TimeUnit timeUnit) {
		try {
			if(taskTime<=0) {
				accountStringRedisTemplate.delete(taskKey);
			} else {
				accountStringRedisTemplate.expire(taskKey, taskTime, timeUnit);
			}
		} catch (Exception e) {
			LOGGER.error("expire task exception", e);
		}
	}

	/**
	 * 节点是否正在销毁
	 * @return
	 */
	public static Boolean podStopping(){
		return ApplicationCloseListener.APPLICATION_STOPPING.get();
	}
}
