package cn.taqu.account.cron;

import cn.taqu.account.common.ScriptRunStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dto.TencentImgQualityDTO;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.service.*;
import cn.taqu.account.thread.TencentImgQualityCall;
import cn.taqu.account.thread.ThirdPartFactory;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-30 11:41
 */
@Slf4j
@Component
@Lazy(false)
public class AccountScriptAvatarTask {

    private static String TASK_NAME = "account_script_avatar_task";
    @Autowired
    private AccountsPhotoDao accountsPhotoDao;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private MessageService messageService;
    @Autowired
    private TaskHelper taskHelper;
    @Autowired
    private AccountsService accountsService;

    @Autowired
    private AccountsInfoService accountsInfoService;

    @Scheduled(fixedDelay = 1000, initialDelay = 200)
    public void processAccountScriptAvatar1() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 1, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(1);
        }
    }
    @Scheduled(fixedDelay = 1000, initialDelay = 210)
    public void processAccountScriptAvatar11() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 11, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(11);
        }
    }
    @Scheduled(fixedDelay = 1000, initialDelay = 220)
    public void processAccountScriptAvatar21() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 21, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(21);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 400)
    public void processAccountScriptAvatar2() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 2, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(2);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 410)
    public void processAccountScriptAvatar12() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 12, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(12);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 420)
    public void processAccountScriptAvatar22() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 22, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(22);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 600)
    public void processAccountScriptAvatar3() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 3, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(3);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 610)
    public void processAccountScriptAvatar13() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 13, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(13);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 620)
    public void processAccountScriptAvatar23() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 23, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(23);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 800)
    public void processAccountScriptAvatar4() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 4, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(4);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 810)
    public void processAccountScriptAvatar14() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 14, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(14);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 820)
    public void processAccountScriptAvatar24() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 24, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(24);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1000)
    public void processAccountScriptAvatar5() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 5, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(5);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1010)
    public void processAccountScriptAvatar15() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 15, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(15);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1020)
    public void processAccountScriptAvatar25() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 25, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(25);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1100)
    public void processAccountScriptAvatar6() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 6, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(6);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1110)
    public void processAccountScriptAvatar16() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 16, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(16);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1120)
    public void processAccountScriptAvatar26() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 26, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(26);
        }
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 1300)
    public void processAccountScriptAvatar7() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 7, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(7);
        }
    }


    @Scheduled(fixedDelay = 1000, initialDelay = 1310)
    public void processAccountScriptAvatar17() {
        if (TaskHelper.podStopping()) {
            return;
        }
        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 17, 2000L, TimeUnit.SECONDS);
        if (!taskRun) {
            this.getAndProcess(17);
        }
    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1320)
//    public void processAccountScriptAvatar27() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 27, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(27);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1500)
//    public void processAccountScriptAvatar8() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 8, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(8);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1510)
//    public void processAccountScriptAvatar18() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 18, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(18);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1520)
//    public void processAccountScriptAvatar28() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 28, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(28);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1700)
//    public void processAccountScriptAvatar9() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 9, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(9);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1710)
//    public void processAccountScriptAvatar19() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 19, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(19);
//        }
//    }
//    @Scheduled(fixedDelay = 1000, initialDelay = 1720)
//    public void processAccountScriptAvatar29() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 29, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(29);
//        }
//    }
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1900)
//    public void processAccountScriptAvatar10() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 10, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(10);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1910)
//    public void processAccountScriptAvatar20() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 20, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(20);
//        }
//    }
//
//
//    @Scheduled(fixedDelay = 1000, initialDelay = 1920)
//    public void processAccountScriptAvatar30() {
//        if (TaskHelper.podStopping()) {
//            return;
//        }
//        boolean taskRun = taskHelper.isTaskRun(TASK_NAME + 30, 2000L, TimeUnit.SECONDS);
//        if (!taskRun) {
//            this.getAndProcess(30);
//        }
//    }

    public void getAndProcess(Integer index) {
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            for (int j = 0; j < 1000; j++) {
                if (TaskHelper.podStopping()) {
                    return;
                }
                MqResponse mqResponse = mqClient.pop(CommConst.MQ_SRCIPT_AVATAR);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取用户头像质量检测失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }

                // {"account_uuid":"t0nhoul565i"}
                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                // 用户uuid
                String accountUuid = MapUtils.getString(map, "account_uuid");
                handler(accountUuid);

                mqResponse = mqClient.remove(CommConst.MQ_SRCIPT_AVATAR, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除用户头像质量检测:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.error("从TQMQ获取用户头像质量检测:" + msgBody + "处理失败", e);
        } finally {
            taskHelper.expireTask(TASK_NAME + index, 0L, TimeUnit.SECONDS);
        }
    }

    public void handler(String accountUuid) {
        // 判断一下是否真人
        AccountsPhoto accountAvatar = accountsPhotoDao.getAccountAvatar(accountUuid, null, false);
        // 头像为空
        if (accountAvatar == null) {
            log.warn("活跃用户重扫头像质量.头像不存在.uuid={}", accountUuid);
            return;
        }
        // 已经跑过
        Object avatarScriptQuality = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "avatar_script_quality");
        if(Objects.equals(avatarScriptQuality, ScriptRunStatus.DONE.getStatus())){
            log.info("已经跑过头像uuid={}, {},{}", accountUuid, avatarScriptQuality, ScriptRunStatus.DONE.getStatus());
            return;
        }
        // 头像质量检测
        String avatarUrl = accountAvatar.getPhoto_url();
        avatarUrl = AvatarHandleService.getAvatarSrcPhotoUrl(avatarUrl);

        try {
            Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, avatarUrl));
            if (imgQualityFuture == null) {
                return;
            }
            TencentImgQualityDTO tencentImgQualityDTO = imgQualityFuture.get(5, TimeUnit.SECONDS);
            if (tencentImgQualityDTO == null) {
                return;
            }
            Boolean imgQualityPass = tencentImgQualityDTO.isImgQualityPass();
            if (!imgQualityPass) {
                Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"sex_type"}, "1", false, false).get(accountUuid);
                String sexType = MapUtils.getString(infoMap, "sex_type");
                // 头像置默认
                accountsPhotoService.setAvatarAsDef(accountUuid, sexType);
                sendSystemMsg(accountUuid);
                log.info("活跃用户重扫头像质量.uuid={}.清理头像url={}", accountUuid, avatarUrl);
//                // 真人情况
//                if (realPersonCertification.equals("1")) {
//                    log.info("活跃用户重扫头像质量.uuid={}.真人.清理头像url={}", accountUuid, avatarUrl);
//                } else {
//                    // 非真人情况
//                    log.info("活跃用户重扫头像质量.uuid={}.非真人.清理头像url={}", accountUuid, avatarUrl);
//                }
                try{
                    log.info("头像数据变更.头像数据从审核队列中移除.头像uuid={}.id={}", accountUuid, accountAvatar.getId().toString());
                    SoaService.sendReviewPhotoSkip(null, accountAvatar.getId(), avatarUrl, "");
                }catch (Exception e){
                    log.warn("头像数据变更.头像数据从审核队列中移除.失败头像uuid={}.id={}", accountUuid, accountAvatar.getId().toString(), e);
                }

                accountsService.checkAccountEmptyFacePhoto(accountUuid);
            } else {
                log.info("活跃用户重扫头像质量.uuid={}.无需清理头像", accountUuid);
            }
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "avatar_script_quality",  ScriptRunStatus.DONE.getStatus());

        } catch (Exception e) {
            log.warn("活跃用户重扫头像质量失败,accountUuid={}, photoUrl={}", accountUuid, avatarUrl, e);
        }
    }

    private void sendSystemMsg(String accountUuid) {

        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info = "您的头像比较模糊，已为您重置，可以%s重新上传头像哟～";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "点击这里");
        contentReplace.put("r", "m=mine&a=info");
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "您的头像比较模糊，已为您重置");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);

        messageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
    }
}
