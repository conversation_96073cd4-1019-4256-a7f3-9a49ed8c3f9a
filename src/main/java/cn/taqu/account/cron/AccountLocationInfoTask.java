package cn.taqu.account.cron;

import cn.taqu.account.service.AccountsLocationInfoService;
import cn.taqu.account.vo.LocationInfoVO;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 用户位置信息处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-10-12 17:43
 */
@Component
@Lazy(false)
@Slf4j
public class AccountLocationInfoTask {

    /*
     * 2024.04.26 bbs和message有推，首页、推荐列表，用户主动行为触发（暂时保留吧）
     */
    private static String QUEUE_NAME="account_location_info";

    @Autowired
    private AccountsLocationInfoService accountsLocationInfoService;

    @Scheduled(fixedDelay = 1000, initialDelay = 200)
    public void processAccountLocationInfo1() {
        this.getAndProcess();
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 400)
    public void processAccountLocationInfo2() {
        this.getAndProcess();
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 600)
    public void processAccountLocationInfo3() {
        this.getAndProcess();
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 800)
    public void processAccountLocationInfo4() {
        this.getAndProcess();
    }

    public void getAndProcess(){
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                MqResponse mqResponse = mqClient.pop(QUEUE_NAME);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取用户位置信息数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }

                // {"ip":"**************","account_uuid":"t0nhoul565i","longitude":"116.397128","latitude":"39.916527","city_id ":"33","time":"**********"}
                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                // 用户uuid
                String accountUuid = MapUtils.getString(map, "account_uuid");
                // 时间
                String ip = MapUtils.getString(map, "ip");
                // longitude 经度
                String longitude = MapUtils.getString(map, "longitude", "");
                if("0".equals(longitude)){
                    longitude = "";
                }
                // latitude 纬度
                String latitude = MapUtils.getString(map, "latitude", "");
                if("0".equals(latitude)){
                    latitude = "";
                }
                // 城市
                String cityIdStr = MapUtils.getString(map, "city_id", "");
                Long cityId = null;

                try{
                    if(StringUtils.isNotBlank(cityIdStr) && !"0".equals(cityIdStr)){
                        cityId = Long.parseLong(cityIdStr);
                    }
                }catch (Exception e){
                    log.warn("AccountLocationInfoTask city数据错误=[{}]", cityIdStr);
                }
                // 业务处理
                handler(accountUuid, ip, longitude, latitude, cityId);

                mqResponse = mqClient.remove(QUEUE_NAME, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除用户位置信息:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.error("从TQMQ获取到的用户位置信息:" + msgBody + "处理失败", e);
        }
    }

    /**
     * 业务处理
     * @param accountUuid
     * @param ip
     * @param longitude
     * @param latitude
     * @param cityId
     */
    private void handler(String accountUuid, String ip, String longitude, String latitude, Long cityId) {
        log.info("AccountLocationInfoTask用户位置更新，uuid=[{}], ip=[{}], longitude=[{}], latitude=[{}], cityId=[{}]", accountUuid, ip, longitude, latitude, cityId);
        try {
            LocationInfoVO locationInfoVO = accountsLocationInfoService.saveAccountLocationInfo(accountUuid, ip, longitude, latitude, cityId);
            if(locationInfoVO == null){
                log.warn("AccountLocationInfoTask保存用户位置信息错误，uuid=[{}], ip=[{}], longitude=[{}], latitude=[{}], cityId=[{}]", accountUuid, ip, longitude, latitude, cityId);
            }
        }catch (Exception e){
            log.warn("消费用户位置信息失败.", e);
        }
    }
}
