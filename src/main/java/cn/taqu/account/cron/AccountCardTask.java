package cn.taqu.account.cron;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.service.AccountsCardService;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;

@Component
@Lazy(false)
public class AccountCardTask {
    private Logger logger = LoggerFactory.getLogger(AccountCardTask.class);

    @Autowired
    private AccountsCardService accountsCardService;
    @Autowired
    private TaskHelper taskHelper;
    @Qualifier("accountStringRedisTemplate")
    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void revokeExpireCard() {
        if(TaskHelper.podStopping()){
            return;
        }
        String runKey = "revoke:expr:card:run";
        if(taskHelper.isTaskRun(runKey, 4L, TimeUnit.MINUTES)) {
//            logger.info("回收过期靓号任务已经在运行中");
            return;
        }

        logger.info("执行回收过期靓号任务");
        try {
            String lastDate = DateUtil.dateToString8(DateUtil.changeDay(new Date(), -1));
            String redisKey = RedisKeyConstant.CARD_TTL_SET.setArg(lastDate);
            Long cardId;
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                cardId = null;
                try {
                    String cardIdStr = accountStringRedisTemplate.opsForSet().randomMember(redisKey);
                    if(StringUtils.isBlank(cardIdStr)) {
                        break;
                    }

                    logger.info("开始回收过期靓号:{}，过期日期:{}", cardIdStr, lastDate);
                    cardId = Long.valueOf(cardIdStr);
                    accountsCardService.revokeExpireCard(cardId);
                    accountStringRedisTemplate.opsForSet().remove(redisKey, cardIdStr);
                    logger.info("结束回收过期靓号:{}，过期日期:{}", cardIdStr, lastDate);
                } catch (Exception e) {
                    logger.error("回收过期靓号失败, 获取到的要回收的靓号id为:" + cardId, e);
                }
            }
        } catch (Exception e) {
            logger.error("回收过期靓号任务运行失败", e);
        } finally {
            taskHelper.expireTask(runKey, -1L, TimeUnit.SECONDS);
        }
    }

    @Scheduled(fixedDelay = 2 * 1000)
    public void regGrantNormalCard() {
        if(TaskHelper.podStopping()){
            return;
        }
        String runKey = "reg:grant:card:run";
        if(taskHelper.isTaskRun(runKey, 1L, TimeUnit.MINUTES)) {
            return;
        }
//        logger.info("注册用户自动发放靓号(普通号)");

        try {
            String preGrantCardKey = RedisKeyConstant.PRE_GRANT_CARD.getPattern();
            AtomicInteger maxCount = new AtomicInteger(1000);//单次最多处理1000个
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                if(maxCount.get() <= 0) {
                    break;
                }

                try {
                    Long size = accountStringRedisTemplate.opsForList().size(preGrantCardKey);
                    if(size == null || size == 0) {
                        break;
                    }

                    int limit = Math.min(size.intValue(), 200);
                    List<Long> cardIdList = accountsCardService.mGetNormalCard(9, limit);
                    if(cardIdList == null) {
                        break;
                    }

                    cardIdList.forEach(cardId -> {
                        maxCount.addAndGet(-1);

                        String accountUuid = accountStringRedisTemplate.opsForList().leftPop(preGrantCardKey);
                        if(StringUtils.isBlank(accountUuid)) {
                            return;
                        }

                        logger.info("开始给账号:{}分配靓号(普通号)", accountUuid);
                        try {
                            accountsCardService.grantNormalCard(accountUuid, cardId);
                            logger.info("给账号:{}分配靓号(普通号)成功，分配到的号码为:{}", accountUuid, cardId);
                        } catch (ServiceException se) {
                            if(!"uuid_had_card".equals(se.getCodeStatus().value())) {
                                logger.error("分配靓号(普通号)失败，用户uuid:"+accountUuid+", cardId:"+cardId, se);
                            }
                            if ("card_id_exists".equals(se.getCodeStatus().value())) {
                                if(!StringUtils.isBlank(accountUuid)) {
                                    accountsCardService.preGrantNormalCard(accountUuid);
                                }
                            }
                        } catch (Exception e) {
                            logger.error("分配靓号(普通号)失败，用户uuid:"+accountUuid+", cardId:"+cardId, e);
                            if(!StringUtils.isBlank(accountUuid)) {
                                accountsCardService.preGrantNormalCard(accountUuid);
                            }

                            try {
                                Thread.sleep(1000);//错了就歇一秒
                            } catch (InterruptedException e1) {}
                        }
                    });
                } catch (Exception e) {
                    logger.error("自动发放靓号异常，给注册用户自动发放靓号(普通号)失败", e);
                    try {
                        Thread.sleep(1000);//错了就歇一秒
                    } catch (InterruptedException e1) {}
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("自动发放靓号异常，给注册用户自动发放靓号(普通号)失败", e);
        } finally {
            taskHelper.expireTask(runKey, -1L, TimeUnit.SECONDS);
        }
    }
}
