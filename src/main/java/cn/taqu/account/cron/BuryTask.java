package cn.taqu.account.cron;

import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.service.AccountsService;
import cn.taqu.account.service.BuryService;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.Identities;
import cn.taqu.core.utils.JsonUtils;

/**
 * 业务定时任务
 *
 * <AUTHOR>
 * @date 2020/08/21
 */
@Component
@Lazy(false)
public class BuryTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuryTask.class);

    @Autowired
    private AccountsService accountsService;
    @Autowired
    private BuryService buryService;

    /**
     * 定从发送埋点数据到数据中心
     */
    @Scheduled(fixedDelay = 5000)
    public void sendDataToDataCenter() {
        buryService.sendBuryDataToDataCenter();
    }

    /**
     * 定从发送埋点数据到风控系统
     */
    @Scheduled(fixedDelay = 5000)
    public void sendDataToAntispam() {
        buryService.sendBuryDataToAntispam();
    }

    /**
     * 定从发送昵称筛选埋点数据到mq
     */
//    @Deprecated
//    @Scheduled(fixedDelay = 5000)
//    public void sendNicknameToContentFilterQueue() {
//        buryService.sendToContentFilterQueue();
//    }

	/**
	 * 用户注销异常处理
	 */
	@Scheduled(fixedDelay = 5000)
	public void accountDestory() {
		while (true) {
            if(TaskHelper.podStopping()){
                return;
            }
			try {
				String accountUuid = buryService.getDestroyUuid();
				if(StringUtils.isBlank(accountUuid)) {
					break;
				}
				LOGGER.info("处理注销用户：{}", accountUuid);
				accountsService.afterDestroy(accountUuid);
			} catch (Exception e) {
				LOGGER.error("处理注销用户数据异常:uuid" + accountsService, e);
				break;
			}
		}
	}

	/**
	 * 处理头像相册修改
	 */
	@Scheduled(fixedDelay = 1000, initialDelay=5*1000)
	public void accountAvatarPhotoUpdateTask1() {
        SoaBaseParams.fromThread().setDistinctRequestId(Identities.uuid2());
	    accountAvatarPhotoUpdateTask();
    }

	/**
     * 处理头像相册修改
     */
	@Scheduled(fixedDelay = 1000, initialDelay=10*1000)
	public void accountAvatarPhotoUpdateTask2() {
        SoaBaseParams.fromThread().setDistinctRequestId(Identities.uuid2());
	    accountAvatarPhotoUpdateTask();
	}

	/**
     * 处理头像相册修改
     */
	private void accountAvatarPhotoUpdateTask() {
	    for (int i = 0; i < 1000; i++) {
            if(TaskHelper.podStopping()){
                return;
            }
            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).pop(CommConst.MQ_ACCOUNT_AVATAR_PHOTO_UPDATE);
            if (mqResponse.fail()) {
                LOGGER.error("从{}获取数据失败，{}-{}", CommConst.MQ_ACCOUNT_AVATAR_PHOTO_UPDATE, mqResponse.getCode(), mqResponse.getMsg());
                break;
            }
            String msgBody = mqResponse.getMsg_body();
            if(StringUtils.isBlank(msgBody)) {
                break;
            }
            Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {});
            String accountUuid = MapUtils.getString(map, "account_uuid", "");
            Integer photoOrignCheck = MapUtils.getInteger(map, "photoOrignCheck", null);
            String tracerId = MapUtils.getString(map, "tracer_id", null);
            if (StringUtils.isNotBlank(tracerId)) {
                SoaBaseParams.fromThread().setDistinctRequestId(tracerId);
            }
            buryService.doAccountAvatarPhotoUpdate(accountUuid);
            buryService.doAccountPhotoCheck(accountUuid, photoOrignCheck);
            // 判断高颜值标签 是否需要删除
//            buryService.doAccountPhotoFaceLevel(accountUuid);

            mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).remove(CommConst.MQ_ACCOUNT_AVATAR_PHOTO_UPDATE, mqResponse.getMsg_id());
            if (mqResponse.fail()) {
                LOGGER.error("从{}删除数据{}失败，{}-{}",CommConst.MQ_ACCOUNT_AVATAR_PHOTO_UPDATE, mqResponse.getMsg_id(), mqResponse.getCode(), mqResponse.getMsg());
                break;
            }
        }
	}

}
