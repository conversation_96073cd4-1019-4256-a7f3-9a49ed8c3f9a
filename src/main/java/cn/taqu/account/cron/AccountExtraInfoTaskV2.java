package cn.taqu.account.cron;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import cn.taqu.account.service.AccountsService;
import cn.taqu.account.service.AsyncTrackingService;
import lombok.extern.slf4j.Slf4j;

/**
 * 需要去掉，用 @AccountActiveTask.class
 * 
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Component
public class AccountExtraInfoTaskV2 {

    // TODO 用户系统不适合接入该topic，topic包含所有网关请求，会包含非他趣数据
    private static final String TOPIC = "user_active_heartbeat";
    
    private static final String TAQU_APPCODE = "1";

    @Autowired
    private AccountsService accountsService;

    @Autowired
    private AsyncTrackingService asyncTrackingService;

    @KafkaListener(topics = {TOPIC}, containerFactory = "kafkaListenerContainerFactory")
    public void process(List<ConsumerRecord<String, String>> records) {
        records.parallelStream().forEach(this::accountProcess);
    }

    private void accountProcess(ConsumerRecord<String, String> record) {
        String message = record.value();

        if (StringUtils.isBlank(message)) {
            return;
        }

        /*
         *   {                                                                                                                                                                                                                                                                                                                  
         *     "URI":"/live_api/v1/AppReport/frontOnlineReport",                                                                                                                                                                                                                                                                
         *     "ab_bisols":"10_0",                                                                                                                                                                                                                                                                                              
         *     "ab_exps":"323_794,375_933,321_788,485_1256,449_1171,460_1197,534_1431,555_1484,559_1492,406_1013,296_723,546_1467,561_1495,566_1508,532_1427,221_505,393_973,442_1157,345_842,563_1500,571_1520,429_1129,562_1498,282_688,324_795,327_802,333_815,325_798,468_1217,464_1205,445_1164,456_1188,570_1516,344_840",
         *     "ab_gisols":"",                                                                                                                                                                                                                                                                                                  
         *     "accountType":"1",                                                                                                                                                                                                                                                                                               
         *     "appVersion":"80000",                                                                                                                                                                                                                                                                                            
         *     "appcode":"1",                                                                                                                                                                                                                                                                                                   
         *     "channel":"dev2",                                                                                                                                                                                                                                                                                                
         *     "city":"303",                                                                                                                                                                                                                                                                                                    
         *     "cloned":"1",                                                                                                                                                                                                                                                                                                    
         *     "ctime":"1714099933422",                                                                                                                                                                                                                                                                                         
         *     "distinctRequestId":"a6bd1dc58d2250855b2b282480c88e79",                                                                                                                                                                                                                                                          
         *     "gender":"2",                                                                                                                                                                                                                                                                                                    
         *     "ip":"**************",                                                                                                                                                                                                                                                                                           
         *     "latitude":"24.62349004",                                                                                                                                                                                                                                                                                        
         *     "longitude":"118.03093424",                                                                                                                                                                                                                                                                                      
         *     "platformId":"1",                                                                                                                                                                                                                                                                                                
         *     "token":"M84672c9cf9b8b1377b30b15811d6c0ca",                                                                                                                                                                                                                                                                     
         *     "uuid":"bghjgebddggcbajg"                                                                                                                                                                                                                                                                                        
         *   }                                                                                                                                                                                                                                                                                                                  
         */
        Map<String, Object> map = JSONUtil.toBean(message, new TypeReference<Map<String, Object>>() {
        }, false);

        String appcode = MapUtils.getString(map, "appcode");
        String ip = MapUtils.getString(map, "ip");
        String accountUuid = MapUtils.getString(map, "uuid");
        String longitude = MapUtils.getString(map, "longitude");
        String latitude = MapUtils.getString(map, "latitude");
        String cityId = MapUtils.getString(map, "city");
        String appVersion = MapUtils.getString(map, "appVersion");

        //TODO uuid为空 或者 appcode非1的过滤，后期应该要换topic
        if (StringUtils.isBlank(accountUuid) || !Objects.equals(appcode, TAQU_APPCODE)) {
            return;
        }
        
        log.info("处理user_active_heartbeat, json={}", message);

        Map<String, String> accountInfoHashValues = new HashMap<>();
        if (StringUtils.isNotBlank(longitude) && NumberUtils.isCreatable(longitude)) {
            accountInfoHashValues.put("longitude", longitude);
        }
        if (StringUtils.isNotBlank(latitude) && NumberUtils.isCreatable(latitude)) {
            accountInfoHashValues.put("latitude", latitude);
        }
        if (StringUtils.isNotBlank(cityId) && StringUtils.isNumeric(cityId) && !"0".equals(cityId)) {
            accountInfoHashValues.put("city_id", cityId);
        }
        if (StringUtils.isNotBlank(appVersion) && StringUtils.isNumeric(appVersion) && !"0".equals(appVersion)) {
            accountInfoHashValues.put("last_active_app_version", appVersion);
        }

        accountsService.updateAccountInfosRedis(accountUuid, accountInfoHashValues);

        // 异步记录用户位置信息
        Long city = StringUtils.isNotBlank(cityId) && StringUtils.isNumeric(cityId) && !"0".equals(cityId) ? Long.parseLong(cityId) : null;
        asyncTrackingService.trackingAccountLocationInfo(accountUuid, ip, longitude, latitude, city);
    }
}
