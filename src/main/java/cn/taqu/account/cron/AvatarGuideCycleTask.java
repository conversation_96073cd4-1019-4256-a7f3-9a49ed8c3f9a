package cn.taqu.account.cron;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AvatarGuidePopupRecordDao;
import cn.taqu.account.dto.InfoFiledCacheDTO;
import cn.taqu.account.event.AvatarGuideCycleReporter;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.model.AvatarGuidePopupRecord;
import cn.taqu.account.utils.RedisLockUtil;
import com.google.common.util.concurrent.RateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 头像指导定时任务
 *
 * @desc
 * 里面两个定时任务：拆分两个是因为合并起来后续不好维护
 * 1、弹窗时间超过一个月
 * 2、请求算法失败的场景需要重新推送（每30分钟执行一次）：这里数据里很少，不需要高频执行空跑
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Profile({"prod", "test"})
public class AvatarGuideCycleTask {

    private final StringRedisTemplate accountBizStringRedisTemplate;
    private final AvatarGuidePopupRecordDao avatarGuidePopupRecordDao;
    private final AccountBaseInfoManager accountBaseInfoManager;
    private final RedisLockUtil lockUtil;
    private final AvatarGuideCycleReporter avatarGuideCycleReporter;

    /**
     * 循环推送任务
     * 每天凌晨1点执行，捞取30天内没有弹窗的用户进行推送
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @SuppressWarnings("UnstableApiUsage")
    public void avatarGuideCycleTask() {
        String lockKey = "avatar:guide:cycle:lock";
        boolean locked = lockUtil.lock(lockKey, 3600); // 1小时锁定时间
        if (!locked) {
            return;
        }

        log.info("头像指导循环任务starting");
        StopWatch started = StopWatch.createStarted();
        long id = 0L;
        int size = 200;
        long total = 0L;
        boolean flag = true;
        // 管控频率，防止后续数据量大影响线上库
        RateLimiter rateLimiter = RateLimiter.create(20);

        try {
            while (flag) {
                rateLimiter.acquire();
                // 获取近期没有弹窗记录的用户列表, 分批查询
                Integer startDate = getDateBeforeDays(1);
                List<AvatarGuidePopupRecord> records = avatarGuidePopupRecordDao.findNeverPopupBeforeDay(startDate, id, size);
                if (CollectionUtils.isEmpty(records)) {
                    flag = false;
                    continue;
                }

                total += records.size();
                id = records.get(records.size() - 1).getId();
                // 捞取头像
                List<String> uuidList = records.stream().map(AvatarGuidePopupRecord::getAccountUuid).distinct().collect(Collectors.toList());
                List<InfoFiledCacheDTO> infoList = accountBaseInfoManager.listAvatar(uuidList);
                // 推送算法
                pushData(infoList);
            }
        } catch (Exception e) {
            log.error("头像指导巡检执行异常", e);
        } finally {
            lockUtil.unLock(lockKey);
            started.stop();
            long time = started.getTime();
            log.info("头像指导巡检执行完成size:{}, 耗时：{}", total, time);
        }
    }

    /**
     * 巡检失败队列(每30分钟执行一次)
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void cycleFailQueue() {
        String lockKey = "avatar:guide:cycle:fail:lock";
        boolean locked = lockUtil.lock(lockKey, 3600); // 1小时锁定时间
        if (!locked) {
            return;
        }

        log.info("头像指导失败队列巡检任务starting");
        StopWatch started = StopWatch.createStarted();
        int total = 0;
        try {
            // 正常错误一天不会超过200个，超过200就说明波动很大了
            List<Object> result = accountBizStringRedisTemplate.executePipelined((RedisConnection connection) -> {
                StringRedisConnection stringConn = (StringRedisConnection) connection;
                for (int i = 0; i < 200; i++) {
                    stringConn.sPop(RedisKeyConstant.AVATAR_GUIDE_ERROR_QUEUE);
                }

                return null;
            });

            List<String> uuidList = result.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(uuidList)) {
                return;
            }

            List<InfoFiledCacheDTO> infoList = accountBaseInfoManager.listAvatar(uuidList);
            // 推送算法
            pushData(infoList);
            total = infoList.size();
        } catch (Exception e) {
            log.error("头像指导失败队列巡检执行异常", e);
        } finally {
            lockUtil.unLock(lockKey);
            started.stop();
            long time = started.getTime();
            log.info("头像指导失败队列巡检执行完成size:{}, 耗时：{}", total, time);
        }

    }

    /**
     * 推送算法
     *
     * @param infoList
     */
    private void pushData(List<InfoFiledCacheDTO> infoList) {
        infoList.forEach(info -> {
            avatarGuideCycleReporter.report(info.getUuid(), info.getAvatar());
        });
    }

    /**
     * 获取指定天数之前的日期（YYYYMMDD格式）
     */
    private Integer getDateBeforeDays(int days) {
        return Integer.parseInt(LocalDate.now().minusDays(days).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

}
