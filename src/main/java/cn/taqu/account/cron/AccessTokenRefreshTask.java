package cn.taqu.account.cron;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.taqu.account.client.TengxunyunApiClient;

/**
 * 刷新AccessToken的定时任务
 *
 * <AUTHOR>
 * @date 2021/06/02
 */
@Component
@Lazy(false)
@Profile({"prod","test"})
public class AccessTokenRefreshTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccessTokenRefreshTask.class);

    @Autowired
    private TaskHelper taskHelper;
    @Autowired
    private TengxunyunApiClient tengxunyunApiClient;

    /**
     * 腾讯云定时获取 Access Token  20分钟
     * https://cloud.tencent.com/document/product/1007/37304
     */
    @Scheduled(fixedDelay = 20 * 60 * 1000)
    public void tengxunyunRefresh() {
        if(TaskHelper.podStopping()){
            return;
        }
        String runKey = "AccessTokenRefreshTask:tengxunyunRefresh";
        if(taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
            return;
        }
        LOGGER.info("更新腾讯云凭证");
        tengxunyunApiClient.generateAccessTokenAndSignTicket();
    }

}
