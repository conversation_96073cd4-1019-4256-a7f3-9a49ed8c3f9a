package cn.taqu.account.cron;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import cn.taqu.account.service.AccountsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Component
public class AccountDestroyAfterCoolTask {

    private static final String TOPIC = "j70_account_cancel_1";

    @Autowired
    private AccountsService accountsService;

    @KafkaListener(topics = {TOPIC}, containerFactory = "kafkaListenerContainerFactory")
    public void process(List<ConsumerRecord<String, String>> records) {
        records.parallelStream().forEach(this::accountProcess);
    }

    private void accountProcess(ConsumerRecord<String, String> record) {
        String message = record.value();

        if (StringUtils.isBlank(message)) {
            return;
        }

        if (message.length() <= 2) {
            return;
        }

        List<Map<String, String>> list = JSONUtil.toBean(message, new TypeReference<List<Map<String, String>>>() {
        }, false);

        if (CollectionUtils.isNotEmpty(list)) {
            for (Map<String, String> map : list) {
                String uuid = map.get("uuid");
                if (StringUtils.isNotBlank(uuid)) {
                    accountsService.actualDestroyAfterFreeze(uuid);
                    log.info("用户{}触发真实注销", uuid);
                }
            }
        }

    }
}
