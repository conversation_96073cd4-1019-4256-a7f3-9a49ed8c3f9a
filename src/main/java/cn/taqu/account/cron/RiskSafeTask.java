package cn.taqu.account.cron;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.taqu.account.common.*;
import cn.taqu.account.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RiskSafeIdConstant;
import cn.taqu.account.dao.AccountsIntroductionPhotoLogDao;
import cn.taqu.account.model.AccountsIdealTargetLog;
import cn.taqu.account.model.AccountsIntroductionLog;
import cn.taqu.account.model.AccountsIntroductionPhotoLog;
import cn.taqu.account.model.AccountsLifeLog;
import cn.taqu.account.model.AccountsMajorLog;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.VoiceSignInfo;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.service.AccountsIntroductionService;
import cn.taqu.account.service.AccountsLifeService;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.account.service.IdealTargetService;
import cn.taqu.account.service.RiskSafeService;
import cn.taqu.account.service.SchoolService;
import cn.taqu.account.service.VoiceSignInfoService;
import cn.taqu.account.utils.EnvUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 风控安全审核（卡审核定时任务）
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Component
@Lazy(false)
@Slf4j
public class RiskSafeTask {

    // 卡审核上线日期
    private static Long oldDataStartTime = 1706025600L;

    @Autowired
    private TaskHelper taskHelper;

    @Autowired
    private VoiceSignInfoService voiceSignInfoService;

    @Autowired
    private AccountsIntroductionService accountsIntroductionService;

    @Autowired
    private AccountsIntroductionPhotoLogDao accountsIntroductionPhotoLogDao;

    @Autowired
    private AccountsInfoService accountsInfoService;

    @Autowired
    private AccountsPhotoService accountsPhotoService;
    
    @Autowired
    private RedisLockUtil redisLockUtil;

    @Autowired
    private SchoolService schoolService;

    @Autowired
    private IdealTargetService idealTargetService;
    
    @Autowired
    private AccountsLifeService accountsLifeService;

    /**
     * 语音签名卡审核处理
     */
//    @Scheduled(fixedDelay = 1 * 60 * 1000) // 测试使用
    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void voiceSignReview() {
        if (TaskHelper.podStopping()) {
            return;
        }
        String runKey = "RiskSafeTask:voiceSignReview";
        if (taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
            return;
        }

        // 查询1小时还未审核的记录（每次最多查50条）
        List<VoiceSignInfo> list = voiceSignInfoService.queryNotAudit(System.currentTimeMillis() / 1000 - 3600, oldDataStartTime, 0, 50);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        log.info("[语音签名卡审核]定时任务,size:{}", list.size());
        Map<String, VoiceSignInfo> voiceSignInfoMap = list.stream().collect(Collectors.toMap(this::getBizId, a -> a, (k1, k2) -> k1));
        Map<String, RiskSafeHitTypeResponseDTO> bizStatusMap = RiskSafeService.queryBizStatus(1, RiskDetectEnum.AUDIO_SIGN.name(), voiceSignInfoMap.keySet());
        bizStatusMap.forEach((bizId, riskSafeHitTypeResponseDTO) -> {
            try {
                if (Objects.nonNull(riskSafeHitTypeResponseDTO) && StringUtils.isNotBlank(riskSafeHitTypeResponseDTO.getHitType())) {
                    switch (riskSafeHitTypeResponseDTO.getHitType()) {
                        case "noExist":
                            VoiceSignInfo voiceSignInfo = voiceSignInfoMap.get(bizId);
                            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
                                voiceSignInfo.getAccount_uuid(),
                                new String[]{"reg_appcode", "reg_cloned"}
                            );
                            Integer regAppcode = MapUtils.getInteger(infoMap, "reg_appcode", 1);
                            Integer regCloned = MapUtils.getInteger(infoMap, "reg_cloned", 1);
                            ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.audioReviewFromRiskDetect(voiceSignInfo.getAccount_uuid(), voiceSignInfo.getId(), voiceSignInfoService.getVoiceSignPreDomain() + voiceSignInfo.getVoice_sign_url(), voiceSignInfo.getVoice_sign_duration(), RiskDetectEnum.AUDIO_SIGN.name(), null, regAppcode, regCloned);
                            if (contentCheckDTO == null ||
                                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
                                voiceSignInfoService.auditReject(Long.parseLong(bizId), "system", "", true);
                            }
                            break;
                        case "pass":
                            voiceSignInfoService.auditPass(Long.parseLong(bizId), "system");
                            break;
                        case "block":
                            voiceSignInfoService.auditReject(Long.parseLong(bizId), "system", "", true);
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("[语音签名卡审核]定时任务处理失败,bizId:{},errorMsg:{}", bizId, e.getMessage(), e);
            }
        });
    }

    /**
     * 自我介绍卡审核处理
     */
    @Scheduled(fixedDelay = 5 * 60 * 1000)
//    @Scheduled(fixedDelay = 60 * 1000)
    public void introductionReview() {
        if (TaskHelper.podStopping()) {
            return;
        }
        String runKey = "RiskSafeTask:introductionReview";
        if (taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
            return;
        }

        // 查询1小时还未审核的记录（每次最多查50条）
        List<AccountsIntroductionLog> list = accountsIntroductionService.queryNotAudit(System.currentTimeMillis() / 1000 - 3600, oldDataStartTime, 0, 50);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        log.info("[自我介绍卡审核]定时任务,size:{}", list.size());
        Map<String, AccountsIntroductionLog> introductionMap = list.stream().collect(Collectors.toMap(this::getBizId, a -> a, (k1, k2) -> k1));
        Map<String, RiskSafeHitTypeResponseDTO> bizStatusMap = RiskSafeService.queryBizStatus(1, RiskDetectEnum.TEXT_INTRODUCTION.name(), introductionMap.keySet());
        bizStatusMap.forEach((bizId, riskSafeHitTypeResponseDTO) -> {
            try {
                if (Objects.nonNull(riskSafeHitTypeResponseDTO) && StringUtils.isNotBlank(riskSafeHitTypeResponseDTO.getHitType())) {
                    switch (riskSafeHitTypeResponseDTO.getHitType()) {
                        case "noExist":
                            // 风控那边没有数据，需要重新触发审核
                            AccountsIntroductionLog introductionLog = introductionMap.get(bizId);
                            List<AccountsIntroductionPhotoLog> introductionPhotoLogs = accountsIntroductionPhotoLogDao.findByIntroductionLogId(introductionLog.getId());
                            List<String> photoUrls = CollectionUtils.isNotEmpty(introductionPhotoLogs) ? introductionPhotoLogs.stream().map(t -> CommConst.AVATAR_SRC_HOST + t.getPhotoUrl()).collect(Collectors.toList()) : Collections.emptyList();
                            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
                                introductionLog.getAccountUuid(),
                                new String[]{"reg_appcode", "reg_cloned"}
                            );
                            Integer regAppcode = MapUtils.getInteger(infoMap, "reg_appcode", 1);
                            Integer regCloned = MapUtils.getInteger(infoMap, "reg_cloned", 1);
                            ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(introductionLog.getAccountUuid(), introductionLog.getContent(), photoUrls, "", bizId, RiskDetectEnum.TEXT_INTRODUCTION.name(), regAppcode, regCloned);
                            if (contentCheckDTO == null ||
                                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
                                accountsIntroductionService.auditReject(Long.parseLong(bizId), "system");
                            }
                            break;
                        case "pass":
                            accountsIntroductionService.auditPass(Long.parseLong(bizId), "system");
                            break;
                        case "block":
                            accountsIntroductionService.auditReject(Long.parseLong(bizId), "system");
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("[自我介绍卡审核]定时任务处理失败,bizId:{},errorMsg:{}", bizId, e.getMessage(), e);
            }
        });
    }

    /**
     * 头像卡审核处理
     */
//    @Scheduled(fixedDelay = 1 * 30 * 1000) // 测试使用
    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void avatarReview() {
        if (TaskHelper.podStopping()) {
            return;
        }
        String runKey = "RiskSafeTask:avatarReview";
        if (taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
            return;
        }

        // 查询1小时还未审核的记录（每次最多查50条）
        List<AccountsPhoto> list = accountsPhotoService.queryNotAudit(System.currentTimeMillis() / 1000 - 3600, oldDataStartTime, 1, 6, 50);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        log.info("[头像卡审核]定时任务,size:{}", list.size());

        List<String> realPersonList = new ArrayList<>();
        List<String> notRealPersonList = new ArrayList<>();
        list.forEach(accountsPhoto -> {
            Integer checkType = accountsPhotoService.getPhotoCheckType(accountsPhoto);
            if (PhotoOrignCheckEnum.REAL_PERSON_AVATAR.getValue().equals(checkType)) {
                realPersonList.add(RiskSafeService.getAccountPhotoBizId(accountsPhoto.getId(), accountsPhoto.getPhoto_url()));
            } else if (PhotoOrignCheckEnum.NOT_REAL_PERSON_AVATAR.getValue().equals(checkType)) {
                notRealPersonList.add(RiskSafeService.getAccountPhotoBizId(accountsPhoto.getId(), accountsPhoto.getPhoto_url()));
            }
        });
        Map<String, AccountsPhoto> accountsPhotoMap = list.stream().collect(Collectors.toMap(this::getBizId, a -> a, (k1, k2) -> k1));
        processAvatarReview(realPersonList, accountsPhotoMap, RiskDetectEnum.REAL_PERSON_AVATAR);
        processAvatarReview(notRealPersonList, accountsPhotoMap, RiskDetectEnum.NOT_REAL_PERSON_AVATAR);
    }

    private void processAvatarReview(List<String> bizIdList, Map<String, AccountsPhoto> accountsPhotoMap, RiskDetectEnum riskDetectEnum) {
        Integer checkType = RiskDetectEnum.REAL_PERSON_AVATAR.equals(riskDetectEnum) ? 1 : 2;
        Map<String, RiskSafeHitTypeResponseDTO> bizStatusMap = RiskSafeService.queryBizStatus(1, riskDetectEnum.name(), bizIdList);
        bizStatusMap.forEach((bizId, riskSafeHitTypeResponseDTO) -> {
            try {
                if (Objects.nonNull(riskSafeHitTypeResponseDTO) && StringUtils.isNotBlank(riskSafeHitTypeResponseDTO.getHitType())) {
                    AccountsPhoto accountsPhoto = accountsPhotoMap.get(bizId);
                    String accountUuid = accountsPhoto.getAccount_uuid();
                    Map<Long, String> photoMap = new HashMap<>();
                    photoMap.put(accountsPhoto.getId(), accountsPhoto.getPhoto_url());
                    
                    String lockKey = RedisKeyConstant.REDIS_LOCK_KEY.setArg(LockKey.FACE_COMPARE_AND_AUDIT, accountUuid);
                    redisLockUtil.executeWithLock(lockKey, 3000, () -> {
                        switch (riskSafeHitTypeResponseDTO.getHitType()) {
                            case "noExist":
                            
                                Integer appcode = CommConst.APPCODE_TAQU;
                                Integer cloned = accountsInfoService.getAccountCloned(accountsPhoto.getAccount_uuid());
                                String photoUrl = accountsPhoto.getPhoto_url();    
                                photoUrl = AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl);
                                
                                ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = RiskSafeService.imgReviewFromRiskDetect(appcode, cloned, accountsPhoto.getAccount_uuid(), riskDetectEnum.name(), Arrays.asList(photoUrl), CommConst.OLD_CLIENT_PACKAGE, accountsPhoto.getId(), null);
                                if (shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), RiskDetectRiskLevelEnum.REJECT.name())) {
                                    // 机审不通过
                                    accountsPhotoService.auditProcess(accountsPhoto.getAccount_uuid(), "system", checkType, RiskSafeHitTypeEnum.BLOCK, photoMap);
                                }
                                log.warn("[头像卡审核]未找到审核记录,bizId:{}", bizId);
                                break;
                            case "pass":
                                accountsPhotoService.auditProcess(accountUuid, "system", checkType, RiskSafeHitTypeEnum.PASS, photoMap);
                                break;
                            case "block":
                                accountsPhotoService.auditProcess(accountUuid, "system", checkType, RiskSafeHitTypeEnum.BLOCK, photoMap);
                                break;
                        }
                        return true;
                    });
                    
                }
            } catch (Exception e) {
                log.warn("[头像卡审核]定时任务处理失败,bizId:{},errorMsg:{}", bizId, e.getMessage(), e);
            }
        });
    }

    public String getBizId(VoiceSignInfo voiceSignInfo) {
       return String.valueOf(voiceSignInfo.getId());
    }

    public String getBizId(AccountsIntroductionLog accountsIntroductionLog) {
        return String.valueOf(accountsIntroductionLog.getId());
    }

    public String getBizId(AccountsPhoto accountsPhoto) {
        return RiskSafeService.getAccountPhotoBizId(accountsPhoto.getId(), AvatarHandleService.getAvatarSrcPhotoUrl(accountsPhoto.getPhoto_url()));
    }

    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void majorReview() {
        if (TaskHelper.podStopping()) {
            return;
        }
        String runKey = "RiskSafeTask:majorReview";
        if (taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
            return;
        }

        // 查询1小时还未审核的记录（每次最多查50条）
        List<AccountsMajorLog> list = schoolService.getMajorLogDao().queryNotAudit(DateUtil.currentTimeSeconds() - 3600, oldDataStartTime, 50);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        log.info("[专业卡审核]定时任务,size:{}", list.size());
        Map<String, AccountsMajorLog> majorMap = list.stream().collect(Collectors.toMap(l -> String.valueOf(l.getId()), Function.identity(), (k1, k2) -> k1));
        Map<String, RiskSafeHitTypeResponseDTO> bizStatusMap = RiskSafeService.queryBizStatus(1, RiskDetectEnum.TEXT_MAJOR.name(), majorMap.keySet());
        bizStatusMap.forEach((bizId, riskSafeHitTypeResponseDTO) -> {
            try {
                if (Objects.nonNull(riskSafeHitTypeResponseDTO) && StringUtils.isNotBlank(riskSafeHitTypeResponseDTO.getHitType())) {
                    RiskSafeCheckResponseDTO resp = new RiskSafeCheckResponseDTO();
                    resp.setBizId(bizId);
                    resp.setSafeId(RiskSafeIdConstant.TEXT_MAJOR);
                    resp.setOperator("system");
                    resp.setHitTypeCode(riskSafeHitTypeResponseDTO.getHitType());
                    switch (riskSafeHitTypeResponseDTO.getHitType()) {
                        case "noExist":
                            // 风控那边没有数据，需要重新触发审核
                            AccountsMajorLog majorLog = majorMap.get(bizId);
                            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
                                majorLog.getAccountUuid(),
                                new String[]{"reg_appcode", "reg_cloned"}
                            );
                            Integer regAppcode = MapUtils.getInteger(infoMap, "reg_appcode", 1);
                            Integer regCloned = MapUtils.getInteger(infoMap, "reg_cloned", 1);
                            ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(majorLog.getAccountUuid(), majorLog.getMajor(), Collections.emptyList(), "", bizId, RiskDetectEnum.TEXT_MAJOR.name(), regAppcode, regCloned);
                            if (contentCheckDTO == null ||
                                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
                                schoolService.audit(resp);
                            }
                            break;
                        case "pass":
                        case "block":
                            schoolService.audit(resp);
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("[专业卡审核]定时任务处理失败,bizId:{},errorMsg:{}", bizId, e.getMessage(), e);
            }
        });
    }

    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void idealReview() {
        if (TaskHelper.podStopping()) {
            return;
        }
        String runKey = "RiskSafeTask:idealReview";
        if (taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
            return;
        }

        // 查询1小时还未审核的记录（每次最多查50条）
        List<AccountsIdealTargetLog> list = idealTargetService.getLogDao().queryNotAudit(DateUtil.currentTimeSeconds() - 3600, oldDataStartTime, 50);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        log.info("[理想型卡审核]定时任务,size:{}", list.size());
        Map<String, AccountsIdealTargetLog> majorMap = list.stream().collect(Collectors.toMap(l -> String.valueOf(l.getId()), Function.identity(), (k1, k2) -> k1));
        Map<String, RiskSafeHitTypeResponseDTO> bizStatusMap = RiskSafeService.queryBizStatus(1, RiskDetectEnum.TEXT_IDEAL.name(), majorMap.keySet());
        bizStatusMap.forEach((bizId, riskSafeHitTypeResponseDTO) -> {
            try {
                if (Objects.nonNull(riskSafeHitTypeResponseDTO) && StringUtils.isNotBlank(riskSafeHitTypeResponseDTO.getHitType())) {
                    RiskSafeCheckResponseDTO resp = new RiskSafeCheckResponseDTO();
                    resp.setBizId(bizId);
                    resp.setSafeId(RiskSafeIdConstant.TEXT_IDEAL);
                    resp.setOperator("system");
                    resp.setHitTypeCode(riskSafeHitTypeResponseDTO.getHitType());
                    switch (riskSafeHitTypeResponseDTO.getHitType()) {
                        case "noExist":
                            // 风控那边没有数据，需要重新触发审核
                            AccountsIdealTargetLog idealLog = majorMap.get(bizId);
                            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
                                idealLog.getAccountUuid(),
                                new String[]{"reg_appcode", "reg_cloned"}
                            );
                            Integer regAppcode = MapUtils.getInteger(infoMap, "reg_appcode", 1);
                            Integer regCloned = MapUtils.getInteger(infoMap, "reg_cloned", 1);
                            ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(idealLog.getAccountUuid(), idealLog.getIdealTarget(), Collections.emptyList(), "", bizId, RiskDetectEnum.TEXT_IDEAL.name(), regAppcode, regCloned);
                            if (contentCheckDTO == null ||
                                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
                                idealTargetService.audit(resp);
                            }
                            break;
                        case "pass":
                        case "block":
                            idealTargetService.audit(resp);
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("[理想型卡审核]定时任务处理失败,bizId:{},errorMsg:{}", bizId, e.getMessage(), e);
            }
        });
    }
    
    /**
     * 我的生活卡审核处理
     */
//    @Scheduled(fixedDelay = 1 * 60 * 1000) // 测试使用
    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void accountsLifeReview() {
        if (TaskHelper.podStopping()) {
            return;
        }
        String runKey = "RiskSafeTask:lifeReview";
        if (taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
            return;
        }
        
        // 查询1小时还未审核的记录（每次最多查50条）
        List<AccountsLifeLog> list = accountsLifeService.queryNotAudit(DateUtil.currentTimeSeconds() - 3600, oldDataStartTime, 50);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        log.info("[我的生活卡审核]定时任务,size:{}", list.size());
        Map<String, AccountsLifeLog> logMap = list.stream().collect(Collectors.toMap(l -> String.valueOf(l.getId()), Function.identity(), (k1, k2) -> k1));
        Map<String, RiskSafeHitTypeResponseDTO> bizStatusMap = RiskSafeService.queryBizStatus(1, RiskDetectEnum.ACCOUNTS_LIFE.name(), logMap.keySet());
        // 真人照片场景
        bizStatusMap.forEach((bizId, riskSafeHitTypeResponseDTO) -> {
            try {
                if (Objects.nonNull(riskSafeHitTypeResponseDTO) && StringUtils.isNotBlank(riskSafeHitTypeResponseDTO.getHitType())) {
                    RiskSafeCheckResponseDTO resp = new RiskSafeCheckResponseDTO();
                    resp.setBizId(bizId);
                    resp.setSafeId(RiskSafeIdConstant.ACCOUNTS_LIFE);
                    resp.setOperator("system");
                    resp.setHitTypeCode(riskSafeHitTypeResponseDTO.getHitType());
                    switch (riskSafeHitTypeResponseDTO.getHitType()) {
                        case "noExist":
                            // 风控那边没有数据，需要重新触发审核
                            AccountsLifeLog lifeLog = logMap.get(bizId);
                            ClonedPlanformAppVersionDto version = accountsInfoService.getAccountClonedPlanformAppVersion(lifeLog.getAccountUuid());
                            Integer appcode = CommConst.APPCODE_TAQU;
                            Integer cloned = version.getCloned();
                            RiskDetectEnum detectEnum = RiskDetectEnum.ACCOUNTS_LIFE;
                            if (AccountsLifeService.REAL_PERSON_SEQ.contains(lifeLog.getSeq()) && AccountsLifeService.isRealPersonVersion(version.getPlatformId(), version.getAppVersion())) {
                                if (AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(lifeLog.getVerifyStatus())) {
                                    log.info("风控卡审核，真人比对失败照片，跳过{}",lifeLog);
                                    return;
                                }
                                detectEnum = RiskDetectEnum.ACCOUNTS_LIFE_REAL_PERSON;
                                resp.setSafeId(RiskSafeIdConstant.ACCOUNTS_LIFE_REAL_PERSON);
                            }
                            ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(lifeLog.getAccountUuid(), lifeLog.getRemark(),
                                Lists.newArrayList(AvatarHandleService.getAvatarSrcPhotoUrl(lifeLog.getPhotoUrl())), CommConst.OLD_CLIENT_PACKAGE, bizId, detectEnum.name(), appcode, cloned);
                            if (contentCheckDTO == null ||
                                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
                                accountsLifeService.audit(resp);
                            }
                            break;
                        case "pass":
                        case "block":
                            accountsLifeService.audit(resp);
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("[我的生活卡审核]定时任务处理失败,bizId:{},errorMsg:{}", bizId, e.getMessage(), e);
            }
        });
    }
}
