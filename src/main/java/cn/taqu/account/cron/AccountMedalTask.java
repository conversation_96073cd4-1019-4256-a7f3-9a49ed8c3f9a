package cn.taqu.account.cron;

import cn.taqu.account.model.AccountsMedalInfo;
import cn.taqu.account.service.AccountsMedalInfoService;
import cn.taqu.core.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2019/1/16 3:27 PM
 */
@Component
@Lazy(false)
public class AccountMedalTask {
    private Logger logger = LoggerFactory.getLogger(AccountMedalTask.class);

    @Autowired
    private AccountsMedalInfoService accountsMedalInfoService;
    @Autowired
    private TaskHelper taskHelper;

    //@Scheduled(cron = "0 1,11,21 1 * * ?")
    @Scheduled(fixedDelay = 60 * 1000)
    public void revokeExpireCard() {
        if(TaskHelper.podStopping()){
            return;
        }
        String runKey = "medal:expr:run";
        if(taskHelper.isTaskRun(runKey, 1L, TimeUnit.MINUTES)) {
            return;
        }
//        logger.info("回收头衔任务开始");

        long timeSecond = DateUtil.getTodayEndSecond() - 86400;
        while (true) {
            if(TaskHelper.podStopping()){
                return;
            }
            try {
                List<AccountsMedalInfo> medalList = accountsMedalInfoService.getByEndTime(timeSecond, 100);
                if(CollectionUtils.isEmpty(medalList)) {
                    break;
                }

                medalList.forEach(medalInfo -> {
                    try {
                        accountsMedalInfoService.revokeAccountActor(medalInfo.getAccount_uuid(), medalInfo.getMedal_id().intValue(), 3);
                    } catch (Exception e) {
                        logger.error("回收过期头衔异常, uuid:" + medalInfo.getAccount_uuid(), e);
                    }
                });
            } catch (Exception e) {
                logger.error("回收过期头衔异常", e);
            }
        }
    }
}
