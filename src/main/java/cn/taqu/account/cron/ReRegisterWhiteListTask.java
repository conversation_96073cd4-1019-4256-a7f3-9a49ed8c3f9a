package cn.taqu.account.cron;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.taqu.account.service.ReRegisterLimitService;
import cn.taqu.account.service.ReRegisterWhiteListService;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.utils.DateUtil;

@Component
@Lazy(false)
public class ReRegisterWhiteListTask {
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private ReRegisterWhiteListService reRegisterWhiteListService;
    private static Logger LOGGER = LoggerFactory.getLogger(ReRegisterWhiteListTask.class);
    /**
     * redis key : 更新再注册白名单
     */
    private static final String UPDATE_REREGISTER_WHITELIST = "update_reRegister_Whitelist";

    /**
     * 锁
     */
    private static final String LOCK = "lock";


    /**
     * 过期时间60s
     */
    private static final int EXP_TIME= 60;

    /**
     * 10分钟执行一次，将超过时效的再注册白名单记录设置为失效
     */
    @Scheduled(cron = "30 0/10 * * * ? ")
    public void process(){
        if(TaskHelper.podStopping()){
            return;
        }
        // 同一个时间段只需要单个实例执行即可
        if (!redisLockUtil.setIfAbsent(UPDATE_REREGISTER_WHITELIST, LOCK, EXP_TIME)){
            return;
        }
        long endTime = DateUtil.currentTimeSeconds()- ReRegisterLimitService.TIMEOUT;
        // 再往一天，防止宕机
        long startTime = endTime - ReRegisterLimitService.TIMEOUT - 60 * 60 *24 ;
        LOGGER.info("执行定时任务，将超过时效的白名单置为失效");
        reRegisterWhiteListService.updateStatusByTime(startTime,endTime);
    }
}
