package cn.taqu.account.cron;

import cn.taqu.account.service.AccountsMemberInfoService;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 近期账号登陆定时任务
 *
 * <AUTHOR>
 * @date 2021/7/1
 */
@Component
@Lazy(false)
@Slf4j
public class AccountCurrentActiveTask {

    // 15天
    private static final Long SECOND_15_DAY = 60 * 60 * 24 * 15L;
    // 150天
    private static final Long SECOND_150_DAY = 60 * 60 * 24 * 150L;
    // 开发环境
    private static String DEV_PROFILE = "dev";
    // 默认appcode 1、他趣
    private static Integer DEFAULT_APPCODE = 1;
    // 推送队列
    private static final String MQ_PUSH_ASYNC_INVOKE_QUEUE = "push_async_invoke_queue";
    // 测试环境MQ url
    private static String CLIENT_TEST = "http://g2.test.k8s.taqu.cn/v2/Soa/jService";

    @Autowired
    private TaskHelper taskHelper;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Value("${spring.profiles.active}")
    private String activeProfiles;

    /**
     * 定时：每天7点【单例执行】
     * 功能：获取过去15天登陆过的用户uuid
     * 流程说明：
     *  1、查询活跃用户（数据量较大3000w，定时执行时效性要求低，采用全表扫描，每次根据id范围递增扫1000条，判断ios平台（platform_id为2）与近期15天的数据）
     *  2、将活跃用户uuid存到redis（根据对uuid进行hash操作，存到对应redis）
     */
    @Scheduled(cron = "0 0 7 * * ?")
    public void currentActiveInPass15Days(){
        if(TaskHelper.podStopping()){
            return;
        }
        // 确保任务不会并行 开发环境跳过
        String runKey = "accountCurrentLoginTask:currentActiveInPass15Days";
        if(!isDevProfile()){
            if(taskHelper.isTaskRun(runKey, 10L, TimeUnit.MINUTES)) {
                return;
            }
        }
        // redis存放key的数量
        Integer size = 5;
        // 从凌晨0点算起，倒推15天，作为活跃起始时间
        Long startTime = null;
        // 开发环境下，数据量较少，活跃用户范围为前150天, 其他环境活跃用户范围为15天
        if(isDevProfile()){
            startTime = DateUtil.getTodayBeginSecond() - SECOND_150_DAY;
        }else{
            startTime = DateUtil.getTodayBeginSecond() - SECOND_15_DAY;
        }
        log.info("----------------  开始：定时获取过去15天活跃的用户任务 活跃起始时间戳：{} ----------------", startTime);
//        collectActiveAccountUuids(size, startTime);
        log.info("----------------  结束：定时获取过去15天活跃的用户任务  ----------------");

        log.info("----------------  开始：开始批量推送消息给近15天活跃用户  ----------------");
//        handlerActiveAccountUuids(size);
        log.info("----------------  结束：开始批量推送消息给近15天活跃用户  ----------------");
    }

//    /**
//     * 采集用户活跃uuid
//     * @param size
//     */
//    private void collectActiveAccountUuids(Integer size, Long startTime){
//
//        // 起始id，每次递增id数 step，每次查询条数
//        // endId = startId + step
//        Long startId, endId;
//        Long step = 1000L;
//        Long maxId = accountsMemberInfoService.getMaxId();
//        // 根据最大id算出总页数
//        int totalPage = (int)Math.ceil(maxId * 1.0 / step);
//        for (int i = 0; i < totalPage; i++) {
//            if(TaskHelper.podStopping()){
//                return;
//            }
//            startId = i * step + 1;
//            endId = (i + 1) * step;
//            // 按批次查询符合条件的uuid
//            List<String> uuids = accountsMemberInfoService.batchGetActiveAccountUuidsInPassDays(startId, endId, PlatformEnum.IPHONE, startTime);
//            // 将用户uuid存到redis
//            if(uuids != null && uuids.size() > 0){
//                accountsMemberInfoService.pushActiveAccountUuid(size, uuids);
//            }
//        }
//    }

//    /**
//     * 从redis获取活跃用户uuid，推给活跃系统
//     * @param size  redis数量
//     */
//    private void handlerActiveAccountUuids(Integer size){
//        List<String> uuidArr = new ArrayList<>();
//        // 批量发送条数
//        Integer batchPushSize = 100;
//        for (int i = 0; i < size; i++) {
//            if(TaskHelper.podStopping()){
//                return;
//            }
//            String key = RedisKeyConstant.MEMBER_ACTIVE_15DAY_UUID_LIST.setArg(i);
//            while(memberStringRedisTemplate.opsForSet().size(key) > 0){
//                if(TaskHelper.podStopping()){
//                    return;
//                }
//                String uuid = memberStringRedisTemplate.opsForSet().pop(key);
//                // 如果待发送数组长度大于等于 批量发送长度，则先执行一次踊跃用户批量推送
//                if(uuidArr.size() >= batchPushSize){
////                    activeAccountPush(uuidArr);
//                }
//                uuidArr.add(uuid);
//            }
//        }
//        if(uuidArr.size() > 0){
////            activeAccountPush(uuidArr);
//        }
//    }


    /**
     * 判断当前环境是否为dev环境
     * @return  true 是dev环境 false 不是dev环境
     */
    private boolean isDevProfile(){
        return DEV_PROFILE.equals(activeProfiles);
    }
}
