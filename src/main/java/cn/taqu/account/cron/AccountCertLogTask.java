package cn.taqu.account.cron;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.AccountCertDataDTO;
import cn.taqu.account.service.AccountCertLogService;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 15:52
 */
@Component
@Lazy(false)
@Slf4j
public class AccountCertLogTask {

    @Autowired
    private AccountCertLogService accountCertLogService;

    @Scheduled(fixedDelay = 1000, initialDelay = 800)
    public void task1() {
        this.getAndProcess();
    }

    public void getAndProcess(){
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            for (int i = 0; i < CommConst.PER_BATCH_EXEC_1000; i++) {
                if(TaskHelper.podStopping()){
                    return;
                }
                MqResponse mqResponse = mqClient.pop(CommConst.MQ_CERT_LOG);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取认证记录日志数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }

                AccountCertDataDTO accountCertData = JsonUtils.stringToObject2(msgBody, AccountCertDataDTO.class);
                accountCertLogService.handleLog(accountCertData);

                mqResponse = mqClient.remove(CommConst.MQ_CERT_LOG, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除认证记录日志:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.warn("从TQMQ获取认证记录日志:" + msgBody + "处理失败", e);
        }
    }

}
