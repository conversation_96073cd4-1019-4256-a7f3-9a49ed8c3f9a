package cn.taqu.account.cron;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.account.common.ChangeAvatarTypeEnum;
import cn.taqu.account.common.RiskDetectEnum;
import cn.taqu.account.common.RiskDetectRiskLevelEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dto.ShumeiImgCheckResponseDTO;
import cn.taqu.account.dto.TencentImgQualityDTO;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.service.AccountsPhotoCheckFailService;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.account.service.BuryService;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.service.SystemNoticeService;
import cn.taqu.account.service.VersionSwitchService;
import cn.taqu.account.thread.ShumeImgCheckWithSmidCall;
import cn.taqu.account.thread.TencentImgQualityCall;
import cn.taqu.account.thread.ThirdPartFactory;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 账号注册
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-09 14:47
 */
@Component
@Lazy(false)
@Slf4j
public class AccountPostRegisterTask {

    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private BuryService buryService;
    @Autowired
    private AvatarHandleService avatarHandleService;
    @Autowired
    private AccountsPhotoDao accountsPhotoDao;

    @Autowired
    private AccountsPhotoCheckFailService accountsPhotoCheckFailService;

    @Autowired
    private SystemNoticeService systemNoticeService;

    public static String REG_AVATAR_ILLEGAL="reg_avatar_illegal";

    @Scheduled(fixedDelay = 1000, initialDelay = 200)
    public void process1() {
        this.getAndProcess();
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 400)
    public void process2() {
        this.getAndProcess();
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 600)
    public void process3() {
        this.getAndProcess();
    }

    @Scheduled(fixedDelay = 1000, initialDelay = 800)
    public void process4() {
        this.getAndProcess();
    }

    public void getAndProcess(){
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                MqResponse mqResponse = mqClient.pop(CommConst.MQ_POST_REGISTER);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取用户后置注册处理数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }

                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                // 用户uuid
                String accountUuid = MapUtils.getString(map, "account_uuid");
                String tracerId = MapUtils.getString(map, "tracer_id");
                if (StringUtils.isNotBlank(tracerId)) {
                    SoaBaseParams.fromThread().setDistinctRequestId(tracerId);
                }
                // 业务处理
                handler(accountUuid);

                mqResponse = mqClient.remove(CommConst.MQ_POST_REGISTER, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除后置注册处理信息:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            // TODO: 2022/10/17 图片质量检测一直报错 先暂时改成warn级别
//            log.error("从TQMQ获取到的用户后置注册处理信息:" + msgBody + "处理失败", e);
            log.warn("从TQMQ获取到的用户后置注册处理信息:" + msgBody + "处理失败", e);
        }
    }

    /**
     * 业务处理
     * @param accountUuid
     */
    private void handler(String accountUuid) {

        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"avatar_origin", "sex_type","platform_id","app_version"}, "1", false, false).get(accountUuid);
        String avatar = infoMap.getOrDefault("avatar_origin", "").toString();
        String sexType = MapUtils.getString(infoMap, "sex_type");
        String platformId=MapUtils.getString(infoMap, "platform_id");
        String appVersion=MapUtils.getString(infoMap, "app_version");

        // 头像
        if(StringUtils.isBlank(avatar)){
            log.info("注册后头像检测.uuid={},默认头像为空跳过检测", accountUuid);
            return;
        }

        // 默认头像
        if(AvatarHandleService.isDefAvatar(avatar)){
            log.info("注册后头像检测.uuid={},默认头像跳过检测", accountUuid);
            BuryService.pushBbsAvatarTask(accountUuid, ChangeAvatarTypeEnum.NONE_TO_DEFAULT.getValue());
            return;
        }

        // 头像检测
        String avatarUrl = avatarHandleService.getAvatarByVersion(avatar, null);
        avatarUrl = AccountsPhotoService.replacePhotoDomainCompare(avatarUrl);

        AccountsPhoto accountsPhotoOld = accountsPhotoDao.getAccountAvatar(accountUuid, null, false);
        Boolean result = detectAvatar(accountUuid, avatarUrl, Objects.nonNull(accountsPhotoOld) ? accountsPhotoOld.getId() : 0L);
        log.info("注册后头像检测.uuid={},result={}", accountUuid, result);
        // 机审通过
        if(result){
            // 机审通过
            BuryService.pushBbsAvatarTask(accountUuid, ChangeAvatarTypeEnum.NONE_TO_HAVE.getValue());
        }else{
            //初始注册头像是否违规，1 违规 0 没违规
            accountsInfoService.setRegisterAvatarStatus(accountUuid, NumberUtils.INTEGER_ONE);
            //保存审核失败的头像，供展示
            AccountsPhoto accountAvatar = accountsPhotoDao.getAccountAvatar(accountUuid, null, false);
            accountsPhotoCheckFailService.saveCheckFailAvatar(accountAvatar);
            // 机审不通过 推送社区违规头像
            buryService.toBbsAccountAvatarIll(accountUuid);
            // 头像置灰
//            accountsPhotoService.setAvatarIll(accountUuid);
            // 头像置默认
            accountsPhotoService.setAvatarAsDef(accountUuid, sexType);
            try{
                log.info("头像数据变更.头像数据从审核队列中移除.头像uuid={}.id={}", accountUuid, accountAvatar.getId().toString());
                SoaService.sendReviewPhotoSkip(null, accountAvatar.getId(), avatarUrl, "");
            }catch (Exception e){
                log.warn("头像数据变更.头像数据从审核队列中移除.失败头像uuid={}.id={}", accountUuid, accountAvatar.getId().toString(), e);
            }
            log.info("AccountPostRegisterTask platformId:{}|appVersion:{}|switch:{}|check:{}",platformId,appVersion
                    ,JsonUtils.objectToString(VersionSwitchService.SWITCH_20231201)
                    ,VersionSwitchService.SWITCH_20231201.isGeVersion(platformId, appVersion));
            if(VersionSwitchService.SWITCH_20231201.isGeVersion(platformId, appVersion)){
                //新版小秘书，接入推送中台，发安全助手
                systemNoticeService.processSystemNotice(accountUuid,REG_AVATAR_ILLEGAL,1,1);
            }else {
                // 小秘书
                accountsPhotoService.sendAvatarIllSystemMessage(accountUuid);
            }
        }

    }

    private Boolean detectAvatar(String accountUuid, String avatarUrl, Long photoId) {
        log.info("图片检测.uuid={}.avatar={}", accountUuid, avatarUrl);

        // 头像违规检测：数美
        // 图片质量检测：腾讯
        // AI图片检测：腾讯图片比对
        TencentImgQualityDTO tencentImgQualityDTO = null;
        Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, avatarUrl));
        try {
            tencentImgQualityDTO = imgQualityFuture.get(10000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.warn("线程池处理.InterruptedException", e);
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        } catch (ExecutionException e) {
            if(e.getCause() instanceof ServiceException){
                ServiceException serviceException = (ServiceException) e.getCause();
                throw serviceException;
            }else{
                log.warn("ExecutionException", e);
            }
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        } catch (TimeoutException e) {
            try {
                imgQualityFuture.cancel(true);
                log.warn("线程池处理.超时停止");
            } catch (Exception ee) {
                // 结束子进程
                log.warn("线程池处理.超时停止.结束子进程失败", ee);
            }
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        }
        log.info("图片质量检测.结果={}", tencentImgQualityDTO == null ? "" :JSON.toJSONString(tencentImgQualityDTO));
        if (tencentImgQualityDTO == null) {
            throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
        }
        if (!tencentImgQualityDTO.isImgQualityPass()) {
            return false;
        }

        ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = null;
        Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
        Future<ShumeiImgCheckResponseDTO> shumeiFuture = ThirdPartFactory.shumeiImgCheckPool.submit(new ShumeImgCheckWithSmidCall(accountUuid, avatarUrl, RiskDetectEnum.IMAGE_AVATAR.name(), CommConst.OLD_CLIENT_PACKAGE, appcode, cloned, photoId, SoaBaseParams.fromThread().getDistinctRequestId()));
        try {
            shumeiImgCheckResponseDTO = shumeiFuture.get(16000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.warn("线程池处理.InterruptedException", e);
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        } catch (ExecutionException e) {
            if (e.getCause() instanceof ServiceException) {
                ServiceException serviceException = (ServiceException) e.getCause();
                throw serviceException;
            }else{
                log.warn("ExecutionException", e);
            }
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        } catch (TimeoutException e) {
            try {
                shumeiFuture.cancel(true);
                log.warn("线程池处理.超时停止");
            } catch (Exception ee) {
                // 结束子进程
                log.warn("线程池处理.超时停止.结束子进程失败", ee);
            }
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        }
        log.info("图片违规检测.结果={}", shumeiImgCheckResponseDTO == null ? "" : JSON.toJSONString(shumeiImgCheckResponseDTO));
        if (shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), RiskDetectRiskLevelEnum.REJECT.name())) {
            return false;
        }

        return true;
    }

}
