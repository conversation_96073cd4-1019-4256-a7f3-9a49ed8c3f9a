package cn.taqu.account.cron;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.account.mq.AbstractPulsarConsumer;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.core.utils.JsonUtils;
import com.qiniu.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.event.EventConst.ACCOUNT_PHOTO_AUDIT_EVENT_PULSAR_TOPIC;
import static cn.taqu.account.event.EventConst.ACCOUNT_PHOTO_NUM_EVENT_PULSAR_TOPIC;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Component
public class AccountPhotoNumUpdateTask extends AbstractPulsarConsumer {

    public static final String TOPIC = "account_photo_audit_event";

    private static final String SINK_TOPIC = "account_photo_num_event";

    @Autowired
    private AccountsPhotoService accountsPhotoService;

    @Autowired
    private AccountsInfoService accountsInfoService;

    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;

    private final Producer<String> auditEventProducer;

    private final Producer<String> numEventProducer;

    public AccountPhotoNumUpdateTask(PulsarFactory pulsarFactory) {
        super(pulsarFactory, ACCOUNT_PHOTO_AUDIT_EVENT_PULSAR_TOPIC);
        auditEventProducer = pulsarFactory.producer(ACCOUNT_PHOTO_AUDIT_EVENT_PULSAR_TOPIC);
        numEventProducer = pulsarFactory.producer(ACCOUNT_PHOTO_NUM_EVENT_PULSAR_TOPIC);
    }

    public void sendAuditEvent(Map<String, Object> map) {
        String str = JsonUtils.objectToString(map);
        auditEventProducer.sendAsync(JsonUtils.objectToString(map)).thenAccept(m -> log.info("相册审核pulsar事件推送成功：{}", str));
    }

    @Override
    protected void onMessage(Consumer<String> consumer, Message<String> msg) {
        log.info("相册审核pulsar事件：{}", msg.getValue());
        accountProcess(msg.getValue());
        ack(consumer, msg);
    }

    // todo 停止消费
    @KafkaListener(topics = {TOPIC}, containerFactory = "kafkaListenerContainerFactory")
    public void process(List<ConsumerRecord<String, String>> records) {
        log.info("相册审核kafka事件：{}", records.size());
//        records.parallelStream().forEach(this::accountProcess);
        records.stream().map(ConsumerRecord::value).forEach(this::accountProcess);
    }

    private void accountProcess(String message) {
        if (StringUtils.isBlank(message)) {
            return;
        }

        if (message.length() <= 2) {
            return;
        }

        Map<String, String> map = JSONUtil.toBean(message, new TypeReference<Map<String, String>>() {
        }, false);

        if (MapUtils.isNotEmpty(map)) {
            String accountUuid = MapUtils.getString(map, "account_uuid");

            List<Map<String, String>> photos = accountsPhotoService.getAccountsAllPhoto(accountUuid, false);

            long photoNum = photos.stream()
                    .filter(photo -> MapUtils.getInteger(photo, "seq") != 1)
                    .count();

            Map<String, Object> infoByUuid = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"real_person_certification"}, "1", false, false).get(accountUuid);
            // 头像活体通过 或 已经活体 或 已真人认证
            Boolean isRealPersonCertification = infoByUuid.getOrDefault("real_person_certification", "2").equals(String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));

            // verify_status = 1 and status = 2
            long realPersonPhotoNum = isRealPersonCertification ?
                    photos.stream()
                            .filter(photo -> MapUtils.getInteger(photo, "seq") != 1)
                            .filter(photo -> MapUtils.getInteger(photo, "verify_status") == 1)
                            .filter(photo -> MapUtils.getInteger(photo, "status") == 2)
                            .count() : 0;


            Map<String, Object> sinkMap = new HashMap<>();
            sinkMap.put("account_uuid", accountUuid);
            sinkMap.put("photo_num", photoNum);
            sinkMap.put("real_person_photo_num", realPersonPhotoNum);
            sinkMap.put("create_time", DateUtil.currentSeconds());

//            kafkaSinkUtil.push(SINK_TOPIC, accountUuid, sinkMap);
            numEventProducer.newMessage(Schema.STRING).key(accountUuid).value(JsonUtils.objectToString(sinkMap))
                .sendAsync().thenAccept(m -> log.info("{}相册数量pulsar事件推送成功", accountUuid));

        }
    }
}
