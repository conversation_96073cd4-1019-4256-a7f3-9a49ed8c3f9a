package cn.taqu.account.cron;

import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.service.AccountsActiveLogService;
import cn.taqu.account.service.AccountsMemberInfoService;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.MultiMqClient;
import cn.taqu.core.common.client.MultiMqClientFactory;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户活跃相关数据处理
 */
@Component
@Lazy(false)
@Slf4j
public class AccountActiveTask {
    // g4 推入的数据
    private String queueName = "taqu_account_active";

    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    private AccountsActiveLogService accountsActiveLogService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    private static final String DELETE_ACCOUNTS_ACTIVE_LOG_TASK_REDIS_KEY = "deleteAccountsActiveLog:task:lock";

    @Scheduled(fixedDelay = 1000)
    public void processActiveTime() {
        this.getAndProcess();
    }

    @Scheduled(fixedDelay = 1000)
    public void processReview() {
        this.review();
    }

    private void getAndProcess() {
        String msgBody = null;
        try {
            MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                MqResponse mqResponse = mqClient.pop(queueName);
                if (mqResponse.fail()) {
                    log.error("从TQMQ获取用户最后活跃时间数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }
                
                /*
                 * {                                                
                 *  "accountType": "1",                              
                 *  "appcode": "1",                                  
                 *  "city_id": "303",                                
                 *  "cloned": "1",                                   
                 *  "ip": "*************",                           
                 *  "lang": "zh_cn",                                 
                 *  "latitude": "24.********",                       
                 *  "longitude": "118.********",                     
                 *  "mtracer_id": "3403c8474332da49c9fb16f6a99291e9",
                 *  "time": **********,                              
                 *  "token": "Maf463b2a22e078bab8b513a7f8c727f8",    
                 *  "uuid": "bgjeeigaehjjejje"                       
                 * }                                                
                 */
                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                // 用户uuid
                String uuid = MapUtils.getString(map, "uuid");
                // 时间
                Long time = MapUtils.getLong(map, "time");
                // 城市
                Integer cityId = MapUtils.getInteger(map, "city_id");
                // token
                String token = MapUtils.getString(map, "token", "");
                // latitude 纬度
                String latitude = MapUtils.getString(map, "latitude", "");
                // longitude 经度
                String longitude = MapUtils.getString(map, "longitude", "");
                // ip 
                String ip = MapUtils.getString(map, "ip", "");
                
                if (StringUtils.isBlank(uuid) || time == null) {
                    log.error("从TQMQ获取到的用户活跃数据:{}错误，uuid或者time为空", msgBody);
                } else {
                    accountsMemberInfoService.updateLastActiveTime(uuid, time, cityId);
                    // 2021.09.02 记录用户设备关系
                    if(StringUtils.isNotBlank(token)) {
                        accountsMemberInfoService.updateAccountAndTokenInfo(uuid, time, token, latitude, longitude, ip );
                    }
                }

                mqResponse = mqClient.remove(queueName, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从TQMQ删除用户活跃数据:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.error("从TQMQ获取到的用户活跃数据:" + msgBody + "处理失败", e);
        }
    }

    /**
     * 90天未活跃用户，头像、昵称、个人简介重新审核
     */
    private void review() {
        String msgBody = null;
        try {
            MultiMqClient mqClient = MultiMqClientFactory.createTqMq(SoaServer.GO.MULTI_TQMQ);
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                MqResponse mqResponse = mqClient.pop("returning_user_review", CommConst.NOT_ACTIVE_FOR_90_DAYS);
                if (mqResponse.fail()) {
                    log.error("从multiTqmq获取90天未活跃用户数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
                    break;
                }

                msgBody = mqResponse.getMsg_body();
                if (StringUtils.isBlank(msgBody)) {
                    break;
                }
                log.info("从multiTqmq获取90天未活跃用户数据:{}, msgId:{}", msgBody, mqResponse.getMsg_id());

                Map<Object, Object> map = JsonUtils.stringToObject2(msgBody, new TypeReference<Map<Object, Object>>() {
                });

                String uuid = MapUtils.getString(map, "account_uuid");
                if (StringUtils.isBlank(uuid)) {
                    log.error("从multiTqmq获取90天未活跃用户数据:{}错误，uuid为空", msgBody);
                } else {
                    accountsMemberInfoService.review(uuid);
                }

                mqResponse = mqClient.remove("returning_user_review", CommConst.NOT_ACTIVE_FOR_90_DAYS, mqResponse.getMsg_id());
                if (mqResponse.fail()) {
                    log.error("从multiTqmq删除90天未活跃用户数据:{}失败, msgId:{}", msgBody, mqResponse.getMsg_id());
                }
            }
        } catch (Exception e) {
            log.error("从multiTqmq获取90天未活跃用户数据:" + msgBody + "处理失败", e);
        }
    }

    /**
     * 每天9:40定时清理 accounts_active_log表中 用户最近20次活跃记录 以外的数据
     */
    @Scheduled(cron = "0 40 9 * * ?")
    public void deleteAccountsActiveLog() {
        if(TaskHelper.podStopping()){
            return;
        }
        //只需要一台机器执行即可
        if(!redisLockUtil.setIfAbsent(DELETE_ACCOUNTS_ACTIVE_LOG_TASK_REDIS_KEY, "lock" , 3000)){
            return;
        }
        log.info("----------------开始：定时清理 accounts_active_log表数据----------------");

        String key;
        String uuid = null;
        for (int i = 0; i < 12; i++) {
            if(TaskHelper.podStopping()){
                return;
            }
            char part = (char) (97 + i);
            key = RedisKeyConstant.ACCOUNT_ACTIVE_UUID.setArg(part + "");
            while (true) {
                if(TaskHelper.podStopping()){
                    return;
                }
                try {
                    uuid = accountStringRedisTemplate.opsForSet().pop(key);
                    if (StringUtils.isBlank(uuid)) {
                        break;
                    }
                    // 根据uuid清理数据
                    accountsActiveLogService.cleanActiveLogByUuid(uuid);
                    log.info("accounts_active_log表数据清理成功，uuid：{}", uuid);
                } catch (Exception e) {
                    log.warn("accounts_active_log表数据清理失败，key:" + key + "uuid:" + uuid, e);
                }
            }

        }

        log.info("----------------结束：定时清理 accounts_active_log表数据----------------");
    }
}
