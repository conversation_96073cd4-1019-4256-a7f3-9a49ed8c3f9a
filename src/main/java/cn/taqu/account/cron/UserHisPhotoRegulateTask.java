package cn.taqu.account.cron;

import cn.taqu.account.common.AliyunLiveFaceDetectStatus;
import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RiskSafeIdConstant;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dao.HisImageSimilarRecordDao;
import cn.taqu.account.dao.TransactionWrapper;
import cn.taqu.account.dto.ClonedPlanformAppVersionDto;
import cn.taqu.account.dto.ImgVectorDetectDto;
import cn.taqu.account.dto.ImgVectorDetectRespDto;
import cn.taqu.account.model.AccountsLife;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.HisImageSimilarRecord;
import cn.taqu.account.service.*;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.taqu.account.constant.RedisKeyConstant.*;

/**
 * 重复照片治理
 * <AUTHOR>
 * @date 2025/5/12 下午3:28
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserHisPhotoRegulateTask {

    private final StringRedisTemplate accountBizStringRedisTemplate;

    private final AccountsPhotoDao photoDao;

    private final AccountsLifeService accountsLifeService;

    private final RedisLockUtil lockUtil;

    private final HisImageSimilarRecordDao hisImageSimilarRecordDao;

    private final StringRedisTemplate accountStringRedisTemplate;

    public static Boolean isOpen = true;

    //private final RedissonClient redissonBizClient;

    private final Set<Integer> visibleStatus = Sets.newHashSet(2, 3, 4 , 7);

    @Resource
    private AvatarHandleService avatarHandleService;

    @Resource
    private AccountsInfoService infoService;

    @Resource
    private AccountsPhotoService accountsPhotoService;

    public Set<String> filter;

    @SuppressWarnings("all")
    @Scheduled(cron = "30 * * * * ?")
    public void tick() {
        String lockKey = "user:his:photo:regulate:lock2";
        boolean locked = lockUtil.lock(lockKey);
        if (!locked) {
            return;
        }
        log.info("历史重复图片治理start");
        try {
            String uuid = accountBizStringRedisTemplate.opsForList().leftPop(LAST_ACTIVE_USER_QUEUE_30, 4, TimeUnit.SECONDS);
            Date cutoff = DateUtils.addDays(new Date(), 14);
            accountBizStringRedisTemplate.expireAt(LAST_ACTIVE_USER_QUEUE_30, cutoff);
            accountBizStringRedisTemplate.expireAt(USER_PHOTO_DETECT_VERSION_SET, cutoff);
            // 用于判断切换新场景
            accountBizStringRedisTemplate.opsForValue().set(USER_PHOTO_DETECT_CUTOFF, String.valueOf(cutoff.getTime()), 14, TimeUnit.DAYS);
            if (uuid == null) {
                log.info("历史重复图片治理，队列无数据");
                return;
            }
            // 1分钟一次，每批20个
            int length = 20;
            List<String> uuids = new ArrayList<>(length);
            // 只处理真人
            Map<String, Object> infoMap = infoService.singleGetInfo(uuid, new String[] { UuidInfoField.REAL_PERSON_CERTIFICATION, UuidInfoField.SEX_TYPE });
            boolean isReal = "1".equals(MapUtils.getString(infoMap, UuidInfoField.REAL_PERSON_CERTIFICATION, "2"));
            boolean isFemale = "2".equals(MapUtils.getString(infoMap, UuidInfoField.SEX_TYPE, "1"));
            if (isReal && isFemale) {
                uuids.add(uuid);
            } else {
                accountBizStringRedisTemplate.opsForHash().increment(HIS_PHOTO_DETECT_STATS, "notRealOrFemal", 1);
                accountBizStringRedisTemplate.opsForSet().add(USER_PHOTO_DETECT_VERSION_SET, uuid);
            }
            for (int i = 0; i < length - 1; i++) {
                uuid = accountBizStringRedisTemplate.opsForList().leftPop(LAST_ACTIVE_USER_QUEUE_30);
                if (uuid == null) {
                    break;
                }
                // 只处理真人
                infoMap = infoService.singleGetInfo(uuid, new String[] { UuidInfoField.REAL_PERSON_CERTIFICATION, UuidInfoField.SEX_TYPE });
                isReal = "1".equals(MapUtils.getString(infoMap, UuidInfoField.REAL_PERSON_CERTIFICATION, "2"));
                isFemale = "2".equals(MapUtils.getString(infoMap, UuidInfoField.SEX_TYPE, "1"));
                if (isReal && isFemale) {
                    uuids.add(uuid);
                } else {
                    accountBizStringRedisTemplate.opsForHash().increment(HIS_PHOTO_DETECT_STATS, "notRealOrFemal", 1);
                    accountBizStringRedisTemplate.opsForSet().add(USER_PHOTO_DETECT_VERSION_SET, uuid);
                }
            }
            if (CollectionUtils.isEmpty(uuids)) {
                log.info("历史重复图片治理，无真人用户");
                return;
            }

            RateLimiter rateLimiter = RateLimiter.create(6);
            // 处理每个用户的图片
            for (String u : uuids) {
                processUserPhotos(u, rateLimiter);
            }
        } finally {
            lockUtil.unLock(lockKey);
        }
    }

    /**
     * 处理单个用户的所有图片
     * @param accountUuid 用户UUID
     * @param rateLimiter 限流器
     */
    private void processUserPhotos(String accountUuid, RateLimiter rateLimiter) {
        // 1. 真人用户头像(accounts_photo.seq=1)
        AccountsPhoto avatar = photoDao.getAccountAvatar(accountUuid, null, true);
        if (avatar != null) {
            uploadAvatarVector(accountUuid, avatar.getPhoto_url(), rateLimiter);
            accountBizStringRedisTemplate.opsForSet().add(USER_PHOTO_DETECT_VERSION_SET, accountUuid);

            // 2. 获取客态可见的自我介绍图片
            processUserIntroductionPhotos(accountUuid, rateLimiter);

            // 3. 获取客态可见的相册图片
            processUserAlbumPhotos(accountUuid, rateLimiter);

            // 4. 获取客态可见的生活图片
            processUserLifePhotos(accountUuid, rateLimiter);
        }
    }

    /**
     * 处理用户自我介绍图片
     * @param accountUuid 用户UUID
     * @param rateLimiter 限流器
     */
    private void processUserIntroductionPhotos(String accountUuid, RateLimiter rateLimiter) {
        try {
            // 获取客态可见的自我介绍图片
            Map<String, Object> infoMap = infoService.singleGetInfo(accountUuid, new String[] { UuidInfoField.PASS_INTRODUCTION_IMGS });
            if (infoMap == null) {
                log.warn("用户信息不存在，uuid={}", accountUuid);
                return;
            }

            String introductionImgStr = (String) infoMap.get(UuidInfoField.PASS_INTRODUCTION_IMGS);
            if (StringUtils.isNotBlank(introductionImgStr)) {
                List<Map<String, Object>> introductionImgList = JsonUtils.stringToObject(
                    introductionImgStr,
                    new TypeReference<List<Map<String, Object>>>(){}
                );
                if (introductionImgList != null) {
                    for (Map<String, Object> img : introductionImgList) {
                        if (img != null) {
                            String imgName = (String) img.get("img_name");
                            if (StringUtils.isNotBlank(imgName)) {
                                String photoUrl = AvatarHandleService.getAvatarOfSavePhoto(imgName);
                                detectSimilarity(
                                    accountUuid, photoUrl, 0L,
                                    HisImageSimilarRecord.BIZ_TYPE_INTRODUCTION,
                                    0, rateLimiter
                                );
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理用户自我介绍图片失败，uuid={}", accountUuid, e);
        }
    }

    /**
     * 处理用户相册图片
     * @param accountUuid 用户UUID
     * @param rateLimiter 限流器
     */
    private void processUserAlbumPhotos(String accountUuid, RateLimiter rateLimiter) {
        try {
            // 获取客态可见的相册图片(排除头像seq=1)
            List<Map<String, String>> albumPhotos = accountsPhotoService.getAccountCover(accountUuid, true);

            if (albumPhotos != null) {
                for (Map<String, String> photoMap : albumPhotos) {
                    if (photoMap == null) {
                        continue;
                    }

                    String photoUrl = photoMap.get("photo_url");
                    String seqStr = photoMap.get("seq");
                    Long photoId = null;
                    Integer seq = null;

                    try {
                        if (StringUtils.isNotBlank(seqStr)) {
                            seq = Integer.valueOf(seqStr);
                        }
                        String idStr = photoMap.get("id");
                        if (StringUtils.isNotBlank(idStr)) {
                            photoId = Long.valueOf(idStr);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("相册图片ID或seq转换失败，uuid={}, photoMap={}", accountUuid, photoMap);
                    }

                    if (StringUtils.isNotBlank(photoUrl)) {
                        photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
                        detectSimilarity(accountUuid, photoUrl, photoId,
                            HisImageSimilarRecord.BIZ_TYPE_ALBUM, seq, rateLimiter);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理用户相册图片失败，uuid={}", accountUuid, e);
        }
    }

    /**
     * 处理用户生活图片
     * @param accountUuid 用户UUID
     * @param rateLimiter 限流器
     */
    private void processUserLifePhotos(String accountUuid, RateLimiter rateLimiter) {
        try {
            // 获取纯粹的生活图片，不包含从相册兜底的数据
            List<AccountsLife> lifePhotos = accountsLifeService.getAccountsLifeByAccountUuid(accountUuid, false);

            if (lifePhotos != null) {
                // 过滤客态可见的生活图片
                List<AccountsLife> visibleLifePhotos = lifePhotos.stream()
                    .filter(Objects::nonNull)
                    .filter(AccountsLife::verifyVisitorStatus)
                    .filter(life -> AccountsLifeService.REAL_PERSON_SEQ.contains(life.getSeq()))
                    .filter(life -> StringUtils.isNotBlank(life.getPhotoUrl()))
                    .collect(Collectors.toList());

                for (AccountsLife life : visibleLifePhotos) {
                    String photoUrl = life.getPhotoUrl();
                    detectSimilarity(accountUuid, photoUrl, life.getId(), HisImageSimilarRecord.BIZ_TYPE_LIFE, life.getSeq(), rateLimiter);
                }
            }
        } catch (Exception e) {
            log.error("处理用户生活图片失败，uuid={}", accountUuid, e);
        }
    }

    /**
     * 检测头像相似度（只检测，不保存记录）
     * @param accountUuid 用户UUID
     * @param photoUrl 图片URL
     * @param rateLimiter 限流器
     */
    private void uploadAvatarVector(String accountUuid, String photoUrl, RateLimiter rateLimiter) {
        ImgVectorDetectDto dto = new ImgVectorDetectDto();
        dto.setSafeId(RiskSafeIdConstant.SIMILAR_PHOTO_DETECT);
        String url = avatarHandleService.getAvatarByVersion(photoUrl, null);
        String bizId = RiskSafeService.genBizId(url);
        dto.setBizId(bizId);
        dto.setImageUrl(Collections.singletonList(url));
        dto.setSenderUuid(accountUuid);
        // 流控
        rateLimiter.acquire();
        RiskSafeService.imageVectorAdd(dto);
    }

    /**
     * 检测图片相似度并保存记录
     * @param accountUuid 用户UUID
     * @param photoUrl 图片URL
     * @param originId 原始ID
     * @param bizType 业务类型
     * @param seq 排序
     * @param rateLimiter 限流器
     */
    private void detectSimilarity(String accountUuid, String photoUrl, Long originId,
                                  Integer bizType, Integer seq, RateLimiter rateLimiter) {
        ImgVectorDetectDto dto = new ImgVectorDetectDto();
        dto.setSafeId(RiskSafeIdConstant.SIMILAR_PHOTO_DETECT);
        String url = avatarHandleService.getAvatarByVersion(photoUrl, null);
        String bizId = RiskSafeService.genBizId(url);
        dto.setBizId(bizId);
        dto.setImageUrl(Collections.singletonList(url));
        dto.setSenderUuid(accountUuid);

        // 流控
        rateLimiter.acquire();
        ImgVectorDetectRespDto resp = RiskSafeService.imageVectorDetect(dto);
        accountBizStringRedisTemplate.opsForHash().increment(HIS_PHOTO_DETECT_STATS, "total", 1);
        accountBizStringRedisTemplate.expire(HIS_PHOTO_DETECT_STATS, 3, TimeUnit.DAYS);

        if (!resp.pass()) {
            // 保存相似记录
            accountBizStringRedisTemplate.opsForHash().increment(HIS_PHOTO_DETECT_STATS, "similar", 1);
            HisImageSimilarRecord record = new HisImageSimilarRecord();
            record.setOriginId(originId);
            record.setBizId(bizId);
            record.setAccountUuid(accountUuid);
            record.setBizType(bizType);
            record.setState(HisImageSimilarRecord.INITIAL);
            record.setPhotoUrl(photoUrl);
            record.setSeq(seq);
            Long now = DateUtil.currentTimeSeconds();
            record.setCreateTime(now);
            TransactionWrapper.me().wrap(() -> hisImageSimilarRecordDao.merge(record));
        }
    }

    /**
     * 清除重复照片
     */
    @Scheduled(cron = "0 */2 * * * ?")
    public void clearSimilarPhotoJob() {
        String lockKey = "account:similar:clear:lock2";
        boolean locked = lockUtil.lock(lockKey, 180000);
        if (!locked) {
            return;
        }

        // 开关配置
        if (!isOpen) {
            return;
        }

        try {
            filter = new HashSet<>();
            log.info("【图片重复治理】clearSimilarPhotoJob start...");

            // 上次最新一个id
            String key = "account:sim:last:id";
            String lastId = accountBizStringRedisTemplate.opsForValue().get(key);
            long id = StringUtils.isBlank(lastId) ? 0L : Long.parseLong(lastId);
            int limit = 100;

            // 一个定时任务洗20次
            for (int i = 0; i < 3; i++) {
                List<HisImageSimilarRecord> records = hisImageSimilarRecordDao.listPage(id, limit);
                if (CollectionUtils.isEmpty(records)) {
                    log.info("【图片重复治理】重复照片获取为空, > id:{}", id);
                    return;
                }

                // 根据业务类型分别处理重复照片
                processRecordsByBizType(records);

                // 重复表标记完成
                List<Long> ids = records.stream()
                    .filter(Objects::nonNull)
                    .map(HisImageSimilarRecord::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(ids)) {
                    hisImageSimilarRecordDao.batchUpdateStatus(ids, HisImageSimilarRecord.DONE);
                }

                id = records.get(records.size() -1).getId();
                accountBizStringRedisTemplate.opsForValue().set(key, String.valueOf(id));
            }
        } catch (Exception e) {
            log.warn("【图片重复治理】异常:", e);
        } finally {
            lockUtil.unLock(lockKey);
        }
    }

    /**
     * 根据业务类型分别处理重复照片记录
     * @param records 重复照片记录列表
     */
    private void processRecordsByBizType(List<HisImageSimilarRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        // 按业务类型分组
        Map<Integer, List<HisImageSimilarRecord>> recordsByBizType = records.stream()
            .filter(Objects::nonNull)
            .filter(r -> r.getBizType() != null)
            .collect(Collectors.groupingBy(HisImageSimilarRecord::getBizType));

        // 处理相册类型（包含头像）
        List<HisImageSimilarRecord> albumRecords = recordsByBizType.get(HisImageSimilarRecord.BIZ_TYPE_ALBUM);
        if (CollectionUtils.isNotEmpty(albumRecords)) {
            processAlbumRecords(albumRecords);
        }

        // 处理生活类型
        List<HisImageSimilarRecord> lifeRecords = recordsByBizType.get(HisImageSimilarRecord.BIZ_TYPE_LIFE);
        if (CollectionUtils.isNotEmpty(lifeRecords)) {
            processLifeRecords(lifeRecords);
        }

        // 处理自我介绍类型
        List<HisImageSimilarRecord> introductionRecords = recordsByBizType.get(HisImageSimilarRecord.BIZ_TYPE_INTRODUCTION);
        if (CollectionUtils.isNotEmpty(introductionRecords)) {
            processIntroductionRecords(introductionRecords);
        }
    }

    /**
     * 处理相册重复照片
     * @param records 相册重复记录
     */
    private void processAlbumRecords(List<HisImageSimilarRecord> records) {
        // 更新相册状态为仅主态可见
        updateAlbumPhotosToOwnerOnly(records);
        // 确认相册是否已经同步给我的生活，保证我的生活里也不存在重复照片
        clearSimilarLifePhoto(records);
        // 发送相册重复通知
        sendAlbumDuplicateNotice(records);
    }

    /**
     * 处理生活重复照片
     * @param records 生活重复记录
     */
    private void processLifeRecords(List<HisImageSimilarRecord> records) {
        // 更新生活照片状态为仅主态可见
        updateLifePhotosToOwnerOnly(records);
        // 发送生活重复通知
        sendLifeDuplicateNotice(records);
    }

    /**
     * 处理自我介绍重复照片
     * @param records 自我介绍重复记录
     */
    private void processIntroductionRecords(List<HisImageSimilarRecord> records) {
        // 更新自我介绍照片状态为仅主态可见
        updateIntroductionPhotosToOwnerOnly(records);
        // 发送自我介绍重复通知
        sendIntroductionDuplicateNotice(records);
    }

    /**
     * 更新相册照片状态为仅主态可见
     * @param records 相册重复记录
     */
    private void updateAlbumPhotosToOwnerOnly(List<HisImageSimilarRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Set<String> similarUrls = new HashSet<>();
        List<Long> photoIds = records.stream()
            .filter(Objects::nonNull)
            .filter(r -> r.getOriginId() != null)
            .map(r -> {
                if (r.getPhotoUrl() != null) {
                    similarUrls.add(r.getPhotoUrl());
                }
                return r.getOriginId();
            })
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(photoIds)) {
            return;
        }

        List<AccountsPhoto> photos = photoDao.findAccountsPhotoInIds(photoIds);
        if (CollectionUtils.isEmpty(photos)) {
            log.info("【图片重复治理】相册图片不存在:{}", JsonUtils.objectToString(photoIds));
            return;
        }

        List<Long> ids = photos.stream()
            .filter(Objects::nonNull)
            .filter(p -> similarUrls.contains(p.getPhoto_url()))
            .map(AccountsPhoto::getId)
            .collect(Collectors.toList());

        // 批量更新状态为仅主态可见
        log.info("【图片重复治理】批量更新相册为仅主态可见，idList:{}", ids);
        if (CollectionUtils.isNotEmpty(ids)) {
            photoDao.batchUpdateStatus(ids, AccountsPhoto.Status.ILLEGAL.getValue(), AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus());
            // 删除缓存
            deletePhotoCache(records);
        }
    }

    /**
     * 更新生活照片状态为仅主态可见
     * @param records 生活重复记录
     */
    private void updateLifePhotosToOwnerOnly(List<HisImageSimilarRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Set<String> similarUrls = new HashSet<>();
        List<String> uuids = records.stream()
            .filter(Objects::nonNull)
            .filter(r -> r.getAccountUuid() != null && r.getPhotoUrl() != null)
            .map(r -> {
                similarUrls.add(r.getPhotoUrl());
                return r.getAccountUuid();
            })
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(uuids)) {
            return;
        }

        Map<String, List<AccountsLife>> lifeMap = accountsLifeService.mGetAccountsLifeAllByRedis(uuids);
        if (lifeMap == null) {
            return;
        }

        // 收集需要更新的生活照片
        List<AccountsLife> toUpdate = lifeMap.values().stream()
            .flatMap(List::stream)
            .filter(Objects::nonNull)
            .filter(item -> AccountsLifeService.REAL_PERSON_SEQ.contains(item.getSeq()))
            .filter(item -> similarUrls.contains(item.getPhotoUrl()))
            .collect(Collectors.toList());

        // 批量更新状态
        toUpdate.forEach(item -> {
            item.setStatus(AccountsLife.Status.NONE.getValue());
            item.setVerifyStatus(AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus());
            accountsLifeService.updateAccountLifeStatus(item);
        });

        log.info("【图片重复治理】批量更新生活照片为仅主态可见，数量:{}", toUpdate.size());
    }

    /**
     * 更新自我介绍照片状态为仅主态可见
     * @param records 自我介绍重复记录
     */
    private void updateIntroductionPhotosToOwnerOnly(List<HisImageSimilarRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        // 按用户分组处理
        Map<String, List<HisImageSimilarRecord>> recordsByUser = records.stream()
            .filter(Objects::nonNull)
            .filter(r -> r.getAccountUuid() != null && r.getPhotoUrl() != null)
            .collect(Collectors.groupingBy(HisImageSimilarRecord::getAccountUuid));

        for (Map.Entry<String, List<HisImageSimilarRecord>> entry : recordsByUser.entrySet()) {
            String accountUuid = entry.getKey();
            List<HisImageSimilarRecord> userRecords = entry.getValue();

            updateUserIntroductionPhotos(accountUuid, userRecords);
        }
    }

    /**
     * 更新单个用户的自我介绍图片状态
     * @param accountUuid 用户UUID
     * @param userRecords 用户的重复记录
     */
    private void updateUserIntroductionPhotos(String accountUuid, List<HisImageSimilarRecord> userRecords) {
        // 获取用户当前的自我介绍信息
        String[] fields = {
            UuidInfoField.PASS_INTRODUCTION_IMGS,
            UuidInfoField.PASS_INTRODUCTION_STATUS
        };
        Map<String, Object> userInfo = infoService.singleGetInfo(accountUuid, fields);

        if (userInfo == null) {
            log.warn("【图片重复治理】获取用户信息失败：uuid:{}", accountUuid);
            return;
        }

        // 获取重复的图片URL集合
        Set<String> duplicateUrls = userRecords.stream()
                .map(HisImageSimilarRecord::getPhotoUrl)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 处理客态自我介绍图片（清空重复图片）
        String passIntroductionImgsStr = (String) userInfo.get(UuidInfoField.PASS_INTRODUCTION_IMGS);
        String updatedPassImgs = removeIntroductionDuplicatePhotos(passIntroductionImgsStr, duplicateUrls);

        // 更新Redis缓存（只清空客态图片，保留主态图片）
        Map<String, String> cacheUpdates = new HashMap<>();
        cacheUpdates.put(UuidInfoField.PASS_INTRODUCTION_IMGS, updatedPassImgs);

//        if (StringUtils.isBlank(updatedPassImgs) && StringUtils.isNotBlank(passIntroductionImgsStr)) {
//            cacheUpdates.put(UuidInfoField.PASS_INTRODUCTION_STATUS, "-1");
//            log.info("【图片重复治理】自我介绍客态图片被清空，重置状态：uuid:{}", accountUuid);
//        }

        // 批量更新缓存
        accountStringRedisTemplate.opsForHash().putAll(
                RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), cacheUpdates);

        log.info("【图片重复治理】清空自我介绍客态重复图片完成：uuid:{}, 移除重复图片数:{}",
                accountUuid, duplicateUrls.size());

        // 通知图片系统禁用重复图片
//        duplicateUrls.forEach(duplicateUrl -> {
//            try {
//                BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, duplicateUrl,
//                        CommConst.AVATAR_BUCKET, 1, DateUtil.currentTimeSeconds());
//            } catch (Exception e) {
//                log.warn("【图片重复治理】通知图片系统禁用失败：url:{}", duplicateUrl, e);
//            }
//        });
    }

    /**
     * 从自我介绍图片JSON中移除重复图片
     * @param introductionImgsStr 自我介绍图片JSON字符串
     * @param duplicateUrls 重复图片URL集合
     * @return 移除重复图片后的JSON字符串
     */
    private String removeIntroductionDuplicatePhotos(String introductionImgsStr, Set<String> duplicateUrls) {
        if (StringUtils.isBlank(introductionImgsStr) || CollectionUtils.isEmpty(duplicateUrls)) {
            return introductionImgsStr;
        }

        try {
            List<Map<String, Object>> introductionImgs = JsonUtils.stringToObject(
                introductionImgsStr,
                new TypeReference<List<Map<String, Object>>>(){}
            );

            if (CollectionUtils.isEmpty(introductionImgs)) {
                return introductionImgsStr;
            }

            // 过滤掉重复的图片
            List<Map<String, Object>> filteredImgs = introductionImgs.stream()
                    .filter(Objects::nonNull)
                    .filter(img -> {
                        String imgName = (String) img.get("img_name");
                        if (StringUtils.isBlank(imgName)) {
                            return true; // 保留空的图片记录
                        }

                        // 标准化图片URL进行比较
                        String standardUrl = AvatarHandleService.getAvatarOfSavePhoto(imgName);
                        return !duplicateUrls.contains(standardUrl);
                    })
                    .collect(Collectors.toList());

            return JsonUtils.objectToString(filteredImgs);
        } catch (Exception e) {
            log.error("【图片重复治理】解析自我介绍图片JSON失败：{}", introductionImgsStr, e);
            return introductionImgsStr; // 解析失败时返回原始数据
        }
    }

    /**
     * 发送相册重复通知
     * @param records 相册重复记录
     */
    private void sendAlbumDuplicateNotice(List<HisImageSimilarRecord> records) {
        sendTargetedNotice(records, "相册", "m=mine&a=album");
    }

    /**
     * 发送生活重复通知
     * @param records 生活重复记录
     */
    private void sendLifeDuplicateNotice(List<HisImageSimilarRecord> records) {
        sendTargetedNotice(records, "我的生活", "m=account&a=myLife");
    }

    /**
     * 发送自我介绍重复通知
     * @param records 自我介绍重复记录
     */
    private void sendIntroductionDuplicateNotice(List<HisImageSimilarRecord> records) {
        sendTargetedNotice(records, "自我介绍", "m=mine&a=selfIntroduce");
    }

    /**
     * 发送针对性的重复照片通知
     * @param records 重复记录
     * @param moduleType 模块类型（相册、我的生活、自我介绍）
     * @param jumpUrl 跳转地址
     */
    private void sendTargetedNotice(List<HisImageSimilarRecord> records, String moduleType, String jumpUrl) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        JSONArray content = new JSONArray();

        String info = String.format("检测到您的【%s】中和头像照片重复，系统已将其隐藏，仅您自己可见。%%s", moduleType);
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "点击重新上传");
        contentReplace.put("r", jumpUrl);
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "");

        JSONObject contentMap = new JSONObject();
        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");
        content.add(contentMap);

        Set<String> uuids = records.stream()
            .filter(Objects::nonNull)
            .map(HisImageSimilarRecord::getAccountUuid)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 布隆过滤器过滤重复im消息
        Iterator<String> iterator = uuids.iterator();
        while (iterator.hasNext()) {
            String uuid = iterator.next();
            String filterKey = uuid + "_" + moduleType; // 按模块类型区分过滤
            if (filter.contains(filterKey)) {
                log.info("【图片重复治理】已发过{}重复通知:{}", moduleType, uuid);
                iterator.remove();
                continue;
            }
            ClonedPlanformAppVersionDto versionDto = infoService.getAccountClonedPlanformAppVersion(uuid);
            if ("我的生活".equals(moduleType) && VersionSwitchService.SWITCH_20241129.isGeVersion(versionDto.getPlatformId(), versionDto.getAppVersion())) {
                MessageService.systemNotice(uuid, content, "系统消息", "system_link_text", 1);
            }
            if ("相册".equals(moduleType) && !VersionSwitchService.SWITCH_20241129.isGeVersion(versionDto.getPlatformId(), versionDto.getAppVersion())) {
                MessageService.systemNotice(uuid, content, "系统消息", "system_link_text", 1);
            }
            if ("自我介绍".equals(moduleType)) {
                MessageService.systemNotice(uuid, content, "系统消息", "system_link_text", 1);
            }
            filter.add(filterKey);
            log.info("【图片重复治理】发送{}重复通知成功:{}", moduleType, uuid);
        }
    }

    /**
     * 发送im通知
     *
     * @param records
     */
    private void sendImNotice(List<HisImageSimilarRecord> records) {
        JSONArray content = new JSONArray();

        String info = "检测到您的相册中存在重复照片，系统已将其隐藏，仅您自己可见。%s";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "点击重新上传");
        contentReplace.put("r", "m=mine&a=album");
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "");

        JSONObject contentMap = new JSONObject();
        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");
        content.add(contentMap);

        Set<String> uuids = records.stream().map(HisImageSimilarRecord::getAccountUuid).collect(Collectors.toSet());

        // 布隆过滤器过滤重复im消息
        //RBloomFilter<String> filter = getBloomFilter();
        Iterator<String> iterator = uuids.iterator();
        while (iterator.hasNext()) {
            String uuid = iterator.next();
            if (filter.contains(uuid)) {
                log.info("【图片重复治理】已发过小秘书通知:{}", uuid);
                iterator.remove();
                continue;
            }

            MessageService.systemNotice(uuid, content, "系统消息", "system_link_text", 1);
            filter.add(uuid);
        }

    }

    /**
     * 清空我的生活照片里相似的（用于相册同步到生活的情况）
     *
     * @param records
     */
    private void clearSimilarLifePhoto(List<HisImageSimilarRecord> records) {
        List<String> uuids = records.stream()
            .filter(Objects::nonNull)
            .map(HisImageSimilarRecord::getAccountUuid)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(uuids)) {
            return;
        }

        Map<String, List<AccountsLife>> lifeMap = accountsLifeService.mGetAccountsLifeAllByRedis(uuids);
        if (lifeMap == null) {
            return;
        }

        records.forEach(record -> {
            if (record != null && record.getAccountUuid() != null) {
                List<AccountsLife> lifeList = lifeMap.get(record.getAccountUuid());
                if (lifeList != null) {
                    // 查找相同的
                    lifeList.stream()
                        .filter(Objects::nonNull)
                        .filter(item -> Objects.equals(item.getPhotoUrl(), record.getPhotoUrl()))
                        .findAny()
                        .ifPresent(item -> {
                            item.setStatus(AccountsLife.Status.NONE.getValue());
                            item.setVerifyStatus(AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus());
                            accountsLifeService.updateAccountLifeStatus(item);
                            log.info("【图片重复治理】清空我的生活重复图片照片：uuid:{}, lifeId:{}", record.getAccountUuid(), item.getId());
                        });
                }
            }
        });

    }

    /**
     * 更新图片状态为别人不可见（已废弃，使用新的分类处理方法）
     *
     * @param records
     */
    @Deprecated
    public void updateOthersInvisible(List<HisImageSimilarRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Set<String> similarUrls = new HashSet<>();
        List<Long> photoIds = records.stream()
            .filter(Objects::nonNull)
            .filter(r -> r.getOriginId() != null)
            .map(r -> {
                if (r.getPhotoUrl() != null) {
                    similarUrls.add(r.getPhotoUrl());
                }
                return r.getOriginId();
            })
            .collect(Collectors.toList());
        List<AccountsPhoto> photos = photoDao.findAccountsPhotoInIds(photoIds);
        if (CollectionUtils.isEmpty(photos)) {
            log.info("【图片重复治理】相册图片不存在:{}", JsonUtils.objectToString(photoIds));
            return;
        }

        List<Long> ids = photos.stream().filter(p -> similarUrls.contains(p.getPhoto_url())).map(AccountsPhoto::getId).collect(Collectors.toList());

        // TODO 排序需要更新吗？先不重置试试看
        // 批量更新状态
        log.info("【图片重复治理】批量更新相册idList:{}", ids);
        if (CollectionUtils.isNotEmpty(ids)) {
            photoDao.batchUpdateStatus(ids, AccountsPhoto.Status.ILLEGAL.getValue(), AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus());
            // 删除缓存
            deletePhotoCache(records);
        }
    }

    /**
     * 删除缓存
     *
     * @param records
     */
    private void deletePhotoCache(List<HisImageSimilarRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Set<String> uuids = records.stream()
            .filter(Objects::nonNull)
            .map(HisImageSimilarRecord::getAccountUuid)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(uuids)) {
            return;
        }

        RedisSerializer<String> rs = accountStringRedisTemplate.getStringSerializer();
        accountStringRedisTemplate.executePipelined((RedisConnection connection) -> {
            uuids.forEach(uuid -> {
                try {
                    byte[] key = rs.serialize(RedisKeyConstant.ACCOUNT_PHOTO.setArg(uuid));
                    if (key != null) {
                        connection.del(key);
                    }
                } catch (Exception e) {
                    log.warn("删除缓存失败，uuid={}", uuid, e);
                }
            });
            return null;
        }, rs);
    }

    /**
     * 初始化
     */
    //@PostConstruct
    //public void initBloomFilter() {
    //    RBloomFilter<String> bloom = getBloomFilter();
    //    if (bloom.tryInit(500000, 0.1)) {
    //        log.info("【图片重复治理】bloomFilter初始化成功！");
    //        // TODO 先不设置过期时间，到时候手动删除
    //        //bloom.expire(30, TimeUnit.DAYS);
    //    }
    //
    //}

    /**
     * 获取布隆过滤器
     *
     * @return
     */
    //public RBloomFilter<String> getBloomFilter() {
    //    String key = "account:similar:bloom";
    //    return redissonBizClient.getBloomFilter(key, StringCodec.INSTANCE);
    //}

}
