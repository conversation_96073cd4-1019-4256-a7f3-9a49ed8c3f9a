package cn.taqu.account.cron;

import cn.hutool.core.date.DateUnit;
import cn.taqu.account.common.PlatformEnum;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.service.AccountsService;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.Identities;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 用户活跃相关数据处理
 */
//@Component
@Lazy(false)
@Slf4j
public class AccountDestoryTask {


    @Autowired
    private AccountsService accountsService;

    private String accounts = "bhbgdhgaibbdjeha,bhbgdhbfbiecideh,bhbgdgiajcfgejdg,bhbgdheaciacigfg,bhbgdgcchhdhdbbi,bhbgdgdfafbecgfh,bhbgdhcfadfhacji,bhbgdgffdijegbed,bhbgdhjdcfbdjeid,bhbgdhjhacefbife,bhbgdheijgdfhefa,bhbgdgbhhcfbfega,bhbgdgcdgihcacbi,bhbgdhbdcdbhibfd,bhbgdgdhjacbiabh,bhbgdgghihigiffe,bhbgdgbdfiicdjbg,bhbgdhggihgbcabh,bhbgdhgbcgcjifag,bhbgdgccebahiahg,bhbgdgdjggjbjcdi,bhbgdicchcajjfdf,bhbgdgchchhjbgfb,bhbgdhgfgbaifjce,bhbgdibfhhedhhdh,bhbgdhjdhfbjgfdf,bhbgdicdfdejegdh,bhbgdgiccdfibaif,bhbgdgbjicdfdhfb,bhbgdhghjcddibbb,bhbgdghccgbheach,bhbgdgibbeihjjge,bhbgdiaedccdifif,bhbgdiaahajgjccg,bhbgdgdhfbgcgjgh,bhbgdgbgcgghechd,bhbgdhjeeigjagbb,bhbgdicachbbdcdh,bhbgdhaeaaeidccb,bhbgdhgdcjjebhaa,bhbgdgbgfdceddaj,bhbgdgggiiicdedi,bhbgdgedfhbbbhce,bhbgdifjgaebgbei,bhbgdgbihfhdafjj,bhbgdhaejcfbidba,bhbgdhbfhfechdje,bhbgdgehibificdf,bhbgdgiibieifgjh,bhbgdhajjhhgiice,bhbgdgiabgifeiei,bhbgdibghjgbfige,bhbgdgigbigjdejg,bhbgdgheiecjfdbg,bhbgdhdbehcgiihj,bhbgdidihecfjdja,bhbgdhbehbbgidac,bhbgdgdjjbgibcgj,bhbgdiehjbhijejg,bhbgdhddgaabfaih,bhbgdgbibjbjefca,bhbgdgdbcaigdbhh,bhbgdgdaacedbabf,bhbgdgbfafggjbch,bhbgdgibdbgjejie,bhbgdhgjicceecjb,bhbgdgeaijgijdjj,bhbgdgiehcagidgf,bhbgdibjifejebjg,bhbgdigaaajeicag,bhbgdicadcgiiceh,bhbgdhigeegiaida,bhbgdhbcdgbcgaeg,bhbgdhceggdcgcfj,bhbgdgdhgdaafjih,bhbgdgidegjddccc,bhbgdhcafaebgifb,bhbgdhcjdaajjghc,bhbgdgfhebabgdhj,bhbgdgejjdjebfbb,bhbgdgdacigdhafb,bhbgdggbcafghibe,bhbgdgiajiijdjed,bhbgdgbcdghbjhhg,bhbgdhhhcbjghjfc,bhbgdhjgejhfchje,bhbgdgdfaecfcgff,bhbgdgcafcabbidj,bhbgdfiegdfehfdj,bhbgdgbggjijbdcg,bhbgdgghcijeaeib,bhbgdgcgbgjdcfec,bhbgdgccabejbacd,bhbgdibgfegcdidc,bhbgdgfjijegfgdi,bhbgdicbgjdaeebh,bhbgdicjbbijeceh,bhbgdgdggbfgjiea,bhbgdiceghdijhfc,bhbgdgccighidbci,bhbgdgjjhbcahica,bhbgdigdhhjeahdg,bhbgdgcbjchhhaba,bhbgdgiighjfaheh,bhbgdgbiedcgaffe,bhbgdhgddceabhac,bhbgdgdijbbjdbef,bhbgdgddchheieea,bhbgdgdafahjjahj,bhbgdgeafbadhdgc,bhbgdgfgbhdaicdc,bhbgdgegegcfaagf,bhbgdhehjhjhadha,bhbgdhecaaghficf,bhbgdgdhdbdfgjec,bhbgdgehaaaedbcj,bhbgdhigeaafcice,bhbgdgfbbjcbbgfe,bhbgdgechibhhgbh,bhbgdiegeijcddbj,bhbgdggaeabicgji,bhbgdggiedjfbgbe,bhbgdhgfacihjigd,bhbgdgcabbjhhhjd,bhbgdgciaigfchfj,bhbgdhgecgfdehie,bhbgdhjejbdcagga,bhbgdgcdjedbdcfd,bhbgdgbhcbifbeae,bhbgdieigjigfgag,bhbgdgieaabcdcjb,bhbgdhbdgfefgbjd,bhbgdgiadhdecihi,bhbgdhjhjeghjjeb,bhbgdhcjeagfjgie,bhbgdgffgfgggbhd,bhbgdhdiijhidfci,bhbgdhdggbfgfdeg,bhbgdgifjbjfdegi,bhbgdgfbhfddchcb,bhbgdggfaagejcaf,bhbgdgbiccajdfcj,bhbgdggeegjefbei,bhbgdiagaeadbhgf,bhbgdgcbbfddgjbc,bhbgdgbiidichgah,bhbgdghddbghdbfc,bhbgdhjccjaibdib,bhbgdhgdabdfhghe,bhbgdghbgiedajgf,bhbgdgjggbfcbfbd,bhbgdgihgcfeegdj,bhbgdggbhcjdjihb,bhbgdhfedgcedjai,bhbgdgfejbfdaaih,bhbgdgbeeebicaeh,bhbgdfgeggcfibha,bhbgdgbehgeheajf,bhbgdifejdagbefe,bhbgdgdhhabgdjjg,bhbgdghgbfachedi,bhbgdieeceiffagi,bhbgdgddhcgfgeih,bhbgdgbigbccbfhf,bhbgdgcacbcchiai,bhbgdgcgacbjcfbb,bhbgdhbihcjacghi";

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private AccountsInfoService accountsInfoService;

    private String handleLogKey = "destroy:uuid:";

    /**
     * 定时清理指定的账号
     */
//    @Scheduled(cron = "0 17 18 * * ?")
    public void deleteAccountsActiveLog() {
        if (TaskHelper.podStopping()) {
            return;
        }
        log.info("开始注销指定的账号:{}", accounts);
        String[] uuidsArray = accounts.split(",");
        List<String> uuidList = Arrays.asList(uuidsArray);
        if (CollectionUtils.isNotEmpty(uuidList)) {
            for (String uuid : uuidList) {
                SoaBaseParams.fromThread().setDistinctRequestId(Identities.uuid2()); //重新设置每一个uuid的请求id
                SoaBaseParams.fromThread().setAppcode("1");
                SoaBaseParams.fromThread().setCloned("1");
                SoaBaseParams.fromThread().setApp_version("4999");
                String platformName = getAccountPlatformName(uuid);
                log.info("查询到用户注册是的平台是:{}", platformName);
                platformName = StringUtils.isBlank(platformName) ? "iphone" : platformName;
                SoaBaseParams.fromThread().setPlatform_name(platformName);

                String reason = "账号迁移数据不全导致的注册多出一个账号";
                String key = handleLogKey + uuid;
                if (redisTemplate.hasKey(key)) {
                    log.info("账号uuid={},已注销过，无需重复处理。", uuid);
                    continue;
                }
                accountsService.destroyByAccountUuid(uuid, reason, false, false);
                redisTemplate.opsForValue().set(key, "1", 7 * DateUnit.DAY.getMillis(), TimeUnit.MILLISECONDS);
                log.info("完成账号uuid={}的注销处理", uuid);
            }
        }
    }

    public String getAccountPlatformName(String uuid) {
        try {
            Map<String, Object> info = accountsInfoService.getInfoByUuid(new String[]{uuid}, new String[]{"account_name", "avatar", "reg_platform"}, "1", true, false).get(uuid);
//        String accountName = MapUtils.getString(info, "account_name", "");
//        String avatar = MapUtils.getString(info, "avatar", "");
            String regPlatform = MapUtils.getString(info, "reg_platform", "");
            if (StringUtils.isBlank(regPlatform)) {
                return "iphone";
            }
            Integer regPlatformValue = Integer.valueOf(regPlatform);
            if (regPlatformValue.equals(PlatformEnum.ANDROID.getValue())) {
                return "android";
            }
            if (regPlatformValue.equals(PlatformEnum.IPHONE.getValue())) {
                return "iphone";
            }
            if (regPlatformValue.equals(PlatformEnum.IPAD.getValue())) {
                return "ipad";
            }
            return "iphone";
        }catch (Exception ex){
            log.warn("获取用户注册的平台过程中出现异常了，uuid={},异常信息为:",uuid,ex);
            return "iphone";
        }
    }
}
