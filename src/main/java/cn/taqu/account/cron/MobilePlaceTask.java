package cn.taqu.account.cron;

import cn.taqu.account.common.AccountActionTypeEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.service.AccountsService;
import cn.taqu.account.utils.mq.AntiSpamMqUtil;
import cn.taqu.core.common.constant.ICodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 手机号归属地查询定时任务
 */
@Component
@Lazy(false)
public class MobilePlaceTask {
    private Logger logger = LoggerFactory.getLogger(MobilePlaceTask.class);

    @Autowired
    private AccountsService accountsService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private AntiSpamMqUtil antiSpamMqUtil;

    @Scheduled(fixedRate = 1 * 1000)
    public void setMobilePlace() {
        int i=0;
        while (i++<=1000) {
            if(TaskHelper.podStopping()){
                return;
            }
            String data = accountStringRedisTemplate.opsForList().leftPop(RedisKeyConstant.MOBILE_PLACE_LIST.getPattern());
            if(StringUtils.isBlank(data)) {
                break;
            }

            String[] datas = StringUtils.split(data, "#");
            if(datas == null || datas.length != 2) {
                logger.warn("处理手机号码归属地，数据[{}]格式异常，正常格式应为'${uuid}#${mobile}'", data);
                continue;
            }

            String accountUuid = datas[0];
            String mobile = datas[1];

            logger.debug("开始处理手机号码归属地, uuid:{}, mobile:{}", accountUuid, mobile);

            try {
                accountsService.setMobilePlace(accountUuid, mobile);
                // 推入队列告知反垃圾系统
                antiSpamMqUtil.pushToAccountActionMq(accountUuid, "", AccountActionTypeEnum.EDIT_INFO,null);
            } catch (ServiceException se) {
                ICodeStatus codeStatus = se.getCodeStatus();
                if(codeStatus != null && codeStatus != SysCodeStatus.UNDEFINED) {
                    logger.error("处理手机号码归属地异常, uuid:{}, 原因:{}", accountUuid, codeStatus.getReasonPhrase());
                } else {
                    logger.error("处理手机号码归属地异常, uuid:"+ accountUuid, se);
                }
            } catch (Exception e) {
                logger.error("处理手机号码归属地异常, uuid:"+ accountUuid, e);
            }
        }
    }
}
