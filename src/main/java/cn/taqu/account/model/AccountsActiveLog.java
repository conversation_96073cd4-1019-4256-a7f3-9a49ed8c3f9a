package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Classname AccountsActiveLog
 * @Description 用户活跃记录表
 * @Date 2020/12/14 上午9:32
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_active_log")
public class AccountsActiveLog extends BaseEntity {
    /**
     * 账号uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 活跃时间(DATE_8格式)
     */
    @Column(name = "active_time")
    private Integer activeTime;
}
