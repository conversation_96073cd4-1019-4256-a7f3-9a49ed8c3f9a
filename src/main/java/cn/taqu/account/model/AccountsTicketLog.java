package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户ticket变化日志
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_ticket_log")
public class AccountsTicketLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -4892755206971251139L;

    private String old_ticket;
    private String new_ticket;
    private String device_token; // 设备token
    private String account_uuid;
    private Integer opt_type; // 引起ticket变化的操作类型 1:登录; 2:登出
    private Long create_time; // 创建时间
    private String client_ip;//客户端IP
    private Integer appcode;
}
