package cn.taqu.account.model;

import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "voice_sign_info")
public class VoiceSignInfo extends BaseEntity {


    /**
     * 用户uuid
     */
    private String account_uuid;
    /**
     * 语音签名url
     */
    private String voice_sign_url;
    /**
     * 语音签名时长
     */
    private Integer voice_sign_duration;
    /**
     * bucket，用于拼接语音url域名前缀
     */
    private String bucket;
    /**
     * 语音签名状态，0-审核中，1-审核通过，2-审核拒绝
     */
    private Integer status;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 审核原因
     */
    private String audit_reason;
    /**
     * 审核时间
     */
    private Long audit_time;
    /**
     * 创建时间
     */
    private Long create_time;

    /**
     * 来源
     */
    private String source;
    /**
     * 用户名
     */
//    @Transient
//    private String account_name;
//    @Transient
//    private String sex_type;

}
