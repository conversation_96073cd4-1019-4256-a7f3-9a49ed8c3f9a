package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Deprecated
@Getter
@Setter
@Entity
@Table( name = "accounts_level")
@DynamicUpdate
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountsLevel extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 4486138728498100592L;
	
	private Integer level_num;// 等级编号
	private Long min_level_score;// 等级经验下限
	private Long max_level_score;// 等级经验上限
	private String honor_man;// 男用头衔
	private String honor_women;// 女用头衔
	private String honor_unsex;// 头衔未知
	private String desc;// 描述
	private Integer privilege_id;// 该等级关联的权限0未关联
	private Long create_time;// 建立时间
	private Long update_time;// 最后更新时间
	private String privilege_desc;//等级权限的描述
	private Integer appcode;//appcode 应用类型码 

}
