package cn.taqu.account.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 我的生活操作审核记录表
 * 
 * <AUTHOR>
 * 2024年11月25日下午4:45:26
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_life_log")
public class AccountsLifeLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -4538699491440702433L;

    /**
     * 用户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;
    
    /**
     * 图片地址（相对）
     */
    @Column(name = "photo_url")
    private String photoUrl;
    
    /**
     * 图片宽
     */
    @Column(name = "width")
    private Integer width;
    
    /**
     * 图片高
     */
    @Column(name = "height")
    private Integer height;

    /**
     * 图片描述
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 排序字段
     */
    @Column(name = "seq")
    private Integer seq;

    /**
     * 审核状态，枚举
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 对比活体底图认证状态
     */
    @Column(name = "verify_status")
    private Integer verifyStatus;

    /**
     * 认证的错误信息
     */
    @Column(name = "error_msg")
    private String errorMsg;

    /**
     * 数美审核结果描述
     */
    @Column(name = "risk_resp_data")
    private String riskRespData;
    
    /**
     * 审核时间
     */
    @Column(name = "audit_time")
    private Long auditTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    
}
