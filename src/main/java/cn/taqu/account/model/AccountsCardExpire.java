package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 过期靓号
 */
@Entity
@Table(name = "accounts_card_expire")
@Getter
@Setter
public class AccountsCardExpire extends BaseEntity {
    @Column(name = "account_uuid")
    private String accountUuid;
    @Column(name = "card_id")
    private Long cardId;
    /**
     * 号码等级
     * @see {@link AccountsCard.Level}
     */
    private String level;
    /**
     * 到期时间
     */
    @Column(name = "end_time")
    private Long endTime;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;
}
