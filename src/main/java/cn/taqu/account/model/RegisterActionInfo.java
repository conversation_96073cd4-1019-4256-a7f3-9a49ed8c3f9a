package cn.taqu.account.model;

import cn.taqu.core.jackson.Date20Serializer;
import cn.taqu.core.orm.base.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@Table(name = "register_action_info")
@Entity
@DynamicInsert
@DynamicUpdate
@Getter
@Setter
public class RegisterActionInfo extends BaseEntity {
    @Column(name = "account_uuid")
    private String accountUuid;

    private String token;

    @Column(name = "action_ip")
    private String actionIp;

    private Integer appcode;

    private Integer cloned;

    @Column(name = "platform_id")
    private Integer platformId;

    private String channel;

    @Column(name = "app_version")
    private Long appVersion;

    @Column(name = "action_mode")
    private String actionMode;

    @JsonSerialize(using = Date20Serializer.class)
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 平台名称
     */
    @Column(name = "platform_name")
    private String platformName;
    /**
     * 网络情况，1:2G 2:3G 3:wifi 4:4G
     */
    private String access;
    /**
     * 别名
     */
    private String alias;
    /**
     * 性别 1:男 2:女
     */
    private Integer gender;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 经度加密
     */
    @Column(name = "longitude_cipher")
    private String longitudeCipher;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 纬度加密
     */
    @Column(name = "latitude_cipher")
    private String latitudeCipher;
    /**
     * 城市
     */
    private Integer city;
}
