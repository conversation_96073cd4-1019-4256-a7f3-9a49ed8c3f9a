package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "accounts_dress")
@DynamicInsert
@DynamicUpdate
public class AccountsDress extends BaseEntity {
	private String account_uuid;
	private Long dress_id;//装扮id，来自于社区
	private Integer status;//装饰状态 1:装扮;2:过期;3:用户卸下装扮
	private Integer dress_type;//装饰类型[1:头像挂件;2:帖子背景,3:聊天气泡]
	private Long create_time;
	private Long update_time;
}
