package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 账号靓号实体
 */
@Entity
@Table(name = "accounts_card")
@Getter
@Setter
public class AccountsCard extends BaseEntity {
    @Column(name = "account_uuid")
    private String accountUuid;
    @Column(name = "card_id")
    private Long cardId;
    /**
     * 到期时间
     */
    @Column(name = "end_time")
    private Long endTime;
    /**
     * 状态 0:未使用; 1:使用中
     */
    private Integer status;
    /**
     * 号码等级
     * @see {@link Level}
     */
    private String level;
    /**
     * 是否使用过, 0:否; 1:是;
     */
    @Column(name = "is_used")
    private Integer isUsed;
    /**
     * 来源 0:系统发放默认靓号; 1:后台发放; 2:别人赠送获得
     */
    private Integer source;
    @Column(name = "create_time")
    private Long createTime;

    public static class Level {
        public static final String SSS = "SSS";
        public static final String SS = "SS";
        public static final String S = "S";
        public static final String A = "A";
        public static final String B = "B";
        public static final String C = "C";
        /** 普通等级 **/
        public static final String N = "N";

        public static boolean isNormal(String level) {
            return !StringUtils.equalsAny(level, SSS, SS, S, A, B, C);
        }
    }
}
