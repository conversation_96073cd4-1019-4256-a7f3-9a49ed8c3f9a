package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 头像指导弹窗记录表
 * 
 * <AUTHOR>
 * @date 2025/6/24
 */
@Entity
@Table(name = "avatar_guide_popup_record")
@Getter
@Setter
public class AvatarGuidePopupRecord extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户UUID
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 弹窗类型 1:更改头像 2:需要美颜
     */
    @Column(name = "popup_type")
    private Integer popupType;

    /**
     * 弹窗日期
     * 弹窗时间=0，表示未弹窗过
     */
    @Column(name = "popup_date")
    private Integer popupDate;

    /**
     * 检查日期
     * 收到算法侧告知需要检查弹窗提示
     */
    @Column(name = "check_date")
    private Integer checkDate;

    /**
     * 头像URL
     */
    @Column(name = "avatar_url")
    private String avatarUrl;

    /**
     * 创建时间戳
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间戳
     */
    @Column(name = "update_time")
    private Long updateTime;

}
