package cn.taqu.account.model;

import cn.taqu.core.jackson.Date20Serializer;
import cn.taqu.core.orm.base.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/**
 * 账户登出日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-12 11:20
 */
@Table(name = "logout_action_info_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class LogoutActionInfoLog  extends BaseEntity {

    /**
     * 账户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 操作ip
     */
    @Column(name = "action_ip")
    private String actionIp;

    /**
     * 应用码
     */
    private Integer appcode;

    /**
     * 分身
     */
    private Integer cloned;

    /**
     * 平台 1:android 2:iphone 3:ipad
     */
    @Column(name = "platform_id")
    private Integer platformId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * app版本
     */
    @Column(name = "app_version")
    private Long appVersion;

    /**
     *  登出类型
     */
    @Column(name = "logout_type")
    private String logoutType;

    /**
     * 创建时间
     */
    @JsonSerialize(using = Date20Serializer.class)
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间戳(默认与创建时间一致)
     */
    @JsonSerialize(using = Date20Serializer.class)
    @Column(name = "update_time")
    private Long updateTime;

    /**
     * 设备号
     */
    private String token;

    /**
     * 平台名称
     */
    @Column(name = "platform_name")
    private String platformName;

    /**
     * 网络情况，1:2G 2:3G 3:wifi 4:4G
     */
    private String access;

    /**
     * 别名
     */
    private String alias;

    /**
     * 性别 1:男 2:女
     */
    private Integer gender;

    /**
     * 经度
     */
    private String longitude;
    /**
     * 经度加密
     */
    @Column(name = "longitude_cipher")
    private String longitudeCipher;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 纬度加密
     */
    @Column(name = "latitude_cipher")
    private String latitudeCipher;

    /**
     * 城市
     */
    private Integer city;
}
