package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 图片对比日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 15:14
 */
@Table(name = "photo_compare_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class PhotoCompareLog extends BaseEntity {

    /**
     * 图片
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 图片对比底图
     */
    @Column(name = "base_photo_url")
    private String basePhotoUrl;

    /**
     * 图片对比图
     */
    @Column(name = "verify_photo_url")
    private String verifyPhotoUrl;

    /**
     * 是否相似 0-不相似 1-相似
     */
    @Column(name = "similarity_status")
    private Integer similarityStatus;

    /**
     * 图片对比的相似度
     */
    @Column(name = "similarity_score")
    private Float similarityScore;

    /**
     * 失败时信息
     */
    @Column(name = "error_msg")
    private String errorMsg;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    @Column(name = "compare_type")
    private Integer compareType;
}
