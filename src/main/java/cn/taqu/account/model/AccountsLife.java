package cn.taqu.account.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 我的生活状态表
 * 
 * <AUTHOR>
 * 2024年11月25日下午4:45:26
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_life")
public class AccountsLife extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -4538699491440702433L;

    /**
     * 用户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;
    
    /**
     * 图片地址（相对） 主态
     */
    @Column(name = "photo_url_origin")
    private String photoUrlOrigin;
    
    /**
     * 图片宽 主态
     */
    @Column(name = "width_origin")
    private Integer widthOrigin;
    
    /**
     * 图片高 主态
     */
    @Column(name = "height_origin")
    private Integer heightOrigin;

    /**
     * 图片描述 主态
     */
    @Column(name = "remark_origin")
    private String remarkOrigin;
    
    /**
     * 图片地址（相对）
     */
    @Column(name = "photo_url")
    private String photoUrl;
    
    /**
     * 图片宽
     */
    @Column(name = "width")
    private Integer width;
    
    /**
     * 图片高
     */
    @Column(name = "height")
    private Integer height;
    
    /**
     * 图片描述
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 排序字段
     */
    @Column(name = "seq")
    private Integer seq;

    /**
     * 审核状态，枚举 主态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 对比活体底图认证状态 主态
     */
    @Column(name = "verify_status")
    private Integer verifyStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    

    public enum Status {

        /**
         * 默认 -1
         */
        NONE(-1),
        /**
         * 审核中 0
         */
        AUDITING(0),
        /**
         * 审核通过 1
         */
        AUDIT_SUCCESS(1),
        /**
         * 审核拒绝 2 正常不会有，审核拒绝删除图片
         */
        AUDIT_FAIL(2),
        /**
         * 相册机审通过 3
         */
        PHOTO_AUTO_CHECK(3);

        private int value;
        
        Status(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
        
        public static Status of(Integer value) {
            if(value == null) {
                return null;
            }
            Status[] enums = Status.values();
            for (Status e : enums) {
                if (e.getValue()==value) {
                    return e;
                }
            }
            return null;
        }

    }

    /**
     * 审核中状态，主态编辑判断用
     * 
     * @return
     */
    public boolean verifyAuditingStatus() {
        return status == Status.AUDITING.getValue();
    }
    
    /**
     * 客态可见状态
     * 
     * @return
     */
    public boolean verifyVisitorStatus() {
        return status == Status.AUDIT_SUCCESS.getValue()
            || status == Status.PHOTO_AUTO_CHECK.getValue();
    }
    
    public static AccountsLife.Status accountsPhotoStatusToAccountsLifeStatus(AccountsPhoto.Status status) {
        if(status == null) {
            return Status.NONE;
        }
        // 线上看数据，目前只看到1，2，6，8，其他兜底处理
        switch (status) {
            case ILLEGAL:
            case NO_CHECK:
            case RE_CHECK:
            case AUTO_CHECK:
            case ACCOUNTS_LIFE_AUTO_CHECK:
                return Status.NONE; 
                
            case PASS:
            case DEFAULT_PIC:
                return Status.AUDIT_SUCCESS; 
                
            case NO_CHECK_ACCOUNT:
            case REVIEW_90:
            case ONLY_AUTO_CHECK:
                return Status.PHOTO_AUTO_CHECK; 
                
            default:
                return Status.NONE; 
        }
    }
    
}
