package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 账号标签实体类，对应数据库中的`jiaoliuqu`.`school`表
 */
@Data
@Entity
@Table(name = "accounts_ideal_target_log")
public class AccountsIdealTargetLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @Column(name = "account_uuid")
    private String accountUuid;


    @Column(name = "ideal_target")
    private String idealTarget;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 风控响应
     */
    @Column(name = "risk_resp_data")
    private String riskRespData;


    /**
     *
     */
    @Column(name = "audit_time")
    private Long auditTime;

    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;

}
