package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 个人封面状态表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 09:35
 */
@Deprecated
@Table(name = "accounts_home_cover_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsHomeCoverLog extends BaseEntity {

    /**
     * 账户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 封面id
     */
    @Column(name = "home_cover_id")
    private Long homeCoverId;

    /**
     * 图片地址
     */
    @Column(name = "photo_url")
    private String photoUrl;

    /**
     * 图片地址
     */
    @Column(name = "pass_photo_url")
    private String passPhotoUrl;

    /**
     * 审核等级
     */
    @Column(name = "risk_level")
    private String riskLevel;

    /**
     * 审核描述
     */
    @Column(name = "risk_description")
    private String riskDescription;

    /**
     * 图片违规检测订单号
     */
    @Column(name = "img_check_order_no")
    private String imgCheckOrderNo;

    /**
     * 图片质量检测订单号
     */
    @Column(name = "img_quality_order_no")
    private String imgQualityOrderNo;

    /**
     * 图片地址
     */
    @Column(name = "home_cover_status")
    private Integer homeCoverStatus;

    /**
     * 审核时间
     */
    @Column(name = "check_time")
    private Long checkTime;

    /**
     * 操作员
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}
