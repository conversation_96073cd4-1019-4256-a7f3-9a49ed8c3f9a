package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 
 * 地理位置表
 * <AUTHOR>
 * 2018年1月3日 下午3:55:11
 */
@Entity
@Table(name = "region")
public class Region extends BaseEntity implements Serializable {
	
	private static final long serialVersionUID = 1832397260969265012L;

	private String sign;//标记
	private Long parent_id;//父地区id
	private String region_path;//
	private Integer level;//地区级别
	private String region_name;//地区名称
	private Integer is_unavailable;//是否不使用   0-使用，1-不使用
	private Integer sort;//排序
	private Integer postcode;//邮编
	private String longitude;//经度
	private String latitude;//纬度
	
	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}
	public Long getParent_id() {
		return parent_id;
	}
	public void setParent_id(Long parent_id) {
		this.parent_id = parent_id;
	}
	public String getRegion_path() {
		return region_path;
	}
	public void setRegion_path(String region_path) {
		this.region_path = region_path;
	}
	public Integer getLevel() {
		return level;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	public String getRegion_name() {
		return region_name;
	}
	public void setRegion_name(String region_name) {
		this.region_name = region_name;
	}
	public Integer getIs_unavailable() {
		return is_unavailable;
	}
	public void setIs_unavailable(Integer is_unavailable) {
		this.is_unavailable = is_unavailable;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public Integer getPostcode() {
		return postcode;
	}
	public void setPostcode(Integer postcode) {
		this.postcode = postcode;
	}
	public String getLongitude() {
		return longitude;
	}
	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
	public String getLatitude() {
		return latitude;
	}
	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}
	
}
