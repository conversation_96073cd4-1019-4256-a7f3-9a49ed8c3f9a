package cn.taqu.account.model;

import lombok.Data;

import javax.persistence.*;

/**
 * 用户认证信息变更记录日志表
 */
@Data
@Entity
@Table(name = "accounts_certification_log")
public class AccountsCertificationLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String account_uuid;
    private String content;
    private String operator_name;
    private Long create_time;

}