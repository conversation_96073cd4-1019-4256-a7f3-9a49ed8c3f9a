package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "account_freeze_admin_logout_log")
@Entity
@Data
public class AccountFreezeAdminLogoutLog extends BaseEntity {

    /**
     * 图片
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 操作人
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

}
