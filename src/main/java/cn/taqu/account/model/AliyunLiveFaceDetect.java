package cn.taqu.account.model;


import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 活体人脸验证成功后的记录表
 * 表名会误导，之后不一定是用阿里云
 */
@Getter
@Setter
@Entity
@Table(name = "aliyun_live_face_detect")
@DynamicInsert
@DynamicUpdate
public class AliyunLiveFaceDetect extends BaseEntity {


    /**
     * 用户uuid（唯一键）
     */
    private String account_uuid;

    /**
     * 活体人脸检测成功后采集的底图
     */
    private String base_photo_url;

    /**
     * 活体人脸检测成功后，传给阿里活体人脸检测的验证图
     */
    private String verify_photo_url;

    /**
     * 创建时间
     */
    private Long create_time;

    /**
     * 更新时间
     */
    private Long update_time;

    /**
     * 账号性别 -1 默认值 0-未知 1-男 2-女
     */
    private Integer account_gender;

    /**
     * 图片性别 -1 默认值 0-未知 1-男 2-女
     */
    private Integer base_photo_gender;

    /**
     * 性别比对结果 -1 默认值 0-性别不一致 1-性别一致
     */
    private Integer gender_compare_result;

    /**
     * 来源
     */
    private String source;
}
