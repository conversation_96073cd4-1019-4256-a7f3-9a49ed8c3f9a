package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@Entity
@Getter
@Setter
@Table(name = "personal_profile_info")
public class PersonalProfileInfo extends BaseEntity {

    /**
     * 用户uuid
     */
    private String account_uuid;

    /**
     * 个人简介内容
     */
    private String personal_profile;

    /**
     * 阿里文本检测结果，0-不通过，1-通过，2-审核
     */
    private Integer risk_level;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long create_time;

    /**
     * 审核时间
     */
    private Long audit_time;

    /**
     * 数美suggestion
     */
    private String suggestion;

    /**
     * 订单号
     */
    private String request_id;

    /**
     * 完整结果
     */
    private String response_str;

    /**
     * 用户昵称
     */
    @Transient
    private String account_name;
    /**
     * 用户昵称
     */
    @Transient
    private String account_name_default;

    @AllArgsConstructor
    @Getter
    public enum status{
        //审核中
        AUDITING(0),
        //审核通过
        AUDIT_SUCCESS(1),
        //审核拒绝
        AUDIT_FAIL(2),
        //90天重新活跃，复审
        REVIEW_90(3),
        ;

        private Integer value;
    }
}
