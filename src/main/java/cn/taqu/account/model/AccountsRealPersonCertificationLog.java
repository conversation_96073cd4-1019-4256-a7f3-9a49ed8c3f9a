package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-20 10:02
 */
@Table(name = "accounts_real_person_certification_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsRealPersonCertificationLog extends BaseEntity {

    /**
     * 账户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 活体时间
     */
    @Column(name = "live_face_detect_time")
    private Long liveFaceDetectTime;

    /**
     * 真人认证时间
     */
    @Column(name = "real_person_certification_time")
    private Long realPersonCertificationTime;

    /**
     * 来源
     */
    @Column(name = "source")
    private String source;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;

}
