package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户关怀模式名单
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Entity
@Table(name = "care_model")
@Getter
@Setter
public class CareModel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户UUID
     */
    @Column(name = "account_uuid", nullable = false, length = 64)
    private String accountUuid;

    /**
     * 用户版本号
     */
    @Column(name = "version", nullable = false)
    private Long version;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

}
