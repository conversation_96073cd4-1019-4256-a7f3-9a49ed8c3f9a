package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户创建/修改昵称记录表
 * <AUTHOR>
 * 2017年3月23日 下午4:16:16
 */
@Entity
@Table(name = "accounts_set_name_log")
public class AccountsSetNameLog extends BaseEntity implements Serializable {

	private static final long serialVersionUID = -1887196738174717884L;

	private Long account_id = 0L;//用户id
	private String account_uuid;//用户uuid
	private String account_name_old;//旧昵称
	private String account_name_new;//新昵称
	private Integer default_nick_name;// 默认昵称
	private Integer old_default_nick_name;// 旧昵称默认昵称
	private Integer type;//操作类型	1-创建 2-修改
	private String oper;//操作者 用户uuid 或 后台用户id
	private Integer oper_type;//操作类型：1-app操作，2-后台操作
	private Long create_time;//创建时间

	public Long getAccount_id() {
		return account_id;
	}
	public void setAccount_id(Long account_id) {
		this.account_id = account_id;
	}
	public String getAccount_uuid() {
		return account_uuid;
	}
	public void setAccount_uuid(String account_uuid) {
		this.account_uuid = account_uuid;
	}
	public String getAccount_name_old() {
		return account_name_old;
	}
	public void setAccount_name_old(String account_name_old) {
		this.account_name_old = account_name_old;
	}
	public String getAccount_name_new() {
		return account_name_new;
	}
	public void setAccount_name_new(String account_name_new) {
		this.account_name_new = account_name_new;
	}

	public Integer getDefault_nick_name() {
		return default_nick_name;
	}

	public void setDefault_nick_name(Integer default_nick_name) {
		this.default_nick_name = default_nick_name;
	}

	public Integer getOld_default_nick_name() {
		return old_default_nick_name;
	}

	public void setOld_default_nick_name(Integer old_default_nick_name) {
		this.old_default_nick_name = old_default_nick_name;
	}

	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Long getCreate_time() {
		return create_time;
	}
	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}
	public String getOper() {
		return oper;
	}
	public void setOper(String oper) {
		this.oper = oper;
	}
	public Integer getOper_type() {
		return oper_type;
	}
	public void setOper_type(Integer oper_type) {
		this.oper_type = oper_type;
	}

}
