package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 账号标签实体类，对应数据库中的`jiaoliuqu`.`school`表
 */
@Data
@Entity
@Table(name = "school")
public class School extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @Column(name = "school_name")
    private String schoolName;

    /**
     * 5大专 6大学
     */
    @Column(name = "school_type")
    private Long schoolType;


    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;

}
