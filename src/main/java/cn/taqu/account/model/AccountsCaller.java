package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;

@Deprecated
@Setter
@Getter
@Entity
@Table(name = "accounts_caller")
public class AccountsCaller extends BaseEntity {
    /** 用户uud **/
    private String account_uuid;
    /** 语音聊主状态 0:不是语音聊主; 1:是语音聊主; **/
    private Integer voice_status;
    /** 视频聊主状态 0:不是视频聊主; 1:是视频聊主; **/
    private Integer video_status;
    /** 创建时间 **/
    private Long create_time;
}
