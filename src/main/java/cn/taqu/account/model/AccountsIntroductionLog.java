package cn.taqu.account.model;

import cn.taqu.account.dto.IntroductionPhotoDTO;
import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 10:50
 */
@Table(name = "accounts_introduction_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsIntroductionLog extends BaseEntity {

    /**
     * uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 内容
     */
    private String content;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    /**
     * 审核时间
     */
    @Column(name = "audit_time")
    private Long auditTime;

    /**
     * 昵称
     */
    @Transient
    private String accountName;

    /**
     * 性别
     */
    @Transient
    private String sexType;

    /**
     * 头像
     */
    @Transient
    private String avatar;
    /**
     * 图片集合
     */
    @Transient
    private List<IntroductionPhotoDTO> photoList;

}
