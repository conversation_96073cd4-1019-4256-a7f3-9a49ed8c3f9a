package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 10:59
 */
@Table(name = "accounts_introduction_photo_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsIntroductionPhotoLog extends BaseEntity {

    /**
     * uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 存储空间
     */
    private String bucket;

    /**
     * 图片url
     */
    @Column(name = "photo_url")
    private String photoUrl;

    /**
     * 自我介绍主键id
     */
    @Column(name = "introduction_log_id")
    private Long introductionLogId;

    /**
     * 图片宽度
     */
    @Column(name = "width")
    private Integer width;

    /**
     * 图片高度
     */
    @Column(name = "height")
    private Integer height;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}
