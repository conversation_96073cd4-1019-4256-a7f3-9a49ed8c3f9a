package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 等级勋章表
 * @ClassName AccountsMedal.java
 * <AUTHOR>
 * @date 2015年9月28日 下午7:05:14
 */
@Entity
@Table( name = "accounts_medal")
@DynamicUpdate
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountsMedal extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1576581517496818108L;
	private String medal_name;//头衔名称
	
	/**
	 * 等级勋章图片地址
	 * 2024.01.11 备注，现在的数据，已经没有用这个字，数据库是空字符串
	 */
	@Deprecated 
	private String pic_url;
	private Long privilege_id;//权限ID
	private Long create_time;//创建时间
	private Long update_time;//编辑时间
	private Integer status = 1;//状态 0:禁用; 1:生效;

	public AccountsMedal() {
	}
	
	public String getMedal_name() {
		return medal_name;
	}
	public void setMedal_name(String medal_name) {
		this.medal_name = medal_name;
	}
	public String getPic_url() {
		return pic_url;
	}
	public void setPic_url(String pic_url) {
		this.pic_url = pic_url;
	}
	public Long getPrivilege_id() {
		return privilege_id;
	}
	public void setPrivilege_id(Long privilege_id) {
		this.privilege_id = privilege_id;
	}
	public Long getCreate_time() {
		return create_time;
	}
	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}
	public Long getUpdate_time() {
		return update_time;
	}
	public void setUpdate_time(Long update_time) {
		this.update_time = update_time;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
}
