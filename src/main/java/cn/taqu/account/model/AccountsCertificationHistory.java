package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户实名成功历史表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-29 13:51
 */
@Table(name = "accounts_certification_history")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsCertificationHistory extends BaseEntity {

    /**
     * 账号uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

//    /**
//     * 真实姓名
//     */
//    @Column(name = "real_name")
//    private String realName;
//
//    /**
//     * 身份证号
//     */
//    @Column(name = "identity_no")
//    private String identityNo;
    /**
     * 身份证件类型：1-身份证；2-外国人；3-台湾居民；4-港澳居民; 0-未认证
     */
    @Column(name = "identity_type")
    private Integer identityType;
//    /**
//     * 用户提现账号
//     */
//    @Column(name = "reward_account")
//    private String rewardAccount;
    /**
     * 提现账户是否完成认证
     */
    @Column(name = "reward_account_checked")
    private Integer rewardAccountChecked;
    /**
     * 操作人
     */
    @Column(name = "operator")
    private String operator;
    /**
     * 操作时间
     */
    @Column(name = "operator_time")
    private Long operatorTime;
    /**
     * 底图
     */
    @Column(name = "base_photo_url")
    private String basePhotoUrl;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}
