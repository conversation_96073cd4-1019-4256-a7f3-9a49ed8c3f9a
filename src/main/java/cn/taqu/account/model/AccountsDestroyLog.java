package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_destroy_log")
@ToString
public class AccountsDestroyLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -5711750131536790759L;

    private String account_uuid;
    private String mobile;
    private String mobile_cipher;
    private String mobile_digest;
    private String account_name;
    private String reason; // 注销原因
    private Long create_time;
    /**
     * 状态(0:失效; 1:生效)
     */
    private Integer status;
    private Long update_time;
    @Column(name = "wechat_id")
    private String wechatId;
    @Column(name = "apple_id")
    private String appleId;
    @Column(name = "qq_id")
    private String qqId;
    @Column(name = "visitor_id")
    private String visitorId;
    @Column(name = "weibo_id")
    private String weiboId;

    /**
     * 魅力值等级
     */
    @Column(name = "charm_rating")
    private Integer charmRating;
    /**
     * 需要再注册校验 0:不需要  1：需要
     */
    @Column(name = "need_to_limit_state")
    private Integer needToLimitState;
    
    @AllArgsConstructor
    @Getter
    public enum status{
        /**
         * 有效
         */
        VALID(1),
        /**
         * 无效
         */
        INVALID(0),
        ;

        private Integer value;
    }
}
