package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "accounts_gray_list")
@DynamicInsert
@DynamicUpdate
public class AccountsGrayList extends BaseEntity {

	@Column(name="account_uuid")
	private String accountUuid;
	private String token;
	@Column(name="account_name")
	private String accountName;// 用户昵称
	private String remark;// 备注
	@Column(name="operate_name")
	private String operateName;// 操作人
	@Column(name="create_time")
	private Long createTime;// 时间戳
	@Column(name="update_time")
	private Long updateTime;// 更新
	
}
