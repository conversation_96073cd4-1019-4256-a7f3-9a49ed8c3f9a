package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户手机app安装信息记录
 */
@Entity
@Table(name = "third_app_info_record")
@Data
public class ThirdAppInfoRecord extends BaseEntity {

    /**
     * 用户设备token
     */
    private String token;

    /**
     * 第三方应用信息
     */
    private String third_app_info;

    /**
     * 创建时间
     */
    private Long create_time;

    /**
     * 更新时间
     */
    private Long update_time;
}
