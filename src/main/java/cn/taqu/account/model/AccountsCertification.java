package cn.taqu.account.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * 用户认证表
 * Created by cqa on 2017/10/25.
 */
@Data
@Entity
@Table(name = "accounts_certification")
public class AccountsCertification implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 账号uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;
    /**
     * 真实姓名
     */
    @Column(name = "real_name")
    private String realName;
    /**
     * 真实姓名_密文
     */
    @Column(name = "real_name_cipher")
    private String realNameCipher;
    /**
     * 真实姓名_摘要密文
     */
    @Column(name = "real_name_digest")
    private String realNameDigest;
    /**
     * 身份证号
     */
    @Column(name = "identity_no")
    private String identityNo;
    /**
     * 身份证号_密文
     */
    @Column(name = "identity_no_cipher")
    private String identityNoCipher;
    /**
     * 身份证号_摘要密文
     */
    @Column(name = "identity_no_digest")
    private String identityNoDigest;
    /**
     * 身份证号_生日时间戳
     * 认证时直接填充，使用时直接获取
     */
    @Column(name = "identity_no_birth")
    private Long identityNoBirth;
    /**
     * 身份证件类型：1-身份证；2-外国人；3-台湾居民；4-港澳居民
     */
    @Column(name = "identity_type")
    private Integer identityType;
    /**
     * 芝麻认证业务编码
     */
    @Column(name = "biz_no")
    private String bizNo;
    /**
     * 用户提现账号
     */
    @Column(name = "reward_account")
    private String rewardAccount;
    /**
     * 提现账户是否完成认证
     */
    @Column(name = "reward_account_checked")
    private Integer rewardAccountChecked;
    /**
     * 支付宝账号可用状态  enum RewardAccountStatus
     */
    @Column(name = "reward_account_status")
    private Integer rewardAccountStatus;
    /**
     * 用户提现账号_密文
     */
    @Column(name = "reward_account_cipher")
    private String rewardAccountCipher;
    /**
     * 用户提现账号_摘要密文
     */
    @Column(name = "reward_account_digest")
    private String rewardAccountDigest;
    /**
     * 蚂蚁金服认证标识 1-是 0-否
     */
    @Column(name = "is_checked")
    private Integer isChecked;
    /**
     * 认证时图片
     */
    @Column(name = "certification_photo_url")
    private String certificationPhotoUrl;
    /**
     * 身份证绑定状态
     */
    @Column(name = "identity_no_status")
    private Integer identityNoStatus;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    
    public enum RewardAccountStatus {

        /**
         * 0 默认值 - 不可用
         */
        DEFAULT(0),
        /**
         * 1 绑定 - 可用
         */
        BIND(1),
        /**
         * 2 解绑 - 不可用
         */
        UNBIND(2),
        /**
         * 3 绑定失效 - 不可用
         */
        BIND_INVALID(3);

        private int value;

        RewardAccountStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

    }
}
