/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @author:<PERSON><PERSON><PERSON><PERSON>
 */
@Entity
@Table(name = "members")
@DynamicUpdate
public class Members extends BaseEntity implements Serializable {

	private static final long serialVersionUID = -3876655426126313823L;
	
	private String token;
	private Long create_time;
	
	public Members(){
		
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public Long getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}
	
}
