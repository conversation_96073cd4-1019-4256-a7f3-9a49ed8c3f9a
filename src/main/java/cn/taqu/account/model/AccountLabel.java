package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 账号标签
 */
@Data
@Entity
@Table(name = "account_label")
@DynamicInsert
@DynamicUpdate
public class AccountLabel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "account_uuid")
    private String accountUuid;

    @Column(name = "label_ids")
    private String labelIds;

    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;

}
