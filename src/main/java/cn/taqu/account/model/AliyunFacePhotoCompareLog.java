package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@Getter
@Setter
@Entity
@Table(name = "aliyun_face_photo_compare_log")
public class AliyunFacePhotoCompareLog extends BaseEntity {

    private String account_uuid;

    /**
     * 认证的人脸图片url
     */
    private String verify_photo_url;

    /**
     * 阿里云人脸活体认证的底图
     */
    private String base_photo_url;

    /**
     * 图片相似度
     */
    private Float similarity_score;

    /**
     * 认证状态
     */
    private Integer status;

    /**
     * accounts_photo的主键id
     */
    private Long accounts_photo_id;

    /**
     * 创建时间
     */
    private Long create_time;

    /**
     * 认证错误信息，认证成功该字段为空
     */
    private String error_msg;

    /**
     * 最后一次取消认证的时间
     */
    private Long cancel_update_time;

    /**
     * 更新时间
     */
   // private Long update_time;


    /**
     * 账号性别 -1 默认值 0-未知 1-男 2-女
     */
    private Integer account_gender;

    /**
     * 图片性别 -1 默认值 0-未知 1-男 2-女
     */
    private Integer base_photo_gender;

    /**
     * 性别比对结果 -1 默认值 0-性别不一致 1-性别一致
     */
    private Integer gender_compare_result;

    /**
     * 昵称
     */
    @Transient
    private String account_name;

    /**
     * 性别
     */
    @Transient
    private String sex_type;

    /**
     * 实名认证底图
     */
    @Transient
    private String certification_photo_url;
}
