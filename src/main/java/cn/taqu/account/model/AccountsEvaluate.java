package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户评价表
 */
@Entity
@Table(name = "accounts_evaluate")
@DynamicUpdate
@DynamicInsert
public class AccountsEvaluate extends BaseEntity implements Serializable{
    private String from_account_uuid;//评价人账号uuid
    private String to_account_uuid;//被评价人账号uuid
    private Integer type;//类型 1:喜欢; 2:不喜欢
    private String label_ids;//评价的标签id，多个标签id用英语逗号分隔
    private Long create_time;//创建时间
    private Long update_time;//修改时间

    public String getFrom_account_uuid() {
        return from_account_uuid;
    }

    public void setFrom_account_uuid(String from_account_uuid) {
        this.from_account_uuid = from_account_uuid;
    }

    public String getTo_account_uuid() {
        return to_account_uuid;
    }

    public void setTo_account_uuid(String to_account_uuid) {
        this.to_account_uuid = to_account_uuid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getLabel_ids() {
        return label_ids;
    }

    public void setLabel_ids(String label_ids) {
        this.label_ids = label_ids;
    }

    public Long getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Long create_time) {
        this.create_time = create_time;
    }

    public Long getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Long update_time) {
        this.update_time = update_time;
    }
}
