package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 人脸比对日志
 *
 * <AUTHOR>
 * @date 2025/7/12 11:24
 */
@EqualsAndHashCode(callSuper = true)
@Table(name = "face_compare_log")
@Entity
@Data
public class FaceCompareLog extends BaseEntity {

    /**
     * 用户UUID
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 识别方法
     */
    private String method;

    /**
     * 时间日期(格式：yyyyMMdd)
     */
    private Integer dt;

    /**
     * 认证类型
     */
    @Column(name = "cert_type")
    private String certType;

    /**
     * 采集图url
     */
    @Column(name = "verify_url")
    private String verifyUrl;

    /**
     * 基础对比图url
     */
    @Column(name = "base_url")
    private String baseUrl;

    /**
     * 得分情况
     */
    private Float score;

    /**
     * 是否相似，0-否，1-是
     */
    @Column(name = "is_similar")
    private Integer isSimilar;

    /**
     * 失败原因
     */
    @Column(name = "error_msg")
    private String errorMsg;

    /**
     * 对比方法
     */
    @Column(name = "compare_method")
    private String compareMethod;

    /**
     * 比对分值
     */
    @Column(name = "compare_score")
    private Float compareScore;

    /**
     * 比对是否相似，0-否，1-是
     */
    @Column(name = "compare_is_similar")
    private Integer compareIsSimilar;

    /**
     * 比对失败原因
     */
    @Column(name = "compare_error_msg")
    private String compareErrorMsg;

    /**
     * 创建时间戳
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间戳
     */
    @Column(name = "update_time")
    private Long updateTime;

    @Column(name = "request_id")
    private String requestId;

    @Column(name = "compare_request_Id")
    private String compareRequestId;

}
