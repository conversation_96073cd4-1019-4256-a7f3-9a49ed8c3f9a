package cn.taqu.account.model;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/**
 * 用户成就表
 */
@Entity
@Table(name = "accounts_achievement")
@DynamicInsert
@DynamicUpdate
public class AccountsAchievement {
    /**
     * 经验成就id，该成就比较特殊
     * <ol>
     *     <li>经验勋章固定佩戴，且排在所有已佩戴勋章的第一位</li>
     *     <li>不占用勋章佩戴位置，即除了经验勋章，其他勋章最多仍可以佩戴三个</li>
     * </ol>
     */
    public static final long EXPER_ACHIEVEMENT_ID = 11L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String account_uuid;
    private Long achievement_id;
    private Integer achievement_level;
    private Integer is_adorn;
    private Long create_time;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccount_uuid() {
        return account_uuid;
    }

    public void setAccount_uuid(String account_uuid) {
        this.account_uuid = account_uuid;
    }

    public Long getAchievement_id() {
        return achievement_id;
    }

    public void setAchievement_id(Long achievement_id) {
        this.achievement_id = achievement_id;
    }

    public Integer getAchievement_level() {
        return achievement_level;
    }

    public void setAchievement_level(Integer achievement_level) {
        this.achievement_level = achievement_level;
    }

    public Integer getIs_adorn() {
        return is_adorn;
    }

    public void setIs_adorn(Integer is_adorn) {
        this.is_adorn = is_adorn;
    }

    public Long getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Long create_time) {
        this.create_time = create_time;
    }
}
