package cn.taqu.account.model;


import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Table(name = "unbind_whitelist")
@Entity
@DynamicInsert
@DynamicUpdate
@Getter
@Setter
public class UnbindWhitelist extends BaseEntity implements Serializable {


    @Column(name = "account_uuid")
    private String accountUuid;

    @Column(name = "old_uuid")
    private String oldUuid;

    @Column(name = "identity_no")
    private String identityNo;
    /**
     * 身份证号_密文
     */
    @Column(name = "identity_no_cipher")
    private String identityNoCipher;
    /**
     * 身份证号_摘要密文
     */
    @Column(name = "identity_no_digest")
    private String identityNoDigest;

    /**
     * 即cloned值
     */
    @Column(name = "channel")
    private Integer channel;

    private String channelName;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "old_name")
    private String oldName;

    /**
     * 风控状态 0：允许解绑，1：不允许解绑
     */
    @Column(name = "risk_status")
    private Integer riskStatus;


    private String riskStatusStr;

    /**
     * 状态 0：成功，1：解绑成功，-1：失败
     */
    @Column(name = "status")
    private Integer status;

    private String statusStr;

    @Column(name = "link")
    private String link;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;


    private Long operateTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    /**
     * 操作人
     */
    @Column(name = "operate_name")
    private String operateName;

    @Column(name = "is_delete")
    private Integer isDelete;

    public String getChannelName() {

        return channelName;
    }

    public String getRiskStatusStr() {
        if(riskStatus==null){
            return "";
        }
        if(riskStatus==0){
            return "允许解绑";
        }
        if(riskStatus==-1){
            return "不允许解绑";
        }
        return "";
    }

    public String getStatusStr() {
        if(status==null){
            return "";
        }
        if(status==0){
            return "成功";
        }
        if(status==1){
            return "解绑成功";
        }
        if(status==-1){
            return "失败";
        }
        return "";
    }

}
