package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户注销恢复记录表
 * <AUTHOR>
 * @date 2021/04/13
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_destroy_renew_log")
public class AccountsDestroyRenewLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -5711750131536790759L;

    @Column(name = "account_uuid")
    private String accountUuid;
    @Column(name = "create_time")
    private Long createTime;
    @Column(name = "update_time")
    private Long updateTime;
    @Column(name = "operate_name")
    private String operateName;
    
}
