package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户标签
 * <AUTHOR>
 * 2017年12月12日 上午9:28:54
 */
@Entity
@Table(name = "accounts_label")
public class AccountsLabel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1832397260969265012L;

    private String account_uuid;//用户uuid
    private String like_type_label;//喜欢类型标签（标签id，逗号分隔）
    private String personality_label;//喜欢类型标签（标签id，逗号分隔）
    private String friendship_prefer_label; // 交友偏好（标签id，逗号分隔）

    public String getAccount_uuid() {
        return account_uuid;
    }

    public void setAccount_uuid(String account_uuid) {
        this.account_uuid = account_uuid;
    }

    public String getLike_type_label() {
        return like_type_label;
    }

    public void setLike_type_label(String like_type_label) {
        this.like_type_label = like_type_label;
    }

    public String getPersonality_label() {
        return personality_label;
    }

    public void setPersonality_label(String personality_label) {
        this.personality_label = personality_label;
    }

    public String getFriendship_prefer_label() {
        return friendship_prefer_label;
    }

    public void setFriendship_prefer_label(String friendship_prefer_label) {
        this.friendship_prefer_label = friendship_prefer_label;
    }
}
