package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * @Author: zy
 * @Date: 2020/6/22 16:28
 */
@Entity
@Table(name = "random_nickname")
@Data
public class RandomNickname extends BaseEntity implements Serializable {

    @Column(name = "random_nickname")
    private String randomNickname;
    @Column(name = "sex_type")
    private Integer sexType;  //1男  2女
    @Column(name = "status")
    private Integer status;
    @Column(name = "group_id")
    private Long groupId;
    @Column(name = "cloned")
    private Long cloned;
    @Column(name = "create_time")
    private Long createTime;
    @Column(name = "update_time")
    private Long updateTime;


    @Transient
    private String randomGroupName;
    @Transient
    private String partsOfSpeech;
    @Transient
    private Integer countNum;
}
