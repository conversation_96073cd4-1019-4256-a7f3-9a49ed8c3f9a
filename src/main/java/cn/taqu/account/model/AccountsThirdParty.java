/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.model;
import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
/**
 * 第三方账号实体
 * @author:la<PERSON><PERSON><PERSON>
 */
@Entity
@Table(name = "accounts_third_party")
public class AccountsThirdParty extends BaseEntity{
	/**
	 * @Fields serialVersionUID :
	 */
	private static final long serialVersionUID = 7509792380453413141L;

	//状态
	/**
	 * 绑定
	 */
	public final static Integer STATUS_BINDING = 1;

	/**
	 * 解绑
	 */
	public final static Integer STATUS_UNBINDING = 2;

	//登录类型
	/**
	 * 微信
	 */
	public final static String TYPE_WECHAT = "WeChat";

	/**
	 * 微信提现用
	 */
	public final static String TYPE_WECHAT_MONEY = "WeChatMoney";
	/**
	 * QQ
	 */
	public final static String TYPE_QQ = "QQ";

	/**
	 * 微博
	 */
	@Deprecated
	public final static String TYPE_WEIBO = "WeiBo";

	/**
	 * 微信公众号
	 */
	public final static String TYPE_WECHAT_OFFICIAL = "WeChat_Official";

	/**
	 * 苹果登录
	 */
	public final static String TYPE_APPLE = "Apple";

	/**
	 * 游客登陆
	 */
	@Deprecated
	public final static String VISITOR = "Visitor";


	private Long account_id;//账号ID
	private String account_uuid;//账户uuid

	private String type;//登录类型

	// 2023.08.03 相关查询改为查用户中台
	private String open_id;//第三方用户ID

	// 2023.08.03 相关查询改为查用户中台
	private String union_id;//微信用户唯一ID

	private Integer status;//状态:1绑定,2解绑

	private Long create_time;//创建时间

	private Long update_time;//更新时间

	public AccountsThirdParty(){

	}
	public Long getAccount_id() {
		return account_id;
	}
	public void setAccount_id(Long account_id) {
		this.account_id = account_id;
	}
	public String getAccount_uuid() {
		return account_uuid;
	}
	public void setAccount_uuid(String account_uuid) {
		this.account_uuid = account_uuid;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getOpen_id() {
		return open_id;
	}
	public void setOpen_id(String open_id) {
		this.open_id = open_id;
	}
	public String getUnion_id() {
		return union_id;
	}
	public void setUnion_id(String union_id) {
		this.union_id = union_id;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Long getCreate_time() {
		return create_time;
	}
	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}
	public Long getUpdate_time() {
		return update_time;
	}
	public void setUpdate_time(Long update_time) {
		this.update_time = update_time;
	}

}
