package cn.taqu.account.model;

import cn.taqu.account.common.AliyunLiveFaceDetectStatus;
import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

/**
 * 用户个人相册（第一张为头像）
 *
 * <AUTHOR>
 * 2017年4月18日 上午11:24:06
 */
@Entity
@Table(name = "accounts_photo")
@Getter
@Setter
public class AccountsPhoto extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1832397260969265012L;

    private String account_uuid;//用户uuid
    private String photo_url;//图片地址
    private String bucket;//图片所属空间
    private Integer seq;//排序字段
    private Integer status = Status.NO_CHECK.value;//头像抽查审核状态
    private Integer verify_status = 0; //阿里云人脸比对认证，0-未认证，1-已认证
    private String error_msg;  //阿里云认证失败的原因
    private Integer face_score; //颜值分
    private Long create_time;//创建时间
    private Long update_time;//更新时间
    /**
     * 数美审核描述
     */
    private String risk_description;

    /**
     * 数美图片审核
     */
    private String risk_photo_url;

    /**
     * 点赞数
     */
    @Deprecated
    private Long like_num;


    public static AccountsPhoto.Status accountsLifeStatusToAccountsPhotoStatus(AccountsLife.Status status, Integer verifyStatus) {
        if (status == null) {
            return Status.NO_CHECK;
        }
        // 线上看数据，目前只看到1，2，6，8，其他兜底处理
        switch (status) {
            case NONE:
            case AUDIT_FAIL:
                return Status.NO_CHECK;

            case PHOTO_AUTO_CHECK:
                return Status.ONLY_AUTO_CHECK;

            case AUDITING:
                return Status.ACCOUNTS_LIFE_AUTO_CHECK;

            case AUDIT_SUCCESS:
                // 真人 && 人审通过才算通过状态，2025.4.1
                if (Objects.equals(AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus(), verifyStatus)) {
                    return Status.PASS;
                }
                return Status.ACCOUNTS_LIFE_AUTO_CHECK;

            default:
                return Status.NO_CHECK;
        }


    }

    public boolean verifyPhotoCheckingStatus() {
        return status == Status.NO_CHECK.getValue() ||
                status == Status.RE_CHECK.getValue() ||
                status == Status.AUTO_CHECK.getValue() ||
                status == Status.NO_CHECK_ACCOUNT.getValue() ||
                status == Status.ONLY_AUTO_CHECK.getValue() ||
                status == Status.ACCOUNTS_LIFE_AUTO_CHECK.getValue()
                ;
    }

    public enum Status {

        /**
         * -1已处罚
         */
        ILLEGAL(-1),
        /**
         * 1-未处理
         */
        NO_CHECK(1),
        /**
         * 2已忽略
         */
        PASS(2),
        /**
         * 3默认头像
         */
        DEFAULT_PIC(3),
        /**
         * 4待复审(已审核过一次，可以展示了，但是头像抽查时仍然会出现)
         */
        RE_CHECK(4),
        /**
         * 5.等待机审
         */
        AUTO_CHECK(5),
        /**
         * 6.用户操作后的待审核状态，2020.02.05临时处理
         */
        NO_CHECK_ACCOUNT(6),
        /**
         * 7.90天重新活跃，复审
         */
        REVIEW_90(7),
        /**
         * 8.只过机审
         */
        ONLY_AUTO_CHECK(8),
        /**
         * 9.我的生活机审通过
         */
        ACCOUNTS_LIFE_AUTO_CHECK(9);


        private int value;

        Status(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static Status of(Integer value) {
            if(value == null) {
                return null;
            }
            Status[] enums = Status.values();
            for (Status e : enums) {
                if (e.getValue()==value) {
                    return e;
                }
            }
            return null;
        }

    }

}
