package cn.taqu.account.model;


import cn.taqu.core.orm.base.BaseEntity;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户sn，每个用户的sn都是不一样的，做为给第三方用的用户唯一id，此表不能使用分表，因为该表的id将做为推荐算法偏移值，分表的话id会重复，造成偏移值也重复
 */
@Entity
@Table(name = "account_sn_list")
@DynamicInsert
@DynamicUpdate
public class AccountSnList extends BaseEntity {
    @Column(name = "account_uuid")
    private String accountUuid;
    @Column(name = "account_sn")
    private String accountSn;

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public String getAccountSn() {
        return StringUtils.trimToEmpty(this.accountSn);
    }

    public void setAccountSn(String accountSn) {
        this.accountSn = StringUtils.trimToEmpty(accountSn);
    }

}
