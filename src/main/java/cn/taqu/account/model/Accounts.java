package cn.taqu.account.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 会员实体，对应数据库中的会员表accounts
 *
 * <AUTHOR>
 * @ClassName Accounts.java
 * @date 2015年9月14日 下午5:42:34
 */
@Getter
@Setter
@Entity
@Table(name = "accounts")
@DynamicInsert
@DynamicUpdate
public class Accounts implements Serializable {
    private static final long serialVersionUID = -297534381342117352L;

    public static List<String> fields = Arrays.asList("account_id", "uuid", "account_ticket", "account_key", "account_name", "account_password", "account_type", "sex_type", "member_id", "email",
            "forum_status", "cs_id", "account_status", "create_time", "last_logintime", "appcode", "cloned", "account_coin", "min_sys_mess_id", "max_sys_mess_id", "avatar", "avatar_status", "avatar_time",
            "is_logged_in", "mobile", "mobile_cipher","mobile_digest", "mobile_place", "reg_style", "channel", "platform_id");

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long account_id;
    private String uuid;
    @Deprecated
    private String account_ticket;//登录令牌
    private String account_key;//PC端登录凭证
    private String account_name;//昵称
    private String account_password;//登录密码
    private Integer account_type;//类型，1:注册用户; 2:匿名用户
    private Integer sex_type;//性别，1:男; 2:女; null:未知
    private Long member_id;//对应设备号
    @Deprecated
    private String email;//邮箱
    private Integer forum_status = 1;//头像抽查审核状态 1未处理 -1已处罚 2已忽略 3默认头像
    private Long cs_id;//头像修改的秒时间戳
    private Integer account_status = 1;// 账号状态 0:注销; 1:正常状态
    private Long create_time;//创建时间
    private Long last_logintime;//最后一次登录时间
    private Integer appcode;//注册时的app应用码 1:他趣;
    private Integer cloned;//分身版
    private Integer account_coin;//用户金币值
    @Deprecated
    private Integer min_sys_mess_id;//用户可接收的最小私信值 2021.3.3业务已下线
    @Deprecated
    private Integer max_sys_mess_id;//用户可接收的最大私信值 2021.3.3业务已下线
    private String avatar;//用户当前头像
    private Integer avatar_status;//头像状态 0:未审核; 1:已审核  2020.10.15 数据库应该没用到这个字段
    private Long avatar_time;//更新头像时间
    private Integer is_logged_in;//用户退出,0:退出; 1:登录
    // 2023.08.03 相关查询改为查用户中台
    private String mobile;//手机号码
    // 2023.08.03 相关查询改为查用户中台
    private String mobile_cipher; //手机号加密（可解密，每次加密结果不一样）
    // 2023.08.03 相关查询改为查用户中台
    private String mobile_digest; //手机号摘要加密（不可解密，每次加密结果一样，用于查询）
    @Deprecated
    private String mobile_place;//手机号码归属地
    private String reg_style;//注册方式(mobile，qq，weibo，wechat等)
    private String channel;//注册时的渠道
    private Integer platform_id;//注册时的平台
    
}