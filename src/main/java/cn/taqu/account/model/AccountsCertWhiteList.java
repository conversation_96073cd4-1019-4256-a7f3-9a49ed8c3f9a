package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Objects;

/**
 * 历史业务，线上没使用
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-15 15:52
 */
@Deprecated
@Table(name = "accounts_cert_white_list")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsCertWhiteList extends BaseEntity {


    /**
     * 账号uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 来源 0-默认 1-直播 2-聊天室 3-趣聊
     */
    @Column(name = "white_list_from")
    private Integer whiteListFrom;

    /**
     * 操作人
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    @Transient
    private String accountName;
    @Transient
    private String avatar;
    @Transient
    private Integer sexType;

    /**
     * 白名单来源枚举
     */
    public enum WhiteListFromEnum{
        // 直播
        ZHIBO(1),
        // 聊天室
        LIAOTIANSHI(2),
        // 恰聊
        ZHIBO_LIAOTIANSHI(3),
        ;

        private Integer value;

        WhiteListFromEnum(Integer value){
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        /**
         * 是否包含value
         * @param value
         * @return
         */
        public static Boolean isContains(Integer value){
            for (WhiteListFromEnum fromEnum : WhiteListFromEnum.values()) {
                if(Objects.equals(fromEnum.getValue(), value)){
                    return true;
                }
            }
            return false;
        }
    }
}
