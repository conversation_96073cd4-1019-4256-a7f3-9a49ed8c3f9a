package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * token新旧关联关系
 * 
 * <AUTHOR>
 * @date 2020/08/19
 */
@Entity
@Getter
@Setter
@Table(name = "token_new_old_relation")	
public class TokenNewOldRelation extends BaseEntity implements Serializable {
	
	private static final long serialVersionUID = -4138500538584166654L;

    @Column(name = "new_token")
	private String newToken;
	
    @Column(name = "old_token")
    private String oldToken;

    @Column(name = "create_time")
	private Long createTime;
	
    @Column(name = "update_time")
	private Long updateTime;

}
