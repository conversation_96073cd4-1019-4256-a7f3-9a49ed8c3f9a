package cn.taqu.account.model;

import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "chat_real_certification_log")
public class ChatRealCertificationLog extends BaseEntity {

    private String account_uuid;

    /**
     * 业务级实名认证的底图
     */
    private String certification_photo_url;

    /**
     * 应用真人认证活体的底图
     */
    private String face_photo_url;

    /**
     * 图片相似度
     */
    private Float similarity_score;

    /**
     * 认证状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long create_time;

    /**
     * 认证错误信息，认证成功该字段为空
     */
    private String error_msg;
    
    /**
     * 来源 1-聊天室；2-趣币兑换
     */
    private Integer source;

}
