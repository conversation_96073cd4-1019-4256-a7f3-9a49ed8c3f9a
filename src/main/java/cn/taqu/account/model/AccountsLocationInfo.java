package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户位置信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-10-12 15:06
 */
@Table(name = "accounts_location_info")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsLocationInfo extends BaseEntity {

    /**
     * 用户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 最后一次ip地址
     */
    private String ip;

    /**
     * 经度
     */
    private String longitude;
    /**
     * 经度加密
     */
    @Column(name = "longitude_cipher")
    private String longitudeCipher;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 纬度加密
     */
    @Column(name = "latitude_cipher")
    private String latitudeCipher;

    /**
     * 城市id
     */
    @Column(name = "city_id")
    private Long cityId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    @Column(name = "data_sources")
    private Integer dataSources;

    @Column(name = "use_provincial_capital")
    private Integer useProvincialCapital;

}
