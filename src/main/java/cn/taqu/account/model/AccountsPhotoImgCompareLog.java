package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-13 18:10
 */
@Table(name = "accounts_photo_img_compare_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsPhotoImgCompareLog extends BaseEntity {

    /**
     * uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 图片地址
     */
    @Column(name = "base_photo_url")
    private String basePhotoUrl;
    /**
     * 图片地址
     */
    @Column(name = "verify_photo_url")
    private String verifyPhotoUrl;

    /**
     *
     */
    @Column(name = "face_model_version")
    private String faceModelVersion;

    /**
     * 相似分
     */
    @Column(name = "score")
    private Float socre;

    /**
     * 响应结果
     */
    @Column(name = "response_str")
    private String responseStr;

    /**
     * 第三方订单号
     */
    @Column(name = "third_order_no")
    private String thirdOrderNo;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

}
