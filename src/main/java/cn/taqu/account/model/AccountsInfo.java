/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.model;

import cn.taqu.account.service.ToolsService;
import cn.taqu.core.orm.base.BaseEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 账号信息实体对象
 * @author:<PERSON><PERSON><PERSON><PERSON>
 */
@Entity
@Table(name = "accounts_info")
@DynamicUpdate
@DynamicInsert
public class AccountsInfo extends BaseEntity {

	private static final long serialVersionUID = 3847000165956422161L;

	@Deprecated
	private Long account_id;//账户id

	private String account_uuid;//账户uuid

	@Deprecated
	private Integer age = 0;//年龄,由birth计算出

	private Long birth;//出生年月日,例如:1988-06-29的时间戳

	private String constellation;//星座,例如:巨蟹座、处女座

	private String baseaddr;//所在地 省市代码,例如:222,333

	private String hometown;//家乡 省市代码,例如:222,333

	private Integer education = 0;//受教育程度,例如:[1:小学,2:初中,3:高中,4:大专,5:本科,6:研究生](预留)

	private Integer income = 0;//年收入，0：未填写，1：5万以下，2：5～10万，3：10～20万，4：20～30万， 5：30～50万，6：50～100万，7：100万以上

	private Integer job = 0;//职业或者从事的行业,例如:[0:未知,1:公务员,2:学生,3:白领,4:个人户,5:其他](预留)

	@Deprecated
	private Integer sexual = 0;//性取向 [0:未知,1:爱好男,2:爱好女,3:双性恋,4:无性恋]

	private Integer dating_intention = 1;//交友意向 [(1:随缘、2:求撩、3:勿扰)]

	@Deprecated
	private Long tqcoin = 0L;//趣币数量

	private Integer affectivestatus = 0;//婚恋情况[0:未知,1:单身,2:恋爱中,3:已婚,4:离异/丧偶]

	private String personal_profile;//个人简介

	private Integer personal_profile_status; // 0-未填写，1-审核中，2-审核通过。3-审核拒绝

	private Long create_time;//创建时间

	@Deprecated
	private Integer update_sex_number;//修改性别的次数

	private Long update_time;//最后一次更新时间

	@Deprecated
	private Integer is_check_mobile;//是否绑定手机[1:是，0:否]

	@Deprecated
	private Integer sex_type_is_secret = 0;//性别是否保密(0:不保密;1:保密)

	@Deprecated
	private Integer age_is_secret = 0;//年龄是否保密(0:不保密;1:保密)

	@Deprecated
	private Integer affectivestatus_is_secret = 0;//婚恋状况是否保密(0:不保密;1:保密)

	@Deprecated
	private Integer baseaddr_is_secret = 0;//地址是否保密(0:不保密;1:保密)

	@Deprecated
	private Integer hometown_is_secret = 0;//家乡是否保密(0:不保密;1:保密)

	@Deprecated
	private Integer sexual_is_secret = 0;//性取向是否保密(0:不保密;1:保密)

	private Integer personal_profile_be_allow = 1;//是否允许展示个人简介(1:允许展示;0:不允许展示)。app、后台手动设置，注意与等级/头衔权限中的是否展示个人权限设置区分开

	private Integer gender_certification = 0;//性别认证[0:未认证,1:女性认证]

	private Integer voice_certification = 0;//语音认证[0:未认证,1:认证]

	private Integer zhima_certification = 0;//芝麻认证[0:未认证,1:认证]

	@Deprecated
	private Integer aliyun_final_decision = 0;//风险等级。0：无风险；1：高风险；2：中风险(阿里返回)

	@Deprecated
	private String sm_type;//SM类型，值为S或者M

	private String education_level; //学历，直接保存客户端传过来的学历数据，由于education之前已经存在，所以用该字段。对应缓存中的education

	private String trade; //行业

	private String profession; //职业

	private Integer height; //身高

	private Integer weight; //体重

	private Integer real_photo_certification; //是否包含真人照片，0-不包含，1-包含

	private Integer photo_number; // 照片数

	private Integer chat_real_certification; // 业务级实名认证，0-未认证，1-认证

	private Integer person_face_avatar; // 人脸头像，0-未认证，1-认证

	/**
	 * 交友目的
	 */
	@Deprecated
	private String make_friend_objective;

	/**
	 * 交友目的id
	 */
	@Deprecated
	private Integer make_friend_objective_id;

	@Deprecated
	private Integer change_age_status; // 年龄是否允许修改 1允许/2不允许

	// 字段可能被复用
	private Integer real_person_certification; // 真人认证状态，0-未认证，1-认证     // -1-未认证，1-认证成功，2-认证失败

	//初始注册头像是否违规，1 违规 0 没违规
	private Integer register_avatar_status;
	
	// 我的生活排序 逗号分割
	private String accounts_life_seq;

	/**
	 * 绑定手机
	 */
	public final static Integer CHECK_MOBILE_BIND = 1;

	public final static Integer CHECK_MOBILE_UNBIND = 0;

	public AccountsInfo(){
	}

	public Long getAccount_id() {
		return account_id;
	}

	public void setAccount_id(Long account_id) {
		this.account_id = account_id;
	}

	public String getAccount_uuid() {
		return account_uuid;
	}

	public void setAccount_uuid(String account_uuid) {
		this.account_uuid = account_uuid;
	}

	public Integer getAge() {
		return this.birth == null ? 0 : ToolsService.calAgeFromBirth(this.birth);
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public Long getBirth() {
		return birth;
	}

	public void setBirth(Long birth) {
		this.birth = birth;
	}

	public String getConstellation() {
		return constellation;
	}

	public void setConstellation(String constellation) {
		this.constellation = constellation;
	}

	public String getBaseaddr() {
		return baseaddr;
	}

	public void setBaseaddr(String baseaddr) {
		this.baseaddr = baseaddr;
	}

	public Integer getEducation() {
		return education;
	}

	public void setEducation(Integer education) {
		this.education = education;
	}

	public Integer getIncome() {
		return income;
	}

	public void setIncome(Integer income) {
		this.income = income;
	}

	public Integer getJob() {
		return job;
	}

	public void setJob(Integer job) {
		this.job = job;
	}

	public Integer getSexual() {
		return sexual;
	}

	public void setSexual(Integer sexual) {
		this.sexual = sexual;
	}

	public Long getTqcoin() {
		return tqcoin;
	}

	public void setTqcoin(Long tqcoin) {
		this.tqcoin = tqcoin;
	}

	public Integer getAffectivestatus() {
		return affectivestatus;
	}

	public void setAffectivestatus(Integer affectivestatus) {
		this.affectivestatus = affectivestatus;
	}

	public Long getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}

	public Integer getUpdate_sex_number() {
		return update_sex_number;
	}

	public void setUpdate_sex_number(Integer update_sex_number) {
		this.update_sex_number = update_sex_number;
	}

	public Long getUpdate_time() {
		return update_time;
	}

	public void setUpdate_time(Long update_time) {
		this.update_time = update_time;
	}

	public Integer getIs_check_mobile() {
		return is_check_mobile;
	}

	public void setIs_check_mobile(Integer is_check_mobile) {
		this.is_check_mobile = is_check_mobile;
	}

	public Integer getSex_type_is_secret() {
		return sex_type_is_secret;
	}

	public void setSex_type_is_secret(Integer sex_type_is_secret) {
		this.sex_type_is_secret = sex_type_is_secret;
	}

	public Integer getAge_is_secret() {
		return age_is_secret;
	}

	public void setAge_is_secret(Integer age_is_secret) {
		this.age_is_secret = age_is_secret;
	}

	public Integer getAffectivestatus_is_secret() {
		return affectivestatus_is_secret;
	}

	public void setAffectivestatus_is_secret(Integer affectivestatus_is_secret) {
		this.affectivestatus_is_secret = affectivestatus_is_secret;
	}

	public Integer getBaseaddr_is_secret() {
		return baseaddr_is_secret;
	}

	public void setBaseaddr_is_secret(Integer baseaddr_is_secret) {
		this.baseaddr_is_secret = baseaddr_is_secret;
	}

	public Integer getSexual_is_secret() {
		return sexual_is_secret;
	}

	public void setSexual_is_secret(Integer sexual_is_secret) {
		this.sexual_is_secret = sexual_is_secret;
	}

	public String getHometown() {
		return hometown;
	}

	public void setHometown(String hometown) {
		this.hometown = hometown;
	}

	public Integer getDating_intention() {
		return dating_intention;
	}

	public void setDating_intention(Integer dating_intention) {
		this.dating_intention = dating_intention;
	}

	public String getPersonal_profile() {
		return personal_profile;
	}

	public void setPersonal_profile(String personal_profile) {
		this.personal_profile = personal_profile;
	}

	public Integer getPersonal_profile_status() {
		return personal_profile_status;
	}

	public void setPersonal_profile_status(Integer personal_profile_status) {
		this.personal_profile_status = personal_profile_status;
	}

	public Integer getHometown_is_secret() {
		return hometown_is_secret;
	}

	public void setHometown_is_secret(Integer hometown_is_secret) {
		this.hometown_is_secret = hometown_is_secret;
	}

	public Integer getPersonal_profile_be_allow() {
		return personal_profile_be_allow;
	}

	public void setPersonal_profile_be_allow(Integer personal_profile_be_allow) {
		this.personal_profile_be_allow = personal_profile_be_allow;
	}

	public Integer getGender_certification() {
		return gender_certification;
	}

	public void setGender_certification(Integer gender_certification) {
		this.gender_certification = gender_certification;
	}

	public Integer getVoice_certification() {
		return voice_certification;
	}

	public void setVoice_certification(Integer voice_certification) {
		this.voice_certification = voice_certification;
	}

	public Integer getAliyun_final_decision() {
		return aliyun_final_decision;
	}

	public void setAliyun_final_decision(Integer aliyun_final_decision) {
		this.aliyun_final_decision = aliyun_final_decision;
	}

	public Integer getZhima_certification() {
		return zhima_certification;
	}

	public String getSm_type() {
		return sm_type;
	}

	public void setSm_type(String sm_type) {
		this.sm_type = sm_type;
	}

	public void setZhima_certification(Integer zhima_certification) {
		this.zhima_certification = zhima_certification;
	}


	public String getEducation_level() {
		return education_level;
	}

	public void setEducation_level(String education_level) {
		this.education_level = education_level;
	}

	public String getTrade() {
		return trade;
	}

	public void setTrade(String trade) {
		this.trade = trade;
	}

	public String getProfession() {
		return profession;
	}

	public void setProfession(String profession) {
		this.profession = profession;
	}

	public Integer getHeight() {
		return height;
	}

	public void setHeight(Integer height) {
		this.height = height;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public Integer getReal_photo_certification() {
		return real_photo_certification;
	}

	public void setReal_photo_certification(Integer real_photo_certification) {
		this.real_photo_certification = real_photo_certification;
	}

	public Integer getPhoto_number() {
		return photo_number;
	}

	public void setPhoto_number(Integer photo_number) {
		this.photo_number = photo_number;
	}

	public Integer getChat_real_certification() {
		return chat_real_certification;
	}

	public void setChat_real_certification(Integer chat_real_certification) {
		this.chat_real_certification = chat_real_certification;
	}

	public Integer getPerson_face_avatar() {
		return person_face_avatar;
	}

	public void setPerson_face_avatar(Integer person_face_avatar) {
		this.person_face_avatar = person_face_avatar;
	}

	public String getMake_friend_objective() {
		return make_friend_objective;
	}

	public void setMake_friend_objective(String make_friend_objective) {
		this.make_friend_objective = make_friend_objective;
	}

	public Integer getMake_friend_objective_id() {
		return make_friend_objective_id;
	}

	public void setMake_friend_objective_id(Integer make_friend_objective_id) {
		this.make_friend_objective_id = make_friend_objective_id;
	}

	public Integer getChange_age_status() {
		return change_age_status;
	}

	public void setChange_age_status(Integer change_age_status) {
		this.change_age_status = change_age_status;
	}


	public Integer getReal_person_certification() {
		return real_person_certification;
	}

	public void setReal_person_certification(Integer real_person_certification) {
		this.real_person_certification = real_person_certification;
	}

	public Integer getRegister_avatar_status() {
		return register_avatar_status;
	}

	public void setRegister_avatar_status(Integer register_avatar_status) {
		this.register_avatar_status = register_avatar_status;
	}

    public String getAccounts_life_seq() {
        return accounts_life_seq;
    }

    public void setAccounts_life_seq(String accounts_life_seq) {
        this.accounts_life_seq = accounts_life_seq;
    }
	
}

