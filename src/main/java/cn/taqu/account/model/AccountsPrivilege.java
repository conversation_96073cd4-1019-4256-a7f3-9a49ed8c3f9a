package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/27.
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_privilege")
@DynamicUpdate
public class AccountsPrivilege extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1406476389233303436L;

    private String privilege_config;
    private Long create_time;
    private Long update_time;
}
