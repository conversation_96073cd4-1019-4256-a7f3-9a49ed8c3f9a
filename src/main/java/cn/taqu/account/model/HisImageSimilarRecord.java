package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 历史重复照片记录表
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "his_image_similar_record")
public class HisImageSimilarRecord extends BaseEntity implements Serializable {

    public static final Integer INITIAL = 0;

    public static final Integer DONE = 1;

    // 业务类型常量
    public static final Integer BIZ_TYPE_ALBUM = 1;
    public static final Integer BIZ_TYPE_LIFE = 2;
    public static final Integer BIZ_TYPE_INTRODUCTION = 3;

    @JsonProperty("origin_id")
    @Column(name = "origin_id")
    private Long originId;

    /**
     * 用户uuid
     */
    @JsonProperty("account_uuid")
    @Column(name = "account_uuid")
    private String accountUuid;

    @JsonProperty("biz_id")
    @Column(name = "biz_id")
    private String bizId;
    
    /**
     * 1、相册 2、生活 3、自我介绍
     */
    @JsonProperty("biz_type")
    @Column(name = "biz_type")
    private Integer bizType;

    @JsonProperty("photo_url")
    @Column(name = "photo_url")
    private String photoUrl;
    
    /**
     * 相似图片地址
     */
    @Column(name = "seq")
    private Integer seq;
    
    /**
     * 0待处理 1已处理
     */
    @Column(name = "state")
    private Integer state;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    @Column(name = "create_time")
    private Long createTime;

}
