package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "accounts_ideal_target")
public class AccountsIdealTarget extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @Column(name = "account_uuid")
    private String accountUuid;


    @Column(name = "ideal_target_origin")
    private String idealTargetOrigin;

    @Column(name = "ideal_target")
    private String idealTarget;

    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;

}
