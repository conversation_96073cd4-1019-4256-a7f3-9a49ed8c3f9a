package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 再注册白名单实体类
 *
 * <AUTHOR>
 * @date 2021/07/21
 */
@Table(name = "reregister_whitelist")
@Entity
@DynamicInsert
@DynamicUpdate
@Getter
@Setter
public class ReRegisterWhitelist extends BaseEntity implements Serializable {

    /**
     * 注册类型: 1 - 手机号， 2 - 第三方
     */
    @Column(name = "register_type")
    private Integer registerType;

    /**
     * openid类型: 1 - QQ，2 - 微信，3 - 微博, 4 - 苹果
     */
    @Column(name = "openid_type")
    private Integer openidType;

    /**
     * openid
     */
    private String openid;

    /**
     * 手机号码
     */
    private String mobile;
    
    /**
     * 手机号加密
     */
    @Column(name = "mobile_cipher")
    private String mobileCipher; 
    /**
     * 手机号摘要加密
     */
    @Column(name = "mobile_digest")
    private String mobileDigest;
    
    /**
     * 公会名称
     */
    private String society;
    /**
     * 状态 0：过期  1：生效
     */
    private Integer status;
    /**
     * 原因
     */
    private String reason;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    /**
     * 操作人
     */
    @Column(name = "operate_name")
    private String operateName;
}
