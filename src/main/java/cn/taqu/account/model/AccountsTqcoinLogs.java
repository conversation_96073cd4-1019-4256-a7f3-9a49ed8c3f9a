package cn.taqu.account.model;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;

@Deprecated
@Entity
@Table( name = "accounts_tqcoin_logs")
public class AccountsTqcoinLogs extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 904315905573692416L;
	private Long account_id = 0L;//用户账号id, 默认设置为0
	private Integer tqcoin_num;//趣币变化额度(有正负值)
	private Integer type;//类型 1完善个人信息2帖子被加精3帖子被推荐4.兑换商品5.后台操作增减趣币
	private String info;//记录信息，关于趣币变化的文字描述信息
	private Long param;//额外参数信息(帖子ID)
	private Long create_time;//创建时间
	private Long operator;//操作人的账号ID
	private String account_uuid;//用户uuid(与account_id对应，不过此处存放的是uuid)
	private String content_uuid;//内容uuid(与param对应，不过此处存放的是uuid)
	private String transfer_account_uuid;//交易用户uuid
	
	public Long getAccount_id() {
		return account_id;
	}
	public void setAccount_id(Long account_id) {
		this.account_id = account_id;
	}
	public Integer getTqcoin_num() {
		return tqcoin_num;
	}
	public void setTqcoin_num(Integer tqcoin_num) {
		this.tqcoin_num = tqcoin_num;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getInfo() {
		return info;
	}
	public void setInfo(String info) {
		this.info = info;
	}
	public Long getParam() {
		return param;
	}
	public void setParam(Long param) {
		this.param = param;
	}
	public Long getCreate_time() {
		return create_time;
	}
	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}
	public Long getOperator() {
		return operator;
	}
	public void setOperator(Long operator) {
		this.operator = operator;
	}
	public String getAccount_uuid() {
		return account_uuid;
	}
	public void setAccount_uuid(String account_uuid) {
		this.account_uuid = account_uuid;
	}
	public String getContent_uuid() {
		return content_uuid;
	}
	public String getTransfer_account_uuid() {
		return transfer_account_uuid;
	}
	public void setTransfer_account_uuid(String transfer_account_uuid) {
		this.transfer_account_uuid = transfer_account_uuid;
	}
	public void setContent_uuid(String content_uuid) {
		this.content_uuid = content_uuid;
	}
}
