package cn.taqu.account.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;

/**
 * 游客账号表
 */
@Deprecated
@Getter
@Setter
@Entity
@Table(name = "accounts_guest")
public class AccountsGuest implements Serializable {
    public static Collection<String> fields = Arrays.asList("account_id","account_ticket","account_name","sex_type","member_id","create_time","appcode","uuid");

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long account_id;//主键id，自增
    private String uuid;//唯一标识
    private String account_ticket;//登录令牌
    private String account_name;//昵称
    private Integer sex_type;//性别，1:男; 2:女; 0:未知
    private Long member_id;//对应设备号
    private Long create_time;//创建时间
    private Integer appcode;//注册时的app应用码 1:他趣; 
}
