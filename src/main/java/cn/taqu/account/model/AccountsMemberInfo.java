package cn.taqu.account.model;
import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
/**
 * 账号设备信息表
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_member_info")
@DynamicInsert
@DynamicUpdate
public class AccountsMemberInfo extends BaseEntity {
    /**
     * 账号uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;
    /**
     * 设备token
     */
    private String token;
    /**
     * app版本
     */
    @Column(name = "app_version")
    private Long appVersion;
    /**
     * 应用码
     */
    private Integer appcode;
    /**
     * 分身版
     */
    private Integer cloned;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 平台id
     */
    @Column(name = "platform_id")
    private Integer platformId;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;
    /**
     * 最后打开时间(即最后一次上报设备的时间)
     */
    @Column(name = "last_open_time")
    private Long lastOpenTime;
    /**
     * 最后活跃时间(即最后使用app的时间)
     */
    @Column(name = "last_active_time")
    private Long lastActiveTime;
    /**
     * 最后更新时间
     */
    @Column(name = "update_time")
    private Long update_time;
}