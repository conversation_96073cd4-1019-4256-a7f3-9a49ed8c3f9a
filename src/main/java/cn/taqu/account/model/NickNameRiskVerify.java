package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by zhuangyi on 2019/7/23.
 */
@Data
@Entity
@Table(name = "nick_name_risk_verify")
public class NickNameRiskVerify extends BaseEntity {

    @Column(name = "account_uuid")
    private String accountUuid; //用户uuid
    @Column(name = "old_name")
    private String oldName; //修改旧昵称
    @Column(name = "new_name")
    private String newName; //更新昵称
    /**
     * 是否默认昵称
     */
    @Column(name = "default_nick_name")
    private Integer defaultNickName;
    /**
     * 旧昵称默认
     */
    @Column(name = "old_default_nick_name")
    private Integer oldDefaultNickName;
    @Column(name = "update_time")
    private Long updateTime; //更新时间
    @Column(name = "type")
    /**
     * 操作类型
     */
    private Integer type;
    @Column(name = "status")
    private Integer status; //当前状态
    @Column(name = "operator")
    private String operator; //操作人
    @Column(name = "suggestion")
    private String suggestion; //敏感词阿里check信息
    @Column(name = "appcode")
    private Integer appcode; //敏感词阿里check信息

    /**
     * 数美suggestion
     */
    @Column(name = "suggestion_code")
    private String suggestionCode;

    /**
     * 订单号
     */
    @Column(name = "request_id")
    private String requestId;

    /**
     * 完整结果
     */
    @Column(name = "response_str")
    private String responseStr;

    @AllArgsConstructor
    @Getter
    public enum Type{
        /**
         * 新增
         */
        ADD(1),
        /**
         * 修改
         */
        UPDATE(2),
        ;

        private Integer value;
    }

    @AllArgsConstructor
    @Getter
    public enum Status{
        //审核中
        AUDITING(0),
        //审核通过
        AUDIT_SUCCESS(1),
        //审核拒绝
        AUDIT_FAIL(2),
        //90天重新活跃，复审
        REVIEW_90(3),
        ;

        private Integer value;
    }

}
