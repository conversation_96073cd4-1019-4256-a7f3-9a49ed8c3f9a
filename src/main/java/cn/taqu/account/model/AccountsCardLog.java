package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "accounts_card_log")
public class AccountsCardLog extends BaseEntity {
    @Column(name = "account_uuid")
    @JsonProperty(value = "account_uuid")
    private String accountUuid;
    @Column(name = "card_id")
    @JsonProperty(value = "card_id")
    private Long cardId;
    private String level;
    /**
     * 有效天数
     */
    @JsonProperty(value = "valid_day")
    @Column(name = "valid_day")
    private Long validDay;
    /**
     * 操作类型 1:发放; 2:回收; 3:使用; 4:赠送给别人; 5:通过别人赠送获得
     */
    private Integer type;
    @Column(name = "operate_name")
    @JsonProperty(value = "operate_name")
    private String operateName;
    private String remark;
    @Column(name = "create_time")
    @JsonProperty(value = "create_time")
    private Long createTime;

    public static class Type {
        /** 发放 **/
        public static final int GRANT = 1;
        /** 回收 **/
        public static final int REVOKE = 2;
        /** 使用 **/
        public static final int USE = 3;
        /** 赠送给别人 **/
        public static final int GIVEN = 4;
        /** 通过别人赠送获得 **/
        public static final int GET = 5;
    }

}
