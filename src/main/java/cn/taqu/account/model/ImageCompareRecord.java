package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 我的生活状态表
 * 
 * <AUTHOR>
 * 2024年11月25日下午4:45:26
 */
@Getter
@Setter
@Entity
@Table(name = "image_compare_record")
public class ImageCompareRecord extends BaseEntity implements Serializable {

    public static final Integer ALBUM = 1;

    public static final Integer LIFE = 2;

    /**
     * 原表主键
     */
    @Column(name = "origin_id")
    private Long originId;
    
    /**
     * 风控业务id
     */
    @Column(name = "biz_id")
    private String bizId;
    
    /**
     * 1.相册 2.我的生活
     */
    @Column(name = "biz_type")
    private Integer bizType;


    /**
     * 图片描述 主态
     */
    @Column(name = "conclusion")
    private String conclusion;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

}
