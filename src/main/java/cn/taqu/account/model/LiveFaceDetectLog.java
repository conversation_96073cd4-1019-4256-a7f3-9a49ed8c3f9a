package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 活体认证日志
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-18 11:40
 */
@Table(name = "live_face_detect_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class LiveFaceDetectLog extends BaseEntity {

    /**
     * 用户uuid（唯一键）
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 活体人脸检测成功后采集的底图
     */
    @Column(name = "base_photo_url")
    private String basePhotoUrl;

    /**
     * 活体人脸检测成功后，传给阿里活体人脸检测的验证图
     */
    @Column(name = "verify_photo_url")
    private String verifyPhotoUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    /**
     * 第三方 1-腾讯云 2-阿里云
     */
    @Column(name = "live_face_detect_type")
    private Integer liveFaceDetectType;

    /**
     * 认证接口状态 0-无 1-成功 2-失败
     */
    @Column(name = "live_face_detect_status")
    private Integer liveFaceDetectStatus;

    /**
     * 订单号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 结果状态 0-无 1-成功 2-失败
     */
    @Column(name = "result_status")
    private Integer resultStatus;

    /**
     * 原因 默认0-成功 1-对比失败 2-人脸对比失败 3-活体实名与实名不一致
     */
    @Column(name = "result_type")
    private Integer resultType;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
}
