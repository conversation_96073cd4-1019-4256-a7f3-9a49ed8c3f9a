package cn.taqu.account.model;

import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * CREATE TABLE `like_label` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '名称',
  `extra` varchar(100) DEFAULT '' COMMENT '额外字段',
  `pic_url` varchar(1024) NOT NULL DEFAULT '' COMMENT '图片路径，无域名',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '标签类型 1-电影/电视剧，2-音乐，3-明星，4-游戏',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `unq_id` varchar(50) DEFAULT '' COMMENT '外部唯一主键',
  `update_time` int(11) DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE,
  KEY `idx_unq_id` (`unq_id`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8 COMMENT='喜欢的内容标签'
 */ 
@Entity
@Getter
@Setter
@Table(name = "like_label")
public class LikeLabel extends BaseEntity{

	/**
	 * 名称
	 */
	@Column(name = "name")
	private String name;

	/**
	 * 额外参数 音乐-歌手名
	 */
	@Column(name = "extra")
	private String extra;

	/**
	 * 图片地址 不包含域名
	 */
	@Column(name = "pic_url")
	private String picUrl;

	/**
	 * 标签类型 1-电影/电视剧，2-音乐，3-明星，4-游戏
	 */
	@Column(name = "type")
	private Integer type;

	/**
	 * 状态 1启用 0禁用
	 */
	@Column(name = "status")
	private Integer status;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	private Long createTime;

	/**
	 * 外部唯一键
	 */
	@Column(name = "unq_id")
	private String unqId;

	/**
	 * 更新时间
	 */
	@Column(name = "update_time")
	private Long updateTime;

	
	public static enum Type{
		/**
		 * 1 电影/电视剧
		 */
		TV(1,"tv_list"),
		/**
		 * 2 音乐
		 */
		MUSIC(2,"music_list"),
		/**
		 * 3 明星
		 */
		STAR(3,"star_list"),
		/**
		 * 4 游戏
		 */
		GAME(4,"game_list");
		
		
		public int value;
		public String mapKey;
		
		private Type(int value, String mapKey) {
			this.value = value;
			this.mapKey = mapKey;
		}
		
		public static boolean isIn(Integer type) {
			Type[] values = values();
			for (Type entry : values) {
				if(Objects.equals(entry.value, type)) {
					return true;
				}
			}
			return false;
		}
	}

	public static enum Status{
		/**
		 * 使用
		 */
		USE(1),
		/**
		 * 未使用
		 */
		UNUSE(0)
		;
		
		public int value;
		private Status(int value) {
			this.value = value;
		}
		
		public static boolean isIn(Integer status) {
			Status[] values = values();
			for (Status entry : values) {
				if(Objects.equals(entry.value, status)) {
					return true;
				}
			}
			return false;
		}
		
	}

}

