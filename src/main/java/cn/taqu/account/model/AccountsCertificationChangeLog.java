package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Objects;

/**
 * 用户实名变更记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 13:51
 */
@Table(name = "accounts_certification_change_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsCertificationChangeLog extends BaseEntity {

    /**
     * 账号uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 真实姓名
     */
    @Column(name = "real_name")
    private String realName;
    /**
     * 真实姓名_密文
     */
    @Column(name = "real_name_cipher")
    private String realNameCipher;
    /**
     * 真实姓名_摘要密文
     */
    @Column(name = "real_name_digest")
    private String realNameDigest;
    /**
     * 身份证号
     */
    @Column(name = "identity_no")
    private String identityNo;
    /**
     * 身份证号_密文
     */
    @Column(name = "identity_no_cipher")
    private String identityNoCipher;
    /**
     * 身份证号_摘要密文
     */
    @Column(name = "identity_no_digest")
    private String identityNoDigest;
    
    

    /**
     * 记录类型 0-默认 1-绑定 2-解绑
     */
    @Column(name = "log_type")
    private Integer logType;

    /**
     * 操作人
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 操作时间
     */
    @Column(name = "operator_time")
    private Long operatorTime;

    /**
     * 操作类型 0-默认 1-用户 2-后台 3-脚本
     */
    @Column(name = "operator_type")
    private Integer operatorType;
    /**
     * 底图
     */
    @Column(name = "base_photo_url")
    private String basePhotoUrl;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    public enum LogTypeEnum{
        /**
         * 0-默认
         */
        DEFAULT(0),
        /**
         * 1-绑定
         */
        BIND(1),
        /**
         * 2-解绑
         */
        UNBIND(2),
        ;

        private Integer value;

        LogTypeEnum(Integer value) {
            this.value = value;
        }

        public static String getNameByValue(Integer logType) {
            if(Objects.equals(logType, BIND.getValue())){
                return "绑定";
            } else if(Objects.equals(logType, UNBIND.getValue())){
                return "解绑";
            } else{
                return "";
            }
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum OperatorTypeEnum{
        /**
         * 0-默认
         */
        DEFAULT(0),
        /**
         * 1-用户
         */
        USER(1),
        /**
         * 2-后台 backstage
         */
        BACKSTAGE(2),
        /**
         * 3-脚本 script
         */
        SCRIPT(3),

        /**
         * 4-用户注销 destroy
         */
        DESTROY(4)
        ;

        private Integer value;

        OperatorTypeEnum(Integer value) {
            this.value = value;
        }
        public Integer getValue() {
            return value;
        }
    }
}
