package cn.taqu.account.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * CREATE TABLE `accounts_like_label` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `account_uuid` char(16) NOT NULL DEFAULT '' COMMENT '用户uuid',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '标签类型 1-电影/电视剧，2-音乐，3-明星，4-游戏',
  `label_id` int(11) NOT NULL DEFAULT '0' COMMENT ' 标签id',
  `stick_type` int(2) NOT NULL DEFAULT '0' COMMENT '是否置顶 1-是，0-否',
  `seq` int(3) NOT NULL DEFAULT '0' COMMENT '置顶排序字段',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE,
  KEY `idx_account_uuid` (`account_uuid`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_update_time` (`update_time`) USING BTREE,
  KEY `idx_stick_type` (`stick_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8 COMMENT='用户喜欢的标签';
 */ 
@Entity
@Getter
@Setter
@Table(name = "accounts_like_label")
public class AccountsLikeLabel extends BaseEntity{

	@Column(name = "account_uuid")
	private String accountUuid;

	/**
	 * 标签类型 1-电影/电视剧，2-音乐，3-明星，4-游戏
	 */
	@Column(name = "type")
	private Integer type;

	/**
	 * 标签id
	 */
	@Column(name = "label_id")
	private Long labelId;
	
	/**
	 * 是否置顶 1-是，0-否
	 */
	@Column(name = "stick_type")
	private Integer stickType;

	/**
	 * 置顶排序字段
	 */
	@Column(name = "seq")
	private Integer seq;
	
	@Column(name = "create_time")
	private Long createTime;

	@Column(name = "update_time")
	private Long updateTime;


	public static enum Type{
		/**
		 * 电影/电视剧
		 */
		VT(1),
		/**
		 * 音乐
		 */
		MUSIC(2),
		/**
		 * 明星
		 */
		STAR(3),
		/**
		 * 游戏
		 */
		GAME(4);
		
		public int value;
		
		private Type(int value) {
			this.value = value;
		}
	}
	
	public static enum StickType{
		/**
		 * 置顶
		 */
		YES(1),
		/**
		 * 未置顶
		 */
		NO(0)
		;
		
		public int value;
		private StickType(int value) {
			this.value = value;
		}
		
	}
}

