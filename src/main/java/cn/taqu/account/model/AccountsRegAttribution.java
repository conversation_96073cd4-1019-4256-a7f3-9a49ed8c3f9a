package cn.taqu.account.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * CREATE TABLE `accounts_reg_attribution` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_uuid` char(16) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '账号uuid',
  `token` varchar(100) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '设备token',
  `app_version` int(11) DEFAULT '0' COMMENT '应用版本',
  `appcode` tinyint(2) DEFAULT '1' COMMENT '应用码',
  `cloned` tinyint(2) DEFAULT '1' COMMENT '分身版',
  `channel` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '渠道编号',
  `platform_id` tinyint(2) DEFAULT '0' COMMENT '平台id',
  `media_code` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '媒体渠道唯一标识',
  `creative_label` varchar(100) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '创意标签',
  `create_time` int(11) DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_idx_account_uuid` (`account_uuid`) USING BTREE,
  KEY `idx_token` (`token`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='账号注册归因表'
 */ 
@Entity
@Getter
@Setter
@Table(name = "accounts_reg_attribution")
public class AccountsRegAttribution extends BaseEntity{

	/**账号uuid*/
	@Column(name = "account_uuid")
	private String accountUuid;

	/**设备token*/
	@Column(name = "token")
	private String token;

	/**应用版本*/
	@Column(name = "app_version")
	private Integer appVersion;

	/**应用码*/
	@Column(name = "appcode")
	private Integer appcode;

	/**分身版*/
	@Column(name = "cloned")
	private Integer cloned;

	/**渠道编号*/
	@Column(name = "channel")
	private String channel;

	/**平台id*/
	@Column(name = "platform_id")
	private Integer platformId;

	/**媒体渠道唯一标识*/
	@Column(name = "media_code")
	private String mediaCode;

	/**创意标签*/
	@Column(name = "creative_label")
	private String creativeLabel;
	
	/** 账户id/渠道id,自增，如195 */
	@Column(name = "gid")
	private Long gid;
	
	/** 计划id，自增，如966 */
	@Column(name = "pid")
	private Long pid;

	/**创建时间*/
	@Column(name = "create_time")
	private Long createTime;

	/**更新时间*/
	@Column(name = "update_time")
	private Long updateTime;


}

