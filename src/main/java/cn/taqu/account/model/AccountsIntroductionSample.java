package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "accounts_introduction_sample")
public class AccountsIntroductionSample extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "sex_type")
    private Integer sexType;


    @Column(name = "sample")
    private String sample;

    @Column(name = "images")
    private String images;

    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;

}
