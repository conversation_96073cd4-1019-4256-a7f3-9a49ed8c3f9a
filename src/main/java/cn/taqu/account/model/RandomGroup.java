package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * @Author: zy
 * @Date: 2020/6/22 16:28
 */
@Entity
@Table(name = "random_group")
@Data
public class RandomGroup extends BaseEntity implements Serializable {


    @Column(name = "random_group_name")
    private String randomGroupName;
    @Column(name = "status")
    private Integer status;
    @Column(name = "parts_of_speech")
    private Integer partsOfSpeech;  //1形容词 2名词
    @Column(name = "create_time")
    private Long createTime;
    @Column(name = "update_time")
    private Long updateTime;


    @Transient
    private Integer countNum;
}
