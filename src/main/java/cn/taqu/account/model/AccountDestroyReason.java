package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户注销原因
 *
 * <AUTHOR>
 * 2025年04月15日下午4:45:26
 */
@Data
@Entity
@Table(name = "account_destroy_reason")
public class AccountDestroyReason extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 0L;

    /**
     * 用户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 加密手机
     */
    @Column(name = "mobile_cipher")
    private String mobileCipher;

    /**
     * 加密手机
     */
    @Column(name = "mobile_digest")
    private String mobileDigest;

    /**
     * 一级id
     */
    @Column(name = "parent_id")
    private Integer parentId;

    /**
     * 二级id
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 具体原因
     */
    private String reason;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

}
