package cn.taqu.account.model;

import java.io.Serializable;
import java.util.Objects;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import cn.taqu.account.constant.PersonalityLabelEnum;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.taqu.core.orm.base.BaseEntity;

/**
 * 个性标签
 *
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "personality_label")
@DynamicInsert
@DynamicUpdate
public class PersonalityLabel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 8154248054528441357L;

    private String content; //标签内容
    // 2019.10.22 对象转json时必存在 警惕转成null字符串
    @JsonInclude
    private String description; //描述

    /**
     * 标签类型
     *
     * @see PersonalityLabelEnum
     */
    private Integer type;
    private Integer second_type; //二级分类(1:喜欢;2:不喜欢)
    private Integer sex; //标签性别 1男2女3通用
    private Integer status; //状态 0启动1禁用
    @Transient
    private Long use_count; //使用次数
    private Long create_time;//创建时间
    private Long update_time;//更新时间

    /**
     * 排序，数值越大，排序越靠前
     */
    private Integer sort;

    public Integer getSecond_type() {
        return Objects.equals(type, 4) ? second_type : null;
    }

    public void setSecond_type(Integer second_type) {
        this.second_type = second_type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getUse_count() {
        return use_count;
    }

    public void setUse_count(Long use_count) {
        this.use_count = use_count;
    }

    public Long getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Long create_time) {
        this.create_time = create_time;
    }

    public Long getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Long update_time) {
        this.update_time = update_time;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSort() {
        if (sort == null) {
            sort = 0;
        }
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * 性别是否匹配
     *
     * @param sexType
     * @return
     */
    public boolean match(Integer sexType) {
        if (Objects.equals(this.sex, sexType)) {
            return true;
        }

        // 3代表通用
        return Objects.equals(this.sex, 3);
    }

}
