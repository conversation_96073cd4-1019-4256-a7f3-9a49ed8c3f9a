package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table( name = "accounts_forum_profile")
public class AccountsForumProfile extends BaseEntity implements Serializable {

	private static final long serialVersionUID = -8392562851846138409L;

	private Long account_id = 0L;//用户帐户ID
	private String account_uuid;//账户uuid
	@Deprecated
	private Integer account_level = 0;//用户等级表ID
	private Integer account_actor = 0;//用户特殊头衔0没有特殊头衔
	@Deprecated
	private Long experience = 0L;//用户经验值
	private Long total_posts = 0L;//该用户发帖总量
	private Long invaild_posts = 0L;//该用户发布的有效帖子数
	private Long unvaild_posts = 0L;//该用户发布的无效帖子数
	private Long total_reviews = 0L;//总回复量
	private Long invaild_reviews = 0L;//有效回复量
	private Long unvaild_reviews = 0L;//无效回复量
	private Long total_reply_num = 0L;//总回帖量
	private Long invaild_reply_num = 0L;//有效回帖量
	private Long unvaild_reply_num = 0L;//无效回帖量
	private Long total_mail_num = 0L;//总私信人数
	private Long total_mail_letter_num = 0L;//总发送私信条目数
	private Long total_warnings = 0L;//总警告次数
	private Integer total_forbid_token = 0;//禁设备次数
	private Integer total_forbid_account = 0;//总禁言次数
	private Long last_speak_time;//最后发言时间
	private Long total_receive_reviews = 0L;//总接受到的回复数量
	private Long total_receive_replys = 0L;//总接收的评论数量
	private Long last_online_time;//最后在线时间
	private Long browse_posts = 0L;//浏览帖子数量
	@Deprecated
	private Integer peipei_level = 1;//配配等级值，默认1级
	@Deprecated
	private Long peipei_experience = 0L;//配配经验值
	@Deprecated
	private String driver_level = "C"; //驾照级别 C或V
	@Deprecated
	private Long kilometer = 0L; //驾照公里数
	
	public AccountsForumProfile() {
	}
	
	public Long getAccount_id() {
		return account_id;
	}
	public void setAccount_id(Long account_id) {
		this.account_id = account_id;
	}

	public String getAccount_uuid() {
		return account_uuid;
	}

	public void setAccount_uuid(String account_uuid) {
		this.account_uuid = account_uuid;
	}

	/**
	 * 获取用户等级(返回的是accounts_level表的id字段)
	 */
	public Integer getAccount_level() {
		return account_level;
	}
	/**
	 * 设置用户等级(该字段保存的是accounts_level表的id字段)
	 */
	public void setAccount_level(Integer account_level) {
		this.account_level = account_level;
	}
	public Integer getAccount_actor() {
		return account_actor;
	}
	public void setAccount_actor(Integer account_actor) {
		this.account_actor = account_actor;
	}
	public Long getExperience() {
		return experience;
	}
	public void setExperience(Long experience) {
		this.experience = experience;
	}
	public Long getTotal_posts() {
		return total_posts;
	}
	public void setTotal_posts(Long total_posts) {
		this.total_posts = total_posts;
	}
	public Long getInvaild_posts() {
		return invaild_posts;
	}
	public void setInvaild_posts(Long invaild_posts) {
		this.invaild_posts = invaild_posts;
	}
	public Long getUnvaild_posts() {
		return unvaild_posts;
	}
	public void setUnvaild_posts(Long unvaild_posts) {
		this.unvaild_posts = unvaild_posts;
	}
	public Long getTotal_reviews() {
		return total_reviews;
	}
	public void setTotal_reviews(Long total_reviews) {
		this.total_reviews = total_reviews;
	}
	public Long getInvaild_reviews() {
		return invaild_reviews;
	}
	public void setInvaild_reviews(Long invaild_reviews) {
		this.invaild_reviews = invaild_reviews;
	}
	public Long getUnvaild_reviews() {
		return unvaild_reviews;
	}
	public void setUnvaild_reviews(Long unvaild_reviews) {
		this.unvaild_reviews = unvaild_reviews;
	}
	public Long getTotal_reply_num() {
		return total_reply_num;
	}
	public void setTotal_reply_num(Long total_reply_num) {
		this.total_reply_num = total_reply_num;
	}
	public Long getInvaild_reply_num() {
		return invaild_reply_num;
	}
	public void setInvaild_reply_num(Long invaild_reply_num) {
		this.invaild_reply_num = invaild_reply_num;
	}
	public Long getUnvaild_reply_num() {
		return unvaild_reply_num;
	}
	public void setUnvaild_reply_num(Long unvaild_reply_num) {
		this.unvaild_reply_num = unvaild_reply_num;
	}
	public Long getTotal_mail_num() {
		return total_mail_num;
	}
	public void setTotal_mail_num(Long total_mail_num) {
		this.total_mail_num = total_mail_num;
	}
	public Long getTotal_mail_letter_num() {
		return total_mail_letter_num;
	}
	public void setTotal_mail_letter_num(Long total_mail_letter_num) {
		this.total_mail_letter_num = total_mail_letter_num;
	}
	public Long getTotal_warnings() {
		return total_warnings;
	}
	public void setTotal_warnings(Long total_warnings) {
		this.total_warnings = total_warnings;
	}
	public Integer getTotal_forbid_token() {
		return total_forbid_token;
	}
	public void setTotal_forbid_token(Integer total_forbid_token) {
		this.total_forbid_token = total_forbid_token;
	}
	public Integer getTotal_forbid_account() {
		return total_forbid_account;
	}
	public void setTotal_forbid_account(Integer total_forbid_account) {
		this.total_forbid_account = total_forbid_account;
	}
	public Long getLast_speak_time() {
		return last_speak_time;
	}
	public void setLast_speak_time(Long last_speak_time) {
		this.last_speak_time = last_speak_time;
	}
	public Long getTotal_receive_reviews() {
		return total_receive_reviews;
	}
	public void setTotal_receive_reviews(Long total_receive_reviews) {
		this.total_receive_reviews = total_receive_reviews;
	}
	public Long getTotal_receive_replys() {
		return total_receive_replys;
	}
	public void setTotal_receive_replys(Long total_receive_replys) {
		this.total_receive_replys = total_receive_replys;
	}
	public Long getLast_online_time() {
		return last_online_time;
	}
	public void setLast_online_time(Long last_online_time) {
		this.last_online_time = last_online_time;
	}
	public Long getBrowse_posts() {
		return browse_posts;
	}
	public void setBrowse_posts(Long browse_posts) {
		this.browse_posts = browse_posts;
	}
	public Integer getPeipei_level() {
		return peipei_level;
	}
	public void setPeipei_level(Integer peipei_level) {
		this.peipei_level = peipei_level;
	}
	public Long getPeipei_experience() {
		return peipei_experience;
	}
	public void setPeipei_experience(Long peipei_experience) {
		this.peipei_experience = peipei_experience;
	}
	public String getDriver_level() {
		return driver_level;
	}
	public void setDriver_level(String driver_level) {
		this.driver_level = driver_level;
	}
	public Long getKilometer() {
		return kilometer;
	}
	public void setKilometer(Long kilometer) {
		this.kilometer = kilometer;
	}
}
