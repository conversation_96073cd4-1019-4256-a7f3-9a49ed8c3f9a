package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-13 18:10
 */
@Table(name = "accounts_photo_img_quality_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsPhotoImgQualityLog extends BaseEntity {

    /**
     * uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 图片地址
     */
    @Column(name = "photo_url")
    private String photoUrl;

    /**
     * 长图
     */
    @Column(name = "long_image")
    private Boolean longImage;

    /**
     * 黑白
     */
    @Column(name = "black_and_white")
    private Boolean blackAndWhite;

    /**
     * 大图
     */
    @Column(name = "small_image")
    private Boolean smallImage;

    /**
     * 大图
     */
    @Column(name = "big_image")
    private Boolean bigImage;

    /**
     * 纯图
     */
    @Column(name = "pure_image")
    private Boolean pureImage;

    /**
     * 清晰度得分
     */
    @Column(name = "clarity_score")
    private Integer clarityScore;

    /**
     * 美观度得分
     */
    @Column(name = "aesthetic_score")
    private Integer aestheticScore;

    /**
     * 响应结果
     */
    @Column(name = "response_str")
    private String responseStr;

    /**
     * 第三方订单号
     */
    @Column(name = "third_order_no")
    private String thirdOrderNo;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;



}
