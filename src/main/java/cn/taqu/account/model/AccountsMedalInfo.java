package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Arrays;
import java.util.Collection;

@Getter
@Setter
@Table(name = "accounts_medal_info")
@Entity
public class AccountsMedalInfo extends BaseEntity {
    public static Collection<String> fields = Arrays.asList("account_uuid", "medal_id", "update_time", "end_time", "status");

    private String account_uuid;
    private Long medal_id;
    private Long update_time;
    private Long end_time;
    private Integer status;//1:正常; 2:手动卸载; 3:已过期;
}
