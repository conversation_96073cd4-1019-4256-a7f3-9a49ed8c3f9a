package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 认证相关操作日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-29 14:51
 */
@Table(name = "accounts_cert_log")
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class AccountsCertLog extends BaseEntity {

    /**
     * 账号uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 真实姓名
     */
    @Column(name = "cert_white_list")
    private Integer certWhiteList;
    /**
     * 真实姓名
     */
    @Column(name = "cert_type")
    private Integer certType;

    /**
     * 身份证号
     * 2024.07.19 数据库不存，不只是身份证
     */
//    @Deprecated
//    @Column(name = "cert_no")
//    private String certNo;

    /**
     * 操作人
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 操作时间
     */
    @Column(name = "operator_time")
    private Long operatorTime;

    /**
     * 操作类型 0-默认 1-绑定 2-解绑 3-换绑
     */
    @Column(name = "operator_type")
    private Integer operatorType;

    /**
     * 底图
     */
    @Column(name = "base_photo_url")
    private String basePhotoUrl;
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    public enum OperatorTypeEnum{
        /**
         * 0-默认
         */
        DEFAULT(0),
        /**
         * 1-绑定
         */
        BIND(1),
        /**
         * 2-解绑
         */
        CANCEL(2),
        /**
         * 3-换绑 script
         */
        CHANGE(3),
        ;

        private Integer value;

        OperatorTypeEnum(Integer value) {
            this.value = value;
        }
        public Integer getValue() {
            return value;
        }
    }
}
