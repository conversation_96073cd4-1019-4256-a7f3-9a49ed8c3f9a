/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.model;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @author:laik<PERSON><PERSON>
 */
@Setter
@Getter
@Entity
@Table(name = "members_app_info")
public class MembersAppInfo extends BaseEntity implements Serializable {

	private static final long serialVersionUID = -7852488269543345623L;
	
	private Long member_id;//设备ID
	
	private Long app_version;
	
	private Integer appcode;//应用类型
	
	private String place;//渠道编号
	
	private Integer platform_id;
	
	private Long create_time;
	
	private Long update_time;

	private Integer is_jailbroken;
	/**
	 * 是否开启通知 0:否; 1:是;
	 */
	private Integer notice_enable;
	private Integer cloned;
}
