package cn.taqu.account.utils;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import cn.taqu.account.dto.SwitchDto20221027;
import cn.taqu.core.protocol.SoaBaseParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
public class AccountDestroyUtil {


    private static final String destroyTip1 = "本次操作，您的账号将会被注销，平台特为您设有14天冷静期，14天之内登录账号将撤销本次注销，可正常登录使用；如账号内所存在的任何相关礼物、道具到期，平台将无法进行恢复，其他信息数据保留不变；超过14天后账号内任何信息数据将完全清空，且不可申请恢复，账号将永久注销，无法登录使用。";
    private static final String destroyTip2 = "此账号注销中，请在14天内不再登入该账号，以确保账号注销完成。注销14天后${time}前再次登入可撤销本次注销申请。";

    private static Map<String, Map<String, String>> freezeConfig;
//    {
//    "1_1": {
//        "android": 7999,
//        "ios": 7999
//    }
//}

    private static Map<String, Map<String, String>> destroyTipsConfig;

    public static void initConfig(String raw) {
        freezeConfig = JSONUtil.toBean(raw, new TypeReference<Map<String, Map<String, String>>>() {
        }, false);
        log.info("freezeConfig: {}", raw);
    }

    public static void initDestroyTipsConfig(String raw) {
        destroyTipsConfig = JSONUtil.toBean(raw, new TypeReference<Map<String, Map<String, String>>>() {
        }, false);
        log.info("destroyTipsConfig: {}", raw);
    }

    public static boolean hasDestroyFreeze() {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        Integer appCode = soaBaseParams.getAppcode();
        Integer cloned = soaBaseParams.getCloned();
        return hasDestroyFreeze(appCode, cloned);
    }

    public static boolean hasDestroyFreeze(Integer appCode, Integer cloned) {
        String freezeAppMark = appCode + "_" + cloned;

        if (freezeConfig.containsKey(freezeAppMark)) {
            Map<String, String> map = freezeConfig.get(freezeAppMark);
            Long androidVersion = MapUtils.getLong(map, "android", 7999L);
            Long iosVersion = MapUtils.getLong(map, "ios", 7999L);
            SwitchDto20221027 switchDto = new SwitchDto20221027(androidVersion, iosVersion);
            return switchDto.isGeVersion();
        }
        return false;
    }

    public static String getDestroyTip1() {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        Integer appCode = soaBaseParams.getAppcode();
        Integer cloned = soaBaseParams.getCloned();
        return getDestroyTips1(appCode, cloned);
    }

    public static String getDestroyTips1(Integer appCode, Integer cloned) {
        String freezeAppMark = appCode + "_" + cloned;
        return MapUtils.getString(destroyTipsConfig.get(freezeAppMark), "tip1", destroyTip1);
    }

    public static String getDestroyTip2() {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        Integer appCode = soaBaseParams.getAppcode();
        Integer cloned = soaBaseParams.getCloned();
        return getDestroyTips2(appCode, cloned);
    }

    public static String getDestroyTips2(Integer appCode, Integer cloned) {
        String freezeAppMark = appCode + "_" + cloned;
        return MapUtils.getString(destroyTipsConfig.get(freezeAppMark), "tip2", destroyTip2)
                .replace("${time}", cn.hutool.core.date.DateUtil.date().toString("HH:mm"));
    }

}
