package cn.taqu.account.utils;

import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

@Component
public class IncorrectResultSizeDataAccessExceptionToNull implements ExceptionToNull {

    /**
     * 如果捕获到IncorrectResultSizeDataAccessException,返回null.
     * @param action
     * @param <R>
     * @return
     */
    @Override
    public <R> R execute(Supplier<R> action) {
        R result;
        try {
            result = action.get();
        } catch (IncorrectResultSizeDataAccessException e) {
            return null;
        }
        return result;
    }
}
