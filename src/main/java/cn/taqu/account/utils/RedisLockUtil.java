package cn.taqu.account.utils;


import java.io.Serializable;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import cn.taqu.core.exception.ServiceException;
import redis.clients.jedis.Protocol;
import redis.clients.util.SafeEncoder;

/**
 * redis分布式锁工具
 *
 * <AUTHOR>
 */
@Component
public final class RedisLockUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisLockUtil.class);

    private static final int DEFUALT_EXPIRE = 60;

    @Autowired
    private StringRedisTemplate lockStringRedisTemplate;

    private RedisLockUtil() {

    }

    /**
     * 获取锁后执行
     *
     * @param supplier
     * @return
     */
    public <T> T executeWithLock(String key, Supplier<T> supplier) {
        this.lockUtilSuccess(key);
        T result;
        try {
            result = supplier.get();
        } finally {
            this.unLock(key);
        }
        return result;
    }

    /**
     * 获取锁后执行
     *
     * @param supplier
     * @return
     */
    public <T> T executeWithLock(String key, int expire, Supplier<T> supplier) {
    	this.lockUtilSuccess(key, expire);
    	T result;
    	try {
    		result = supplier.get();
    	} finally {
    		this.unLock(key);
    	}
    	return result;
    }

    /**
     * 获取锁。如果锁被占有则阻塞，直到获取到锁。
     *
     * @param key
     */
    public void lockUtilSuccess(String key) {
        while (true) {
            boolean hold = this.lock(key);
            if (!hold) {
                try {
                    Thread.sleep(10L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOGGER.error("线程被interrupt了。", e);
                    throw new ServiceException(e);
                }
            } else {
                break;
            }
        }
    }

    /**
     * 获取锁。如果锁被占有则阻塞，直到获取到锁。
     *
     * @param key
     */
    public void lockUtilSuccess(String key,int expire) {
    	while (true) {
    		boolean hold = this.lock(key, expire);
    		if (!hold) {
    			try {
    				Thread.sleep(20L);
    			} catch (InterruptedException e) {
    				Thread.currentThread().interrupt();
    				LOGGER.error("线程被interrupt了。", e);
    				throw new ServiceException(e);
    			}
    		} else {
    			break;
    		}
    	}
    }

    /**
     * 获取锁。如果锁被占有则阻塞，直到获取到锁。
     * @param key
     * @param sleepMs 设置间歇时间
     * @param expireMs 设置锁超时时间
     * @param times 尝试次数
     * @return
     */
    public Boolean lockLimitTimes(String key, int sleepMs, int expireMs, int times) {
        boolean hold = false;
        for (int i = 0; i < times; i++) {
            hold = this.lock(key, expireMs);
            if (!hold) {
//                LOGGER.info("key加锁失败.key={}.上限次数={}.当前次数={}", key, times, (i+1));
                try {
                    Thread.sleep(sleepMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOGGER.error("线程被interrupt了。", e);
                    throw new ServiceException(e);
                }
            } else {
                break;
            }
        }
        return hold;
    }

    public boolean lock(String key) {
        return lock(key, DEFUALT_EXPIRE);
    }

    /**
     * 加锁
     *
     * @param key    redis key
     * @param expire 过期时间，单位毫秒
     * @return true:加锁成功，false，加锁失败
     */
    public boolean lock(String key, int expire) {
        ValueOperations<String, String> opsForValue = lockStringRedisTemplate.opsForValue();

        long value = System.currentTimeMillis() + expire;
        Boolean status = opsForValue.setIfAbsent(key, String.valueOf(value));
        if (status) {
            return true;
        }

        long oldExpireTime = parseLongEx(opsForValue.get(key));
        if (oldExpireTime < System.currentTimeMillis()) {
            //超时
            long newExpireTime = System.currentTimeMillis() + expire;
            long currentExpireTime = parseLongEx(opsForValue.getAndSet(key, String.valueOf(newExpireTime)));
            if (currentExpireTime == oldExpireTime) {
                return true;
            }
        }
        return false;
    }

    private long parseLongEx(String raw) {
        if (StringUtils.isBlank(raw)) {
            return 0;
        }

        return Long.parseLong(raw);
    }

    public void unLock(String key) {
//        ValueOperations<String, String> opsForValue = accountStringRedisTemplate.opsForValue();
//        long oldExpireTime = parseLongEx(opsForValue.get(key));
//        if (oldExpireTime > System.currentTimeMillis()) {
//        }
        lockStringRedisTemplate.delete(key);
    }

    /**
     * 设置有超时时间的分布式锁，true-成功加锁，false-已被其他线程执行，加锁失败
     * @param key
     * @param value
     * @param exptime
     * @return
     */
    public boolean setIfAbsent(final String key, final Serializable value, final long exptime) {
        Boolean b = (Boolean) lockStringRedisTemplate.execute(new RedisCallback() {
            @Override
            public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                RedisSerializer valueSerializer = lockStringRedisTemplate.getValueSerializer();
                RedisSerializer keySerializer = lockStringRedisTemplate.getKeySerializer();
                Object obj = connection.execute("set", keySerializer.serialize(key),
                        valueSerializer.serialize(value),
                        SafeEncoder.encode("NX"),
                        SafeEncoder.encode("EX"),
                        Protocol.toByteArray(exptime));
                return obj != null;
            }
        });
        return b;
    }


    public boolean lock(String key, String me, long expire) {
        ValueOperations<String, String> opsForValue = lockStringRedisTemplate.opsForValue();
        Boolean status = opsForValue.setIfAbsent(key, me);
        if (status) {
            lockStringRedisTemplate.expire(key, expire, TimeUnit.MILLISECONDS);
        }
        return Optional.of(status).orElse(false);
    }

}
