package cn.taqu.account.utils;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 时间限制工具类，运用于各种场景，方便测试校验时间限制是否生效
 *
 * <AUTHOR>
 * @date 2024/9/10 8:08 下午
 */
@Slf4j
public class LimitTimeUtils {

    /**
     * 类型匹配
     *
     * @param mode
     * @return
     */
    public static LimitMode of(String mode) {
        Optional<LimitMode> optional = Arrays.stream(LimitMode.values()).filter(m -> Objects.equals(m.name(), mode)).findFirst();
        if (optional.isPresent()) {
            return optional.get();
        }

        log.error("[时间限制工具]系统异常:{}", mode);
        throw new ServiceException(CodeStatus.COMMON_ERROR_CODE);
    }

    /**
     * 限制模式
     */
    public enum LimitMode {
        /**
         * 分钟级限制
         */
        MINUTES,
        /**
         * 小时级限制
         */
        HOURS,
        /**
         * 日级限制
         */
        DAYS,
        /**
         * 月份级限制
         */
        MONTHS,
        ;

        /**
         * 匹配算法
         *
         * @param mode
         * @return
         */
        Boolean match(String mode) {
            return this.name().equals(mode);
        }

        /**
         * 获取获取时间配置
         *
         * @return 返回时间模式key，如20240910、20240910_1024、202409....
         */
        public String timeKey() {
            LocalDateTime now = LocalDateTime.now();

            switch (this) {
                case MINUTES:
                    return DateHelper.getMinute(now);
                case HOURS:
                    return DateHelper.getHour(now);
                case DAYS:
                    return DateHelper.getDt(now);
                case MONTHS:
                    return DateHelper.getMonth(now);
                default:
                    return "";
            }
        }

        /**
         * 获取获取时间单位
         * 特殊说明：月份模式比较特殊返回days
         *
         * @param threshold 限制阈值
         * @return left: 时间单位
         * right： 限制阈值
         */
        public Pair<Integer, TimeUnit> leaseTime(Integer threshold) {
            // 月份需要特殊处理
            if (LimitMode.MONTHS.match(this.name())) {
                return Pair.of(threshold * 31, TimeUnit.DAYS);
            }

            return Pair.of(threshold, TimeUnit.valueOf(this.name()));
        }
    }

}
