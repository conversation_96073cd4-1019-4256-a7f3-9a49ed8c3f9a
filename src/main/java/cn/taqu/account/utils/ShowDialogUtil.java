package cn.taqu.account.utils;

import cn.taqu.account.common.ClonedEnum;
import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.common.ShanLianEnum;
import cn.taqu.account.constant.CommConst;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * ShowDialog 相关工具类
 *
 * <AUTHOR>
 * @date 2021/03/29
 */
public class ShowDialogUtil {

    /**
     * 小秘书弹窗title
     */
    public static String secTips() {
//        return ClonedEnum.isShanLian(SoaBaseParams.fromThread().getCloned()) ? CommConst.SL_SEC_TIPS : CommConst.TQ_SEC_TIPS;
        Integer cloned=SoaBaseParams.fromThread().getCloned();
        List<String> clonedList=ShanLianEnum.getNvBaoCloneList();
        if(clonedList.contains(String.valueOf(cloned)) || ClonedGroupEnum.YOUNG_GROUP.contains(cloned)){
            return CommConst.NVBAO_SEC_TIPS;
        }
        if(ClonedEnum.TAQU_PLUS.getCode().equals(cloned)){
            return CommConst.NVBAO_SEC_TIPS;
        }

        return ShanLianEnum.isShanLianApp(SoaBaseParams.fromThread().getCloned()) ?
                ShanLianEnum.tipTitle(SoaBaseParams.fromThread().getCloned()) :
                CommConst.TQ_SEC_TIPS;
    }

    public static void throwShowDialog(String content, String confirmText, String confirmSchema, String cancelText, String cancelSchema) {
        throwShowDialog(secTips(), content, confirmText, confirmSchema, cancelText, cancelSchema);
    }

    /**
     * 通过异常抛出 ShowDialog 客户端提示框，带按钮
     * https://notes.dingtalk.com/doc/nb9XJe9qxyb2lzyA?orgId=6541433&dd_progress=false&showmenu=false </br>
     *
     * @param title         标题，加粗黑色，字体较大，用于显示重要信息；
     * @param content       内容，普通字色，展示详细信息；
     * @param confirmText   右边高亮按钮文本，一般是【确定】按钮，传空不展示；
     * @param confirmSchema 右边按钮的公共跳转，可传空；
     * @param cancelText    左边灰色按钮文本，一般是【取消】按钮，传空不展示；
     * @param cancelSchema  左边按钮的公共跳转，可传空；
     */
    public static void throwShowDialog(String title, String content, String confirmText, String confirmSchema, String cancelText, String cancelSchema) {
        Map<String, String> dialogMap = Maps.newHashMap();
        if(StringUtils.isNotBlank(title)) {
            dialogMap.put("title", title);
        }
        if(StringUtils.isNotBlank(content)) {
            dialogMap.put("content", content);
        }
        if(StringUtils.isNotBlank(confirmText)) {
            dialogMap.put("confirmText", confirmText);
        }
        if(StringUtils.isNotBlank(confirmSchema)) {
            dialogMap.put("confirmSchema", confirmSchema);
        }
        if(StringUtils.isNotBlank(cancelText)) {
            dialogMap.put("cancelText", cancelText);
        }
        if(StringUtils.isNotBlank(cancelSchema)) {
            dialogMap.put("cancelSchema", cancelSchema);
        }

        throw new ServiceException("show_dialog", JsonUtils.objectToString(dialogMap));
    }
}
