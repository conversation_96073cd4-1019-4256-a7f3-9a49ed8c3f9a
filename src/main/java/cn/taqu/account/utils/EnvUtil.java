package cn.taqu.account.utils;

import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import cn.taqu.core.utils.LocalConfUtil;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@UtilityClass
public class EnvUtil {
    
    public static final List<String> GRAY_LIST = Arrays.asList("gray", "1");
    public static final List<String> ONLINE_LIST = Arrays.asList("online", "0");

    public final String ONLINE = "online";

    public final String GRAY   = "gray";

    public final String TEST   = "test";

    public final String DEV    = "dev";

    public String getEnv() {
        String env = System.getenv("TQ_K8S_ENV");
        if (StringUtils.isBlank(env)) {
            env = DEV;
        }
        return env;
    }

    public String getTopicName(String topic) {
        String env = getEnv();
        return ONLINE.equals(env) || TEST.equals(env) ? topic : topic + "_" + env;
    }
    
    public static boolean isGray() {
        String env = LocalConfUtil.getLocalEnv();
        
        if(GRAY_LIST.contains(env)) {
            return true;
        }else {
            return false;
        }
    }
    
    
}
