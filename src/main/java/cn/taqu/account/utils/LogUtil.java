package cn.taqu.account.utils;

import cn.taqu.core.utils.LocalConfUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 简单打测试环境日志，因为系统设置测试环境日志级别为info。
 * 防止测试期间日志带上线，造成内存飙升。
 *
 * <AUTHOR>
 * @date 2024/12/12 15:39
 */
@Slf4j
public class LogUtil {

    /**
     * 测试环境
     *
     * @param content
     * @param val
     */
    public static void info4Dev(String content, Object... val) {
        if (isTestEnv()) {
            log.info(content, val);
        }
    }

    /**
     * 测试环境
     *
     * @param content
     * @param val
     */
    public static void info4Gray(String content, Object... val) {
        String localEnv = LocalConfUtil.getLocalEnv();
        boolean gray = localEnv.equals("gray");
        if (gray) {
            log.info(content, val);
            return;
        }

        if (localEnv.startsWith("test")) {
            log.info(content, val);
        }
    }

    /**
     * 是否测试环境
     *
     * @return
     */
    private static boolean isTestEnv() {
        try {
            String localEnv = LocalConfUtil.getLocalEnv();
            return localEnv.startsWith("test");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 是否灰度环境
     *
     * @return
     */
    private static boolean isGrayEnv() {
        try {
            String localEnv = LocalConfUtil.getLocalEnv();
            return localEnv.equals("gray");
        } catch (Exception e) {
            return false;
        }
    }
}
