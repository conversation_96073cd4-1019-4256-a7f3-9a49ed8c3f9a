package cn.taqu.account.utils;

import java.util.regex.Pattern;

/**
 * 用户相关的方法
 * 
 * <AUTHOR>
 * @date 2021/09/08
 */
public class AccountUtil {

    // 校验uuid正则表达式
    private static final Pattern UUID_PATTERN = Pattern.compile("^[A-Za-z0-9]{11,16}$");

    /**
     * 校验是否是uuid
     * 
     * @param text
     * @return
     */
    public static boolean checkUuid(String text) {
        return UUID_PATTERN.matcher(text).matches();
    }
}
