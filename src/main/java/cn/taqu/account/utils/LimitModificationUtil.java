package cn.taqu.account.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Component
public class LimitModificationUtil {

    private String rediskey_avatar_limit_num = "twenty_avatar_limit_{uuid}";

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private AccountsInfoService accountsInfoService;

    private static boolean isLimit = false;

    private static long startTime;

    private static long endTime;

//    /account/profile/modifyLimit
//    {
//        "isLimit": "true",
//        "startTime": "2022-10-16 00:00:00",
//        "endTime": "2022-10-22 23:59:59"
//    }

    public static void initConfig(String raw) {
        Map<String, String> map = JSONUtil.toBean(raw, new TypeReference<Map<String, String>>() {
        }, true);
        isLimit = MapUtils.getBoolean(map, "isLimit", false);
        startTime = DateUtil.parse(MapUtils.getString(map, "startTime", "2099-12-31 00:00:00")).getTime();
        endTime = DateUtil.parse(MapUtils.getString(map, "endTime", "2099-12-31 00:00:00")).getTime();
    }

    //    /account/profile/modifyAvatarLimit
//    {
//        "isLimit": "true",                             // 是否开启限制
//        "limitSize": 3,                                // 限制修改次数
//        "limitRegisterDay": 2,                         // 限制注册天数
//        "limitSex": "1|2",                             // 限制性别
//        "startTime": "2022-10-16 00:00:00",            // 限制开始时间
//        "endTime": "2022-10-22 23:59:59"               // 限制结束时间
//    }
    private static boolean avatarIsLimit = false;
    private static int avatarLimitSize;
    private static int avatarLimitRegisterDay;
    private static List<Integer> avatarLimitSex;
    private static long avatarStartTime;
    private static long avatarEndTime;

    public static void initAvatarConfig(String raw) {
        Map<String, String> map = JSONUtil.toBean(raw, new TypeReference<Map<String, String>>() {
        }, false);
        avatarIsLimit = MapUtils.getBoolean(map, "isLimit", false);
        avatarLimitSize = MapUtils.getInteger(map, "limitSize", 3);
        avatarLimitRegisterDay = MapUtils.getInteger(map, "limitRegisterDay", 2);
        avatarLimitSex = Arrays.stream(MapUtils.getString(map, "limitSex", "1|2").split("\\|")).map(Integer::parseInt).collect(Collectors.toList());
        avatarStartTime = DateUtil.parse(MapUtils.getString(map, "startTime", "2099-12-31 00:00:00")).getTime();
        avatarEndTime = DateUtil.parse(MapUtils.getString(map, "endTime", "2099-12-31 00:00:00")).getTime();
    }

    public static void isLimitModify() {
        long current = System.currentTimeMillis();
        if (isLimit && current >= startTime && current <= endTime) {
            throw new ServiceException("modify_limitation_error", "系统维护中，暂时无法修改");
        }
    }

    public void isLimitAvatarModify(String uuid) {
        long current = System.currentTimeMillis();

        if (isLimit && current >= startTime && current <= endTime) {
            if (avatarIsLimit && current >= avatarStartTime && current <= avatarEndTime) {
                Map<String, Object> uuidInfos = accountsInfoService.getInfoByUuid(
                        new String[]{uuid},
                        new String[]{"sex_type", "create_time"},
                        "1", false, false).get(uuid);
                // 判断用户性别是否符合
                if (uuidInfos != null && uuidInfos.containsKey("sex_type")) {
                    if (avatarLimitSex.contains(MapUtils.getInteger(uuidInfos, "sex_type"))) {
                        // 判断注册时间是否符合
                        if (uuidInfos.containsKey("create_time")) {
                            if (DateUtil.currentSeconds() - MapUtils.getLong(uuidInfos, "create_time") <= avatarLimitRegisterDay * 86400L) {
                                // 判断是否达到次数上限
                                int currentSize = getRequestSize(uuid);
                                if (currentSize < avatarLimitSize) {
                                    recordRequest(uuid, currentSize, avatarEndTime - current); // 请求记录+1
                                    return;
                                } else {
                                    log.warn("uuid: {} 20大期间头像设置达到次数上限", uuid);
                                }
                            }
                        }
                    }
                }
            }

            throw new ServiceException("modify_limitation_error", "系统维护中，暂时无法修改");
        }
    }

    private int getRequestSize(String uuid) {
        String redisKey = rediskey_avatar_limit_num.replace("{uuid}", uuid);
        String num = accountStringRedisTemplate.opsForValue().get(redisKey);
        return StringUtils.isBlank(num) ? 0 : Integer.parseInt(num);
    }

    private void recordRequest(String uuid, int currentSize, long expireTime) {
        String redisKey = rediskey_avatar_limit_num.replace("{uuid}", uuid);
        accountStringRedisTemplate.opsForValue().set(redisKey, currentSize + 1 + "");
        accountStringRedisTemplate.expire(redisKey, expireTime + RandomUtil.randomInt(1000), TimeUnit.MILLISECONDS);
    }
}
