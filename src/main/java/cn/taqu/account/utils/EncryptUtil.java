package cn.taqu.account.utils;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by 杜晓东 on 2019/12/5.
 * 加密的值如果是null，需要设置成空字符串，不然加密那边有问题
 */
@Slf4j
public class EncryptUtil {
    public static final String ACCOUNT_CODE = "account";
    public static final String KEY = "key";
    public static final String SERVICE_OPERATION = "operation";

    /**
     * 单个加密
     * @param content
     * @return
     */
    public static String encrypt(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        String method = "encrypt";
        Object[] form = {ACCOUNT_CODE, content};
        SoaClient soaClient = SoaClientFactory.create(SoaServer.GO.ENCRYPT);
        SoaResponse response = soaClient.post().call(SERVICE_OPERATION, method, form);
        if (response.fail()) {
            log.error(LoggerUtils.soaRequestFail("go_encrypt", SERVICE_OPERATION, method, response.getCode(), response.getMsg()));
            return "";
        }
        return response.getData();
    }

    /**
     * 单个解密
     * @param content
     * @return
     */
    public static String decrypt(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        String method = "decrypt";
        Map<String, String> map = ImmutableMap.of(KEY, content);
        Object[] form = {ACCOUNT_CODE, map};
        SoaClient soaClient = SoaClientFactory.create(SoaServer.GO.ENCRYPT);
        SoaResponse response = soaClient.post().call(SERVICE_OPERATION, method, form);
        if (response.fail()) {
            log.error(LoggerUtils.soaRequestFail("go_decrypt", SERVICE_OPERATION, method, response.getCode(), response.getMsg()));
            return "";
        }
        String data = response.getData();
        return JSON.parseObject(data).getString(KEY);

    }

    /**
     * 批量加密
     * @param content
     * @return
     */
    public static Map<String,String> batchEncrypt(Map<String,String> content){
        Map<String,String> result = Maps.newHashMap();
        if(MapUtils.isEmpty(content)){
            return result;
        }

        String method = "batchEncrypt";
        Object[] form = {ACCOUNT_CODE, content};
        SoaClient soaClient = SoaClientFactory.create(SoaServer.GO.ENCRYPT);
        SoaResponse response = soaClient.post().call(SERVICE_OPERATION, method, form);
        if (response.fail()) {
            log.error(LoggerUtils.soaRequestFail("go_batchEncrypt", SERVICE_OPERATION, method, response.getCode(), response.getMsg()));
            return result;
        }
        result = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String,String>>() {});
        return result;
    }

    /**
     * 批量解密
     * @param content
     * @return
     */
    public static Map<String,String> batchDecrypt(Map<String,String> content){
        Map<String,String> result = Maps.newHashMap();
        if(MapUtils.isEmpty(content)){
            return result;
        }

        String method = "batchDecrypt";
        Object[] form = {ACCOUNT_CODE, new Object[]{content}};
        SoaClient soaClient = SoaClientFactory.create(SoaServer.GO.ENCRYPT);
        SoaResponse response = soaClient.post().call(SERVICE_OPERATION, method, form);
        if (response.fail()) {
            log.error(LoggerUtils.soaRequestFail("go_batchDecrypt", SERVICE_OPERATION, method, response.getCode(), response.getMsg()));
            return result;
        }
        List<Map<String,String>> resData = JsonUtils.stringToObject2(response.getData(), new TypeReference<List<Map<String,String>>>() {});
        if(CollectionUtils.isNotEmpty(resData)){
            return resData.get(0);
        }
        return result;
    }

    /**
     * 获取内容的摘要（类型于对内容进行md5加密）
     * @param content
     * @return
     */
    public static String sm3(String content){
        if (StringUtils.isBlank(content)) {
            return "";
        }
        String method = "sm3";
        //Object[] form = {ACCOUNT_CODE, content};
        SoaClient soaClient = SoaClientFactory.create(SoaServer.GO.ENCRYPT);
        SoaResponse response = soaClient.post().call(SERVICE_OPERATION, method, content);
        if (response.fail()) {
            log.error(LoggerUtils.soaRequestFail("go_sm3", SERVICE_OPERATION, method, response.getCode(), response.getMsg()));
            return "";
        }
        return response.getData();
    }

    /**
     * 批量获取摘要
     * @param content
     * @return
     */
    public static Map<String,String> batchSm3(Map<String,String> content){
        Map<String,String> result = Maps.newHashMap();
        if(MapUtils.isEmpty(content)){
            return result;
        }
        String method = "batchsm3";
        //Object[] form = {ACCOUNT_CODE, content};
        SoaClient soaClient = SoaClientFactory.create(SoaServer.GO.ENCRYPT);
        SoaResponse response = soaClient.post().call(SERVICE_OPERATION, method, content);
        if (response.fail()) {
            log.error(LoggerUtils.soaRequestFail("go_batchsm3", SERVICE_OPERATION, method, response.getCode(), response.getMsg()));
            return result;
        }
        result = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String,String>>() {});
        return result;
    }

}
