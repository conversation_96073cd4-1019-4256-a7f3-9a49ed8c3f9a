package cn.taqu.account.utils.dingding;

import cn.taqu.account.constant.CommConst;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class DingTalkHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DingTalkHandler.class);



    /**
     * push markdown消息
     *
     * @param sendId
     * @param text
     */
    public void pushMarkdownMq(String sendId, String text, String webhookKey, String title, String[] atMobiles) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("service", "asyncrobot");
        map.put("method", "pushMarkdownMsg");
        Map<String, Object> map2 = Maps.newHashMap();
        map2.put("webhook_key", webhookKey);
        map2.put("atDingtalkIds", sendId);
        map2.put("atMobiles", atMobiles);
        map2.put("text", text);
        map2.put("title", title);
        map.put("asyncforms", map2);
        LOGGER.info("pushMarkdownMq{}", JSONObject.toJSONString(map));
        MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.PUSH_ASYNC_INVOKE_QUEUE,map,0L);
        if (mqResponse.fail()) {
            LOGGER.error("推送{}钉钉消息失败，消息内容：{}，失败原因：{}", CommConst.PUSH_ASYNC_INVOKE_QUEUE,JSONObject.toJSONString(map),mqResponse.getMsg());
        }
    }

}




