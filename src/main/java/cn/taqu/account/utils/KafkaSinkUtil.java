package cn.taqu.account.utils;

import cn.taqu.core.utils.LocalConfUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Component
public class KafkaSinkUtil {

    @Autowired
    private KafkaTemplate<String, String> onlineKafkaTemplate;

    @Autowired
    private KafkaTemplate<String, String> onlineBusinessKafkaTemplate;

//    @Async
    public void push(String topicName, Object eventBody) {
        onlineKafkaTemplate.send(topicName, JSON.toJSONString(eventBody));
    }

//    @Async
    public void push(String topicName, String key, Object eventBody) {
        onlineKafkaTemplate.send(topicName, key, JSON.toJSONString(eventBody)).addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
            @Override
            public void onFailure(Throwable throwable) {
                log.warn("topic = {} , key = {}, 推送失败", topicName, key);
            }

            @Override
            public void onSuccess(SendResult<String, String> sendResult) {
                log.info("topic = {} , key = {}, 推送成功", topicName, key);
            }
        });
    }

    public void pushToOnlineBusinessKafka(String topicName, Object eventBody) {
        pushToOnlineBusinessKafka(false, topicName, null, eventBody);
    }

    public void pushToOnlineBusinessKafka(String topicName, String key, Object eventBody) {
        pushToOnlineBusinessKafka(false, topicName, key, eventBody);
    }

    public void pushToOnlineBusinessKafka(boolean grayIsolation, String topicName, Object eventBody) {
        pushToOnlineBusinessKafka(grayIsolation, topicName, null, eventBody);
    }

    public void pushToOnlineBusinessKafka(boolean grayIsolation, String topicName, String key, Object eventBody) {
        String topic = getTopicName(topicName, grayIsolation);
        String body = JSON.toJSONString(eventBody);
        log.info("pushToOnlineBusinessKafka topic:{}, key:{}, eventBody:{}", topic, key, body);
        onlineBusinessKafkaTemplate.send(topic, key, body);
    }

    private static final List<String> GRAY = Arrays.asList("gray", "1");

    private String getTopicName(String topicName, boolean grayIsolation) {

        return grayIsolation && GRAY.contains(LocalConfUtil.getLocalEnv()) ? topicName + "_gray" : topicName;
    }


}
