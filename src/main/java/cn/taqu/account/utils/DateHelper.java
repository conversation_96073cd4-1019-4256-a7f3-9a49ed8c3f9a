package cn.taqu.account.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/5/27
 */
@Slf4j
public class DateHelper {

    public static final Integer ONE_DAY_MILL = ********;
    public static final Integer ONE_DAY_SECOND = 86400;


    private static final String FORMAT = "yyyyMMdd";
    private static final String FORMAT2 = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取每周第一天
     *
     * @return
     */
    public static int getFirstDayOfWeek(LocalDate date) {
        LocalDate first = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        String format = DateTimeFormatter.ofPattern(FORMAT).format(first);
        return Integer.parseInt(format);
    }

    /**
     * 获取每周第一天
     *
     * @return
     */
    public static long getFirstDayOfWeek2(LocalDate date) {
        LocalDate firstDayOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        return firstDayOfWeek.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取上周第一天
     *
     * @return
     */
    public static int getFirstDayOfLastWeek(LocalDate date) {
        LocalDate firstDayOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        long timestamp = firstDayOfWeek.atStartOfDay().toEpochSecond(ZoneOffset.of("+8"));
        return getDt(new Date(timestamp * 1000 - 7 * DateHelper.ONE_DAY_MILL));
    }

    /**
     * 获取每周最后一天
     *
     * @return
     */
    public static int getEndDayOfWeek(LocalDate date) {
        LocalDate end = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        String format = DateTimeFormatter.ofPattern(FORMAT).format(end);
        return Integer.parseInt(format);
    }

    /**
     * 获取每周最后一天
     *
     * @return
     */
    public static long getEndDayOfWeek2(LocalDate date) {
        long startTime = getFirstDayOfWeek2(date);
        return startTime + ONE_DAY_MILL * 7 - 1;
    }

    /**
     * 获取上周的最后一天
     *
     * @return
     */
    public static int getEndDayOfLastWeek(LocalDate date) {
        LocalDate firstDayOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        long timestamp = firstDayOfWeek.atStartOfDay().toEpochSecond(ZoneOffset.of("+8"));
        return getDt(new Date(timestamp * 1000 - DateHelper.ONE_DAY_MILL));
    }

    /**
     * 获取每月第一天
     *
     * @return
     */
    public static int getFirstDayOfMonth(LocalDate date) {
        LocalDate firstDayOfWeek = date.with(TemporalAdjusters.firstDayOfMonth());
        long timestamp = firstDayOfWeek.atStartOfDay().toEpochSecond(ZoneOffset.of("+8"));
        return getDt(new Date(timestamp * 1000));
    }

    /**
     * 获取每月最后一天
     *
     * @return
     */
    public static int getEndDayOfMonth(LocalDate date) {
        LocalDate firstDayOfWeek = date.with(TemporalAdjusters.lastDayOfMonth());
        long timestamp = firstDayOfWeek.atStartOfDay().toEpochSecond(ZoneOffset.of("+8"));
        return getDt(new Date(timestamp * 1000));
    }

    /**
     * 获取今天最后的毫秒值
     *
     * @return
     */
    public static long getStartOfToday(LocalDate date) {
        return LocalDateTime.of(date.getYear(),
                date.getMonth(),
                date.getDayOfMonth(),
                0,
                0,
                0)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取今天最后的毫秒值
     *
     * @return
     */
    public static long getEndOfToday(LocalDate date) {
        return LocalDateTime.of(date.getYear(),
                date.getMonth(),
                date.getDayOfMonth(),
                23,
                59,
                59)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取当前日期
     *
     * @return
     */
    public static Integer getDt(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT);
        return Integer.parseInt(sdf.format(date));
    }

    /**
     * 获取当前日期
     *
     * @return
     */
    public static Integer getDt(LocalDate date) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(FORMAT);
        return Integer.parseInt(dtf.format(date));
    }

    /**
     * 获取当前日期
     *
     * @return
     */
    public static String getDt(LocalDateTime now) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(FORMAT);
        return dtf.format(now);
    }

    /**
     * 获取当前日期
     *
     * @return
     */
    public static Integer getDt(Long milli) {
        LocalDate date = Instant.ofEpochMilli(milli).atZone(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(FORMAT);
        return Integer.parseInt(dtf.format(date));
    }

    /**
     * “20240423”转LocalDate
     *
     * @param dateTimeStr 日期
     * @return
     */
    public static LocalDate parseToDate(String dateTimeStr) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(FORMAT);
        return LocalDate.parse(dateTimeStr, dtf);
    }

    /**
     * “20240423”转LocalDateTime
     *
     * @param dateTimeStr
     * @return 当日的0点0分0秒
     */
    public static LocalDateTime parseToDateTime(String dateTimeStr) {
        LocalDate localDate = parseToDate(dateTimeStr);
        return localDate.atStartOfDay();
    }

    /**
     * “20240423”转LocalDateTime
     *
     * @param dateTimeStr
     * @return 当日的0点0分0秒
     */
    public static LocalDateTime parseToDateTime2(String dateTimeStr) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(FORMAT2);
        return LocalDateTime.parse(dateTimeStr, dtf);
    }


    /**
     * @param dateTimeStr “20240423”转成时间戳
     * @return 当日的0点0分0秒
     */
    public static long parseToTimestamp(String dateTimeStr) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(FORMAT);
        LocalDateTime localDateTime = parseToDateTime(dateTimeStr);
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }


    /**
     * 获取当前日期
     *
     * @return
     */
    public static Integer getDtOfToday() {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(FORMAT);
        return Integer.parseInt(dtf.format(LocalDate.now()));
    }

    private static final String DATE_STRING_SECOND = "yyyy-MM-dd HH:mm:ss";

    public static String milliToStringSecond(Long milli) {
        if (milli == null) {
            return "";
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_STRING_SECOND);
        LocalDateTime localDateTime = milliToLocalDateTime(milli);
        return dateTimeFormatter.format(localDateTime);
    }

    public static LocalDateTime milliToLocalDateTime(Long milli) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(milli), ZoneId.systemDefault());
    }

    /**
     * 多久前
     *
     * @param updateTime
     * @return
     */
    public static String beforeTimeDesc(Long updateTime) {
        long diffMillis = Instant.now().toEpochMilli() - updateTime;

        if (diffMillis < 0) {
            return "错误：当前时间在过去时间之前";
        }

        long diffSeconds = diffMillis / 1000;
        long diffMinutes = diffSeconds / 60;
        long diffHours = diffMinutes / 60;
        long diffDays = diffHours / 24;

        // 构造结果字符串
        if (diffDays > 0) {
            return diffDays + "天前";
        } else if (diffHours > 0) {
            return diffHours + "小时前";
        } else if (diffMinutes > 0) {
            return diffMinutes + "分钟前";
        } else if (diffSeconds > 0) {
            return diffSeconds + "秒前";
        } else {
            return "刚刚";
        }
    }

    /**
     * 格式 yyyy.MM.dd - HH:mm:ss"
     *
     * @param createTime
     * @return
     */
    public static String getString(Long createTime) {
        // 将毫秒时间戳转换为Instant对象
        Instant instant = Instant.ofEpochMilli(createTime);

        // 将Instant对象转换为本地日期时间对象，指定时区为默认时区
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());

        // 指定日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd - HH:mm:ss");

        // 格式化日期时间
        String formattedDateTime = localDateTime.format(formatter);
        return formattedDateTime;
    }

    /**
     * 格式 yyyy.MM.dd - HH:mm:ss"
     *
     * @param localDateTime
     * @return
     */
    public static String format(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH:mm分");
        return localDateTime.format(formatter);
    }

    public static String getMinute(LocalDateTime now) {
        // 指定日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmm");

        // 格式化日期时间
        return now.format(formatter);
    }

    public static String getHour(LocalDateTime now) {
        // 指定日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HH");

        // 格式化日期时间
        return now.format(formatter);
    }

    public static String getMonth(LocalDateTime now) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMM");
        return dtf.format(now);
    }
}
