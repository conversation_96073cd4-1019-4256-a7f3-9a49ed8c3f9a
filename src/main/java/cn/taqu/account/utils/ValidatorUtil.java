package cn.taqu.account.utils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;

/**
 * 对象数据校验
 * 
 * <AUTHOR>
 * 2023年2月22日上午10:51:36
 */
public class ValidatorUtil{

    /**
     * 校验对象数据
     * 
     * @param object
     * @param groups
     * @return
     */
    public static <T> Set<ConstraintViolation<T>> validate(T object, Class<?>... groups){
        return Validation.buildDefaultValidatorFactory().getValidator().validate(object, groups);
    }
    
    /**
     * 校验对象数据，返回异常时文案
     * 
     * @param object
     * @param groups
     * @return
     */
    public static <T> List<String> validateReturnMessage(T object, Class<?>... groups){
        Set<ConstraintViolation<T>> set = Validation.buildDefaultValidatorFactory().getValidator().validate(object, groups);
        return set.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
    }

}
