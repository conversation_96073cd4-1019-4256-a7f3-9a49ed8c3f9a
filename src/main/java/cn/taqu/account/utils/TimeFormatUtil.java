package cn.taqu.account.utils;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

import cn.taqu.core.utils.DateUtil;

public class TimeFormatUtil {

    public static Long formatDateToLong(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return (long) (calendar.getTimeInMillis() / 1000);
    }

    /**
     * 获取当天最开始的时刻
     * @return
     */
    public static Long getCurrentDayInitTime(){
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        return calendar.getTimeInMillis()/1000;
    }

    /**
     * 根据天数获取n天前的初始时刻
     */
    public static Long getInitTimeByDays(Integer days){
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH,-days);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        return calendar.getTimeInMillis()/1000;
    }

    /**
     * 根据天数获取n天前的初始时刻
     */
    public static Date getInitDateByDays(Integer days){
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH,-days);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        return calendar.getTime();
    }

    //获取今天最后时间
    public static Long getDateEndTime(){
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY,23);
        calendar.set(Calendar.MINUTE,59);
        calendar.set(Calendar.SECOND,59);
        return calendar.getTime().getTime()/1000;
    }

    //获取今天剩余的秒数
    public static Long getTodayRemainSec(){
        return getDateEndTime() - System.currentTimeMillis()/1000;
    }

    public static Long getLastTimeThisWeek(){
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.WEEK_OF_YEAR,1);
//        calendar.set(Calendar.DAY_OF_WEEK,1);
//        calendar.set(Calendar.HOUR_OF_DAY, 23);
//        calendar.set(Calendar.SECOND, 59);
//        calendar.set(Calendar.MINUTE, 59);
//        return calendar.getTimeInMillis()/1000;
        // 获取下周一 00:00:00 时间戳 - 1
        return getNextWeekDay(1) - 1;
    }

    //获取本周剩余的秒数
    public static Long getRemainSecThisWeek(){
        return getLastTimeThisWeek() - DateUtil.currentTimeSeconds();
    }

    /**
     * 获取下一个周几的起始 秒级 时间戳
     * index 1 代表下一个周一
     * @param index
     * @return
     */
    public static Long getNextWeekDay(int index){
        LocalDate ld = LocalDate.now().with(TemporalAdjusters.next(DayOfWeek.of(index)));
        return ld.atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli() / 1000;
    }

    /**
     * 获取本月最后一天截止的秒数
     * @return
     */
    public static Long getLastDateOfMonth(){
        LocalDate ld = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        return ld.atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli() / 1000 + 60 * 60 *24;
    }

    /**
     * 获取本月最后一天截止的秒数 + 随机数
     * @return
     */
    public static Long getLastDateOfMonthWithRandom(){
        Long endTime = getLastDateOfMonth();
        if(endTime <= 0){
            endTime = 1L;
        }
        Random random = new Random();
        endTime += random.nextInt(5);
        return endTime;
    }

    /**
     * 获取今日剩余过期的毫秒数，加上随机数 不返回0
     * @return
     */
    public static Long getTodayEndMsTimeStampWithRandom(){
        long endMs = DateUtil.getDateEnd(new Date()).getTime() - System.currentTimeMillis();
        if(endMs <= 0){
            endMs = 1;
        }
        Random random = new Random();
        endMs += random.nextInt(1000) ;
        return endMs;
    }

    /**
     * 获取倒计时时间文本
     *
     * @param smallTime
     * @param bigTime
     * @return
     */
    public static String getCountdownContent(Long smallTime, Long bigTime) {
        long timeDistance = bigTime - smallTime;
        long intHour = (long) Math.floor(timeDistance / 3600);
        timeDistance -= intHour * 3600;
        long intMinute = (long) Math.floor(timeDistance / 60);
        timeDistance -= intMinute * 60;
        long intSecond = (long) Math.floor(timeDistance / 1);
        String strTime = intHour + "时" + intMinute + "分"
                + intSecond + "秒";
        return strTime;

    }

}
