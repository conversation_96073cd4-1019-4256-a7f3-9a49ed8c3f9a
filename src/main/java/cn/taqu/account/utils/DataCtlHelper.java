package cn.taqu.account.utils;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LocalConfUtil;
import cn.taqu.core.utils.ReflectionUtils;

/**
 * <AUTHOR>
 * @date 2025/1/16 下午5:21
 */
public class DataCtlHelper {

    /**
     * 注销
     */
    private static final String TEST_APPROVAL_CODE = "B82F0E50-D998-49D1-9E3E-EB5B80A625D2";

    private static final String ONLINE_CANCEL_APPROVAL_CODE = "72D5B13D-6942-4BD1-9B4E-DBFF2666C1B4";

    private static final String ONLINE_EDIT_APPROVAL_CODE = "09ED1206-4FCA-4F80-BE00-ABE48C58C59A";

    private static final String ONLINE_VIEW_APPROVAL_CODE = "********-91D6-4573-91F3-3CE9130083C0";

    public static String getCancelApprovalCode() {
        String env = LocalConfUtil.getLocalEnv();
        if ("online".equals(env) || "gray".equals(env)) {
            return ONLINE_CANCEL_APPROVAL_CODE;
        }
        return TEST_APPROVAL_CODE;
    }

    public static String getEditApprovalCode() {
        String env = LocalConfUtil.getLocalEnv();
        if ("online".equals(env) || "gray".equals(env)) {
            return ONLINE_EDIT_APPROVAL_CODE;
        }
        return TEST_APPROVAL_CODE;
    }

    public static String getViewApprovalCode() {
        String env = LocalConfUtil.getLocalEnv();
        if ("online".equals(env) || "gray".equals(env)) {
            return ONLINE_VIEW_APPROVAL_CODE;
        }
        return TEST_APPROVAL_CODE;
    }

    public static String extractString(String form, int index) {
        try {
            return JsonUtils.reader().readTree(form).get(index).asText();
        } catch (Exception e) {
            return null;
        }
    }

    public static String extractString(String form, int index, String field) {
        try {
            return JsonUtils.reader().readTree(form).get(index).get(field).textValue();
        } catch (Exception e) {
            return null;
        }
    }

    public static String selfUrl() {
        SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.ACCOUNT);
        return (String) ReflectionUtils.getFieldValue(soaClient, "url");
    }

}
