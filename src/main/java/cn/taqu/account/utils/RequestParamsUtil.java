package cn.taqu.account.utils;

import cn.taqu.account.common.ClonedEnum;
import com.google.common.base.Objects;

import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;

/**
 * RequestParams 相关判断可以写这里
 *
 * <AUTHOR>
 * @date 2020/07/01 09:40
 */
public class RequestParamsUtil {

	private static final Integer PLATFORM_ANDROID = 1;
	private static final Integer PLATFORM_IOS = 2;
	private static final Integer PLATFORM_IPAD = 3;

	/**
	 * 是否是配配App
	 *
	 * <AUTHOR>
	 * @date 2020/07/01 13:46
	 * @return
	 */
	public static boolean isPeipeiApp() {
		Integer cloned = RequestParams.getSoa_basic_java().getCloned();
		if(Objects.equal(cloned, ClonedEnum.PEIPEI.getCode()) || Objects.equal(cloned, ClonedEnum.QIALIAO.getCode())) {
			return true;
		}
		return false;
	}

	/**
	 * 是否是配配ios App
	 *
	 * <AUTHOR>
	 * @date 2020/07/01 13:46
	 * @return
	 */
	public static boolean isPeipeiIosApp() {
		Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
		if(isPeipeiApp() && (Objects.equal(platformId, PLATFORM_IOS) || Objects.equal(platformId, PLATFORM_IPAD))){
			return true;
		}
		return false;
	}

	/**
	 * 是否是安卓app App
	 *
	 * <AUTHOR>
	 * @date 2020/07/01 13:46
	 * @return
	 */
	public static boolean isPeipeiAndroidApp() {
		Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
		if(isPeipeiApp() && Objects.equal(platformId, PLATFORM_ANDROID)){
			return true;
		}
		return false;
	}

	/**
	 * 赋予默认值 appcode、cloned
	 */
	public static void initDefaultAppcodeAndCloned(int appcode, int cloned) {
	    initDefaultAppcode(appcode);
	    initDefaultCloned(cloned);
    }
	
	/**
	 * 赋予默认值 appcode
	 */
	public static void initDefaultAppcode(int appcode) {
	    if(SoaBaseParams.fromThread().getAppcode() == null) {
	        SoaBaseParams.fromThread().setAppcode(String.valueOf(appcode));
	    }
	}
	
	/**
     * 赋予默认值 cloned
     */
	public static void initDefaultCloned(int cloned) {
	    if(SoaBaseParams.fromThread().getCloned() == null) {
            SoaBaseParams.fromThread().setCloned(String.valueOf(cloned));
        }
	}
}
