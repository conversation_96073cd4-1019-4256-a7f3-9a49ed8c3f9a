package cn.taqu.account.utils.mq;

import cn.taqu.account.common.AccountActionTypeEnum;
import cn.taqu.account.common.RegisterLoginTypeEnum;
import cn.taqu.account.dto.AFRefreshMqDataDto;
import cn.taqu.account.dto.AccountActionMqMsg;
import cn.taqu.account.dto.TradingAccountDto;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.DateUtil;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class AntiSpamMqUtil {
    private Logger logger = LoggerFactory.getLogger(AntiSpamMqUtil.class);
    private static final String ACCOUNT_ACTION_MQ = "mq_tqAntiSpam_accountAction";
    private static final String UUID_AF_REFRESH_MQ = "mq_antiSpam_uuidAFRefresh";
    private static final String TRADING_ACCOUNT_MQ = "mq_riskControl_tradingAccount";


    public void pushToAccountActionMq(String accountUuid, String accountName, AccountActionTypeEnum type, RegisterLoginTypeEnum registerLoginTypeEnum) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }

        MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
        AccountActionMqMsg accountActionMqMsg = new AccountActionMqMsg();
        accountActionMqMsg.setAccountUuid(accountUuid);
        accountActionMqMsg.setAccountName(StringUtils.trimToEmpty(accountName));
        accountActionMqMsg.setRegisterLoginType(registerLoginTypeEnum);
        if (type == AccountActionTypeEnum.BLACK || type == AccountActionTypeEnum.UN_BLACK) {
            accountActionMqMsg.setAccountStatus(type);
        } else {
            accountActionMqMsg.setActionType(type);
        }
        //推迟20秒推入队列，保证j24已经完成阿里风险检测和数美风险检测并且已经入库
        MqResponse mqResponse = mqClient.push(ACCOUNT_ACTION_MQ, accountActionMqMsg, 20L);
        if (mqResponse.fail()) {
            logger.error("用户uuid推入反垃圾队列失败，uuid:{}; type:{}; 错误码:{},错误描述:{}", accountUuid, type, mqResponse.getCode(), mqResponse.getMsg());
        }
    }

    @Async
    public void pushToUuidAFRefreshMq(String accountUuid, Integer appcode, Integer cloned, Long appVersion, String ip) {
        AFRefreshMqDataDto dto = new AFRefreshMqDataDto();
        dto.setAccountUuid(accountUuid);
        dto.setAppcode(appcode);
        dto.setCloned(cloned);
        dto.setAppVersion(appVersion);
        dto.setIp(ip);

        MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
        MqResponse mqResponse = mqClient.push(UUID_AF_REFRESH_MQ, dto, null);
        if (mqResponse.fail()) {
            logger.error("推入风控队列失败。队列内容: {}; 响应信息: {}。", new Gson().toJson(dto), new Gson().toJson(mqResponse));
        }
    }

    /**
     * 推送买卖账户mq
     *
     * @param accountUuid           账户uuid
     * @param accountName           帐户昵称
     * @param registerLoginTypeEnum 注册登录类型
     * @param latitude              纬度
     * @param longitude             经度
     * @param token                 token
     * @param ip                    ip
     */
    public void pushToTradingAccountMq(String accountUuid, String accountName, RegisterLoginTypeEnum registerLoginTypeEnum, String latitude, String longitude,
                                       String token, String ip) {
        // 只推送验证码登录的
        if (StringUtils.isBlank(accountUuid) || !registerLoginTypeEnum.equals(RegisterLoginTypeEnum.VERIFICATION_CODE)) {
            return;
        }

        MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
        TradingAccountDto tradingAccountDto = new TradingAccountDto();
        tradingAccountDto.setAccountUuid(accountUuid);
        tradingAccountDto.setAccountName(StringUtils.trimToEmpty(accountName));
        tradingAccountDto.setLatitude(latitude);
        tradingAccountDto.setLongitude(longitude);
        tradingAccountDto.setToken(token);
        tradingAccountDto.setIp(ip);
        tradingAccountDto.setHitTime(DateUtil.currentTimeSeconds());
        MqResponse mqResponse = mqClient.push(TRADING_ACCOUNT_MQ, tradingAccountDto, 300L);
        if (mqResponse.fail()) {
            logger.error("推入买卖账户队列失败，dto:{}; 错误码:{},错误描述:{}", tradingAccountDto, mqResponse.getCode(), mqResponse.getMsg());
        }else {
            logger.info("推入买卖账户队列成功，dto：{}",tradingAccountDto);
        }
    }

}
