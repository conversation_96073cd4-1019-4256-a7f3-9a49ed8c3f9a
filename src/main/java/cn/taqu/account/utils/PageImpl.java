package cn.taqu.account.utils;

import java.io.Serializable;
import java.util.List;

public class PageImpl<T> implements Serializable {
    private static final long serialVersionUID = -8152293981772750966L;
    private List<T> content;
    private long totalElements;
    private long numberOfElements;
    private long totalPages;
    private boolean firstPage;
    private boolean lastPage;
    private long size;
    private long number;
    public List<T> getContent() {
        return content;
    }
    public void setContent(List<T> content) {
        this.content = content;
    }
    public long getTotalElements() {
        return totalElements;
    }
    public void setTotalElements(long totalElements) {
        this.totalElements = totalElements;
    }
    public long getNumberOfElements() {
        return numberOfElements;
    }
    public void setNumberOfElements(long numberOfElements) {
        this.numberOfElements = numberOfElements;
    }
    public long getTotalPages() {
        return totalPages;
    }
    public void setTotalPages(long totalPages) {
        this.totalPages = totalPages;
    }
    public boolean isFirstPage() {
        return firstPage;
    }
    public void setFirstPage(boolean firstPage) {
        this.firstPage = firstPage;
    }
    public boolean isLastPage() {
        return lastPage;
    }
    public void setLastPage(boolean lastPage) {
        this.lastPage = lastPage;
    }
    public long getSize() {
        return size;
    }
    public void setSize(long size) {
        this.size = size;
    }
    public long getNumber() {
        return number;
    }
    public void setNumber(long number) {
        this.number = number;
    }
}
