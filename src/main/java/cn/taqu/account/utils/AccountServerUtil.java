package cn.taqu.account.utils;

import java.util.List;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class AccountServerUtil {

    public static String servicePhone;
    public static List<String> accountAvatarTypeList;


    public static void initConfig(String raw) {
        servicePhone = raw;
        log.info("servicePhone: {}", raw);
    }

    public static void initAccountAvatarType(String raw) {
        log.info("initAccountAvatarType: {}", raw);
        accountAvatarTypeList = JSON.parseObject(raw, List.class);
        log.info("accountAvatarTypeList: {}", JSON.toJSONString(accountAvatarTypeList));

    }


}
