package cn.taqu.account.utils;

import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.StringUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 * @date 2025/4/1 下午6:34
 */
public class SoaHeaderBuilder {

    public static HttpHeaders buildHeadersFromSoaParams() {
        SoaBaseParams soaBasic = SoaBaseParams.fromThread();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headers.add("timestamp", StringUtil.nullNumberToEmptyString(soaBasic.getTimestamp()));
        headers.add("app_version", StringUtil.nullNumberToEmptyString(soaBasic.getApp_version()));
        headers.add("channel", StringUtil.nullToEmptyWithTrim(soaBasic.getChannel()));
        headers.add("appcode", StringUtil.nullNumberToEmptyString(soaBasic.getAppcode()));
        headers.add("alias", StringUtil.nullToEmptyWithTrim(soaBasic.getAlias()));
        headers.add("gender", StringUtil.nullNumberToEmptyString(soaBasic.getGender()));
        headers.add("token", StringUtil.nullToEmptyWithTrim(soaBasic.getToken()));
        headers.add("access", StringUtil.nullToEmptyWithTrim(soaBasic.getAccess()));
        headers.add("platform_name", StringUtil.nullToEmptyWithTrim(soaBasic.getPlatform_name()));
        headers.add("platform_id", StringUtil.nullNumberToEmptyString(soaBasic.getPlatform_id()));
        headers.add("ip", StringUtil.nullToEmptyWithTrim(soaBasic.getIp()));
        headers.add("cloned", StringUtil.nullNumberToEmptyString(soaBasic.getCloned()));
        headers.add("longitude", StringUtil.nullToEmptyWithTrim(soaBasic.getLongitude()));
        headers.add("latitude", StringUtil.nullToEmptyWithTrim(soaBasic.getLatitude()));
        headers.add("log_origin", StringUtil.nullToEmptyWithTrim(soaBasic.getOrigin()));
        headers.add("offset_time", StringUtil.nullNumberToEmptyString(soaBasic.getNextOffsetTimeMills()));
        headers.add("distinctRequestId", StringUtil.nullToEmptyWithTrim(soaBasic.getDistinctRequestId()));
        headers.add("env", StringUtil.nullToEmptyWithTrim(soaBasic.getEnv()));
        headers.add("origin_system", StringUtil.nullToEmptyWithTrim(soaBasic.getOriginSystem()));
        headers.add("ctime", soaBasic.getCtime());
        headers.add("serial_sequence", soaBasic.getSerialSequenceWrapper().getNextSerialSequence());
        headers.add("client_uri", StringUtil.nullToEmptyWithTrim(soaBasic.getClientUri()));
        headers.add("city", StringUtil.nullNumberToEmptyString(soaBasic.getCity()));
        headers.add("city_name", StringUtil.nullToEmptyWithTrim(soaBasic.getCity_name()));
        headers.add("noretry", StringUtil.nullNumberToEmptyString(soaBasic.getNoretry()));
        return headers;
    }
}
