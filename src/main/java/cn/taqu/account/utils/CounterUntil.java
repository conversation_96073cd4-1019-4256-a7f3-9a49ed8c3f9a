package cn.taqu.account.utils;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import cn.taqu.core.protocol.SoaBaseParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;

import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.LocalConfUtil;
import io.prometheus.client.Counter;

/**
 * <AUTHOR> Wu.D.J
 */
@Component
public class CounterUntil {
    
    private static String REDIS_KEY_PREFIX_ACCOUNT = "getInfoByUuid:account:";
    
    public static final Counter COUNTER = Counter.build()
        .name("account_info_cache_counter")
        .labelNames("field")
        .help("Total requests.")
        .register();
    
    public static final Counter COUNTER2 = Counter.build()
        .name("account_info_field")
        .labelNames("field")
        .help("Total requests.")
        .register();
    
    public static final Counter COUNTER3 = Counter.build()
        .name("account_info_size")
        .labelNames("num")
        .help("Total requests.")
        .register();

    public static final Counter CACHE_PENETRATE_COUNTER = Counter.build()
        .name("account_info_cache_penetrate")
        .labelNames("field", "service", "method")
        .help("Total penetrate requests.")
        .register();

    
    @Autowired
    @Qualifier("lockStringRedisTemplate")
    private StringRedisTemplate lockStringRedisTemplate;

    @Async(value = "metricsTaskExecutor")
    public void count( String[] accountUuids, String[] labels, String origin, String originSystem, String clientUri, String method) {
        for (String label : labels) {
            COUNTER.labels(label).inc();
        }
        
        String localEnv = LocalConfUtil.getLocalEnv();
        if(localEnv.startsWith("test") || localEnv.startsWith("dev")) {
            // 2023.11.06 测试环境开始记录
            String redisKey = REDIS_KEY_PREFIX_ACCOUNT + DateUtil.dateToString8(new Date());
            // 记录
            lockStringRedisTemplate.opsForSet().add(redisKey, accountUuids);
            lockStringRedisTemplate.expire(redisKey, 20, TimeUnit.DAYS);
            String label = Joiner.on(",").join(labels);
            label = origin + "-" + originSystem + "-" + clientUri + "-" + label;
            COUNTER2.labels(label).inc();
            
            // 查询个数分组下，不然统计项太多了
            Integer uuidNum = accountUuids.length;
            String uuidNumStr = String.valueOf(uuidNum);
            if(uuidNum > 10 && uuidNum <= 20) {
                uuidNumStr = "11~20";
            }else if(uuidNum > 20 && uuidNum <= 50) {
                uuidNumStr = "21~50";
            }else if(uuidNum > 50 && uuidNum <= 100) {
                uuidNumStr = "51~100";
            }else if(uuidNum > 100 && uuidNum <= 200) {
                uuidNumStr = "101~200";
            }else if(uuidNum > 200 && uuidNum <= 500) {
                uuidNumStr = "201~500";
            }else if(uuidNum > 500 ) {
                uuidNumStr = ">500";
            }
            String num = origin + "-" + originSystem + "-" + clientUri + "-" + method + "-" + uuidNumStr + "-" + labels.length;
            COUNTER3.labels(num).inc();
            
        }
        
    }

    public static void cachePenetrated(String field) {
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        String service = Optional.ofNullable(baseParams.getService()).orElse("-");
        String method = Optional.ofNullable(baseParams.getMethod()).orElse("-");
        try {
            CACHE_PENETRATE_COUNTER.labels(field, service, method).inc();
        } catch (Exception ig) {}
    }



}
