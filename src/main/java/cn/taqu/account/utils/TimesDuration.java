package cn.taqu.account.utils;

import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.Optional;

/**
 * 用来表示诸 "5/1分钟", "10/1小时"之类配置
 * <AUTHOR>  2019/6/10 2:24 PM
 */
@Getter
@ToString
public class TimesDuration {
    private long times;
    private Duration duration;

    private void setTimes(String times) {
        if(StringUtils.isBlank(times) || !StringUtils.isNumeric(times)) {
            throw new IllegalArgumentException("次数 " + times + " 不是一个数字");
        }
        this.times = Long.valueOf(times);
    }

    private void setDuration(String durationWithUnit) {
        int durationEndIndex = 0;
        for(int index=0, len=durationWithUnit.length(); index<len; index++) {
            char c = durationWithUnit.charAt(index);
            if(!(c >= '0' && c <= '9')) {
                durationEndIndex = index;
                break;
            }
        }

        String durationSub = durationWithUnit.substring(0, durationEndIndex);
        if(StringUtils.isBlank(durationSub) || !StringUtils.isNumeric(durationSub)) {
            throw new IllegalArgumentException("时间间隔错误，未设置时间间隔");
        }

        long duration = Long.parseLong(durationSub);
        String unit = durationWithUnit.substring(durationEndIndex).trim();
        switch (unit.toLowerCase()) {
            case "s":
                this.duration = Duration.ofSeconds(duration);
                break;
            case "m":
                this.duration = Duration.ofMinutes(duration);
                break;
            case "h":
                this.duration = Duration.ofHours(duration);
                break;
            case "d":
                this.duration = Duration.ofDays(duration);
                break;
             default:
                 throw new IllegalArgumentException("时间单位 " + unit + " 错误");
        }

    }

    private TimesDuration() {}

    /**
     * 从字符串中解析设置 格式固定为 times/duration(unit)<br/>，单位支持s(秒)、m(分钟)、h(小时)、d(天)单位不区分大小写
     * 例:
     * <ol>
     *     <li>30秒5次</li>: 30/5s
     *     <li>每分钟10次</li>: 10/1m
     *     <li>2小时20次/li>: 20/2h
     *     <li>2天8次/li>: 8/2d
     *     <li></li>
     * </ol>
     * @param value
     * @return
     */
    public static Optional<TimesDuration> of(String value) {
        if(StringUtils.isBlank(value)) {
            return Optional.ofNullable(null);
        }

        int virguleIndex = value.indexOf("/");
        if(virguleIndex <= 0) {
            throw new IllegalArgumentException("参数格式错误，正确格式如: 5/30s");
        }

        String times = value.substring(0, virguleIndex).trim();
        String durationWithUnit = value.substring(virguleIndex + 1).trim();

        TimesDuration instance = new TimesDuration();
        instance.setTimes(times);
        instance.setDuration(durationWithUnit);
        return Optional.of(instance);
    }
}
