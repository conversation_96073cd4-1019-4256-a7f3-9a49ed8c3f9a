package cn.taqu.account.etcd;

import cn.taqu.account.soa.handler.AccountFollowServiceImpl;

import java.util.Properties;

/**
 * 吸引力比例配置
 *
 * <AUTHOR>
 * @date 2024/12/4 下午4:11
 */
@EtcdHookBean(key = "/soa/go/taqu-user-links")
public final class UserLinkEtcdHook implements EtcdHook {

    @Override
    public void onLoad(String value, Properties properties) {
        AccountFollowServiceImpl.URL = value;
    }

    @Override
    public void onRefresh(String value) {
        AccountFollowServiceImpl.URL = value;
    }
}
