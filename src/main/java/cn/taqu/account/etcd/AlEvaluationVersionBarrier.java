package cn.taqu.account.etcd;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Properties;

/**
 * 算法评分版本控制
 * <AUTHOR>
 * @date 2025/5/29 下午6:42
 */
@EtcdHookBean(key = "/algo-eval/version")
public final class AlEvaluationVersionBarrier implements EtcdHook {

    private static Version version;

    @Override
    public void onLoad(String value, Properties properties) {
        if (StringUtils.isNotBlank(value)) {
            version = JsonUtils.stringToObject(value, Version.class);
        } else {
            version = new Version();
        }
    }

    @Override
    public void onRefresh(String value) {
        if (StringUtils.isNotBlank(value)) {
            version = JsonUtils.stringToObject(value, Version.class);
        } else {
            version = new Version();
        }
    }

    public static boolean pass() {
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        if (baseParams.getCloned() == null) {
            return false;
        }
        if (baseParams.getPlatform_id() == null) {
            return false;
        }
        if (baseParams.getApp_version() == null) {
            return false;
        }
        if (ClonedGroupEnum.MAIN_GROUP.contains(baseParams.getCloned())) {
            if (baseParams.getPlatform_id() == 1) {
                return baseParams.getApp_version() >= version.androidMain;
            } else {
                return baseParams.getApp_version() >= version.iosMain;
            }
        } else {
            if (baseParams.getPlatform_id() == 1) {
                return baseParams.getApp_version() >= version.androidGirl;
            } else {
                return baseParams.getApp_version() >= version.iosGirl;
            }
        }
    }


    @Getter
    @Setter
    public static class Version {

        private Integer iosMain = 11340;

        private Integer androidMain = 8391;

        private Integer iosGirl = 999999999;

        private Integer androidGirl = 99999999;
    }
}
