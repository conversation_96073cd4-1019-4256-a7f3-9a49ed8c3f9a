package cn.taqu.account.etcd;

import cn.taqu.account.config.biz.AllureConfig;

import java.util.Properties;

/**
 * 吸引力积分配置
 * <AUTHOR>
 * @date 2024/12/4 下午4:11
 */
@EtcdHookBean(key = "/allure/point")
public final class AllurePointEtcdHook implements EtcdHook {

    @Override
    public void onLoad(String value, Properties properties) {
        AllureConfig.setPointConf(value);
    }

    @Override
    public void onRefresh(String value) {
        AllureConfig.setPointConf(value);
    }
}
