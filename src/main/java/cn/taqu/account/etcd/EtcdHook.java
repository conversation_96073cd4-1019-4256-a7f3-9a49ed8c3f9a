package cn.taqu.account.etcd;

import java.util.Properties;

/**
 * etcd钩子
 * <p>
 *     子类不支持有参构造函数，如果需要注入使用 {@link javax.annotation.Resource} 注解
 * </p>
 * <AUTHOR>
 * @date 2024/12/4 下午4:01
 */
interface EtcdHook {

    /**
     * 初始化
     * @param value
     * @param properties
     */
    void onLoad(String value, Properties properties);

    /**
     * 变更
     * @param value
     */
    void onRefresh(String value);
}
