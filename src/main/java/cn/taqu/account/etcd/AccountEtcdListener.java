package cn.taqu.account.etcd;

import cn.taqu.account.client.FujianGovApiClient;
import cn.taqu.account.client.TengxunyunApiClient;
import cn.taqu.account.client.mp.MPAccountRequest;
import cn.taqu.account.common.ShanLianEnum;
import cn.taqu.account.config.KafkaConfig;
import cn.taqu.account.config.biz.FriendshipPreferLabelConfig;
import cn.taqu.account.config.biz.HomepageBackgroundConfig;
import cn.taqu.account.config.biz.RetrieveAccountConfig;
import cn.taqu.account.constant.CodeStatusLetter;
import cn.taqu.account.service.*;
import cn.taqu.account.special.SpecialAccountHandler;
import cn.taqu.account.thread.ThirdPartFactory;
import cn.taqu.account.utils.AccountDestroyUtil;
import cn.taqu.account.utils.AccountServerUtil;
import cn.taqu.account.utils.LimitModificationUtil;
import cn.taqu.core.etcd.BaseEtcdListener;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import com.justinsb.etcd.EtcdClient;
import com.justinsb.etcd.EtcdResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 *	账号系统etcd监听器
 */
@Slf4j
@Component
public class AccountEtcdListener extends BaseEtcdListener {
	private static final Logger LOGGER = LoggerFactory.getLogger(AccountEtcdListener.class);

	//根目录
	private static String etcdRootDir = "account";
	//数据库key
	private static String dbKey = etcdRootDir + "/db/default";
	//redis key
	private static final String accountRedisKey = etcdRootDir + "/redis/account";
	private static final String accountBizRedisKey = etcdRootDir + "/redis/accountBiz";
	private static final String ticketRedisKey = etcdRootDir + "/redis/ticket";
	private static final String uuidRedisKey = etcdRootDir + "/redis/uuid";
	private static final String privilegeRedisKey = etcdRootDir + "/redis/privilege";
	private static final String levelRedisKey = etcdRootDir + "/redis/level";
	private static final String medalRedisKey = etcdRootDir + "/redis/medal";
	private static final String snRedisKey = etcdRootDir + "/redis/sn";
	private static final String regionRedisKey = etcdRootDir + "/redis/region";
	private static final String kickRedisKey = etcdRootDir + "/redis/kick";
	private static String lockRedisKey = etcdRootDir + "/redis/lock";
	// 注册限制配置 key
	private static String registerLimitConfKey = etcdRootDir + "/antiFraud/registerLimitConf";
	// 再注册限制配置 key
	private static String reRegisterLimitConfKey = etcdRootDir + "/antiFraud/reRegisterLimitConf";
	//weixin key
	private static String weixinKey = etcdRootDir +"/weixin/default";
	//weixin official key
	private static String weixinOfficialKey = etcdRootDir +"/weixinOfficial/default";
	//avatar key
	private static String avatarDefaultKey = etcdRootDir +"/avatar/default";
	//avatar key
	private static String avatarBaseKey = etcdRootDir +"/avatar/base";
	//avatar_repalce key
	private static String avatarRepalceKey = etcdRootDir +"/avatar/replace";
	// 图片审核配置
	private static String photoCheckKey = etcdRootDir + "/photo/check";
	// 替换图片域名配置
	private static String replacePhotoDomainKey = etcdRootDir + "/photo/domain";

	//shiro key
	private static String shiroKey = etcdRootDir +"/shiro/default";
	//登录限制
	private static String limitLoginIpKey = etcdRootDir +"/limit/login/ip";
	private static String limitLoginTokenKey = etcdRootDir +"/limit/login/token";
	private static String limitLoginAccountKey = etcdRootDir +"/limit/login/account";
	/**
	 * 账户登录版本限制
	 */
	private static String accountLoginVersionLimitConf = etcdRootDir + "/limit/login/loginVersionLimitConf";
	private static final String activeDayConf = etcdRootDir + "/limit/login/activeDayConf";
	//阿里云风险防控账号配置key
	private static String aliyunPreventionKey = etcdRootDir + "/aliyun/prevention";
	//阿里云风险识别账号配置key
	private static String aliyunSafKey = etcdRootDir + "/aliyun/saf";
	//是否使用风险识别pro true/false
	private static String useAccountAbuseProKey = etcdRootDir + "/aliyun/useAccountAbusePro";
	//阿里云手机号码归属地查询配置
	private static String aliyunMobilePlaceKey = etcdRootDir + "/aliyun/mobilePlace";
	private static String weV2Key = etcdRootDir + "/we/v2";
	//支付宝认证配置
	private static String alipayCertificationNewKey = etcdRootDir + "/alipay/certificationNew";
	// 支付宝配置
	private static String alipayCertverifyConsultKey = etcdRootDir + "/alipay/certverifyConsult";
	//公用跳转链接配置
	private static String relactionKey = etcdRootDir + "/relaction/config";
	//文案配置
	private static String letterKey = etcdRootDir + "/letter/config";
	private static String zhimaSchemaKey = etcdRootDir + "/zhima/schema";
	private static String driverExcludeRuleKey = etcdRootDir + "/driver/excludeRule";
	private static String chuangLanAndroidKey = etcdRootDir + "/chuanglan/android";
	private static String chuangLanIosKey = etcdRootDir + "/chuanglan/ios";
	// 新增ios闪验新版配置（版本控制，临时配置，之后还是用 chuangLanIosKey）
	private static String chuangLanIosKeyTemp = etcdRootDir + "/chuanglan/iosTemp";
	private static String chuangLanLimitKey = etcdRootDir + "/chuanglan/limit";
	private static String chuanglanFlashVersionKey = etcdRootDir + "/chuanglan/flashVersion";
	//用户昵称库
	private static String nicknameDepotKey = etcdRootDir + "/nickname/depot";
	private static String bruteForceAppVersionsKey = etcdRootDir + "/bruteForce/appVersions";
	// 七牛账号
	private static String qiniuDefaultKey = etcdRootDir + "/qiniu/default";
	// 开启自动检测图片
	private static String qiniuOpenAutoCheckPicKey = etcdRootDir + "/qiniu/openAutoCheckPic";
	//高风险女用户自动款号时间段
	private static String regProhibitHighRiskFemale = etcdRootDir + "/regProhibit/highRisk/female";
	//中风险女用户自动款号时间段
	private static String regProhibitMiddleRiskFemale = etcdRootDir + "/regProhibit/middleRisk/female";
	//获取随机昵称的用户id区间范围
	private static String randomNicknameIdIntervalKey = etcdRootDir + "/randomNicknameIdInterval";
	//获取访问个人主页推送开关
	private static String visitHomepageOverThreeKey = etcdRootDir + "/pushMsg/visitHomepageOverThree";
	//获取关注用户动态推送开关
	private static String postUpdatePushUuidKey = etcdRootDir + "/pushMsg/postUpdatePushUuid";
	//获取附近的用户动态更新推送开关
	private static String accountsPhotoFilterNumKey = etcdRootDir + "/accountsPhotoFilterNum";
	//阿里云活体人脸检测参数
	private static String aliyunFaceDetectConf = etcdRootDir + "/aliyun/faceDetect";
	//语音签名参数
	private static String voiceSignConf = etcdRootDir + "/accountInfo/voiceSign";
	//语音签名文案
	private static String voiceSignCopywritingList = etcdRootDir + "/accountInfo/voiceSignCopywritingList";
	//语音签名文案(区分性别)
	private static String voiceSignCopywritingMap = etcdRootDir + "/accountInfo/voiceSignCopywritingMap";
	//个人简介参数
	private static String personalProfileConf = etcdRootDir + "/accountInfo/personalProfile";
	//个人资料完善度版本配置
	private static String personalDataVersionConf = etcdRootDir + "/accountInfo/dataVersion";
	//星座灵魂契合度信息
	private static String constellationSoulFitInfo = etcdRootDir + "/accountInfo/soulFitInfo";
	//是否启用昵称登录
	private static String isLoginByNameConf = etcdRootDir + "/login/isLoginByName";
	//苹果登录信息
	private static String appleLoginInfo = etcdRootDir + "/login/appleLoginInfo";
	//遇见推送开关
	@Deprecated
	private static String meetPushConf = etcdRootDir + "/pushMsg/meetPushKey";
	//查询时是否启用手机号加密字段
	private static String useEncryptMobileConf = etcdRootDir + "/encrypt/useEncryptMobile";
	//查询支付宝信息是否启用加密字段
	private static String useAlipayEncryptInfoConf = etcdRootDir + "/encrypt/useAlipayEncryptInfo";
	//查询支付宝信息是否启用加密字段
	private static String useCertificationDbConf = etcdRootDir + "/encrypt/useCertificationDb";
	// 短信充值提示短信内容
	private static String smsRechargeTipContent = etcdRootDir + "/sms/rechargeTip/content";
	//用户相册zset数据处理开关
	private static String accountsPhotoZsetConf = etcdRootDir + "/accountInfo/accountsPhotoZset";
	//消息推送开关汇总配置
	private static String pushSwitchConf = etcdRootDir + "/pushMsg/switch";
	private static String uuidLoginLimitConf = etcdRootDir + "/login/uuidLoginLimit";
	//老用户重新活跃-个人信息重新审核开关
	private static String returningUserReviewSwitchConf = etcdRootDir + "/accountInfo/returningUser/reviewSwitch";
	// blackListCertificationConfig版本控制配置
	private static String blackListCertificationConfigConf = etcdRootDir + "/versionControl/blackListCertificationConfig";
	// photoNumConfigConf版本控制配置
	private static String photoNumConfigConf = etcdRootDir + "/versionControl/photoNumConfigConfig";
	// certificationType
	private static String certificationTypeConf = etcdRootDir + "/certification/certificationType";
	// excludeAppidList
	private static String excludeAppidListConf = etcdRootDir + "/certification/excludeAppidList";
	// certificationConf
	private static String certificationConf = etcdRootDir + "/certification/certificationConfig";
	// 交友资料完善引导（引导开关+引导配置）
	private static String friendChecksConf = etcdRootDir + "/accountInfo/friendChecks";
	// 列表资料卡完善引导（标题、描述、展示位置）
	private static String dataCardConf = etcdRootDir + "/accountInfo/dataCard";
	// 审核人员账号(不受一些限制)
	private static String loginAuditAccount = etcdRootDir + "/login/auditAccount";
	// ticket配置
	private static String accountTicketConfig = etcdRootDir + "/ticket/ticketConfig";
	// 个人信息开关
	private static String accountInfoSwitchKey = etcdRootDir + "/accountInfo/switch";
	// 消息提示
	private static String messageBoxTipsKey = etcdRootDir + "/messageBoxTips/conf";
	// 消息提示
	private static String introductionKey = etcdRootDir + "/accountInfo/introduction/conf";
	// 第三方配置
	private static String thirdPartConfigKey = etcdRootDir + "/thirdPart/config";
	// 迭代开关配置
	private static String switch********Key = etcdRootDir + "/switch/2022_02_01";
	// 迭代开关配置
	private static String switch********Key = etcdRootDir + "/switch/2022_03_02";
	//注册后头像违规通知，判断用户appVersion，旧版仍然调用旧接口。新版小秘书，接入推送中台，发安全助手
	private static String switch********Key = etcdRootDir + "/switch/2023_12_01";
	// 私信_Profile优化
	private static String switch********Key = etcdRootDir + "/switch/2024_11_29";
	// 密码登录风控开开关，测试环境关闭，线上打开 https://o15vj1m4ie.feishu.cn/wiki/wikcnF843w96bR1IynIcoXxPfmb
	private static String switchPwdLoginDeviceRiskKey = etcdRootDir + "/switch/pwdLoginDeviceRisk";
	// 账号中台开关
	private static String MP_ACCOUNT_SWITCH = etcdRootDir + "/mpAccount/switch";

	private static String MP_ACCOUNT_SWITCH_2 = etcdRootDir + "/mpAccount/switch_520";

	private static String SL_ACCOUNT_SWITCH = etcdRootDir + "/slAccount/restrictUpdate";

	private static String kafkaServers = etcdRootDir + "/kafka/servers";

	private static String kafkaOnlineBusiness = etcdRootDir + "/kafka/onlineBusiness";

	private static String switchLimitProfileModification = etcdRootDir + "/profile/modifyLimit";
	private static String switchLimitProfileModification2 = etcdRootDir + "/profile/modifyAvatarLimit";

	private static String clonedDefaultAvatar = etcdRootDir + "/avatar/clonedDefault";

	private static String ACCOUNT_DESTORY_FREEZE = etcdRootDir + "/freeze/appmark";
	private static String ACCOUNT_DESTORY_FREEZE_TIP = etcdRootDir + "/freeze/tip";
	private static String ACCOUNT_SERVER_PHONE = etcdRootDir + "/server/phone";

	private static String ACCOUNT_SERVER_AVATAR = etcdRootDir + "/server/avatarType";

	private static String SOA_PHP_CITY_AB_TEST = etcdRootDir + "/soa/php/cityAbTest";

	private static String THIRD_REG_BIND_MOBILE = etcdRootDir + "/thirdReg/config";

	private static String SOA_PHP_PRIVACY = etcdRootDir + "/soa/php/privacy";

	private static String UNBIND_LINK=etcdRootDir + "/unbind/link";

	private static String RISK_APPEAL_LINK = etcdRootDir + "/appeal/link";

	private static String RISK_APPEAL_LINK_GRAY = etcdRootDir + "/appeal/linkGray";

	private static String fujianGovApiConfigKey = etcdRootDir + "/fujianGovApi/config";
	private static String fujianGovApiCheckResidenceInfoKey = etcdRootDir + "/fujianGovApi/checkResidenceInfo";

	private static String uploadAvatarBucketLiveFaceSwitchKey = etcdRootDir + "/switch/uploadAvatarBucketLiveFaceSwitch";

	private static String uploadAvatarBucketCertificationSwitchKey = etcdRootDir + "/switch/uploadAvatarBucketCertificationSwitch";

	private static String useAccountPrivateBucketSwitchKey = etcdRootDir + "/switch/useAccountPrivateBucketSwitch";

	private static String accountsLocationInfoSwitchEncryptionKey = etcdRootDir + "/switch/accountsLocationInfoSwitchEncryption";

	private static String accountsLocationInfoSwitchWriteKey = etcdRootDir + "/switch/accountsLocationInfoSwitchWrite";

	private static String loginActionInfoSwitchEncryptionKey = etcdRootDir + "/switch/loginActionInfoSwitchEncryption";

	private static String loginActionInfoSwitchWriteKey = etcdRootDir + "/switch/loginActionInfoSwitchWrite";

	private static String registerActionInfoSwitchEncryptionKey = etcdRootDir + "/switch/registerActionInfoSwitchEncryption";

	private static String registerActionInfoSwitchWriteKey = etcdRootDir + "/switch/registerActionInfoSwitchWrite";

	private static String logoutActionInfoLogSwitchWriteKey = etcdRootDir + "/switch/logoutActionInfoLogSwitchWrite";

	private static String accountsCertificationChangeLogSwitchEncryptionKey = etcdRootDir + "/switch/accountsCertificationChangeLogSwitchEncryption";

	private static String accountsCertificationChangeLogSwitchWriteKey = etcdRootDir + "/switch/accountsCertificationChangeLogSwitchWrite";

	private static String reregisterWhitelistSwitchEncryptionKey = etcdRootDir + "/switch/reregisterWhitelistSwitchEncryption";

	private static String reregisterWhitelistSwitchWriteKey = etcdRootDir + "/switch/reregisterWhitelistSwitchWrite";

	private static String unbindWhitelistSwitchEncryptionKey = etcdRootDir + "/switch/unbindWhitelistSwitchEncryption";

	private static String unbindWhitelistSwitchWriteKey = etcdRootDir + "/switch/unbindWhitelistSwitchWrite";

	private static String accountsCertificationSwitchEncryptionKey = etcdRootDir + "/switch/accountsCertificationSwitchEncryption";

	private static String accountsCertificationSwitchWriteKey = etcdRootDir + "/switch/accountsCertificationSwitchWrite";

	private static String rewardAccountSwitchEncryptionKey = etcdRootDir + "/switch/rewardAccountSwitchEncryption";

	private static String accountsChatCertificationSwitchEncryptionKey = etcdRootDir + "/switch/accountsChatCertificationSwitchEncryption";

	private static String accountsChatCertificationSwitchWriteKey = etcdRootDir + "/switch/accountsChatCertificationSwitchWrite";

	private static String accountsDestroyLogSwitchEncryptionKey = etcdRootDir + "/switch/accountsDestroyLogSwitchEncryption";

	private static String accountsDestroyLogSwitchWriteKey = etcdRootDir + "/switch/accountsDestroyLogSwitchWrite";

	private static String accountsSwitchEncryptionKey = etcdRootDir + "/switch/accountsSwitchEncryption";

	/**
	 * 手机找回账号
	 */
	private static final String retrieveAccountByMobileKey = etcdRootDir + "/mobile/retrieveAccount";
	/**
     * 我的生活，容器配置
     */
    private static final String accountsLifeConfigKey = etcdRootDir + "/accountsLife/config";
	/**
	 * 交友偏好标签配置
	 */
	private static final String labelFriendshipPreferKey = etcdRootDir + "/label/friendshipPrefer";
	private static final String accountsSwitchWriteKey = etcdRootDir + "/switch/accountsSwitchWrite";
	private static final String loginRegLimitConfigKey = etcdRootDir + "/limit/loginRegLimitConfig";
	/**
	 * 用户主页背景
	 */
	private static final String homepageBackgroundKey = etcdRootDir + "/homepage/background";

	private final Map<String, EtcdHook> hookMap;

	public AccountEtcdListener(DefaultListableBeanFactory beanFactory) {
		this.hookMap = new HashMap<>(16);
		scanHook(beanFactory);
	}

	private void scanHook(DefaultListableBeanFactory beanFactory) {
		Set<Class<?>> loadedHooks = new EtcdHookRegistrar(
			beanFactory,
			(ann, bean) -> hookMap.put(etcdRootDir + ann.key(), bean)
		).register();
		log.info("Registered etcd hooks: {}", loadedHooks);
	}

	@Override
	public Properties doInitProperties(EtcdClient client) {
		Properties properties = new Properties();
		hookMap.forEach((key, hook) -> {
			try {
				String value = getEtcdValue(client, key);
				hook.onLoad(value, properties);
			} catch (Exception e) {
				String msg = "Etcd hook onLoad异常，key =" + key;
				log.error(msg, e);
			}
		});
		try {
			String dbJson = client.get(dbKey).node.value;
			LOGGER.info("etcd获取到数据库配置");
			Properties p = JsonUtils.stringToObject(dbJson, Properties.class);
			properties.setProperty("datasource.primary.url", getProperty(p, "url"));
			properties.setProperty("datasource.primary.username", getProperty(p, "username"));
			properties.setProperty("datasource.primary.password", getProperty(p, "password"));

			String accountRedisKeyJson = client.get(accountRedisKey).node.value;
			LOGGER.info("etcd获取到account redis配置");
			p = JsonUtils.stringToObject(accountRedisKeyJson, Properties.class);
			properties.setProperty("redis.account.host", getProperty(p, "host"));
			properties.setProperty("redis.account.port", getProperty(p, "port"));

			LOGGER.info("etcd获取到account biz redis配置");
			String accountBizRedisKeyJson = client.get(accountBizRedisKey).node.value;
			p = JsonUtils.stringToObject(accountBizRedisKeyJson, Properties.class);
			properties.setProperty("redis.accountBiz.host", getProperty(p, "host"));
			properties.setProperty("redis.accountBiz.port", getProperty(p, "port"));

			String ticketRedisKeyJson = client.get(ticketRedisKey).node.value;
			LOGGER.info("etcd获取到ticket redis配置");
			p = JsonUtils.stringToObject(ticketRedisKeyJson, Properties.class);
			properties.setProperty("redis.ticket.host", getProperty(p, "host"));
			properties.setProperty("redis.ticket.port", getProperty(p, "port"));

			String uuidRedisKeyJson = client.get(uuidRedisKey).node.value;
			LOGGER.info("etcd获取到uuid redis配置");
			p = JsonUtils.stringToObject(uuidRedisKeyJson, Properties.class);
			properties.setProperty("redis.uuid.host", getProperty(p, "host"));
			properties.setProperty("redis.uuid.port", getProperty(p, "port"));

			String privilegeRedisKeyJson = client.get(privilegeRedisKey).node.value;
			LOGGER.info("etcd获取到privilege redis配置");
			p = JsonUtils.stringToObject(privilegeRedisKeyJson, Properties.class);
			properties.setProperty("redis.privilege.host", getProperty(p, "host"));
			properties.setProperty("redis.privilege.port", getProperty(p, "port"));

			String levelRedisKeyJson = client.get(levelRedisKey).node.value;
			LOGGER.info("etcd获取到level redis配置");
			p = JsonUtils.stringToObject(levelRedisKeyJson, Properties.class);
			properties.setProperty("redis.level.host", getProperty(p, "host"));
			properties.setProperty("redis.level.port", getProperty(p, "port"));

			String medalRedisKeyJson = client.get(medalRedisKey).node.value;
			LOGGER.info("etcd获取到medal redis配置");
			p = JsonUtils.stringToObject(medalRedisKeyJson, Properties.class);
			properties.setProperty("redis.medal.host", getProperty(p, "host"));
			properties.setProperty("redis.medal.port", getProperty(p, "port"));

			String snRedisKeyJson = client.get(snRedisKey).node.value;
			p = JsonUtils.stringToObject(snRedisKeyJson, Properties.class);
			properties.setProperty("redis.sn.host", p.getProperty("host"));
			properties.setProperty("redis.sn.port", p.getProperty("port"));

			String regionRedisKeyJson = client.get(regionRedisKey).node.value;
			p = JsonUtils.stringToObject(regionRedisKeyJson, Properties.class);
			properties.setProperty("redis.region.host", p.getProperty("host"));
			properties.setProperty("redis.region.port", p.getProperty("port"));

			String kickRedisKeyJson = client.get(kickRedisKey).node.value;
			p = JsonUtils.stringToObject(kickRedisKeyJson, Properties.class);
			properties.setProperty("redis.kick.host", p.getProperty("host"));
			properties.setProperty("redis.kick.port", p.getProperty("port"));

			String lockRedisKeyJson = client.get(lockRedisKey).node.value;
			p = JsonUtils.stringToObject(lockRedisKeyJson, Properties.class);
			properties.setProperty("redis.lock.host", p.getProperty("host"));
			properties.setProperty("redis.lock.port", p.getProperty("port"));

			String weixinKeyJson = client.get(weixinKey).node.value;
			if(StringUtils.isNoneBlank(weixinKeyJson)) {
                LOGGER.info("{}，可删除",weixinKey);
            }
//			LOGGER.info("etcd获取到weixin配置");
//			p = JsonUtils.stringToObject(weixinKeyJson, Properties.class);
//			WeixinApiClient.baseUri = getProperty(p, "url");
//			AccountsThirdPartyService.baseUri = getProperty(p, "url");
//			AccountsThirdPartyService.weixinAppid = getProperty(p, "appid");
//			AccountsThirdPartyService.weixinSecret = getProperty(p, "secret");
//			AccountsThirdPartyService.weixinGrantType = getProperty(p, "grant_type");

			String weixinOfficialKeyJson = client.get(weixinOfficialKey).node.value;
			if(StringUtils.isNoneBlank(weixinOfficialKeyJson)) {
                LOGGER.info("{}，可删除",weixinOfficialKey);
            }
//			LOGGER.info("etcd获取到weixin official配置");
//			p = JsonUtils.stringToObject(weixinOfficialKeyJson, Properties.class);
//			AccountsThirdPartyService.officialBaseUri = getProperty(p, "url");
//			AccountsThirdPartyService.weixinOfficialAppid = getProperty(p, "appid");
//			AccountsThirdPartyService.weixinOfficialSecret = getProperty(p, "secret");
//			AccountsThirdPartyService.weixinOfficialGrantType = getProperty(p, "grant_type");

			String avatarKeyJson = client.get(avatarDefaultKey).node.value;
			LOGGER.info("etcd获取到avatar配置");
			p = JsonUtils.stringToObject(avatarKeyJson, Properties.class);
			properties.setProperty("default.avatar.baseurl", getProperty(p, "url"));

			String avatarBaseKeyJson = client.get(avatarBaseKey).node.value;
			LOGGER.info("etcd获取到avatar base配置");
			p = JsonUtils.stringToObject(avatarBaseKeyJson, Properties.class);
			AccountsService.baseAvatarUrl = getProperty(p, "url");

			String avatarReplaceKeyJson = client.get(avatarRepalceKey).node.value;
			LOGGER.info("etcd获取到avatar replace配置");
			p = JsonUtils.stringToObject(avatarReplaceKeyJson, Properties.class);
			AccountsInfoService.regReplaceAvatarPart = getProperty(p, "url");
			AccountsService.regReplaceAvatarPart = getProperty(p, "url");
			AccountsPhotoService.regReplaceAvatarPart = getProperty(p, "url");

			String replacePhotoDomainKeyJson = client.get(replacePhotoDomainKey).node.value;
			LOGGER.info("etcd获取到replacePhotoDomain配置");
			AccountsPhotoService.setReplacePhotoDomain(replacePhotoDomainKeyJson);

			String photoCheckKeyJson = client.get(photoCheckKey).node.value;
			LOGGER.info("etcd获取到photo check 配置");
			if (StringUtils.isNotBlank(photoCheckKeyJson)) {
                LOGGER.info("etcd获取到photoCheckKey配置，可删除");
            }
//			AccountsPhotoService.setConf(photoCheckKeyJson);

			String shiroKeyJson = client.get(shiroKey).node.value;
			LOGGER.info("etcd获取到shiro配置");
			p = JsonUtils.stringToObject(shiroKeyJson, Properties.class);
			properties.setProperty("shiro.filterChainDefinitions", getProperty(p, "filterChainDefinitions"));

			String limitLoginIpKey = client.get(AccountEtcdListener.limitLoginIpKey).node.value;
			LOGGER.info("etcd获取到limit login ip配置");
			p = JsonUtils.stringToObject(limitLoginIpKey, Properties.class);
			AccountsService.VERIFY_LOGIN_IP_LIMIT_TIMES = Integer.parseInt(getProperty(p, "times"));
			AccountsService.VERIFY_LOGIN_IP_LIMIT_RANGE = Integer.parseInt(getProperty(p, "range"));

			String limitLoginTokenKey = client.get(AccountEtcdListener.limitLoginTokenKey).node.value;
			LOGGER.info("etcd获取到limit login token配置");
			p = JsonUtils.stringToObject(limitLoginTokenKey, Properties.class);
			AccountsService.VERIFY_LOGIN_TOKEN_LIMIT_TIMES = Integer.parseInt(getProperty(p, "times"));
			AccountsService.VERIFY_LOGIN_TOKEN_LIMIT_RANGE = Integer.parseInt(getProperty(p, "range"));

			String limitLoginAccountKey = client.get(AccountEtcdListener.limitLoginAccountKey).node.value;
			LOGGER.info("etcd获取到limit login account配置");
			p = JsonUtils.stringToObject(limitLoginAccountKey, Properties.class);
			AccountsService.VERIFY_LOGIN_ACCOUNT_LIMIT_TIMES = Integer.parseInt(getProperty(p, "times"));
			AccountsService.VERIFY_LOGIN_ACCOUNT_LIMIT_RANGE = Integer.parseInt(getProperty(p, "range"));

			String accountLoginVersionLimitConfStr = client.get(accountLoginVersionLimitConf).node.value;
			LOGGER.info("获取登录版本控制开关");
			AccountLoginVersionLimitService.setOn(accountLoginVersionLimitConfStr);

			String activeDayStr = client.get(activeDayConf).node.value;
			if(StringUtils.isNoneBlank(activeDayStr)) {
                LOGGER.info("{}，可删除",activeDayConf);
            }
//			LOGGER.info("etcd获取到limit login account（活跃天数配置）");
//			AccountsService.setActiveDayConf(activeDayStr);

			String aliyunPreventionJson = client.get(aliyunPreventionKey).node.value;
			if(StringUtils.isNotBlank(aliyunPreventionJson)) {
			    LOGGER.info("etcd获取到aliyun prevention配置，可删除");
			}

            String aliyunSafJson = client.get(aliyunSafKey).node.value;
            if (StringUtils.isNotBlank(aliyunSafJson)) {
                LOGGER.info("etcd获取到aliyun saf配置，可删除");
            }

			String aliyunMobilePlaceJson = client.get(aliyunMobilePlaceKey).node.value;
			LOGGER.info("etcd获取到aliyun 手机号码归属地查询配置");
			MobilePlaceService.paserConfig(aliyunMobilePlaceJson);

			String weConfigJson = client.get(weV2Key).node.value;
			MeService.parseWeConfig(weConfigJson);

			String alipayCertificationNewJson = client.get(alipayCertificationNewKey).node.value;
			if (StringUtils.isNotBlank(alipayCertificationNewJson)) {
                LOGGER.info("etcd获取到aliyun certification配置，可删除");
            }
//			AccountsCertificationService.parseConfigV3(alipayCertificationNewJson);

			// alipayCertverifyConsultKey
			String alipayCertverifyConsultJsonn = client.get(alipayCertverifyConsultKey).node.value;
			LOGGER.info("etcd获取到新版alipay alipayCertverifyConsultKey");
			AccountsCertificationService.alipayDefaultClient(alipayCertverifyConsultJsonn);

			String relactionJson = client.get(relactionKey).node.value;
			RelactionService.parseRelactionJson(relactionJson);

			String letterJson = client.get(letterKey).node.value;
			CodeStatusLetter.paserLetter(letterJson);

			String schemaJson = client.get(zhimaSchemaKey).node.value;
			if (StringUtils.isNotBlank(schemaJson)) {
                LOGGER.info("etcd获取到zhimaSchemaKey配置，可删除");
            }
//			AccountsCertificationService.initAppSchemaUrlMap(schemaJson);

			String driverExcludeRuleJson = client.get(driverExcludeRuleKey).node.value;
			if (StringUtils.isNotBlank(driverExcludeRuleJson)) {
                LOGGER.info("etcd获取到driverExcludeRuleKey配置，可删除");
            }
//			DriverKilometerRulesService.initAppExcludeRulesType(driverExcludeRuleJson);

			String chuangLanAndroidJson = client.get(chuangLanAndroidKey).node.value;
			if(StringUtils.isNoneBlank(chuangLanAndroidJson)) {
                LOGGER.info("{}，可删除",chuangLanAndroidKey);
            }
//			FlashClient.initAndroidAppKey(chuangLanAndroidJson);

			String chuangLanIosJson = client.get(chuangLanIosKey).node.value;
			if(StringUtils.isNoneBlank(chuangLanIosJson)) {
                LOGGER.info("{}，可删除",chuangLanIosKey);
            }
//			FlashClient.initIosAppKey(chuangLanIosJson);

			String chuangLanIosJsonTemp = client.get(chuangLanIosKeyTemp).node.value;
			if(StringUtils.isNoneBlank(chuangLanIosJsonTemp)) {
                LOGGER.info("{}，可删除",chuangLanIosKeyTemp);
            }
//			FlashClient.initIosAppKeyTemp(chuangLanIosJsonTemp);

			String chuangLanLimitValue = client.get(chuangLanLimitKey).node.value;
			if(StringUtils.isNoneBlank(chuangLanLimitValue)) {
                LOGGER.info("{}，可删除",chuangLanLimitKey);
            }
//			FlashClient.initLimit(chuangLanLimitValue);

			String chuanglanFlashVersion = client.get(chuanglanFlashVersionKey).node.value;
			if(StringUtils.isNoneBlank(chuanglanFlashVersion)) {
                LOGGER.info("{}，可删除",chuanglanFlashVersionKey);
            }

			String nicknameListJson = client.get(nicknameDepotKey).node.value;
			if(StringUtils.isNoneBlank(nicknameListJson)) {
                LOGGER.info("{}，可删除",nicknameDepotKey);
            }
//			AccountsService.initNicknameList(nicknameListJson);

			String bruteForceAppVersions = client.get(bruteForceAppVersionsKey).node.value;
			if(StringUtils.isNoneBlank(bruteForceAppVersions)) {
                LOGGER.info("{}，可删除",bruteForceAppVersionsKey);
            }

			String qiniuDefaultJson = client.get(qiniuDefaultKey).node.value;
			QiniuService.initConfig(qiniuDefaultJson);

			String qiniuOpenAutoCheckPicJson = client.get(qiniuOpenAutoCheckPicKey).node.value;
			if(StringUtils.isNoneBlank(qiniuOpenAutoCheckPicJson)) {
                LOGGER.info("{}，可删除",qiniuOpenAutoCheckPicKey);
            }
			String regProhibitHighRiskFemaleJson = client.get(regProhibitHighRiskFemale).node.value;
			if(StringUtils.isNoneBlank(regProhibitHighRiskFemaleJson)) {
			    LOGGER.info("{}，可删除",regProhibitHighRiskFemale);
			}

			String regProhibitMiddleRiskFemaleJson = client.get(regProhibitMiddleRiskFemale).node.value;
			if(StringUtils.isNoneBlank(regProhibitMiddleRiskFemaleJson)) {
                LOGGER.info("{}，可删除",regProhibitMiddleRiskFemale);
            }

			String useAccountAbuseProValue = client.get(useAccountAbuseProKey).node.value;
	        if(StringUtils.isNoneBlank(useAccountAbuseProValue)) {
	            LOGGER.info("{}，可删除",useAccountAbuseProKey);
	        }
			String randomNicknameIdIntervalValue = client.get(randomNicknameIdIntervalKey).node.value;
			if(StringUtils.isNoneBlank(randomNicknameIdIntervalValue)) {
                LOGGER.info("{}，可删除",randomNicknameIdIntervalKey);
            }
//			AccountsService.setRandomNicknameIdInterval(randomNicknameIdIntervalValue);

			String visitHomepageOverThreeValue = client.get(visitHomepageOverThreeKey).node.value;
			if(StringUtils.isNoneBlank(visitHomepageOverThreeValue)) {
                LOGGER.info("{}，可删除",visitHomepageOverThreeKey);
            }

			String postUpdatePushUuidValue = client.get(postUpdatePushUuidKey).node.value;
			if(StringUtils.isNoneBlank(postUpdatePushUuidValue)) {
                LOGGER.info("{}，可删除",postUpdatePushUuidKey);
            }

			String accountsPhotoFilterNumValue = client.get(accountsPhotoFilterNumKey).node.value;
			AccountsPhotoFilterService.accountsPhotoFilterNum = Integer.parseInt(accountsPhotoFilterNumValue);

			String aliyunFaceDetectConfValue = client.get(aliyunFaceDetectConf).node.value;
			AliyunLiveFaceDetectService.initConf(aliyunFaceDetectConfValue);

			String voiceSignConfValue = client.get(voiceSignConf).node.value;
			LOGGER.info("etcd获取到语音签名配置配置");
			VoiceSignInfoService.initConf(voiceSignConfValue);

			String voiceSignCopywritingListValue = client.get(voiceSignCopywritingList).node.value;
			LOGGER.info("etc获取语音签名文案");
			VoiceSignInfoService.initCopyWritingList(voiceSignCopywritingListValue);

			String voiceSignCopywritingMapValue = client.get(voiceSignCopywritingMap).node.value;
			LOGGER.info("etc获取语音签名文案(性别)");
			VoiceSignInfoService.initCopyWritingMap(voiceSignCopywritingMapValue);

			String personalProfileConfValue = client.get(personalProfileConf).node.value;
			LOGGER.info("etcd获取到个人简介配置配置");
			PersonalProfileInfoService.initConf(personalProfileConfValue);

			String personalDataVersionConfValue = client.get(personalDataVersionConf).node.value;
			LOGGER.info("etcd获取到个人资料完善度版本配置");
			AccountsInfoService.setDataVersion(personalDataVersionConfValue);

			String constellationSoulFitInfoValue = client.get(constellationSoulFitInfo).node.value;
			LOGGER.info("etcd获取灵魂契合度信息");
			AccountsInfoService.setSoulFitInfo(constellationSoulFitInfoValue);

			String isLoginByNameValue = client.get(isLoginByNameConf).node.value;
			LOGGER.info("etcd获取是否根据昵称登录开关");
			AccountsService.setNicknameLoginConf(isLoginByNameValue);

//			String appleLoginInfoValue = client.get(appleLoginInfo).node.value;
//			LOGGER.info("etcd获取苹果登录配置信息");
//			AccountsThirdPartyService.setAppleLoginInfo(appleLoginInfoValue);

			String meetPushValue = client.get(meetPushConf).node.value;
			if(StringUtils.isNoneBlank(meetPushValue)) {
                LOGGER.info("etcd获取遇见推送配置信息，可删除");
            }
			String useEncryptMobileValue = client.get(useEncryptMobileConf).node.value;
//			LOGGER.info("etcd获取查询时是否使用加密的手机号码配置信息");
			if(StringUtils.isNoneBlank(useEncryptMobileValue)) {
                LOGGER.info("{}，可删除",useEncryptMobileConf);
            }
//			MobileService.setPushConf(useEncryptMobileValue);

			String useAlipayEncryptInfoValue = client.get(useAlipayEncryptInfoConf).node.value;
//			LOGGER.info("etcd获取查询时是否使用加密的支付宝配置信息");
			if(StringUtils.isNoneBlank(useAlipayEncryptInfoValue)) {
                LOGGER.info("{}，可删除",useAlipayEncryptInfoConf);
            }
//			AccountsCertificationService.initEncryptConf(useAlipayEncryptInfoValue);

			String useCertificationDbConfValue = client.get(useCertificationDbConf).node.value;
			LOGGER.info("etcd获取认证信息是否直接查库配置信息");
			if(StringUtils.isNoneBlank(useCertificationDbConfValue)) {
                LOGGER.info("etcd获取认证信息是否直接查库配置信息，可删除");
            }

			String smsRechargeTipContentValue = client.get(smsRechargeTipContent).node.value;
			LOGGER.info("etcd获取短信充值提示短信内容");
			if(StringUtils.isNoneBlank(smsRechargeTipContentValue)) {
                LOGGER.info("etcd获取短信充值提示短信内容，可删除");
            }

			String accountsPhotoZsetConfValue = client.get(accountsPhotoZsetConf).node.value;
			if(StringUtils.isNoneBlank(accountsPhotoZsetConfValue)) {
			    LOGGER.info("etcd获取相册zset开关配置，可删除");
			}
//			UpdatePhotoToZsetTask.initConf(accountsPhotoZsetConfValue);

			String pushSwitchConfValue = client.get(pushSwitchConf).node.value;
			if(StringUtils.isNoneBlank(pushSwitchConfValue)) {
                LOGGER.info("etcd获取推送开关汇总配置，可删除");
            }
//			PushSwitchService.initSwitch(pushSwitchConfValue);

			String uuidLoginLimitConfValue = client.get(uuidLoginLimitConf).node.value;
			LOGGER.info("etcd获取uuid登录限制配置");
			AccountsService.initUuidLimitConf(uuidLoginLimitConfValue);

			String returningUserReviewSwitchJson = client.get(returningUserReviewSwitchConf).node.value;
			LOGGER.info("etcd获取到[老用户重新活跃-个人信息重新审核开关]配置");
			p = JsonUtils.stringToObject(returningUserReviewSwitchJson, Properties.class);
			AccountsMemberInfoService.NICK_NAME_REVIEW = Integer.parseInt(getProperty(p, "nick_name"));
			AccountsMemberInfoService.AVATAR_REVIEW = Integer.parseInt(getProperty(p, "avatar"));
			AccountsMemberInfoService.PERSONAL_INFO_REVIEW = Integer.parseInt(getProperty(p, "personal_info"));

			String blackListCertificationConfigValue = client.get(blackListCertificationConfigConf).node.value;
			LOGGER.info("etcd获取blackListCertificationConfig版本控制配置");
			VersionControlService.setBlackListCertificationConfig(blackListCertificationConfigValue);

			String photoNumConfigValue = client.get(photoNumConfigConf).node.value;
			LOGGER.info("etcd获取photoNumConfig版本控制配置");
			VersionControlService.setPhotoNumConfig(photoNumConfigValue);

			String certificationTypeValue = client.get(certificationTypeConf).node.value;
//			LOGGER.info("etcd获取certificationType版本控制配置");
			if(StringUtils.isNoneBlank(certificationTypeValue)) {
                LOGGER.info("etcd获取certificationType版本控制配置，可删除");
            }
//			AccountsCertificationService.CERTIFICATION_TYPE = certificationTypeValue;

			EtcdResult etcdResult = client.get(excludeAppidListConf);
			if(etcdResult != null && etcdResult.node != null) {
			    TengxunyunApiClient.updateExcludeAppidList( etcdResult.node.value);
			}

			String certificationConfValue = client.get(certificationConf).node.value;
			LOGGER.info("etcd获取certificationConf配置");
			AccountsCertificationService.initCertificationConf(certificationConfValue);

			String registerLimitConfValue = client.get(registerLimitConfKey).node.value;
			LOGGER.info("etcd获取registerLimitConfValue注册限制配置");
			RegisterLimitService.setConf(registerLimitConfValue);

			String reRegisterLimitConfValue = client.get(reRegisterLimitConfKey).node.value;
			LOGGER.info("etcd获取reRegisterLimitConfKey再注册限制配置");
			ReRegisterLimitService.setConf(reRegisterLimitConfValue);

			String friendChecksConfValue = client.get(friendChecksConf).node.value;
			LOGGER.info("etcd获取friendChecksConf交友资料完善引导（引导开关+引导配置）");
			AccountsInfoService.setFriendChecks(friendChecksConfValue);

			String dataCardConfValue = client.get(dataCardConf).node.value;
			LOGGER.info("etcd获取dataCardConf列表资料卡完善引导（标题、描述、展示位置）");
			AccountsInfoService.setDataCard(dataCardConfValue);

			String loginAuditAccountValue = client.get(loginAuditAccount).node.value;
			LOGGER.info("etcd获取审核人员配置");
			SpecialAccountHandler.updateAuditAccountList(loginAuditAccountValue);

			String accountTicketValue = client.get(accountTicketConfig).node.value;
			LOGGER.info("etcd获取ticket过期时间配置");
			AccountsService.initAccountTicketConfig(accountTicketValue);

			String accountInfoSwitchValue = client.get(accountInfoSwitchKey).node.value;
			LOGGER.info("etcd获取accountInfos开关配置");
			AccountsInfoService.initSwitchOfAccounts(accountInfoSwitchValue);

			String messageBoxTipsValue = client.get(messageBoxTipsKey).node.value;
			LOGGER.info("etcd获取amessageBoxTips开关配置");
			AccountsInfoService.initMessageBoxTips(messageBoxTipsValue);

			// introductionKey
			String introductionValue = client.get(introductionKey).node.value;
			LOGGER.info("etcd获取introduction配置");
			AccountsIntroductionService.initConf(introductionValue);

			// thirdPartConfigKey
			String thirdPartConfigValue = client.get(thirdPartConfigKey).node.value;
			LOGGER.info("etcd获取thirdValue配置");
			ThirdPartFactory.initConfig(thirdPartConfigValue);

			// switch********Key
			String switch********Value = client.get(switch********Key).node.value;
			LOGGER.info("etcd获取********开关配置");
//			VersionSwitchService.initSwitch********(switch********Value);
			if(StringUtils.isNoneBlank(switch********Value)) {
                LOGGER.info("etcd获取switch********Key版本控制配置，可删除");
            }

			// switch********Key
			String switch********Value = client.get(switch********Key).node.value;
			LOGGER.info("etcd获取********开关配置");
			VersionSwitchService.initSwitch********(switch********Value);

			//switch********Key
			String switch********Value = client.get(switch********Key).node.value;
			LOGGER.info("etcd获取********开关配置");
			VersionSwitchService.initSwitch********(switch********Value);

			//switch********Key
			String switch********Value = client.get(switch********Key).node.value;
			LOGGER.info("etcd获取********开关配置");
			VersionSwitchService.initSwitch********(switch********Value);

			// MP_ACCOUNT_SWITCH
			String mpAccountSwitch = client.get(MP_ACCOUNT_SWITCH).node.value;
			LOGGER.info("etcd获取账号中台开关配置");
			MPAccountRequest.initConfig(mpAccountSwitch);

			// mp account switch v520
			String mpAccountSwitch2 = client.get(MP_ACCOUNT_SWITCH_2).node.value;
			LOGGER.info("etcd获取账号中台开关配置2");
			MPAccountRequest.initSwitchConfig(mpAccountSwitch2);

			String slAccount = client.get(SL_ACCOUNT_SWITCH).node.value;
			LOGGER.info("etcd获取闪恋强制升级开关配置");
			ShanLianEnum.initUpdateConfig(slAccount);
			MPAccountRequest.initSwitchConfig(mpAccountSwitch2);

			String kafkaServer = client.get(kafkaServers).node.value;
			LOGGER.info("etcd获取kafka连接配置");
			KafkaConfig.onlineKafkaServer = kafkaServer;

			String onlineBusiness = client.get(kafkaOnlineBusiness).node.value;
			LOGGER.info("etcd获取kafka连接配置");
			KafkaConfig.onlineBusinessServer = onlineBusiness;

			String profileLimit = client.get(switchLimitProfileModification).node.value;
			LOGGER.info("etcd获取资料修改限制配置");
			LimitModificationUtil.initConfig(profileLimit);

			String avatarProfileLimit = client.get(switchLimitProfileModification2).node.value;
			LOGGER.info("etcd获取头像资料修改限制配置");
			LimitModificationUtil.initAvatarConfig(avatarProfileLimit);

			String cdAvatar = client.get(clonedDefaultAvatar).node.value;
			CloneDefaultAvatarService.initAvatar(cdAvatar);

			String accountDestroyFreeze = client.get(ACCOUNT_DESTORY_FREEZE).node.value;
			AccountDestroyUtil.initConfig(accountDestroyFreeze);

			String accountDestroyFreezeTip = client.get(ACCOUNT_DESTORY_FREEZE_TIP).node.value;
			AccountDestroyUtil.initDestroyTipsConfig(accountDestroyFreezeTip);

			String accountServerPhone = client.get(ACCOUNT_SERVER_PHONE).node.value;
			AccountServerUtil.initConfig(accountServerPhone);

			String accountServerAvatarType = client.get(ACCOUNT_SERVER_AVATAR).node.value;
			AccountServerUtil.initAccountAvatarType(accountServerAvatarType);

			String soaPhpCityAbTest = client.get(SOA_PHP_CITY_AB_TEST).node.value;
			CityAbTestService.REQ_URL = soaPhpCityAbTest;

			String thirdRegBindMobile = client.get(THIRD_REG_BIND_MOBILE).node.value;
			AccountsThirdPartyService.initRegConfig(thirdRegBindMobile);

			String switchPwdLoginDeviceRiskValue = client.get(switchPwdLoginDeviceRiskKey).node.value;
			AntiSpamService.SWITCH_PWD_LOGIN_DEVICE_RISK = Integer.parseInt(switchPwdLoginDeviceRiskValue);

			String soaPhpPrivacy = client.get(SOA_PHP_PRIVACY).node.value;
			PrivacyService.REQ_URL = soaPhpPrivacy;

			String umbindLinkConf = client.get(UNBIND_LINK).node.value;
			UnbindWhitelistService.initConfig(umbindLinkConf);

			String riskAppealLink = client.get(RISK_APPEAL_LINK).node.value;
			AccountsService.RISK_APPEAL_LINK = riskAppealLink;

			String riskAppealLinkGray = client.get(RISK_APPEAL_LINK_GRAY).node.value;
			AccountsService.RISK_APPEAL_LINK_GRAY = riskAppealLinkGray;

			String fujianGovApiConfigValue = client.get(fujianGovApiConfigKey).node.value;
			FujianGovApiClient.initConfig(fujianGovApiConfigValue);

			String fujianGovApiCheckResidenceInfoValue = client.get(fujianGovApiCheckResidenceInfoKey).node.value;
			FujianGovApiClient.initCheckResidenceInfoConfig(fujianGovApiCheckResidenceInfoValue);

			String retrieveAccountByMobileValue = client.get(retrieveAccountByMobileKey).node.value;
			RetrieveAccountConfig.setRetrieveConfig(retrieveAccountByMobileValue);

			String accountsLifeConfigValue = client.get(accountsLifeConfigKey).node.value;
			AccountsLifeService.setConfig(accountsLifeConfigValue);

			String friendshipPreferLabelValue = client.get(labelFriendshipPreferKey).node.value;
			FriendshipPreferLabelConfig.setLabelConfig(friendshipPreferLabelValue);

			String uploadAvatarBucketLiveFaceSwitchValue = client.get(uploadAvatarBucketLiveFaceSwitchKey).node.value;
			ToolsService.initUploadAvatarBucketLiveFaceSwitch(uploadAvatarBucketLiveFaceSwitchValue);

			String uploadAvatarBucketCertificationSwitchValue = client.get(uploadAvatarBucketCertificationSwitchKey).node.value;
			ToolsService.initUploadAvatarBucketCertificationSwitch(uploadAvatarBucketCertificationSwitchValue);

			String useAccountPrivateBucketSwitchValue = client.get(useAccountPrivateBucketSwitchKey).node.value;
			ToolsService.initUseAccountPrivateBucketSwitch(useAccountPrivateBucketSwitchValue);

			String accountsLocationInfoSwitchEncryptionValue = client.get(accountsLocationInfoSwitchEncryptionKey).node.value;
			ToolsService.accountsLocationInfoSwitchEncryption(accountsLocationInfoSwitchEncryptionValue);

			String accountsLocationInfoSwitchWriteValue = client.get(accountsLocationInfoSwitchWriteKey).node.value;
			ToolsService.accountsLocationInfoSwitchWrite(accountsLocationInfoSwitchWriteValue);

			String loginActionInfoSwitchEncryptionValue = client.get(loginActionInfoSwitchEncryptionKey).node.value;
			ToolsService.loginActionInfoSwitchEncryption(loginActionInfoSwitchEncryptionValue);

			String loginActionInfoSwitchWriteValue = client.get(loginActionInfoSwitchWriteKey).node.value;
			ToolsService.loginActionInfoSwitchWrite(loginActionInfoSwitchWriteValue);

			String registerActionInfoSwitchEncryptionValue = client.get(registerActionInfoSwitchEncryptionKey).node.value;
			ToolsService.registerActionInfoSwitchEncryption(registerActionInfoSwitchEncryptionValue);

			String registerActionInfoSwitchWriteValue = client.get(registerActionInfoSwitchWriteKey).node.value;
			ToolsService.registerActionInfoSwitchWrite(registerActionInfoSwitchWriteValue);

			String logoutActionInfoLogSwitchWriteValue = client.get(logoutActionInfoLogSwitchWriteKey).node.value;
			ToolsService.logoutActionInfoLogSwitchWrite(logoutActionInfoLogSwitchWriteValue);

			String accountsCertificationChangeLogSwitchEncryptionValue = client.get(accountsCertificationChangeLogSwitchEncryptionKey).node.value;
			ToolsService.accountsCertificationChangeLogSwitchEncryption(accountsCertificationChangeLogSwitchEncryptionValue);

			String accountsCertificationChangeLogSwitchWriteValue = client.get(accountsCertificationChangeLogSwitchWriteKey).node.value;
			ToolsService.accountsCertificationChangeLogSwitchWrite(accountsCertificationChangeLogSwitchWriteValue);

			String reregisterWhitelistSwitchEncryptionValue = client.get(reregisterWhitelistSwitchEncryptionKey).node.value;
			ToolsService.reregisterWhitelistSwitchEncryption(reregisterWhitelistSwitchEncryptionValue);

			String reregisterWhitelistSwitchWriteValue = client.get(reregisterWhitelistSwitchWriteKey).node.value;
			ToolsService.reregisterWhitelistSwitchWrite(reregisterWhitelistSwitchWriteValue);

			String unbindWhitelistSwitchEncryptionValue = client.get(unbindWhitelistSwitchEncryptionKey).node.value;
			ToolsService.unbindWhitelistSwitchEncryption(unbindWhitelistSwitchEncryptionValue);

			String unbindWhitelistSwitchWriteValue = client.get(unbindWhitelistSwitchWriteKey).node.value;
			ToolsService.unbindWhitelistSwitchWrite(unbindWhitelistSwitchWriteValue);

			String accountsCertificationSwitchEncryptionValue = client.get(accountsCertificationSwitchEncryptionKey).node.value;
			ToolsService.accountsCertificationSwitchEncryption(accountsCertificationSwitchEncryptionValue);

			String accountsCertificationSwitchWriteValue = client.get(accountsCertificationSwitchWriteKey).node.value;
			ToolsService.accountsCertificationSwitchWrite(accountsCertificationSwitchWriteValue);

			String rewardAccountSwitchEncryptionValue = client.get(rewardAccountSwitchEncryptionKey).node.value;
			ToolsService.rewardAccountSwitchEncryption(rewardAccountSwitchEncryptionValue);

			String accountsChatCertificationSwitchEncryptionValue = client.get(accountsChatCertificationSwitchEncryptionKey).node.value;
			ToolsService.accountsChatCertificationSwitchEncryption(accountsChatCertificationSwitchEncryptionValue);

			String accountsChatCertificationSwitchWriteValue = client.get(accountsChatCertificationSwitchWriteKey).node.value;
			ToolsService.accountsChatCertificationSwitchWrite(accountsChatCertificationSwitchWriteValue);

			String accountsDestroyLogSwitchEncryptionValue = client.get(accountsDestroyLogSwitchEncryptionKey).node.value;
			ToolsService.accountsDestroyLogSwitchEncryption(accountsDestroyLogSwitchEncryptionValue);

			String accountsDestroyLogSwitchWriteValue = client.get(accountsDestroyLogSwitchWriteKey).node.value;
			ToolsService.accountsDestroyLogSwitchWrite(accountsDestroyLogSwitchWriteValue);

			String accountsSwitchEncryptionValue = client.get(accountsSwitchEncryptionKey).node.value;
			ToolsService.accountsSwitchEncryption(accountsSwitchEncryptionValue);

			String accountsSwitchWriteValue = client.get(accountsSwitchWriteKey).node.value;
			ToolsService.accountsSwitchWrite(accountsSwitchWriteValue);

			String loginRegLimitConfigValue = client.get(loginRegLimitConfigKey).node.value;
			LoginRegLimitService.setConf(loginRegLimitConfigValue);

			String bgValue = getEtcdValue(client, homepageBackgroundKey);
			HomepageBackgroundConfig.setConf(bgValue);

		} catch (Exception e) {
			LOGGER.error("加载etcd配置失败", e);
		}

		return properties;
	}

	@Override
	public void doListen(String key, String value) {
		LOGGER.info("监听到etcd变化, key={}", key);
		key = key.startsWith("/")?key.substring(1):key;
		EtcdHook hook = hookMap.get(key);
		if (hook != null) {
			hook.onRefresh(value);
			return;
		}

		//根据etcd变动的key做相应的修改
		if (dbKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeDataSource(getProperty(p, "url"), getProperty(p, "username"), getProperty(p, "password"));
		} else if (accountRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("accountStringRedisTemplate", getProperty(p, "host"), getProperty(p, "port"));
		} else if (accountBizRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("accountBizStringRedisTemplate", getProperty(p, "host"), getProperty(p, "port"));
		} else if (ticketRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("ticketStringRedisTemplate", getProperty(p, "host"), getProperty(p, "port"));
		} else if (uuidRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("uuidStringRedisTemplate", getProperty(p, "host"), getProperty(p, "port"));
		} else if (privilegeRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("privilegeStringRedisTemplate", getProperty(p, "host"), getProperty(p, "port"));
		} else if (levelRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("levelStringRedisTemplate", getProperty(p, "host"), getProperty(p, "port"));
		} else if (activeDayConf.endsWith(key)) {
//			AccountsService.setActiveDayConf(value);
		} else if (medalRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("medalStringRedisTemplate", getProperty(p, "host"), getProperty(p, "port"));
		} else if (snRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("snStringRedisTemplate", p.getProperty("host"), p.getProperty("port"));
		} else if(regionRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("regionStringRedisTemplate", p.getProperty("host"), p.getProperty("port"));
		} else if(kickRedisKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("kickStringRedisTemplate", p.getProperty("host"), p.getProperty("port"));
		} else if(lockRedisKey.equals(key)){
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			this.changeRedis("lockStringRedisTemplate", p.getProperty("host"), p.getProperty("port"));
		}else if(weixinKey.equals(key)) {
//			Properties p = JsonUtils.stringToObject(value, Properties.class);
			//WeixinApiClient weixinApiClient = SpringContextHolder.getBean(WeixinApiClient.class);
//			WeixinApiClient.baseUri = getProperty(p, "url");
//
//			AccountsThirdPartyService.baseUri = getProperty(p, "url");
//			AccountsThirdPartyService.weixinAppid = getProperty(p, "appid");
//			AccountsThirdPartyService.weixinSecret = getProperty(p, "secret");
//			AccountsThirdPartyService.weixinGrantType = getProperty(p, "grant_type");
		} else if(weixinOfficialKey.equals(key)) {
//			Properties p = JsonUtils.stringToObject(value, Properties.class);
//			AccountsThirdPartyService.officialBaseUri = getProperty(p, "url");
//			AccountsThirdPartyService.weixinOfficialAppid = getProperty(p, "appid");
//			AccountsThirdPartyService.weixinOfficialSecret = getProperty(p, "secret");
//			AccountsThirdPartyService.weixinOfficialGrantType = getProperty(p, "grant_type");
		} else if(avatarDefaultKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			AccountsService accountsService = SpringContextHolder.getBean(AccountsService.class);
			accountsService.defaultAvatarBaseurl = getProperty(p, "url");
		} else if(avatarRepalceKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			AccountsInfoService.regReplaceAvatarPart = getProperty(p, "url");
			AccountsService.regReplaceAvatarPart = getProperty(p, "url");
			AccountsPhotoService.regReplaceAvatarPart = getProperty(p, "url");
		} else if(avatarBaseKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			AccountsService.baseAvatarUrl = getProperty(p, "url");
		} else if(weV2Key.equals(key)) {
			MeService.parseWeConfig(value);
		} else if(shiroKey.equals(key)) {
//			Properties p = JsonUtils.stringToObject(value, Properties.class);
			//this.changeShiroFilterChains(getProperty(p, "filterChainDefinitions"));
		} else if(limitLoginIpKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			String times = getProperty(p, "times");
			String range = getProperty(p, "range");
			if(StringUtils.isNotBlank(times)){
				try {
					AccountsService.VERIFY_LOGIN_IP_LIMIT_TIMES = Integer.parseInt(times);
				} catch (Exception e) {
					LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value, e);
				}
			}else{
				LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value);
			}
			if(StringUtils.isNotBlank(range)){
				try {
					AccountsService.VERIFY_LOGIN_IP_LIMIT_RANGE = Integer.parseInt(range);
				} catch (Exception e) {
					LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value, e);
				}
			}else{
				LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value);
			}
		} else if(limitLoginTokenKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			String times = getProperty(p, "times");
			String range = getProperty(p, "range");
			if(StringUtils.isNotBlank(times)){
				try {
					AccountsService.VERIFY_LOGIN_TOKEN_LIMIT_TIMES = Integer.parseInt(times);
				} catch (Exception e) {
					LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value, e);
				}
			}else{
				LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value);
			}
			if(StringUtils.isNotBlank(range)){
				try {
					AccountsService.VERIFY_LOGIN_TOKEN_LIMIT_RANGE = Integer.parseInt(range);
				} catch (Exception e) {
					LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value, e);
				}
			}else{
				LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value);
			}
		} else if(limitLoginAccountKey.equals(key)) {
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			String times = getProperty(p, "times");
			String range = getProperty(p, "range");
			if(StringUtils.isNotBlank(times)){
				try {
					AccountsService.VERIFY_LOGIN_ACCOUNT_LIMIT_TIMES = Integer.parseInt(times);
				} catch (Exception e) {
					LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value, e);
				}
			}else{
				LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value);
			}
			if(StringUtils.isNotBlank(range)){
				try {
					AccountsService.VERIFY_LOGIN_ACCOUNT_LIMIT_RANGE = Integer.parseInt(range);
				} catch (Exception e) {
					LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value, e);
				}
			}else{
				LOGGER.error("监听到错误的etcd设置 key, key={},value={}", key,value);
			}
		} else if(aliyunMobilePlaceKey.equals(key)) {
			MobilePlaceService.paserConfig(value);
		}else if(alipayCertificationNewKey.equals(key)) {
//			AccountsCertificationService.parseConfigV3(value);
		} else if(alipayCertverifyConsultKey.equals(key)){
			AccountsCertificationService.alipayDefaultClient(value);
		} else if(zhimaSchemaKey.equals(key)) {
//			AccountsCertificationService.initAppSchemaUrlMap(value);
		} else if(relactionKey.equalsIgnoreCase(key)) {
			RelactionService.parseRelactionJson(value);
		} else if(letterKey.equalsIgnoreCase(key)) {
			CodeStatusLetter.paserLetter(value);
		} else if(driverExcludeRuleKey.equalsIgnoreCase(key)) {
//			DriverKilometerRulesService.initAppExcludeRulesType(value);
		} else if(chuangLanAndroidKey.equals(key)) {
//			FlashClient.initAndroidAppKey(value);
		} else if(chuangLanIosKey.equals(key)) {
//			FlashClient.initIosAppKey(value);
		} else if(chuangLanIosKeyTemp.equals(key)) {
//		    FlashClient.initIosAppKeyTemp(value);
		} else if(chuangLanLimitKey.equals(key)) {
//			FlashClient.initLimit(value);
		} else if(nicknameDepotKey.equals(key)) {
//			AccountsService.initNicknameList(value);
		} else if(qiniuDefaultKey.equals(key)) {
			QiniuService.initConfig(value);
		} else if(randomNicknameIdIntervalKey.equals(key)) {
//			AccountsService.setRandomNicknameIdInterval(value);
		} else if(visitHomepageOverThreeKey.equals(key)){
//			VisitHomepagePushTask.setIsPushOn(value);
		} else if(accountsPhotoFilterNumKey.equals(key)){
			AccountsPhotoFilterService.accountsPhotoFilterNum = Integer.parseInt(value);
		}else if(aliyunFaceDetectConf.equals(key)){
			AliyunLiveFaceDetectService.initConf(value);
		}else if(voiceSignConf.equals(key)){
			VoiceSignInfoService.initConf(value);
		}else if(voiceSignCopywritingList.equals(key)){
			VoiceSignInfoService.initCopyWritingList(value);
		}else if(voiceSignCopywritingMap.equals(key)) {
			VoiceSignInfoService.initCopyWritingMap(value);
		}else if(personalProfileConf.equals(key)){
			PersonalProfileInfoService.initConf(value);
		}else if(personalDataVersionConf.equals(key)){
			AccountsInfoService.setDataVersion(value);
		}else if(constellationSoulFitInfo.equals(key)){
			AccountsInfoService.setSoulFitInfo(value);
		}else if(isLoginByNameConf.equals(key)){
			AccountsService.setNicknameLoginConf(value);
		}else if(appleLoginInfo.equals(key)){
//			AccountsThirdPartyService.setAppleLoginInfo(value);
		}else if(useEncryptMobileConf.equals(key)){
//			MobileService.setPushConf(value);
		}else if(useAlipayEncryptInfoConf.equals(key)){
//			AccountsCertificationService.initEncryptConf(value);
		}else if(accountLoginVersionLimitConf.equals(key)){
			AccountLoginVersionLimitService.setOn(value);
		}else if(smsRechargeTipContent.equals(key)) {
//			MessageService.RECHARGE_TIP = value;
		}else if(uuidLoginLimitConf.equals(key)){
			AccountsService.initUuidLimitConf(value);
		}else if(returningUserReviewSwitchConf.equals(key)){
			Properties p = JsonUtils.stringToObject(value, Properties.class);
			AccountsMemberInfoService.NICK_NAME_REVIEW = Integer.parseInt(getProperty(p, "nick_name"));
			AccountsMemberInfoService.AVATAR_REVIEW = Integer.parseInt(getProperty(p, "avatar"));
			AccountsMemberInfoService.PERSONAL_INFO_REVIEW = Integer.parseInt(getProperty(p, "personal_info"));
		}else if(blackListCertificationConfigConf.equals(key)) {
			VersionControlService.setBlackListCertificationConfig(value);
		}else if (photoNumConfigConf.equals(key)) {
			VersionControlService.setPhotoNumConfig(value);
		} else if (certificationTypeConf.equals(key)) {
//    	    AccountsCertificationService.CERTIFICATION_TYPE = value;
		} else if (excludeAppidListConf.equals(key)) {
			TengxunyunApiClient.updateExcludeAppidList(value);
		} else if (registerLimitConfKey.equals(key)) {
			RegisterLimitService.setConf(value);
		} else if (reRegisterLimitConfKey.equals(key)) {
			ReRegisterLimitService.setConf(value);
		} else if (photoCheckKey.equals(key)) {
//			AccountsPhotoService.setConf(value);
		} else if (friendChecksConf.equals(key)) {
			AccountsInfoService.setFriendChecks(value);
		} else if (dataCardConf.equals(key)) {
			AccountsInfoService.setDataCard(value);
		} else if (loginAuditAccount.equals(key)) {
			SpecialAccountHandler.updateAuditAccountList(value);
		}else if(certificationConf.equals(key)){
			AccountsCertificationService.initCertificationConf(value);
		}else if(replacePhotoDomainKey.equals(key)){
			AccountsPhotoService.setReplacePhotoDomain(value);
		}else if(accountTicketConfig.equals(key)){
			AccountsService.initAccountTicketConfig(value);
		}else if(accountInfoSwitchKey.equals(key)){
			AccountsInfoService.initSwitchOfAccounts(value);
		}else if(messageBoxTipsKey.equals(key)){
			AccountsInfoService.initMessageBoxTips(value);
		}else if(introductionKey.equals(key)){
			AccountsIntroductionService.initConf(value);
		}else if(thirdPartConfigKey.equals(key)){
			ThirdPartFactory.initConfig(value);
		}else if(switch********Key.equals(key)){
//			VersionSwitchService.initSwitch********(value);
		}else if(switch********Key.equals(key)){
			VersionSwitchService.initSwitch********(value);
		}else if(switch********Key.equals(key)){
			VersionSwitchService.initSwitch********(value);
		}else if(switch********Key.equals(key)){
	        VersionSwitchService.initSwitch********(value);
		}else if (MP_ACCOUNT_SWITCH.equals(key)) {
			MPAccountRequest.initConfig(value);
		} else if (MP_ACCOUNT_SWITCH_2.equals(key)) {
			MPAccountRequest.initSwitchConfig(value);
		} else if (SL_ACCOUNT_SWITCH.equals(key)) {
			ShanLianEnum.initUpdateConfig(value);
		} else if (kafkaServers.equals(key)) {
			KafkaConfig.onlineKafkaServer = value;
		} else if (kafkaOnlineBusiness.equals(key)) {
			KafkaConfig.onlineBusinessServer = value;
		} else if (switchLimitProfileModification.equals(key)) {
			LimitModificationUtil.initConfig(value);
		} else if (switchLimitProfileModification2.equals(key)) {
			LimitModificationUtil.initAvatarConfig(value);
		} else if (clonedDefaultAvatar.equals(key)) {
			CloneDefaultAvatarService.initAvatar(value);
		} else if (ACCOUNT_DESTORY_FREEZE.equals(key)) {
			AccountDestroyUtil.initConfig(value);
		} else if (ACCOUNT_DESTORY_FREEZE_TIP.equals(key)) {
			AccountDestroyUtil.initDestroyTipsConfig(value);
		} else if (SOA_PHP_CITY_AB_TEST.equals(key)) {
			CityAbTestService.REQ_URL = value;
		} else if (THIRD_REG_BIND_MOBILE.equals(key)) {
			AccountsThirdPartyService.initRegConfig(value);
		} else if (switchPwdLoginDeviceRiskKey.equals(key)) {
			AntiSpamService.SWITCH_PWD_LOGIN_DEVICE_RISK = Integer.parseInt(value);
		} else if (SOA_PHP_PRIVACY.equals(key)) {
			PrivacyService.REQ_URL = value;
		} else if (UNBIND_LINK.equals(key)) {
			UnbindWhitelistService.initConfig(value);
		} else if (RISK_APPEAL_LINK.equals(key)) {
			AccountsService.RISK_APPEAL_LINK = value;
		} else if (RISK_APPEAL_LINK_GRAY.equals(key)) {
			AccountsService.RISK_APPEAL_LINK_GRAY = value;
		} else if (fujianGovApiConfigKey.equals(key)) {
			FujianGovApiClient.initConfig(value);
		} else if (fujianGovApiCheckResidenceInfoKey.equals(key)) {
			FujianGovApiClient.initCheckResidenceInfoConfig(value);
		} else if (retrieveAccountByMobileKey.equals(key)) {
			RetrieveAccountConfig.setRetrieveConfig(value);
		} else if (accountsLifeConfigKey.equals(key)) {
            AccountsLifeService.setConfig(value);
		} else if (labelFriendshipPreferKey.equals(key)) {
			FriendshipPreferLabelConfig.setLabelConfig(value);
		} else if (uploadAvatarBucketLiveFaceSwitchKey.equals(key)) {
			ToolsService.initUploadAvatarBucketLiveFaceSwitch(value);
		} else if (uploadAvatarBucketCertificationSwitchKey.equals(key)) {
			ToolsService.initUploadAvatarBucketCertificationSwitch(value);
		} else if (useAccountPrivateBucketSwitchKey.equals(key)) {
			ToolsService.initUseAccountPrivateBucketSwitch(value);
		} else if (accountsLocationInfoSwitchEncryptionKey.equals(key)) {
			ToolsService.accountsLocationInfoSwitchEncryption(value);
		} else if (accountsLocationInfoSwitchWriteKey.equals(key)) {
			ToolsService.accountsLocationInfoSwitchWrite(value);
		} else if (loginActionInfoSwitchEncryptionKey.equals(key)) {
			ToolsService.loginActionInfoSwitchEncryption(value);
		} else if (loginActionInfoSwitchWriteKey.equals(key)) {
			ToolsService.loginActionInfoSwitchWrite(value);
		} else if (registerActionInfoSwitchEncryptionKey.equals(key)) {
			ToolsService.registerActionInfoSwitchEncryption(value);
		} else if (registerActionInfoSwitchWriteKey.equals(key)) {
			ToolsService.registerActionInfoSwitchWrite(value);
		} else if (logoutActionInfoLogSwitchWriteKey.equals(key)) {
			ToolsService.logoutActionInfoLogSwitchWrite(value);
		} else if (accountsCertificationChangeLogSwitchEncryptionKey.equals(key)) {
			ToolsService.accountsCertificationChangeLogSwitchEncryption(value);
		} else if (accountsCertificationChangeLogSwitchWriteKey.equals(key)) {
			ToolsService.accountsCertificationChangeLogSwitchWrite(value);
		} else if (reregisterWhitelistSwitchEncryptionKey.equals(key)) {
			ToolsService.reregisterWhitelistSwitchEncryption(value);
		} else if (reregisterWhitelistSwitchWriteKey.equals(key)) {
			ToolsService.reregisterWhitelistSwitchWrite(value);
		} else if (unbindWhitelistSwitchEncryptionKey.equals(key)) {
			ToolsService.unbindWhitelistSwitchEncryption(value);
		} else if (unbindWhitelistSwitchWriteKey.equals(key)) {
			ToolsService.unbindWhitelistSwitchWrite(value);
		} else if (accountsCertificationSwitchEncryptionKey.equals(key)) {
			ToolsService.accountsCertificationSwitchEncryption(value);
		} else if (accountsCertificationSwitchWriteKey.equals(key)) {
			ToolsService.accountsCertificationSwitchWrite(value);
		} else if (rewardAccountSwitchEncryptionKey.equals(key)) {
			ToolsService.rewardAccountSwitchEncryption(value);
		} else if (accountsChatCertificationSwitchEncryptionKey.equals(key)) {
			ToolsService.accountsChatCertificationSwitchEncryption(value);
		} else if (accountsChatCertificationSwitchWriteKey.equals(key)) {
			ToolsService.accountsChatCertificationSwitchWrite(value);
		} else if (accountsDestroyLogSwitchEncryptionKey.equals(key)) {
			ToolsService.accountsDestroyLogSwitchEncryption(value);
		} else if (accountsDestroyLogSwitchWriteKey.equals(key)) {
			ToolsService.accountsDestroyLogSwitchWrite(value);
		} else if (accountsSwitchEncryptionKey.equals(key)) {
			ToolsService.accountsSwitchEncryption(value);
		} else if (accountsSwitchWriteKey.equals(key)) {
			ToolsService.accountsSwitchWrite(value);
		} else if (loginRegLimitConfigKey.equals(key)) {
			LoginRegLimitService.setConf(value);
		} else if (homepageBackgroundKey.equals(key)) {
			HomepageBackgroundConfig.setConf(value);
		} else {
			LOGGER.warn("未知的etcd key, key={}", key);
			return;
		}
	}

	/**
	 * 获取值，之前写法，只要有一个etcd忘记配置，会堵住下面其他配置项流程
	 */
	public String getEtcdValue(EtcdClient client, String key) {
		try {
			return client.get(key).node.value;
		} catch (Exception e) {
			log.warn("etcd配置异常,key:{}", key);
		}
		return StringUtils.EMPTY;
	}

}
