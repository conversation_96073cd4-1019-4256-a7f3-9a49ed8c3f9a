package cn.taqu.account.etcd;

import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.*;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/4 下午4:11
 */
@Slf4j
@Getter
@EtcdHookBean(key = "/pulsar/config")
public final class PulsarFactory implements EtcdHook, ApplicationListener<ContextClosedEvent> {

    private PulsarClient            pulsarClient;

    private Config                        config;

    private final List<ConfigListener> listeners = new ArrayList<>(4);

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        pulsarClient.closeAsync().whenComplete((n, e) -> {
            if (e == null) {
                log.info("pulsar client closed");
            } else {
                log.warn("unable to close client");
            }
        });
    }

    /**
     * 初始化配置
     *
     * @param str
     */
    public void setClient(String str) {
        if (StringUtils.isNotBlank(str)) {
            config = JsonUtils.stringToObject(str, new TypeReference<Config>() {});
        } else {
            config = new Config();
        }

        if (pulsarClient != null) {
            pulsarClient.closeAsync();
        }
        try {
            this.pulsarClient = PulsarClient.builder()
                .connectionTimeout(5, TimeUnit.SECONDS)
                .enableTcpNoDelay(true)
                .ioThreads(Optional.ofNullable(config.ioThreads).orElse(1))
                .listenerThreads(Optional.ofNullable(config.listenerThreads).orElse(2))
                .serviceUrl(config.url)
                .build();
            log.info("pulsar client init success");
        } catch (PulsarClientException e) {
            log.error("Unable to init pulsar client");
        }
    }

    public Producer<String> producer(String topic) {
        try {
            return pulsarClient.newProducer(Schema.STRING)
                .topic(topic)
                .create();
        } catch (PulsarClientException e) {
            throw new UnsupportedOperationException("Pulsar producer not initialized.", e);
        }
    }

    public void subscribe(MessageListener<String> messageListener, String... topic) {
        pulsarClient.newConsumer(Schema.STRING)
            .topic(topic)
            .subscriptionName("j2")
            .subscriptionType(SubscriptionType.Shared)
            .messageListener(messageListener)
            .negativeAckRedeliveryDelay(1, TimeUnit.SECONDS)
            .subscribeAsync().whenComplete((c, e) -> {
                if (e != null) {
                    String err = "Unable to sub " + Arrays.toString(topic);
                    log.error(err, e);
                }
            });
    }

    @Override
    public void onLoad(String value, Properties properties) {
        setClient(value);
    }

    @Override
    public void onRefresh(String value) {
        setClient(value);
        // 配置变更 通知producer
        listeners.forEach(l -> l.onChange(this, config));
    }

    public void addListener(ConfigListener listener) {
        listeners.add(listener);
    }

    /**
     * 配置
     */
    @Data
    public static class Config {
        /**
         * 最大选择限制
         */
        public String url = "pulsar://pulsar1.test.hbmonitor.com:6650";

        private Integer ioThreads = 1;

        private Integer listenerThreads = 2;

    }

    public interface ConfigListener {
        void onChange(PulsarFactory factory, Config config);
    }

}
