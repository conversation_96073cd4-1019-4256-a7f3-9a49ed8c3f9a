package cn.taqu.account.etcd;

import cn.hutool.core.date.DateUtil;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.core.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/4 下午4:11
 */
@EtcdHookBean(key = "/avatar/uploadThreshold")
public final class AvatarModifyCtrl implements EtcdHook {


    /**
     * 配置
     */
    public Integer threshold = 5;

    @Lazy
    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;


    @Override
    public void onLoad(String value, Properties properties) {
        if (StringUtils.isNotBlank(value)) {
            threshold = Integer.parseInt(value);
        }
    }

    @Override
    public void onRefresh(String value) {
        if (StringUtils.isNotBlank(value)) {
            threshold = Integer.parseInt(value);
        }
    }

    public void increase(String uuid) {
        String key = RedisKeyConstant.AVATAR_MODIFY_TIME.setArg(DateUtil.today());
        Long count = accountBizStringRedisTemplate.opsForHash().increment(key, uuid, 1);
        if (count > threshold) {
            throw new ServiceException(String.format("每日最多上传头像%s次", threshold));
        }
        if (accountBizStringRedisTemplate.getExpire(key) <= -1) {
            accountBizStringRedisTemplate.expire(key, 1, TimeUnit.DAYS);
        }
    }


}
