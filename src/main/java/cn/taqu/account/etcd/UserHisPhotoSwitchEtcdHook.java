package cn.taqu.account.etcd;

import cn.taqu.account.cron.UserHisPhotoRegulateTask;
import cn.taqu.account.soa.handler.AccountFollowServiceImpl;

import java.util.Properties;

/**
 * 用户历史照片重复任务开关
 *
 * <AUTHOR>
 * @date 2025/05/13 下午4:11
 */
@EtcdHookBean(key = "/hisPhoto/isOpen")
public final class UserHisPhotoSwitchEtcdHook implements EtcdHook {

    @Override
    public void onLoad(String value, Properties properties) {
        UserHisPhotoRegulateTask.isOpen = Boolean.parseBoolean(value);
    }

    @Override
    public void onRefresh(String value) {
        UserHisPhotoRegulateTask.isOpen = Boolean.parseBoolean(value);
    }

}
