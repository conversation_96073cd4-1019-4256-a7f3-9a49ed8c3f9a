package cn.taqu.account.etcd;

import cn.taqu.account.config.biz.AllureConfig;

import java.util.Properties;

/**
 * 吸引力比例配置
 * <AUTHOR>
 * @date 2024/12/4 下午4:11
 */
@EtcdHookBean(key = "/allure/ratio")
public final class AllureRatioEtcdHook implements EtcdHook {

    @Override
    public void onLoad(String value, Properties properties) {
        AllureConfig.setRatioConf(value);
    }

    @Override
    public void onRefresh(String value) {
        AllureConfig.setRatioConf(value);
    }
}
