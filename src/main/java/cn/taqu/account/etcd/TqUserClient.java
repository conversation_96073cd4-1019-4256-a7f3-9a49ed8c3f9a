package cn.taqu.account.etcd;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/1 下午5:26
 */
@Slf4j
@Component
public class TqUserClient {

    private final SoaServer soaServer = new SoaServer("/soa/go/taqu-users");

    public void reportCity(String uuid, Integer cityId, Integer sexType) {
        log.info("上报城市信息 {} {}", uuid, cityId);
        SoaClient soaClient = SoaClientFactory.create(soaServer);
        Map<String, Object> body = ImmutableMap.of(
            "account_uuid", uuid,
            "account_info", ImmutableMap.of("city_id", cityId.toString(), "sex_type", sexType.toString())
        );
        try {
            SoaResponse response = soaClient.call("/Account/regReport", body);
            if (response.fail()) {
                String msg = "上报城市信息异常，原因：" + response.getMsg();
                log.warn(msg);
            }
        } catch (Exception e) {
            log.warn("上报城市信息异常", e);
        }
    }

}
