package cn.taqu.account.etcd;

import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/12/4 下午4:11
 */
@EtcdHookBean(key = "/label/config")
public final class LabelEtcdHook implements EtcdHook {


    /**
     * 配置
     */
    public static Config config;

    /**
     * 初始化配置
     *
     * @param str
     */
    public static void setConf(String str) {
        config = JsonUtils.stringToObject(str, new TypeReference<Config>() {});
    }

    @Override
    public void onLoad(String value, Properties properties) {
        setConf(value);
    }

    @Override
    public void onRefresh(String value) {
        setConf(value);
    }

    /**
     * 配置
     */
    @Data
    public static class Config {
        /**
         * 最大选择限制
         */
        public Integer maxLimit;
    }

}
