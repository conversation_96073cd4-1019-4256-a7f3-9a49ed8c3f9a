package cn.taqu.account.etcd;

import cn.taqu.core.utils.JsonUtils;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人脸比对配置
 *
 * <AUTHOR>
 * @date 2025/07/09 下午4:11
 */
@Slf4j
@EtcdHookBean(key = "/compareFace/config")
public final class CompareFaceConfig implements EtcdHook {

    public static Config CONFIG;

    @PostConstruct
    public void init() {
        CONFIG = new Config();

        /**
         * 策略1
         */
        QualityValid compareFace = new QualityValid();
        compareFace.setQualityControl(1L);
        compareFace.setSimilarityScore(50F);

        /**
         * 策略2
         */
        QualityValid detectCompareFace = new QualityValid();
        detectCompareFace.setQualityControl(0L);
        compareFace.setSimilarityScore(50F);

        HashMap<String, QualityValid> qualityValidMap = Maps.newHashMap();
        qualityValidMap.put(CompareMethod.CompareFace.name(), compareFace);
        qualityValidMap.put(CompareMethod.DetectFaceSimilarity.name(), compareFace);
        CONFIG.setQualityValidMap(qualityValidMap);
        CONFIG.setMethod(CompareMethod.CompareFace.name());
        CONFIG.setCompareSwitch(false);
    }

    @Override
    public void onLoad(String value, Properties properties) {
        setConf(value);
    }

    @Override
    public void onRefresh(String value) {
        setConf(value);
    }

    private void setConf(String value) {
        if (StringUtils.isNotBlank(value)) {
            CONFIG = JsonUtils.stringToObject(value, Config.class);
            log.info("人脸识别管控配置[liveFace/config]:{}", value);
        }
    }

    /**
     * 配置
     */
    @Data
    public static class Config {

        /**
         * 调用方法
         */
        private String method;

        /**
         * 质量管控参数配置，多套一起配置，后面方便做数据比对
         * <p>
         * key：CompareMethod枚举的key
         */
        private Map<String, QualityValid> qualityValidMap;

        /**
         * 比对开关
         */
        private Boolean compareSwitch;

        /**
         * 比对日期:具体某日，20250712,20250713;
         * 多个用,隔开
         */
        private String compareDate;

        /**
         * 需要比对的策略(先支持一个策略，多个策略正常场景也不存在)
         */
        private String compareMethod;

    }

    /**
     * 比对方法
     */
    public enum CompareMethod {
        /**
         * <a href="https://cloud.tencent.com/document/product/867/32802">...</a>
         */
        CompareFace,

        /**
         * <a href="https://cloud.tencent.com/document/product/867/114852#API-Inspector">...</a>
         */
        DetectFaceSimilarity;

        static final Map<String, CompareMethod> methods;

        static {
            methods = Arrays.stream(CompareMethod.values()).collect(Collectors.toMap(Enum::name, Function.identity()));
        }

        public static CompareMethod getBy(String method) {
            CompareMethod compareMethod = methods.get(method);
            if (compareMethod == null) {
                log.warn("人脸比对方法未配置");
                return CompareFace;
            }

            return compareMethod;
        }

    }

    /**
     * 质量管控校验
     */
    @Data
    public static class QualityValid {

        /**
         * 头像质量限制管控(compareFace)
         */
        private Long qualityControl;

        /**
         * 头像质量比对分值, >= 该数值标识相似
         */
        private Float similarityScore;

    }

}
