package cn.taqu.account.etcd;

import cn.hutool.core.lang.ClassScanner;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;

import java.lang.reflect.Modifier;
import java.util.Set;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2024/12/6 下午5:13
 */
@RequiredArgsConstructor
final class EtcdHookRegistrar {

    private final DefaultListableBeanFactory beanFactory;

    private final BiConsumer<EtcdHookBean, EtcdHook> postRegister;

    Set<Class<?>> register() {
        return ClassScanner.scanPackage(this.getClass().getPackage().getName(), type -> {
            if (type.isAnnotation()) {
                return false;
            }
            if (!EtcdHook.class.isAssignableFrom(type)) {
                return false;
            }
            @SuppressWarnings("unchecked")
            Class<EtcdHook> clz = (Class<EtcdHook>) type;
            if (type.isInterface()) {
                return false;
            }
            if (Modifier.isAbstract(type.getModifiers())) {
                return false;
            }
            EtcdHookBean ann = clz.getAnnotation(EtcdHookBean.class);
            if (ann == null) {
                return false;
            }
            BeanDefinition gbd = BeanDefinitionBuilder.genericBeanDefinition(clz)
                .setScope(BeanDefinition.SCOPE_SINGLETON)
                .getBeanDefinition();
            String beanName = StrUtil.lowerFirst(clz.getSimpleName());
            beanFactory.registerBeanDefinition(beanName, gbd);
            EtcdHook hook = beanFactory.getBean(clz);
            postRegister.accept(ann, hook);
            return true;
        });
    }
}
