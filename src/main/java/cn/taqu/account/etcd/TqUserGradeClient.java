package cn.taqu.account.etcd;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/1 下午5:26
 */
@Slf4j
@Component
public class TqUserGradeClient {

    private final SoaServer soaServer = new SoaServer("/soa/go/taqu-user-grade");

    public Integer getCharmLevelByUuid(String uuid) {
        SoaClient client = SoaClientFactory.create(soaServer);
        Map<String, Object> body = new HashMap<>();
        body.put("account_uuid", uuid);
        try {
            SoaResponse response = client.call("/Charm/getCharmLevelByUuid", body);
            if (response.fail()) {
                return 0;
            }
            Map<String, String> map = JsonUtils.stringToObject(response.getData(), new TypeReference<Map<String, String>>() {});
            return Integer.valueOf(map.get("level"));
        } catch (Exception e) {
            log.warn("getCharmLevelByUuid异常", e);
            return 0;
        }
    }

    public UserPrivilege privilege(String uuid, String key, Integer sexType) {
        SoaClient client = SoaClientFactory.create(soaServer);
        Map<String, Object> body = ImmutableMap.of(
            "account_uuid", uuid,
            "privilege_key", key,
            "sex_type", sexType.toString()
        );
        try {
            SoaResponse response = client.call("/Grow/checkHasPrivilege", body);
            if (response.fail()) {
                return UserPrivilege.NONE;
            }
            return JsonUtils.stringToObject(response.getData(), UserPrivilege.class);
        } catch (Exception e) {
            log.warn("privilege获取异常", e);
            return UserPrivilege.NONE;
        }
    }

    public Map<String, Long> batchGetAccountGrowScore(Collection<String> uuids) {
        Map<String, Object> body = ImmutableMap.of(
            "account_uuids", uuids
        );
        SoaClient client = SoaClientFactory.create(soaServer);
        try {
            SoaResponse response = client.call("/Grow/batchGetAccountGrowScore", body);
            if (response.fail()) {
                return Collections.emptyMap();
            }
            Map<String, Map<String, Long>> resp = JsonUtils.stringToObject(response.getData(), new TypeReference<Map<String, Map<String, Long>>>() {});
            return resp.get("list");
        } catch (Exception e) {
            log.warn("batchGetAccountGrowScore异常", e);
            return Collections.emptyMap();
        }
    }


    @Data
    public static class UserPrivilege {

        public static UserPrivilege NONE = new UserPrivilege();

        private Boolean enable;

        private Integer reach_score;

        private String privilege_name;

        private Integer limit_num;

        static {
            NONE.enable = false;
            NONE.reach_score = 0;
        }
    }
}
