package cn.taqu.account.etcd;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.soa.AIGCSoaService;
import cn.taqu.account.utils.SoaHeaderBuilder;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.SpringContextHolder;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

import static cn.taqu.account.constant.CommConst.AVATAR_HOST;
import static cn.taqu.account.constant.CommConst.HTTP;

/**
 * <AUTHOR>
 * @date 2025/4/1 下午5:26
 */
@Slf4j
@EtcdHookBean(key = "/soa/python/touch-rank-server")
public class TouchRankClient implements EtcdHook, AIGCSoaService {

    /**
     *  -1分代表算法没返回算分结果 待pulsar推入分数进行更新
     * @see cn.taqu.account.mq.AlgorithmEvaluationConsumer
     */
    public static final Integer PROCESSING = -1;

    private String host;

    private static final SoaServer SERVER = new SoaServer("/soa/python/touch-rank-server");

    @Resource
    private RestTemplate restTemplate;

    @Override
    public void onLoad(String value, Properties properties) {
        host = value;
    }

    @Override
    public void onRefresh(String value) {
        host = value;
    }

    public AiResult appearanceEval(String uuid, String content, Integer sexType) {
        return eval("/alg/aigc/appearancescore", uuid, content, sexType);
    }

    public AiResult sincereEval(String uuid, String content, Integer sexType) {
        return eval("/alg/aigc/sincerescore", uuid, content, sexType);
    }

    public AiResult eval(String path, String uuid, String content, Integer sexType) {
        Map<String, Object> body = ImmutableMap.of(
            "uuid", uuid,
            "content", content,
            "sex", sexType
        );
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, SoaHeaderBuilder.buildHeadersFromSoaParams());
        try {
            HttpEntity<AiResult> resp = restTemplate.postForEntity(host.concat(path), entity, AiResult.class);
            AiResult response = resp.getBody();
            log.info("算法打分 {} {} {} {}", path, uuid, content, response);
            if (!response.successful()) {
                String msg = "图片打分异常，原因：" + response.getMsg();
                log.warn(msg);
            }
            return response;
        } catch (Exception e) {
            log.warn("算法打分异常", e);
            AiResult result = new AiResult();
            result.code = 500;
            result.score = PROCESSING;
            result.is_true = 0;
            return result;
        }
    }

    @Override
    public void avatarQuality(String uuid, String url) {
        if (!url.startsWith(HTTP)) {
            url = AVATAR_HOST + url;
        }
        SoaClient soaClient = SoaClientFactory.create(SERVER);
        soaClient.setTryLimit(1);
        HashMap<String, String> param = Maps.newHashMap();
        param.put("uuid", uuid);
        param.put("url", url);
        log.info("头像推送算法比对,uuid:{},url:{}", uuid, url);
        SoaResponse call = soaClient.call("/alg/aigc/avatarquality", param);
        if (!Objects.equals("200", call.getCode())) {
            Object obj = SpringContextHolder.getBean("accountBizStringRedisTemplate");
            if (obj instanceof StringRedisTemplate) {
                log.warn("头像指导算法接口推送失败,uuid:{}, url:{}", uuid, url);
                // 调用失败，放到失败队列
                StringRedisTemplate accountBizStringRedisTemplate = (StringRedisTemplate)obj;
                accountBizStringRedisTemplate.opsForSet().add(
                        RedisKeyConstant.AVATAR_GUIDE_ERROR_QUEUE, uuid);
            }
        }
    }

    @Data
    public static class AiResult {

        private Integer code;

        private String msg;

        private Integer score;

        private Integer is_true;

        public boolean successful() {
            return 200 == code;
        }

        public boolean isTrue() {
            return 1 == is_true;
        }
    }

}
