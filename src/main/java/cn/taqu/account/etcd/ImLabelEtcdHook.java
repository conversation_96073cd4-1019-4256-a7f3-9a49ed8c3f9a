package cn.taqu.account.etcd;

import cn.taqu.account.service.impl.AccountLabelServiceImplV2;

import java.util.Properties;

/**
 * im标签展示限制
 *
 * <AUTHOR>
 * @date 2025/5/19 下午4:11
 */
@EtcdHookBean(key = "/imLabel/limit")
public final class ImLabelEtcdHook implements EtcdHook {


    @Override
    public void onLoad(String value, Properties properties) {
        AccountLabelServiceImplV2.imLabelLimit = Integer.parseInt(value);
    }

    @Override
    public void onRefresh(String value) {
        AccountLabelServiceImplV2.imLabelLimit = Integer.parseInt(value);
    }
}
