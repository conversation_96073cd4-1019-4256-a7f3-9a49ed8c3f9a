package cn.taqu.account.etcd;

import org.apache.commons.lang3.StringUtils;

import java.util.Properties;

/**
 * 关怀模式具体配置
 *
 * <AUTHOR>
 * @date 2025/6/27 下午4:11
 */
@EtcdHookBean(key = "/careModel/age")
public final class CareModelConfig implements EtcdHook {

    /**
     * 默认60
     */
    public static Integer LIMIT_AGE = 60;

    @Override
    public void onLoad(String value, Properties properties) {
        handle(value);
    }

    @Override
    public void onRefresh(String value) {
        handle(value);
    }

    public void handle(String value) {
        if (StringUtils.isNotBlank(value)) {
            LIMIT_AGE = Integer.parseInt(value);
        }
    }
}
