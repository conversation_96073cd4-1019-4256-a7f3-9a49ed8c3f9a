package cn.taqu.account.etcd;

import cn.taqu.account.common.PlatformEnum;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * 版本控制(全局版本控制)
 *
 * <AUTHOR>
 * @date 2025/7/18 下午6:11
 */
@Slf4j
@EtcdHookBean(key = "/globe/versionControl")
public final class VersionControlConfig implements EtcdHook {

    /**
     * 版本控制总map
     * key：业务线
     * value: 具体版本配置平台{
     * "1": 8000,
     * "2": 80001
     * }
     * 可参考现有配置业务场景进行更改新增
     */
    private static Map<String, Map<Integer, Long>> VERSION_MAP;

    @Override
    public void onLoad(String value, Properties properties) {
        VERSION_MAP = JsonUtils.stringToObject(value, new TypeReference<Map<String, Map<Integer, Long>>>() {
        });
    }

    @Override
    public void onRefresh(String value) {
        VERSION_MAP = JsonUtils.stringToObject(value, new TypeReference<Map<String, Map<Integer, Long>>>() {
        });
    }

    /**
     * 全局版本控制，具体业务线
     */
    @Getter
    public enum Scene {
        /**
         * 60岁提现限制
         */
        WITHDRAW_LIMIT_60,
        /**
         * 女包60岁提现限制
         */
        GIRL_GROUP_WITHDRAW_LIMIT_60,
        ;
    }

    /**
     * @param platformId
     * @param version
     * @return
     */
    public static boolean isPass(Scene scene, Integer platformId, Long version) {
        Map<Integer, Long> map = VERSION_MAP.get(scene.name());
        if (map == null || map.isEmpty()) {
            log.warn("版本未配置:{}", scene.name());
            return false;
        }

        if (Objects.equals(PlatformEnum.ANDROID.getValue(), platformId)) {
            return version >= map.get(PlatformEnum.ANDROID.getValue());
        }

        if (Objects.equals(PlatformEnum.IPHONE.getValue(), platformId)
                || Objects.equals(PlatformEnum.IPAD.getValue(), platformId)) {
            return version >= map.get(PlatformEnum.IPHONE.getValue());
        }

        log.warn("未知平台:{}", platformId);
        return false;
    }

}
