package cn.taqu.account.etcd;

import cn.taqu.account.service.impl.DestroyServiceImpl;

import java.util.Properties;

/**
 * 注销内容配置
 *
 * <AUTHOR>
 * @date 2025/04/07 下午4:11
 */
@EtcdHookBean(key = "/destroy/reason")
public final class DestroyReasonConfigEtcdHook implements EtcdHook {

    @Override
    public void onLoad(String value, Properties properties) {
        DestroyServiceImpl.setConf(value);
    }

    @Override
    public void onRefresh(String value) {
        DestroyServiceImpl.setConf(value);
    }
}
