package cn.taqu.account.etcd;

import cn.taqu.core.utils.JsonUtils;
import lombok.Data;

import java.util.Map;
import java.util.Properties;

/**
 * 头像指导
 *
 * <AUTHOR>
 * @date 2024/12/4 下午4:11
 */
@EtcdHookBean(key = "/avatar/guide")
public final class AvatarGuideConfig implements EtcdHook {

    /**
     * 每个模式配置都不一样
     */
    public static Config CONFIG;

    @Override
    public void onLoad(String value, Properties properties) {
        CONFIG = JsonUtils.stringToObject(value, Config.class);
    }

    @Override
    public void onRefresh(String value) {
        CONFIG = JsonUtils.stringToObject(value, Config.class);
    }

    /**
     * 具体配置
     */
    @Data
    public static class Config {
        /**
         * ui相关
         */
        private Map<Integer, UI> ui;

        /**
         * 业务逻辑
         */
        private Biz biz;
    }

    /**
     * 具体ui相关配置（客户单展示）
     */
    @Data
    public static class UI {
        /**
         * 标题
         */
        private String titleBg;

        private String titleDarkBg;

        /**
         * 副标题
         */
        private String subTitle;

        /**
         * 默认美颜方案
         */
        private String defaultBeauty;
    }

    /**
     * 具体业务配置
     */
    @Data
    public static class Biz {
        /**
         * 巡检天数
         */
        private Integer circleDay;
    }
}
