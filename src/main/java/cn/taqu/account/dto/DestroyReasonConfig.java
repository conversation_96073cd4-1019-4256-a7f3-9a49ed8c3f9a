package cn.taqu.account.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 注销原因配置
 *
 * <AUTHOR>
 * @date 2025/4/7 15:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DestroyReasonConfig {

    @JsonProperty("reason_id")
    private Integer reasonId;

    private Integer id;

    private String content;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<DestroyReasonConfig> children;

    /**
     * 配置转化
     */
    public void setReasonId() {
        reasonId = this.id;
    }

}
