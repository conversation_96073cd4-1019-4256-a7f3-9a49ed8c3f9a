package cn.taqu.account.dto;

import cn.taqu.account.common.PlatformEnum;
import cn.taqu.account.constant.CommConst;
import cn.taqu.core.protocol.SoaBaseParams;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 这个配置用于2022年3月1期迭代开关
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-10 16:22
 */
@Slf4j
public class SwitchDto20220302 {

    /**
     * 是否开启支付宝验证
     * ******** 此开关仅控制安卓用户是否验证，苹果用户不能开启(无法过审)
     */
    private final Integer openAlipayAuth;

    private final Integer iosVersion;

    private final Integer androidVersion;

    private final Integer cancelCertTimes;

    private final String cancelCertLimitMsg;

    public SwitchDto20220302(Integer openAlipayAuth, Integer iosVersion, Integer androidVersion, Integer cancelCertTimes, String cancelCertLimitMsg){
        this.openAlipayAuth = openAlipayAuth;
        this.iosVersion = iosVersion;
        this.androidVersion = androidVersion;
        this.cancelCertTimes = cancelCertTimes;
        this.cancelCertLimitMsg = cancelCertLimitMsg;
    }

    /**
     * 是否提现账号需要支付宝验证
     * @return
     */
    public Integer getOpenAlipayAuth(){
        Integer platformId = SoaBaseParams.fromThread().getPlatform_id();
        log.info("getOpenAlipayAuth - platform: {}, open: {}", platformId, openAlipayAuth);
        // ******** 此开关仅控制安卓用户是否验证，苹果用户不能开启(无法过审)
        if(PlatformEnum.ANDROID.getValue().equals(platformId)) {
            return openAlipayAuth == null ? 0 : openAlipayAuth;
        }
        return 0;
    }

    /**
     * 是否开关开启
     * @return
     */
    public Boolean isOpenAlipayAuth(){
        return Objects.equals(openAlipayAuth, CommConst.YES_1);
    }

    /**
     * 是否大于等于版本号
     * @return
     */
    public Boolean isGeVersion(){
        Integer platformId = SoaBaseParams.fromThread().getPlatform_id();
        Long appVersion = SoaBaseParams.fromThread().getApp_version();

        if(platformId == null || appVersion == null){
            return true;
        }
        if(PlatformEnum.ANDROID.getValue().equals(platformId)){
            return appVersion >= androidVersion;
        }else if(PlatformEnum.IPHONE.getValue().equals(platformId) || PlatformEnum.IPAD.getValue().equals(platformId)){
            return appVersion >= iosVersion;
        }else {
            return true;
        }
    }

    public Integer getCancelCertTimes() {
        return cancelCertTimes;
    }

    public String getCancelCertLimitMsgWithTimes() {
        String msg = String.format(cancelCertLimitMsg, cancelCertTimes);
        return msg;
    }
}
