package cn.taqu.account.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 手机信息【用于风控数据传递】
 *
 * <AUTHOR>
 * @date 2024/8/13 10:11 上午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MobileInfoDTO {

    /**
     * 手机品牌
     */
    private String mobileBrand;

    /**
     * 手机系统版本
     */
    private String mobileSysVersion;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 网络类型
     */
    private String networkType;

    /**
     * 网络服务商，中国移动、中国联通、中国电信
     */
    private String networkService;

}
