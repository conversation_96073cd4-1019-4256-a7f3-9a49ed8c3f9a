package cn.taqu.account.dto;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.service.AvatarHandleService;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 来自对getInfoByUuid接口的转化，减少读取繁琐
 * 具体filed字段来自 {@link cn.taqu.account.common.UuidInfoField}
 *
 * <AUTHOR>
 * @date 2024/11/28 10:48
 */
@Data
public class InfoFiledCacheDTO implements Serializable {

    /**
     * 唯一标识
     */
    private String uuid;

    private String avatar;

    @JsonProperty("account_name")
    private String accountName;

    /**
     * 性别
     */
    @JsonProperty("sex_type")
    private String gender;

    /**
     * 所在地
     */
    @JsonProperty("baseaddr")
    private String baseAddr;

    /**
     * 家乡
     */
    private String hometown;

    /**
     * 学历
     */
    private String education;

    /**
     * 职业
     */
    private String profession;

    /**
     * 身高
     */
    private String height;

    /**
     * 体重
     */
    private String weight;

    /**
     * 收入
     */
    private String income;

    /**
     * 个人简介（已审核通过的）
     */
    @JsonProperty("pass_personal_profile")
    private String profile;

    /**
     * 自我介绍（已审核通过的）
     */
    @JsonProperty("pass_introduction_content")
    private String introduction;

    /**
     * 情感状况
     */
    @JsonProperty("affectivestatus")
    private String affectiveStatus;

    /**
     * 城市ip
     */
    @JsonProperty("city_id")
    private String cityId;

    /**
     * 省份ip
     */
    @JsonProperty(UuidInfoField.IP_PRO)
    private String ipPro;

    /**
     * 登录时的appcode
     */
    @JsonProperty("login_appcode")
    private String loginAppcode;

    /**
     * 登录时的马甲包
     */
    @JsonProperty("login_cloned")
    private String loginCloned;

    /**
     * 登录时的平台id
     */
    @JsonProperty("platform_id")
    private String platformId;

    /**
     * app版本号
     */
    @JsonProperty("app_version")
    private String appVersion;

    /**
     * 是否真人认证，0-否，1-是
     */
    @JsonProperty(UuidInfoField.ZHIMA_CERTIFICATION)
    private String zhimaCertification;

    /**
     * 真人头像
     */
    @JsonProperty(UuidInfoField.REAL_AVATAR_CERTIFICATION)
    private String realAvatarCertification;

    /**
     * 头像认证状态，0-未认证，1-认证成功，2-认证失败
     * 相当于 real_person_certification
     */
    @JsonProperty(UuidInfoField.PROFILE_VERIFY_STATUS)
    @Deprecated
    private String profileVerifyStatus;

    /**
     * 身份证上的10位时间戳
     */
    @JsonProperty(UuidInfoField.IDENTITY_NO_BIRTH)
    private Long identityNoBirth;

    /**
     * 头像+相册，返回审核通过的
     */
    private List<Photo> photo;

    /**
     * 相册
     */
    @Data
    public static class Photo {
        /**
         * 图片url
         */
        @JsonProperty("photo_url")
        private String photoUrl;
        /**
         * 真人照片认证，0-未认证，1-认证通过，2-认证失败
         */
        @JsonProperty("verify_status")
        private Integer verifyStatus;
    }

    /**
     * 用户状态
     * 用户状态目前是从缓存中取，不太建议。建议从j70获取。后续会统一整改
     */
    @JsonProperty(UuidInfoField.ACCOUNT_STATUS)
    private Integer accountStatus;

    /************************** 统一类型转化 **************************
     */
    public Integer getHeight() {
        return StringUtils.isBlank(height) ? 0 : Integer.parseInt(height);
    }

    public Integer getWeight() {
        return StringUtils.isBlank(weight) ? 0 : Integer.parseInt(weight);
    }

    public Integer getGender() {
        return StringUtils.isBlank(gender) ? 0 : Integer.parseInt(gender);
    }

    public Integer getIncome() {
        return StringUtils.isBlank(income) ? 0 : Integer.parseInt(income);
    }

    public Integer getAffectiveStatus() {
        return StringUtils.isBlank(affectiveStatus) ? 0 : Integer.parseInt(affectiveStatus);
    }

    public Integer getLoginAppcode() {
        return StringUtils.isBlank(loginAppcode) ? 0 : Integer.parseInt(loginAppcode);
    }

    public Integer getLoginCloned() {
        return StringUtils.isBlank(loginCloned) ? 0 : Integer.parseInt(loginCloned);
    }

    public Integer getPlatformId() {
        return StringUtils.isBlank(platformId) ? 0 : Integer.parseInt(platformId);
    }

    public Long getAppVersion() {
        return StringUtils.isBlank(appVersion) ? 0L : Long.parseLong(appVersion);
    }

    public boolean realPersonCertificationPass() {
        int a = StringUtils.isBlank(realAvatarCertification) ? 0 : Integer.parseInt(realAvatarCertification);
        int b = StringUtils.isBlank(profileVerifyStatus) ? 0 : Integer.parseInt(profileVerifyStatus);
        boolean c = !AvatarHandleService.isDefAvatar(avatar);
        return a == 1 && b == 1 && c;
    }

}
