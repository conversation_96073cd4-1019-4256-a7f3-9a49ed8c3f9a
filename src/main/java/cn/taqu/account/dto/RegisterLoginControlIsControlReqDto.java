package cn.taqu.account.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 
 * https://api.admin.internal.taqu.cn/docs/api/api-1fldcj3chrjtd
 * 
 * <AUTHOR>
 * 2024年6月18日上午9:17:47
 */
@Data
@Builder
public class RegisterLoginControlIsControlReqDto {
    /*
     * {                     
     *     "appCode":1,      
     *     "cloned":1,       
     *     "type":"登录",      
     *     "model":"苹果",     
     *     "platform":"ipad",
     *     "version":100,    
     *     "token":"token"   
     * }   
     */         
    
    /**
     * 应用
     */
    private Integer appCode;
    /**
     * 分身包
     */
    private Integer cloned;
    /**
     * 类型：登录、注册
     */
    private String type;
    /**
     * 方式：验证码、 闪验、微信、QQ、微博、苹果、账号密码
     */
    private String model;
    /**
     * 平台：android; iphone; ipad;
     */
    private String platform;
    /**
     * 版本
     */
    private Long version;
    /**
     * 设备号
     */
    private String token;
    
    
    public interface type{
        String LOGIN = "登录";
        String REGISTER = "注册";
    }
    
    public interface model{
        /**
         *  注册
         */
        String MOBILE = "手机号";
        /**
         *  登录
         */
        String VCODE = "验证码";
        /**
         *  登录
         */
        String FLASH = "闪验";
        /**
         *  登录
         */
        String PASSWORD = "账号密码";
        /**
         *  登录、注册
         */
        String WECHAT = "微信";
        /**
         *  登录、注册
         */
        String QQ = "QQ";
        /**
         *  登录、注册
         */
        String APPLE = "苹果";
        /**
         *  登录、注册
         */
        String WEIBO = "微博";
    }
}
