package cn.taqu.account.dto.sms;

import java.io.Serializable;

import lombok.Data;

/**
 * 业务请求类
 * 
 * <AUTHOR>
 * @date 2022/11/09
 */
@Data
public class BusinessSmsBaseReq implements Serializable {
    private static final long serialVersionUID = -7351697638657592281L;

    //应用代码 业务传
    private Integer appcode;

    //分身版本代码 业务传
    private Integer cloned;

    //手机号 单条 业务传
    private String phone;
    
    //短信标识 业务传
    private String tagCode;
    
    //短信内容 业务传
    private String content;
    
    /**
     * 非必填，是否使用消息模板 0-否，1-是；默认0，若为1，则template必填
     * 2024.02.28 新增
     */
    private Integer templateFlag;
    
    /**
     * 非必填，消息模板
     * 2024.02.28 新增
     */
    private TemplateDto template;
    
    public BusinessSmsBaseReq() {
        super();
    }
    
}
