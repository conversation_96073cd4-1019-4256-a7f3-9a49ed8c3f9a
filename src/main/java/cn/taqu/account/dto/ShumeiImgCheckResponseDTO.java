package cn.taqu.account.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Classname ShumeiImgCheckResponseDTO
 * @Description 
 * @Date 2021/1/20 下午4:34
 */
@Getter
@Setter
public class ShumeiImgCheckResponseDTO {
    /**
     * 返回码
     */
    private Integer code;
    /**
     * 返回码详情描述
     */
    private String message;
    /**
     * 风险级别
     */
    private String riskLevel;

    /**
     * 风险原因
     *  当 riskLevel 为 PASS 时 为"正常"
     */
    private String riskDescription;

    /**
     * 响应结果lo
     */
    private String responseStr;

    /**
     * 订单号
     */
    private String requestId;

    /**
     * 图片地址 需要手动设置
     */
    private String photoUrl;
}
