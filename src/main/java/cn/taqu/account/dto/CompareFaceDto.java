package cn.taqu.account.dto;

import cn.taqu.account.etcd.CompareFaceConfig;
import cn.taqu.account.service.AliyunLiveFaceDetectService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class CompareFaceDto {

    /**
     * 是否相似
     */
    private boolean similarity = false;

    /**
     * 对比分数
     */
    private Float score = 0.0f;

    /**
     * code
     */
    private String code = "";

    /**
     * 内容
     */
    private String msg = "";
    /**
     * requestId
     */
    private String requestId = "";

    private String faceModelVersion;

    // 需要手动设置
    private String basePhotoUrl;

    private String verifyPhotoUrl;

    /**
     * 比对策略
     */
    private CompareFaceConfig.CompareMethod compareMethod;

    public Boolean isRequestSuccess(){
        if(StringUtils.isBlank(this.code)){
            return true;
        }
        if(AliyunLiveFaceDetectService.COMPARE_UNCATCH_ERR_CODE.contains(this.code)){
            this.score = 0.0f;
            this.similarity = false;
            return true;
        }
        return false;
    }
}
