package cn.taqu.account.dto;

import cn.taqu.account.common.PlatformEnum;
import cn.taqu.core.protocol.SoaBaseParams;
import lombok.extern.slf4j.Slf4j;

/**
 * https://o15vj1m4ie.feishu.cn/wiki/VFsXwMYgeixaV6kAShPcrNTXnkh 私信_Profile优化
 *
 * <AUTHOR> 2024年11月29日下午3:52:53
 */
@Slf4j
public class SwitchDto20241129 {

    private final Long androidVersion;

    private final Long iosVersion;

    public SwitchDto20241129(Long iosVersion, Long androidVersion) {
        this.androidVersion = androidVersion;
        this.iosVersion = iosVersion;
    }

    /**
     * 是否大于等于版本号
     *
     * @return
     */
    public Boolean isGeVersion() {
        Integer platformId = SoaBaseParams.fromThread().getPlatform_id();
        Long appVersion = SoaBaseParams.fromThread().getApp_version();

        return isGeVersion(platformId, appVersion);
    }

    public Boolean isGeVersion(String platformIdStr, String appVersionStr) {
        Integer platformId = null;
        Long appVersion = null;
        try {
            platformId = Integer.valueOf(platformIdStr);
            appVersion = Long.valueOf(appVersionStr);
        } catch (Exception e) {
            log.warn("isGeVersion error platformId:{}|appVersion:{}", platformId, appVersion);
        }
        return isGeVersion(platformId, appVersion);
    }

    public Boolean isGeVersion(Integer platformId, Long appVersion) {
        if (platformId == null || appVersion == null) {
            return true;
        }
        if (PlatformEnum.ANDROID.getValue().equals(platformId)) {
            return appVersion >= androidVersion;
        } else if (PlatformEnum.IPHONE.getValue().equals(platformId)
            || PlatformEnum.IPAD.getValue().equals(platformId)) {
            return appVersion >= iosVersion;
        } else {
            return true;
        }
    }

}
