package cn.taqu.account.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 吸引力缓存
 *
 * <AUTHOR>
 * @date 2024/11/29 10:22
 */
@Data
public class AllureCacheDTO implements Serializable {

    /**
     * 得分
     */
    private Integer point;
    /**
     * 比例
     */
    private Integer ratio;
    /**
     * 每日系统积分
     */
    private Integer dailySystemPoint;
    /**
     * 每日系统比例
     */
    private Integer dailySystemRatio;
    /**
     * 完成项数
     */
    private Integer finishItem;

}
