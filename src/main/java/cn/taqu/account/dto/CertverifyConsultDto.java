package cn.taqu.account.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-07 14:34
 */
@Accessors(chain = true)
@Data
public class CertverifyConsultDto {

    /**
     * 是否通过
     */
    private Boolean passed;

    /**
     * 验证信息
     */
    private String verifyInfo;

    /**
     * 业务id
     */
    private String verifyId;

    /**
     * 阿里云返回业务msg
     */
    private String responseMsg;

    /**
     * 阿里云返回业务code
     */
    private String respnseCode;

    private String responseStr;
}
