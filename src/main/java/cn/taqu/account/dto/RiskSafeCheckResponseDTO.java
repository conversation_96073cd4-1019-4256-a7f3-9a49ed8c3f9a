package cn.taqu.account.dto;

import cn.taqu.account.common.RiskDetectRiskLevelEnum;
import cn.taqu.account.common.RiskSafeHitTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * 风控安全校验（https://api.admin.internal.taqu.cn/docs/api/api-1eh48gmvrqft6）
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Data
public class RiskSafeCheckResponseDTO implements Serializable {

    // 应用编码
    private String appCode;

    // 分身号
    private String cloned;

    // 规则ID
    private String safeId;

    // 业务唯一键
    private String bizId;

    // 发送用户uuid
    private String senderUuid;

    // 接收用户uuid
    private String receiverUuid;

    // 请求ID
    private String requestId;

    // 打击方式，等待审核结果wait、通过pass、拒绝block
    private String hitTypeCode;

    // 一级标签
    private String label1;

    // 二级标签
    private String label2;

    // 三级标签
    private String label3;

    // 检测方，SHUMEI数美，TAQU他趣
    private String checker;

    // 命中模型
    private String model;

    // 违规类型
    private String violationType;

    // 命中的关键字
    private String hitWordList;

    // 脱敏后的文本内容，如敏感词打\*
    private String filteredText;

    // 是否展示发送方提示语，默认false，false不展示，true展示
    private Boolean showSenderPrompt;

    // 发送方提示语（违规方提示语）
    private String senderPrompt;

    // 是否展示接收方提示语，默认false，false不展示，true展示
    private Boolean showReceiverPrompt;

    // 接收方提示语（对方提示语）
    private String receiverPrompt;

    // 引导认证跳转链接
    private String validUrl;

    // 提示
    private String toast;

    // 操作者
    private String operator;

    // 扩展信息
    private Map<String, Object> extra;

    // 透传参数
    private Map<String, Object> through;

    /**
     * 接入新风控接口，转换成旧对象模型，避免改动太多
     *
     * @return
     */
    public ShumeiContentCheckDTO toCheckDTO() {
        ShumeiContentCheckDTO dto = new ShumeiContentCheckDTO();
        dto.setRequestId(requestId);
        dto.setSuggestion(StringUtils.isNoneBlank(hitTypeCode) ? hitTypeCode.toUpperCase() : "");
        dto.setViolationType(violationType);
        dto.setBody(filteredText);
        dto.setModel(model);
        dto.setChecker(checker);
        dto.setRiskLabel1(label1);
        dto.setRiskLabel2(label2);
        dto.setRiskLabel3(label3);
        dto.setToast(toast);
        return dto;
    }

    public ShumeiImgCheckResponseDTO toImgCheckDTO() {
        ShumeiImgCheckResponseDTO dto = new ShumeiImgCheckResponseDTO();
        dto.setRequestId(requestId);
        dto.setRiskLevel("block".equals(hitTypeCode) ? RiskDetectRiskLevelEnum.REJECT.name() : hitTypeCode.toUpperCase());
        dto.setRiskDescription(label1 + label2 + label3 + "");
        return dto;
    }

    public String getOperator() {
        if (StringUtils.isNotBlank(operator)) {
            return operator;
        }
        return Objects.nonNull(extra) && extra.containsKey("operator") ? extra.get("operator").toString() : "";
    }

    public String getBlockReason() {
        return Objects.nonNull(extra) && extra.containsKey("blockReason") ? extra.get("blockReason").toString() : "";
    }

    public Long getPhotoId() {
        return Objects.nonNull(extra) && extra.containsKey("photoId") && Objects.nonNull(extra.get("photoId")) ? Long.parseLong(extra.get("photoId").toString()) : null;
    }

    public String getPhotoUrl() {
        return Objects.nonNull(extra) && extra.containsKey("photoUrl") && Objects.nonNull(extra.get("photoUrl")) ? extra.get("photoUrl").toString() : "";
    }

    public String getIsOldData() {
        return Objects.nonNull(extra) && extra.containsKey("isOldData") && Objects.nonNull(extra.get("isOldData")) ? extra.get("isOldData").toString() : null;
    }

    public String getPhotoUrlByThrough() {
        return Objects.nonNull(through) && through.containsKey("photoUrl") && Objects.nonNull(through.get("photoUrl")) ? through.get("photoUrl").toString() : "";
    }

    public String getRealBizId() {
        if (StringUtils.isNotBlank(bizId) && bizId.contains("-")) {
            return bizId.split("-")[0];
        }
        return bizId;
    }

    public RiskSafeHitTypeEnum getRiskSafeHitType() {
        return RiskSafeHitTypeEnum.of(hitTypeCode);
    }
}
