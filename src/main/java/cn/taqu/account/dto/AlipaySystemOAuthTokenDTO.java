package cn.taqu.account.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 接口地址：https://opendocs.alipay.com/open/02qp09
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-09 15:54
 */
@Accessors(chain = true)
@Data
public class AlipaySystemOAuthTokenDTO {

    /**
     * 访问令牌。通过该令牌调用需要授权类接口
     */
    private String accessToken;
    private String alipayUserId;
    private String authTokenType;
    /**
     * 访问令牌的有效时间，单位是秒。
     */
    private String expiresIn;
    /**
     * 刷新令牌的有效时间，单位是秒。
     */
    private String reExpiresIn;
    /**
     * 刷新令牌。通过该令牌可以刷新access_token
     */
    private String refreshToken;
    /**
     * 支付宝用户的唯一标识。以2088开头的16位数字。
     */
    private String userId;
}
