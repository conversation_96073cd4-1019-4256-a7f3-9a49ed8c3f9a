package cn.taqu.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 买卖账户埋点帐户dto
 *
 * <AUTHOR>
 * @date 2021/09/01
 */
@Getter
@Setter
@ToString
public class TradingAccountDto {
    /**
     * 账户uuid
     */
    private String accountUuid;
    /**
     * token
     */
    private String token;
    /**
     * 帐户昵称
     */
    private String accountName;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * ip
     */
    private String ip;

    /**
     * 性别
     */
    private Integer sexType;


    /**
     * 命中时间
     */
    private Long hitTime;
}
