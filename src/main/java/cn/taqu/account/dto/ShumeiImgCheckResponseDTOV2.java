package cn.taqu.account.dto;

import cn.taqu.account.common.RiskDetectRiskLevelEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Classname ShumeiImgCheckResponseDTO
 * @Description 
 * @Date 2021/1/20 下午4:34
 */
@Getter
@Setter
public class ShumeiImgCheckResponseDTOV2 {
    private String requestId;//第三方流水号
    private String suggestion;//BLOCK拒绝，REVIEW 审核，PASS 通过
    private String violationType;// 违规类型
    private String checker;// 检测方，SHUMEI数美，TAQU他趣
    private String riskLabel1;// 一级标签   三级标签如果没有命中则为空
    private String riskLabel2;// 二级标签
    private String riskLabel3;//三级标签
    private String text; //ocr文字

    public static ShumeiImgCheckResponseDTO convertToShumeiImgCheckResponseDTO(ShumeiImgCheckResponseDTOV2 responseDTOV2) {
        ShumeiImgCheckResponseDTO dto = new ShumeiImgCheckResponseDTO();
        dto.setRequestId(responseDTOV2.getRequestId());
        dto.setRiskLevel(responseDTOV2.getSuggestion().equals(RiskDetectRiskLevelEnum.BLOCK.name()) ?
                RiskDetectRiskLevelEnum.REJECT.name() :
                responseDTOV2.getSuggestion());
        dto.setRiskDescription(responseDTOV2.riskLabel1 + responseDTOV2.riskLabel2 + responseDTOV2.riskLabel3 + "");
        return dto;
    }
}
