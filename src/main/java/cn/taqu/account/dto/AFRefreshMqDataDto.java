package cn.taqu.account.dto;

public class AFRefreshMqDataDto {
    private String accountUuid;
    private Integer appcode;
    private Integer cloned;
    private Long appVersion;
    private String ip;

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public Integer getAppcode() {
        return appcode;
    }

    public void setAppcode(Integer appcode) {
        this.appcode = appcode;
    }

    public Integer getCloned() {
        return cloned;
    }

    public void setCloned(Integer cloned) {
        this.cloned = cloned;
    }

    public Long getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(Long appVersion) {
        this.appVersion = appVersion;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
