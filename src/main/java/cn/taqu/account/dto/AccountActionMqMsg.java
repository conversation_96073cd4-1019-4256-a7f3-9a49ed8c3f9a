package cn.taqu.account.dto;

import cn.taqu.account.common.AccountActionTypeEnum;
import cn.taqu.account.common.RegisterLoginTypeEnum;

public class AccountActionMqMsg {
    private String accountUuid;
    private String accountName;
    private AccountActionTypeEnum actionType;
    private AccountActionTypeEnum accountStatus;

    private RegisterLoginTypeEnum registerLoginType;

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public AccountActionTypeEnum getActionType() {
        return actionType;
    }

    public void setActionType(AccountActionTypeEnum actionType) {
        this.actionType = actionType;
    }

    public AccountActionTypeEnum getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(AccountActionTypeEnum accountStatus) {
        this.accountStatus = accountStatus;
    }


    public RegisterLoginTypeEnum getRegisterLoginType() {
        return registerLoginType;
    }

    public void setRegisterLoginType(RegisterLoginTypeEnum registerLoginType) {
        this.registerLoginType = registerLoginType;
    }
}
