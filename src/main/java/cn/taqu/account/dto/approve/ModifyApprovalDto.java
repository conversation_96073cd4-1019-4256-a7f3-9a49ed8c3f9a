package cn.taqu.account.dto.approve;

import lombok.Data;

/**
 * @Description: 数据修改审批入参
 * @Author: zhangyj
 * @Date: 2023/11/23
 * @Vesion 1.0.1
 */
@Data
public class ModifyApprovalDto {

    private String originSys;

    private String menuCode;

    private String buttonCode;

    private String approvalCode;

    private String bizId;

    private String operationType;

    private String approvalFormParam;

    private String applyReason;

    private String modifyData;

    private CallBackUrlInfo callBackUrlInfo;

    private String fileUrl;

    private Long opTime;

    /**
     * callback 回调接口信息
     */
    @Data
    public static class CallBackUrlInfo {
        private String method;
        private String service;
        private String paramType;
        private String url;
    }

    @Data
    public static class FormParam {
        private String id;
        private String type = "input";
        private String value;
    }

    public enum ParamType {
        /**
         * 参数类型为body
         */
        body,
        /**
         * 参数类型为soaform
         */
        soaForm;
    }


}
