package cn.taqu.account.dto.approve;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 导出数据文件入参
 * @Author: zhangyj
 * @Date: 2023/11/23
 * @Vesion 1.0.1
 */
@Data
@Accessors(chain = true)
public class ViewApprovalDto {

    private String originSys;

    private String menuCode;

    private String buttonCode;

    private String approvalCode;

    private String approvalFormParam;

//    private String fileUrl;

    /**
     * 数据行为: 1查看,2导出,3修改
     */
    private Integer dataActionType;
    /**
     * 管控类型策略: 0飞书审批,1飞书消息,2记录日志
     */
    private Integer chargeDataType;

    private String applyReason;

    private Long opTime;

}
