package cn.taqu.account.dto;

import cn.taqu.account.common.AccountActionTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AccountAction {
    private Long id;

    private String accountUuid;

    private String token;

    private String accountName;

    private Integer sexType;

    private AccountActionTypeEnum actionType;

    private String actionIp;

    private String actionAttribution;

    private String phoneAttributionProvince;

    private String phoneAttributionCity;

    /**
     * 明文字段，需要再feature/phone_optimize分支上线之后下掉
     */
    @Deprecated
    private String phone;

    private String phoneMD5;

    private String phoneAttribution;

    private Integer aliyunFinalDecision;

    private Integer appcode;

    private Integer cloned;

    private Integer platformId;

    private String channel;

    private Long appVersion;

    private String actionMode;

    private Integer voiceCertification;

    private String aliTagsStr;

    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 网络情况，1:2G 2:3G 3:wifi 4:4G
     */
    private String access;
    /**
     * 别名
     */
    private String alias;
    /**
     * 性别 1:男 2:女
     */
    private Integer gender;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 城市
     */
    private Integer city;
}
