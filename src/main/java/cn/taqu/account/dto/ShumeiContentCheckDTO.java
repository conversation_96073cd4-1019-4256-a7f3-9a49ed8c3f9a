package cn.taqu.account.dto;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-14 15:36
 */
@ToString
@Data
public class ShumeiContentCheckDTO {

    private String requestId;//第三方流水号

    private String suggestion;//BLOCK拒绝，REVIEW 审核，PASS 通过，WAIT(机审通过，等待人审)

//    private Float score;//置信度

    private String violationType;// 违规类型

    private String body;//处理后的文本

//    private List<SensitiveLabel> sensitiveWordList;// 命中敏感词时才会返回

    private String model;// 命中的模型

    private String checker;// 检测方，SHUMEI数美，TAQU他趣

    private String riskLabel1;// 一级标签

    private String riskLabel2;// 二级标签

    private String riskLabel3;// 三级标签
    
    // 提示
    private String toast;

//    private Integer riskType; // 风险类型

//    @ToString
//    @Data
//    private static class SensitiveLabel {
//        private String name; // 敏感词名单
//
//        private List<String> words;//敏感词
//    }

    public String getReason() {
        return riskLabel1 + riskLabel2 + riskLabel3 + "";
    }

}
