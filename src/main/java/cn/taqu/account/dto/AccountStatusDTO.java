package cn.taqu.account.dto;

import lombok.Data;

/**
 * 用户状态
 *
 * <AUTHOR>
 * @date 2024/7/31 8:01 下午
 */
@Data
public class AccountStatusDTO {

    /**
     * 用户uuid
     */
    private String uuid;

    /**
     * 账号状态
     */
    private Integer status;

    /**
     * 注销时间
     */
    private Long destroyTime;

    /**
     * 获取注销时间
     *
     * @return
     */
    public Long getDestroyTime() {
        return this.destroyTime != null ? this.destroyTime / 1000 : null;
    }

}
