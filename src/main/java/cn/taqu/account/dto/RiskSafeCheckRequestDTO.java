package cn.taqu.account.dto;

import lombok.Data;

import java.util.List;

/**
 * 风控安全校验（https://api.admin.internal.taqu.cn/docs/api/api-1eh48gmvrqft6）
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Data
public class RiskSafeCheckRequestDTO {

    // 【必填】应用编码
    private Integer appCode;

    // 分身号
    private Integer cloned;

    // 【必填】规则ID
    private String safeId;

    // 【必填】业务唯一键
    private String bizId;

    // 【必填】设备ID（旧版本没有的默认传：oldClientPackage）
    private String deviceId;

    // 【必填】上传时间（时间戳，精确到秒）
    private Long postTime;

    // 【必填】发送用户uuid
    private String senderUuid;

    // 接收用户uuid
    private String receiverUuid;

    // 文本
    private String content;

    // 图片地址集合，注意是完整链接
    private List<String> imageUrl;

    // 音频地址集合，注意是完整链接
    private List<String> voiceFileUrl;

    // 扩展信息
    private Object extra;

    // 透传参数 ，业务方自定义
    private Object through;
}
