package cn.taqu.account.dto;

import cn.taqu.account.service.AliyunLiveFaceDetectService;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-11-30 17:30
 */
@Data
public class TencentImgQualityDTO {

    private Boolean LongImage;

    private Boolean BlackAndWhite;

    private Boolean SmallImage;

    private Boolean BigImage;

    private Boolean PureImage;

    private Integer ClarityScore;

    private Integer AestheticScore;

    private String RequestId;

    // 需要手动设置
    private String photoUrl;

    public Boolean isImgQualityPass(){
        return this.ClarityScore != null && this.ClarityScore >= AliyunLiveFaceDetectService.IMG_QUALITY_TENCENT_CLARITY_SCORE;
    }
}
