package cn.taqu.account.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 头像指导
 *
 * <AUTHOR>
 * @date 2025/6/23 14:28
 */
@Data
public class AvatarGuideDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String uuid;

    /**
     * 场景 1-更换头像，2-美颜
     */
    private Integer scene;

    /**
     * 地址
     */
    private String url;

    /**
     * 检查结果 success / fail
     */
    private String result;

    /**
     * 失败提示
     */
    private String error;

    public boolean isFail() {
        return Objects.equals(this.result, "fail");
    }
}
