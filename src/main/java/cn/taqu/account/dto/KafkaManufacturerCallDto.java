package cn.taqu.account.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> Wu.D.J
 */
@Getter
@Setter
@Builder
public class KafkaManufacturerCallDto {

    /**
     * 触发用户
     */
    private String uuid;

    /**
     * 调用结果:
     * true: 调用成功
     * false: 调用失败
     */
    private String callResult;

    /**
     * 厂商
     */
    private ManufacturerType manufacturer;

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 所属业务
     */
    private BelongBiz belongBiz;

    /**
     * 触发阶段：(只针对人脸核身场景)
     * 1.表下发订单号阶段
     * 2.表结果校验阶段
     */
    private String triggerPhase;

    /**
     * 预留扩展字段
     */
    private String ext;

    /**
     * 触发时间
     */
    private Long time;

    public enum ManufacturerType {
        TENCENT,
        QINIU,
        ALIYUN
    }

    public enum EventType {
        FACE_IDENTIFY,    // 人脸核身
        FACE_DETECT,      // 人脸识别
        PICTURE_ANALYSIS, // 图像分析
    }

    public enum BelongBiz {
        UNDEFINED,           // 未定义
        APP_CERTIFICATION,   // 应用实名认证
        CHAT_CERTIFICATION,  // 业务级实名认证
        REAL_PERSON,         // 真人认证
        CHANGE_MOBILE,       // 高风险用户手机号换绑
        CASH_MONITOR,        // 高风险用户金融监管
        WITHDRAW,        // 提现扫脸
        RETRIEVE_ACCOUNT,    // 旧账号找回
    }

    public static KafkaManufacturerCallDto tencentFaceIdentify(String uuid, BelongBiz biz, String triggerPhase, String callResult) {
        return build(ManufacturerType.TENCENT, EventType.FACE_IDENTIFY, uuid, biz, triggerPhase, callResult);
    }

    public static KafkaManufacturerCallDto tencentFaceDetect(String uuid, BelongBiz biz, String triggerPhase, String callResult) {
        return build(ManufacturerType.TENCENT, EventType.FACE_DETECT, uuid, biz, triggerPhase, callResult);
    }

    public static KafkaManufacturerCallDto tencentPicAnalysis(String uuid, BelongBiz biz, String triggerPhase, String callResult) {
        return build(ManufacturerType.TENCENT, EventType.PICTURE_ANALYSIS, uuid, biz, triggerPhase, callResult);
    }

    private static KafkaManufacturerCallDto build(ManufacturerType manufacturer, EventType eventType, String uuid, BelongBiz biz, String triggerPhase, String callResult) {
        return KafkaManufacturerCallDto.builder()
                .uuid(uuid)
                .callResult(callResult)
                .manufacturer(manufacturer)
                .eventType(eventType)
                .belongBiz(biz)
                .triggerPhase(triggerPhase)
                .time(DateUtil.currentSeconds())
                .build();
    }

}
