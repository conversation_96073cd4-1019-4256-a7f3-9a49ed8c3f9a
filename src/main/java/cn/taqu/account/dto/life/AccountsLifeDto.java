package cn.taqu.account.dto.life;

import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 * 2024年11月26日下午2:13:12
 */
@Data
public class AccountsLifeDto {

    
    /**
     * 图片地址（相对）
     */
    @NotBlank(message = "图片地址异常")
    private String photoUrl;

    /**
     * 图片描述
     */
    private String remark;
    
    /**
     * 图片宽
     */
    private Integer width;
    
    /**
     * 图片高
     */
    private Integer height;
    
    /**
     * 排序
     */
    @Range(min = 1, max = 5, message = "排序异常")
    private Integer seq;
}
