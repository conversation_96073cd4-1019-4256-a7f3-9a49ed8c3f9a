package cn.taqu.account.dto;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;


/**
 * 实名认证
 *
 * <AUTHOR>
 */
@Data
public class ConsultsDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    @NotNull(message = "提现账户不能为空")
    private String withdrawalAccount;
    @NotNull(message = "用户名字不能为空")
    private String accountRealName;
//    @NotNull(message = "外部流水号不能为空")
//    private String outBizNo;
//    @NotNull(message = "提现金额不能为空")
//    private Long applyAmount;

}
