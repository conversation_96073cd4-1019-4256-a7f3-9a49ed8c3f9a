package cn.taqu.account.dto;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.Data;

/**
 * 存储用户设备关系
 * 
 * <AUTHOR>
 * @date 2021/09/03
 */
@Data
public class AccountAndTokenInfoDto {

    public String accountUuid = "";
    public String token = "";
    public String time = "";
    public String latitude = "";
    public String longitude = "";
    public String ip = "";
    
    public Map<String,String> toMap(){
        Map<String,String> map = Maps.newHashMap();
        map.put("accountUuid", this.accountUuid);
        map.put("token", this.token);
        map.put("time", this.time);
        map.put("latitude", this.latitude);
        map.put("longitude", this.longitude);
        map.put("ip", this.ip);
        return map;
    }
}
