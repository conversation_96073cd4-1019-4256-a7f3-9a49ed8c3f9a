package cn.taqu.account.dto;

import cn.taqu.account.common.PlatformEnum;
import cn.taqu.core.protocol.SoaBaseParams;

/**
 * 这个配置用于2022年2月1期迭代开关
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-01 13:41
 */
public class SwitchDto20221027 {

    private final Long androidVersion;

    private final Long iosVersion;

    public SwitchDto20221027(Long androidVersion, Long iosVersion){
        this.androidVersion = androidVersion;
        this.iosVersion = iosVersion;
    }

    /**
     * 是否大于等于版本号
     * @return
     */
    public Boolean isGeVersion(){
        Integer platformId = SoaBaseParams.fromThread().getPlatform_id();
        Long appVersion = SoaBaseParams.fromThread().getApp_version();

        if(platformId == null || appVersion == null){
            return false;
        }
        if(PlatformEnum.ANDROID.getValue().equals(platformId)){
            return appVersion >= androidVersion;
        }else if(PlatformEnum.IPHONE.getValue().equals(platformId) || PlatformEnum.IPAD.getValue().equals(platformId)){
            return appVersion >= iosVersion;
        }else {
            return false;
        }
    }
}
