package cn.taqu.account.dto;

import cn.taqu.core.protocol.SoaBaseParams;
import lombok.Data;

/**
 * <AUTHOR> Wu.D.J
 */
@Data
public class KafkaUserInfoTrackingDTO {

    private Integer userSize;

    private String[] fields;

    private Long timestamp;
    private Long app_version;
    private Integer appcode;
    private Integer cloned;
    private String origin;
    private String service;
    private String method;
    private String clientUri;
    private String originSystem;
    private String env;

    public static KafkaUserInfoTrackingDTO create(String[] accountUuids, String[] fields) {
        KafkaUserInfoTrackingDTO kafkaUserInfoTrackingDTO = new KafkaUserInfoTrackingDTO();
        kafkaUserInfoTrackingDTO.setUserSize(accountUuids.length);
        kafkaUserInfoTrackingDTO.setFields(fields);

        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        kafkaUserInfoTrackingDTO.setTimestamp(soaBaseParams.getTimestamp());
        kafkaUserInfoTrackingDTO.setApp_version(soaBaseParams.getApp_version());
        kafkaUserInfoTrackingDTO.setAppcode(soaBaseParams.getAppcode());
        kafkaUserInfoTrackingDTO.setCloned(soaBaseParams.getCloned());
        kafkaUserInfoTrackingDTO.setOrigin(soaBaseParams.getOrigin());
        kafkaUserInfoTrackingDTO.setService(soaBaseParams.getService());
        kafkaUserInfoTrackingDTO.setMethod(soaBaseParams.getMethod());
        kafkaUserInfoTrackingDTO.setClientUri(soaBaseParams.getClientUri());
        kafkaUserInfoTrackingDTO.setOriginSystem(soaBaseParams.getOriginSystem());
        kafkaUserInfoTrackingDTO.setEnv(soaBaseParams.getEnv());

        return kafkaUserInfoTrackingDTO;
    }


}
