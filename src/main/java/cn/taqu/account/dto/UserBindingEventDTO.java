package cn.taqu.account.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/23 下午2:05
 */
@Data
public class UserBindingEventDTO implements Serializable {
    // 1.手机绑定 2.实名认证 3.业务实名认真 4.支付宝绑定
    private Integer eventType;

    // 1.绑定 2.换绑
    private Integer subType;

    private Long eventTime;

    private String cipher;

    private String uuid;

    private Integer appCode;

    private Integer cloned;

}
