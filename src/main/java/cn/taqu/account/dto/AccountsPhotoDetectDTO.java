package cn.taqu.account.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-14 15:18
 */
@Data
public class AccountsPhotoDetectDTO {

    private String photoUrl;

    /**
     * 全路径图片（可能为空）
     */
    private String fullPhotoUrl;

    /**
     * 数美检测结果
     */
    private ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO;

    /**
     * 图片质量结果
     */
    private TencentImgQualityDTO tencentImgQualityDTO;

    /**
     * 图片比对结果
     */
    private CompareFaceDto compareFaceDto;

    public Boolean isSuccess(){
        return shumeiImgCheckResponseDTO != null && tencentImgQualityDTO != null && compareFaceDto != null;
    }
}
