package cn.taqu.account.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 位置信息DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-10-12 16:03
 */
@Data
@Accessors(chain = true)
public class LocationInfoDTO {

    /**
     * 省份编码
     */
    private Integer province;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private Integer city;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 数据类型 1-离线ip库配数据，2-通过第三方接口获取的数据
     */
    private Integer dataSources;

    /**
     * 是否使用省会作为city 1-是，0-否
     */
    private Integer useProvincialCapital;

    /**
     * 地区名称
     */
    private String district;

}
