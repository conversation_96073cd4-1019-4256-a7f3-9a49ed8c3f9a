package cn.taqu.account.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 图形校验
 *
 * <AUTHOR>
 * @date 2024/09/10
 */
@Data
public class RiskPictureCheckRespDTO implements Serializable {

    /**
     * 许可状态 -1拒绝（限频、ip异常等） 0无需验证（放行） 1需要验证 2验证中
     */
    private Integer permitStatus;

    /**
     * 是否需要展示toast
     */
    private Integer isToast;

    /**
     * toast信息
     */
    private String toastMsg;

    /**
     * 返回给h5的跳转地址
     */
    private String captchaUrl;

    /**
     * 许可状态
     */
    @AllArgsConstructor
    public enum PermitStatus {

        /**
         * 拒绝
         */
        REJECT(-1),

        /**
         * 不需要验证
         */
        PASS(0),

        /**
         * 需要验证
         */
        NEED_VALID(1)

        ;
        /**
         * 状态
         */
        private Integer status;

        /**
         * 匹配
         *
         * @param code
         * @return
         */
        public boolean match(Integer code) {
            return Objects.equals(this.status, code);
        }
    }
}
