package cn.taqu.account.monitor;

import java.io.BufferedWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.taqu.core.utils.SpringContextHolder;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.exporter.common.TextFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-15 14:22
 */
public class TaquMetricesServlet extends HttpServlet {
    private CollectorRegistry registry;

    public TaquMetricesServlet() {
        this(CollectorRegistry.defaultRegistry);
    }

    public TaquMetricesServlet(CollectorRegistry registry) {
        this.registry = registry;
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setStatus(200);
        resp.setContentType("text/plain; version=0.0.4; charset=utf-8");
        BufferedWriter writer = new BufferedWriter(resp.getWriter());

        try {
            TextFormat.write004(writer, this.registry.filteredMetricFamilySamples(this.parse(req)));
            PrometheusMeterRegistry prometheusMeterRegistry = SpringContextHolder.getBean(PrometheusMeterRegistry.class);
            if(prometheusMeterRegistry != null){
                writer.write(prometheusMeterRegistry.scrape());
            }
            writer.flush();
        } finally {
            writer.close();
        }

    }

    private Set<String> parse(HttpServletRequest req) {
        String[] includedParam = req.getParameterValues("name[]");
        return (Set)(includedParam == null ? Collections.emptySet() : new HashSet(Arrays.asList(includedParam)));
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        this.doGet(req, resp);
    }
}
