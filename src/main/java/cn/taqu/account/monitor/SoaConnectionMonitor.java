package cn.taqu.account.monitor;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaClientListener;
import cn.taqu.core.common.client.SoaServer;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 下午4:02
 */
@Component
public class SoaConnectionMonitor implements SoaClientListener {

    public SoaConnectionMonitor() {
        SoaClientFactory.addClientListener(this);
    }

    @Override
    public void onCreate(SoaServer server, SoaClient client) {
        List<Tag> tag = Collections.singletonList(Tag.of("code", server.getCode()));
        Metrics.gauge("soa_pool_max", tag, client, SoaClient::getTotal);
        Metrics.gauge("soa_pool_available", tag, client, SoaClient::getAvailable);
        Metrics.gauge("soa_pool_pending", tag, client, SoaClient::getPending);
        Metrics.gauge("soa_pool_leased", tag, client, SoaClient::getLeased);
    }
}
