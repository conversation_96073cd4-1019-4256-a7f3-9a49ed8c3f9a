package cn.taqu.account.monitor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.alibaba.druid.pool.DruidDataSource;

import cn.taqu.core.datasource.MultiplyDataSource;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Wu.D.J
 * db 监控
 */
@Slf4j
@Component
public class DbConnectionMonitor implements ApplicationRunner {

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("db monitor start");
        MultiplyDataSource multiplyDataSource = (MultiplyDataSource) dataSource;
        Map<Object, Object> targetDataSources = multiplyDataSource.getTargetDataSources();
        DataSource psDataSource = (DataSource) targetDataSources.get("ps");
        monitor("primaryDs", psDataSource);
    }

    private void monitor(String k, DataSource ds) throws Exception {
        if (ds instanceof DruidDataSource) {
            log.info("druid ds monitor start");
            DruidDataSource druidDataSource = (DruidDataSource) ds;

            List<Tag> tags = Collections.singletonList(new ImmutableTag("poolName", k));
            Metrics.gauge("druid_active_count", tags, druidDataSource, DruidDataSource::getActiveCount);
            Metrics.gauge("druid_active_peak", tags, druidDataSource, DruidDataSource::getActivePeak);
            Metrics.gauge("druid_error_count", tags, druidDataSource, DruidDataSource::getErrorCount);
            Metrics.gauge("druid_execute_count", tags, druidDataSource, DruidDataSource::getExecuteCount);
            Metrics.gauge("druid_max_active", tags, druidDataSource, DruidDataSource::getMaxActive);
            Metrics.gauge("druid_min_idle", tags, druidDataSource, DruidDataSource::getMinIdle);
            Metrics.gauge("druid_max_wait", tags, druidDataSource, DruidDataSource::getMaxWait);
            Metrics.gauge("druid_max_wait_thread_count", tags, druidDataSource, DruidDataSource::getMaxWaitThreadCount);
            Metrics.gauge("druid_pooling_count", tags, druidDataSource, DruidDataSource::getPoolingCount);
            Metrics.gauge("druid_pooling_peak", tags, druidDataSource, DruidDataSource::getPoolingPeak);
            Metrics.gauge("druid_rollback_count", tags, druidDataSource, DruidDataSource::getRollbackCount);
            Metrics.gauge("druid_wait_thread_count", tags, druidDataSource, DruidDataSource::getWaitThreadCount);

            Metrics.gauge("druid_pool_error_count", tags, druidDataSource, DruidDataSource::getErrorCount);
            Metrics.gauge("druid_pool_active_connections", tags, druidDataSource, DruidDataSource::getActiveCount);
            Metrics.gauge("druid_pool_idle_connections", tags, druidDataSource, DruidDataSource::getPoolingCount);
            Metrics.gauge("druid_pool_sum_connections", tags, druidDataSource, dataSource -> (dataSource.getPoolingCount() + dataSource.getActiveCount()));
            Metrics.gauge("druid_pool_wait_connection_thread", tags, druidDataSource, DruidDataSource::getWaitThreadCount);

        }
    }
}
