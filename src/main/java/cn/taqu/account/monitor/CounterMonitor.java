package cn.taqu.account.monitor;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.extern.slf4j.Slf4j;

/**
 * Prometheus打点计数监控
 * 
 * <AUTHOR>
 * 2023年8月10日上午11:26:26
 */
@Slf4j
public class CounterMonitor {

    /**
     * reportAccountInfo 接口传参计数
     * @param versionNumber
     */
    public static void reportAccountInfo(int versionNumber) {
        try {
            Counter counter = Metrics.counter("reportAccountInfo_versionNumber", 
                "versionNumber", String.valueOf(versionNumber)
                );
            counter.increment();
        } catch (Exception e) {
            log.warn("reportAccountInfo打点失败", e);
        }
        
    }
    
    
}
