package cn.taqu.account.configuration;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Configuration
public class SpringRedisConfig {

    @Resource
    private Environment env;

    @Bean(name = "ticketRedisConnectionFactory")
    public RedisConnectionFactory ticketRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.ticket.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.ticket.port", Integer.class));
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(500);
        jedisPoolConfig.setMinIdle(10);
        jedisPoolConfig.setMaxIdle(50);
        jedisConnectionFactory.setPoolConfig(jedisPoolConfig);
        return jedisConnectionFactory;
    }

    @Bean(name = "ticketStringRedisTemplate")
    public StringRedisTemplate ticketStringRedisTemplate(@Qualifier("ticketRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "accountRedisConnectionFactory")
    @Primary
    public RedisConnectionFactory accountRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.account.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.account.port", Integer.class));
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(512);
        jedisPoolConfig.setMinIdle(16);
        jedisPoolConfig.setMaxIdle(64);
        jedisConnectionFactory.setPoolConfig(jedisPoolConfig);
        return jedisConnectionFactory;
    }

    @Bean(name = "accountStringRedisTemplate")
    public StringRedisTemplate accountStringRedisTemplate(@Qualifier("accountRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "accountBizRedisConnectionFactory")
    public RedisConnectionFactory accountBizRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.accountBiz.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.accountBiz.port", Integer.class));
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(500);
        jedisPoolConfig.setMinIdle(10);
        jedisPoolConfig.setMaxIdle(50);
        jedisConnectionFactory.setPoolConfig(jedisPoolConfig);
        return jedisConnectionFactory;
    }

    @Bean(name = "accountBizStringRedisTemplate")
    public StringRedisTemplate accountBizStringRedisTemplate(@Qualifier("accountBizRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "uuidRedisConnectionFactory")
    public RedisConnectionFactory uuidRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.uuid.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.uuid.port", Integer.class));
        return jedisConnectionFactory;
    }

    @Bean(name = "uuidStringRedisTemplate")
    public StringRedisTemplate uuidStringRedisTemplate(@Qualifier("uuidRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "privilegeRedisConnectionFactory")
    public RedisConnectionFactory privilegeRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.privilege.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.privilege.port", Integer.class));
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(500);
        jedisPoolConfig.setMinIdle(10);
        jedisPoolConfig.setMaxIdle(50);
        jedisConnectionFactory.setPoolConfig(jedisPoolConfig);
        return jedisConnectionFactory;
    }

    @Bean(name = "privilegeStringRedisTemplate")
    public StringRedisTemplate privilegeStringRedisTemplate(@Qualifier("privilegeRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "levelRedisConnectionFactory")
    public RedisConnectionFactory levelRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.level.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.level.port", Integer.class));
        return jedisConnectionFactory;
    }

    @Bean(name = "levelStringRedisTemplate")
    public StringRedisTemplate levelStringRedisTemplate(@Qualifier("levelRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "medalRedisConnectionFactory")
    public RedisConnectionFactory medalRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.medal.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.medal.port", Integer.class));
        return jedisConnectionFactory;
    }

    @Bean(name = "medalStringRedisTemplate")
    public StringRedisTemplate medalStringRedisTemplate(@Qualifier("medalRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "snRedisConnectionFactory")
    public RedisConnectionFactory snRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.sn.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.sn.port", Integer.class));
        return jedisConnectionFactory;
    }

    @Bean(name = "snStringRedisTemplate")
    public StringRedisTemplate snStringRedisTemplate(@Qualifier("snRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "regionRedisConnectionFactory")
    public RedisConnectionFactory regionRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.region.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.region.port", Integer.class));
        return jedisConnectionFactory;
    }

    @Bean(name = "regionStringRedisTemplate")
    public StringRedisTemplate regionStringRedisTemplate(@Qualifier("regionRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "kickRedisConnectionFactory")
    public RedisConnectionFactory kickRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.kick.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.kick.port", Integer.class));
        return jedisConnectionFactory;
    }

    @Bean(name = "kickStringRedisTemplate")
    public StringRedisTemplate kickStringRedisTemplate(@Qualifier("kickRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    @Bean(name = "lockRedisConnectionFactory")
    public RedisConnectionFactory lockRedisConnectionFactory() {
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(env.getRequiredProperty("redis.lock.host"));
        jedisConnectionFactory.setPort(env.getRequiredProperty("redis.lock.port", Integer.class));
        return jedisConnectionFactory;
    }

    @Bean(name = "lockStringRedisTemplate")
    public StringRedisTemplate lockStringRedisTemplate(@Qualifier("lockRedisConnectionFactory") RedisConnectionFactory cf) {
        return new StringRedisTemplate(cf);
    }

    //@Bean(destroyMethod = "shutdown")
    //RedissonClient redissonBizClient() {
    //    return redissonClient(env.getRequiredProperty("redis.accountBiz.host"),
    //            env.getRequiredProperty("redis.accountBiz.port", Integer.class));
    //}

    //private RedissonClient redissonClient(String host, int port) {
    //    Config config = new Config();
    //    config.setUseScriptCache(true);
    //    config.setCodec(new Kryo5Codec());
    //    SingleServerConfig singleConfig = config.useSingleServer();
    //    singleConfig.setAddress("redis://" + host + ":" + port);
    //    singleConfig.setConnectionPoolSize(512);
    //    singleConfig.setIdleConnectionTimeout(30000);
    //    singleConfig.setConnectTimeout(3000);
    //    singleConfig.setTimeout(2000);
    //    singleConfig.setRetryInterval(2000);
    //    singleConfig.setKeepAlive(true);
    //    return Redisson.create(config);
    //}
}
