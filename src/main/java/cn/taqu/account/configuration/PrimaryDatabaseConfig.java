package cn.taqu.account.configuration;

import cn.taqu.account.filter.DruidCharsetFilter;
import cn.taqu.core.configuration.BaseDataSourceConfig;
import cn.taqu.core.datasource.MultiplyDataSource;
import cn.taqu.core.orm.Hibernates;
import com.alibaba.druid.pool.DruidDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 多数据源配置
 *
 * <AUTHOR>
 * @since 1.0
 */
@Configuration
@EnableTransactionManagement
//@PropertySource({"classpath:application.properties"}) // 使用evn，没有自动装备，无需此配置
@EnableJpaRepositories(basePackages = {"cn.taqu.account.dao"},
        entityManagerFactoryRef = "primaryEntityManagerFactory",
        transactionManagerRef = "primaryTransactionManager")
public class PrimaryDatabaseConfig extends BaseDataSourceConfig {

    private Logger logger = LoggerFactory.getLogger(PrimaryDatabaseConfig.class);

    @Resource
    private Environment env;

    private DataSource primaryDataSource() {
        logger.info("-------------------- primaryDataSource init ---------------------");
        DruidDataSource dataSource =  druidDataSource(env.getProperty("datasource.primary.url"),
                env.getProperty("datasource.primary.username"), env.getProperty("datasource.primary.password"));
        dataSource.getProxyFilters().add(new DruidCharsetFilter());
        return dataSource;
    }

    @Bean(name = "dataSource")
    @Primary
    public DataSource multiplyDataSource() {
        MultiplyDataSource multiplyDataSource = new MultiplyDataSource();
        Map<Object, Object> targetDataSources = new HashMap<>();
        DataSource ps = primaryDataSource();
        targetDataSources.put("ps", ps);
        multiplyDataSource.setTargetDataSources(targetDataSources);
        multiplyDataSource.setDefaultTargetDataSource(ps);
        return multiplyDataSource;
    }

    @Bean(name = "primaryEntityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean primaryEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder.dataSource(multiplyDataSource())
                .properties(jpaProperties())
                .packages("cn.taqu") // cn.taqu.account.model
                //.persistenceUnit("system")
                .build();
    }

    private Map<String, Object> jpaProperties() {
        Map<String, Object> props = new HashMap<>();
        props.put("hibernate.show_sql", false);
        props.put("hibernate.dialect", Hibernates.getDialect(multiplyDataSource()));
        //props.put("hibernate.ejb.naming_strategy", new SpringNamingStrategy());//看源码中好像有根据hibernate4 5自行选择
        return props;
    }

    @Bean
    public PersistenceExceptionTranslationPostProcessor exceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    @Bean(name = "primaryTransactionManager")
    @Primary
    public PlatformTransactionManager primaryTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(primaryEntityManagerFactory(builder).getObject());
    }

}
