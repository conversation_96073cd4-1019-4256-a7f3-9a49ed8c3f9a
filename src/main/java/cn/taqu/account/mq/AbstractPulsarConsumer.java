package cn.taqu.account.mq;

import cn.taqu.account.etcd.PulsarFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.MessageListener;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.context.SmartLifecycle;

/**
 * <AUTHOR>
 * @date 2025/2/19 上午9:52
 */
@Slf4j
public abstract class AbstractPulsarConsumer implements SmartLifecycle, MessageListener<String> {

    protected Integer                   threads = 1;

    private final String[]                topic;

    private final PulsarFactory   pulsarFactory;

    private volatile boolean            running;

    protected AbstractPulsarConsumer(PulsarFactory pulsarFactory, String... topic) {
        this.pulsarFactory = pulsarFactory;
        this.topic = topic;
    }

    @Override
    public boolean isRunning() {
        return running;
    }

    @Override
    public void start() {
        running = true;
        int threads = Math.min(Runtime.getRuntime().availableProcessors() - 1, this.threads);
        for (int i = 0; i < threads; i++) {
            pulsarFactory.subscribe(this, topic);
        }
    }

    @Override
    public void stop() {
        running = false;
    }

    @Override
    public void received(Consumer<String> consumer, Message<String> msg) {
        if (!running) {
            log.warn("进程已下线");
            return;
        }
        onMessage(consumer, msg);
    }

    protected void ack(Consumer<String> consumer, Message<String> msg) {
        try {
            consumer.acknowledge(msg);
        } catch (PulsarClientException e) {
            String err = "pulsar ack err" + msg.getTopicName();
            log.error(err, e);
        }
    }

    @Override
    public int getPhase() {
        return Integer.MAX_VALUE;
    }

    @Override
    public boolean isAutoStartup() {
        return true;
    }

    @Override
    public void stop(Runnable callback) {
        stop();
        callback.run();
    }

    protected abstract void onMessage(Consumer<String> consumer, Message<String> msg);

}
