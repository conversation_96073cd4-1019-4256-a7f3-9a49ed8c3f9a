package cn.taqu.account.mq;

import cn.taqu.account.constant.AvatarGuidePopupEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AvatarGuidePopupRecordDao;
import cn.taqu.account.dao.TransactionWrapper;
import cn.taqu.account.dto.AvatarGuideDTO;
import cn.taqu.account.etcd.AvatarGuideConfig;
import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.account.model.AvatarGuidePopupRecord;
import cn.taqu.account.soa.UserRatingService;
import cn.taqu.account.utils.DateHelper;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.util.concurrent.TimeUnit;

/**
 * 头像指导
 *
 * <AUTHOR>
 * @date 2025/6/24 10:36
 */
@Slf4j
@Component
public class AvatarGuideConsumer extends AbstractPulsarConsumer {

    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;
    @Resource
    private AvatarGuidePopupRecordDao avatarGuidePopupRecordDao;
    @Resource
    private UserRatingService userRatingService;

    public AvatarGuideConsumer(PulsarFactory pulsarFactory) {
        super(pulsarFactory, "persistent://middle_tenant/alg_ns/avatar_quality_improvement");
    }

    @Override
    protected void onMessage(Consumer<String> consumer, Message<String> msg) {
        log.info("收到用户头像引导:{}", msg.getValue());
        process(msg.getValue());
        ack(consumer, msg);
    }

    /**
     * 逻辑处理
     *
     * @param value
     */
    public void process(String value) {
        AvatarGuideDTO avatar = JsonUtils.stringToObject(value, AvatarGuideDTO.class);
        if (avatar == null) {
            return;
        }

        // 算法调用失败场景
        if (avatar.isFail()) {
            putFailQueue(avatar);
            return;
        }

        // 是否已美颜过
        if (hasBeauty(avatar.getUuid(), avatar.getScene())) {
            return;
        }

        // 检查是否已经有记录
        Integer today = DateHelper.getDt(LocalDate.now());
        AvatarGuidePopupRecord record = avatarGuidePopupRecordDao.find(avatar.getUuid());
        Long now = Instant.now().toEpochMilli();
        if (record != null) {
            // 校验是否重复提示美颜(防止算法侧重复计算通知)
            if (!validRepetition(avatar, record)) {
                return;
            }
        } else {
            record = new AvatarGuidePopupRecord();
            record.setAccountUuid(avatar.getUuid());
            record.setCreateTime(now);
        }
        record.setCheckDate(today);
        record.setPopupType(avatar.getScene());
        record.setPopupDate(0);
        record.setAvatarUrl(avatar.getUrl());
        record.setUpdateTime(now);
        AvatarGuidePopupRecord finalRecord = record;
        TransactionWrapper.me().wrap(() -> {
            avatarGuidePopupRecordDao.merge(finalRecord);
        });

        String key = RedisKeyConstant.AVATAR_GUIDE_POPUP.setArg(avatar.getUuid());
        accountBizStringRedisTemplate.opsForValue().set(key, value, AvatarGuideConfig.CONFIG.getBiz().getCircleDay(), TimeUnit.DAYS);
    }

    /**
     * 是否美颜过
     *
     * @param uuid
     * @return
     */
    private boolean hasBeauty(String uuid, Integer scene) {
        if (!AvatarGuidePopupEnum.BEAUTY.match(scene)) {
            return false;
        }

        String key = RedisKeyConstant.AVATAR_HAS_BEAUTY.setArg(uuid);
        String value = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(value)) {
            log.info("头像已美颜过,{}", uuid);
            return true;
        }

        return false;
    }

    /**
     * 重复校验判断（这里理论上是不会走到这里，出现重复计算可能是数据上报出现问题？或者上传相同头像导致）
     *
     * @param avatar
     * @param record
     * @return
     */
    private boolean validRepetition(AvatarGuideDTO avatar, AvatarGuidePopupRecord record) {
        // 还没弹窗过
        if (record.getPopupDate() == 0) {
            return true;
        }

        LocalDate localDate = DateHelper.parseToDate(record.getPopupDate().toString());
        boolean after = localDate.plusDays(AvatarGuideConfig.CONFIG.getBiz().getCircleDay()).isAfter(LocalDate.now());
        if (after) {
            log.warn("头像引导弹窗处于重复期间内，data:{}, record:{}", JsonUtils.objectToString(avatar), JsonUtils.objectToString(record));
            return false;
        }

        return true;
    }

    /**
     * 失败队列
     *
     * @param avatar
     */
    private void putFailQueue(AvatarGuideDTO avatar) {
        log.warn("算法头像比对失败:{}", JsonUtils.objectToString(avatar));
        accountBizStringRedisTemplate.opsForSet().add(
                RedisKeyConstant.AVATAR_GUIDE_ERROR_QUEUE, avatar.getUuid());
    }

}
