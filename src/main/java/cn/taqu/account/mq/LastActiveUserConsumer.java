package cn.taqu.account.mq;

import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.concurrent.TimeUnit;

import static cn.taqu.account.constant.RedisKeyConstant.LAST_ACTIVE_USER_QUEUE_30;
import static cn.taqu.account.constant.RedisKeyConstant.USER_PHOTO_DETECT_VERSION_SET;

/**
 *
 * <AUTHOR>
 * @date 2025/5/12 下午2:23
 */
@Slf4j
@Component
public class LastActiveUserConsumer extends AbstractPulsarConsumer {

    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;

    public LastActiveUserConsumer(PulsarFactory factory) {
        super(factory, "persistent://middle_tenant/mp_ns/account_active_user_30");
    }

    @Override
    protected void onMessage(Consumer<String> consumer, Message<String> msg) {
        log.info("收到近30活跃用户 {}", msg.getValue());
        User user = JsonUtils.stringToObject(msg.getValue(), User.class);
        // 活跃用户缓冲队列
        boolean processed = accountBizStringRedisTemplate.opsForSet().isMember(USER_PHOTO_DETECT_VERSION_SET, user.getAccountUuid());
        if (BooleanUtils.isTrue(processed)) {
            log.info("已处理用户{}忽略", user.getAccountUuid());
        } else {
            accountBizStringRedisTemplate.opsForList().rightPush(LAST_ACTIVE_USER_QUEUE_30, user.getAccountUuid());
            accountBizStringRedisTemplate.opsForSet().add(USER_PHOTO_DETECT_VERSION_SET, user.getAccountUuid());
        }
        ack(consumer, msg);
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class User implements Serializable {

        private String accountUuid;

        private String tag;
    }
}
