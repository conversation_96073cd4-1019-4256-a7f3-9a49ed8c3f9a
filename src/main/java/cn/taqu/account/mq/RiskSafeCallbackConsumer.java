package cn.taqu.account.mq;

import cn.taqu.account.common.AllureSceneEnum;
import cn.taqu.account.common.LockKey;
import cn.taqu.account.common.RiskSafeHitTypeEnum;
import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.constant.ActionEventEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RiskSafeIdConstant;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.account.event.ActionNoticeReport;
import cn.taqu.account.event.EventTrackReporter;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.service.*;
import cn.taqu.account.soa.AIGCSoaService;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.Identities;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.Schema;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

import static cn.taqu.account.event.EventConst.ACCOUNT_AVATAR_EVENT_PULSAR_TOPIC;

/**
 * 风控安全审核回调（https://o15vj1m4ie.feishu.cn/wiki/Mfigwy61YiMl0VkmsXYctDpcnQG）
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskSafeCallbackConsumer implements PulsarFactory.ConfigListener {

    private static final String RISK_SAFE_CALLBACK_TOPIC = "risk_safe_center_callback_user";

    private final VoiceSignInfoService voiceSignInfoService;

    private final AccountsHomeCoverService accountsHomeCoverService;

    private final NickNameRiskVerifyService nickNameRiskVerifyService;

    private final PersonalProfileInfoService personalProfileInfoService;

    private final AccountsIntroductionService accountsIntroductionService;

    private final AccountsPhotoService accountsPhotoService;

    private final SchoolService schoolService;

    private final IdealTargetService idealTargetService;

    private final AccountsLifeService accountsLifeService;

    private final RedisLockUtil redisLockUtil;

    private final EventTrackReporter eventTrackReporter;

    private final AllureService allureService;

    private final PulsarFactory pulsarFactory;

    private final ActionNoticeReport actionNoticeReport;

    public volatile Producer<String> avatarEventProducer;

    private final AIGCSoaService aigcSoaService;

    @PostConstruct
    public void init() {
        avatarEventProducer = pulsarFactory.producer(ACCOUNT_AVATAR_EVENT_PULSAR_TOPIC);
    }

    @Override
    public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
        Producer<String> origin = avatarEventProducer;
        this.avatarEventProducer = factory.producer(ACCOUNT_AVATAR_EVENT_PULSAR_TOPIC);
        origin.closeAsync();
    }

    /**
     * 风控安全回调
     *
     * @param records
     */
    @KafkaListener(topics = {"#{T(cn.taqu.account.utils.EnvUtil).getTopicName(\"" + RISK_SAFE_CALLBACK_TOPIC + "\")}"}, containerFactory = "kafkaListenerContainerFactory")
    public void process(List<ConsumerRecord<String, String>> records) {
        records.parallelStream().forEach(this::consumer);
    }

    public void consumer(ConsumerRecord<String, String> record) {
        String message = record.value();
        RiskSafeCheckResponseDTO dto = JsonUtils.stringToObject(message, RiskSafeCheckResponseDTO.class);
        SoaBaseParams.fromThread().setDistinctRequestId(Identities.uuid2());
        if (StringUtils.isNotBlank(dto.getAppCode())) {
            SoaBaseParams.fromThread().setAppcode(dto.getAppCode());
        }
        if (StringUtils.isNotBlank(dto.getCloned())) {
            SoaBaseParams.fromThread().setCloned(dto.getCloned());
        }
        log.info("[风控安全审核]审核回调,accountUuid:{},message:{}", dto.getSenderUuid(), message);
        if (StringUtils.isBlank(dto.getSafeId())) {
            return;
        }
        auditProcess(dto);
    }

    public void auditProcess(RiskSafeCheckResponseDTO dto) {
        String safeId = dto.getSafeId();
        switch (safeId) {
            case RiskSafeIdConstant.INTRODUCTION:
                // 自我介绍回调
                accountsIntroductionService.audit(dto);
                reportIfPass(dto, "自我介绍");
                break;
            case RiskSafeIdConstant.TEXT_PERSONAL_PROFILE:
                // 个人简介回调
                personalProfileInfoService.audit(dto);
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.CARD, null);
                reportIfPass(dto, "个性签名");
                break;
            case RiskSafeIdConstant.AUDIO_SIGN:
                // 语音签名回调
                voiceSignInfoService.audit(dto);
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.CARD, null);
                break;
            case RiskSafeIdConstant.IMAGE_COVER:
                // 个人封面回调
                accountsHomeCoverService.audit(dto);
                reportIfPass(dto, "个人封面");
                break;
            case RiskSafeIdConstant.TEXT_NICKNAME:
                // 昵称审核回调
                nickNameRiskVerifyService.audit(dto);
                reportIfPass(dto, "昵称");
                break;
            case RiskSafeIdConstant.REAL_PERSON_AVATAR_OLD:
            case RiskSafeIdConstant.REAL_PERSON_AVATAR:
                // 真人认证头像
                accountsPhotoAudit(dto, 1);
                AccountsPhoto avatar = accountsPhotoService.getAccountAvatar(dto.getSenderUuid(), null, true);
                String url = Optional.ofNullable(avatar).map(AccountsPhoto::getPhoto_url).orElse("");
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.AVATAR, url);
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.CARD, null);
                if (RiskSafeHitTypeEnum.PASS.equals(dto.getRiskSafeHitType())) {
                    aigcSoaService.avatarQuality(dto.getSenderUuid(), url);
                }

                reportIfPass(dto, "真人认证头像");
                break;
            case RiskSafeIdConstant.NOT_REAL_PERSON_AVATAR_OLD:
            case RiskSafeIdConstant.NOT_REAL_PERSON_AVATAR:
                // 非真人认证头像
                accountsPhotoAudit(dto, 2);
                AccountsPhoto avatar1 = accountsPhotoService.getAccountAvatar(dto.getSenderUuid(), null, true);
                String url1 = Optional.ofNullable(avatar1).map(AccountsPhoto::getPhoto_url).orElse("");
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.AVATAR, url1);
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.CARD, null);
                reportIfPass(dto, "非真人认证头像");
                break;
            case RiskSafeIdConstant.REAL_PERSON_COVER:
                // 真人认证相册
                accountsPhotoAudit(dto, 3);
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.CARD, null);
                reportIfPass(dto, "真人认证相册");
                break;
            case RiskSafeIdConstant.TEXT_MAJOR:
                // 专业回调
                schoolService.audit(dto);
                reportIfPass(dto, "专业");
                break;
            case RiskSafeIdConstant.TEXT_IDEAL:
                // 理想型
                idealTargetService.audit(dto);
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.CARD, null);
                reportIfPass(dto, "我的理想型");
                break;
            case RiskSafeIdConstant.ACCOUNTS_LIFE:
            case RiskSafeIdConstant.ACCOUNTS_LIFE_REAL_PERSON:
                // 我的生活
                accountsLifeService.audit(dto);
                allureService.refreshAllureV2(dto.getSenderUuid(), AllureSceneEnum.CARD, null);
                reportIfPass(dto, "我的生活（相册）");
                break;
            default:
                log.info("[风控安全审核]审核回调未处理类型,accountUuid:{},safeId:{}", dto.getSenderUuid(), dto.getSafeId());
                break;
        }
    }

    private void reportIfPass(RiskSafeCheckResponseDTO dto, String position) {
        if (RiskSafeHitTypeEnum.PASS.equals(dto.getRiskSafeHitType())) {
            // 埋点上报
            Map<String, Object> payload = new HashMap<>(2);
            payload.put("position", position);
            eventTrackReporter.report(dto.getSenderUuid(), "my_info_audit_success_new", payload);
            if ("真人认证头像".equals(position) || "非真人认证头像".equals(position)) {
                Map<String, Map<String, Object>> map = SpringContextHolder.getBean(AccountsInfoService.class).getInfoByUuid(new String[]{ dto.getSenderUuid() }, new String[] { UuidInfoField.CREATE_TIME, UuidInfoField.SEX_TYPE, UuidInfoField.AVATAR}, null, true, false);
                Map<String, Object> info = map.get(dto.getSenderUuid());
                String val = JsonUtils.objectToString(info);
                avatarEventProducer.newMessage(Schema.STRING)
                    .key(dto.getSenderUuid())
                    .value(val)
                    .sendAsync().whenComplete((messageId, throwable) -> {
                        if (throwable != null) {
                            log.info("头像审核推送给算法失败 {}", dto.getSenderUuid());
                        } else {
                            log.info("头像审核推送给算法成功 {} {}", dto.getSenderUuid(), val);
                        }
                    });
            }
        }
    }


    /**
     * 风控安全审核
     *
     * @param dto 消息内容
     * @param checkType  1-真人认证头像 2-非真人认证头像 3-真人相册
     */
    public void accountsPhotoAudit(RiskSafeCheckResponseDTO dto, int checkType) {
        log.info("[风控安全审核]真人/非真人图片审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{},checkType:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason(), checkType);
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        Map<Long, String> photoMap = getPhotoMap(dto);
        String accountUuid = dto.getSenderUuid();

        String lockKey = RedisKeyConstant.REDIS_LOCK_KEY.setArg(LockKey.FACE_COMPARE_AND_AUDIT, accountUuid);
        redisLockUtil.executeWithLock(lockKey, 3000, () -> {
            accountsPhotoService.auditProcess(accountUuid, dto.getOperator(), checkType, riskSafeHitTypeEnum, photoMap);
            // 头像过审需要上报
            if (RiskSafeHitTypeEnum.PASS.equals(dto.getRiskSafeHitType()) && (checkType == 1 || checkType == 2)) {
                actionNoticeReport.report(dto.getSenderUuid(), ActionEventEnum.AVATAR_UPLOAD_AUDIT_PASS);
            }
            return true;
        });
    }

    private Map<Long, String> getPhotoMap(RiskSafeCheckResponseDTO dto) {
        Map<Long, String> photoMap = new HashMap<>();
        if (Objects.equals("1", dto.getIsOldData())) {
            Long photoId = dto.getPhotoId();
            if (Objects.nonNull(photoId)) {
                photoMap.put(photoId, dto.getPhotoUrl());
            }
        } else {
            Long bizId = Long.parseLong(dto.getRealBizId());
            photoMap.put(bizId, dto.getPhotoUrlByThrough());
        }
        return photoMap;
    }

}
