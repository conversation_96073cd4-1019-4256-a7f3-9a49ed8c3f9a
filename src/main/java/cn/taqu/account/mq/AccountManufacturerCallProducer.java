package cn.taqu.account.mq;

import cn.taqu.account.dto.KafkaManufacturerCallDto;
import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Producer;
import org.springframework.stereotype.Component;

import static cn.taqu.account.event.EventConst.ACCOUNT_MANUFACTURER_CALL_PULSAR_TOPIC;

/**
 * <AUTHOR>
 * @date 2025/6/16 下午6:26
 */
@Slf4j
@Component
public class AccountManufacturerCallProducer implements PulsarFactory.ConfigListener {

    public volatile Producer<String> producer;

    public AccountManufacturerCallProducer(PulsarFactory factory) {
        this.producer = factory.producer(ACCOUNT_MANUFACTURER_CALL_PULSAR_TOPIC);
        factory.addListener(this);
    }

    @Override
    public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
        Producer<String> origin = producer;
        this.producer = factory.producer(ACCOUNT_MANUFACTURER_CALL_PULSAR_TOPIC);
        origin.closeAsync();
    }

    public void send(KafkaManufacturerCallDto dto) {
        producer.newMessage()
                .key(dto.getUuid())
                .value(JsonUtils.objectToString(dto))
                .sendAsync().thenAccept(m -> log.info("厂商调用事件推送成功"));
    }
}
