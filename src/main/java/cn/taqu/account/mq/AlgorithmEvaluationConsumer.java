package cn.taqu.account.mq;

import cn.taqu.account.common.AllureSceneEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.account.service.AllureService;
import cn.taqu.core.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 算法 颜值分 真诚度分
 * <AUTHOR>
 * @date 2025/5/12 下午2:23
 */
@Slf4j
@Component
public class AlgorithmEvaluationConsumer extends AbstractPulsarConsumer {

    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;

    @Resource
    private AllureService allureService;

    public AlgorithmEvaluationConsumer(PulsarFactory factory) {
        super(factory, "persistent://middle_tenant/mp_ns/account_profile_al_score");
    }

    @Override
    protected void onMessage(Consumer<String> consumer, Message<String> msg) {
        log.info("收到算法打分 {}", msg.getValue());
        AlEvaluation eval = JsonUtils.stringToObject(msg.getValue(), AlEvaluation.class);
        if ("success".equals(eval.getResult())) {
            allureService.algorithmScoreCallback(eval.uuid, AllureSceneEnum.valOf(eval.scene), eval.score);
        } else {
            // 兜底
            String key = RedisKeyConstant.ACCOUNT_AllURE_V2.setArg(eval.uuid);
            accountBizStringRedisTemplate.delete(key);
            log.warn("算法打分回调失败，清理吸引力缓存 {}", key);
        }
        ack(consumer, msg);
    }

    @Data
    public static class AlEvaluation implements Serializable {

        private String uuid;

        private Integer scene;

        private BigDecimal score;

        private String result;

        private String error;
    }
}
