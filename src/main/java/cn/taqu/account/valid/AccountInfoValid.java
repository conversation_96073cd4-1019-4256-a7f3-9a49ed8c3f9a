package cn.taqu.account.valid;

import cn.taqu.core.exception.ServiceException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 用户信息校验
 *
 * <AUTHOR>
 * @date 2024/11/19 14:05
 */
public class AccountInfoValid {

    /**
     * 行业
     */
    private static final List<String> TRADE = Lists.newArrayList("服务业", "金融", "IT互联网", "生产制造", "房产建筑", "轻工贸易", "教育培训", "文化传媒", "农林牧渔", "通信电子", "医疗生物", "能源环保", "其他");

    /**
     * 职业
     */
    private static final List<String> PROFESSION = Lists.newArrayList("市场销售", "娱乐/餐饮", "经营管理", "个体/网店", "交通物流", "服务/接班", "美容/造型师", "医疗健康", "酒店/旅游", "行政后勤", "人力HR", "机电维修", "安保", "运动健身", "摄影婚庆", "家政保洁", "客服", "销售/理财", "银行", "财税/审计", "证券", "高管", "市场商务", "风投/投行", "担保/信贷", "保险", "人力资源", "行政后勤", "客户服务", "融资租赁", "咨询服务", "拍卖典当", "研发", "设计", "互联网产品销售", "运营/编辑", "产品经理", "市场商务", "高管", "运维/安全", "人力HR", "行政后勤", "软件测试", "客服", "项目管理", "生产管理", "生产运营", "销售与服务", "电子/电器", "汽车", "机械制造", "服装/纺织", "技工", "生物/制药", "医疗器械", "化工", "装修施工", "房产销售", "经纪人", "设计规划", "项目管理", "高管", "市场商务", "行政人事", "质检造价", "开发/物业", "贸易销售", "纺织服装", "贸易进出口", "采购", "仓储/物流", "食品饮料", "建材家居", "商贸百货", "包装印刷", "市场商务", "行政人事", "工艺礼品", "产品设计", "产品研发", "珠宝首饰", "质检/认证", "机电仪表", "客服", "市场销售", "幼教", "艺术体育", "职业技能", "人民教师", "行政人事", "教务/管理", "课外辅导", "科研/学者", "中专技校", "移民留学", "特岗教师", "设计", "动画", "行政人事", "传媒销售", "品牌", "公关", "策划", "高管", "艺人", "经纪人", "演出/会展", "市场商务", "编导制作", "编辑记者", "艺术家", "收藏", "出版发行", "自由撰稿人", "兽医", "饲养", "养殖", "农林牧渔销售", "项目管理", "技术员", "加工/质检", "市场商务", "机械设备", "电子销售", "生产制造", "技工普工", "硬件研发", "工程/维护", "经营管理", "市场商务", "行政人事", "工业设计", "采购物控", "增值业务", "医疗销售", "医生", "护士/护理", "理疗师", "行政人事", "经营管理", "市场商务", "医生生产", "医学研发", "辅诊/药剂", "宠物", "能源/矿工", "地质勘查", "环境科学", "环保", "学生", "律师/法务", "社团协会", "咨询", "顾问", "调研", "数据分析", "翻译", "其他");

    /**
     * 学历
     */
    private static final List<String> EDUCATION = Lists.newArrayList("小学", "初中", "职高", "高中", "专科", "本科", "硕士", "博士");

    /**
     * 学历校验
     *
     * @param educationLevel
     */
    public static void validEducation(String educationLevel) {
        if (StringUtils.isNotBlank(educationLevel) && !EDUCATION.contains(educationLevel)) {
            throw new ServiceException("请求失败，请重试！");
        }
    }

    /**
     * 职业校验
     *
     * @param profession
     */
    public static void validProfession(String trade, String profession) {
        if (StringUtils.isNotBlank(trade) && !TRADE.contains(trade)) {
            throw new ServiceException("请求失败，请重试！");
        }

        if (StringUtils.isNotBlank(profession) && !PROFESSION.contains(profession)) {
            throw new ServiceException("请求失败，请重试！");
        }
    }

}
