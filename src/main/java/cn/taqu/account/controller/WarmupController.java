package cn.taqu.account.controller;

import cn.taqu.warmup.data.WarmupCompletedEvent;
import cn.taqu.warmup.tool.WarmupHealth;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationListener;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/18 下午4:43
 */
@RestController
@RequestMapping("/warmupState")
@ConditionalOnMissingBean(WarmupHealth.class)
public class WarmupController implements ApplicationListener<WarmupCompletedEvent> {

    private static final Logger log = LoggerFactory.getLogger(WarmupController.class);

    private volatile long readyTime = Long.MAX_VALUE;

    @Override
    public void onApplicationEvent(WarmupCompletedEvent event) {
        if (event.getSource() > 0) {
            // 20秒缓冲时间
            readyTime = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(20);
        } else {
            readyTime = System.currentTimeMillis();
        }
        log.info("【预热】总耗时{}ms，服务就绪时间 {}", event.getSource(), readyTime);
    }

    @GetMapping
    public ResponseEntity<?> health() {
        if (readyTime < System.currentTimeMillis()) {
            return ResponseEntity.ok(new Health.Builder().up().build());
        }
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(
            new Health.Builder().outOfService().build()
        );
    }

}