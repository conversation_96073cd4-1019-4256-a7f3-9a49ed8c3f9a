package cn.taqu.account.controller;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.common.constant.ICodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ApiException;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.log.Log;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.web.bind.UnsatisfiedServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.MessageFormat;

@ControllerAdvice
public class SpringExceptionHandler extends ResponseEntityExceptionHandler {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(SpringExceptionHandler.class);
	
	@ExceptionHandler(value = { ServiceException.class })
	public @ResponseBody
    String serviceHandelException(ServiceException ex, HttpServletResponse response, HttpServletRequest request) throws IOException{
		ICodeStatus codeStatus = ex.getCodeStatus();
		if(codeStatus == null) {
			codeStatus = CodeStatus.getCodeStatus(ex.getMessage());
		}
		if(codeStatus.equals(SysCodeStatus.UNDEFINED)){
			Log.warn("{}-{}-{}", codeStatus.value(), codeStatus.getReasonPhrase(), ex.getMessage());
			return JsonUtils.objectToString(JsonResult.failed(ex.getMessage()));
		}
		Log.warn("{}-{}", codeStatus.value(), codeStatus.getReasonPhrase());
		return httpErrorJson(response, codeStatus);
	}
	
	@ResponseBody
	@ExceptionHandler(value = { ApiException.class })
	public final JsonResult handleException(ApiException ex,HttpServletRequest request) {
		Log.error(ex.getMessage());
		return JsonResult.failed(ex.getMessage());
	}
	
	@ResponseBody
	@ExceptionHandler(value = { UnsatisfiedServletRequestParameterException.class })
	public final JsonResult unsatisfiedServletRequestParameterException(UnsatisfiedServletRequestParameterException ex, HttpServletRequest request) {
		String message = MessageFormat.format("错误码[{0}]:{1}", SysCodeStatus.NOT_METHOD.value(), SysCodeStatus.NOT_METHOD.getReasonPhrase());
		LOGGER.error(request.getQueryString() + " " + message);
		return JsonResult.failedCode(SysCodeStatus.NOT_METHOD);
	}

	@ExceptionHandler(value = Exception.class)
	public @ResponseBody
	String handleUncaughtException(Exception ex, HttpServletResponse response, HttpServletRequest request) throws IOException {
		/*
		 * 乐观锁失败问题，先记录warn日志，临时解决，清明节后寻找真正解决方案，
		 * 该问题在并发下会出现，例如同时请求更新个人资料，此时会有两个session同时去update，其中一个会update更新成功后，另一个再去进行update操作时返回的更新数是0，产生了此错误
		 * 异常日志链接：
		 * 1. http://117.50.7.118/log/errors/20180403084801java847905ae20986cdc115f7de33e7e18b4.log
		 * 2. http://117.50.7.118/log/errors/20180402211201javaded3351013edaf0eed26a4d14e45ed23.log
		 */
		if(ex instanceof ObjectOptimisticLockingFailureException
				&& StringUtils.contains(ex.getMessage(), "Batch update returned unexpected row count from update [0]; actual row count: 0; expected: 1")) {
			Log.warn(SysCodeStatus.ERROR.getReasonPhrase(), ex);
		} else {
			Log.error(SysCodeStatus.ERROR.getReasonPhrase(), ex);
		}
		return httpErrorJson(response, SysCodeStatus.ERROR);
	}

	private String httpErrorJson(HttpServletResponse response, ICodeStatus codeStatus){
		response.setHeader("Content-Type", "application/json");
        response.setStatus(HttpServletResponse.SC_OK);
        Gson gson = new Gson();
        return gson.toJson(JsonResult.failedCode(codeStatus));
	}
	
}
