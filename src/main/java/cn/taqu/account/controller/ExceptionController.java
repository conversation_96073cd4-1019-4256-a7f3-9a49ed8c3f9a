package cn.taqu.account.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ErrorAttributes;
import org.springframework.boot.autoconfigure.web.ErrorController;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Objects;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.common.constant.ICodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.web.springmvc.JsonResult;

@Controller
@RequestMapping(value = "/error")
@EnableConfigurationProperties({ ServerProperties.class })
public class ExceptionController implements ErrorController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ExceptionController.class);

	private ErrorAttributes errorAttributes;

	@Autowired
	private ServerProperties serverProperties;

	/**
	 * 初始化ExceptionController
	 * 
	 * @param errorAttributes
	 */
	@Autowired
	public ExceptionController(ErrorAttributes errorAttributes) {
		Assert.notNull(errorAttributes, "ErrorAttributes must not be null");
		this.errorAttributes = errorAttributes;
	}

	/**
	 * 定义404的ModelAndView
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(produces = "text/html", value = "404")
	public String errorHtml404(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model) {
		response.setStatus(getStatus(request).value());
		JsonResult jsonResult = new JsonResult();
		model.put("result", jsonResult);
		Throwable throwable = getThrowable(request);

		jsonResult.setSuccess(false);
		jsonResult.setCode(String.valueOf(getStatus(request).value()));

		if (throwable instanceof ServiceException) {
			LOGGER.warn("", throwable);
			ServiceException se = (ServiceException) throwable;
			ICodeStatus codeStatus = se.getCodeStatus();
			if (codeStatus != null) {
				jsonResult.setCode(codeStatus.value());
				jsonResult.setMsg(codeStatus.getReasonPhrase());
			} else {
				String message = se.getMessage();
				ICodeStatus ics = CodeStatus.getCodeStatus(message);
				if (Objects.equal(ics.value(), SysCodeStatus.UNDEFINED.value())) {
					jsonResult.setMsg(se.getMessage());
				} else {
					jsonResult.setCode(ics.value());
					jsonResult.setMsg(ics.getReasonPhrase());
				}
			}

			return "commons/404";
		}
		LOGGER.error("", throwable);
		jsonResult.setMsg(SysCodeStatus.UNDEFINED.value());

		return "commons/404";
	}

	/**
	 * 定义404的JSON数据
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "404")
	@ResponseBody
	public JsonResult error404(HttpServletRequest request) {
		JsonResult jsonResult = new JsonResult();
		Throwable throwable = getThrowable(request);
		jsonResult.setSuccess(false);
		jsonResult.setCode(String.valueOf(getStatus(request).value()));

		if (throwable instanceof ServiceException) {
			LOGGER.warn("", throwable);
			ServiceException se = (ServiceException) throwable;
			ICodeStatus codeStatus = se.getCodeStatus();
			if (codeStatus != null) {
				jsonResult.setCode(codeStatus.value());
				jsonResult.setMsg(codeStatus.getReasonPhrase());
			} else {
				String message = se.getMessage();
				ICodeStatus ics = CodeStatus.getCodeStatus(message);
				if (Objects.equal(ics.value(), SysCodeStatus.UNDEFINED.value())) {
					jsonResult.setMsg(se.getMessage());
				} else {
					jsonResult.setCode(ics.value());
					jsonResult.setMsg(ics.getReasonPhrase());
				}
			}
			return jsonResult;
		}
		LOGGER.error("", throwable);

		jsonResult.setMsg(SysCodeStatus.UNDEFINED.value());
		return jsonResult;
	}

	/**
	 * 定义404的ModelAndView
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(produces = "text/html", value = "400")
	public String errorHtml400(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model) {
		response.setStatus(getStatus(request).value());
		JsonResult jsonResult = new JsonResult();
		model.put("result", jsonResult);
		Throwable throwable = getThrowable(request);
		jsonResult.setSuccess(false);
		jsonResult.setCode(String.valueOf(getStatus(request).value()));

		if (throwable instanceof ServiceException) {
			LOGGER.warn("", throwable);
			ServiceException se = (ServiceException) throwable;
			ICodeStatus codeStatus = se.getCodeStatus();
			if (codeStatus != null) {
				jsonResult.setCode(codeStatus.value());
				jsonResult.setMsg(codeStatus.getReasonPhrase());
			} else {
				String message = se.getMessage();
				ICodeStatus ics = CodeStatus.getCodeStatus(message);
				if (Objects.equal(ics.value(), SysCodeStatus.UNDEFINED.value())) {
					jsonResult.setMsg(se.getMessage());
				} else {
					jsonResult.setCode(ics.value());
					jsonResult.setMsg(ics.getReasonPhrase());
				}
			}
			return "commons/400";
		}
		LOGGER.error("", throwable);

		jsonResult.setMsg(SysCodeStatus.UNDEFINED.value());

		return "commons/400";
	}

	/**
	 * 定义404的JSON数据
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "400")
	@ResponseBody
	public JsonResult error400(HttpServletRequest request) {
		JsonResult jsonResult = new JsonResult();
		Throwable throwable = getThrowable(request);
		jsonResult.setSuccess(false);
		jsonResult.setCode(String.valueOf(getStatus(request).value()));

		if (throwable instanceof ServiceException) {
			LOGGER.warn("", throwable);
			ServiceException se = (ServiceException) throwable;
			ICodeStatus codeStatus = se.getCodeStatus();
			if (codeStatus != null) {
				jsonResult.setCode(codeStatus.value());
				jsonResult.setMsg(codeStatus.getReasonPhrase());
			} else {
				String message = se.getMessage();
				ICodeStatus ics = CodeStatus.getCodeStatus(message);
				if (Objects.equal(ics.value(), SysCodeStatus.UNDEFINED.value())) {
					jsonResult.setMsg(se.getMessage());
				} else {
					jsonResult.setCode(ics.value());
					jsonResult.setMsg(ics.getReasonPhrase());
				}
			}
			return jsonResult;
		}
		LOGGER.error("", throwable);

		jsonResult.setMsg(SysCodeStatus.UNDEFINED.value());
		return jsonResult;
	}

	/**
	 * 定义500的ModelAndView
	 * 
	 * @param request
	 * @param response
	 * @return
	 * @throws JsonProcessingException
	 */
	@RequestMapping(produces = "text/html", value = "500")
	public String errorHtml500(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws JsonProcessingException {
		response.setStatus(HttpStatus.OK.value());
		JsonResult jsonResult = new JsonResult();
		model.put("result", jsonResult);
		Throwable throwable = getThrowable(request);
		jsonResult.setSuccess(false);
		jsonResult.setCode(SysCodeStatus.ERROR.value());
		jsonResult.setMsg(SysCodeStatus.ERROR.getReasonPhrase());
		if (throwable instanceof ServiceException) {
			LOGGER.warn("", throwable);
			ServiceException se = (ServiceException) throwable;
			ICodeStatus codeStatus = se.getCodeStatus();
			if (codeStatus != null) {
				jsonResult.setCode(codeStatus.value());
				jsonResult.setMsg(codeStatus.getReasonPhrase());
			} else {
				String message = se.getMessage();
				ICodeStatus ics = CodeStatus.getCodeStatus(message);
				if (Objects.equal(ics.value(), SysCodeStatus.UNDEFINED.value())) {
					jsonResult.setMsg(se.getMessage());
				} else {
					jsonResult.setCode(ics.value());
					jsonResult.setMsg(ics.getReasonPhrase());
				}
			}
			return "commons/500";
		}
		LOGGER.error("", throwable);

		return "commons/500";
	}

	/**
	 * 定义500的错误JSON信息
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "500")
	@ResponseBody
	public JsonResult error500(HttpServletRequest request, HttpServletResponse response) {
		response.setStatus(HttpStatus.OK.value());
		JsonResult jsonResult = new JsonResult();
		Throwable throwable = getThrowable(request);
		jsonResult.setSuccess(false);
		jsonResult.setCode(SysCodeStatus.ERROR.value());
		jsonResult.setMsg(SysCodeStatus.ERROR.getReasonPhrase());
		if (throwable instanceof ServiceException) {
			LOGGER.warn("", throwable);
			ServiceException se = (ServiceException) throwable;
			ICodeStatus codeStatus = se.getCodeStatus();
			if (codeStatus != null) {
				jsonResult.setCode(codeStatus.value());
				jsonResult.setMsg(codeStatus.getReasonPhrase());
			} else {
				String message = se.getMessage();
				ICodeStatus ics = CodeStatus.getCodeStatus(message);
				if (Objects.equal(ics.value(), SysCodeStatus.UNDEFINED.value())) {
					jsonResult.setMsg(se.getMessage());
				} else {
					jsonResult.setCode(ics.value());
					jsonResult.setMsg(ics.getReasonPhrase());
				}
			}
			return jsonResult;
		}
		LOGGER.error("", throwable);
		return jsonResult;
	}

	/**
	 * Determine if the stacktrace attribute should be included.
	 * 
	 * @param request
	 *            the source request
	 * @param produces
	 *            the media type produced (or {@code MediaType.ALL})
	 * @return if the stacktrace attribute should be included
	 */
	protected boolean isIncludeStackTrace(HttpServletRequest request, MediaType produces) {
		ErrorProperties.IncludeStacktrace include = this.serverProperties.getError().getIncludeStacktrace();
		if (include == ErrorProperties.IncludeStacktrace.ALWAYS) {
			return true;
		}
		if (include == ErrorProperties.IncludeStacktrace.ON_TRACE_PARAM) {
			return getTraceParameter(request);
		}
		return false;
	}

	/**
	 * 获取错误的信息
	 * 
	 * @param request
	 * @param includeStackTrace
	 * @return
	 */
	@SuppressWarnings("unused")
	private Map<String, Object> getErrorAttributes(HttpServletRequest request, boolean includeStackTrace) {
		RequestAttributes requestAttributes = new ServletRequestAttributes(request);
		return this.errorAttributes.getErrorAttributes(requestAttributes, includeStackTrace);
	}

	/**
	 * 是否包含trace
	 * 
	 * @param request
	 * @return
	 */
	private boolean getTraceParameter(HttpServletRequest request) {
		String parameter = request.getParameter("trace");
		if (parameter == null) {
			return false;
		}
		return !"false".equals(parameter.toLowerCase());
	}

	/**
	 * 获取错误编码
	 * 
	 * @param request
	 * @return
	 */
	private HttpStatus getStatus(HttpServletRequest request) {
		Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
		if (statusCode == null) {
			return HttpStatus.INTERNAL_SERVER_ERROR;
		}
		try {
			return HttpStatus.valueOf(statusCode);
		} catch (Exception ex) {
			return HttpStatus.INTERNAL_SERVER_ERROR;
		}
	}

	/**
	 * 获取异常
	 * 
	 * @param request
	 * @return
	 */
	private Throwable getThrowable(HttpServletRequest request) {
		return (Throwable) request.getAttribute("javax.servlet.error.exception");
	}

	/**
	 * 实现错误路径,暂时无用
	 * 
	 * @see ExceptionMvcAutoConfiguration#containerCustomizer()
	 * @return
	 */
	@Override
	public String getErrorPath() {
		return "";
	}

}