package cn.taqu.account.controller.api.jrpc;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.MembersService;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 设备相关Controller
 * <AUTHOR>
 * 2017年7月5日 下午2:54:21
 */
@RestController
@RequestMapping(value="/api", params="service=members")
public class MembersController {
	
	@Autowired
	private MembersService membersService;
	
	/**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 客户端接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/
	
	/**
	 * 上报应用消息
	 * http://10.10.50.205:1234/Account_System/v5/Members/reportAppInfo
	 * @param params
	 * @return
	 * <AUTHOR>
	 * 2017年6月2日 下午6:43:40
	 */
	@Api
	@RequestMapping(params = "method=reportAppInfo")
	public JsonResult reportAppInfo(RequestParams params) {
		//请求参数
    	String token = RequestParams.getSoa_basic_java().getToken();
    	Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
    	Long appVersion = RequestParams.getSoa_basic_java().getApp_version();
    	Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
    	Integer cloned = RequestParams.getSoa_basic_java().getCloned();
    	String channel = RequestParams.getSoa_basic_java().getChannel();
		String accountUuid = params.getFormStringOption(0);
		Integer noticeEnable = params.getFormIntegerDefault(1, 0);
		membersService.reportAppInfo(token, platformId, appVersion, appcode, cloned, channel, accountUuid, noticeEnable);
		return JsonResult.success();
	}
	
	
	/**
	 * 转j53处理
	 * 转换新旧token
	 * 传入新token和旧token。
	 * 
	 * @param params
	 * @return
	 */
	@Deprecated
    @Api
    @RequestMapping(params = "method=convertToken")
    public JsonResult convertToken(RequestParams params) {
        String newToken = params.getFormString(0);
        String oldToken = params.getFormStringOption(1);
        Map<String, String> data = membersService.convertToken(newToken, oldToken);
        JsonResult success = JsonResult.success();
        success.setData(data);
        return success;
    }
	
	/**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 客户端接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/
	
	/**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 其他系统接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

	/**
	 * 根据账号uuid批量查询用户设备信息
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getInfoByAccountUuids")
	public JsonResult getInfoByAccountUuids(RequestParams params) {
		String[] accountUuids = params.getFormStringArray(0);
		return JsonResult.success(membersService.mGetInfoByAccountUuids(accountUuids));
	}

	/**
	 * 根据uuid查询账号对应的设备token，返回以uuid为key，以token为value的map
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getTokenByUuids")
	public JsonResult getTokenByUuids(RequestParams params) {
		String[] uuids = params.getFormStringArray(0);
		return JsonResult.success(membersService.getTokenByUuids(uuids));
	}
	
	/**
	 * 根据token查询设备的创建(注册)时间
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getCreateTimeFromCache")
	public JsonResult getCreateTimeFromCache(RequestParams params) {
		String token = params.getFormString(0);
		Long createTime = membersService.getCreateTimeFromCache(token);
		return JsonResult.success(createTime);
	}

	/**
	 * 根据token查询设备的前一个活跃时间，即今天之前的最后一个活跃时间
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getPrevActiveTime")
	public JsonResult getPrevActiveTime(RequestParams params) {
		String token = params.getFormString(0);
		Long activeTime = membersService.getTokenPrevActiveTime(token);
		return JsonResult.success(activeTime);
	}

	/**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 其他系统接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/
	
}