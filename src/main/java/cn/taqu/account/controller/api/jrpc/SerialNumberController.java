package cn.taqu.account.controller.api.jrpc;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AccountSnListService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

@RestController
@RequestMapping(value="/api", params="service=serialNumber")
public class SerialNumberController {

    @Autowired
    private AccountSnListService accountSnListService;

    @RequestMapping(params = "method=getAccountsSnByUuid")
    public JsonResult getAccountsSnByUuid(RequestParams params) {
        String formUuid = params.getFormString(0);
        String toUuid = params.getFormString(1);
        Map<String, Map<String, String>> result = accountSnListService.getAccountSnByUuid(formUuid, toUuid);
        return JsonResult.success(result);
    }

    @RequestMapping(params = "method=getByUuids")
    public JsonResult getByUuids(RequestParams params) {
        String[] accountUuids = params.getFormStringArray(0);
        Map<String, String> result = accountSnListService.getByUuids(accountUuids);
        return JsonResult.success(result);
    }

    @RequestMapping(params = "method=getAccountInfoBySn")
    public JsonResult getAccountInfoBySn(RequestParams params) {
        String[] accountSns = params.getFormStringArray(0);
        Map<String, Map<String, String>> result = accountSnListService.getAccountInfoBySn(accountSns);
        return JsonResult.success(result);
    }
}
