/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.client.mp.MPAccountRequest;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.RegisterLoginControlIsControlReqDto;
import cn.taqu.account.model.AccountsThirdParty;
import cn.taqu.account.model.LoginActionInfo;
import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.account.service.*;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.account.utils.ShowDialogUtil;
import cn.taqu.account.utils.mq.AntiSpamMqUtil;
import cn.taqu.account.vo.AppleLoginVo;
import cn.taqu.account.vo.LoginVo;
import cn.taqu.account.vo.RegVo;
import cn.taqu.account.vo.WeChatLoginVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import cn.taqu.core.common.constant.SysCodeStatus;

/**
 * 第三方调用
 *
 * @author:laikunzhen
 */
@Slf4j
@RestController
@RequestMapping(value = "api", params = "service=thirdLogin")
public class AccountsThirdPartyController {
    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private MobileService mobileService;
    @Autowired
    private AccountActionService accountActionService;
    @Autowired
    private AntiSpamMqUtil antiSpamMqUtil;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RiskService riskService;

    @Autowired
    private RedisLockUtil redisLockUtil;

    @Autowired
    private ThirdPartyLoginChannelHolder channelHolder;

    /**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 客户端接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

    /**
     * 用户绑定第三方登录
     * http://10.10.50.205:1234/Account_System/v5/ThirdLogin/bindThirdLogin
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:40:24
     */
    @Api
    @RequestMapping(params = "method=bindThirdLogin")//bind
    public JsonResult bindThirdLogin(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        String type = params.getFormString(1);
        String open_id = params.getFormString(2);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        accountsThirdPartyService.bind(type, open_id, accountUuid);
        return JsonResult.success();
    }

    /**
     * 用户是否绑定第三方登录信息(加上是否设置密码)
     * http://10.10.50.205:1234/Account_System/v5/ThirdLogin/getAccountThirdLoginInfo
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:41:26
     */
    @Api
    @RequestMapping(params = "method=getAccountThirdLoginInfo")//isRegister
    public JsonResult getAccountThirdLoginInfo(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        Map<String, Object> reMap = MPAccountRequest.isRequestMP() ?
                accountsThirdPartyService.isRegisterFromMP(accountUuid) :
                accountsThirdPartyService.isRegister(accountUuid);

        return JsonResult.success(reMap);
    }

    /**
     * 用户解除第三方绑定
     * http://10.10.50.205:1234/Account_System/v5/ThirdLogin/reBindThirdLogin
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:41:50
     */
    @Api
    @RequestMapping(params = "method=reBindThirdLogin")//deBind
    public JsonResult reBindThirdLogin(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        String type = params.getFormString(1);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        accountsThirdPartyService.deBind(type, accountUuid);
        return JsonResult.success();
    }

    /**
     * 第三方登录(QQ,微博,游客)
     * http://10.10.50.205:1234/Account_System/v5/ThirdLogin/thirdLogin
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:42:17
     */
    @Api
    @RequestMapping(params = "method=thirdLogin")//login
    public JsonResult thirdLogin(RequestParams params) {
        SoaBaseParams soaBasicJava = RequestParams.getSoa_basic_java();
        Integer platformId = soaBasicJava.getPlatform_id();
        String token = soaBasicJava.getToken();
        Integer appcode = soaBasicJava.getAppcode();
        Integer cloned = soaBasicJava.getCloned();
        String ip = soaBasicJava.getIp();
        
        //请求参数
        String openId = params.getFormString(0);
        String type = params.getFormString(1);
        if (type.equals(AccountsThirdParty.TYPE_WEIBO) || type.equals(AccountsThirdParty.VISITOR)) {
            throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
        }
        if (!type.equals(AccountsThirdParty.TYPE_QQ)) {
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }
        
        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }

        // 2024.06.18 登录注册管控，抛异常
        accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.LOGIN, RegisterLoginControlIsControlReqDto.model.QQ);

        int versionNumber = params.getFormIntegerDefault(2, 0);
        String shumeiDeviceId = params.getFormStringOption(3);
        //20年45周迭代,注册的时候如果填写了手机,则判断是否给他绑定
        String mobile = params.getFormStringOption(4);
        //这个就是quickAccessToken，闪验token
        String accessToken = params.getFormStringOption(5);
        String extraJson = params.getFormStringDefault(6, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        LoginVo loginVo = accountsThirdPartyService.login(type, openId, versionNumber, mobile, shumeiDeviceId, accessToken, extraJson);
        SoaBaseParams soaBaseParams = RequestParams.getSoa_basic_java();
        //用户登录版本限制
        accountsService.loginVersionLimit(loginVo.getUuid(), soaBaseParams.getPlatform_id(), soaBaseParams.getApp_version());
        //设置数美设备id
        if (StringUtils.isNotBlank(shumeiDeviceId)) {
            loginVo.setShumeiDeviceId(shumeiDeviceId);
        }
        // ******** 即将进入绑定流程的openId要过一下j50检测
        if(Objects.equals(0, loginVo.getBind_info())){
            if(riskService.checkOpenIdRisk(type, openId)){
                ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
            }
            channelHolder.via(openId, RegStyleEnum.getByName(type).name());
        }
        if (!Objects.equals(0, loginVo.getBind_info())) {
            //接入阿里风控
//            addPreventionWithIgnoreException(type, loginVo);
            // 保存登录行为信息
            me().mergeLoginActionInfo(RequestParams.getSoa_basic_java(), loginVo.getUuid(), type);
            // 推入队列告知反垃圾系统
            antiSpamMqUtil.pushToAccountActionMq(loginVo.getUuid(), "", AccountActionTypeEnum.LOGIN,null);
            antiSpamMqUtil.pushToUuidAFRefreshMq(loginVo.getUuid(), soaBaseParams.getAppcode(), soaBaseParams.getCloned(), soaBaseParams.getApp_version(), soaBaseParams.getIp());

            //推送队列
            accountsService.loginoutBury(token, platformId, appcode, loginVo.getUuid(), "1", cloned);
            // 登录成功
            accountsService.pushToAdmpDeviceLogin(token, loginVo.getUuid(), loginVo.getSex_type(), DateUtil.currentTimeSeconds(), platformId, appcode, cloned, ip);
        }

        //返回数据
        Map<String, Object> resultMap = new HashMap<>();
        if (loginVo.getBind_info() != null && 0 == loginVo.getBind_info()) {
            //AccountsThirdParty == null ,设置bind_info == 0 ,不存在游客账号,进入游客注册流程,
            BuryService.preRegBuryToAdmp(PreRegisterTypeEnum.QQ.getType());
            resultMap.put("bind_info", String.valueOf(loginVo.getBind_info()));
        } else {
            //查到第三方账号AccountsThirdParty的数据
            resultMap.putAll(accountsThirdPartyService.handelThirdLoginResult(loginVo, versionNumber));
        }
        return JsonResult.success(resultMap);
    }

    @Async
    public void mergeRegisterActionInfo(SoaBaseParams soaBasicJava, String accountUuid, String actionMode) {
        RegisterActionInfo registerActionInfo = new RegisterActionInfo();
        registerActionInfo.setAccountUuid(accountUuid);
        registerActionInfo.setToken(soaBasicJava.getToken());
        registerActionInfo.setActionIp(soaBasicJava.getIp());
        registerActionInfo.setAppcode(soaBasicJava.getAppcode());
        registerActionInfo.setCloned(soaBasicJava.getCloned());
        registerActionInfo.setPlatformId(soaBasicJava.getPlatform_id());
        registerActionInfo.setChannel(soaBasicJava.getChannel());
        registerActionInfo.setAppVersion(soaBasicJava.getApp_version());
        registerActionInfo.setCreateTime(DateUtil.currentTimeSeconds());
        registerActionInfo.setActionMode(actionMode);
        registerActionInfo.setPlatformName(soaBasicJava.getPlatform_name());
        registerActionInfo.setAccess(soaBasicJava.getAccess());
        registerActionInfo.setAlias(soaBasicJava.getAlias());
        registerActionInfo.setGender(soaBasicJava.getGender());
        if(ToolsService.registerActionInfoSwitchWrite.isOn(true)) {
            registerActionInfo.setLongitude(soaBasicJava.getLongitude());
            registerActionInfo.setLatitude(soaBasicJava.getLatitude());
        }else {
            log.info("register_action_info不写加密字段");
            registerActionInfo.setLongitude("");
            registerActionInfo.setLatitude("");
        }
        registerActionInfo.setLongitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLongitude()));
        registerActionInfo.setLatitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLatitude()));
        registerActionInfo.setCity(soaBasicJava.getCity());
        accountActionService.merge(registerActionInfo);
    }

    @Deprecated
    @RequestMapping(params = "method=regVerifyMobile")
    public JsonResult regVerifyMobile(RequestParams params) {
        String type = params.getFormString(0);
        String mobile = params.getFormStringOption(1);
        String verify = params.getFormStringOption(2);
        String accessToken = params.getFormStringOption(3);

        Map<String, Object> map = mobileService.regVerifyMobileByCode(type, mobile, verify, accessToken);

        return JsonResult.success(map);
    }

    @RequestMapping(params = "method=regConfig")
    public JsonResult regConfig(RequestParams params) {
        Map<String, String> map = new HashMap<>();
        map.put("bind_mobile", AccountsThirdPartyService.regNeedBindMobile(SoaBaseParams.fromThread().getCloned(), SoaBaseParams.fromThread().getChannel()) ? "1" : "0");
        return JsonResult.success(map);
    }

    /**
     * 目前在使用的第三方注册接口，相比thirdReg多出accessToken，更加安全。thirdReg在新版本中不再推荐使用。
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=thirdRegWithAuth")//register
    public JsonResult thirdRegWithAuth(RequestParams params) {
        // 配配注册限制
        AccountController.limitPeipeiAccountReg();
        
        SoaBaseParams soaBasicJava = RequestParams.getSoa_basic_java();
        String token = soaBasicJava.getToken();
        Integer platformId = soaBasicJava.getPlatform_id();
        Long appVersion = soaBasicJava.getApp_version();
        Integer appcode = soaBasicJava.getAppcode();
        Integer cloned = soaBasicJava.getCloned();
        String channel = soaBasicJava.getChannel();
        
        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }
        
        //请求参数
        String type = params.getFormString(0);//第三方登录类型,只能为QQ,WeiBo,WeChat,Apple,Visitor中一个
        String openId = params.getFormString(1);//第三方登录用户凭证，如果是游客模式注册时，openId为设备的token
        String unionId = params.getFormStringOption(2);//微信用户凭证(确保唯一) 微信注册必填
        String nickname = params.getFormStringOption(3);//昵称
        Integer sexType = params.getFormIntegerOption(4);//性别
        String avatar = params.getFormStringOption(5);//用户头像
        String accessToken = params.getFormStringOption(6);//如果是WeiBo或者QQ或者Apple登录，access_token必填
        int versionNumber = params.getFormIntegerDefault(7, 0);
        String shumeiDeviceId = params.getFormStringDefault(8, CommConst.OLD_CLIENT_PACKAGE);
        Long birth = params.getFormLongOption(9);
        String mobile = params.getFormStringOption(10);
        String quickAccessToken = params.getFormStringOption(11);
        Integer age = params.getFormIntegerOption(12);
        Integer defaultNickName = params.getFormIntegerDefault(13, 0);
        // 注册方式 new-新版  old-旧版 2024.04.18 客户端早就回滚了，此字段无用
        String registerNoUsed = params.getFormStringDefault(14, "old");
        // 交友目的id 2024.04.18 客户端早就回滚了，此字段无用
        Integer makeFriendObjectiveIdNoUsed = params.getFormIntegerDefault(15, 0);
        String extraJson = params.getFormStringDefault(16, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        log.info("thirdRegWithAuth smid: {}, age:{}, register:{}, makeFriendObjectiveId:{}", shumeiDeviceId, age, registerNoUsed, makeFriendObjectiveIdNoUsed);

        log.info("type: {}, openid: {}, token: {}, unionid: {}, accessToken: {}, mobile: {}, quickAccsessToken: {}", type, openId, token, unionId, accessToken, mobile, quickAccessToken);

        if (sexType == null) {
            sexType = 0;
        }

        if (StringUtils.isBlank(token) || platformId == null || appcode == null || appVersion == null || StringUtils.isBlank(channel)) {
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }
        // 校验channel长度，大于则直接抛出异常 SysCodeStatus.REQUEST_PARA_ERROR
        AccountsService.checkAccountsChannelLen(channel);

        RegStyleEnum style = RegStyleEnum.getByName(type);
        boolean check = channelHolder.validate(openId, style.name());
        if (!check) {
            log.warn("RegStyle not matched, openId = {}, style = {}", openId, style);
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }
        if(Objects.equals(style, RegStyleEnum.WECHAT)) {
            // 2024.06.18 登录注册管控，抛异常
            accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.REGISTER, RegisterLoginControlIsControlReqDto.model.WECHAT);
        }else if(Objects.equals(style, RegStyleEnum.QQ)) {
            accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.REGISTER, RegisterLoginControlIsControlReqDto.model.QQ);
        }else if(Objects.equals(style, RegStyleEnum.APPLE)) {
            accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.REGISTER, RegisterLoginControlIsControlReqDto.model.APPLE);
        }else {
            log.warn("不支持的第三方注册类型，不进行注册管控，type={}", type);
        }

        if (StringUtils.isNotBlank(avatar)) {
            Map<String, String> map = JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(avatar), new TypeReference<Map<String, String>>() {
            });
            String imgName = map.get("img_name");
            if(StringUtils.isNotBlank(imgName) && imgName.indexOf(accountsService.getAvatarUrlPrefix()) < 0){
                log.info("图片特殊处理.拼接域名={}", imgName);
                avatar = accountsService.getAvatarUrlPrefix() + imgName;
            }else{
                log.info("图片特殊处理.不拼接域名={}", imgName);
                avatar = imgName;
            }
        }

        // 添加分布式锁 保证openId只注册一次
        String lockKey = RedisKeyConstant.ACCOUNT_THIRD_REGISTER_OPENID_LOCK.setArg(openId);
        boolean lock = redisLockUtil.lock(lockKey, 10000); // has unLock
        if (!lock) {
            throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
        }
        //没有生日的情况下，如果有age,使用age转换
        if(birth==null){
            if(age!=null && age>0){
                Date date=new Date();
                date=DateUtil.changeMonth(date,-age*12);
                birth=DateUtil.toSecond(date);
            }
        }

        try {
            RegVo regVo = accountsThirdPartyService.register(type, openId, unionId, nickname, token, platformId, appcode, cloned, appVersion, channel, sexType, avatar, versionNumber, birth, mobile, shumeiDeviceId, quickAccessToken, age, defaultNickName, extraJson);

            // 游客、微博、apple账号注册时只校验IP
            if (RegStyleEnum.VISITOR.equals(style) || RegStyleEnum.WEIBO.equals(style) || RegStyleEnum.APPLE.equals(style)){
                regVo.setOpenId("");
                regVo.setUnionId("");
            }else {
                regVo.setOpenId(openId);
                regVo.setUnionId(unionId);
            }

            accountsService.registerBury(appcode, regVo);
            if (StringUtils.isNotBlank(shumeiDeviceId)) {
                regVo.setShumeiDeviceId(shumeiDeviceId);
            }

            //添加注册行为信息
            me().mergeRegisterActionInfo(soaBasicJava, regVo.getUuid(), type);
            // 推入队列告知反垃圾系统
            antiSpamMqUtil.pushToAccountActionMq(regVo.getUuid(), regVo.getNickname(), AccountActionTypeEnum.REGISTER,null);
//            this.thirdRegSpam(type, regVo, soaBasicJava);
            Integer ageVO=regVo.getAge();
            if(ageVO==null || ageVO<=0){
                regVo.setAge(age);
            }
            //2017.7.26 新增
            Map<String, Object> resultMap = accountsService.handleThirdReg(regVo, appcode, versionNumber);
            return JsonResult.success(resultMap);
        } finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 微信第三方登录
     * http://10.10.50.205:1234/Account_System/v5/ThirdLogin/weChatLogin
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:44:19
     */
    @Api
    @RequestMapping(params = "method=weChatLogin")//weChatLogin
    public JsonResult weChatLogin(RequestParams params) {
        SoaBaseParams soaBasicParams = RequestParams.getSoa_basic_java();
        Integer platformId = soaBasicParams.getPlatform_id();
        String token = soaBasicParams.getToken();
        Integer appcode = soaBasicParams.getAppcode();
        Integer cloned = soaBasicParams.getCloned();
        Long appVersion = soaBasicParams.getApp_version();
        String ip = soaBasicParams.getIp();
        
        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }
        
        // 2024.06.18 登录注册管控，抛异常
        accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.LOGIN, RegisterLoginControlIsControlReqDto.model.WECHAT);

        //请求参数
        String code = params.getFormString(0);
        int versionNumber = params.getFormIntegerDefault(1, 0);
        String shumeiDeviceId = params.getFormStringOption(2);
        String extraJson = params.getFormStringDefault(3, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        Object result = accountsThirdPartyService.weChatLogin(code, versionNumber, shumeiDeviceId, extraJson);
        Map<String, Object> resultMap = new HashMap<>();

        // 如果已经绑定他趣账号
        if (result instanceof LoginVo) {
            LoginVo loginVo = (LoginVo) result;
            //用户登录版本限制
            accountsService.loginVersionLimit(loginVo.getUuid(), platformId, appVersion);

            // 保存登录行为信息
            me().mergeLoginActionInfo(soaBasicParams, loginVo.getUuid(), ActionModeEnum.WECHAT.value());
            // 推入队列告知反垃圾系统
            antiSpamMqUtil.pushToAccountActionMq(loginVo.getUuid(), "", AccountActionTypeEnum.LOGIN,null);
            antiSpamMqUtil.pushToUuidAFRefreshMq(loginVo.getUuid(), appcode, cloned, appVersion, ip);
            resultMap.putAll(accountsThirdPartyService.handelThirdLoginResult(loginVo, versionNumber));

            //推送队列
            accountsService.loginoutBury(token, platformId, appcode, loginVo.getUuid(), "1", cloned);
            // 登陆成功推送AdmpDeviceLogin
            accountsService.pushToAdmpDeviceLogin(token, loginVo.getUuid(), loginVo.getSex_type(), DateUtil.currentTimeSeconds(), platformId, appcode, cloned, ip);
        } else {
            WeChatLoginVo loginVo = (WeChatLoginVo) result;
            if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.WeChat.getValue(), loginVo.getOpen_id())){
                ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
            }
            resultMap.put("bind_info", String.valueOf(loginVo.getBind_info()));
            resultMap.put("nickname", loginVo.getNickname());
            resultMap.put("sex_type", String.valueOf(loginVo.getSex_type()));
            resultMap.put("avatar", loginVo.getAvatar());
            resultMap.put("union_id", loginVo.getUnion_id());
            resultMap.put("open_id", loginVo.getOpen_id());
            BuryService.preRegBuryToAdmp(PreRegisterTypeEnum.WECHAT.getType());
            channelHolder.via(loginVo.getOpen_id(), RegStyleEnum.WECHAT.name());
        }

        return JsonResult.success(resultMap);
    }

    /**
     * 苹果第三方登录
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=appleLogin")//weChatLogin
    public JsonResult appleLogin(RequestParams params) {
        SoaBaseParams soaBasicParams = RequestParams.getSoa_basic_java();
        Integer platformId = soaBasicParams.getPlatform_id();
        String token = soaBasicParams.getToken();
        Integer appcode = soaBasicParams.getAppcode();
        Integer cloned = soaBasicParams.getCloned();
        Long appVersion = soaBasicParams.getApp_version();
        String ip = soaBasicParams.getIp();
        
        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }
        
        // 2024.06.18 登录注册管控，抛异常
        accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.LOGIN, RegisterLoginControlIsControlReqDto.model.APPLE);

        //请求参数
        String identifyToken = params.getFormString(0);
        String code = params.getFormString(1);
        String shumeiDeviceId = params.getFormStringOption(2);
        if (StringUtils.isBlank(shumeiDeviceId)) {
            log.warn("appleLogin shumeiDeviceId isBlank");
        }

        int versionNumber = params.getFormIntegerDefault(3, 0);
        // 2021.11.10 新增参数 ios的bundleId
        String bundleId = params.getFormStringDefault(4, "");
        String extraJson = params.getFormStringDefault(5, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        log.info("appleLogin bundleId:{}, extraJson:{}", bundleId, extraJson);
        Object result = accountsThirdPartyService.appleLogin(identifyToken, code, versionNumber, bundleId, shumeiDeviceId, extraJson);
        Map<String, Object> resultMap = new HashMap<>();

        // 如果已经绑定他趣账号
        if (result instanceof LoginVo) {
            LoginVo loginVo = (LoginVo) result;

            //用户登录版本限制
            accountsService.loginVersionLimit(loginVo.getUuid(), platformId, appVersion);

            // 保存登录行为信息
            me().mergeLoginActionInfo(soaBasicParams, loginVo.getUuid(), ActionModeEnum.APPLE.value());
            // 推入队列告知反垃圾系统
            antiSpamMqUtil.pushToAccountActionMq(loginVo.getUuid(), "", AccountActionTypeEnum.LOGIN,null);
            antiSpamMqUtil.pushToUuidAFRefreshMq(loginVo.getUuid(), appcode, cloned, appVersion, ip);
            //特殊逻辑，解决苹果注册使用age,导致弹框问题，只为
            Integer ageVO=loginVo.getAge();
            if(ageVO==null || ageVO<=0){
                loginVo.setAge(18);
            }
            resultMap.putAll(accountsThirdPartyService.handelThirdLoginResult(loginVo, versionNumber));

            //推送队列
            accountsService.loginoutBury(token, platformId, appcode, loginVo.getUuid(), "1", cloned);
            // 登陆成功推送AdmpDeviceLogin
            accountsService.pushToAdmpDeviceLogin(token, loginVo.getUuid(), loginVo.getSex_type(), DateUtil.currentTimeSeconds(), platformId, appcode, cloned, ip);
        } else {
            AppleLoginVo loginVo = (AppleLoginVo) result;
            if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Apple.getValue(), loginVo.getOpen_id())){
                ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
            }
            resultMap.put("bind_info", String.valueOf(loginVo.getBind_info()));
            resultMap.put("open_id", loginVo.getOpen_id());
            resultMap.put("token", loginVo.getAccess_token());
            channelHolder.via(loginVo.getOpen_id(),  RegStyleEnum.APPLE.name());
        }
        return JsonResult.success(resultMap);
    }

    /**
     * 查询微信第三方信息
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=queryWechatUserInfo")
    public JsonResult queryWechatUserInfo(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        String code = params.getFormString(1);

        Map<String, Object> result = accountsThirdPartyService.queryWechatUserInfo(code);
        Map<String, Object> resultMap = new HashMap<>();

        if(resultMap != null) {
            resultMap.put("nickname", MapUtils.getString(result, "nickname", ""));
            resultMap.put("avatar", MapUtils.getString(result, "headImgUrl", ""));
            resultMap.put("unionid", MapUtils.getString(result, "unionid", ""));
            resultMap.put("openid", MapUtils.getString(result, "openid", ""));
        }

        return JsonResult.success(resultMap);
    }

    /**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 客户端接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

    /**
     * ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 其他系统接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
     **/

    private AccountsThirdPartyController me() {
        return applicationContext.getBean(AccountsThirdPartyController.class);
    }

    @Async
    public void mergeLoginActionInfo(SoaBaseParams soaBasicJava, String accountUuid, String actionMode) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }

        LoginActionInfo loginActionInfo = new LoginActionInfo();
        loginActionInfo.setAccountUuid(accountUuid);
        loginActionInfo.setToken(soaBasicJava.getToken());
        loginActionInfo.setActionIp(soaBasicJava.getIp());
        loginActionInfo.setAppcode(soaBasicJava.getAppcode());
        loginActionInfo.setCloned(soaBasicJava.getCloned());
        loginActionInfo.setPlatformId(soaBasicJava.getPlatform_id());
        loginActionInfo.setChannel(soaBasicJava.getChannel());
        loginActionInfo.setAppVersion(soaBasicJava.getApp_version());
        loginActionInfo.setCreateTime(DateUtil.currentTimeSeconds());
        loginActionInfo.setActionMode(actionMode);
        loginActionInfo.setPlatformName(soaBasicJava.getPlatform_name());
        loginActionInfo.setAccess(soaBasicJava.getAccess());
        loginActionInfo.setAlias(soaBasicJava.getAlias());
        loginActionInfo.setGender(soaBasicJava.getGender());
        if(ToolsService.loginActionInfoSwitchWrite.isOn(true)) {
            loginActionInfo.setLongitude(soaBasicJava.getLongitude());
            loginActionInfo.setLatitude(soaBasicJava.getLatitude());
        }else {
            log.info("login_action_info不写加密字段");
            loginActionInfo.setLongitude("");
            loginActionInfo.setLatitude("");
        }

        loginActionInfo.setLongitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLongitude()));
        loginActionInfo.setLatitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLatitude()));
        loginActionInfo.setCity(soaBasicJava.getCity());
        accountActionService.merge(loginActionInfo);
    }

    @RequestMapping(params = "method=getRandomNickname")
    public JsonResult getRandomNickname(RequestParams params) {
        Integer gender = params.getFormIntegerDefault(0, 1);
        Map<String, Object> result = Maps.newHashMap();
        result.put("random_name", accountsThirdPartyService.getRandomNickname(gender));
        return JsonResult.success(result);
    }
    /**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 其他系统接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

    /**
     * 绑定的第三方信息状态
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=isBindThirdParty")
    public JsonResult isBindThirdParty(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = accountsThirdPartyService.getAccountsThirdPartyBindStatus(accountUuid);
        return JsonResult.success(result);
    }

    @RequestMapping(params = "method=getAccountWeChatOpenId")
    public JsonResult getAccountWeChatOpenId(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = accountsThirdPartyService.getAccountWeChatOpenId(accountUuid);
        return JsonResult.success(result);
    }

    @Deprecated
    @RequestMapping(params = "method=batchGetAccountWeChatOpenId")
    public JsonResult batchGetAccountWeChatOpenId(RequestParams params) {
        String[] accountUuids = params.getFormStringArray(0);
        log.info("batchGetAccountWeChatOpenId accountUuids:{}", accountUuids);
        return JsonResult.success(accountsThirdPartyService.batchGetAccountWeChatOpenId(accountUuids));
    }

}


