package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.common.BanOpenIdTypeEnum;
import cn.taqu.account.common.PrometheusMetricsEnum;
import cn.taqu.account.constant.BizConst;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.dao.AccountsCertificationHistoryDao;
import cn.taqu.account.search.ChatRealCertificationLogSearch;
import cn.taqu.account.service.*;
import cn.taqu.account.utils.ShowDialogUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户认证
 * Created by cqa on 2017/10/25.
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=certification")
public class CertificationController {
    @Autowired
    private AccountsCertificationService accountsCertificationService;
    @Autowired
    private AccountsCertificationHistoryDao accountsCertificationHistoryDao;
    @Autowired
    private AccountsCertificationChangeLogService accountsCertificationChangeLogService;
    @Autowired
    private RiskService riskService;
    @Autowired
    private CareModelService cardModelService;

    /**
     * 取消认证
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cancelCertification")
    public JsonResult cancelCertification(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String token = SoaBaseParams.fromThread().getToken();
        Map<String, String> result = accountsCertificationService.cancelCertification(accountUuid, token);
        return JsonResult.success(result);
    }

    /**
     * 根据uuid批量获取认证信息（未加密）
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getByUuids")
    public JsonResult getByUuids(RequestParams params) {
        String[] account_uuids = params.getFormStringArray(0);
        Map<String, Map<String, String>> result = accountsCertificationService.getByUuids(account_uuids);
        return JsonResult.success(result);
    }

    /**
     * 根据uuid获取认证信息（优先返回应用级实名认证，再然后业务级实名认证）
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfoByUuid")
    public JsonResult getByUuid(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, String> result = accountsCertificationService.getCertInfoByUuid(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * 根据uuid批量获取认证信息（加密）
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listInfoByAccountUuid")
    public JsonResult listInfoByUuids(RequestParams params) {
        String[] accountUuids = params.getFormStringArray(0);
        Map<String, Map<String, String>> result = accountsCertificationService.listInfoByAccountUuid(accountUuids);
        return JsonResult.success(result);
    }

    /**
     * 根据uuid批量获取认证信息与同身份证号的关联uuid
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getRelateAccountByUuids")
    public JsonResult getRelateAccountByUuids(RequestParams params) {
        String[] account_uuids = params.getFormStringArray(0);
        Map<String, Map<String, String>> result = accountsCertificationService.getRelateAccountByUuids(account_uuids);
        return JsonResult.success(result);
    }

    /**
     * 根据身份证号关联查询关联过的账号
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listRelateAccountByIdentityNo")
    public JsonResult listRelateAccountByIdentityNo(RequestParams params) {
    	String identityNo = params.getFormString(0);
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);

        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
        if (StringUtils.isNotBlank(clonedStr)) {
            cloned = Integer.valueOf(clonedStr);
        }
        log.info("listRelateAccountByIdentityNo cloned: {}", cloned);

        List<String> list = accountsCertificationService.listRelateAccountByIdentityNo(cloned, identityNo);
    	return JsonResult.success(list);
    }

    /**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 客户端接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

    /**
     * 判断使用那种认证方式
     *
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCertificationType")
    public JsonResult getCertificationType(RequestParams params) {
        Map<String, String> result = AccountsCertificationService.getCertificationTypeMap();
    	JsonResult success = JsonResult.success();
    	success.setData(result);
    	return success;
    }

    /**
     * 应用级实名认证
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initCertifyV2")
    public JsonResult initCertifyV2(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CERTIFICATION_INIT_CERTIFY_V2_COUNTER);
        String accountUuid = params.getFormString(0);
        String certName = params.getFormStringOption(1);
        String certNo = params.getFormStringOption(2);
        //if whether need certName and certNo, 1 - need, 0 - needn't
        Integer certType = params.getFormIntegerDefault(3, BizConst.NEED_CERTIFICATION_INFO);

        log.info("initCertify certType: {}", certType);

        if (certType == BizConst.NEED_CERTIFICATION_INFO) {
            if (StringUtils.isBlank(certName) || StringUtils.isBlank(certNo)) {
                throw new ServiceException(CodeStatus.IDENTITY_INVALID);
            }
            if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Identity.getValue(), certNo)){
                ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
            }
        } else {
            if (StringUtils.isNotBlank(certNo)) {
                if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Identity.getValue(), certNo)){
                    ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
                }
            }
        }

        // 可能有x结尾的，统一转大写
        certNo = certNo.toUpperCase();
        Map<String, String> result = accountsCertificationService.initCertifyV2(accountUuid, certName, certNo, certType);
//        accountsCertificationService.delAccountsCertificationCache(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * 聊天室实名认证
     */
    @Deprecated
    @RequestMapping(params = "method=initCertifyForChat")
    public JsonResult initCertifyForChat(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CERTIFICATION_INIT_CERTIFY_V2_COUNTER);
        String accountUuid = params.getFormStringOption(0);
        String certName = params.getFormString(1);
        String certNo = params.getFormString(2);
        // 可能有x结尾的，统一转大写
        certNo = certNo.toUpperCase();

        if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Identity.getValue(), certNo)){
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }
        Map<String, String> result = accountsCertificationService.initCertifyForBiz(accountUuid, certName, certNo, 1);
        return JsonResult.success(result);
    }

    /**
     * 业务级实名认证初始化（第一步）
     * 替换 initCertifyForChat 方法
     */
    @RequestMapping(params = "method=initCertifyForBiz")
    public JsonResult initCertifyForBiz(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CERTIFICATION_INIT_CERTIFY_V2_COUNTER);
        String accountUuid = params.getFormString(0);
        String certName = params.getFormString(1);
        String certNo = params.getFormString(2);
        Integer source = params.getFormInteger(3);

        // 可能有x结尾的，统一转大写
        certNo = certNo.toUpperCase();
        if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Identity.getValue(), certNo)){
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }
        Map<String, String> result = accountsCertificationService.initCertifyForBiz(accountUuid, certName, certNo, source);
        return JsonResult.success(result);
    }

    /**
     * 腾讯云实名认证
     * 校验实名认证结果
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=syncCertifyV2")
    public JsonResult syncCertifyV2(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CERTIFICATION_SYNC_CERTIFY_V2_COUNTER);
        String orderno = params.getFormString(0);
        // 2021.04.28 新增参数 黑名单实名认证后加入白名单 http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
        String certPhotoUrl = params.getFormStringDefault(1, "");
        accountsCertificationService.syncCertifyV2(orderno);
        return JsonResult.success();
    }

    /**
     * 聊天室实名认证
     * 校验实名认证结果
     */
    @Deprecated
    @RequestMapping(params = "method=syncCertifyForChat")
    public JsonResult syncCertifyForChat(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CERTIFICATION_SYNC_CERTIFY_V2_COUNTER);
        String orderno = params.getFormString(0);
        // 2021.04.28 新增参数 黑名单实名认证后加入白名单 http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
        String certPhotoUrl = params.getFormStringDefault(1, "");

        accountsCertificationService.syncCertifyForBiz(orderno, 1);
        return JsonResult.success();
    }

    /**
     * 业务级实名认证
     * 校验实名认证结果（第二步）
     * 替换 syncCertifyForChat 方法
     */
    @RequestMapping(params = "method=syncCertifyForBiz")
    public JsonResult syncCertifyForBiz(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CERTIFICATION_SYNC_CERTIFY_V2_COUNTER);
        String orderno = params.getFormString(0);
        // 2021.04.28 新增参数 黑名单实名认证后加入白名单 http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
        String certPhotoUrl = params.getFormStringDefault(1, "");
        Integer source = params.getFormInteger(2);

        accountsCertificationService.syncCertifyForBiz(orderno, source);
        return JsonResult.success();
    }

    /**
     * 实名认证比对（用于账号找回）
     */
    @RequestMapping(params = "method=validRealNameForRetrieve")
    public JsonResult validRealNameForRetrieve(RequestParams params) {
        String uuid = params.getFormString(0);
        String realName = params.getFormStringDefault(1, "");
        String identityNo = params.getFormString(2);
        log.info("validRealNameForRetrieve：{}, {}, {}", uuid, realName, identityNo);
        Boolean result = accountsCertificationService.validRealName(uuid, realName, identityNo);
        return JsonResult.success(ImmutableMap.of("isPass", result));
    }

    /**
     * 根据身份证查询历史变更记录
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getChangeLogListByIdentityNo")
    public JsonResult getChangeLogListByIdentityNo(RequestParams params) {
        String identityNo = params.getFormString(0);
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);

        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
        if (StringUtils.isNotBlank(clonedStr)) {
            cloned = Integer.valueOf(clonedStr);
        }
        log.info("getChangeLogListByIdentityNo cloned: {}", cloned);

        // 可能有x结尾的，统一转大写
        identityNo = identityNo.toUpperCase();

        List<Map<String, Object>> changeLogList = accountsCertificationChangeLogService.getChangeLogList(cloned, identityNo);
        return JsonResult.success(changeLogList);
    }

    /**
     * 根据身份证查询历史变更记录
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getChangeLogListByAccountUuid")
    public JsonResult getChangeLogListByAccountUuid(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);

        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
        if (StringUtils.isNotBlank(clonedStr)) {
            cloned = Integer.valueOf(clonedStr);
        }
        log.info("getChangeLogListByAccountUuid cloned: {}", cloned);

        List<Map<String, Object>> changeLogList = accountsCertificationChangeLogService.getChangeLogListByAccountUuid(cloned, accountUuid);
        return JsonResult.success(changeLogList);
    }

    /**
     * 获取用户的业务级认证状态及绑定信息
     */
    @RequestMapping(params = "method=isNeedCertInfo")
    public JsonResult isNeedCertInfo(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, Object> res = accountsCertificationService.isNeedCertInfo(accountUuid);
        return JsonResult.success(res);
    }

    /**
     * 业务级实名认证前查询接口
     */
    @RequestMapping(params = "method=initCertifyConfigFoBiz")
    public JsonResult initCertifyConfigFoBiz(RequestParams params) {
        Map<String, Object> res = accountsCertificationService.initCertifyConfigFoBiz();
        return JsonResult.success(res);
    }


    /**
     * 非中国大陆居民实名认证接口，目前直接在接口内认证
     *
     */
    @RequestMapping(params = "method=initCertifyForNonIdCard")
    public JsonResult initCertifyForNonIdCard(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CERTIFICATION_INIT_CERTIFY_NONIDCARD_COUNTER);
        String accountUuid = params.getFormString(0);
        Integer approveType = params.getFormInteger(1);
        Integer identityType = params.getFormInteger(2);
        String certName = params.getFormStringDefault(3, "");
        String certNo = params.getFormStringDefault(4, "");
        Integer source = params.getFormIntegerDefault(5, null);
        //if whether need certName and certNo, 1 - need, 0 - needn't
        Integer certType = params.getFormIntegerDefault(4, BizConst.NEED_CERTIFICATION_INFO);

        // 可能有x结尾的，统一转大写
        certNo = certNo.toUpperCase();

        if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Identity.getValue(), certNo)){
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }
        accountsCertificationService.initCertifyForNonIdCard(accountUuid, approveType, identityType, certName, certNo, source, certType);
        return JsonResult.success();
    }

    /**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 客户端接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

    /**
     * 修改支付宝账号
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateRewardAccount")
    public JsonResult updateRewardAccount(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String rewardAccount = params.getFormString(1);
        String operatorName = params.getFormString(2);
        accountsCertificationService.updateRewardAccount(accountUuid, rewardAccount, operatorName, false);
        return JsonResult.success();
    }

    /**
     * 修改支付宝账号，校验：短信
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateRewardAccountByCode")
    public JsonResult updateRewardAccountByCode(RequestParams params){
        String accountUuid = params.getFormString(0);
        String account = params.getFormString(1);
        String vcode = params.getFormString(2);
        accountsCertificationService.updateRewardAccountByCode(accountUuid,account,vcode, false);
        return JsonResult.success();
    }

    /**
     * 修改支付宝账号，校验：短信，是否完成SDK验证
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateRewardAccountByCodeV2")
    public JsonResult updateRewardAccountByCodeV2(RequestParams params){
        String accountUuid = params.getFormString(0);
        String account = params.getFormString(1);
        String vcode = params.getFormString(2);
        accountsCertificationService.updateRewardAccountByCodeV2(accountUuid,account,vcode);
        return JsonResult.success();
    }

    /**
     * 查询身份证绑定的账号
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCheckedAccountUuidByIdentityNo")
    public JsonResult getCheckedAccountUuidByIdentityNo(RequestParams params){
        String identityNo = params.getFormString(0);
        identityNo = StringUtils.trim(identityNo);
        // 可能有x结尾的，统一转大写
        identityNo = identityNo.toUpperCase();
        String result = accountsCertificationService.getCheckedAccountUuidByIdentityNo(identityNo);
        JsonResult success = JsonResult.success();
        success.setData(result);
        return success;
    }

    /**
     * 支付宝账号验证预咨询
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initCertverifyRewardAccount")
    public JsonResult initRewardAccount(RequestParams params){
        String accountUuid = params.getFormString(0);
        String rewardAccount = params.getFormString(1);
        Map<String, String> result = accountsCertificationService.initCertverifyRewardAccount(accountUuid, rewardAccount);
        return JsonResult.success(result);
    }

    /**
     * 支付宝账号验证咨询结果
     * @param params
     * @return
     */
    @RequestMapping(params = "method=syncCertverifyRewardAccount")
    public JsonResult syncCertverifyRewardAccount(RequestParams params){
        String accountUuid = params.getFormString(0);
        String accessToken = params.getFormString(1);
        Map<String, String> result = accountsCertificationService.syncCertverifyRewardAccount(accountUuid, accessToken);
        return JsonResult.success(result);
    }

    /**
     * 获取验证结果
     * @param params
     * @return
     */
    @RequestMapping(params = "method=checkCertverifyRewardAccount")
    public JsonResult checkCertverifyRewardAccount(RequestParams params){
        String accountUuid = params.getFormString(0);
        String rewardAccount = params.getFormString(1);
        String alipayAuthCode = params.getFormString(2);
        Map<String, String> result = accountsCertificationService.checkCertverifyRewardAccount(accountUuid, rewardAccount, alipayAuthCode);
        return JsonResult.success(result);
    }

    /**
     * 获取验证结果
     * @param params
     * @return
     */
    @RequestMapping(params = "method=checkMyCertverifyRewardAccount")
    public JsonResult checkMyCertverifyRewardAccount(RequestParams params){
        String accountUuid = params.getFormString(0);
        String alipayAuthCode = params.getFormString(1);
        Map<String, String> result = accountsCertificationService.checkMyCertverifyRewardAccount(accountUuid, alipayAuthCode);
        return JsonResult.success(result);
    }

    /**
     * 支付宝账号验证预咨询 已绑定
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initBindCertverifyRewardAccount")
    public JsonResult initBindCertverifyRewardAccount(RequestParams params){
        String accountUuid = params.getFormString(0);
        Map<String, String> result = accountsCertificationService.initCertverifyRewardAccount(accountUuid, null);
        return JsonResult.success(result);
    }

    /**
     * 取消业务级实名认证
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cancelChatRealCertification")
    public JsonResult cancelChatRealCertification(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String token = SoaBaseParams.fromThread().getToken();
        Map<String, String> result = accountsCertificationService.cancelCertificationForChat(accountUuid, false, token);
        return JsonResult.success(result);
    }


    /**
     * 获取业务级实名认证记录
     */
    @RequestMapping(params = "method=getChatRealCertificationList")
    public JsonResult getChatRealCertificationList(RequestParams params) {
        Integer pageNumber = params.getFormIntegerDefault(0, 1);
        Integer pageSize = params.getFormIntegerDefault(1, 20);
        Long startTime = params.getFormLongOption(2);
        Long endTime = params.getFormLongOption(3);
        String certificationStatus = params.getFormStringOption(4);
        String accountUuid = params.getFormStringOption(5);
        String nickname = params.getFormStringDefault(6, null);

        log.info("request:{},{},{},{},{},{}", pageNumber, pageSize, startTime, endTime, certificationStatus, accountUuid);

        ChatRealCertificationLogSearch search = ChatRealCertificationLogSearch.builder()
                .start_time(startTime == 0 ? null : startTime)
                .end_time(endTime == 0 ? null : endTime)
                .verify_status(StringUtils.isBlank(certificationStatus) ? null : Integer.parseInt(certificationStatus))
                .account_uuid(accountUuid)
                .build();

        List<Map<String, Object>> res = accountsCertificationService.getChatRealCertificationList(pageNumber, pageSize, search);
        return JsonResult.success(res);
    }

    /**
     * 获取用户身份证年龄
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getIdentityNoBirth")
    public JsonResult getIdentityNoBirth(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        Long birth=accountsCertificationService.getIdentityNoBirth(accountUuid);
        return JsonResult.success(birth);
    }

    /**
     * 批量获取用户认证年龄
     * 1、先获取应用认证
     * 2、再取业务认证
     * 3、最后取用户输入
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCertificationAges")
    public JsonResult getCertificationBirths(RequestParams params) {
        String uuids = params.getFormStringOption(0);
//        String uuids="[\"bijb584vuis6\",\"celp955qurn8\",\"cg8m7pydwjfh\",\"timlij44n2y\",\"a9wt2ox9u1v\",\"a9e3il4qaul\"]";
        if(StringUtils.isBlank(uuids)){
            return JsonResult.failed("参数校验失败");
        }
        List<String> uuidList= JsonUtils.stringToObject(uuids,List.class);
        if(CollectionUtils.isEmpty(uuidList)) {
            log.warn("getCertificationAges传入uuid为空");
            return JsonResult.success(Maps.newHashMap());
        }
        Map<String,String> birthMap=accountsCertificationService.getIdentityNoBirthByUuids(uuidList);
        return JsonResult.success(birthMap);
    }

    /**
     * 批量获取用户认证生日
     * 1、先判断是否认证
     * 2、已经认证，先获取应用认证
     * 3、否则取用户输入生日
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getAppCertificationBirths")
    public JsonResult getAppCertificationBirths(RequestParams params) {
        String uuids = params.getFormStringOption(0);
//        String uuids="[\"bijb584vuis6\",\"celp955qurn8\",\"cg8m7pydwjfh\",\"timlij44n2y\",\"a9wt2ox9u1v\",\"a9e3il4qaul\"]";
        if(StringUtils.isBlank(uuids)){
            return JsonResult.failed("参数校验失败");
        }
        List<String> uuidList= JsonUtils.stringToObject(uuids,List.class);
        if(uuidList.size()>20){
            return JsonResult.failed("参数校验失败,查询记录数不能超过20");
        }

        if(CollectionUtils.isEmpty(uuidList)) {
            log.warn("getAppCertificationBirths传入uuid为空");
            return JsonResult.success(Maps.newHashMap());
        }

        Map<String,String> birthMap=accountsCertificationService.getAppIdentityNoBirthByUuids(uuidList);
        return JsonResult.success(birthMap);
    }

    /**
     * 两个账号绑定的有效身份证是否一直</br>
     * 用于判断主包和女包两个账号是否都绑定了同一个身份证
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=isSameIdentityNo")
    public JsonResult isSameIdentityNo(RequestParams params) {
        String accountUuid1 = params.getFormString(0);
        String accountUuid2 = params.getFormString(1);
        Integer isSameIdentityNo = accountsCertificationService.isSameIdentityNo(accountUuid1, accountUuid2);
        return JsonResult.success(isSameIdentityNo);
    }

    @RequestMapping(params = "method=getIdentityType")
    public JsonResult getIdentityType(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer identityType = accountsCertificationHistoryDao.getIdentityType(accountUuid);
        HashMap<String, Integer> map = Maps.newHashMap();
        map.put("identity_type", identityType);
        return JsonResult.success(map);
    }

    /**
     * 是否关怀模式
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=careModel")
    public JsonResult careModel(RequestParams params) {
        String accountUuid = params.getFormString(0);
        return JsonResult.success(cardModelService.isCareModel(accountUuid));
    }

}
