package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.dao.RegisterActionInfoDao;
import cn.taqu.account.search.RandomNicknameSearch;
import cn.taqu.account.service.RegisterActionInfoService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 注册接口
 *
 * <AUTHOR>
 * @date 2024/11/15 17:52
 */
@RestController
@RequestMapping(value = "/api", params = "service=register")
public class RegisterController {

    @Resource
    private RegisterActionInfoService registerActionInfoService;

    @RequestMapping(params = "method=listInfo")
    public JsonResult listInfo(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        return JsonResult.success(registerActionInfoService.listInfo(Lists.newArrayList(uuids)));
    }
}
