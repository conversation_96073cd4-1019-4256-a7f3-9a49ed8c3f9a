package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.life.AccountsLifeDto;
import cn.taqu.account.service.AccountsLifeService;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.service.ClickFilterService;
import cn.taqu.account.utils.ValidatorUtil;
import cn.taqu.account.vo.life.GetAccountsLifeVo;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 我的生活
 *
 * <AUTHOR>
 * 2024年11月25日下午5:14:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=accountsLife")
public class AccountsLifeController {

    @Autowired
    private AccountsLifeService accountsLifeService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private ClickFilterService clickFilterService;

    /**
     * 获取用户自己的我的生活
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getAccountsLife")
    public JsonResult getAccountsLife(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        GetAccountsLifeVo getAccountsLifeVo = accountsLifeService.getAccountsLifeForClientMyself(accountUuid);
        return JsonResult.success(getAccountsLifeVo);
    }


    /**
     * 设置我的生活
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=setAccountsLife")
    public JsonResult setAccountsLife(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        String bucket = params.getFormStringDefault(1, AccountsPhotoService.DEFAULT_AVATAR_BUCKET);
        // AccountsLifeDto
        String photoStr = params.getFormString(2);
        String smid = params.getFormStringDefault(3, CommConst.OLD_CLIENT_PACKAGE);

        if (!clickFilterService.isValidClick(ClickFilterService.TYPE_SET_ACCOUNTS_LIFE, accountUuid, 2)) {
            throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
        }

        AccountsLifeDto accountsLifeDto = JsonUtils.stringToObject2(photoStr, new TypeReference<AccountsLifeDto>() {});
        List<String> validateReturnMessage = ValidatorUtil.validateReturnMessage(accountsLifeDto);
        if(CollectionUtils.isNotEmpty(validateReturnMessage)) {
            log.warn("设置我的生活传参异常，accountUuid={}, photoStr={}, errorMsg={}", accountUuid, photoStr, JsonUtils.objectToString(validateReturnMessage));
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }

        accountsLifeService.setAccountsLife(accountUuid, bucket, accountsLifeDto, smid);
        // 判断是否完成社区任务
//        accountsPhotoService.checkFinishIncentiveTaskWithoutCheck(accountUuid);
        return JsonResult.success();
    }

    /**
     * 删除我的生活
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=delAccountsLife")
    public JsonResult delAccountsLife(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        Integer seq = params.getFormInteger(1);

        if (!clickFilterService.isValidClick(ClickFilterService.TYPE_DEL_ACCOUNTS_LIFE, accountUuid, 1)) {
            throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
        }

        accountsLifeService.delAccountsLife(accountUuid, seq);
        accountsLifeService.refreshAllure(accountUuid);
        return JsonResult.success();
    }

    /**
     * 设置我的生活排序
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=setAccountsLifeSeq")
    public JsonResult setAccountsLifeSeq(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        String seq = params.getFormString(1);
        accountsLifeService.setAccountsLifeSeq(accountUuid, seq);
        return JsonResult.success();
    }


}
