package cn.taqu.account.controller.api.jrpc;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.service.AccountsDestroyLogService;
import cn.taqu.account.utils.RequestParamsUtil;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(value = "api", params = "service=accountsDestroyLog")
public class AccountsDestroyLogController {

    @Autowired
    private AccountsDestroyLogService accountsDestroyLogService;

    /**
     * 判断手机号是否注销过
     * @param params
     * @return
     */
    @RequestMapping(params = "method=isMobileDestroy")
    public JsonResult isMobileDestroy(RequestParams params) {
        // 2023.08.10 增加cloned逻辑
        String mobile = params.getFormString(0);
        // 占位，暂时不用
        Integer appcode = params.getFormInteger(1);
        Integer cloned = params.getFormInteger(2);
        
        return JsonResult.success(accountsDestroyLogService.isMobileDestroy(cloned, mobile));
    }
    
    /**
     * 判断手机号是否注销过
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hasDestroyInMobile")
    public JsonResult hasDestroyInMobile(RequestParams params) {
        // 2023.08.10 增加cloned逻辑
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String[] mobileArr = params.getFormStringArray(0);
        // 占位，暂时不用
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);
        // 有传cloned，则赋值到头参数cloned中
        if(StringUtils.isNotBlank(clonedStr)) {
            if(StringUtils.isNumeric(clonedStr)) {
                soaBaseParams.setCloned(clonedStr);
            }else {
                log.error("业务cloned传值错误，cloned={}", clonedStr);
            }
        }
        RequestParamsUtil.initDefaultAppcodeAndCloned(CommConst.APPCODE_TAQU, CommConst.CLONED_TAQU);
        
        Map<String, Boolean> map = accountsDestroyLogService.hasDestroyInMobile(soaBaseParams.getCloned(), mobileArr);
        return JsonResult.success(map);
    }
}
