package cn.taqu.account.controller.api.jrpc;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AccountsTqcoinLogsService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.StringUtil;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 趣币相关的控制器
 * @ClassName QuCoinController.java
 * <AUTHOR>
 * @date 2015年9月29日 下午2:17:15
 */
@Deprecated
@RestController
@RequestMapping(value="/api", params="service=qucoin")
public class QuCoinController {

	@Autowired
	private AccountsTqcoinLogsService accountsTqcoinLogsService;
	
	/**
	 * 通过uuid获取趣币数，支持批量获取
	 * @param params
	 * @return
	 */
	@Deprecated
	@RequestMapping(params="method=getByUuids")
	public JsonResult getByUuids(RequestParams params) {
		String[] uuids = params.getFormStringArray(0);
		Map<String, Long> tqcoin = accountsTqcoinLogsService.getQuCoinByUuid(uuids);
		return JsonResult.success(tqcoin);
	}

	/**
	 * 根据uuid增加(减少)趣币数
	 * @param params
	 * @return
	 */
	@Deprecated
	@RequestMapping(params="method=addQuCoinByUuid")
	public JsonResult addQuCoinByUuid(RequestParams params) {
		String uuid = params.getFormString(0);
		Integer coinNum = params.getFormInteger(1);
		Integer type = params.getFormInteger(2);
		String info = params.getFormString(3);
		String operator = params.getFormStringOption(4);
		String contentUuid = params.getFormStringOption(5);
		String param = params.getFormStringOption(6);
		String transferAccountUuid = params.getFormStringOption(7);
		
		int finalCoinNum = accountsTqcoinLogsService.addQuCoinByUuid(uuid, coinNum, type, StringUtil.toLong(operator, 0L), info, contentUuid, StringUtil.toLong(param, 0L), transferAccountUuid);
		
		Map<String, Integer> result = new HashMap<>();
		result.put("coin_num", finalCoinNum);
		return JsonResult.success(result);
	}

}
