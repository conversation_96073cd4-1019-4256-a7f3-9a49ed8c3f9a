package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.search.AccountsMedalInfoSearch;
import cn.taqu.account.service.AccountsMedalInfoService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户头像关系表
 */
@Slf4j
@RestController
@RequestMapping(value="/api", params="service=medalInfo")
public class MedalInfoController {

	@Autowired
	private AccountsMedalInfoService accountsMedalInfoService;

	/**
	 * 设置用户头衔
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=setAccountActor")
	public JsonResult setAccountActor(RequestParams params) {
		String accountUuid = params.getFormString(0);
		Integer medalId = params.getFormInteger(1);
		int effectDays = params.getFormInteger(2);
		log.info("SOA设置头衔 accountUuid={} ,medalId={}, effectDays={}", accountUuid, medalId, effectDays);
		accountsMedalInfoService.setAccountActor(accountUuid, medalId, effectDays);
		return JsonResult.success(true);
	}

	/**
	 * 回收用户头衔
	 * @param params
	 * @return
	 */
	@Deprecated
	@RequestMapping(params = "method=revokeAccountActor")
	public JsonResult revokeAccountActor(RequestParams params) {
		String accountUuid = params.getFormString(0);
		Integer medalId = params.getFormInteger(1);
	    log.info("SOA回收用户头衔 accountUuid={} ,medalId={}", accountUuid, medalId);
		accountsMedalInfoService.revokeAccountActor(accountUuid, medalId, 2);
		return JsonResult.success(true);
	}

	/**
	 * 延长头衔有效期
	 * @param params
	 * @return
	 */
	@Deprecated
	@RequestMapping(params = "method=extendEffectDays")
	public JsonResult extendEffectDays(RequestParams params) {
		String accountUuid = params.getFormString(0);
		int effectDays = params.getFormInteger(1);
		accountsMedalInfoService.extendEffectDays(accountUuid, effectDays);
		return JsonResult.success(true);
	}

	/**
	 * 分页查询
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getListDataForPage")
	public JsonResult getListDataForPage(RequestParams params) {
		String queryParams = params.getFormString(0);
		Integer pageNumber = params.getFormIntegerDefault(1,1);
		Integer pageSize = params.getFormIntegerDefault(2,20);
		return JsonResult.success(accountsMedalInfoService.pageQuery(JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(queryParams), AccountsMedalInfoSearch.class), pageNumber, pageSize));
	}
}
