package cn.taqu.account.controller.api.jrpc;


import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AliyunLiveFaceDetectService;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 类命名有点坑，之后可能不是用阿里云
 * 
 * <AUTHOR>
 * @date 2021/06/03
 */
@RequestMapping(value = "/api", params = "service=aliyunFaceDetect")
@RestController
public class AliyunFaceDetectController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunFaceDetectController.class);

    @Autowired
    private AliyunLiveFaceDetectService aliyunLiveFaceDetectService;

    /**
     * 获取用户图片认证信息
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getPhotoInfoByAccountUuid")
    public JsonResult getPhotoInfoByAccountUuid(RequestParams params){
        String accountUuid = params.getFormString(0);
        return JsonResult.success(aliyunLiveFaceDetectService.getPhotoInfoByAccountUuid(accountUuid));
    }

    /**
     * 获取图片和底图的对比结果
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getPhotoCompareResult")
    public JsonResult getPhotoCompareResult(RequestParams params){
        String accountUuid = params.getFormString(0);
        String photoUrl = params.getFormString(1);
        
        // 2021.06.11 安卓特殊处理图片地址，可能出现 
        // https://avatar01.jiaoliuqu.com/https://avatar01.jiaoliuqu.com/taqu_android_avatar_114_1623398231713_1_0_70616.JPEG
        String photoUrlAfter = photoUrl;
        if(StringUtils.countMatches(photoUrl, "https://") > 1) {
            photoUrlAfter = photoUrl.substring(photoUrl.lastIndexOf("https://"));
            LOGGER.warn("客户端接口传参异常，特殊处理后图片地址，photoUrl={}, photoUrlAfter={}", photoUrl, photoUrlAfter);
        }    
        
        Map<String, Object> result = aliyunLiveFaceDetectService.getPhotoCompareResult(accountUuid, photoUrlAfter);
        return JsonResult.success(result);
    }
    
}
