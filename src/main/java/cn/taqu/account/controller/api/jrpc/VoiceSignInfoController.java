package cn.taqu.account.controller.api.jrpc;


import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.taqu.account.service.VoiceSignInfoService;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

@RequestMapping(value = "/api", params = "service=voiceSignInfo")
@RestController
@Slf4j
public class VoiceSignInfoController {

    @Autowired
    private VoiceSignInfoService voiceSignInfoService;

    @RequestMapping(params = "method=getCopywriting")
    public JsonResult getCopywriting(RequestParams params){
        String ramdomCopywiting = voiceSignInfoService.getCopywriting();
        Map<String,Object> map = Maps.newHashMap();
        map.put("copywriting",ramdomCopywiting);
        return JsonResult.success(map);
    }

    @RequestMapping(params = "method=getCopywritingV2")
    public JsonResult getCopywritingV2(RequestParams params){
        String accountUuid = params.getFormStringOption(0);
        String ramdomCopywiting = voiceSignInfoService.getCopywriting(accountUuid);
        Map<String,Object> map = Maps.newHashMap();
        map.put("copywriting",ramdomCopywiting);
        return JsonResult.success(map);
    }

    /**
     * 直接设置语音签名 不审核 用于后台或脚本处理
     *
     * <AUTHOR>
     * @date 2020/05/12 09:38
     * @param params
     * @return
     */
    @RequestMapping(params = "method=setVoiceSignInfoNoAudit")
    public JsonResult setVoiceSignInfoNoAudit(RequestParams params){
		String accountUuid = params.getFormString(0);
		String voiceSignInfo = params.getFormString(1);
		voiceSignInfoService.setVoiceSignInfoNoAudit(accountUuid,voiceSignInfo);
        return JsonResult.success();
    }
    
    /**
     * 重置用户语音签名 -- 风控使用，不对其他业务
     * 
     * @param params
     * @return
     */
    @RequestMapping(params = "method=resetVoiceSignInfo")
    public JsonResult resetVoiceSignInfo(RequestParams params){
        String accountUuid = params.getFormString(0);
        String operator = params.getFormString(1);
        String auditReason = params.getFormString(2);
        
        voiceSignInfoService.resetVoiceSignInfo(accountUuid, operator, auditReason);
        return JsonResult.success();
    }

    /**
     * 获取用户自己的信息
     * <AUTHOR>
     * @date 2020/05/12 19:49
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getVoiceSignInfoByAccountUuid")
    public JsonResult getVoiceSignInfoByAccountUuid(RequestParams params){
        String accountUuid = params.getFormString(0);
        return JsonResult.success(voiceSignInfoService.getVoiceSignInfoByAccountUuid(accountUuid));
    }

    /**
     * 【客户端使用】获取用户语音签名信息
     *
     * @param params 参数个数
     * @return {@link JsonResult}
     */
    @Api
    @RequestMapping(params = "method=getMyVoiceSignInfo")
    public JsonResult getMyVoiceSignInfo(RequestParams params){
        String accountUuid = params.getFormString(0);

        return JsonResult.success(voiceSignInfoService.getVoiceSignInfoByAccountUuid(accountUuid));
    }

}
