package cn.taqu.account.controller.api.jrpc;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.service.AccountsCertService;
import cn.taqu.account.service.AccountsService;
import cn.taqu.account.service.AccountsThirdPartyService;
import cn.taqu.account.vo.AccountLucVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 后台操作
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-21 17:22
 */
@RequestMapping(value = "api", params = "service=backendOperation")
@RestController
@Slf4j
public class BackendOperationController {

    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;
    @Autowired
    private AccountsCertService accountsCertService;

    /**
     * 注销某个用户
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=accountDestroy")
    public JsonResult accountDestroy(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String token = SoaBaseParams.fromThread().getToken();
        String reason = params.getFormStringDefault(1, token + "在后台执行了注销操作");
        log.info("后台账户注销.token={}.uuid={}.reason={}", token, accountUuid, reason);
        accountsService.destroyByAccountUuid(accountUuid, reason, false, false);
        return JsonResult.success();
    }

    /**
     * 获取用户绑定信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getBindInfoByUuid")
    public JsonResult getBindInfoByUuid(RequestParams params){
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = accountsThirdPartyService.getBindInfoByUuid(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * 取消认证
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cancelCert")
    public JsonResult cancelCert(RequestParams params){
        String accountUuid = params.getFormString(0);
        Integer[] cancelTypes = params.getFormIntegerArrayOption(1);
        String token = SoaBaseParams.fromThread().getToken();
        accountsCertService.cancelCert(accountUuid, Arrays.asList(cancelTypes), token);
        return JsonResult.success();
    }

    @Deprecated
    @RequestMapping(params = "method=mobileWarmup")
    public JsonResult mobileWarmup(RequestParams params){
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 认证信息
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=searchCertInfoForBackstage")
    public JsonResult  searchCertInfoForBackstage(RequestParams params){
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }
    
    /**
     * 后台获取真人、实名底图
     * 
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listAccountCertInfoForBackstage")
    public JsonResult listAccountCertInfoForBackstage(RequestParams params){
        String[] accountUuidArr = params.getFormStringArray(0);
        List<String> list = Lists.newArrayList(accountUuidArr);
        Map<String, Map<String, String>> result = accountsService.listAccountCertInfoForBackstage(list);
        return JsonResult.success(result);
    }

    
    /**
     * soa - 添加luc用户
     * 需求文档：https://o15vj1m4ie.feishu.cn/wiki/CP87wW3q1iTjydkPkQncXWMbn1e
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=addAccountForLuc")
    public JsonResult addAccountForBackstage(RequestParams params) {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread().bindToThread();
        if(soaBaseParams.getAppcode() == null) {
            soaBaseParams.setAppcode("1");
        }
        if(soaBaseParams.getCloned() == null) {
            soaBaseParams.setCloned("1");
        }
        String accountData = params.getFormString(0);
        AccountLucVo accountLucVo = JsonUtils.stringToObject2(accountData, new TypeReference<AccountLucVo>() {});
        accountLucVo = accountsService.addAccountForLuc(accountLucVo);
        return JsonResult.success(accountLucVo);
    }
}
