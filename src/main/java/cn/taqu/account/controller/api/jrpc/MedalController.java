package cn.taqu.account.controller.api.jrpc;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.taqu.account.model.AccountsMedal;
import cn.taqu.account.search.AccountsMedalSearch;
import cn.taqu.account.service.AccountsMedalService;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 用户等级勋章相关的控制器
 * @ClassName MedalController.java
 * <AUTHOR>
 * @date 2015年9月28日 下午6:59:31
 */
@RestController
@RequestMapping(value="/api", params="service=medal")
public class MedalController {

	@Autowired
	private AccountsMedalService accountsMedalService;

	/**
	 * 分页查询头衔列表
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getListDataForPage")
	public JsonResult getListDataForPage(RequestParams params) {
		String queryParams = params.getFormString(0);
		Integer pageNumber = params.getFormIntegerDefault(1,1);
		Integer pageSize = params.getFormIntegerDefault(2,20);
		return JsonResult.success(accountsMedalService.pageQuery(JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(queryParams), AccountsMedalSearch.class), pageNumber, pageSize));
	}

	/**
	 * 创建新的头衔
	 * @return
	 */
	@RequestMapping(params = "method=create")
	public JsonResult create(RequestParams params) {
		String levelData = params.getFormString(0);
		AccountsMedal accountsMedal = accountsMedalService.create(JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(levelData), AccountsMedal.class));
		Map<String, String> result = Maps.newHashMap();
		result.put("id", accountsMedal.getId().toString());
		return JsonResult.success(result);
	}

	/**
	 * 获取头衔权限
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getPrivilege")
	public JsonResult getPrivilege(RequestParams params) {
		Long medalId = params.getFormLongOption(0);
		Long privilegeId = params.getFormLongOption(1);
		String[] fields = params.getFormStringArrayOption(2);
		if(medalId==null && privilegeId==null) {
			throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
		}
		return JsonResult.success(accountsMedalService.getPrivilege(medalId, privilegeId, fields));
	}

	/**
	 * 修改头衔
	 * @return
	 */
	@RequestMapping(params = "method=update")
	public JsonResult update(RequestParams params) {
		Long id = params.getFormLong(0);
		String medalData = params.getFormString(1);
		AccountsMedal accountsMedal = accountsMedalService.update(id, JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(medalData), AccountsMedal.class));
		Map<String, String> result = Maps.newHashMap();
		result.put("id", accountsMedal.getId().toString());
		return JsonResult.success(result);
	}

	@Deprecated
	@RequestMapping(params = "method=deleteById")
	public JsonResult deleteById(RequestParams params) {
		Long id = params.getFormLong(0);
		accountsMedalService.deleteById(id);
		return JsonResult.success();
	}

	/**
	 * 更新头衔权限
	 * @param params
	 * @return
	 */
	@Deprecated
	@RequestMapping(params = "method=updatePrivilege")
	public JsonResult updatePrivilege(RequestParams params) {
		Long medalId = params.getFormLong(0);
		if(Objects.equals(0L, medalId)) {
			throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
		}
		Long privilegeId = params.getFormLong(1);
		String privilegeConfig = params.getFormString(2);
		accountsMedalService.updatePrivilege(medalId, privilegeId, JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(privilegeConfig), Map.class));
		return JsonResult.success(true);
	}

	/**
	 * 获取用户等级勋章信息<br/>
	 * <b>service:</b> medal <br/>
	 * <b>method:</b> getInfo <br/>
	 * <b>form:</b> [medalId 要查询的等级勋章配置id, option: String 指定要返回的字段(指定一个字段)或[option1:String 指定要返回的字段1, option2:指定要返回的字段2...](指定多个字段)] option只能是medal_name、pic_url、privilege_id中的一个 <br/>
	 * 没有查询到返回错误，查询到返回option指定的字段或者返回全部字段(没有指定返回字段时)
	 * 
	 * @Title getInfo
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @Date 2015年9月28日 下午7:20:39
	 */
	@RequestMapping(params="method=getInfo")
	public JsonResult getInfo(RequestParams params) {
		Long medalId = params.getFormLong(0);
		String[] fields  = params.getFormStringArrayOption(1);
		Map<String, String> medal = accountsMedalService.getInfo(medalId, fields);
		return JsonResult.success(medal);
	}

	@RequestMapping(params="method=getInfos")
	public JsonResult getInfos(RequestParams params) {
		Long[] medalIds = params.getFormLongArray(0);
		String[] fields  = params.getFormStringArrayOption(1);
		Map<Long, Map<String, String>> medal = accountsMedalService.mGetInfo(Arrays.asList(medalIds), fields);
		return JsonResult.success(medal);
	}
}
