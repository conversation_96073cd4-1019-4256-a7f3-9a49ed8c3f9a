package cn.taqu.account.controller.api.jrpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.search.RandomNicknameSearch;
import cn.taqu.account.service.RandomNicknameService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * @Author: zy
 * @Date: 2020/6/23 11:24
 */
@RestController
@RequestMapping(value = "/api", params = "service=randomNickname")
public class RandomNicknameController {

    @Autowired
    private RandomNicknameService randomNicknameService;


    @RequestMapping(params = "method=findPageList")
    public JsonResult findPageList(RequestParams params) {
        String randomGroupId = params.getFormStringDefault(0, "");
        String sexType = params.getFormStringDefault(1, "");
        String partsOfSpeech = params.getFormStringDefault(2, "");
        String cloned = params.getFormStringDefault(3, "");
        Integer page = params.getFormInteger(4);
        Integer pageSize = params.getFormInteger(5);

        RandomNicknameSearch search = new RandomNicknameSearch();
        search.setRandomGroupId(randomGroupId);
        search.setSexType(sexType);
        search.setPartsOfSpeech(partsOfSpeech);
        search.setCloned(cloned);
        search.setPage(page);
        search.setRows(pageSize);

        return JsonResult.success(randomNicknameService.findPageList(search));
    }

    @RequestMapping(params = "method=batchAdd")
    public JsonResult batchAdd(RequestParams params) {
        String[] randomNicknameArr = params.getFormStringArray(0);
        Integer sexType = params.getFormInteger(1);
        Integer status = params.getFormInteger(2);
        Long groupId = params.getFormLong(3);
        String clone = params.getFormString(4);
        randomNicknameService.batchAdd(randomNicknameArr, sexType, status, groupId, clone);
        return JsonResult.success("新增成功");
    }

    @RequestMapping(params = "method=deleteById")
    public JsonResult deleteById(RequestParams params) {
        Long id = params.getFormLong(0);
        randomNicknameService.deleteById(id);
        return JsonResult.success("删除成功");

    }

}
