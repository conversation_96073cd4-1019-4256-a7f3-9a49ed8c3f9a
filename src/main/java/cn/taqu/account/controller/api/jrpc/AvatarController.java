package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.cron.AvatarGuideCycleTask;
import cn.taqu.account.service.AvatarService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.SpringContextHolder;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 头像业务
 *
 * <AUTHOR>
 * @date 2025/6/23 11:01
 */
@RestController
@RequestMapping(value = "/api", params = "service=avatar")
public class AvatarController {

    @Resource
    private AvatarService avatarService;

    /**
     * 首页弹窗提醒
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=guidePopup")
    public JsonResult guidePopup(RequestParams params) {
        String uuid = params.getFormString(0);
        return JsonResult.success(avatarService.guidePopup(uuid));
    }

    /**
     * 巡检测试
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cycleTest")
    public JsonResult cycleTest(RequestParams params) {
        SpringContextHolder.getBean(AvatarGuideCycleTask.class).avatarGuideCycleTask();
        return JsonResult.success();
    }

    /**
     * 巡检失败测试
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cycleFailTest")
    public JsonResult cycleFailTest(RequestParams params) {
        SpringContextHolder.getBean(AvatarGuideCycleTask.class).cycleFailQueue();
        return JsonResult.success();
    }
}
