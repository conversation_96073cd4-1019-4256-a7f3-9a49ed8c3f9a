package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.dto.approve.ModifyApprovalDto;
import cn.taqu.account.service.AccountFreezeAdminLogoutLogService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "api", params = "service=freeze")
public class AccountFreezeController {

    @Autowired
    private AccountFreezeAdminLogoutLogService accountFreezeAdminLogoutLogService;

    @RequestMapping(params = "method=getAccountFreezeList")
    public JsonResult getAccountFreezeList(RequestParams params) {
        String jsonStr = params.getFormString(0);
        Integer page = params.getFormInteger(1);
        Integer pageSize = params.getFormInteger(2);

        log.info("request param jsonStr: {}, page: {}, pageSize: {}", jsonStr, page, pageSize);

        Map<String, Object> reqParams = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, Object>>() {
        });

        return JsonResult.success(accountFreezeAdminLogoutLogService.getAccountFreezeList(reqParams, page, pageSize));
    }

    @RequestMapping(params = "method=getAccountFreezeAdminLogoutLog")
    public JsonResult getAccountFreezeAdminLogoutLog(RequestParams params) {
        String jsonStr = params.getFormString(0);
        Integer page = params.getFormInteger(1);
        Integer pageSize = params.getFormInteger(2);

        log.info("request param jsonStr: {}, page: {}, pageSize: {}", jsonStr, page, pageSize);


        Map<String, Object> reqParams = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, Object>>() {
        });

        return JsonResult.success(accountFreezeAdminLogoutLogService.getAccountFreezeAdminLogoutLog(reqParams, page, pageSize));
    }

    @RequestMapping(params = "method=freezeLogout")
    public JsonResult freezeLogout(RequestParams params) {
        String uuid = params.getFormString(0);

        log.info("uuid: {}", uuid);

        accountFreezeAdminLogoutLogService.freezeLogout(uuid);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=feishuFreezeLogout")
    public JsonResult freezeLogoutApproval(RequestParams params) {
        String json = params.getFormString(0);
        ModifyApprovalDto dto = JsonUtils.stringToObject(json, ModifyApprovalDto.class);

        accountFreezeAdminLogoutLogService.createFreezeLogoutApproval(dto);
        return JsonResult.success();
    }

}
