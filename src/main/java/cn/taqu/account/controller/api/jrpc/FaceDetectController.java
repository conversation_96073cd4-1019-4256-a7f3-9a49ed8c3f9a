package cn.taqu.account.controller.api.jrpc;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.util.RandomUtil;
import cn.taqu.account.common.LockKey;
import cn.taqu.account.common.PrometheusMetricsEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.model.UnbindWhitelist;
import cn.taqu.account.service.AliyunLiveFaceDetectService;
import cn.taqu.account.service.MessageService;
import cn.taqu.account.service.MonitorService;
import cn.taqu.account.service.UnbindWhitelistService;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 活体认证
 *
 * <AUTHOR>
 * @Classname FaceDetectController
 * @Date 2021/1/20 上午11:29
 */
@Slf4j
@RequestMapping(value = "/api", params = "service=faceDetect")
@RestController
public class FaceDetectController {

    @Autowired
    private AliyunLiveFaceDetectService aliyunLiveFaceDetectService;

    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private UnbindWhitelistService unbindWhitelistService;

    @Autowired
    private MessageService messageService;
    @Autowired
    private RedisLockUtil redisLockUtil;

    @Deprecated
    @RequestMapping(params = "method=detectFace")
    public JsonResult detectFace(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 获取活体人脸检测的请求参数
     * 走腾讯云
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=initCertify")
    public JsonResult initCertify(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 获取活体人脸检测结果
     * 走腾讯云
     */
    @Deprecated
    @RequestMapping(params = "method=getLiveResult")
    public JsonResult getLiveResult(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    @Deprecated
    @RequestMapping(params = "method=saveGender")
    public JsonResult saveGender(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 获取SDK配置
     * 走腾讯云
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initFaceDetectConf")
    public JsonResult initFaceDetectConf(HttpServletRequest servletRequest, RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.INIT_FACE_DETECT_CONF_COUNTER);
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = aliyunLiveFaceDetectService.initFaceDetectConf(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * SDK活体校验结果
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=checkFaceDetectResult")
    public JsonResult checkFaceDetectResult(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CHECK_FACE_DETECT_RESULT_COUNTER);
        String accountUuid = params.getFormString(0);
        String basePhotoUrl = params.getFormStringOption(1);
        String source = params.getFormStringDefault(2, "");
        Map<String, Object> map = aliyunLiveFaceDetectService.checkFaceDetectResult(accountUuid, basePhotoUrl, source);
        return JsonResult.success(map);
    }

    /**
     * 真人认证确认接口（头像-底图）对比并存储
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=compareAndCheckFaceDetect")
    public JsonResult compareAndCheckFaceDetect(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer version = params.getFormIntegerDefault(1, 0);
        
        String lockKey = RedisKeyConstant.REDIS_LOCK_KEY.setArg(LockKey.FACE_COMPARE_AND_AUDIT, accountUuid);
        Map<String, String> map = redisLockUtil.executeWithLock(lockKey, 3000, () -> {
            return aliyunLiveFaceDetectService.compareAndCheckFaceDetect(accountUuid, version);
        });
        
        return JsonResult.success(map);
    }

    /**
     * 获取真人认证状态
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getRealPersonCertification")
    public JsonResult getRealPersonCertification(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, String> map = aliyunLiveFaceDetectService.getRealPersonCertification(accountUuid);
        return JsonResult.success(map);
    }
    /**
     * 根据uuid数组获取活体底图
     * 2024.07.11 后续下线
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listBasePhotoUrlByAccountUuid")
    public JsonResult listBasePhotoUrlByAccountUuid(RequestParams params) {
        String[] accountUuidArr = params.getFormStringArray(0);
        Map<String, String> result = aliyunLiveFaceDetectService.listBasePhotoUrlByAccountUuid(accountUuidArr);
        return JsonResult.success(result);
    }

    @Api
    @RequestMapping(params = "method=getAccountCertification")
    public JsonResult getAccountCertification(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, String> map = aliyunLiveFaceDetectService.getAccountCertification(accountUuid);
        return JsonResult.success(map);
    }

    /**
     * 获取SDK配置
     * 走腾讯云
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initFaceDetectConfForChangeMobile")
    public JsonResult initFaceDetectConfForChangeMobile(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.INIT_FACE_DETECT_CONF_COUNTER);
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = aliyunLiveFaceDetectService.initFaceDetectConfForChangeMobile(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * face detect for change mobile process, return random code to accomplish subsequent procedures if face detect success
     */
    @RequestMapping(params = "method=detectFaceForChangeMobile")
    public JsonResult detectFaceForChangeMobile(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CHECK_FACE_DETECT_RESULT_COUNTER);
        String accountUuid = params.getFormString(0);
        String verifyPhotoUrl = params.getFormStringOption(1);

        // face detect and photo compare
        aliyunLiveFaceDetectService.detectFaceForChangeMobile(accountUuid, verifyPhotoUrl);

        Map<String, String> map = new HashMap<>();

        // random code creativity
        String randomCode = RandomUtil.randomString(8);
        String redisKey = RedisKeyConstant.ACCOUNT_MODIFY_MOBILE_RANDOM.setArg(accountUuid);
        accountStringRedisTemplate.opsForValue().set(redisKey, randomCode, 30, TimeUnit.MINUTES);
        map.put("randomCode", randomCode);

        return JsonResult.success(map);
    }

    /**
     * 获取SDK配置
     * 走腾讯云
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initFaceDetectConfForFinMonitor")
    public JsonResult initFaceDetectConfForFinMonitor(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.INIT_FACE_DETECT_CONF_COUNTER);
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = aliyunLiveFaceDetectService.initFaceDetectConfForCashMonitor(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * face detect for change mobile process, return random code to accomplish subsequent procedures if face detect success
     */
    @RequestMapping(params = "method=detectFaceForFinMonitor")
    public JsonResult detectFaceForFinMonitor(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CHECK_FACE_DETECT_RESULT_COUNTER);
        String accountUuid = params.getFormString(0);
        String verifyPhotoUrl = params.getFormStringOption(1);

        aliyunLiveFaceDetectService.detectFaceForCashMonitor(accountUuid, verifyPhotoUrl);
        return JsonResult.success();
    }

    /**
     * 获取SDK配置[找回账号]
     * 走腾讯云
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initFaceDetectForRetrieve")
    public JsonResult initFaceDetectForRetrieve(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.INIT_FACE_DETECT_CONF_COUNTER);
        String accountUuid = params.getFormString(0);
        log.info("initFaceDetectForRetrieve：{}", accountUuid);
        Map<String, Object> result = aliyunLiveFaceDetectService.initFaceDetectForRetrieve(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * 真人认证结果比对[找回账号]
     */
    @RequestMapping(params = "method=compareDetectFaceForRetrieve")
    public JsonResult compareDetectFaceForRetrieve(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CHECK_FACE_DETECT_RESULT_COUNTER);
        String accountUuid = params.getFormString(0);
        log.info("compareDetectFaceForRetrieve:{}", accountUuid);
        aliyunLiveFaceDetectService.compareDetectFaceForRetrieve(accountUuid);
        return JsonResult.success();
    }

    /**
     * 获取SDK配置
     * 走腾讯云
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initLiveFaceDetect")
    public JsonResult initLiveFaceDetect(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.INIT_FACE_DETECT_CONF_COUNTER);
        String accountUuid = params.getFormString(0);
        String detectScene = params.getFormString(1);
        Map<String, Object> result = aliyunLiveFaceDetectService.initFaceDetectConfForHighRisk(accountUuid, detectScene);
        return JsonResult.success(result);
    }

    /**
     * face detect for change mobile process, return random code to accomplish subsequent procedures if face detect success
     */
    @RequestMapping(params = "method=compareLiveFaceDetect")
    public JsonResult compareLiveFaceDetect(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CHECK_FACE_DETECT_RESULT_COUNTER);
        String accountUuid = params.getFormString(0);
        String detectScene = params.getFormString(1);
        // 2023.11.20 新版腾讯云不返回，需要服务端获取
        String verifyPhotoUrl = params.getFormStringOption(2);
        aliyunLiveFaceDetectService.detectFaceForHighRisk(accountUuid, detectScene, verifyPhotoUrl);
        return JsonResult.success();
    }

    /**
     * 活体第一步，预先校验，非必须，看客户端自己调用
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=precheckLiveFaceDetect")
    public JsonResult precheckLiveFaceDetect(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.PRECHECK_LIVE_FACE_DETECT_COUNTER);
        String accountUuid = params.getFormString(0);
        Integer version = params.getFormIntegerDefault(1, 0);
        aliyunLiveFaceDetectService.precheckLiveFaceDetect(accountUuid, version);
        return JsonResult.success();
    }

    /**
     * 获取SDK配置
     * 走腾讯云
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=initWhiteListFaceDetectConf")
    public JsonResult initWhiteListFaceDetectConf(HttpServletRequest servletRequest, RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.INIT_FACE_DETECT_CONF_COUNTER);
        String accountUuid = params.getFormString(0);
        Long whitelistId = params.getFormLongOption(1);
        //场景，备后续扩展
        // 2024.11.15 扫脸需求借用该接口
        // 可选字段 unbind_old_account、withdrawal_verify
        String scene = params.getFormString(2); 
        
        if(Objects.equals("withdrawal_verify", scene)) {
            /*
             * 提现扫脸需求 https://o15vj1m4ie.feishu.cn/wiki/QF65wbvYyiBtcKkFmbsceCSmnBg
             * 产品不想发包，才借用该接口处理，以下处理和原接口业务完全不同
             */
            Map<String, Object> result = aliyunLiveFaceDetectService.initFaceDetectConfForWithdraw(accountUuid);
            return JsonResult.success(result);
            
        }else {
            
            UnbindWhitelist unbindWhitelist=unbindWhitelistService.getUnbindWhitelistById(whitelistId);
            Integer status=unbindWhitelistService.checkUnbindWhitelist(unbindWhitelist,accountUuid);
            if(status==null || status!=0){
                return JsonResult.failed("白名单校验失败,或者已经解绑");
            }
            String oldUuid=unbindWhitelist.getOldUuid();
            Map<String, Object> result = aliyunLiveFaceDetectService.initWhitelistFaceDetectConf(oldUuid);
            
            return JsonResult.success(result);
        }
        
    }

    /**
     * SDK活体校验结果
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=checkWhiteListFaceDetectResult")
    public JsonResult checkWhiteListFaceDetectResult(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.CHECK_FACE_DETECT_RESULT_COUNTER);
        String accountUuid = params.getFormString(0);
        String basePhotoUrl = params.getFormStringOption(1);
        String source = params.getFormStringDefault(2, "");
        Long whitelistId = params.getFormLongOption(3);
        //场景，备后续扩展
        // 2024.11.15 扫脸需求借用该接口
        // 可选字段 unbind_old_account、withdrawal_verify
        String scene = params.getFormStringDefault(4, "");
        // ios客户端传参错了，兼容旧包
        String detectScene = params.getFormStringDefault(5, "");
        
        if(StringUtils.isBlank(scene)) {
            scene = detectScene;
        }
        
        if(Objects.equals("withdrawal_verify", scene)) {
            /*
             * 提现扫脸需求 https://o15vj1m4ie.feishu.cn/wiki/QF65wbvYyiBtcKkFmbsceCSmnBg
             * 产品不想发包，才借用该接口处理，以下处理和原接口业务完全不同
             */
            Map<String, Object> map = aliyunLiveFaceDetectService.detectFaceForWithdraw(accountUuid);
            return JsonResult.success(map);
        }else {
            UnbindWhitelist unbindWhitelist=unbindWhitelistService.getUnbindWhitelistById(whitelistId);
            Integer status=unbindWhitelistService.checkUnbindWhitelist(unbindWhitelist,accountUuid);
            if(status==null || status!=0){
                return JsonResult.failed("白名单校验失败,或者已经解绑");
            }
            String link=unbindWhitelist.getLink();
            String oldUuid=unbindWhitelist.getOldUuid();
            try{
                Map<String, Object> map = aliyunLiveFaceDetectService.checkWhitelistFaceDetectResult(oldUuid, basePhotoUrl, source);
                String veryfyStatus=MapUtils.getString(map,"veryfy_status");
                if(!"1".equals(veryfyStatus)){
                    sendFailSystemMessage(accountUuid, link);
                    return JsonResult.success(map);
                }
                unbindWhitelistService.cancelCert(oldUuid, whitelistId);
                sendSuccessSystemMessage(accountUuid);
                return JsonResult.success(map);
            }catch (ServiceException se){
                sendFailSystemMessage(accountUuid, link);
                throw se;
            }
        }
        
    }
    
    /**
     * 是否完成提现扫脸
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=isCompleteDetectFaceForWithdraw")
    public JsonResult isCompleteDetectFaceForWithdraw(RequestParams params) {
        String accountUuid = params.getFormString(0);
        boolean completeDetectFaceForWithdraw = aliyunLiveFaceDetectService.isCompleteDetectFaceForWithdraw(accountUuid);
        return JsonResult.success(completeDetectFaceForWithdraw);
    }

    public void sendSuccessSystemMessage(String accountUuid) {

        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info = "您已解除历史账号的实名认证绑定，可重新认证" +
                "\n" +
                "\n" ;
        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "老帐号解绑");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);
        messageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
    }

    public void sendFailSystemMessage(String accountUuid,String link) {

        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info = "认证失败，您可点击 %s，再次尝试认证。" +
                "\n" +
                "\n" ;
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "这里");
        contentReplace.put("r", "m=web&a=url&ul="+link);
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "老帐号解绑");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);
        try{
            messageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
        }catch (Exception e){
            log.warn("sendFailSystemMessage fail accountUuid:{}|link:{}",accountUuid,link,e);
        }
    }

}
