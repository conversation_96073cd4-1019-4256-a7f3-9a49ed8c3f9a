package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.search.RandomGroupSearch;
import cn.taqu.account.service.RandomGroupService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: zy
 * @Date: 2020/6/24 10:19
 */
@RestController
@RequestMapping(value = "/api", params = "service=randomGroup")
public class RandomGroupController {
    @Autowired
    private RandomGroupService randomGroupService;


    @RequestMapping(params = "method=findPageList")
    public JsonResult findPageList(RequestParams params) {

        Integer page = params.getFormInteger(0);
        Integer pageSize = params.getFormInteger(1);

        RandomGroupSearch search = new RandomGroupSearch();
        search.setPage(page);
        search.setRows(pageSize);

        return JsonResult.success(randomGroupService.findPageList(search));
    }

    @RequestMapping(params = "method=findList")
    public JsonResult findList() {
        return JsonResult.success(randomGroupService.findOpenStatusList());
    }

    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params) {
        String groupName = params.getFormString(0);
        String partsOfSpeech = params.getFormString(1);
        String status = params.getFormString(2);

        return JsonResult.success(
            ImmutableMap.of(
                "id",
                randomGroupService.add(groupName, partsOfSpeech, status)
            )
        );
    }

    @RequestMapping(params = "method=edit")
    public JsonResult edit(RequestParams params) {
        Long id = params.getFormLong(0);
        String groupName = params.getFormString(1);
        String partsOfSpeech = params.getFormString(2);
        String status = params.getFormString(3);

        randomGroupService.edit(id, groupName, status, partsOfSpeech);
        return JsonResult.success("修改成功");
    }


    @RequestMapping(params = "method=deleteById")
    public JsonResult deleteById(RequestParams params) {
        Long id = params.getFormLong(0);

        randomGroupService.deleteGroupById(id);
        return JsonResult.success("删除成功");
    }
}
