package cn.taqu.account.controller.api.jrpc;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.service.AccountsHomeCoverService;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 封面
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 14:09
 */
@Deprecated
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=accountsHomeCover")
public class AccountsHomeCoverController {

    @Autowired
    private AccountsHomeCoverService accountsHomeCoverService;

    /**
     * 设置个人主页封面
     * 2024.05.17 下线
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=setAccountsHomeCover")
    public JsonResult setAccountsHomeCover(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 获取历史审核记录
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getHomeCoverCheckLogByUuid")
    public JsonResult getHomeCoverLog(RequestParams params){
        String accontUuid = params.getFormString(0);
        List<Map<String, Object>> homeCoverLogs = accountsHomeCoverService.getHomeCoverCheckLogByUuid(accontUuid);
        return JsonResult.success(homeCoverLogs);
    }

    
}
