package cn.taqu.account.controller.api.jrpc;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.IntroductionPhotoDTO;
import cn.taqu.account.service.AccountsIntroductionService;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.account.utils.LimitModificationUtil;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 自我介绍
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-16 10:01
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=introduction")
public class AccountsIntroductionController {

    @Autowired
    private AccountsIntroductionService accountsIntroductionService;

    /**
     * 保存自我介绍
     * @param params
     * @return
     */
    @RequestMapping(params = "method=setIntroduction")
    public JsonResult setIntroduction(RequestParams params){
        // 针对20大的资料修改限制
        LimitModificationUtil.isLimitModify();

        String accountUuid = params.getFormString(0);
        String content = params.getFormString(1);
        String bucket = params.getFormStringDefault(2, AccountsPhotoService.DEFAULT_AVATAR_BUCKET);
        String photoStr = params.getFormString(3);
        String smid = StringUtils.isBlank(params.getFormStringOption(4)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(4);

        log.info("setIntroduction smid: {}", smid);


        JSONArray imgList = JSON.parseObject(photoStr).getJSONArray("img_list");
        for (int i = 0; i < imgList.size(); i++) {
            JSONObject img = imgList.getJSONObject(i);
            // 图片有可能带参数，要去除
            String img_name = img.getString("img_name");
            String width = img.getString("width");
            String height = img.getString("height");
            img_name = AvatarHandleService.removeParams(img_name);
            
            img.put("img_name", img_name);
            if(!StringUtils.isNumeric(width) || !StringUtils.isNumeric(height)){
                img.put("width", 0);
                img.put("height", 0);
            }
        }
        List<IntroductionPhotoDTO> introductionPhotoDTOs = imgList.toJavaList(IntroductionPhotoDTO.class);

        accountsIntroductionService.setAccountsIntroductionInTime(accountUuid, content, bucket, introductionPhotoDTOs, smid);
        return JsonResult.success();
    }

    /**
     * 获取我的自我介绍
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getMyIntroduction")
    public JsonResult getMyIntroduction(RequestParams params){
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = accountsIntroductionService.getMyIntroduction(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * 获取自我介绍优秀案例
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getIntroductionFromGoodSamples")
    public JsonResult getIntroductionFromGoodSamples(RequestParams params){
        String accountUuid = params.getFormString(0);
        String currentAccountUuid = params.getFormStringDefault(1, "");
        Map<String, Object> result = accountsIntroductionService.getIntroductionFromGoodSamples(accountUuid, currentAccountUuid);
        return JsonResult.success(result);
    }

    /**
     * 重置用户自我介绍 -- 风控使用，不对其他业务
     * 
     * @param params
     * @return
     */
    @RequestMapping(params = "method=resetAccountsIntroduction")
    public JsonResult resetAccountsIntroduction(RequestParams params){
        String accountUuid = params.getFormString(0);
        accountsIntroductionService.resetAccountsIntroduction(accountUuid);
        return JsonResult.success();
    }
    
    /**
     * 看看其他人怎么写
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getIntroductionFromGoodSamplesUrl")
    public JsonResult getIntroductionFromGoodSamplesUrl(RequestParams params){
        Map<String, String> result = accountsIntroductionService.getIntroductionFromGoodSamplesUrl();
        return JsonResult.success(result);
    }

    /**
     * 获取自我介绍优秀案例V2
     * @param params
     * @return
     */
    @RequestMapping(params = "method=samplesV2")
    public JsonResult samplesV2(RequestParams params){
        Integer sexType = params.getFormIntegerDefault(1, 1);
        return JsonResult.success(accountsIntroductionService.samplesV2(sexType));
    }

    /**
     * 自我介绍（emoji支持）
     * @param params
     * @return
     */
    @RequestMapping(params = "method=setIntroductionV2")
    public JsonResult setIntroductionV2(RequestParams params){
        // 针对20大的资料修改限制
        LimitModificationUtil.isLimitModify();

        String accountUuid = params.getFormString(0);
        String content = params.getFormString(1);
        String bucket = params.getFormStringDefault(2, AccountsPhotoService.DEFAULT_AVATAR_BUCKET);
        String photoStr = params.getFormString(3);
        String smid = StringUtils.isBlank(params.getFormStringOption(4)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(4);

        log.info("setIntroductionV2 smid: {}", smid);


        JSONArray imgList = JSON.parseObject(photoStr).getJSONArray("img_list");
        for (int i = 0; i < imgList.size(); i++) {
            JSONObject img = imgList.getJSONObject(i);
            // 图片有可能带参数，要去除
            String img_name = img.getString("img_name");
            String width = img.getString("width");
            String height = img.getString("height");
            img_name = AvatarHandleService.removeParams(img_name);

            img.put("img_name", img_name);
            if(!StringUtils.isNumeric(width) || !StringUtils.isNumeric(height)){
                img.put("width", 0);
                img.put("height", 0);
            }
        }
        List<IntroductionPhotoDTO> introductionPhotoDTOs = imgList.toJavaList(IntroductionPhotoDTO.class);

        accountsIntroductionService.setAccountsIntroductionInTimeV2(accountUuid, content, bucket, introductionPhotoDTOs, smid);
        return JsonResult.success();
    }
}
