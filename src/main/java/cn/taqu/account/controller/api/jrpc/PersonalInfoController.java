package cn.taqu.account.controller.api.jrpc;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.service.AccountsPersonalInfoService;
import cn.taqu.account.service.PersonalProfileInfoService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName PersonalInfoController.java
 * <AUTHOR>
 * @date 2015年10月10日 下午5:14:21
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=personalinfo")
public class PersonalInfoController {

	@Autowired
	private AccountsPersonalInfoService accountsPersonalInfoService;
	@Autowired
	AccountsInfoService accountsInfoService;
	@Autowired
	private PersonalProfileInfoService personalProfileInfoService;

	/**
	 * 设置芝麻认证
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=setZhimaCertification")
	public JsonResult setZhimaCertification(RequestParams params) {
		String accountUuid = params.getFormString(0);
		Integer zhimaCertification = params.getFormInteger(1);
		log.info("设置用户实名认证, accountUuid={}, zhimaCertification={}", accountUuid, zhimaCertification);
		
		accountsPersonalInfoService.setZhimaCertification(accountUuid, zhimaCertification);
		return JsonResult.success(true);
	}

	@RequestMapping(params = "method=deletePassPersonalProfile")
	public JsonResult deletePassPersonalProfile(RequestParams params){
		String accountUuid = params.getFormString(0);
		String personalProfile = params.getFormString(1);
		personalProfileInfoService.deletePass(accountUuid,personalProfile);
		return JsonResult.success(true);
	}

	/**
	 * 根据uuid批量获取保密设置
	 * @param params
	 * @return
	 */
	@Deprecated
	@RequestMapping(params = "method=getSecretConfigByUuids")
	public JsonResult getSecretConfigByUuids(RequestParams params) {
		String[] uuids = params.getFormStringArray(0);
		Map<String, Map<String, String>> result = accountsPersonalInfoService.getSecretConfigByUuids(uuids, null);
		return JsonResult.success(result);
	}

	/**
	 * 设置业务级实名认证
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=setChatRealCertification")
	public JsonResult setChatRealCertification(RequestParams params) {
		String accountUuid = params.getFormString(0);
		Integer chatRealCertification = params.getFormInteger(1);
		accountsPersonalInfoService.setChatRealCertification(accountUuid, chatRealCertification);
		return JsonResult.success(true);
	}
}
