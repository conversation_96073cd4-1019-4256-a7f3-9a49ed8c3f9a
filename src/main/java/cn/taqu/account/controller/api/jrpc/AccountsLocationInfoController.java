package cn.taqu.account.controller.api.jrpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AccountsLocationInfoService;
import cn.taqu.account.vo.GetAccountLocationInfoVO;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 用户位置信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-10-12 16:33
 */
@RestController
@RequestMapping(value = "/api", params = "service=accountsLocationInfo")
public class AccountsLocationInfoController {

    @Autowired
    private AccountsLocationInfoService accountsLocationInfoService;

    /**
     * 获取用户位置信息根据uuid
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getLocationInfoByUuid")
    public JsonResult getLocationInfoByUuid(RequestParams params){
        String accountUuid = params.getFormString(0);
        String ip = params.getFormStringDefault(1, "");
        GetAccountLocationInfoVO vo = accountsLocationInfoService.getAccountLocationInfo(accountUuid, ip);
        return JsonResult.success(vo);
    }

}
