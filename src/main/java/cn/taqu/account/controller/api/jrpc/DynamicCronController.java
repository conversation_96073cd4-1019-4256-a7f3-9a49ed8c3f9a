package cn.taqu.account.controller.api.jrpc;

import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 动态的定时任务
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@RequestMapping(value = "/api", params = "service=dynamicCron")
public class DynamicCronController {

    @Autowired
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;

    @RequestMapping(params = "method=startCron")
    @SuppressWarnings("unchecked")
    public JsonResult startCron(RequestParams params) {
        String className = params.getFormString(0);
        String cronExpression = params.getFormString(1);
        Class<Runnable> clz;
        Runnable runnable;
        try {
            clz = (Class<Runnable>) Class.forName(className);
            runnable = clz.newInstance();
        } catch (ClassNotFoundException e) {
            return JsonResult.failed("不存在类：" + className);
        } catch (IllegalAccessException | InstantiationException e) {
            return JsonResult.failed("类实例化错误，类：" + className);
        }
        threadPoolTaskScheduler.schedule(runnable, new CronTrigger(cronExpression));
        return JsonResult.success();
    }

    /**
    @RequestMapping(params = "method=startCron2")
    @SuppressWarnings("unchecked")
    public JsonResult startCron2(RequestParams params) {
        String className = params.getFormString(0);
        String cronExpression = params.getFormString(1);
        Class<Runnable> clz;
        Runnable runnable;
        try {
            clz = (Class<Runnable>) Class.forName(className);
            runnable = clz.newInstance();
        } catch (ClassNotFoundException e) {
            return JsonResult.failed("不存在类：" + className);
        } catch (IllegalAccessException | InstantiationException e) {
            return JsonResult.failed("类实例化错误，类：" + className);
        }
        threadPoolTaskScheduler.schedule(runnable,
                triggerContext -> new CronTrigger(cronExpression).nextExecutionTime(triggerContext));
        return JsonResult.success();
    }
    */

}