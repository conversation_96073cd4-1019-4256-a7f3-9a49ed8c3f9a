package cn.taqu.account.controller.api.jrpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.model.AccountsLevel;
import cn.taqu.account.service.AccountsLevelService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 用户账号经验等级相关的控制器
 * @ClassName LevelController.java
 * <AUTHOR>
 * @date 2015年9月25日 上午9:49:10
 */
@Deprecated
@RestController
@RequestMapping(value="/api", params="service=level")
public class LevelController {
	@Autowired
	private AccountsLevelService accountsLevelService;

	/**
	 * 通过id从数据库中获取等级信息
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getByIdFromDb")
	public JsonResult getByIdFromDb(RequestParams params) {
		Long id = params.getFormLong(0);
		AccountsLevel accountsLevel = accountsLevelService.getByIdFromDb(id);
		return JsonResult.success(accountsLevel);
	}
}
