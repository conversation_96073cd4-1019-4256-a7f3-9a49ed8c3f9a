package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.AccountsDressService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户个性装扮相关的控制器
 *
 * <AUTHOR>
 *         2017年8月1日 上午11:52:33
 */
@RestController
@RequestMapping(value = "/api", params = "service=dress")
public class AccountsDressController {

    @Autowired
    private AccountsDressService accountsDressService;

    @RequestMapping(params = "method=setDressStatus")
    public JsonResult setDressStatus(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long dressId = params.getFormLong(1);
        Integer status = params.getFormInteger(2);
        Integer dressType = params.getFormIntegerOption(3);
        accountsDressService.setDressStatus(accountUuid, dressId, status, dressType);
        return JsonResult.success(true);
    }
}
