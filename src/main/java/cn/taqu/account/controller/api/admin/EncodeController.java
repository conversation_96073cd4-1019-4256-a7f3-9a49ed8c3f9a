package cn.taqu.account.controller.api.admin;

import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 解密接口（线上紧急时刻才使用）
 *
 * <AUTHOR>
 * @date 2025/6/11 11:05
 */
@RestController
@RequestMapping(value = "/api", params = "service=ed")
public class EncodeController {

    /**
     * test
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=process")
    public JsonResult refreshAllureCache(RequestParams params) {
        String param = params.getFormString(0);
        String model = params.getFormString(0);

        String result = null;
        if (model.equals("sm3")) {
            result = EncryptUtil.sm3(param);
        } else if (model.equals("decrypt")) {
            result = EncryptUtil.decrypt(param);
        }
        return JsonResult.success(result);
    }
}
