package cn.taqu.account.controller.api.admin;

import cn.taqu.account.model.AccountLabelCfg;
import cn.taqu.account.service.AccountLabelServiceV2;
import cn.taqu.account.vo.req.LabelAdminReq;
import cn.taqu.account.vo.req.LabelCategoryAdminReq;
import cn.taqu.account.vo.resp.LabelCategoryResp;
import cn.taqu.account.vo.resp.LabelPageResp;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户标签admin
 *
 * <AUTHOR>
 * @date 2024/11/25 10:20
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=label")
public class AccountLabelController {

    @Resource
    private AccountLabelServiceV2 accountLabelServiceV2;

    /**
     * 分页标签数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listPage")
    public JsonResult listPage(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelAdminReq req = JsonUtils.stringToObject(json, LabelAdminReq.class);
        PageData<LabelPageResp> page = accountLabelServiceV2.listPage(req);
        return JsonResult.success(page);
    }

    /**
     * 分类标签数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listCategory")
    public JsonResult listCategory(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        log.info("addCategory, json:{}", json);
        LabelCategoryAdminReq req = JsonUtils.stringToObject(json, LabelCategoryAdminReq.class);
        List<LabelCategoryResp> page = accountLabelServiceV2.listCategory(req);
        return JsonResult.success(ImmutableMap.of("list", page));
    }

    /**
     * 新增分类
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=addCategory")
    public JsonResult addCategory(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        log.info("addCategory, json:{}", json);
        LabelCategoryAdminReq req = JsonUtils.stringToObject(json, LabelCategoryAdminReq.class);

        accountLabelServiceV2.addCategory(req);
        return JsonResult.success();
    }

    /**
     * 更新分类
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateCategory")
    public JsonResult updateCategory(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelCategoryAdminReq req = JsonUtils.stringToObject(json, LabelCategoryAdminReq.class);
        accountLabelServiceV2.updateCategory(req);
        return JsonResult.success();
    }

    /**
     * 获取分类详情
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCategoryInfo")
    public JsonResult getCategoryInfo(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelCategoryAdminReq req = JsonUtils.stringToObject(json, LabelCategoryAdminReq.class);
        return JsonResult.success(accountLabelServiceV2.getCategoryInfo(req.getId()));
    }

    /**
     * 新增标签
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelAdminReq req = JsonUtils.stringToObject(json, LabelAdminReq.class);
        AccountLabelCfg label = new AccountLabelCfg();
        label.setParentId(req.getCategoryId());
        label.setContent(req.getLabelName());
        label.setIconUrl(req.getLabelIcon());
        label.setSort(req.getLabelSort());
        label.setDataStatus(req.getStatus());
        label.setGender(req.getGender());
        label.setIntroduce(req.getIntroduce());
        accountLabelServiceV2.add(label);
        return JsonResult.success();
    }

    /**
     * 新增标签
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=update")
    public JsonResult update(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelAdminReq req = JsonUtils.stringToObject(json, LabelAdminReq.class);
        AccountLabelCfg label = new AccountLabelCfg();
        label.setId(req.getLabelId());
        label.setParentId(req.getCategoryId());
        label.setContent(req.getLabelName());
        label.setIconUrl(req.getLabelIcon());
        label.setSort(req.getLabelSort());
        label.setDataStatus(req.getStatus());
        label.setGender(req.getGender());
        label.setIntroduce(req.getIntroduce());
        accountLabelServiceV2.update(label);
        return JsonResult.success();
    }

    /**
     * 获取标签详情
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfo")
    public JsonResult getInfo(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelAdminReq req = JsonUtils.stringToObject(json, LabelAdminReq.class);
        return JsonResult.success(accountLabelServiceV2.getInfo(req.getId()));
    }

    /**
     * 更新标签状态
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=enable")
    public JsonResult enable(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelAdminReq req = JsonUtils.stringToObject(json, LabelAdminReq.class);
        accountLabelServiceV2.updateStatus(req.getId(), req.getStatus());
        return JsonResult.success();
    }

    /**
     * 更新标签状态
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=enableCategory")
    public JsonResult enableCategory(RequestParams params) {
        String json = params.getFormStringDefault(0, "");
        LabelCategoryAdminReq req = JsonUtils.stringToObject(json, LabelCategoryAdminReq.class);
        accountLabelServiceV2.updateCategoryStatus(req.getCategoryId(), req.getCategoryStatus());
        return JsonResult.success();
    }
}
