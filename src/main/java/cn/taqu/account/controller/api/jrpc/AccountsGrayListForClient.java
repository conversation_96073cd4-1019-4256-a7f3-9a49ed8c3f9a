package cn.taqu.account.controller.api.jrpc;


import cn.taqu.account.service.AccountsGrayListService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(value = "/api", params = "service=accountsList")
public class AccountsGrayListForClient {

    @Autowired
    AccountsGrayListService accountsGrayListService;

    /**
     * 用户是否在灰名单中、给客户端使用，名字需要特殊
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=inList")
    public JsonResult isInAccountGrayList(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);   //自己的ticket转换的uuid
        String toAccountUuid = params.getFormStringOption(1);  //对方的uuid
        String token = params.getFormStringOption(2);
        boolean isIn = accountsGrayListService.isInGrayList(token, toAccountUuid);
        Integer result = isIn ? 1:0;
        Map<String,Object> map = Maps.newHashMap();
        map.put("is_in",result);
        return JsonResult.success(map);
    }
}
