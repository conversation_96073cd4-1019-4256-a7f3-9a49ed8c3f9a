package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.service.AccountsService;
import cn.taqu.account.service.DestroyService;
import cn.taqu.account.vo.req.SetDestroyReasonReq;
import cn.taqu.account.vo.resp.DestroyCertainItem;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 注销接口
 *
 * <AUTHOR>
 * @date 2025/04/07 17:52
 */
@RestController
@RequestMapping(value = "/api", params = "service=destroy")
public class DestroyController {

    @Autowired
    private DestroyService destroyService;
    @Autowired
    private AccountsService accountsService;

    /**
     * 注销原因配置
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=reasonConfig")
    public JsonResult listInfo(RequestParams params) {
        String uuid = params.getFormString(0);
        return JsonResult.success(ImmutableMap.of("list", destroyService.reasonConfig(uuid)));
    }

    /**
     * 注销原因设置
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=submitReason")
    public JsonResult submitReason(RequestParams params) {
        String uuid = params.getFormString(0);
        Integer parentId = params.getFormIntegerDefault(1, 0);
        Integer childrenId = params.getFormIntegerDefault(2, 0);
        String content = params.getFormStringDefault(3, "");
        SetDestroyReasonReq req = new SetDestroyReasonReq(uuid, parentId, childrenId, content);
        destroyService.submitReason(req);
        return JsonResult.success();
    }

    /**
     * 冷静期提示
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=freezeToast")
    public JsonResult freezeToast(RequestParams params) {
        String uuid = params.getFormString(0);
        return JsonResult.success(destroyService.freezeToast(uuid));
    }

    /**
     * 提交注销
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=submitForMobileType")
    public JsonResult submitForMobileType(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        String verify = params.getFormStringDefault(1, "");
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        if (StringUtils.isBlank(verify)) {
            throw new ServiceException(CodeStatus.DESTROYED_VERIFY_BLANK);
        }

        accountsService.destroyV3(accountUuid, verify);
        return JsonResult.success();
    }

    /**
     * 确认页面信息
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=certain")
    public JsonResult certain(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        List<DestroyCertainItem> resp = destroyService.certain(accountUuid);
        ImmutableMap<String, Object> result = ImmutableMap.of("page_title", "请确认您的账号是否满足以下注销条件", "list", resp);
        return JsonResult.success(result);
    }
}
