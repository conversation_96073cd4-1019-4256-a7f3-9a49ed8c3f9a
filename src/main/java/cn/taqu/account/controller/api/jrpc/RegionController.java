package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.RegionService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;

/**
 * 
 * 地区查询
 * <AUTHOR>
 * 2018年1月3日 下午4:01:15
 */
@RestController
@RequestMapping(value="/api", params="service=region")
public class RegionController {

	@Autowired
	private RegionService regionService;

	/**
	 * 查询用户昵称修改记录 <br/>
	 * <b>service:</b> region <br/>
	 * <b>method:</b> getUserPosition <br/>
	 * <b>form:</b> [account_uuid:String 用户uuid] <br/>
	 * @Title getUserPosition
	 * @param params
	 * @return
	 * <AUTHOR>
	 * 2018年1月3日 下午4:01:15
	 */
	@RequestMapping(params = "method=getUserPosition")
	public JsonResult getUserPosition(RequestParams params) {
		String accountUuid = params.getFormString(0);
		Map<String, String> map = regionService.getUserPosition(accountUuid);
		return JsonResult.success(map);
	}

	/**
	 * 批量获取用户所在地信息
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=batchGetUserPosition")
	public JsonResult batchGetUserPosition(RequestParams params) {
		String[] accountUuids = params.getFormStringArray(0);
		Map<String, Map<String, String>> map = regionService.batchGetUserPosition(accountUuids);
		return JsonResult.success(map);
	}

	/**
	 * 批量获取用户所在地省份城市信息
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=mgetProvinceCityInfoByUuids")
	public JsonResult mgetProvinceCityInfoByUuids(RequestParams params){
		String[] accountUuids = params.getFormStringArray(0);
		Map<String, Map<String, Object>> map = regionService.mgetProvinceCityInfoByUuids(accountUuids);
		return JsonResult.success(map);
	}

	/**
	 * 根据城市id获取城市的相关信息
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=mgetInfoByCityId")
	public JsonResult mgetInfoByCityId(RequestParams params) {
		Long[] cityIds = params.getFormLongArray(0);
		Map<Long, Map<String, Object>> cityInfo = regionService.mgetInfoByCityId(Arrays.asList(cityIds));
		return JsonResult.success(cityInfo);
	}

	/**
	 * 根据城市名获取城市相关信息
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getInfoByCityName")
	public JsonResult getInfoByCityName(RequestParams params) {
		String cityName = params.getFormString(0);
		Map<String,Object> cityInfo = regionService.getInfoByCityName(cityName);
		return JsonResult.success(cityInfo);
	}
}