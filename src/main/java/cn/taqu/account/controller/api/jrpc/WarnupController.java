package cn.taqu.account.controller.api.jrpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AccountsService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 数据预热脚本修复跑的接口，不可提供业务端使用
 * 
 * <AUTHOR>
 * @date 2021/03/10
 */
@RestController
@RequestMapping(value = "api", params = "service=warnup")
public class WarnupController {

    @Autowired
    private AccountsService accountsService;


    /**
     * 注销某个用户
     * 
     * @param params
     * @return
     */
    @RequestMapping(params = "method=warnupDestroy")
    public JsonResult warnupDestroy(RequestParams params) {
        String accountUuid = params.getFormString(0);
        accountsService.destroyByAccountUuid(accountUuid, "脚本操作", true, true);
        return JsonResult.success();
    }
}
