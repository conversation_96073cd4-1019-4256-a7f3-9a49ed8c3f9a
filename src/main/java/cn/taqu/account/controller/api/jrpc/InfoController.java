package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.common.AliyunLiveFaceDetectStatus;
import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.common.MakeFriendObjectiveEnum;
import cn.taqu.account.common.PrometheusMetricsEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.AccountAndTokenInfoDto;
import cn.taqu.account.dto.PageParam;
import cn.taqu.account.etcd.AlEvaluationVersionBarrier;
import cn.taqu.account.etcd.AvatarModifyCtrl;
import cn.taqu.account.monitor.CounterMonitor;
import cn.taqu.account.service.*;
import cn.taqu.account.utils.AccountServerUtil;
import cn.taqu.account.utils.LimitModificationUtil;
import cn.taqu.account.utils.LogUtil;
import cn.taqu.account.utils.RequestParamsUtil;
import cn.taqu.account.vo.GetAccountInfoByUserV3Vo;
import cn.taqu.account.vo.GetByTicketVo;
import cn.taqu.account.vo.GetHomepageInfoV3Vo;
import cn.taqu.account.vo.Initialize.InitializeAccountVo;
import cn.taqu.account.vo.resp.HomepageBackgroundResp;
import cn.taqu.account.vo.resp.PageLabelResp;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.SpringContextHolder;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Objects;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.prometheus.client.Counter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * 2017年7月5日 上午9:38:17
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=info")
public class InfoController {
    private static final Logger LOGGER = LoggerFactory.getLogger(InfoController.class);

    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsTqcoinLogsService accountsTqcoinLogsService;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private MeService meService;
    @Autowired
    private MembersService membersService;
    @Autowired
    private AvatarHandleService avatarHandleService;
    @Autowired
    private AccountsLabelService accountsLabelService;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    private VoiceSignInfoService voiceSignInfoService;
    @Autowired
    private AccountsRegAttributionService accountsRegAttributionService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private LimitModificationUtil limitModificationUtil;

    @Autowired
    private AsyncTrackingService asyncTrackingService;

    @Resource
    private AccountLabelService accountLabelService;
    @Resource
    private AccountLabelServiceV2 accountLabelServiceV2;
    @Resource
    private HomepageBackgroundService homepageBackgroundService;
    @Resource
    private AllureService allureService;

    /**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 客户端接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

    /**
     * 获取用户自己的个人资料，只用于编辑
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getAccountInfoByUserV2")
    public JsonResult getAccountInfoByUserV2(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);

        Map<String, Object> resultMap = accountsInfoService.getAccountInfoByUserV2(accountUuid);
        JsonResult jsonResult = JsonResult.success(resultMap);
        Map<String, String> map = Maps.newHashMap();
        map.put("avatar_host", accountsService.getAvatarUrlPrefix());
        map.put("img_host", accountsService.getImgUrlPrefix());
        jsonResult.setExtra(map);
        return jsonResult;
    }

    /**
     * 获取用户自己的资料，用于预览/编辑，查主态
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getAccountInfoByUserV3")
    public JsonResult getAccountInfoByUserV3(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        GetAccountInfoByUserV3Vo vo = accountsInfoService.getAccountInfoByUserV3(accountUuid);
        return JsonResult.success(vo);
    }

    /**
     * 获取用户主页信息，查自己/他人
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getHomepageInfo")
    public JsonResult getHomepageInfo(RequestParams params) {
        String operUuid = params.getFormStringOption(0);
        String accountUuid = params.getFormString(1);
        Integer version = params.getFormIntegerDefault(2, 0);
        Integer isReturnAvatar = params.getFormIntegerDefault(3, 0);
        Map<String, Object> resultMap = accountsInfoService.getHomepageInfo(operUuid, accountUuid, version, isReturnAvatar);
        JsonResult jsonResult = JsonResult.success(resultMap);
        Map<String, String> map = Maps.newHashMap();
        map.put("avatar_host", accountsService.getAvatarUrlPrefix());
        map.put("img_host", accountsService.getImgUrlPrefix());
        jsonResult.setExtra(map);
        return jsonResult;
    }

    /**
     * 获取用户主页信息，查他人，查客态
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getHomepageInfoV3")
    public JsonResult getHomepageInfoV3(RequestParams params) {
        String operUuid = params.getFormString(0);
        String accountUuid = params.getFormString(1);
        // 是否返回头像。抛异常还要返回头像，头大
        Integer isReturnAvatar = params.getFormIntegerDefault(2, 0);
        GetHomepageInfoV3Vo vo = accountsInfoService.getHomepageInfoV3(operUuid, accountUuid, isReturnAvatar);
        JsonResult jsonResult = JsonResult.success(vo);
        return jsonResult;
    }


    /**
     * 获取资料完成度
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfoPercent")
    public JsonResult getInfoPercent(RequestParams params) {
        String accountUuid = params.getFormString(0);
        return JsonResult.success(accountsInfoService.getInfoPercent(accountUuid));
    }

    /**
     * 我-菜单，显示用户信息
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2018年1月9日 下午4:33:31
     */
    @Api
    @RequestMapping(params = "method=getAccountInfoOfMyMenu")
    public JsonResult getAccountInfoOfMyMenu(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        int versionNumber = params.getFormIntegerDefault(1, 0);

        checkClonedIsolation(accountUuid);

        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();

        // 异步记录用户位置信息
        String ip = soaBaseParams.getIp();
        String latitude = soaBaseParams.getLatitude();
        String longitude = soaBaseParams.getLongitude();
        Integer city = soaBaseParams.getCity();
        asyncTrackingService.trackingAccountLocationInfo(accountUuid, ip, longitude, latitude, city == null ? null : Long.valueOf(city));

        Map<String, Object> resultMap = accountsInfoService.getAccountInfoOfMyMenu(accountUuid, versionNumber);
        JsonResult jsonResult = JsonResult.success(resultMap);
        Map<String, String> map = Maps.newHashMap();
        map.put("avatar_host", accountsService.getAvatarUrlPrefix());
        map.put("img_host", accountsService.getImgUrlPrefix());
        jsonResult.setExtra(map);
//        Log.info("[getAccountInfoOfMyMenu]获取到的返回结果: {}", JsonUtils.objectToString2(jsonResult));
        return jsonResult;
    }

    private void checkClonedIsolation(String accountUuid) {
        // 用户当前登录的cloned
        Integer cloned = SoaBaseParams.fromThread().getCloned();

        // 用户注册时的cloned
        Map<String, Object> userInfo = accountsInfoService.getUserInfo(accountUuid, "reg_cloned");
        Integer regCloned = MapUtils.getInteger(userInfo, "reg_cloned");

        // 用户所属的cloned group
        ClonedGroupEnum clonedGroup = ClonedGroupEnum.getClonedGroup(regCloned);


        // 用户当前登录的cloned是否在用户注册时的cloned group中，如果不在，则强踢
        if (!clonedGroup.contains(cloned)) {
            LOGGER.info("checkClonedIsolation uuid: {}, loginCloned: {}, regCloned: {}, clonedGroup: {}", accountUuid, cloned, regCloned, clonedGroup);
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
    }

    /**
     * 2024.08.14 网关api已下线，代码可下
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 上午10:19:54
     * @deprecated 审核原因，不能出现coin字段，接口下线，使用{@link #getScore(RequestParams)}代替
     * 获取账户趣币数
     * http://************:1234/Account_System/v5/Info/getCoinNum
     */
    @Api
    @RequestMapping(params = "method=getCoinNum")
    public JsonResult getCoinNum(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);

        Long coinNum = accountsTqcoinLogsService.getQuCoinByAccountUuid(accountUuid);
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("coin_num", coinNum.toString());
        return JsonResult.success(resultMap);
    }

    /**
     * 2024.08.14 感觉没用了
     *
     * @param params
     * @return
     */
    @Api
    @Deprecated
    @RequestMapping(params = "method=getScore")
    public JsonResult getScore(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);

        Long coinNum = accountsTqcoinLogsService.getQuCoinByAccountUuid(accountUuid);
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("score", coinNum.toString());
        return JsonResult.success(resultMap);
    }

    /**
     * 检验账户昵称能否修改
     * http://************:1234/Account_System/v5/Info/getNicknameRate
     *
     * 2024.08.14 感觉没用了
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 上午10:26:17
     */
    @Deprecated
    @Api
    @RequestMapping(params = "method=getNicknameRate")
    public JsonResult getNicknameRate(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);

        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        Map<String, String> map = accountsInfoService.getSetNicknamePriv(accountUuid, appcode);
        return JsonResult.success(map);
    }

    /**
     * 检验账户个人简介能否修改
     * http://************:1234/Account_System/v5/Info/getProfileRate
     *
     * 2024.08.14 感觉没用了
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 上午10:35:30
     */
    @Deprecated
    @Api
    @RequestMapping(params = "method=getProfileRate")
    public JsonResult getProfileRate(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);

        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        Map<String, String> ticketInfo = accountsPersonalInfoService.getPersonalProfilePriv(accountUuid, appcode);

        if (ticketInfo == null || ticketInfo.isEmpty()) {
            return JsonResult.failedCode(CodeStatus.ACCOUNT_INFO_NO_FOUND);
        }
        String status = ticketInfo.get("status");
        String time = ticketInfo.get("time");
        //老接口，目前客户端还在调用，1表示直接pass
        status = "1";
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("profile_enable", status);
        if (Objects.equal("1", status)) {
            //可以修改
            resultMap.put("title", "");
        } else {
            //不能修改
            resultMap.put("title", "暂时不能修改个人简介\n上次修改:" + DateUtil.dateToString20(DateUtil.fromSecond(Long.parseLong(time))) + "\n个人简介1周内只能修改一次");
        }
        return JsonResult.success(resultMap);
    }

    /**
     * 账户昵称是否已经存在(只允许无uuid的情况下才能调用)
     * http://************:1234/Account_System/v5/Info/isExistNickname
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:34:53
     */
    @Api
    @RequestMapping(params = "method=isExistNickname")
    public JsonResult isExistNickname(RequestParams params) {
        //请求参数
        String nickname = params.getFormString(0);
        boolean exists = accountsInfoService.isExistNickName(nickname);
        Map<String, String> existsMap = new HashMap<>();
        if (exists) {
            existsMap.put("exist_nickname", "1");
        } else {
            existsMap.put("exist_nickname", "0");
        }
        return JsonResult.success(existsMap);
    }

    /**
     * ‘我’页面
     * http://************:1234/Account_System/v5/Info/me
     *
     * 2024.08.14 感觉没用了
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 上午11:41:12
     */
    @Api
    @RequestMapping(params = "method=me")
    public JsonResult me(RequestParams params) {
        //请求参数
        String newUi = params.getFormStringOption(0);
        String version = params.getFormStringOption(1);
        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();

        List<Map<String, String>> list = meService.me(newUi, version, platformId);
        return JsonResult.success(list);
    }

    /**
     * me接口v2版本
     *
     * 2024.08.14 感觉没用了
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=meV2")
    public JsonResult meV2(RequestParams params) {
        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
        Map<String, Object> result = meService.meV2(platformId);
        return JsonResult.success(result);
    }

    /**
     * 注册设备</br>
     * 此接口目前只有ios在使用
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=regDevice")
    public JsonResult regDevice(RequestParams params) {
        //此方法php有调用 members/regMembers，但是regMembers无任何操作
        // 该系统版本是特殊处理，如果该接口要加参数，要和网关对一下
        String iosSysVersion = params.getFormStringOption(0);
        String token = SoaBaseParams.fromThread().getToken();
        membersService.regIosSysVersion(token, iosSysVersion);

        Map<String, Map<String, String>> resultMap = new HashMap<>();
        resultMap.put("first_reg_coupon", Maps.newHashMap());
        return JsonResult.success(resultMap);
    }

    /**
     * 设置账户基础信息
     * http://************:1234/Account_System/v5/Info/setAccountInfoByUser
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 上午11:57:14
     */
    @Api
    @RequestMapping(params = "method=setAccountInfoByUser")
    public JsonResult setAccountInfoByUser(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        String isSecret = params.getFormStringOption(1);
        String sex = params.getFormStringOption(2);
        String age = params.getFormStringOption(3);
        String baseaddr = params.getFormStringOption(4);
        String birth = params.getFormStringOption(5);
        String sexual = params.getFormStringOption(6);
        String affectivestatus = params.getFormStringOption(7);
        String personalProfile = params.getFormStringOption(8);
        String datingIntention = params.getFormStringOption(9);
        String hometown = params.getFormStringOption(10);
        String education = params.getFormStringOption(11);
        String trade = params.getFormStringOption(12);
        String profession = params.getFormStringOption(13);
        Integer height = params.getFormIntegerOption(14);
        Integer weight = params.getFormIntegerOption(15);
        String income = params.getFormStringOption(16);
        String smid = StringUtils.isBlank(params.getFormStringOption(17)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(17);

        // 针对20大的资料修改限制
        if (StringUtils.isNotBlank(personalProfile)) {
            LimitModificationUtil.isLimitModify();
        }

        LOGGER.info("setAccountInfoByUser smid: {}", smid);

        Map<String, Object> accountInfo = Maps.newHashMap();
        accountInfo.put("sex", sex);
        accountInfo.put("age", age);
        accountInfo.put("baseaddr", baseaddr);
        accountInfo.put("birth", birth);
        accountInfo.put("sexual", sexual);
        accountInfo.put("affectivestatus", affectivestatus);
        accountInfo.put("is_secret", isSecret);
        accountInfo.put("personal_profile", personalProfile);
        accountInfo.put("dating_intention", datingIntention);
        accountInfo.put("hometown", hometown);
        accountInfo.put("education",education);
        accountInfo.put("trade",trade);
        accountInfo.put("profession",profession);
        accountInfo.put("height",height);
        accountInfo.put("weight",weight);
        accountInfo.put("income",income);
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();

        Map<String, Object> result = accountsPersonalInfoService.setAccountInfoByUuid(accountUuid, accountInfo, appcode, smid);
        return JsonResult.success(result);
    }

    /**
     * 语音认证
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=voiceVerify")
    public JsonResult voiceVerify(RequestParams params) {
        String ticketId = params.getFormString(0);
        String verify = params.getFormString(1);
        accountsPersonalInfoService.voiceVerify(ticketId, verify);
        return JsonResult.success();
    }

    /**
     * 设置/更新账户昵称
     * http://************:1234/Account_System/v5/Info/setNickname
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:37:09
     */
    @Api
    @RequestMapping(params = "method=setNickname")
    public JsonResult setNickname(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        String nickname = params.getFormString(1);
        Integer useRenameCard = params.getFormIntegerOption(2);//php用户系统无此字段,此字段是为了兼容直播
        String smid = StringUtils.isBlank(params.getFormStringOption(3)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(3);

        LOGGER.info("setNickname smid: {}", smid);

        if (Objects.equal(useRenameCard, 1)) {
            LOGGER.info("app修改昵称，使用改名卡, nickname:{}", nickname);
        }
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();

        accountsService.setNickname(nickname, accountUuid, useRenameCard, appcode, smid);

        // 设置昵称成功
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("nickname", nickname);
        return JsonResult.success(resultMap);
    }

    /**
     * 获取用户标签
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年12月12日 上午9:52:41
     */
    @Api
    @RequestMapping(params = "method=getAccountLabel")
    public JsonResult getAccountLabel(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        Integer type = params.getFormInteger(1);// 类型 1-喜欢类型，2-个性标签

        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        Map<String, List<Map<String, String>>> map = accountsLabelService.getAccountLabelForNew(accountUuid, type);
        return JsonResult.success(map);
    }

    /**
     * 获取指定类型的用户已选择的标签
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getTypeLabelByUuid")
    public JsonResult getTypeLabelByUuid(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer type = params.getFormIntegerDefault(1, 0);
        Map<String, List<Map<String, String>>> result = accountsLabelService.mGetTypeLabelByUuid(Arrays.asList(accountUuid), type).get(accountUuid);
        return JsonResult.success(result);
    }

    /**
     * 获取指定类型的用户已选择的标签（批量）
     * <AUTHOR>
     * @date 2020/03/03 10:01
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listTypeLabelByUuid")
    public JsonResult listTypeLabelByUuid(RequestParams params) {
        String[] accountUuids = params.getFormStringArray(0);
        Integer type = params.getFormIntegerDefault(1, 0);
        Map<String, Map<String, List<Map<String, String>>>> result = accountsLabelService.mGetTypeLabelByUuid(Arrays.asList(accountUuids), type);
        return JsonResult.success(result);
    }

    /**
     * 设置用户标签
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年12月12日 上午11:56:57
     */
    @Api
    @RequestMapping(params = "method=setAccountLabel")
    public JsonResult setAccountLabel(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        Integer type = params.getFormInteger(1);// 类型 1-喜欢类型，2-个性标签
        String ids = params.getFormStringDefault(2, "");

        accountsLabelService.setAccountLabel(accountUuid, type, ids);

        return JsonResult.success();
    }

    /**
     * 设置用户头像
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年12月12日 下午4:07:05
     */
    @Api
    @RequestMapping(params = "method=setAccountAvatar")
    @Deprecated
    public JsonResult setAccountAvatar(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 设置用户头像, 区分"setAccountAvatar"
     * 增加数美图片检测，当检测通过时，才操作保存头像
     */
    @Deprecated
    @RequestMapping(params = "method=setAccountAvatarV2")
    public JsonResult setAccountAvatarV2(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 根据用户ticket_id设置用户相册
     * http://************:1234/Account_System/v5/Info/setAccountsPhoto
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:23:53
     */
    @Api
    @Deprecated
    @RequestMapping(params = "method=setAccountCover")
    public JsonResult setAccountCover(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 根据ticket_id设置相册新接口，添加了
     * @param params
     * @return
     */
    @Api
    @Deprecated
    @RequestMapping(params = "method=setAccountCoverNew")
    public JsonResult setAccountCoverNew(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 根据ticket_id设置相册新接口，添加了 V2
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=setAccountCoverNewV2")
    public JsonResult setAccountCoverNewV2(RequestParams params) {
        // 针对20大的资料修改限制
        LimitModificationUtil.isLimitModify();

        MonitorService.incCounterMetrices(PrometheusMetricsEnum.HTTP_REQUEST_SET_COVER_V2_COUNTER);
        //请求参数
        String accountUuid = params.getFormString(0);
        String photos = params.getFormString(1);
        String bucket = params.getFormStringOption(2);
        Integer version = params.getFormIntegerDefault(3, 0);
        String smid = params.getFormStringDefault(4, CommConst.OLD_CLIENT_PACKAGE);

        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();

        List<Map<String,Object>> imgList = Lists.newArrayList();
        //兼容ios传参问题
        if(!photos.contains("\"img_list\":[(null)]")){
            JSONObject data = JSONObject.parseObject(photos);
            JSONArray jsonArray = data.getJSONArray("img_list");
            imgList = JsonUtils.stringToObject2(jsonArray.toJSONString(), new TypeReference<List<Map<String,Object>>>() {});
        }

        List<String> photoUrlList = Lists.newArrayList();
        Map<String,Integer> verifyStatusMap = Maps.newHashMap();
        Map<String,Float> similarityScoreMap = Maps.newHashMap();
        Map<String,Integer> faceScoreMap = Maps.newHashMap();
        LOGGER.info("设置用户相册,用户uuid={},相册信息={}",accountUuid,photos);
        for(Map<String,Object> photoMap : imgList){
            String url = MapUtils.getString(photoMap,"img_name");
            url = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(url);
            photoMap.put("img_name", url);

            // 这边判断一下图片是否在删除列表里
            String deletePhotoKey = RedisKeyConstant.ACCOUNT_DELETE_PHOTO_URL_MD5.setArg(Md5Util.encryptSHA1(url));
            if(accountStringRedisTemplate.hasKey(deletePhotoKey)){
                // 图片已删除
                LOGGER.info("图片已删除,跳过该图片上传,accountUuid={},url={}", accountUuid, url);
                continue;
            }

            photoUrlList.add(url);
            Float similarityScore = MapUtils.getFloat(photoMap,"similarity_score");
            int verifyStatus = 0;
            if(null != similarityScore && !AliyunLiveFaceDetectService.UN_FACE_VERIFY.equals(similarityScore) && similarityScore >= 0f){
                if(similarityScore >= AliyunLiveFaceDetectService.ALIYUN_SIMILARITY_SCORE){
                    verifyStatus = AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus();
                }else {
                    verifyStatus = AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus();
                }
                similarityScoreMap.put(url,similarityScore);
            }
            verifyStatusMap.put(url,verifyStatus);

            faceScoreMap.put(url,MapUtils.getInteger(photoMap,"face_score"));
        }
        String[] urlArray = new String[photoUrlList.size()];
        List<String> urlList = accountsPhotoService.setAccountCover(accountUuid, photoUrlList.toArray(urlArray), bucket, appcode, 0, version,verifyStatusMap,faceScoreMap,similarityScoreMap, smid);
        //2017.7.26 php 删除相片
        avatarHandleService.deletePhotoFile(urlList, accountUuid);

        // 根据uuid查询用户已审核通过的相册数，落地到account_info表中
        accountsInfoService.updateAccountPhotoNumber(Collections.singleton(accountUuid), "db");
//        accountsPhotoService.checkFinishIncentiveTaskWithoutCheck(accountUuid);

        return JsonResult.success(true);
    }

    /**
     * 新朋友列表，查询用户自己资料
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getNearbyHomePageInfo")
    public JsonResult getNearbyHomePageInfo(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, Object> resultMap = accountsInfoService.getNearbyHomePageInfo(accountUuid);
        JsonResult jsonResult = JsonResult.success(resultMap);
        return jsonResult;
    }

    /**
     * 下发 引导完善资料
     * 根据上传的个人资料信息名，返回不存在的
     *
     * @param params ticket、checks
     * @return {@link JsonResult}
     */
    @Api
    @RequestMapping(params = "method=getGuideCheckInfo")
    public JsonResult getGuideCheckInfo(RequestParams params) {
        String ticket = params.getFormString(0);
        String[] checks = params.getFormString(1).split(",");
        return JsonResult.success(accountsInfoService.getCheckInfo(ticket, checks, null));
    }

    /**
     * 下发 交友列表相关引导
     * 根据ETCD对应的开关和信息名返回结果
     *
     * @param params ticket
     * @return {@link JsonResult}
     */
    @Deprecated
    @Api
    @RequestMapping(params = "method=getFriendCheckInfo")
    public JsonResult getFriendCheckInfo(RequestParams params) {
        String ticket = params.getFormString(0);
        return accountsInfoService.getFriendCheckInfo(ticket);
    }

    /**
     * 2024.08.14 网关api已下线，代码可下
     * @param params
     * @return
     */
    @Deprecated
    @Api
    @RequestMapping(params = "method=getMakeFriendObjectiveList")
    public JsonResult getMakeFriendObjectiveList(RequestParams params) {
        MakeFriendObjectiveEnum[] enums = MakeFriendObjectiveEnum.values();
        List<Map<String, Object>> list = Lists.newArrayList();
        for (MakeFriendObjectiveEnum e : enums) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("id", e.getId());
            map.put("title", e.getTitle());
            map.put("icon", e.getPopIcon());
            map.put("num", MakeFriendObjectiveEnum.getSameObjectiveUserSize());
            list.add(map);
        }
        return JsonResult.success(list);
    }

    /**
     * 2024.04.18 业务回滚了，产品确认只有不会再使用
     * @param params
     * @return
     */
    @Deprecated
    @Api
    @RequestMapping(params = "method=setMakeFriendObjective")
    public JsonResult setMakeFriendObjective(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 登录/启动app调用接口
     * 做一些业务处理
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=initializeAccount")
    public JsonResult initializeAccount(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        Integer source = params.getFormInteger(1);
        InitializeAccountVo initializeAccountVo = accountsInfoService.initializeAccount(accountUuid, source);
        return JsonResult.success(initializeAccountVo);
    }

    /**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 客户端接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

    /**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 其他系统接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

    /**
     * PC站接口，根据account_key获取账号基本信息。
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getByAccountKey")
    public JsonResult getByAccountKey(RequestParams params) {
        String accountKey = params.getFormString(0);
        return JsonResult.success(accountsInfoService.getByAccountKey(accountKey));
    }

    /**
     * 根据ticket查询账号基本信息 <br/>
     * <b>service:</b> info <br/>
     * <b>method:</b> getByTicket <br/>
     * <b>form:</b> [ticket:String 登录凭证 ] <br/>
     * 查询成功返回data格式为{account_id:"account_id值",uuid:"uuid值",account_status:
     * "account_status值",account_type:"account_type值"}<br/>
     *
     * @param params
     * @return
     * @Title getByTicket
     * <AUTHOR>
     * @Date 2015年9月21日 上午10:37:09
     */
    @RequestMapping(params = "method=getByTicket")
    public JsonResult getByTicket(RequestParams params) {
        String ticket = params.getFormStringOption(0);
        if (StringUtils.isBlank(ticket)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        GetByTicketVo result = accountsInfoService.getByTicket(ticket);
        return JsonResult.success(result);
    }

    @RequestMapping(params = "method=getInfoByWebTicket")
    public JsonResult getInfoByWebTicket(RequestParams params) {
        String webTicket = params.getFormStringOption(0);
        String salt = params.getFormStringOption(1);
        String[] fields = params.getFormStringArrayOption(2);
        Map<String, Object> result = accountsInfoService.getInfoByWebTicket(webTicket, salt, fields);
        return JsonResult.success(result);
    }

    /**
     * 通过登录凭证获取用户账号信息 <b>service:</b> info <br/>
     * <b>method:</b> getInfoByTicket <br/>
     * <b>form:</b> [ticket:String 登录凭证] <br/>
     * 没有查询到返回未查询到错误，否则返回data为{account_id:account_id值,uuid:uuid值,account_name: account_name值,account_type:account_type值,sex_type:sex_type值,avatar:
     * avatar值,experience:experience值,account_actor:account_actor值,account_level :account_level值}
     *
     *
     * @param params
     * @return
     * @Title getInfoByTicket
     * <AUTHOR>
     * @Date 2015年9月28日 上午9:06:35
     */
    @Deprecated
    @RequestMapping(params = "method=getInfoByTicket")
    public JsonResult getInfoByTicket(RequestParams params) {
        // 需要淘汰的接口
        String ticket = params.getFormStringOption(0);
        String[] fields = params.getFormStringArrayOption(1);
        String version = params.getFormStringOption(2);
        if (StringUtils.isBlank(ticket)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        Map<String, Object> ticketInfo = accountsInfoService.getInfoByTicket(ticket, fields, version);
        if (ticketInfo == null || ticketInfo.isEmpty()) {
            return JsonResult.failedCode(CodeStatus.ACCOUNT_INFO_NO_FOUND);
        }
        return JsonResult.success(ticketInfo);
    }

    /**
     * 根据uuid获取用户账号信息
     *
     * @param params
     * @return
     * @author:Chenquanan
     * @Date:2017年3月16日 下午1:56:26
     */
    @RequestMapping(params = "method=getInfoByUuid")
    public JsonResult getInfoByUuid(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        String[] fields = params.getFormStringArray(1);
        String version = params.getFormStringOption(2);
        Map<String, Map<String, Object>> userInfo = accountsInfoService.getInfoByUuid(uuids, fields, version, true, true);
        return JsonResult.success(userInfo);
    }

    /**
     * 根据uuid获取用户账号信息
     * 开发仿造 getInfoByUuid 接口
     *
     * <AUTHOR>
     * @date 2020/06/22 15:48
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listInfoByNormalCard")
    public JsonResult listInfoByNormalCard(RequestParams params) {
        String[] cardIds = params.getFormStringArray(0);
        // 预留字段
        String[] fields = params.getFormStringArrayOption(1);
        Map<String, Map<String, Object>> userInfo = accountsInfoService.listInfoByNormalCard(cardIds);
        return JsonResult.success(userInfo);
    }

    /**
     * 获取相的账号uuid(只能获取普通号码的uuid)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfoByNormalCard")
    public JsonResult getInfoByNormalCard(RequestParams params) {
        Long cardId = params.getFormLong(0);
        Map<String, Object> map = accountsInfoService.getInfoByNormalCard(cardId);
        JsonResult jsonResult = JsonResult.success();
        jsonResult.setData(map);
        return jsonResult;
    }

    /**
     * 获取的账号uuid
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfoByCard")
    public JsonResult getInfoByCard(RequestParams params){
        String accountUuid = params.getFormString(0);
        Long cardId = params.getFormLong(1);
        Map<String, String> map = accountsInfoService.getInfoByCard(cardId);
        return JsonResult.success(map);
    }


    /**
     * 只提供给j35用
     *
     * 现在getInfoByUuidOrNickname 是要改成 1 当时昵称查询时 有多个值的就返回其中一个，并且返回有几个人用这个昵称   2 加上一个根据id 查询的，
     *
     * <AUTHOR>
     * @date 2020/06/11 00:12
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfoByUuidOrNicknameOrCardId")
    public JsonResult getInfoByUuidOrNicknameOrCardId(RequestParams params) {
        String uuidOrNicknameOrCardId = params.getFormString(0);
        LOGGER.info("getInfoByUuidOrNicknameOrCardId接口传参：str={}", uuidOrNicknameOrCardId);
        String[] fields = params.getFormStringArrayOption(1);
        Map<String, Object> result = accountsInfoService.getInfoByUuidOrNicknameOrCardId(uuidOrNicknameOrCardId, fields, "1", true);
        return JsonResult.success(result);
    }

    /**
     * 根据uuid数组获取mobile集合
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getMobileByUuid")
    public JsonResult getMobileByUuid(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        log.info("废弃接口：{},{}","info", "getMobileByUuid");
        return JsonResult.success(accountsInfoService.getMobileByUuid(uuids));
    }

    /**
     * 根据uuid获取用户账号信息，该接口的作用同{@link #getInfoByUuid(RequestParams)}，但是该接口不对返回字段(如手机号)进行加密</br>
     * 注：此接口只能后台调用
     *
     * @param params
     * @return
     * @author:Chenquanan
     * @Date:2017年3月16日 下午1:56:26
     */
    @RequestMapping(params = "method=getInfoByUuidsNoSecret")
    public JsonResult getInfoByUuidsNoSecret(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        String[] fields = params.getFormStringArrayOption(1);
        String version = params.getFormStringOption(2);
        Map<String, Map<String, Object>> userInfo = accountsInfoService.getInfoByUuid(uuids, fields, version, false, true);
        return JsonResult.success(userInfo);
    }

    @RequestMapping(params = "method=getInfoByUuidsNoSecretAndLevel")
    public JsonResult getInfoByUuidsNoSecretAndLevel(RequestParams params) {
//        LOGGER.info("getInfoByUuidsNoSecretAndLevel--->start");
        String[] uuids = params.getFormStringArray(0);
        String[] fields = params.getFormStringArrayOption(1);
        String version = params.getFormStringOption(2);
        Map<String, Map<String, Object>> userInfo = accountsInfoService.getInfoByUuid(uuids, fields, version, false, false);
        return JsonResult.success(userInfo);
    }

    /**
     * 设置用户头像，3重校验
     * @param params
     * @return
     */
    @RequestMapping(params = "method=setAccountAvatarV3")
    public JsonResult setAccountAvatarV3(RequestParams params) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.HTTP_REQUEST_SET_AVATAR_V3_COUNTER);
        String accountUuid = params.getFormString(0);
        String url = params.getFormStringOption(1);
        String bucket = params.getFormStringOption(2);
        String smid = StringUtils.isBlank(params.getFormStringOption(3)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(3);
        Integer needDetectFace = params.getFormIntegerDefault(4, 0); // 是否需要检测人脸
        Integer beauty = params.getFormIntegerDefault(5, 0); // 是否美颜过
        // 针对20大的资料修改限制
        limitModificationUtil.isLimitAvatarModify(accountUuid);

        LOGGER.info("20 limit setAccountAvatarV3 smid: {}", smid);
        LOGGER.info("needDetectFace: {}", needDetectFace);

        LOGGER.info("用户设置头像信息setAccountAvatarV3,用户uuid={},url={},bucket={}",accountUuid,url,bucket);

        int dotIndex = url.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < url.length() - 1) {
            String extension = url.substring(dotIndex + 1).toLowerCase();
            LOGGER.info("extension={}",extension);

            if(!AccountServerUtil.accountAvatarTypeList.contains(extension)){
                throw new ServiceException(CodeStatus.AVATAR_TYPE_ERR.value(),CodeStatus.AVATAR_TYPE_ERR.getReasonPhrase()+AccountServerUtil.servicePhone);
            }
        }
        SpringContextHolder.getBean(AvatarModifyCtrl.class).increase(accountUuid);
        accountsPhotoService.setAccountAvatarV3(accountUuid, url, bucket, smid, needDetectFace == 1, beauty == 1);

        return JsonResult.success(true);
    }

    /**
     * 根据Uuid查询用户昵称，<br/>
     * <b>service:</b> info <br/>
     * <b>method:</b> getNameByUuid <br/>
     * <b>form:</b> [uuid:String 要查询的账号id](单个查询)或[[uuid1:String 要查询的uuid1,uuid2:String 要查询的uuid2,uuid3:String 要查询的uuid3...]](批量查询) <br/>
     * 获取失败返回具体的错误信息<br/>
     *
     * @param params
     * @return
     * @Title getNameByUuid
     * <AUTHOR>
     * 2016年12月15日 下午2:51:23
     */
    @RequestMapping(params = "method=getNameByUuid")
    public JsonResult getNameByUuid(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        Map<String, String> result = accountsService.getNameByUuid(uuids);
        return JsonResult.success(result);
    }

    /**
     * 根据Uuid查询用户昵称和注册时间<br/>
     * <b>service:</b> info <br/>
     * <b>method:</b> getNameAndRegtimeByUuid <br/>
     * <b>form:</b> [uuid:String 要查询的账号id](单个查询)或[[uuid1:String 要查询的uuid1,uuid2:String 要查询的uuid2,uuid3:String 要查询的uuid3...]](批量查询) <br/>
     * 获取失败返回具体的错误信息<br/>
     *
     * @param params
     * @return
     * @Title getNameAndRegtimeByUuid
     * <AUTHOR>
     * 2016年12月15日 下午2:51:23
     */
    @RequestMapping(params = "method=getNameAndRegtimeByUuid")
    public JsonResult getNameAndRegtimeByUuid(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        Map<String, Map<String, String>> result = accountsService.getNameAndRegtimeByUuid(uuids);
        return JsonResult.success(result);
    }

    /**
     * 根据uuid获取设备信息
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getLastLoginDeviceByUuid")
    public JsonResult getLastLoginDeviceByUuid(RequestParams params) {
        String uuid = params.getFormString(0);
        String returnToken = params.getFormStringOption(1);
        Map<String, String> map = accountsInfoService.mGetLastLoginDeviceByUuids(new String[]{uuid}, returnToken).get(uuid);
        return JsonResult.success(map);
    }

    /**
     * 根据昵称获取uuid
     *
     * 2020.06.11 昵称可重复后返回结果不全
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getUuidByAccountName")
    public JsonResult getUuidByAccountName(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 根据昵称获取多个用户uuid
     *
     * <AUTHOR>
     * @date 2020/06/11 09:53
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listUuidByAccountName")
    public JsonResult listUuidByAccountName(RequestParams params) {
        String accountName = params.getFormString(0);
        List<String> uuidList = accountsInfoService.listUuidByAccountName(accountName);
        return JsonResult.success(uuidList);
    }

    /**
     * 根据手机号获取uuid
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getUuidByMobile")
    public JsonResult getUuidByMobile(RequestParams params) {
        // 2023.08.10 增加cloned逻辑
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String mobile = params.getFormString(0);
        // 占位，暂时不用
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);
        // 有传cloned，则赋值到头参数cloned中
        if(StringUtils.isNotBlank(clonedStr)) {
            if(StringUtils.isNumeric(clonedStr)) {
                soaBaseParams.setCloned(clonedStr);
            }else {
                LOGGER.error("业务cloned传值错误，cloned={}", clonedStr);
            }
        }
        RequestParamsUtil.initDefaultAppcodeAndCloned(CommConst.APPCODE_TAQU, CommConst.CLONED_TAQU);

        String uuid = accountsInfoService.getUuidByMobile(mobile);
        Map<String, String> result = new HashMap<>();
        result.put("uuid", uuid);
        return JsonResult.success(result);
    }

    /**
     * 根据手机号获取uuid，昵称
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getUuidAndNameByMobile")
    public JsonResult getUuidAndNameByMobile(RequestParams params) {
        // 2023.08.10 增加cloned逻辑
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String mobile = params.getFormString(0);
        // 占位，暂时不用
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);
        // 有传cloned，则赋值到头参数cloned中
        if(StringUtils.isNotBlank(clonedStr)) {
            if(StringUtils.isNumeric(clonedStr)) {
                soaBaseParams.setCloned(clonedStr);
            }else {
                LOGGER.error("业务cloned传值错误，cloned={}", clonedStr);
            }
        }
        RequestParamsUtil.initDefaultAppcodeAndCloned(CommConst.APPCODE_TAQU, CommConst.CLONED_TAQU);

        Map<String, String> result = accountsInfoService.getUuidAndNameByMobile(mobile);
        return JsonResult.success(result);
    }

    /**
     * 根据手机号批量获取uuid
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=batchGetUuidByMobile")
    public JsonResult batchGetUuidByMobile(RequestParams params) {
        // 2023.08.10 增加cloned逻辑
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String[] mobile = params.getFormStringArray(0);
        // 占位，暂时不用
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);
        // 有传cloned，则赋值到头参数cloned中
        if(StringUtils.isNotBlank(clonedStr)) {
            if(StringUtils.isNumeric(clonedStr)) {
                soaBaseParams.setCloned(clonedStr);
            }else {
                LOGGER.error("业务cloned传值错误，cloned={}", clonedStr);
            }
        }
        RequestParamsUtil.initDefaultAppcodeAndCloned(CommConst.APPCODE_TAQU, CommConst.CLONED_TAQU);

        Map<String, String> result = accountsInfoService.batchGetUuidByMobile(mobile);
        return JsonResult.success(result);
    }

    /**
     * 后台搜索用户(APP端禁止调用该接口)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=searchForBackstage")
    public JsonResult searchForBackstage(RequestParams params) {
        String accountName = params.getFormStringOption(0);
        String accountUuid = params.getFormStringOption(1);
        String mobile = params.getFormStringOption(2);
        String accountId = params.getFormStringOption(3);
        String token = params.getFormStringOption(4);
        String cardId = params.getFormStringOption(5);

        accountName = StringUtils.trim(accountName);
        accountUuid = StringUtils.trim(accountUuid);
        mobile = StringUtils.trim(mobile);
        accountId = StringUtils.trim(accountId);
        token = StringUtils.trim(token);
        cardId = StringUtils.trim(cardId);

        return JsonResult.success(accountsInfoService.searchForBackstage(accountName, accountUuid, mobile, accountId, token, cardId));
    }

    /**
     * 后台根据uuid查询用户信息(APP端禁止调用该接口)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfoByUuidForBackstage")
    public JsonResult getInfoByUuidForBackstage(RequestParams params) {
        String uuid = params.getFormString(0);
        String version = params.getFormStringOption(1);
        String cardId = params.getFormStringOption(2);
        Map<String, Object> resultMap = accountsInfoService.getInfoByUuidForBackstage(uuid, version, cardId);
        return JsonResult.success(resultMap);
    }

    /**
     * 根据token查询游客账号信息(uuid)，配合商城迁移游客数据用 2017-04-25
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getGuestInfoByTokenAndAppCode")
    public JsonResult getGuestInfoByTokenAndAppCode(RequestParams params) {
        String token = params.getFormString(0);
        Integer appcode = params.getFormInteger(1);
        return JsonResult.success(accountsInfoService.getInfoByTokenAndAppCode(token, appcode));
    }

    /**
     * 后台根据昵称搜索用户(APP端禁止调用该接口)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=searchByAccountName")
    public JsonResult searchByAccountName(RequestParams params) {
        String accountName = params.getFormStringOption(0);
        return JsonResult.success(accountsInfoService.searchByAccountName(accountName));
    }

    /**
     * 批量查询相似点
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=mFindSimilar")
    public JsonResult mFindSimilar(RequestParams params) {
        String uuid1 = params.getFormString(0);
        String[] uuid2List = params.getFormStringArray(1);
        Map<String, String> similarMap = accountsInfoService.mFindSimilar(uuid1, Arrays.asList(uuid2List));
        return JsonResult.success(similarMap);
    }

    /**
     * 找用户之间的相似点
     * 2024.3.14 无调用，可下线
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=findSimilar")
    public JsonResult findSimilar(RequestParams params) {
        String uuid1 = params.getFormString(0);
        String uuid2 = params.getFormString(1);
        String similar = accountsInfoService.findSimilar(uuid1, uuid2);
        return JsonResult.success(MapUtils.putAll(new HashMap(), new String[]{"similar_remark", similar}));
    }

    /**
     * 供搜索系统调用，根据用户uuid集合获取用户其他信息
     */
    @RequestMapping(params = "method=getAccountsInfoByUuidsForSearch")
    public JsonResult getAccountsInfoByUuidsForSearch(RequestParams params) {
        String[] accountUuids = params.getFormStringArray(0);
        return JsonResult.success(accountsInfoService.getAccountsInfoByUuidsForSearch(Arrays.asList(accountUuids)));
    }

    /**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 其他系统接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

    /**
     * 根据uuid获取accountId，直播跑脚本临时用，不允许其他系统调用 chenquanan 2018-11-22
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getAccountIdByUuid")
    public JsonResult getAccountIdByUuid(RequestParams params) {
        String uuid = params.getFormString(0);
        Long accountId = accountsService.getAccountIdByUuid(uuid);
        return JsonResult.success(accountId);
    }

    /**
     * 根据token查询设备的前一个活跃时间，即今天之前的最后一个活跃时间
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getPrevActiveTime")
    public JsonResult getPrevActiveTime(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long activeTime = accountsMemberInfoService.getAccountPrevActiveTime(accountUuid);
        return JsonResult.success(activeTime);
    }

    /**
     * 根据uuid查询最近活跃时间
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getActiveTime")
    public JsonResult getActiveTime(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer count = params.getFormIntegerDefault(1, 3);
        List<Integer> result = accountsMemberInfoService.getAccountActiveTime(accountUuid, count);
        return JsonResult.success(result);
    }

    /**
     * 获取未活跃天数
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getNoActiveDays")
    public JsonResult getNoActiveDays(RequestParams params) {
        String accountUuid = params.getFormString(0);
        int intervalDays = accountsMemberInfoService.getNoActiveDays(accountUuid);
        Map<String, Integer> result = new HashMap<>();
        result.put("interval_days", intervalDays);
        return JsonResult.success(result);
    }

    @RequestMapping(params = "method=recordVoiceSign")
    public JsonResult recordVoiceSign(RequestParams params) {
        // 针对20大的资料修改限制
        LimitModificationUtil.isLimitModify();

        String accountUuid = params.getFormString(0);
        String voiceSignInfo = params.getFormString(1);
        String source = params.getFormStringDefault(2, "");
        String smid = StringUtils.isBlank(params.getFormStringOption(3)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(3);
        Boolean result = voiceSignInfoService.recordVoiceSign(accountUuid, voiceSignInfo, source, smid);
        Map<String,Object> map = Maps.newHashMap();
        map.put("result",result);
        return JsonResult.success(map);
    }

    /**
     * 获取灵魂契合度信息接口
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getSoulFitInfo")
    public JsonResult getSoulFitInfo(RequestParams params){
        String ownUuid = params.getFormString(0);
        String uuid = params.getFormString(1);
        Map<String,Object> info = accountsInfoService.getSoulFitInfo(ownUuid,uuid);
        return JsonResult.success(info);
    }

    private static final Counter COUNTER = Counter.build()
            .name("account_report_location_count")
            .labelNames("enable", "sex")
            .help("Total requests.")
            .register();

    /**
     * 上报客户端是否启用定位服务
     * @param params
     * @return
     */
    @RequestMapping(params = "method=reportLocationInfo")
    public JsonResult reportLocationInfo(RequestParams params){
        String accountUuid = params.getFormString(0);
        Integer enableLocation = params.getFormInteger(1);
        String baseaddr = params.getFormStringOption(2);

        String sexType = String.valueOf(SoaBaseParams.fromThread().getGender());
        if (StringUtils.isBlank(sexType) || sexType.equals("0")) {
            Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"sex_type"}, "1", false, false).get(accountUuid);
            sexType = MapUtils.getString(infoMap, "sex_type");
        }

        COUNTER.labels(String.valueOf(enableLocation), sexType).inc();

        accountsInfoService.reportLocationInfo(accountUuid,enableLocation,baseaddr);
        return JsonResult.success();
    }

    /**
     * 登录时，下发上报给gio的用户信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=reportAccountInfo")
    public JsonResult reportAccountInfo(RequestParams params){
        String accountUuid = params.getFormString(0);
        int versionNumber = params.getFormIntegerDefault(1, 0);

        CounterMonitor.reportAccountInfo(versionNumber);

        Map<String,Object> result = accountsInfoService.reportAccountInfo(accountUuid, versionNumber);
        return JsonResult.success(result);
    }

    /**
     * 2024.08.14 网关api已下线，代码可下
     *
     * 客户端上报用户的一些常规信息到服务端，目前是收集第三方应用探测
     * 需求地址：http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
     * 问了李阳，暂未明确后续的需求，目前就暂时只做上报，后续根据需求需要进行保存
     */
    @Deprecated
    @RequestMapping(params = "method=reportUserCommonInfo")
    public JsonResult reportUserCommonInfo(RequestParams params){
        String accountUuid = params.getFormStringOption(0);
        String appInfos = params.getFormString(1);
        accountsInfoService.reportUserCommonInfo(accountUuid,appInfos);
        return JsonResult.success();
    }

//    /**
//     * 查询用户创意标签
//     * TODO 给推广用的，感觉没调用了
//     *
//     * @param params
//     * @return
//     */
//    @Deprecated
//    @RequestMapping(params = "method=getAccountCreativeLabel")
//    public JsonResult getAccountCreativeLabel(RequestParams params){
//        String accountUuid = params.getFormString(0);
//        String accountCreativeLabel = accountsRegAttributionService.getAccountCreativeLabel(accountUuid);
//        JsonResult success = JsonResult.success();
//        success.setData(accountCreativeLabel);
//        return success;
//    }

    /**
     * 获取用户主页相册
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getAccountCover")
    public JsonResult getAccountCover(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        String toAccountUuid = params.getFormStringOption(1);

        List<Map<String, String>> result = accountsInfoService.getAccountCover(accountUuid,toAccountUuid);
        JsonResult jsonResult = JsonResult.success(result);
        Map<String, String> map = Maps.newHashMap();
        map.put("avatar_host", accountsService.getAvatarUrlPrefix());
        map.put("img_host", accountsService.getImgUrlPrefix());
        jsonResult.setExtra(map);
        return jsonResult;
    }

    /**
     * 批量 根据uuid和token，查询uuid上一次设备的数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listAccountLastTokenInfo")
    public JsonResult listAccountLastTokenInfo (RequestParams params) {
        String json = params.getFormString(0);
        List<Map<String,String>> list = JsonUtils.stringToObject(json, new TypeReference<List<Map<String, String>> >() {});
        Map<String, AccountAndTokenInfoDto> result = accountsMemberInfoService.listAccountLastTokenInfo(list);
        JsonResult jsonResult = JsonResult.success(result);
        return jsonResult;
    }


    /**
     * 根据用户uuid，查询当前手机登录token，对应的ios系统版本。
     *
     * @param params
     * @return 未上报存储未/非ios设备， 返回空字符串
     */
    @RequestMapping(params = "method=getIosSysVersionByUuid")
    public JsonResult getIosSysVersionByAccountUuid (RequestParams params) {
        String uuid = params.getFormString(0);
        Map<String,String> map = membersService.getIosSysVersionByUuid(uuid);
        JsonResult jsonResult = JsonResult.success(map);
        return jsonResult;
    }

    /**
     * 后台查询用户状态，提供给风控后台(APP端禁止调用该接口)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getUserStatusForBackstage")
    public JsonResult getUserStatusForBackstage(RequestParams params) {
        String uuid = params.getFormString(0);
        return JsonResult.success(accountsInfoService.getUserStatusForBackstage(uuid));
    }

    /**
     * 后台批量查询用户状态，提供给风控后台(APP端禁止调用该接口)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listUserStatusForBackstage")
    public JsonResult listUserStatusForBackstage(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        return JsonResult.success(accountsInfoService.listUserStatusForBackstage(uuids));
    }

    /**
     * 后台批量查询用户状态，提供给风控后台(APP端禁止调用该接口)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getFriendshipPreferLabel")
    public JsonResult getFriendshipPreferLabel(RequestParams params) {
        String uuid = params.getFormString(0);
        return JsonResult.success(accountLabelService.getFriendshipPreferLabel(uuid));
    }

    /**
     * 用户设置交友偏好标签
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年12月12日 上午11:56:57
     */
    @Api
    @RequestMapping(params = "method=saveFriendshipPreferLabel")
    public JsonResult saveFriendshipPreferLabel(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String ids = params.getFormStringDefault(1, "");
        accountLabelService.saveFriendshipPreferLabel(accountUuid, ids);
        return JsonResult.success();
    }

    /**
     * 获取用户已设置的标签（交友偏好）
     */
    @RequestMapping(params = "method=listSetPersonalityLabel")
    public JsonResult listSetPersonalityLabel(RequestParams params) {
        String accountUuid = params.getFormString(0);
        List<String> list = accountLabelService.listSetPersonalityLabel(accountUuid);
        return JsonResult.success(ImmutableMap.of("list", list));
    }

    /**
     * 获取用户标签分类
     */
    @Api
    @RequestMapping(params = "method=listCategoryLabel")
    public JsonResult listCategoryLabel(RequestParams params) {
        String accountUuid = params.getFormString(0);
        return JsonResult.success(accountLabelServiceV2.getCategoryLabel(accountUuid));
    }

    /**
     * 批量查询标签
     */
    @Api
    @RequestMapping(params = "method=pageLabel")
    public JsonResult pageLabel(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long categoryId = params.getFormLong(1);
        Integer pageNum = params.getFormIntegerDefault(2, 0);
        Integer pageSize = params.getFormIntegerDefault(3, 15);
        ImmutableMap<String, List<PageLabelResp>> map = ImmutableMap.of("labelList", accountLabelServiceV2.pageLabel(accountUuid, categoryId, new PageParam(pageNum, pageSize)));
        return JsonResult.success(map);
    }

    /**
     * 设置标签
     */
    @Api
    @RequestMapping(params = "method=setLabel")
    public JsonResult setLabel(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String labelIds = params.getFormString(1);
        List<Long> collect = Arrays.stream(labelIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        accountLabelServiceV2.setLabel(accountUuid, collect);
        return JsonResult.success();
    }

    /**
     * 获取背景列表（资料页）
     */
    @Api
    @RequestMapping(params = "method=listBackground")
    public JsonResult listBackground(RequestParams params) {
        String accountUuid = params.getFormString(0);
        List<HomepageBackgroundResp> list = homepageBackgroundService.listBackground(accountUuid);
        return JsonResult.success(ImmutableMap.of("list", list));
    }

    /**
     * 获取背景列表（资料页）
     */
    @Api
    @RequestMapping(params = "method=setBackground")
    public JsonResult setBackground(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer bgId = params.getFormInteger(1);
        homepageBackgroundService.setBackground(accountUuid, bgId);
        return JsonResult.success();
    }

    /**
     * 获取吸引力
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getAllure")
    public JsonResult getAllure(RequestParams params) {
        String uuid = params.getFormStringDefault(0, "");
        return JsonResult.success(
            AlEvaluationVersionBarrier.pass() ?
                allureService.getAllureV2(uuid) :
                allureService.getAllure(uuid)
        );
    }

    /**
     * 获取他人标签
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getTargetLabel")
    public JsonResult getTargetLabel(RequestParams params) {
        String uuid = params.getFormStringDefault(0, "");
        String target = params.getFormStringDefault(1, "");
        return JsonResult.success(accountLabelServiceV2.getTargetLabelForSoa(uuid, target));
    }

    /**
     * 获取标签与图片
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listLabelAndPhoto")
    public JsonResult listLabelAndPhoto(RequestParams params) {
        String uuids = params.getFormStringDefault(0, "");
        return JsonResult.success(accountsInfoService.listLabelAndPhoto(Lists.newArrayList(uuids.split(","))));
    }
}
