package cn.taqu.account.controller.api.jrpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AccountsLogService;
import cn.taqu.account.vo.CommonPage;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 用户日志相关查询
 * <AUTHOR>
 * 2017年5月5日 下午3:54:17
 */
@RestController
@RequestMapping(value="/api", params="service=log")
public class AccountsLogController {

	@Autowired
	private AccountsLogService accountsLogService;
	
	/**
	 * 查询用户昵称修改记录 <br/>
	 * <b>service:</b> identity <br/>
	 * <b>method:</b> getNicknameLogForBackstage <br/>
	 * <b>form:</b> [page_number:Integer 页数, page_size:Integer 每页数量, account_uuid:String 用户uuid] <br/>
	 * @Title getNicknameLogForBackstage
	 * @param params
	 * @return
	 * <AUTHOR>
	 * 2017年3月14日 下午3:59:59
	 */
	@RequestMapping(params = "method=getNicknameLogForBackstage")
	public JsonResult getNicknameLog(RequestParams params) {
		Integer pageNumber = params.getFormInteger(0);//页数
		Integer pageSize = params.getFormInteger(1);//每页数量
		String accountUuid = params.getFormString(2);

        CommonPage page = accountsLogService.getNicknameLog(pageNumber, pageSize, accountUuid);
		return JsonResult.success(page);
	}

}
