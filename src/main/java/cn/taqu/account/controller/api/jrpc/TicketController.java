package cn.taqu.account.controller.api.jrpc;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.service.TicketService;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 凭证相关的控制器
 * @ClassName TicketController.java
 * <AUTHOR>
 * @date 2015年9月28日 下午1:54:16
 */
@RestController
@RequestMapping(value="/api", params="service=ticket")
public class TicketController {

	@Autowired
	private TicketService ticketService;

	/**
	 * 登录用户名生成登录凭证<br/>
	 * <b>service:</b> ticket <br/>
	 * <b>method:</b> create <br/>
	 * <b>form:</b> [username:String 用户名] <br/>
	 * 返回生成的登录凭证
	 *
	 * @Title create
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @Date 2015年9月28日 下午1:55:39
	 */
	@Deprecated
	@RequestMapping(params="method=create")
	public JsonResult create(RequestParams params) {
	    throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
	}

	/**
	 * 校验ticket是否有效
	 * 已经在网关配置了ticket转uuid，直播有在用，周期请求，判断用户是否登出
	 * @param params
	 * @return
	 */
	@RequestMapping(params="method=isTicketValid")
	public JsonResult isTicketValid(RequestParams params) {
		String accountUuid = params.getFormStringOption(0);
		Map<String, String> result = new HashMap<>();
		result.put("is_valid", "1");
		return JsonResult.success(result);
	}
}
