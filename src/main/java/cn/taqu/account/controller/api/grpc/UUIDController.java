package cn.taqu.account.controller.api.grpc;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.service.UUIDService;
import cn.taqu.core.protocol.annotation.Rpc;
import cn.taqu.core.protocol.grpc.base.BaseRpcController;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

/**
 * UUID相关的控制器
 * @ClassName UUIDController.java
 * <AUTHOR>
 * @date 2015年9月24日 下午3:53:58
 */
@Deprecated
@Controller("UUID")
public class UUIDController extends BaseRpcController {

	@Autowired
	private UUIDService uuidService;
	
	/**
	 * 生成随机UUID<br/>
	 * <b>service:</b> UUID <br/>
	 * <b>method:</b> get <br/>
	 * <b>form:</b>  <br/>
	 * 生成成功返回code为{@link JsonResult#CODE_NORMAL}，data为{uuid:uuid值};生成失败返回{@link CodeStatus GET_UUID_FAILTER}
	 * 
	 * @Title get
	 * @return
	 * <AUTHOR>
	 * @Date 2015年9月24日 下午3:54:47
	 */
	@Rpc
	public JsonResult get() {
		String uuid = uuidService.generateUUID();
		Map<String,String> uuidMap = new HashMap<String,String>();
		uuidMap.put("uuid", uuid);
		return JsonResult.success(uuidMap);
	}
}
