package cn.taqu.account.controller.api.jrpc;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 用户驾照相关处理
 */
@Deprecated
@RestController
@RequestMapping(value = "/api", params = "service=accountDriver")
public class AccountDriverController {

    /**
     * 驾照介绍页
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getMyDriverLevelInfo")
    public JsonResult getMyDriverLevelInfo(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 驾照介绍页
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getMyDriverLevelInfoByUuid")
    public JsonResult getMyDriverLevelInfoByUuid(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);

    }

    /**
     * 增加公里数
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=addKilometer")
    public JsonResult addKilometer(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);

    }

    /**
     * 增加公里数，可以指定增加几公里
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=addKilometerWithKm")
    public JsonResult addKilometerWithKm(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);

    }

    /**
     * 增加公里数 - 后台
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=addKilometerForBackstage")
    public JsonResult addKilometerForBackstage(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);

    }

    @RequestMapping(params = "method=getKilometerLog")
    public JsonResult getKilometerLog(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
    }

    /**
     * 没用了
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getKilometerLogForBackstage")
    public JsonResult getKilometerLogForBackstage(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);

    }

    /**
     * 没用了
     *
     * 获取周公里数排行前几名的用户uuid
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getTopKmWeekRankUuid")
    public JsonResult getTopKmWeekRankUuid(RequestParams params) {
        throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);

    }
}
