package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.AccountsCardLogService;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api", params = "service=accountsCardLog")
public class AccountsCardLogController {
    @Autowired
    private AccountsCardLogService accountsCardLogService;

    /**
     * 获取赠送接收日志
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=giveRecvLog")
    public JsonResult giveRecvLog(RequestParams params) {
        String uuid = params.getFormStringOption(0);
        int page = params.getFormIntegerDefault(1, 1);
        int limit = params.getFormIntegerDefault(2, 10);
        List<Map<String, String>> result = accountsCardLogService.giveRecvLog(uuid, page, limit);
        return JsonResult.success(result);
    }
}
