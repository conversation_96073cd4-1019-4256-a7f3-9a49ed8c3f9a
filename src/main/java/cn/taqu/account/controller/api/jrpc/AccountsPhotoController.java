package cn.taqu.account.controller.api.jrpc;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.taqu.core.utils.SpringContextHolder;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.cron.AccountPhotoNumUpdateTask;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.service.AccountsPhotoCheckFailService;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 个人相册controller
 *
 * <AUTHOR>
 *         2017年4月18日 下午2:50:52
 */
@RestController
@RequestMapping(value = "/api", params = "service=photo")
public class AccountsPhotoController {
    private static Logger LOGGER = LoggerFactory.getLogger(AccountsPhotoController.class);

    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;
    @Autowired
    private AccountsPhotoCheckFailService accountsPhotoCheckFailService;

    /**
     * 获取用户相册信息，此方法目前只有后台查询用户资料才使用，不提供app使用<br/>
     * <b>service:</b> photo <br/>
     * <b>method:</b> getAccountsPhoto <br/>
     * <b>form:</b> [accountUuid:String 用户uuid]<br/>
     *
     *
     * @param params
     * @return
     * @Title getAccountsPhoto
     * <AUTHOR>
     * 2017年4月18日 下午2:52:16
     */
    @RequestMapping(params = "method=getAccountsPhoto")
    public JsonResult getAccountsPhoto(RequestParams params) {
        String accountUuid = params.getFormString(0);
        List<Map<String, String>> mapList = accountsPhotoService.getAccountsPhoto(accountUuid, true);
        return JsonResult.success(mapList);
    }

    /**
     * 批量更新图片状态(审核用)
     *
     * <AUTHOR>
     * @date 2020/03/17 16:17
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateAccountsPhotoStatusBatchV4")
    public JsonResult updateAccountsPhotoStatusBatchV4(RequestParams params) {
        Integer status = params.getFormInteger(0);
        String json = params.getFormString(1);
        Integer checkFrom = params.getFormIntegerDefault(2, 0);
        Map<Long, String> photoMap = JsonUtils.stringToObject(json, new TypeReference<Map<Long,String>>(){});
        if(MapUtils.isEmpty(photoMap)) {
        	 return JsonResult.success(new HashMap<>());
        }
        String operatorToken = RequestParams.getSoa_basic_java().getToken();
        Set<Long> photoIdSet = photoMap.keySet();
        if(photoIdSet.size() != 1){
            throw new ServiceException("photo_more", "只能选择一张图片进行处罚。");
        }
        Long photoId = Lists.newArrayList(photoIdSet).get(0);
        String accountUuid = accountsPhotoService.getAccountUuidByPhotoId(photoId);
        Map<String, String> accountsPhotoMap = Maps.newHashMap();
        // 加锁
        if(StringUtils.isNotBlank(accountUuid)){
            String deleteKey = RedisKeyConstant.ACCOUNT_PHOTO_DOING_DELETE_UUID.setArg(accountUuid);
            Boolean lockSuccess = redisLockUtil.lockLimitTimes(deleteKey, 50, 2000, 60);
            if(!lockSuccess){
                throw new ServiceException(CodeStatus.PHTO_CHECK_BUSY);
            }
            
            accountsPhotoMap = accountsPhotoService.updateAccountsPhotoWithPunish(status, photoId, photoMap.get(photoId), operatorToken);
            // 释放锁
            accountsInfoService.updateAccountPhotoNumber(Collections.singleton(accountUuid), "db");
            redisLockUtil.unLock(RedisKeyConstant.ACCOUNT_PHOTO_DOING_DELETE_UUID.setArg(accountUuid));
            
            // 相册修改事件
            Map<String, Object> map = new HashMap<>();
            map.put("account_uuid", accountUuid);
            map.put("create_time", DateUtil.currentTimeSeconds());
            SpringContextHolder.getBean(AccountPhotoNumUpdateTask.class).sendAuditEvent(map);
//            kafkaSinkUtil.push(AccountPhotoNumUpdateTask.TOPIC, map);
        }

        JsonResult jsonResult = JsonResult.success("设置图片状态成功。");
        jsonResult.setData(Lists.newArrayList(accountsPhotoMap));
        return jsonResult;
    }
    
    /**
     * 重置用户头像(风控使用)
     * @param params
     * @return
     */
    @RequestMapping(params = "method=resetAccountsPhoto")
    public JsonResult resetAccountsPhoto(RequestParams params) {
        Long photoId = params.getFormLong(0);
        String url = params.getFormString(1);
        String accountUuid = params.getFormString(2);
        String operator = params.getFormString(3);
        // 加锁
        String deleteKey = RedisKeyConstant.ACCOUNT_PHOTO_DOING_DELETE_UUID.setArg(accountUuid);
        Boolean lockSuccess = redisLockUtil.lockLimitTimes(deleteKey, 50, 2000, 60);
        if(!lockSuccess){
            throw new ServiceException(CodeStatus.PHTO_CHECK_BUSY);
        }
        
        accountsPhotoService.updateAccountsPhotoWithPunish(AccountsPhoto.Status.ILLEGAL.getValue(), photoId, url, operator, null, accountUuid);
        accountsInfoService.updateAccountPhotoNumber(Collections.singleton(accountUuid), "db");
        // 释放锁
        redisLockUtil.unLock(RedisKeyConstant.ACCOUNT_PHOTO_DOING_DELETE_UUID.setArg(accountUuid));
        
        // 相册修改事件
        Map<String, Object> map = new HashMap<>();
        map.put("account_uuid", accountUuid);
        map.put("create_time", DateUtil.currentTimeSeconds());
//        kafkaSinkUtil.push(AccountPhotoNumUpdateTask.TOPIC, map);
        SpringContextHolder.getBean(AccountPhotoNumUpdateTask.class).sendAuditEvent(map);
        return JsonResult.success();
    }


    /**
     * 图片检测
     * @param params
     * @return
     */
    @RequestMapping(params = "method=detectPhoto")
    public JsonResult detectPhoto(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String avatarUrl = params.getFormString(1);
        String bucket = params.getFormString(2);

        // 没用到
        Integer scene = params.getFormInteger(3);
        // 没用到
        Integer forceSameWithFace = params.getFormInteger(4);
        String smid = StringUtils.isBlank(params.getFormStringOption(5)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(5);

        LOGGER.info("detectPhoto accountUuid:{},avatarUrl:{},smid: {}", accountUuid, avatarUrl, smid);

        Map<String, String> result = accountsPhotoService.detectPhoto(accountUuid, avatarUrl, bucket, smid, scene);
        return JsonResult.success(result);
    }

    /**
     * 图片检测
     * @param params
     * @return
     */
    @RequestMapping(params = "method=testDetectPhoto")
    public JsonResult testDetectPhoto(@RequestParam String accountUuid, @RequestParam String avatarUrl) {
        Map<String, String> result = accountsPhotoService.testDetectPhoto(accountUuid, avatarUrl);
        return JsonResult.success(result);
    }

    /**
     * 点赞头像
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=photoThumbsUpAvatar")
    public JsonResult photoThumbsUpAvatar(RequestParams params){
        String fromAccountUuid = params.getFormString(0);
        String toAccountUuid = params.getFormString(1);
//        accountsPhotoService.photoThumbsUpAvatar(fromAccountUuid, toAccountUuid);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=isAvatarThumbsUp")
    public JsonResult isAvatarThumbsUp(RequestParams params){
        String fromAccountUuid = params.getFormString(0);
        String toAccountUuid = params.getFormString(1);
        return JsonResult.success(accountsPhotoService.isAvatarThumbsUp(fromAccountUuid, toAccountUuid));
    }

    @RequestMapping(params = "method=findCheckFailAvatarByUuid")
    public JsonResult findCheckFailAvatarByUuid(RequestParams params){
        String accountUuid = params.getFormString(0);
        try{
            String url=accountsPhotoCheckFailService.findCheckFailAvatarByUuid(accountUuid);
            String fixUrl=AccountsInfoService.fixUrl(url);
            Map<String,String> map=new HashMap();
            map.put("avatar",fixUrl);
            return JsonResult.success(map);
        }catch (ServiceException se){
            LOGGER.warn("findCheckFailAvatarByUuid fail accountUuid:{}",accountUuid,se);
            return JsonResult.failed(se.getMessage());
        }catch (Exception e){
            LOGGER.error("findCheckFailAvatarByUuid error accountUuid:{}",accountUuid,e);
            return JsonResult.failed(CodeStatus.COMMON_ERROR_CODE.getReasonPhrase());
        }
    }


}
