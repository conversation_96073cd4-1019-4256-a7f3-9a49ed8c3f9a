package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.AccountsCallerService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Deprecated
@RestController
@RequestMapping(value = "api", params = "service=caller")
public class CallerController {

    @Autowired
    private AccountsCallerService accountsCallerService;

    /**
     * 设置语音聊主状态
     * @return
     */
    @RequestMapping(params = "method=setVoiceStatus")
    public JsonResult setVoiceStatus(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer voiceStatus = params.getFormInteger(1);
        log.info("accountUuid：{}, voiceStatus:{}", accountUuid, voiceStatus);
        return JsonResult.success();
    }

    /**
     * 设置视频聊主状态
     * @return
     */
    @RequestMapping(params = "method=setVideoStatus")
    public JsonResult setVideoStatus(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer videoStatus = params.getFormInteger(1);
        log.info("accountUuid：{}, videoStatus:{}", accountUuid, videoStatus);
        return JsonResult.success();
    }
}
