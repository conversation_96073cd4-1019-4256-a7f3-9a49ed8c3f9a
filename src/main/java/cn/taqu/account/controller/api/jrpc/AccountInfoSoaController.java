package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.AccountInformationService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@RestController
@RequestMapping(value = "/api", params = "service=infoSoa")
public class AccountInfoSoaController {

    @Autowired
    private AccountInformationService accountInformationService;

    /**
     * 几乎没啥调用量了
     * 
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getAccountPhotoFaceStatus")
    public JsonResult getAccountPhotoFaceStatus(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, Object> resMap = accountInformationService.getAccountPhotoFaceStatus(accountUuid);
        return JsonResult.success(resMap);
    }


}
