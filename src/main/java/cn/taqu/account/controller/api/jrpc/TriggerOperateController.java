package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.TriggerOperateService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 主要用于画像系统查询用户系统中的数据
 * Created by <PERSON><PERSON><PERSON> on 2017/4/19.
 */
@RestController
@RequestMapping(value = "/api", params = "service=triggerOperate")
public class TriggerOperateController {

    @Resource
    private TriggerOperateService triggerOperateService;

    /**
     * j4有调用
     * 根据账号id查询账号信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getAccountInfoByAccountId")
    public JsonResult getAccountInfoByAccountId(RequestParams params) {
        String uuid = params.getFormString(0);
        return JsonResult.success(triggerOperateService.getAccountInfoByAccountUuid(uuid));
    }

    /**
     * j4有调用
     * 根据设备id查询设备信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getMemberInfoByMemberId")
    public JsonResult getMemberInfoByMemberId(RequestParams params) {
        Long memberId = params.getFormLong(0);
        return JsonResult.success(triggerOperateService.getMemberInfoByMemberId(memberId));
    }
}
