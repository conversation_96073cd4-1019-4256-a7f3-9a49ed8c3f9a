package cn.taqu.account.controller.api.jrpc;

import cn.hutool.crypto.digest.MD5;
import cn.taqu.account.service.CustomerService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/28 下午2:46
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api", params = "service=customerService")
public class CustomerServiceController {

    private final CustomerService customerService;

    private static final String SALT = "7x#K9pL!2mQv$5R";

    @RequestMapping(params = "method=link")
    public JsonResult link(RequestParams params) {
        String uuid = params.getFormString(0);
        String mobileModel = params.getFormStringDefault(1, "");
        return JsonResult.success(customerService.linkInfo(uuid, mobileModel));
    }

    @RequestMapping(params = "method=userInfo")
    public R<?> userInfo(@RequestParam(required = false) String phone,
                         @RequestParam(value = "visitorID", required = false) String visitorId,
                         @RequestParam("timestampKey") String ts,
                         @RequestParam String encryptionToken
    ) {
        String digest = MD5.create().digestHex(ts + SALT);
        log.info("三方用户信息获取 {} {} {} {}", encryptionToken, digest, phone, visitorId);
        return encryptionToken.equals(digest) ?
            R.success(customerService.userInfo(visitorId, phone)) :
            R.fail("illegal access");
    }

    @PostMapping(params = "method=msgArrived")
    public Map<String, Object> msgArrived(@RequestBody NotifyMsg msg) {
        log.info("三方msgArrived {}", msg);
        // 过滤系统消息
        if (!"system".equals(msg.getMode())) {
            customerService.notifyUser(msg.getVisitorId(), msg.getNotifyContent());
        }
        return ImmutableMap.of("success", true);
    }

    @Data
    public static class NotifyMsg {

        // SessionMessage：会话消息； FormMessage：表单评论消息
        @JsonProperty("NotifyContentType")
        private String notifyContentType;

        @JsonProperty("NotifyContent")
        private String notifyContent;

        @JsonProperty("CurrentTime")
        private Long currentTime;

        @JsonProperty("CreateTime")
        private String createTime;

        // reply:坐席回复消息模式；system：系统消息模式；
        @JsonProperty("Mode")
        private String mode;

        @JsonProperty("VisitorId")
        private String visitorId;

        @JsonProperty("SequenceID")
        private String sequenceID;

        @JsonProperty("MsgId")
        private String msgId;

        @JsonProperty("RobotId")
        private String robotId;

        // 坐席ID
        @JsonProperty("AgentId")
        private String agentId;

        // text：文字消息；image：图片消息；voice：语音消息；video：视频消息
        @JsonProperty("MsgType")
        private String msgType;

        @JsonProperty("Content")
        private String content;

        // 当前访客未读的消息个数
        @JsonProperty("VisitorUnreadMessageCount")
        private Integer visitorUnreadMessageCount;
    }

    @Data
    public static class R<T> {
        private String status = "0";

        private String msg = "成功";

        private List<T> data;

        public static <A> R<A> success(A data) {
            R<A> r = new R<>();
            r.setData(Collections.singletonList(data));
            return r;
        }

        public static <A> R<A> fail(String msg) {
            R<A> r = new R<>();
            r.setStatus("-1");
            r.setMsg(msg);
            return r;
        }

    }
}
