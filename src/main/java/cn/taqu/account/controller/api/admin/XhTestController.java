package cn.taqu.account.controller.api.admin;

import cn.taqu.account.constant.RiskCertificationTypeEnum;
import cn.taqu.account.dto.AllureCacheDTO;
import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.service.AliyunLiveFaceDetectService;
import cn.taqu.account.service.AllureService;
import cn.taqu.account.service.ToolsService;
import cn.taqu.account.soa.AIGCSoaService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.SpringContextHolder;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static cn.taqu.account.constant.CommConst.ACCOUNT_PRIVATE_SRC_HOST;

/**
 * 测试接口
 *
 * <AUTHOR>
 * @date 2024/11/25 10:20
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=xhTest")
public class XhTestController {

    /**
     * 刷新吸引力缓存
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=refreshAllureCache")
    public JsonResult refreshAllureCache(RequestParams params) {
        String uuid = params.getFormStringDefault(0, "");
        AllureService service = SpringContextHolder.getBean(AllureService.class);
        service.deleteAllureCache(uuid);
        AllureCacheDTO cache = service.refreshAllure(uuid);
        return JsonResult.success(cache);
    }

    /**
     * 刷新吸引力缓存
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=refreshAllureCache1")
    public JsonResult refreshAllureCache1(RequestParams params) {
        String uuid = params.getFormStringDefault(0, "");
        AllureService service = SpringContextHolder.getBean(AllureService.class);
        AllureCacheDTO cache = service.refreshAllure(uuid);
        return JsonResult.success(cache);
    }

    /**
     * 刷新吸引力缓存
     *
     * @param uuid
     * @return
     */
    @RequestMapping(params = "method=refreshAllureCache2")
    public JsonResult refreshAllureCache2(@RequestParam("uuid") String uuid) {
        AllureService service = SpringContextHolder.getBean(AllureService.class);
        if (uuid.startsWith(",")) {
            uuid = uuid.substring(1);
        }
        service.deleteAllureCache(uuid);
        AllureCacheDTO cache = service.refreshAllure(uuid);
        return JsonResult.success(cache);
    }

    @RequestMapping(params = "method=compareFace")
    public JsonResult compareFace(RequestParams params) {
        String pic1 = params.getFormStringDefault(0, "");
        String pic2 = params.getFormStringDefault(1, "");
        String uuid = params.getFormStringDefault(2, "");
        String type = params.getFormStringDefault(3, "");
        CompareFaceDto compareFaceDto = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).compareFace(pic1, pic2, uuid, RiskCertificationTypeEnum.getBy(type));

        return JsonResult.success(compareFaceDto);
    }
    /**
     * test
     *
     * @return
     */
    @RequestMapping(params = "method=testAI")
    public JsonResult refreshAllureCache2(RequestParams params) {
        String uuid = params.getFormStringDefault(0, "");
        String url = params.getFormStringDefault(1, "");
        AIGCSoaService service = SpringContextHolder.getBean(AIGCSoaService.class);
        service.avatarQuality(uuid, url);
        return JsonResult.success();
    }

    /**
     * 图片私域读取
     */
    @RequestMapping(params = "method=privateDownloadUrl")
    public JsonResult privateDownloadUrl(RequestParams params) {
        String pic = params.getFormStringDefault(0, "");
        if (pic.startsWith(ACCOUNT_PRIVATE_SRC_HOST)) {
            pic = pic.substring(ACCOUNT_PRIVATE_SRC_HOST.length());
        }

        return JsonResult.success(ToolsService.getPrivateBasePic(pic));
    }

}
