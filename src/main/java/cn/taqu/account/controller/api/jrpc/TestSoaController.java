package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.RealPersonService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=test")
public class TestSoaController {

    @Autowired
    private RealPersonService realPersonService;

    @RequestMapping(params = "method=setUserRealPersonCertification")
    public JsonResult setUserRealPersonCertification(RequestParams params) {
        String uuid = params.getFormString(0);
        String photoUrl = params.getFormString(1);

        realPersonService.setUserRealPersonCertification(uuid, photoUrl);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=testSentinel")
    public JsonResult testSentinel(RequestParams params){
        return JsonResult.success();
    }

}
