package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.service.SchoolService;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.StringUtil;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/11/26 下午2:11
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value="/api", params="service=school")
public class SchoolController {

    private final SchoolService service;

    @Api
    @RequestMapping(params = "method=guess")
    public JsonResult guess(RequestParams params) {
        String school = params.getFormString(1);
        if (StringUtil.hasUcs4(school)) {
            throw new ServiceException(CodeStatus.EMOJI_ERROR);
        }
        return JsonResult.success(service.guess(school));
    }

    /**
     * 保存我的学校
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=submit")
    public JsonResult submit(RequestParams params) {
        String uuid = params.getFormString(0);
        Long school = params.getFormLong(1);
        String major = params.getFormStringDefault(2, "");
        String smid = params.getFormStringDefault(3, CommConst.OLD_CLIENT_PACKAGE);
        service.submit(uuid, school, major, smid);
        return JsonResult.success();
    }

    @Api
    @RequestMapping(params = "method=info")
    public JsonResult info(RequestParams params) {
        String uuid = params.getFormString(0);
        String targetUuid = params.getFormStringDefault(1, null);
        if (StringUtils.isNotBlank(targetUuid)) {
            return JsonResult.success(service.info(targetUuid, true));
        }
        return JsonResult.success(service.info(uuid, false));
    }
}
