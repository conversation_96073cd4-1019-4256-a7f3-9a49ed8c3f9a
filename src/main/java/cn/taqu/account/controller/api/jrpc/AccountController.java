package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.common.*;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.RegisterLoginControlIsControlReqDto;
import cn.taqu.account.model.LoginActionInfo;
import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.account.service.*;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.LimitModificationUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.account.utils.RequestParamsUtil;
import cn.taqu.account.utils.mq.AntiSpamMqUtil;
import cn.taqu.account.vo.AccountVo;
import cn.taqu.account.vo.LoginVo;
import cn.taqu.account.vo.RegVo;
import cn.taqu.account.vo.SetNicknameResult;
import cn.taqu.account.vo.resp.DestroyResp;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LocalConfUtil;
import cn.taqu.core.utils.ValidateUtil;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.base.Objects;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户Controller
 *
 * <AUTHOR>
 * 2017年7月5日 上午9:10:25
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=account")
public class AccountController {
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountActionService accountActionService;
    @Autowired
    private AntiSpamMqUtil antiSpamMqUtil;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RedisLockUtil redisLockUtil;

    /**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 客户端接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

    /**
     * 通过ticket验证密码 checkPassword
     * http://************:1234/Account_System/v5/Account/checkPassword
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 上午9:17:30
     */
    @Api
    @RequestMapping(params = "method=checkPassword")
    public JsonResult checkPassword(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        String password = params.getFormString(1);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("check_psw", "1");

        int result = accountsService.checkPassword(accountUuid, password);
        if (result == -1) {
            return JsonResult.failedCode(CodeStatus.PASSWORD_NOT_SET);
        } else if (result == 1) {
            return JsonResult.success(resultMap);
        } else {
            return JsonResult.failedCode(CodeStatus.PASSWORD_NOT_PASS);
        }
    }

    /**
     * 登录（昵称密码、手机号密码）
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:06:12
     */
    @Api
    @RequestMapping(params = "method=login")
    public JsonResult login(RequestParams params) {
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }

        // 2024.06.18 登录注册管控，抛异常
        accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.LOGIN, RegisterLoginControlIsControlReqDto.model.PASSWORD);

        //请求参数
        String username = params.getFormString(0);//可以是手机号码,用户昵称
        String password = params.getFormString(1);
        int versionNumber = params.getFormIntegerDefault(2, 0);
        // 2019-12-03 新增字段
        String shumeiDeviceId = StringUtils.isBlank(params.getFormStringOption(3)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(3);

        // 2020.04.10 接口被刷，特殊处理 自动化环境不限制
        if (StringUtils.isBlank(shumeiDeviceId) && !Objects.equal(LocalConfUtil.getLocalEnv(), "auto")) {
            log.warn("接口未带数美id，可能被刷");
            throw new ServiceException(CodeStatus.USERNAME_OR_PASSWORD_ERROR);
        }
        String extraJson = params.getFormStringDefault(4, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        LoginVo loginVo = accountsService.login(username, password, versionNumber, shumeiDeviceId, extraJson);
        if (StringUtils.isNotBlank(shumeiDeviceId)) {
            loginVo.setShumeiDeviceId(shumeiDeviceId);
        }
        return JsonResult.success(this.loginPostProcess(loginVo, versionNumber, RegisterLoginTypeEnum.PASSWORD));
    }

    /**
     * 趣币字段名称版本 0:返回趣币字段名称为tqcoin; 其他:返回趣币字段名称为score;
     * @param loginVo
     * @param tqcoinNameVersion
     * @return
     */
    private Map<String, Object> loginPostProcess(LoginVo loginVo, int tqcoinNameVersion, RegisterLoginTypeEnum registerLoginTypeEnum) {
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        String token = sbp.getToken();
        Integer platformId = sbp.getPlatform_id();
        Integer appcode = sbp.getAppcode();
        Integer cloned = sbp.getCloned();
        Long appVersion = sbp.getApp_version();
        String ip = sbp.getIp();
        String latitude = sbp.getLatitude();
        String longitude = sbp.getLongitude();

        //用户登录版本限制
        accountsService.loginVersionLimit(loginVo.getUuid(),platformId,appVersion);
        //添加登录行为信息
        me().mergeLoginActionInfo(sbp, loginVo.getUuid(), ActionModeEnum.TAQU.value());
        // 推入队列告知反垃圾系统
        antiSpamMqUtil.pushToAccountActionMq(loginVo.getUuid(), "", AccountActionTypeEnum.LOGIN,registerLoginTypeEnum);
        antiSpamMqUtil.pushToUuidAFRefreshMq(loginVo.getUuid(), appcode, cloned, appVersion, ip);
        antiSpamMqUtil.pushToTradingAccountMq(loginVo.getUuid(),loginVo.getNickname(),registerLoginTypeEnum,latitude,longitude,token,ip);
        //构造返回参数
        Map<String, Object> resultMap = accountsService.handleLoginResult(loginVo, tqcoinNameVersion);
        //推送队列
        accountsService.loginoutBury(token, platformId, appcode, loginVo.getUuid(), "1", cloned);

        // 2020.03.05 更新用户位置
        accountsService.updateAccountExtraInfo(loginVo.getUuid());

        // 登陆成功推送AdmpDeviceLogin
        accountsService.pushToAdmpDeviceLogin(token, loginVo.getUuid(), loginVo.getSex_type(), DateUtil.currentTimeSeconds(), platformId, appcode, cloned, ip);

        return resultMap;
    }

    private AccountController me() {
        return applicationContext.getBean(AccountController.class);
    }

    @Async
    public void mergeLoginActionInfo(SoaBaseParams soaBasicJava, String accountUuid, String actionMode) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }

        LoginActionInfo loginActionInfo = new LoginActionInfo();
        loginActionInfo.setAccountUuid(accountUuid);
        loginActionInfo.setToken(soaBasicJava.getToken());
        loginActionInfo.setActionIp(soaBasicJava.getIp());
        loginActionInfo.setAppcode(soaBasicJava.getAppcode());
        loginActionInfo.setCloned(soaBasicJava.getCloned());
        loginActionInfo.setPlatformId(soaBasicJava.getPlatform_id());
        loginActionInfo.setChannel(soaBasicJava.getChannel());
        loginActionInfo.setAppVersion(soaBasicJava.getApp_version());
        loginActionInfo.setCreateTime(DateUtil.currentTimeSeconds());
        loginActionInfo.setActionMode(actionMode);
        loginActionInfo.setPlatformName(soaBasicJava.getPlatform_name());
        loginActionInfo.setAccess(soaBasicJava.getAccess());
        loginActionInfo.setAlias(soaBasicJava.getAlias());
        loginActionInfo.setGender(soaBasicJava.getGender());
        if(ToolsService.loginActionInfoSwitchWrite.isOn(true)) {
            loginActionInfo.setLongitude(soaBasicJava.getLongitude());
            loginActionInfo.setLatitude(soaBasicJava.getLatitude());
        }else {
            log.info("login_action_info不写加密字段");
            loginActionInfo.setLongitude("");
            loginActionInfo.setLatitude("");
        }

        loginActionInfo.setLongitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLongitude()));
        loginActionInfo.setLatitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLatitude()));
        loginActionInfo.setCity(soaBasicJava.getCity());
        accountActionService.merge(loginActionInfo);
    }

    @Async
    public void mergeRegisterActionInfo(SoaBaseParams soaBasicJava, RegVo regVo, String actionMode) {
        RegisterActionInfo registerActionInfo = new RegisterActionInfo();
        registerActionInfo.setAccountUuid(regVo.getUuid());
        registerActionInfo.setToken(soaBasicJava.getToken());
        registerActionInfo.setActionIp(soaBasicJava.getIp());
        registerActionInfo.setAppcode(soaBasicJava.getAppcode());
        registerActionInfo.setCloned(soaBasicJava.getCloned());
        registerActionInfo.setPlatformId(soaBasicJava.getPlatform_id());
        registerActionInfo.setChannel(soaBasicJava.getChannel());
        registerActionInfo.setAppVersion(soaBasicJava.getApp_version());
        registerActionInfo.setCreateTime(DateUtil.currentTimeSeconds());
        registerActionInfo.setActionMode(actionMode);
        registerActionInfo.setPlatformName(soaBasicJava.getPlatform_name());
        registerActionInfo.setAccess(soaBasicJava.getAccess());
        registerActionInfo.setAlias(soaBasicJava.getAlias());
        registerActionInfo.setGender(regVo.getSex_type());

        if(ToolsService.registerActionInfoSwitchWrite.isOn(true)) {
            registerActionInfo.setLongitude(soaBasicJava.getLongitude());
            registerActionInfo.setLatitude(soaBasicJava.getLatitude());
        }else {
            log.info("register_action_info不写加密字段");
            registerActionInfo.setLongitude("");
            registerActionInfo.setLatitude("");
        }

        registerActionInfo.setLongitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLongitude()));
        registerActionInfo.setLatitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLatitude()));
        registerActionInfo.setCity(soaBasicJava.getCity());
        accountActionService.merge(registerActionInfo);
    }

    /**
     * 登出
     * http://************:1234/Account_System/v5/Account/logout
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:06:33
     */
    @Api
    @RequestMapping(params = "method=logout")
    public JsonResult logout(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormString(0);
        String token = RequestParams.getSoa_basic_java().getToken();
        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        Integer cloned = RequestParams.getSoa_basic_java().getCloned();

        accountsService.logoutByAccountUuid(accountUuid, token, platformId, appcode, cloned, true);

        return JsonResult.success();
    }

    /**
     * 强制登出
     * @param params
     * @return
     */
    @RequestMapping(params = "method=forceLogout")
    public JsonResult forceLogout(RequestParams params) {
        String accountUuid = params.getFormString(0);
        accountsService.forceLogout(accountUuid);
        return JsonResult.success();
    }

    /**
     * 获取注销信息
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=getDestroyInfo")
    public JsonResult getDestroyInfo(RequestParams params){
        String accountUuid = params.getFormStringOption(0);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        return JsonResult.success(accountsService.getDestroyInfo(accountUuid));
    }

    /**
     * 用户注销
     *
     * @param params
     * @return
     * @desc 新版本已绑定手机注销在
     * {@link DestroyController}
     */
    @Api
    @RequestMapping(params = "method=destroyV2")
    public JsonResult destroyV2(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        String reason = params.getFormStringDefault(1, "");
        String mobile = params.getFormStringDefault(2, "");
        String verify = params.getFormStringDefault(3, "");
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        if (StringUtils.isBlank(verify)) {
            throw new ServiceException(CodeStatus.DESTROYED_VERIFY_BLANK);
        }

        accountsService.destroyV2(accountUuid, reason, mobile, verify, "", "", "");
        return JsonResult.success();
    }


    /**
     * 用户注销
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=destroy")
    public JsonResult destroy(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        String reason = params.getFormStringOption(1);
        int version = params.getFormIntegerDefault(2, 0);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        accountsService.destroy(accountUuid, reason);
        return JsonResult.success();
    }

    /**
     * 用户解除注销冷静期
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=relieve")
    public JsonResult relieve(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        accountsService.relieve(accountUuid);
        return JsonResult.success();
    }

    /**
     * 修改密码 ( ‘个人资料’ -> ‘修改密码’ )
     * http://************:1234/Account_System/v5/Account/modifyPassword
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:08:06
     */
    @Api
    @RequestMapping(params = "method=modifyPassword")
    public JsonResult modifyPassword(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        String oldPass = params.getFormString(1);
        String newPass = params.getFormString(2);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        accountsService.modifyPassword(accountUuid, oldPass, newPass);
        return JsonResult.success();
    }

    /**
     * 2024.06.13 无使用
     *
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=regConfig")
    public JsonResult regConfig(RequestParams params) {
        // 1. 他趣、闪恋及闪恋的马甲包：默认性别选男
        List<Integer> maleList = Arrays.asList(1, 4, 60, 76, 61, 68);
        // 2. 投放女用户专用包体：默认性别选女
        List<Integer> femaleList = Arrays.asList(63, 64, 65);

        Integer cloned = SoaBaseParams.fromThread().getCloned();

        Map<String, Object> map = new HashMap<>();
        map.put("default_sex_type", maleList.contains(cloned) ? 1 : (femaleList.contains(cloned) ? 2 : 1));
        map.put("boy_birth_can_change", "1");
        map.put("girl_birth_can_change", "0");

        return JsonResult.success(map);
    }

    /**
     * 注册用户
     * http://************:1234/Account_System/v5/Account/reg
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:09:11
     */
    @Api
    @RequestMapping(params = "method=reg")
    public JsonResult reg(RequestParams params) {
        SoaBaseParams soaBasicJava = RequestParams.getSoa_basic_java();
        // 配配注册限制
        limitPeipeiAccountReg();

        String token = soaBasicJava.getToken();
        Integer platformId = soaBasicJava.getPlatform_id();
        Integer appcode = soaBasicJava.getAppcode();
        Integer cloned = soaBasicJava.getCloned();
        Long appVersion = soaBasicJava.getApp_version();
        String channel = soaBasicJava.getChannel();

        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }

        // 2024.06.18 登录注册管控，抛异常
        accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.REGISTER, RegisterLoginControlIsControlReqDto.model.MOBILE);

        //请求参数
        String mobile = params.getFormString(0);// 只接受手机
        String nickname = params.getFormStringOption(1);
        String password = params.getFormStringOption(2);
        Integer sexType = params.getFormInteger(3);
        String avatar = ToolsService.parseAatarFromAppAvatar(params.getFormStringOption(4));
        int versionNumber = params.getFormIntegerDefault(5, 0);
        String deviceToken = params.getFormStringOption(6); // 通过设备风险SDK获取的deviceToken
        String shumeiDeviceId = params.getFormStringOption(7);
        Long birth = params.getFormLongOption(8);
        Integer defaultNickeName = params.getFormIntegerDefault(9, 0);
        // 注册方式 new-新版  old-旧版  2024.04.18 客户端早就回滚了，此字段无用
        String registerNoUsed = params.getFormStringDefault(10, "old");
        // 交友目的id 2024.04.18 客户端早就回滚了，此字段无用
        Integer makeFriendObjectiveIdNoUsed = params.getFormIntegerDefault(11, 0);
        String extraJson = params.getFormStringDefault(12, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        log.info("reg sexType:{}, register: {}, makeFriendObjectiveId: {}", sexType, registerNoUsed, makeFriendObjectiveIdNoUsed);


        // 添加分布式锁 保证手机号只注册一次
        String lockKey = RedisKeyConstant.ACCOUNT_REGISTER_MOBILE_LOCK.setArg(mobile);
        boolean lock = redisLockUtil.lock(lockKey, 10000); // has unLock
        if (!lock) {
            throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
        }

        try {
            if (token == null || (token = token.trim()).length() == 0 || channel == null || (channel = channel.trim()).length() == 0 || platformId == null || appcode == null || appVersion == null) {
                log.warn("注册失败，存在空头参数");
                throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
            }
            // 校验channel长度，大于则直接抛出异常 SysCodeStatus.REQUEST_PARA_ERROR
            AccountsService.checkAccountsChannelLen(channel);

            RegVo regVo = accountsService.mobileReg(mobile, nickname, password, token, platformId, appcode, cloned, appVersion, channel, sexType, avatar, versionNumber, birth, defaultNickeName, shumeiDeviceId, extraJson);
            //2019-08-13新增,用于阿里云风控新加个参数deviceToken加强监测
            if (StringUtils.isNotBlank(deviceToken)) {
                regVo.setDeviceToken(deviceToken);
            }
            // 数美风控新增shumeiDeviceId参数
            if (StringUtils.isNotBlank(shumeiDeviceId)) {
                regVo.setShumeiDeviceId(shumeiDeviceId);
            }
            return JsonResult.success(this.regPostProcess(regVo, versionNumber,regVo.getRegisterLoginTypeEnumType()));
        } finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 快速登录接口 - v2，接的闪验，没有账号会自动注册，并登录
     * @param params
     * @return
     */
    @RequestMapping(params = "method=quickLoginV2")
    public JsonResult quickLoginV2(RequestParams params) {
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }

        // 2024.06.18 登录注册管控，抛异常
        accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.LOGIN, RegisterLoginControlIsControlReqDto.model.FLASH);

        String accessToken = params.getFormString(0);
        Integer type = params.getFormIntegerDefault(1, 1);
        String deviceToken = params.getFormStringOption(2); // 通过设备风险SDK获取的deviceToken
        String shumeiDeviceId = StringUtils.isBlank(params.getFormStringOption(3)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(3);
        String extraJson = params.getFormStringDefault(4, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        LoginVo resultVo = accountsService.quickLoginV2(accessToken, shumeiDeviceId, extraJson);

        log.info("quick login uuid: {}, smid: {}", resultVo.getUuid(), shumeiDeviceId);

        if (Objects.equal(resultVo.getIs_bind_mobile(), 0)) {
            BuryService.preRegBuryToAdmp(PreRegisterTypeEnum.FLASH.getType());
            return JsonResult.success(resultVo);
        }
        if (StringUtils.isNotBlank(deviceToken)) {
            resultVo.setDeviceToken(deviceToken);
        }
        if (StringUtils.isNotBlank(shumeiDeviceId)) {
            resultVo.setShumeiDeviceId(shumeiDeviceId);
        }
        return JsonResult.success(RegVo.class.isAssignableFrom(resultVo.getClass()) ? this.regPostProcess((RegVo) resultVo, 1,RegisterLoginTypeEnum.QUICK) : this.loginPostProcess(resultVo, 1,RegisterLoginTypeEnum.QUICK));
    }

    /**
     * 自动登录接口，没有账号会自动注册，并登录
     *
     * @return
     */
    @Api
    @RequestMapping(params = "method=autoLogin")
    public JsonResult autoLogin(RequestParams params) {
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        // 限制使用
        if(!LoginRegLimitService.isCanLoginReg(cloned)){
            throw new ServiceException(LoginRegLimitService.CANNOT_LOGIN_REG_NOTICE);
        }

        // 2024.06.18 登录注册管控，抛异常
        accountsService.loginAndRegIsControl(RegisterLoginControlIsControlReqDto.type.LOGIN, RegisterLoginControlIsControlReqDto.model.VCODE);

        String mobile = params.getFormString(0);
        String vcode = params.getFormString(1);
        Integer type = params.getFormIntegerDefault(2, 1); // 是否自动注册，已经废弃
        int versionNumber = params.getFormIntegerDefault(3, 0);
        String deviceToken = params.getFormStringOption(4);
        String shumeiDeviceId = StringUtils.isBlank(params.getFormStringOption(5)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(5);

        // 风控需要的手机信息
        String extraJson = params.getFormStringDefault(6, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        LoginVo resultVo = accountsService.autoLogin(mobile, vcode, shumeiDeviceId, extraJson);
        if (resultVo.getIs_bind_mobile() == 0) {
            BuryService.preRegBuryToAdmp(PreRegisterTypeEnum.MSG.getType());
            return JsonResult.success(resultVo);
        }
        if (StringUtils.isNotBlank(deviceToken)) {
            resultVo.setDeviceToken(deviceToken);
        }
        if (StringUtils.isNotBlank(shumeiDeviceId)) {
            resultVo.setShumeiDeviceId(shumeiDeviceId);
        }
        return JsonResult.success(RegVo.class.isAssignableFrom(resultVo.getClass()) ? this.regPostProcess((RegVo) resultVo, versionNumber,RegisterLoginTypeEnum.VERIFICATION_CODE) : this.loginPostProcess(resultVo, versionNumber,RegisterLoginTypeEnum.VERIFICATION_CODE));
    }

    private Map<String, Object> regPostProcess(RegVo regVo, int tqcoinNameVersion,RegisterLoginTypeEnum registerTypeEnum) {
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        String token = sbp.getToken();
        Integer platformId = sbp.getPlatform_id();
        Integer appcode = sbp.getAppcode();
        Integer cloned = sbp.getCloned();

        //添加注册行为信息
        me().mergeRegisterActionInfo(sbp, regVo, ActionModeEnum.TAQU.value());
        //推入队列通知反垃圾系统
        antiSpamMqUtil.pushToAccountActionMq(regVo.getUuid(), regVo.getNickname(), AccountActionTypeEnum.REGISTER,registerTypeEnum);
        //2017.7.26 新增
        Map<String, Object> resultMap = accountsService.handleReg(regVo, appcode, tqcoinNameVersion);
        accountsService.loginoutBury(token, platformId, appcode, regVo.getUuid(), "1", cloned);
        accountsMemberInfoService.createOrUpdate(regVo.getUuid(), sbp.getToken(), sbp.getApp_version(), sbp.getAppcode(), sbp.getCloned(), sbp.getChannel(), sbp.getPlatform_id());
        accountsMemberInfoService.updateAppVersionCache(regVo.getUuid(), sbp.getApp_version(), sbp.getPlatform_id(), sbp.getChannel());

        // 2020.03.05 更新用户位置
        accountsService.updateAccountExtraInfo(regVo.getUuid());

        // 2020.06.17 IOS注册男用户绑定手机号后发短信  2020.07.04 配配不发短信 http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
//        if(Objects.equal(regVo.getSex_type(), 1) && Objects.equal(platformId, 2) && !Objects.equal(cloned, 50)) {
//        	MessageService.sendSmsRechargeTip(regVo.getMobile());
//        }

        // 2020.04.06刚注册，头像是不可推状态
        BuryService.pushFilterCommonStatusToRecsys(regVo.getUuid(), 1, RecsysReportServerEnum.REAL_AVATAR_UPDATE.value());
        BuryService.pushFilterCommonStatusToRecsys(regVo.getUuid(), 1, RecsysReportServerEnum.AVATAR_UPDATE.value());

        // 推送注册后置处理
        BuryService.pushAccountPostRegister(regVo.getUuid());
        // 2023.08.16 ab实验入组 https://o15vj1m4ie.feishu.cn/wiki/CEFYwjPeViJoCFkF1Enckgv1nzd
        // 2024.11.29 实验已经固化
//        SoaService.getUserExp(regVo.getUuid(), AbRuleCode.DISTANCE_PRIVACY_SETUP, appcode, cloned, token);
        //2023.12.01 Q4社交资料线 数据需求 https://o15vj1m4ie.feishu.cn/docx/OdrNdtiFSo8dXgxtahLcgdUyngh
        // 2024.11.29 实验已经回滚
//        SoaService.getUserExp(regVo.getUuid(), AbRuleCode.FEMALE_DEMAND_REAL_PERSON, appcode, cloned, token);
        return resultMap;
    }
    /**
     * 重置密码 (‘登录’ -> ‘忘记密码？’)
     * http://************:1234/Account_System/v5/Account/resetPassword
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:11:47
     */
    @Api
    @RequestMapping(params = "method=resetPassword")
    public JsonResult resetPassword(RequestParams params) {
        //请求参数
        String mobile = params.getFormString(0);
        String verify = params.getFormString(1);
        String password = params.getFormString(2);
        int version = params.getFormIntegerDefault(3, 1);
        if (!ValidateUtil.isMobile(mobile)) {
            return JsonResult.failedCode(CodeStatus.MOBILE_INVALID);
        }

        int flag = accountsService.resetPassword(mobile, verify, password);
        if (flag == 0) {
            return JsonResult.failedCode(CodeStatus.UNREG_MOBILE);
        } else {
            return JsonResult.success();
        }
    }

    /**
     * 设置密码,适用于第三方登录没有密码
     * http://************:1234/Account_System/v5/Account/setPassword
     *
     * @param params
     * @return
     * <AUTHOR>
     * 2017年7月5日 下午5:12:34
     */
    @Api
    @RequestMapping(params = "method=setPassword")
    public JsonResult setPassword(RequestParams params) {
        //请求参数
        String accountUuid = params.getFormStringOption(0);
        String password = params.getFormString(1);

        accountsService.setPassword(accountUuid, password);
        return JsonResult.success();
    }
    /**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 客户端接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

    /**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 其他系统接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/


    @RequestMapping(params = "method=setNicknameByUuid")
    public JsonResult setNicknameByUuid(RequestParams params) {
        LimitModificationUtil.isLimitModify();

        //请求参数
        String uuid = params.getFormStringOption(0);
        String nickname = params.getFormString(1);
        Integer useRenameCard = params.getFormIntegerOption(2);//php用户系统无此字段,此字段是为了兼容直播
        String smid = StringUtils.isBlank(params.getFormStringOption(3)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(3);
        if (Objects.equal(useRenameCard, 1)) {
            log.info("app修改昵称，使用改名卡, nickname:{}", nickname);
        }
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        accountsService.setNicknameByUuid(nickname, uuid, useRenameCard, appcode, smid);

        // 设置昵称成功
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("nickname", nickname);
        return JsonResult.success(resultMap);
    }

    /**
     * 获取用户每周修改昵称次数
     *
     * <AUTHOR>
     * @date 2020/02/14 14:10
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getAccountChangeNicknameNum")
    public JsonResult getAccountChangeNicknameNum(RequestParams params) {
    	String accountUuid = params.getFormString(0);
    	Long time = params.getFormLong(1);
    	Map<String, String> result = accountsService.getAccountChangeNicknameNum(accountUuid, time);
    	return JsonResult.success(result);
    }

	/**
     * 修改昵称
     *
     * <AUTHOR>
     * @date 2020/02/14 14:11
     * @param params
     * @return
     */
    @RequestMapping(params = "method=setNicknameV2")
    public JsonResult setNicknameV2(RequestParams params) {
        // 针对20大的资料修改限制
        LimitModificationUtil.isLimitModify();

    	//请求参数
    	String uuid = params.getFormStringOption(0);
    	String nickname = params.getFormString(1);
    	Integer useRenameCard = params.getFormIntegerOption(2);//php用户系统无此字段,此字段是为了兼容直播
    	if (Objects.equal(useRenameCard, 1)) {
    		log.info("app修改昵称，使用改名卡, nickname:{}", nickname);
    	}
        String smid = StringUtils.isBlank(params.getFormStringOption(3)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(3);
        log.info("修改昵称,accountUuid:{},nickname:{},useRenameCard:{},smid:{}", uuid, nickname, useRenameCard, smid);
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        accountsService.setNicknameV2(nickname, uuid, useRenameCard, appcode, smid);
        // 设置昵称成功
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("nickname", nickname);
    	return JsonResult.success(resultMap);
    }

    /**
     * 设置/更新用户昵称
     *
     * @param params(account_uuid,nickname)
     * @return
     * @throws IOException
     * @Title:setNicknameForBackstage
     * @author:huangyuehong
     * @Date:2016年2月17日 下午5:15:43
     */
    @RequestMapping(params = "method=setNicknameForBackstage")
    public JsonResult setNicknameForBackstage(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String nickname = params.getFormStringOption(1);
        String userId = params.getFormStringOption(2);

        //todo passVerify通过了以后调用这个方法
        boolean isViolation = AccountActionProfileService.isViolationAccountName(nickname);
        SetNicknameResult setNicknameResult = accountsService.setNicknameForBackstage(accountUuid, nickname, userId, isViolation);
        // 设置昵称成功
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("nickname", setNicknameResult.getNickname());
        return JsonResult.success(resultMap);
    }

    @RequestMapping(params = "method=setNicknameForPunish")
    public JsonResult setNicknameForPunish(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String nickname = params.getFormString(1);
        String userId = params.getFormStringOption(2);

        //todo passVerify通过了以后调用这个方法
        boolean isViolation = AccountActionProfileService.isViolationAccountName(nickname);
        SetNicknameResult setNicknameResult = accountsService.setNicknameForPunish(accountUuid, nickname, userId, isViolation);
        // 设置昵称成功
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("nickname", setNicknameResult.getNickname());
        return JsonResult.success(resultMap);
    }

    /**
     * 后台 - 根据uuid修改密码，新密码没传时默认为123456
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=modifyPasswordForBackstage")
    public JsonResult modifyPasswordForBackstage(RequestParams params) {
        String uuid = params.getFormString(0);
        String password = params.getFormStringOption(1);
        int flag = accountsService.modifyPasswordByUuid(uuid, password);
        if (flag == -1) {
            return JsonResult.failedCode(CodeStatus.MODIFY_PWD_FAILURE);
        } else {
            return JsonResult.success(CodeStatus.MODIFY_PWD_SUCCESS);
        }
    }

    /**
     * 后台 - 添加用户
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=addAccountForBackstage")
    public JsonResult addAccountForBackstage(RequestParams params) {
        String accountData = params.getFormString(0);
        String userId = params.getFormStringOption(1);//后台用户id
        AccountVo accountVo = JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(accountData), AccountVo.class);
        RegVo regVo = accountsService.addAccountForBackstage(accountVo, userId);
        return JsonResult.success(regVo);
    }

    /**
     * 后台 - 修改用户
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateAccountForBackstage")
    public JsonResult updateAccountForBackstage(RequestParams params) {
        String uuid = params.getFormString(0);
        String accountData = params.getFormString(1);
        String userId = params.getFormStringOption(2);//后台用户id
        AccountVo accountVo = JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(accountData), AccountVo.class);
        accountsService.updateAccountForBackstage(uuid, accountVo, userId);
        return JsonResult.success(true);
    }

    @RequestMapping(params = "method=webLogin")
    public JsonResult webLogin(RequestParams params) {
        String mobile = params.getFormString(0);
        String verifyCode = params.getFormString(1);
        Map<String, String> result = accountsService.webLogin(mobile, verifyCode);
        return JsonResult.success(result);
    }

    /**
     * 根据uuid获取用户最后登录系统版本
     *
     * <AUTHOR>
     * @date 2020/03/27 13:49
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getLastAppVersionByAccountUuids")
    public JsonResult getLastAppVersionByAccountUuid(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
    	JsonResult jsonResult = JsonResult.success();
        Map<String, Map<String, Object>> userInfo = accountsService.getLastAppVersionByAccountUuids(uuids);
    	jsonResult.setData(userInfo);
    	return jsonResult;
    }

    /**
     * 用户uuid是否存在（客户端接口 + 服务端接口）
     * 少量在用
     *
     * <AUTHOR>
     * @date 2020/06/05 17:26
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=isExistAccountUuid")
    public JsonResult isExistAccountUuid(RequestParams params) {
        //TODO 接口可下线直接抛异常，客户端处理
    	String uuid = params.getFormString(0);
    	JsonResult jsonResult = JsonResult.success();
    	Boolean exists = accountsService.isExistAccountUuid(uuid);
    	Map<String, String> existsMap = new HashMap<>();
        if (exists) {
            existsMap.put("exist_account_uuid", "1");
        } else {
            existsMap.put("exist_account_uuid", "0");
        }
        jsonResult.setData(existsMap);
    	return jsonResult;
    }

    /**
     * 用户手机号是否存在用户表和注销表
     *
     * <AUTHOR>
     * @date 2021/02/02 09:56
     * @param params
     * @return
     */
    @RequestMapping(params = "method=isExistMobileHistory")
    public JsonResult isExistMobileHistory(RequestParams params) {
        // 2023.08.10 增加cloned逻辑
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String mobile = params.getFormString(0);
        // 占位，暂时不用
        String appcodeStr = params.getFormStringOption(1);
        String clonedStr = params.getFormStringOption(2);
        // 有传cloned，则赋值到头参数cloned中
        if(StringUtils.isNotBlank(clonedStr)) {
            if(StringUtils.isNumeric(clonedStr)) {
                soaBaseParams.setCloned(clonedStr);
            }else {
                log.error("业务cloned传值错误，cloned={}", clonedStr);
            }
        }
        RequestParamsUtil.initDefaultAppcodeAndCloned(CommConst.APPCODE_TAQU, CommConst.CLONED_TAQU);

        JsonResult jsonResult = JsonResult.success();
        Map<String, String> existsMap = accountsService.isExistMobileHistory(mobile);
        jsonResult.setData(existsMap);
        return jsonResult;
    }

    /**
     * ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 其他系统接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
     **/

    /**
     * 少量调用
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getRegStyleAndMobileByUuid")
    public JsonResult getRegStyleAndMobileByUuid(RequestParams params){
        String accountUuid = params.getFormString(0);
        return JsonResult.success(accountsService.getRegStyleAndMobileByUuid(accountUuid));
    }

    @Api
    @RequestMapping(params = "method=loginByContinueKey")
    public JsonResult loginByContinueKey(RequestParams params){
        String ticket = params.getFormString(0);
        String mobile = params.getFormStringOption(1);
        String continueKey = params.getFormString(2);
        int versionNumber = params.getFormIntegerDefault(3, 0);
        String accessToken = params.getFormStringOption(4);
        String shumeiDeviceId = StringUtils.isBlank(params.getFormStringOption(5)) ? CommConst.OLD_CLIENT_PACKAGE : params.getFormStringOption(5);
        String extraJson = params.getFormStringDefault(6, "");
        extraJson = StringEscapeUtils.unescapeHtml4(extraJson);

        String token = RequestParams.getSoa_basic_java().getToken();
        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        Integer cloned = RequestParams.getSoa_basic_java().getCloned();
        LoginVo loginVo = accountsService.loginByContinueKey(mobile, continueKey, versionNumber, accessToken, shumeiDeviceId, extraJson);
        if(StringUtils.isNotBlank(shumeiDeviceId)) {
            loginVo.setShumeiDeviceId(shumeiDeviceId);
        }
        Map<String,Object> result =this.loginPostProcess(loginVo, versionNumber,RegisterLoginTypeEnum.CONTINUE_KEY);

        // 注册时的跳转登录，无ticket，不用强踢
        if (!"noTicketId".equals(ticket)) {
            accountsService.logout(ticket,token,platformId,appcode,cloned,false);
        }

        return JsonResult.success(result);
    }

    /**
     * 注销账号恢复
     * 只恢复到可用，不修复数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=destroyRenew")
    public JsonResult destroyRenew(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String operateName = params.getFormStringOption(1);
        String reason = accountsService.destroyRenew(accountUuid, operateName);
        JsonResult success = JsonResult.success();
        success.setMsg(reason);
        return success;
    }

    /**
     * 获取他趣id
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCardId")
    public JsonResult getCardId(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String cardId = accountsService.getCardId(accountUuid);
        return JsonResult.success(ImmutableMap.of("default_card_id", cardId));
    }

    /**
     * 获取注销状态的账号
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=listDestroy")
    public JsonResult listDestroy(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String targetUuids = params.getFormString(1);
        List<DestroyResp> resp = accountsService.listDestroy(Lists.newArrayList(targetUuids.split(",")));
        return JsonResult.success(ImmutableMap.of("list", resp));
    }

    /**
     * 配配注册限制
     */
    public static void limitPeipeiAccountReg() {
        Integer appcode = SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        // appcode = 1 & cloned = 50
        if (Objects.equal(CommConst.APPCODE_TAQU, appcode) && Objects.equal(ClonedEnum.PEIPEI.getCode(), cloned)) {
            throw new ServiceException(CodeStatus.PEIPEI_REG_LIMIT);
        }
    }
}
