package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.service.IdealTargetService;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/11/26 下午2:11
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value="/api", params="service=ideal")
public class IdealTargetController {

    private final IdealTargetService service;

    /**
     * 保存我的理想型
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=save")
    public JsonResult save(RequestParams params) {
        String uuid = params.getFormString(0);
        String content = params.getFormString(1);
        String smid = params.getFormStringDefault(2, CommConst.OLD_CLIENT_PACKAGE);
        service.save(uuid, content, smid);
        return JsonResult.success();
    }

    @Api
    @RequestMapping(params = "method=samples")
    public JsonResult samples(RequestParams params) {
        Integer sexType = params.getFormInteger(1);
        return JsonResult.success(ImmutableMap.of("list", service.samples(sexType)));
    }

    @Api
    @RequestMapping(params = "method=info")
    public JsonResult info(RequestParams params) {
        String uuid = params.getFormString(0);
        String targetUuid = params.getFormStringDefault(1, null);
        return StringUtils.isNotBlank(targetUuid) ?
            JsonResult.success(service.info(targetUuid, true)) :
            JsonResult.success(service.info(uuid, false));
    }
}
