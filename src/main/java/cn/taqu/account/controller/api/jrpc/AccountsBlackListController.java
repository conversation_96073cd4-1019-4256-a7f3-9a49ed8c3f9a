package cn.taqu.account.controller.api.jrpc;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.service.AccountsBlackListService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 用户黑名单相关的控制器
 *
 * <AUTHOR> 2017年8月8日 上午10:12:33
 */
@RestController
@RequestMapping(value = "/api", params = "service=blacklist")
public class AccountsBlackListController {

    @Autowired
    AccountsBlackListService accountsBlackListService;

    @RequestMapping(params = "method=afterBat")
    public JsonResult afterBat(RequestParams params) {
        String[] uuids = params.getFormStringArrayOption(0);
        String operator = params.getFormStringOption(1);
        if (StringUtils.isEmpty(operator)) {
            // 0为管理员
            operator = "0";
        }
        if (null != uuids && uuids.length > 0) {
            List<String> uuidLists = Arrays.asList(uuids);
            accountsBlackListService.afterBatUuids(uuidLists, operator);
        }
        return JsonResult.success(true);
    }

}
