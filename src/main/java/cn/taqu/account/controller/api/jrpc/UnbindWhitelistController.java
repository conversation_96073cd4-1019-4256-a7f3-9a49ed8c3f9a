package cn.taqu.account.controller.api.jrpc;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.taqu.account.dto.approve.ApprovalStateQueryDto;
import cn.taqu.account.dto.approve.ModifyApprovalDto;
import cn.taqu.account.dto.approve.ViewApprovalDto;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.model.UnbindWhitelist;
import cn.taqu.account.service.ToolsService;
import cn.taqu.account.service.UnbindWhitelistService;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(value="/api", params="service=unbindWhitelist")
public class UnbindWhitelistController {

    @Autowired
    private UnbindWhitelistService unbindWhitelistService;

    public static final String IDENTITY_NO_18="**********";

    public static final String  IDENTITY_NO_15="*******";

    @RequestMapping(params = "method=addUnbindWhitelist")
    @ResponseBody
    public JsonResult addUnbindWhitelist(RequestParams params) {
        String jsonString = params.getFormString(0);
        log.info("addUnbindWhitelist jsonStr:{}",jsonString);
        HashMap<String,Object> jsonObj=JsonUtils.stringToObject2(jsonString, HashMap.class);
        if(jsonObj==null){
            return JsonResult.failed("未检索到相关记录，请核实uuid");
        }
        String accountUuid= MapUtils.getString(jsonObj,"accountUuid");
        String identityNo= MapUtils.getString(jsonObj,"identityNo");
        String operateName= MapUtils.getString(jsonObj,"operateName");
//        String operateCode= MapUtils.getString(jsonObj,"operateCode");


        if(StringUtils.isBlank(accountUuid)){
            return JsonResult.failed("未检索到相关记录，请核实uuid");
        }
        if(StringUtils.isBlank(identityNo)){
            return JsonResult.failed("未检索到相关记录，请核实身份证号");
        }
        if( StringUtils.isBlank(operateName)){
            return JsonResult.failed("参数校验失败,操作人不存在");
        }
        // 可能有x结尾的，统一转大写
        identityNo = identityNo.toUpperCase();

        unbindWhitelistService.addUnbindWhitelist(accountUuid,identityNo,operateName);
        return JsonResult.success("success");
    }

    @RequestMapping(params = "method=feishuAddUnbindWhitelist")
    public JsonResult feishuAddUnbindWhitelist(RequestParams params) {
        String json = params.getFormString(0);
        ModifyApprovalDto dto = JsonUtils.stringToObject(json, ModifyApprovalDto.class);
        return JsonResult.success(
            ImmutableMap.of(
                "id",
                unbindWhitelistService.addUnbindWhitelistApprove(dto)
            )
        );
    }

    @RequestMapping(params = "method=feishuGetUnbindWhitelist")
    public JsonResult feishuGetUnbindWhitelist(RequestParams params) {
        String json = params.getFormString(0);
        ViewApprovalDto dto = JsonUtils.stringToObject(json, ViewApprovalDto.class);
        unbindWhitelistService.viewUnbindWhitelistApprove(dto);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=feishuGetUnbindWhitelistState")
    public JsonResult feishuGetUnbindWhitelistState(RequestParams params) {
        String json = params.getFormString(0);
        ApprovalStateQueryDto dto = JsonUtils.stringToObject(json, ApprovalStateQueryDto.class);
        return JsonResult.success(unbindWhitelistService.approvalState(dto));
    }

    @RequestMapping(params = "method=getUnbindWhitelist")
    @ResponseBody
    public JsonResult getUnbindWhitelist(RequestParams params) {
        String jsonString = params.getFormString(0);
        HashMap<String,Object> jsonObj=JsonUtils.stringToObject2(jsonString, HashMap.class);
        if(jsonObj==null){
            return JsonResult.failed("请输入uuid");
        }
        String accountUuid= MapUtils.getString(jsonObj,"accountUuid");
        if(StringUtils.isBlank(accountUuid)){
            return JsonResult.failed("请输入uuid");
        }
        List<UnbindWhitelist> list=unbindWhitelistService.getUnbindWhitelists(accountUuid);
        for(UnbindWhitelist temp:list){
            String identityNoTemp = "";
            if(ToolsService.unbindWhitelistSwitchEncryption.isOn(true)) {
                identityNoTemp=EncryptUtil.decrypt(temp.getIdentityNoCipher());
            }else {
                identityNoTemp=temp.getIdentityNo();
            }

            String identityNoTempStr=getIdentityNo(identityNoTemp);
            temp.setIdentityNo(identityNoTempStr);
            Integer cloned=temp.getChannel();
//            ClonedGroupEnum tempEnum=ClonedGroupEnum.getClonedGroup(cloned);
            if(ClonedGroupEnum.MAIN_GROUP.contains(cloned)){
                temp.setChannelName("他趣");
            }else if(ClonedGroupEnum.GIRL_GROUP.contains(cloned)){
                temp.setChannelName("女包");
            } else if (ClonedGroupEnum.YOUNG_GROUP.contains(cloned)){
                // 先写个闪糖，后续应该会换名字
                temp.setChannelName("闪糖");
            }
            temp.setOperateTime(temp.getCreateTime());
        }
        return JsonResult.success(list);
    }

    @RequestMapping(params = "method=getTransparentUnbindWhitelist")
    public JsonResult getTransparentUnbindWhitelist(RequestParams params) {
        String jsonString = params.getFormString(0);
        HashMap<String,Object> jsonObj = JsonUtils.stringToObject2(jsonString, HashMap.class);
        if (jsonObj == null) {
            return JsonResult.failed("请输入uuid");
        }
        String accountUuid = MapUtils.getString(jsonObj,"accountUuid");
        if (StringUtils.isBlank(accountUuid)) {
            return JsonResult.failed("请输入uuid");
        }
        List<UnbindWhitelist> list = unbindWhitelistService.getUnbindWhitelists(accountUuid);
        for (UnbindWhitelist row : list) {
            if (ToolsService.unbindWhitelistSwitchEncryption.isOn(true)) {
                row.setIdentityNo(EncryptUtil.decrypt(row.getIdentityNoCipher()));
            }
        }
        return JsonResult.success(list);
    }



    @RequestMapping(params = "method=checkWhitelist")
    @ResponseBody
    public JsonResult checkWhitelist(RequestParams params) {
        String uuid = params.getFormString(0);
        Long whitelistId = params.getFormLong(1);
        if(whitelistId==null || StringUtils.isBlank(uuid)){
            return JsonResult.failed("参数校验失败");
        }
        UnbindWhitelist unbindWhitelist=unbindWhitelistService.getUnbindWhitelistById(whitelistId);
        Map<String,Object> resultMap= Maps.newHashMap();
        Integer status=unbindWhitelistService.checkUnbindWhitelist(unbindWhitelist,uuid);
        if(status!=0 && status!=1){
            resultMap.put("status",status);
            return JsonResult.success(resultMap);
        }
        resultMap.put("status",unbindWhitelist.getStatus());
        resultMap.put("user_name",unbindWhitelist.getUserName());
        resultMap.put("uuid",unbindWhitelist.getAccountUuid());
        resultMap.put("old_uuid",unbindWhitelist.getOldUuid());
        resultMap.put("old_user_name",unbindWhitelist.getOldName());
        return JsonResult.success(resultMap);
    }


    private String getIdentityNo(String identityNo){
        if(StringUtils.isBlank(identityNo) || identityNo.length()<15){
            return "";
        }
        try{
            String start=identityNo.substring(0,4);
            int length=identityNo.length();
            String end=identityNo.substring(length-4,length);
            StringBuffer sb=new StringBuffer(start);
            if (identityNo.length() == 15){
                sb.append(IDENTITY_NO_15);
            }else if (identityNo.length() == 18){
                sb.append(IDENTITY_NO_18);
            }else {
                return "";
            }
            sb.append(end);
            String identityNoStr=sb.toString();
            if(identityNoStr.length()==15 || identityNoStr.length()==18){
                return identityNoStr;
            }
            return "";
        }catch (Exception e){
            log.warn("getIdentityNo fail identityNo:{}",identityNo,e);
        }
        return "";

    }
}
