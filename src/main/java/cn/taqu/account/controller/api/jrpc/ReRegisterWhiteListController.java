package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.model.ReRegisterWhitelist;
import cn.taqu.account.search.ReRegisterWhiteListSearch;
import cn.taqu.account.service.ReRegisterWhiteListService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;


/**
 * 再注册白名单控制器
 *
 * <AUTHOR>
 * @date 2021/07/21
 */
@RestController
@RequestMapping(value="/api", params="service=reRegisterWhitelist")
public class ReRegisterWhiteListController {

    @Autowired
    private ReRegisterWhiteListService reRegisterWhiteListService;

    @RequestMapping(params = "method=add")
    @ResponseBody
    public JsonResult addRegisterWhiteList(RequestParams params) {
        String jsonString = params.getFormString(0);
        ReRegisterWhitelist reRegisterWhitelist = JsonUtils.stringToObject2(jsonString, ReRegisterWhitelist.class);
        reRegisterWhiteListService.add(reRegisterWhitelist);
        return JsonResult.success("添加成功");
    }

    @RequestMapping(params = "method=dataGrid")
    @ResponseBody
    public JsonResult dataGrid(RequestParams params) {
        String jsonString = params.getFormString(0);
        ReRegisterWhiteListSearch search = JsonUtils.stringToObject2(jsonString, ReRegisterWhiteListSearch.class);
        Page<ReRegisterWhitelist> reRegisterWhitelists = reRegisterWhiteListService.pageQuery(search);
        return JsonResult.success(reRegisterWhitelists);
    }

}
