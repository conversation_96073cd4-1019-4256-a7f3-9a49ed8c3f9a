package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.model.AccountsCardLog;
import cn.taqu.account.search.AccountsCardLogSearch;
import cn.taqu.account.service.AccountsCardExpireService;
import cn.taqu.account.service.AccountsCardLogService;
import cn.taqu.account.service.AccountsCardService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api", params = "service=accountsCard")
public class  AccountsCardController {

    @Autowired
    private AccountsCardService accountsCardService;
    @Autowired
    private AccountsCardExpireService accountsCardExpireService;
    @Autowired
    private AccountsCardLogService accountsCardLogService;

    /**
     * 获取可使用的靓号列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cardList")
    public JsonResult cardList(RequestParams params) {
        String uuid = params.getFormStringOption(0);
        int page = params.getFormIntegerDefault(1, 1);
        int limit = params.getFormIntegerDefault(2, 10);
        List<Map<String, String>> result = accountsCardService.getByUuid(uuid, page, limit);
        return JsonResult.success(result);
    }

    /**
     * 获取过期的靓号列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=expireCardList")
    public JsonResult expireCardList(RequestParams params) {
        String uuid = params.getFormStringOption(0);
        int page = params.getFormIntegerDefault(1, 1);
        int limit = params.getFormIntegerDefault(2, 10);
        List<Map<String, String>> result = accountsCardExpireService.getByUuid(uuid, page, limit);
        return JsonResult.success(result);
    }

    /**
     * 获取相应靓号的账号uuid(只能获取普通号码和使用中的靓号的uuid)
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCardUuid")
    public JsonResult getCardUuid(RequestParams params) {
        Long cardId = params.getFormLong(0);
        String uuid = accountsCardService.getUuid(cardId);
        JsonResult jsonResult = JsonResult.success();
        jsonResult.setData(uuid);
        return jsonResult;
    }

    /**
     * 获取相应靓号的账号uuid
     * desc 给客户端
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getUuid")
    public JsonResult getUuid(RequestParams params) {
        String ticketId = params.getFormString(0);
        Long cardId = params.getFormLong(1);
        String uuid = accountsCardService.getUuid(cardId);
        JsonResult jsonResult = JsonResult.success();
        jsonResult.setData(ImmutableMap.of("uuid", uuid));
        return jsonResult;
    }

    /**
     * 使用靓号
     * @param params
     * @return
     */
    @RequestMapping(params = "method=useCard")
    public JsonResult useCard(RequestParams params) {
        String uuid = params.getFormStringOption(0);
        Long cardId = params.getFormLong(1);
        accountsCardService.useCard(uuid, cardId);
        return JsonResult.success();
    }

    /**
     * 转赠靓号
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=giveCard")
    public JsonResult giveCard(RequestParams params) {
        String fromUuid = params.getFormStringOption(0);
        String toUuid = params.getFormString(1);
        Long cardId = params.getFormLong(2);
        accountsCardService.giveCard(fromUuid, toUuid, cardId);
        return JsonResult.success();
    }

    /**
     * 发放靓号
     * @param params
     * @return
     */
    @RequestMapping(params = "method=grantCard")
    public JsonResult grantCard(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long cardId = params.getFormLong(1);
        String cardLevel = params.getFormString(2);
        Long endTime = params.getFormLong(3);
        String operateName = params.getFormString(4);
        accountsCardService.grantCard(accountUuid, cardId, cardLevel, endTime, operateName);
        return JsonResult.success();
    }

    /**
     * 回收靓号
     * @param params
     * @return
     */
    @RequestMapping(params = "method=revokeCard")
    public JsonResult revokeCard(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long cardId = params.getFormLong(1);
        String operateName = params.getFormString(2);
        accountsCardService.revokeCard(accountUuid, cardId, operateName);
        return JsonResult.success();
    }

    /**
     * 批量获取号码信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=mGetAccountCardInfo")
    public JsonResult mGetAccountCardInfo(RequestParams params) {
        Long[] cardIds = params.getFormLongArray(0);
        Map<Long, Map<String, Object>> result = accountsCardService.mGetAccountCardInfo(cardIds);
        return JsonResult.success(result);
    }

    /**
     * 查看靓号历史使用用户
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCardUsedHistory")
    public JsonResult getCardUsedHistory(RequestParams params) {
        Long cardId = params.getFormLong(0);
        Integer pageNumber = params.getFormIntegerDefault(1, 1);
        Integer pageSize = params.getFormIntegerDefault(2, 20);
        Page<Map<String, Object>>  result = accountsCardLogService.getCardUsedHistory(cardId, pageNumber, pageSize);
        return JsonResult.success(result);
    }

    /**
     * 查看靓号历史使用用户
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getAccountCardLog")
    public JsonResult getAccountCardLog(RequestParams params) {
        String queryParms = params.getFormStringOption(0);
        Integer pageNumber = params.getFormIntegerDefault(1, 1);
        Integer pageSize = params.getFormIntegerDefault(2, 20);
        AccountsCardLogSearch search = JsonUtils.stringToObject2(JsonUtils.toEmptyJsonObject(queryParms), AccountsCardLogSearch.class);
        Page<AccountsCardLog> result = accountsCardLogService.getAccountCardLog(search, pageNumber, pageSize);
        return JsonResult.success(result);
    }

    /**
     * 批量发放普通靓号
     * @param params
     * @return
     */
    @RequestMapping(params = "method=mGrantNormalCard")
    public JsonResult mGrantNormalCard(RequestParams params) {
        String accountCardData = params.getFormString(0);
        accountsCardService.mGrantNormalCard(JsonUtils.stringToObject2(JsonUtils.toEmptyJsonObject(accountCardData), new TypeReference<Map<String, Long>>() {}));
        return JsonResult.success();
    }
    
    @Deprecated
    @RequestMapping(params = "method=checkCardGrant")
    public JsonResult checkCardGrant(RequestParams params) {
        Long[] cardIds = params.getFormLongArray(0);
        return JsonResult.success(accountsCardService.checkCardGrant(Arrays.asList(cardIds)));
    }
}
