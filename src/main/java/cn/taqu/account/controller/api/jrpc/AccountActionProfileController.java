package cn.taqu.account.controller.api.jrpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.common.AccountActionTypeEnum;
import cn.taqu.account.dto.AccountAction;
import cn.taqu.account.service.AccountActionProfileService;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

@RestController
@RequestMapping(value = "/api", params = "service=accountActionProfile")
public class AccountActionProfileController {
    @Autowired
    private AccountActionProfileService accountActionProfileService;

    /**
     * 获取账户行为信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getAccountAction")
    public JsonResult getAccountActionByAccountUuid(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String typeStr = params.getFormString(1);
        AccountActionTypeEnum type;
        if (AccountActionTypeEnum.LOGIN.toString().equals(typeStr)) {
            type = AccountActionTypeEnum.LOGIN;
        } else if (AccountActionTypeEnum.REGISTER.toString().equals(typeStr)){
            type = AccountActionTypeEnum.REGISTER;
        } else {
            throw new ServiceException("行为类型只能为登录或注册");
        }
        AccountAction accountAction = accountActionProfileService.getAccountActionByAccountUuid(accountUuid, type);

        JsonResult jsonResult = JsonResult.success();
        jsonResult.setData(accountAction);

        return jsonResult;
    }

    /**
     * 获取账户信息
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getAccountInfo")
    public JsonResult getAccountInfo(RequestParams params) {
        String accountUuid = params.getFormString(0);

        AccountAction accountAction = accountActionProfileService.getAccountInfo(accountUuid);

        JsonResult jsonResult = JsonResult.success();
        jsonResult.setData(accountAction);

        return jsonResult;
    }

}
