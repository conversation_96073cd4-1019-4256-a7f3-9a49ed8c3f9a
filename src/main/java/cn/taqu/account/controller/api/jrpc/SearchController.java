package cn.taqu.account.controller.api.jrpc;

import java.util.Arrays;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.taqu.account.service.AccountsService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 查询相关Controller
 * <AUTHOR>
 * 2017年7月5日 下午2:54:21
 */
@RestController
@RequestMapping(value="/api", params="service=search")
public class SearchController {
	
	@Autowired
	private AccountsService accountsService;
	
	/**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 客户端接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/
	
	

	/**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 客户端接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

	/**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 其他系统接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

	@RequestMapping(params = "method=getAccountsInfoByUuidsForAppSearch")
	public JsonResult getAccountsInfoByUuidsForAppSearch(RequestParams params) {
		String[] accountUuids = params.getFormStringArray(0);
		JsonResult jsonResult = JsonResult.success(accountsService.getAccountsInfoByUuidsForAppSearch(Arrays.asList(accountUuids)));
		Map<String, String> map = Maps.newHashMap();
		map.put("avatar_host", accountsService.getAvatarUrlPrefix());
		jsonResult.setExtra(map);
		return jsonResult;
	}

	/**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 其他系统接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/
	
}