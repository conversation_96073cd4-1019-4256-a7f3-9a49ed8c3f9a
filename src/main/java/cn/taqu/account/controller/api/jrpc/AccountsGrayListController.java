package cn.taqu.account.controller.api.jrpc;

import java.util.Map;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.account.model.AccountsGrayList;
import cn.taqu.account.service.AccountsGrayListService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 用户灰名单（权限受限）
 * 
 * <AUTHOR>
 * @date 2019/09/12 11:09
 */
@RestController
@RequestMapping(value = "/api", params = "service=accountsGrayList")
public class AccountsGrayListController {

    @Autowired
    AccountsGrayListService accountsGrayListService;

    /**
     * 添加用户灰名单
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params) {
        String uuidOrToken = params.getFormString(0);
        String remark = params.getFormString(1);
        String operateName = params.getFormString(2);
        accountsGrayListService.add(uuidOrToken, remark, operateName);
        return JsonResult.success(true);
    }
    
    /**
     * 添加用户灰名单(批量)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=batchAdd")
    public JsonResult batchAdd(RequestParams params) {
    	String[] uuidOrTokens = params.getFormStringArray(0);
    	String remark = params.getFormString(1);
    	String operateName = params.getFormString(2);
    	accountsGrayListService.batchAdd(uuidOrTokens, remark, operateName);
    	return JsonResult.success(true);
    }

    /**
     * 移除用户灰名单
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=remove")
    public JsonResult remove(RequestParams params) {
        String uuidOrTokens = params.getFormString(0);
        accountsGrayListService.remove(uuidOrTokens);
        return JsonResult.success(true);
    }

    /**
     * 根据id移除用户灰名单
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=removeById")
    public JsonResult removeById(RequestParams params) {
        Long id = params.getFormLong(0);
        accountsGrayListService.removeById(id);
        return JsonResult.success(true);
    }

    /**
     * 用户灰名单查询
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getAccountsGrayList")
    public JsonResult getAccountsGrayList(RequestParams params) {
        String param = params.getFormStringOption(0);
        Integer pageNumber = params.getFormIntegerOption(1);
        Integer pageSize = params.getFormIntegerOption(2);
        Page<AccountsGrayList> page = accountsGrayListService.getAccountsGrayList(
                JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(param), new TypeReference<Map<String, Object>>() {
                }), pageNumber, pageSize);
        return JsonResult.success(page);
    }

    /**
     * 用户是否在灰名单中
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=isInGrayList")
    public JsonResult isInGrayList(RequestParams params) {
        String token = params.getFormStringOption(0);
        String accountUuid = params.getFormStringOption(1);
        boolean isIn = accountsGrayListService.isInGrayList(token, accountUuid);
        return JsonResult.success(isIn);
    }
    
    /**
     * 用户是否在灰名单中
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=accountIsInGrayListBatch")
    public JsonResult accountIsInGrayListBatch(RequestParams params) {
        String[] uuidArr = params.getFormStringArray(0);
        Map<String, Boolean> result = accountsGrayListService.accountIsInGrayListBatch(uuidArr);
        return JsonResult.success(result);
    }
    
    /**
     * 返回详情
     * 
     * <AUTHOR>
     * @date 2020/03/20 14:17
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getDetail")
    public JsonResult getDetail(RequestParams params) {
    	String token = params.getFormStringOption(0);
    	String accountUuid = params.getFormStringOption(1);
    	AccountsGrayList accountsGrayList = accountsGrayListService.getDetail(token, accountUuid);
    	return JsonResult.success(accountsGrayList);
    }

    /**
     * 用户是否在灰名单中、给客户端使用
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=isInAccountGrayList")
    public JsonResult isInAccountGrayList(RequestParams params) {
        String accountUuid = params.getFormStringOption(1);
        String token = params.getFormStringOption(0);
        boolean isIn = accountsGrayListService.isInGrayList(token, accountUuid);
        Integer result = isIn ? 1:0;
        Map<String,Object> map = Maps.newHashMap();
        map.put("is_gray",result);
        return JsonResult.success(map);
    }

}
