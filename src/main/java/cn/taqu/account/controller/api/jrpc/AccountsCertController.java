package cn.taqu.account.controller.api.jrpc;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.common.PlatformEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 15:29
 */

@RestController
@RequestMapping(value = "/api", params = "service=accountsCert")
public class AccountsCertController {

    /**
     * 获取取消认证提示
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCancelTips")
    public JsonResult getCancelTips(RequestParams params){
        if(Objects.equals(SoaBaseParams.fromThread().getPlatform_id(), PlatformEnum.ANDROID.getValue())){
            throw new ServiceException(CodeStatus.CANCEL_CERT_CLOSE);
        }
//        String accountUuid = params.getFormString(0);
//        Map<String, String> result = accountsCertService.getCancelTips(accountUuid);
        Map<String, String> result = new HashMap<>();
        result.put("real_person_cancel_tip", "解除真人认证功能暂时无法使用，有真人认证更容易找到心仪的Ta哟～");
        result.put("zhima_cancel_tip", "解除实名认证功能暂时无法使用，有实名认证更容易找到心仪的Ta哟～");
        return JsonResult.success(result);
    }

    /**
     * 取消我对认证
     * 网关接口已下线 2024.05.17
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=cancelMyCert")
    public JsonResult cancelMyCert(RequestParams params) {
        return JsonResult.success();
//        String accountUuid = params.getFormString(0);
//        Integer cancelType = params.getFormInteger(1);
//        Map<String, String> result = accountsCertService.userCancelCert(accountUuid, cancelType);
//        return JsonResult.success(result);
    }

}
