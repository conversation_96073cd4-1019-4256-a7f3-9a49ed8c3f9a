package cn.taqu.account.controller.api.jrpc;

import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.model.LikeLabel;
import cn.taqu.account.search.LikeLabelSearch;
import cn.taqu.account.service.AccountsLikeLabelService;
import cn.taqu.account.service.LikeLabelService;
import cn.taqu.account.vo.CommonPage;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 喜欢的类型
 * 
 * <AUTHOR>
 * @date 2020/05/13 14:53
 */
@RestController
@RequestMapping(value = "/api", params = "service=likeLabel")
public class AccountsLikeLabelController {
    @Autowired
    private AccountsLikeLabelService accountsLikeLabelService;
    @Autowired
    private LikeLabelService likeLabelService;

    /**
	 * 分页查询列表
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getListDataForPage")
	public JsonResult getListDataForPage(RequestParams params) {
		String queryParams = params.getFormString(0);
		Integer pageNumber = params.getFormIntegerDefault(1,1);
		Integer pageSize = params.getFormIntegerDefault(2,20);
		CommonPage<LikeLabel> page = likeLabelService.pageQuery(JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(queryParams), LikeLabelSearch.class), pageNumber, pageSize);
		JsonResult success = JsonResult.success();
		success.setData(page);
		return success;
	}

	/**
	 * 创建
	 * @return
	 */
	@RequestMapping(params = "method=create")
	public JsonResult create(RequestParams params) {
		String json = params.getFormString(0);
		return JsonResult.success(
			ImmutableMap.of(
				"id",
				likeLabelService.create(JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(json), LikeLabel.class))
			)

		);
	}

	/**
	 * 通过id从数据库中获取信息
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=getById")
	public JsonResult getByIdFromDb(RequestParams params) {
		Long id = params.getFormLong(0);
		LikeLabel likeLabel = likeLabelService.getById(id);
		return JsonResult.success(likeLabel);
	}

	/**
	 * 修改
	 * @return
	 */
	@RequestMapping(params = "method=update")
	public JsonResult update(RequestParams params) {
		String json = params.getFormString(0);
		likeLabelService.update(JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(json), LikeLabel.class));
		return JsonResult.success();
	}

	/**
	 * 删除
	 * <AUTHOR>
	 * @date 2020/05/11 16:11
	 * @param params
	 * @return
	 */
	@RequestMapping(params = "method=deleteById")
	public JsonResult deleteById(RequestParams params) {
		Long id = params.getFormLong(0);
		likeLabelService.deleteById(id);
		return JsonResult.success();
	}
	
	// 查询 用户自己(所有)
    @RequestMapping(params = "method=listAccountsLikeLabelAll")
    public JsonResult listAccountsLikeLabelAll(RequestParams requestParams) {
    	String accountUuid = requestParams.getFormString(0);
    	Map<String, List<Map<String, String>>> map = accountsLikeLabelService.listAccountsLikeLabelAll(accountUuid);
    	return JsonResult.success(map);
    }
    
    // 查询 用户自己(所有)
    @RequestMapping(params = "method=listAccountsLikeLabelAllBatch")
    public JsonResult listAccountsLikeLabelAllBatch(RequestParams requestParams) {
    	String[] accountUuids = requestParams.getFormStringArray(0);
    	Map<String,Map<String, List<Map<String, String>>>> map = accountsLikeLabelService.listAccountsLikeLabelAllBatch(accountUuids);
    	return JsonResult.success(map);
    }
    
    
    // 数据预热用
	@RequestMapping(params = "method=uploadJson")
	public JsonResult uploadJson(RequestParams params) {
		String json = params.getFormString(0);
		likeLabelService.uploadJson(json);
		return JsonResult.success();
	}
	
	// 数据预热用
	@RequestMapping(params = "method=uploadJsonStar")
	public JsonResult uploadJsonStar(RequestParams params) {
		String json = params.getFormString(0);
		likeLabelService.uploadJsonStar(json);
		return JsonResult.success();
	}
	
	// 数据预热用
	@RequestMapping(params = "method=uploadJsonGame")
	public JsonResult uploadJsonGame(RequestParams params) {
		String json = params.getFormString(0);
		likeLabelService.uploadJsonGame(json);
		return JsonResult.success();
	}

    /**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓客户端API START↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓*/
    
    // 搜索 分页
    @RequestMapping(params = "method=listLikeLabelPage")
    public JsonResult listLikeLabelPage(RequestParams requestParams) {
    	Integer type = requestParams.getFormInteger(0);
    	Integer page = requestParams.getFormInteger(1);
    	String keyWord = requestParams.getFormString(2);
    	String accountUuid = requestParams.getFormStringOption(3);
    	List<Map<String, String>> list = accountsLikeLabelService.listLikeLabelPage(type, page, keyWord, accountUuid);
    	return JsonResult.success(list);
    }
    
    // 查询 用户自己
    @RequestMapping(params = "method=listAccountsLikeLabel")
    public JsonResult listLikeLabel(RequestParams requestParams) {
    	String accountUuid = requestParams.getFormString(0);
    	Integer type = requestParams.getFormInteger(1);
    	List<Map<String, String>> list = accountsLikeLabelService.listAccountsLikeLabel(accountUuid, type);
    	return JsonResult.success(list);
    }
    
    // 添加 单个添加
    @RequestMapping(params = "method=setOne")
    public JsonResult setOne(RequestParams requestParams) {
    	String accountUuid = requestParams.getFormString(0);
    	Integer type = requestParams.getFormInteger(1);
    	Long id = requestParams.getFormLong(2);
    	accountsLikeLabelService.setOne(accountUuid, type, id);
    	return JsonResult.success();
    }
    
    // 删除
    @RequestMapping(params = "method=batchDel")
    public JsonResult batchDel(RequestParams requestParams) {
    	String accountUuid = requestParams.getFormString(0);
    	Integer type = requestParams.getFormInteger(1);
    	String ids = requestParams.getFormString(2);
    	accountsLikeLabelService.batchDel(accountUuid, type, ids);
    	return JsonResult.success();
    }
    
    // 置顶
    @RequestMapping(params = "method=batchStick")
    public JsonResult batchStick(RequestParams requestParams) {
    	String accountUuid = requestParams.getFormString(0);
    	Integer type = requestParams.getFormInteger(1);
    	String ids = requestParams.getFormString(2);
    	accountsLikeLabelService.batchStick(accountUuid, type, ids);
    	return JsonResult.success();
    }
    
    
    
    /**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑客户端API END↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑*/
}
