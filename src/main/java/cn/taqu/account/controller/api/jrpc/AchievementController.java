package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.service.AccountsAchievementService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RequestMapping(value = "/api", params = "service=achievement")
@RestController
public class AchievementController {
    @Autowired
    private AccountsAchievementService accountsAchievementService;

    /**
     * 用户获取成就勋章后，给用户添加成就勋章
     * @param params
     * @return
     */
    @RequestMapping(params = "method=addAchievement")
    public JsonResult addAchievement(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long achievementId = params.getFormLong(1);
        Integer level = params.getFormInteger(2);
        accountsAchievementService.addAcheivement(accountUuid, achievementId, level);
        return JsonResult.success();
    }

    /**
     * 回收用户成就勋章
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cancelAchievement")
    public JsonResult cancelAchievement(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long achievementId = params.getFormLong(1);
        Integer level = params.getFormInteger(2);
        accountsAchievementService.cancelAchievement(accountUuid, achievementId, level);
        return JsonResult.success();
    }

    /**
     * 佩戴成就勋章
     * @param params
     * @return
     */
    @RequestMapping(params = "method=adornAchievement")
    public JsonResult adornAchievement(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String achievementStr = params.getFormString(1);
        List<String> achievementList = JsonUtils.stringToObject(achievementStr, new TypeReference<List<String>>() {});
        accountsAchievementService.adornAchievement(accountUuid, achievementList);
        return JsonResult.success();
    }

    /**
     * 卸下用户成就勋章
     * @param params
     * @return
     * @return
     */
    @RequestMapping(params = "method=unadornAchievement")
    public JsonResult unadornAchievement(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Long achievementId = params.getFormLong(1);
        Integer level = params.getFormInteger(2);
        accountsAchievementService.unadornAchievement(accountUuid, achievementId, level);
        return JsonResult.success();
    }

    /**
     * 获取用户的全部成就勋章
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getAllAchievement")
    public JsonResult getAllAchievement(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Map<String, Object> result = accountsAchievementService.getAllAchievement(accountUuid);
        return JsonResult.success(result);
    }
}
