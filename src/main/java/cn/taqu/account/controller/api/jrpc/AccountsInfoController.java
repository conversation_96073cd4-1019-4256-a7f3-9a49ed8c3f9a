package cn.taqu.account.controller.api.jrpc;

import java.util.HashMap;
import java.util.Map;

import cn.taqu.account.manager.AccountBaseInfoManager;
import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.AuthInfoDto;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.service.AccountsPersonalInfoService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;

@RestController
@RequestMapping(value = "/api", params = "service=accountsInfo")
public class AccountsInfoController {
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountBaseInfoManager accountBaseInfoManager;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoServicee;

    @RequestMapping(params = "method=getAuthInfo")
    public JsonResult getAuthInfo(RequestParams params) {
        String accountUuid = params.getFormString(0);
        AuthInfoDto authInfo = accountsInfoService.getAuthInfo(accountUuid);

        return JsonResult.success(authInfo);
    }

    /**
     * 根据uuid更新生日
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateAccountsBirthByUuid")
    public JsonResult updateAccountsBirthByUuid(RequestParams params){
        String uuid = params.getFormStringOption(0);
        String birth = params.getFormStringOption(1);
        Map<String, Object> accountInfo = new HashMap<>();
        accountInfo.put("birth", birth);
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        return JsonResult.success(accountsPersonalInfoServicee.setAccountInfoByUuid(uuid, accountInfo, appcode, CommConst.OLD_CLIENT_PACKAGE));
    }

    /**
     * 根据uuid更新个人简介
     * 单个接口已无调用
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=updateAccountspersonalProfileByUuid")
    public JsonResult updateAccountspersonalProfileByUuid(RequestParams params){
        String uuid = params.getFormStringOption(0);
        String personal_profile = params.getFormStringOption(1);
        Map<String, Object> accountInfo = new HashMap<>();
        accountInfo.put("personal_profile", personal_profile);
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        return JsonResult.success(accountsPersonalInfoServicee.setAccountInfoByUuid(uuid, accountInfo, appcode, CommConst.OLD_CLIENT_PACKAGE));
    }

    /**
     * 根据uuid重置个人简介（个性签名）
     * 需求文档：https://o15vj1m4ie.feishu.cn/wiki/WmQ3wRH8WiELcgkwPoNceCCCn6g
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=resetAccountsPersonalProfileByUuid")
    public JsonResult resetAccountsPersonalProfileByUuid(RequestParams params){
        String uuid = params.getFormStringOption(0);
        accountsPersonalInfoServicee.resetAccountsPersonalProfileByUuid(uuid, CommConst.OLD_CLIENT_PACKAGE);
        return JsonResult.success();
    }

     /**
     * 个人邀请状态信息
     * bbs 暂时无调用， 功能是：邀请完善资料任务
     * 后期需要产品确认是否下线
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getAccountInviteInfosByUuid")
    public JsonResult getAccountInviteInfosByUuid(RequestParams params){
        String uuid = params.getFormStringOption(0);
        return JsonResult.success(accountsPersonalInfoServicee.getAccountInviteInfosByUuid(uuid));
    }

    /**
     * 获取个人简介
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getPersonalProfile")
    public JsonResult getPersonalProfile(RequestParams params){
        String uuid = params.getFormStringOption(0);
        Map<String, String> result = accountsInfoService.getPersonalProfile(uuid);
        return JsonResult.success(result);
    }

    /**
     * 更新用户婚恋状态
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateAffectivestatus")
    public JsonResult updateAffectivestatus(RequestParams params){
        String uuid = params.getFormStringOption(0);
        String affectivestatus = params.getFormStringOption(1);
        Map<String, Object> accountInfo = new HashMap<>();
        accountInfo.put("affectivestatus", affectivestatus);
        Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
        return JsonResult.success(accountsPersonalInfoServicee.setAccountInfoByUuid(uuid, accountInfo, appcode, CommConst.OLD_CLIENT_PACKAGE));
    }

    /**
     * 获取真实年龄
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getRealAge")
    public JsonResult getRealAge(RequestParams params){
        String uuid = params.getFormStringOption(0);
        Integer age = accountBaseInfoManager.getRealAge(uuid);
        return JsonResult.success(ImmutableMap.of("age", age));
    }
}
