package cn.taqu.account.controller.api.jrpc;

import cn.hutool.core.util.RandomUtil;
import cn.taqu.account.common.BanOpenIdTypeEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.service.MobileRetrieveService;
import cn.taqu.account.service.MobileService;
import cn.taqu.account.service.RiskService;
import cn.taqu.account.utils.ShowDialogUtil;
import cn.taqu.account.vo.RetrieveAccountStatusVO;
import cn.taqu.account.vo.RetrieveMobileInfoVO;
import cn.taqu.account.vo.RetrieveRiskUrlVO;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName:MobileController.java
 * @Description:用户手机控制器
 * @author:huangyuehong
 * @date:2015年9月16日 下午3:30:51
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=mobile")
public class MobileController {

    @Autowired
    private MobileService mobileService;
    @Autowired
    private MobileRetrieveService mobileRetrieveService;
    @Autowired
    private RedisTemplate accountStringRedisTemplate;
    @Autowired
    private RiskService riskService;

    /**
     * 根据uuid批量查询手机号
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getMobileByUuids")
    public JsonResult getMobileByUuids(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        Map<String, String> accountMobileMap = mobileService.getMobileByUuids(uuids);
        return JsonResult.success(accountMobileMap);
    }

    /**
     * 手机号校验【用于手机找回场景】
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=validMobileForRetrieve")
    public JsonResult validMobileForRetrieve(RequestParams params) {
        String mobile = params.getFormString(0);
        log.info("validMobileForRetrieve：{}", mobile);
        RetrieveRiskUrlVO result = mobileRetrieveService.validMobileForRetrieve(mobile);
        return JsonResult.success(result);
    }

    /**
     * 图形校验是否通过【用于手机找回场景】
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=validPictureResultForRetrieve")
    public JsonResult validPictureResultForRetrieve(RequestParams params) {
        String mobile = params.getFormString(0);
        log.info("validPictureResultForRetrieve：{}", mobile);
        RetrieveMobileInfoVO result = mobileRetrieveService.validPictureResultForRetrieve(mobile);
        return JsonResult.success(result);
    }

    /**
     * 检查账号状态
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=validAccountStatus")
    public JsonResult validAccountStatus(RequestParams params) {
        String mobile = params.getFormString(0);
        String uuid = params.getFormString(1);
        log.info("validPictureResultForRetrieve：{}", mobile);
        RetrieveAccountStatusVO result = mobileRetrieveService.validAccountStatus(mobile, uuid);
        return JsonResult.success(result);
    }

    /**
     * 校验验证码，确认手机号更棒【用于手机找回场景】
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=verifyCodeToBindMobileForRetrieve")
    public JsonResult verifyCodeToBindMobileForRetrieve(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String mobile = params.getFormString(1);
        String verify = params.getFormString(2);
        String shumeiDeviceId = params.getFormString(3);
        String oldMobile = params.getFormString(4);
        log.info("verifyCodeToBindMobileForRetrieve：{}, {}, {}, {}, {}", accountUuid, mobile, verify, shumeiDeviceId, oldMobile);
        mobileService.verifyCodeToBindMobileForRetrieve(accountUuid, mobile, verify, shumeiDeviceId, oldMobile);
        return JsonResult.success();
    }

    /**
     * 修改手机号
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=modifyMobile")
    public JsonResult modifyMobile(RequestParams params) {
        String ticketId = params.getFormStringOption(0);
        String password = params.getFormString(1);
        String mobile = params.getFormString(2);
        String verify = params.getFormString(3);
        String deviceToken = params.getFormStringOption(4);
        String shumeiDeviceId = params.getFormStringOption(5);
        mobileService.modifyMobile(ticketId, password, mobile, verify, deviceToken,shumeiDeviceId);
        return JsonResult.success();
    }

    /**
     * 绑定手机号
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=bindMobile")
    public JsonResult bindMobile(RequestParams params) {
        String accountUuid = params.getFormStringOption(0);
        String mobile = params.getFormString(1);
        String verify = params.getFormString(2);
        String deviceToken = params.getFormStringOption(3);
        String shumeiDeviceId = params.getFormStringOption(4);
        String continueLogin = params.getFormStringOption(5);

        if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Mobile.getValue(), mobile)){
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }

        String rewardLoginType = mobileService.bindMobile(accountUuid, mobile, verify, deviceToken,shumeiDeviceId,continueLogin);
        if(MobileService.THIRD_REWARD_LOGIN.equals(rewardLoginType)){
            Map<String,Object> result = Maps.newHashMap();
            result.put("mobile", mobile);
            result.put("random_str", accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile)));
            result.put("continue_login_type",rewardLoginType);

            return JsonResult.success(result);
        }

        return JsonResult.success();
    }

    /**
     * 闪验绑定手机号
     *
     * @param params
     * @return
     */
    @Api
    @RequestMapping(params = "method=quickBindMobile")
    public JsonResult quickBindMobile(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String accessToken = params.getFormString(1);
        String shumeiDeviceId = params.getFormStringOption(2);
        String continueLogin = params.getFormStringOption(3);

        Map<String,Object> result = mobileService.quickBindMobile(accountUuid, accessToken, shumeiDeviceId, continueLogin);
        return JsonResult.success(result);
    }

    @RequestMapping(params = "method=verifyBindMobile")
    public JsonResult verifyBindMobile(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String verify = params.getFormString(1);

        mobileService.verifyBindMobile(accountUuid, verify);

        Map<String, String> map = new HashMap<>();
        String randomCode = RandomUtil.randomString(8);
        String redisKey = RedisKeyConstant.ACCOUNT_MODIFY_MOBILE_RANDOM.setArg(accountUuid);
        accountStringRedisTemplate.opsForValue().set(redisKey, randomCode, 30, TimeUnit.MINUTES);
        map.put("randomCode", randomCode);

        return JsonResult.success(map);
    }

    @Api
    @RequestMapping(params = "method=modifyMobileV2")
    public JsonResult modifyMobileV2(RequestParams params) {
        String accountUuid = params.getFormString(0);
        String mobile = params.getFormString(1);
        String verify = params.getFormString(2);
        String randomCode = params.getFormString(3);
        String deviceToken = params.getFormStringOption(4);
        String shumeiDeviceId = params.getFormStringOption(5);

        String redisKey = RedisKeyConstant.ACCOUNT_MODIFY_MOBILE_RANDOM.setArg(accountUuid);

        Object cacheRandomCode = accountStringRedisTemplate.opsForValue().get(redisKey);
        if (cacheRandomCode == null || !String.valueOf(cacheRandomCode).equals(randomCode)) {
            throw new ServiceException(CodeStatus.CHANGE_MOBILE_VERIFY_EXPIRE);
        }

        mobileService.modifyMobileV2(accountUuid, mobile, verify, shumeiDeviceId);

        return JsonResult.success();
    }

}
