package cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.model.Accounts;
import cn.taqu.account.service.AccountsService;
import cn.taqu.account.service.MobileService;
import cn.taqu.account.service.VerifyRecordService;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.annotation.Api;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.StringUtil;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 验证码相关Controller
 * <AUTHOR>
 * 2017年7月5日 下午5:22:37
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=verify")
public class VerifyController {

	@Autowired
	private MobileService mobileService;

	@Autowired
	private AccountsService accountsService;

	@Autowired
	private VerifyRecordService verifyRecordService;

	/**↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 客户端接口 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓**/

	/**
	 * 获取短信验证码(注册)
	 * http://************:1234/Account_System/v5/Verify/sendRegisterCode
	 * @param params
	 * @return
	 * <AUTHOR>
	 * 2017年7月5日 下午4:32:58
	 *
	 * @deprecated 发送验证码的操作，由客户端直接调用推送系统 h5在用
	 * 2023.08.09 接口下线
	 */
	@Api
	@RequestMapping(params = "method=sendRegisterCode")
	public JsonResult sendRegisterCode(RequestParams params) {
	    throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
	}

	@Api
	@RequestMapping(params = "method=sendBindMobileVerifyCode")
	public JsonResult sendBindMobileVerifyCode(RequestParams params) {
		String accountUuid = params.getFormString(0);

		Accounts account = accountsService.getByAccountUuid(accountUuid);
		String mobile = account.getMobile();

		if (StringUtils.isBlank(mobile)) {
			throw new ServiceException(CodeStatus.MOBILE_NOT_EXISTS);
		}

		verifyRecordService.sendVerify(VerifyRecordService.Code.MODIFY_MOBILE, VerifyRecordService.SmsType.SINGLE_VCODE, mobile);

		Map<String, String> result = new HashMap<>();
		result.put("mobile", StringUtil.mobileSecret(mobile));
		return JsonResult.success(result);
	}

	/**
	 * 获取语音验证码（二次验证）
	 * http://************:1234/Account_System/v5/Verify/sendVoiceMobileCode
	 * @param params
	 * @return
	 * <AUTHOR>
	 * 2017年7月5日 下午5:47:35
	 */
	@Api
	@RequestMapping(params = "method=sendVoiceMobileCode")//sendVoiceCertificationVerifyCode
	public JsonResult sendVoiceMobileCode(RequestParams params) {
		//请求参数
		String ticketId = params.getFormStringOption(0);
		int version = params.getFormIntegerDefault(1, 1);
		mobileService.sendVoiceCertifyCode(ticketId);
		Map<String, String> result = new HashMap<>();
		result.put("status", "1");
		return JsonResult.success(result);
	}

	/**
	 * 校验注册
	 *
	 * @param params
	 * @return
	 * <AUTHOR>
	 * 2017年7月5日 下午5:47:35
	 * @see {@linkplain AccountController#autoLogin(RequestParams)}
	 * @deprecated
	 */
	@Api
	@RequestMapping(params = "method=checkRegCode")//checkRegVerifyCode
	public JsonResult checkRegCode(RequestParams params) {
		throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);

	}

	/**
	 * 【找回手机】修改手机绑定验证码
	 *
	 * @param params
	 * @return
	 */
	@Api
	@RequestMapping(params = "method=sendVerifyCodeForRetrieve")
	public JsonResult sendVerifyCodeForRetrieve(RequestParams params) {
		String uuid = params.getFormString(0);
		String mobile = params.getFormString(1);
		log.info("sendVerifyCodeForRetrieve：{}, {}", uuid, mobile);
		verifyRecordService.sendVerifyCodeForRetrieve(uuid, mobile);
		return JsonResult.success();
	}

	/**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 客户端接口 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑**/

}
