package cn.taqu.account.controller.api.jrpc;

import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.model.PersonalityLabel;
import cn.taqu.account.search.LabelSearch;
import cn.taqu.account.service.PersonalityLabelService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;

/**
 * 个性标签控制器
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@RequestMapping(value="/api", params="service=personalityLabel")
public class PersonalityLabelController {
	
	@Autowired
	private PersonalityLabelService personalityLabelService;

	@RequestMapping(params = "method=getListDataForPage")
	public JsonResult getListDataForPage(RequestParams params) {
		String queryParams = params.getFormString(0);
		Integer pageNumber = params.getFormIntegerDefault(1, 1);
		Integer pageSize = params.getFormIntegerDefault(2, 20);
		LabelSearch search = JsonUtils.stringToObject(JsonUtils.toEmptyJsonObject(queryParams), LabelSearch.class);
		Page<PersonalityLabel> page = personalityLabelService.pageQuery(search, pageNumber, pageSize);
		
		return JsonResult.success(page);
	}

	@RequestMapping(params = "method=create")
	public JsonResult create(RequestParams params) {
		String labelData = params.getFormString(0);
		return JsonResult.success(
			ImmutableMap.of(
				"id",
				personalityLabelService.create(JsonUtils.stringToObject(labelData, PersonalityLabel.class))
			)
		);
	}

	@RequestMapping(params = "method=getByIdFromDb")
	public JsonResult getByIdFromDb(RequestParams params) {
		Long id = params.getFormLong(0);
		PersonalityLabel personalityLabel = personalityLabelService.getByIdFromDb(id);
		return JsonResult.success(personalityLabel);
	}

	@RequestMapping(params = "method=update")
	public JsonResult update(RequestParams params) {
		String labelData = params.getFormString(0);
		personalityLabelService.update(JsonUtils.stringToObject(labelData, PersonalityLabel.class));
		return JsonResult.success();
	}
	
	@RequestMapping(params = "method=disable")
	public JsonResult disable(RequestParams params) {
		Long id = params.getFormLong(0);
		personalityLabelService.updateStatusById(1, id);
		return JsonResult.success();
	}

	@RequestMapping(params = "method=enabled")
	public JsonResult enabled(RequestParams params) {
		Long id = params.getFormLong(0);
		personalityLabelService.updateStatusById(0, id);
		return JsonResult.success();
	}

	@RequestMapping(params = "method=getAllList")
	public JsonResult getListDataForPage() {
		List<PersonalityLabel> list = personalityLabelService.findList();
		return JsonResult.success(list);
	}

	/**
	 * 获取喜欢类型标签
	 * @return
	 */
	@RequestMapping(params = "method=getLikeLabel")
	public JsonResult getLikeLabel(RequestParams params) {
		Integer sexType = params.getFormInteger(0);
		List<Map<String, String>> likeTypeLabel = personalityLabelService.findByTypeAndSex(1, sexType);
		return JsonResult.success(likeTypeLabel);
	}
}
