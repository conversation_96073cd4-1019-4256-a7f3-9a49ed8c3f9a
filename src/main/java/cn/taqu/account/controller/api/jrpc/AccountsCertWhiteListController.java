package cn.taqu.account.controller.api.jrpc;

import java.util.Arrays;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.taqu.account.model.AccountsCertWhiteList;
import cn.taqu.account.service.AccountsCertService;
import cn.taqu.account.service.AccountsCertWhiteListService;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 认证白名单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-15 16:36
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=certWhiteList")
public class AccountsCertWhiteListController {

    @Autowired
    private AccountsCertWhiteListService accountsCertWhiteListService;
    @Autowired
    private AccountsCertService accountsCertService;

    /**
     * 分页查询
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=getListDataForPage")
    public JsonResult getListDataForPage(RequestParams params) {
        Integer pageNum = params.getFormIntegerDefault(0, 1);
        Integer pageSize = params.getFormIntegerDefault(1, 20);
        Long startTime = params.getFormLongDefault(2, null);
        Long endTime = params.getFormLongDefault(3, null);
        String accountUuid = params.getFormStringDefault(4, null);
        Integer whiteListFrom = params.getFormIntegerDefault(5, null);
        PageData<AccountsCertWhiteList> pageData = accountsCertWhiteListService.pageCertWhiteList(pageNum, pageSize, startTime, endTime, accountUuid, whiteListFrom);
        return JsonResult.success(pageData);
    }

    /**
     * 增加白名单
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=addCertWhiteListFromServer")
    public JsonResult addCertWhiteListFromServer(RequestParams params) {
        return JsonResult.success();
    }

    /**
     * 批量添加用户
     * @param params
     * @return
     */
    @Deprecated
    @RequestMapping(params = "method=batchAddCertWhiteList")
    public JsonResult batchCertWhiteList(RequestParams params){
        String[] datas = params.getFormStringArray(0);

        String loginname = RequestParams.getSoa_basic_java().getToken();
        Map<String, Object> result = accountsCertWhiteListService.batchCertWhiteList(Arrays.asList(datas), loginname);
        return JsonResult.success(result);
    }

    /**
     * 认证前置条件
     * @param params
     * @return
     */
    @RequestMapping(params = "method=needFinishBeforeCert")
    public JsonResult canDoCert(RequestParams params) {
        String accountUuid = params.getFormString(0);
        Integer certType = params.getFormIntegerDefault(1, 1);
        Map<String, String> result = accountsCertService.needToDoBeforeCert(accountUuid, certType);
        return JsonResult.success(result);
    }
}
