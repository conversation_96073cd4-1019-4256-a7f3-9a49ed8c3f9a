package cn.taqu.account.controller;

import cn.taqu.account.manager.AccountsThirdPartyManager;
import cn.taqu.account.model.AccountsCertification;
import cn.taqu.account.model.AccountsThirdParty;
import cn.taqu.account.service.AccountsCertificationService;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@RestController
@RequestMapping(value = "/test")
public class TestController {

    @Autowired
    private AccountsThirdPartyManager accountsThirdPartyManager;

    @Autowired
    private AccountsCertificationService accountsCertificationService;

    @RequestMapping("/t1")
    public JsonResult kafka() {

        String openId = "9BC77A2C9E6CA199DBD7B8B487CCE30A";
        String type = "QQ";

        AccountsThirdParty party = accountsThirdPartyManager.findValidByOpenId(openId, type);
        log.info("party:{}", JsonUtils.objectToString(party));
        Integer count = accountsThirdPartyManager.countByOpenIdAndType(openId, type);
        log.info("count:{}", count);

        return JsonResult.success();
    }

    @RequestMapping("/t2")
    public JsonResult t2() {
        String uuid = "bgjbbbgjddgiichi";
        String type = "WeChat";
        List<String> types = accountsThirdPartyManager.getValidTypeByAccountUuid(uuid);
        log.info("types:{}", JsonUtils.objectToString(types));

        AccountsThirdParty last = accountsThirdPartyManager.findLastestOneByUuid(uuid);
        log.info("last:{}", JsonUtils.objectToString(last));

        AccountsThirdParty party = accountsThirdPartyManager.findValidByAccountUuidAndType(uuid, type);
        log.info("party:{}", JsonUtils.objectToString(party));

        return JsonResult.success();
    }

    @SentinelResource(value="account_test_getAccountsCertification", blockHandler = "testHandler")
    @RequestMapping("/getAccountsCertification")
    public JsonResult test(RequestParams params){
//        String accountUuid = params.getFormStringOption(0);
        String accountUuid="r2mstms8qmu1";
        if(StringUtils.isBlank(accountUuid)){
            accountUuid="r2mstms8qmu";
        }
        AccountsCertification accountsCertification=accountsCertificationService.getAccountsCertification(accountUuid);
        return JsonResult.success(accountsCertification);
    }

    public JsonResult testHandler(RequestParams params, BlockException be){
        log.info("test ",be);
        return JsonResult.failed(JsonUtils.objectToString(be.getRule()));
    }

    @RequestMapping("/testUrl")
    public JsonResult testUrl(RequestParams params){
        return JsonResult.success("success");
    }
}
