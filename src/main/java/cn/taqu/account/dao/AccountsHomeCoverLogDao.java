package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsHomeCoverLog;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 09:41
 */
@Deprecated
public interface AccountsHomeCoverLogDao extends NimbleDao<Long, AccountsHomeCoverLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "home_cover_id", "photo_url", "pass_photo_url", "risk_level", "risk_description", "img_check_order_no", "img_quality_order_no", "home_cover_status", "check_time", "operator", "create_time", "update_time"
    );

    /**
     * 根据uuid查找历史
     * @param accountUuid
     * @return
     */
    List<AccountsHomeCoverLog> listByUuid(String accountUuid, Integer homeCoverStatus);

    /**
     * 根据cover_id查找历史
     * @param coverId
     * @return
     */
    List<AccountsHomeCoverLog> listByCoverId(Long coverId);

    /**
     * 更新为跳过状态
     * @param ids
     */
    void updateSkipStatusByIds(List<Long> ids);

    List<AccountsHomeCoverLog> getHomeCoverLogList(Integer pageNumber, Integer pageSize, Long startTime, Long endTime, Integer value, String accontUuid, String operatorToken);

    /**
     * 获取历史审核记录
     * @param accontUuid
     * @return
     */
    List<AccountsHomeCoverLog> getHomeCoverLogListReviewCheck(String accontUuid, List<Integer> status);
}
