package cn.taqu.account.dao;

import java.util.Arrays;
import java.util.List;

import cn.taqu.account.model.AccountsCertLog;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-29 14:09
 */
public interface AccountsCertLogDao extends NimbleDao<Long, AccountsCertLog> {

    List<String> FIELDS_ALL = Arrays.asList("id", "account_uuid",
            "cert_white_list","cert_type",
            "operator","operator_time","operator_type","base_photo_url","remark",
            "create_time","update_time");

}
