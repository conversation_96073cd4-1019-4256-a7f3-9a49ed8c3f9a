package cn.taqu.account.dao;

import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.model.AccountsIdealTargetLog;
import cn.taqu.account.model.AccountsIntroductionLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:07
 */
public interface AccountsIntroductionLogDao extends NimbleDao<Long, AccountsIntroductionLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "content", "status", "operator", "create_time", "update_time", "audit_time"
    );

    /**
     * 获取id
     * @param introductionLogId
     * @return
     */
    AccountsIntroductionLog getById(Long introductionLogId);

    default AccountsIntroductionLog getLastPass(String uuid, Long auditTime) {
        Sql sql = Sql.build(AccountsIntroductionLog.class, FIELDS_ALL)
            .eq("account_uuid", uuid)
            .eq("status", CommonAuditStatus.AUDIT_SUCCESS.getStatus())
            .lt("audit_time", auditTime)
            .orderBy("audit_time desc")
            .limit(1);
        return this.get(sql, AccountsIntroductionLog.class);
    }

    /**
     * 获取审核列表
     * @param pageNum
     * @param pageSize
     * @param status
     * @param startTime
     * @param endTime
     * @param accontUuid
     * @param operatorToken
     * @return
     */
    List<AccountsIntroductionLog> getIntroductionList(Integer pageNum, Integer pageSize, Integer status, Long startTime, Long endTime, String accontUuid, String operatorToken, String content);

    /**
     * 查询未审核记录（超过1小时未审核）
     *
     * @param expireTime
     * @param startTime
     * @param status
     * @param limit
     * @return
     */
    List<AccountsIntroductionLog> queryNotAudit(Long expireTime, Long startTime, Integer status, Integer limit);
}
