package cn.taqu.account.dao;

import cn.taqu.account.model.NickNameRiskVerify;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/23.
 */

public interface NickNameRiskVerifyDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, NickNameRiskVerify> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "old_name", "new_name", "default_nick_name", "old_default_nick_name", "update_time", "type", "status", "operator","suggestion","appcode", "suggestion_code", "request_id", "response_str");

    void passVerify(Long[] ids, Integer status,String operator);

    void passVerify(Long id, Integer status, String operator);

    List<NickNameRiskVerify> findListByIds(Long[] ids);

    void deleteByUuid(String uuid);

    NickNameRiskVerify findByUuid(String uuid);

}
