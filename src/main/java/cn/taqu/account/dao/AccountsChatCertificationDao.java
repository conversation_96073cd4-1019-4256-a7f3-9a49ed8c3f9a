package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsChatCertification;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by cqa on 2017/11/6.
 */
public interface AccountsChatCertificationDao extends NimbleDao<Long, AccountsChatCertification> {

    List<String> FIELDS_ALL = Arrays.asList("id", "account_uuid", "real_name", "real_name_cipher","real_name_digest",
        "identity_no","identity_no_cipher","identity_no_digest","identity_no_birth","identity_type","biz_no","reward_account", "reward_account_checked", "reward_account_cipher",
        "reward_account_digest","is_checked", "certification_photo_url", "identity_no_status", "create_time","update_time","source");

    List<AccountsChatCertification> getAccountChatCertificationsByIdentityNo(String identityNo, Integer isChecked);

    List<AccountsChatCertification> getAccountChatCertificationsByIdentityNoDigest(String lowerIdentityNoDigest, String upperIdentityNoDigest, Integer isChecked);

    Object[] findIdByAccountUuid(String accountUuid);

    Object[] findInfoByBizNo(String bizNo);

    void addKeyValue(Map<String, Object> keyValueMap);

    void updateById(Map<String, Object> keyValueMap, Long id);

    AccountsChatCertification getByAccountUuid(String accountUuid);

    /**
     * @param accountUuid
     * @return
     */
    String getCertificationPhotoUrlByUuid(String accountUuid);

    /**
     * 批量获取用户业务认证信息
     * @param uuidList
     * @return
     */
    List<AccountsChatCertification> getAccountsChatCertificationByUuids(List<String> uuidList);

}
