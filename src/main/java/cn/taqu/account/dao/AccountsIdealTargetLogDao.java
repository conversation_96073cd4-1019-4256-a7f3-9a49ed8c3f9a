package cn.taqu.account.dao;

import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.model.AccountsIdealTargetLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/26 下午2:15
 */
public interface AccountsIdealTargetLogDao extends NimbleDao<Long, AccountsIdealTargetLog> {

    default AccountsIdealTargetLog getById(Long id) {
        Sql sql = Sql.build("SELECT  * FROM accounts_ideal_target_log WHERE id = ?", id);
        return get(sql);
    }

//    default AccountsIdealTargetLog getLastPass(String uuid, Long auditTime) {
//        Sql sql = Sql.build(AccountsIdealTargetLog.class, Arrays.asList("id", "account_uuid", "ideal_target", "status"))
//            .eq("account_uuid", uuid)
//            .eq("status", CommonAuditStatus.AUDIT_SUCCESS.getStatus())
//            .lt("audit_time", auditTime)
//            .orderBy("audit_time desc")
//            .limit(1);
//        return this.get(sql, AccountsIdealTargetLog.class);
//    }

    default Integer count(String uuid, Integer status) {
        Sql sql = Sql.build("SELECT count(id) FROM accounts_ideal_target_log WHERE account_uuid = ? AND status = ?", uuid, status);
        return queryForInteger(sql);
    }

    default List<AccountsIdealTargetLog> queryNotAudit(Long expireTime, Long startTime, Integer limit) {
        Sql sql = Sql.build(AccountsIdealTargetLog.class, Arrays.asList("id", "account_uuid", "ideal_target", "status"))
            .eq("status", CommonAuditStatus.AUDITING.getStatus())
            .lt("create_time", expireTime)
            .gt("create_time", startTime)
            .orderBy("id desc")
            .limit(limit);
        return this.query(sql);
    }
}
