package cn.taqu.account.dao;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import cn.taqu.account.model.PersonalityLabel;

/**
 * 自动生成Dao接口类
 */
public interface PersonalityLabelDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.PersonalityLabel> {

	public static final List<String> FIELDS_ALL = Arrays.asList("id", "content", "description", "type", "sex", "status", "create_time", "update_time", "second_type");

    List<PersonalityLabel> findByIds(Collection<Long> ids);

    PersonalityLabel getByIdFromDb(Long id);

    int updateStatusById(Integer status, Long id);

    List<PersonalityLabel> getSimpleByTypeAndSex(Integer type, Integer sex);

    List<Long> getAllId();

    /**
     * 根据类型获取所有
     *
     * @param type
     * @return
     */
    List<Long> getAllByType(Integer type);

    /**
     * 权重校验唯一性
     *
     * @param type
     * @param sort
     * @return
     */
    Long sortIsExist(Integer type, Integer sort);
}
