package cn.taqu.account.dao;


import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.PersonalProfileInfo;

public interface PersonalProfileInfoDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.PersonalProfileInfo> {
    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "personal_profile", "risk_level", "status", "operator", "create_time", "audit_time", "suggestion", "request_id", "response_str");

    List<PersonalProfileInfo> getByUuid(String accountUuid);
    
}
