package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsGuest;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.Collection;
import java.util.List;

@Deprecated
public interface AccountsGuestDao extends NimbleDao<Long, AccountsGuest> {
    AccountsGuest getByUuid(String accountUuid);
    AccountsGuest getByAccountName(String accountName);
    AccountsGuest getByMemberId(Long memberId);
    int updateTicket(String ticket, String accountUuid);
    List<AccountsGuest> getByUuidAndMemberIdAndName(String accountUuid, Long memberId, String accountName);
    List<Object[]> getUuidAndAccountNameInUuids(Collection<String> uuids);
    List<Object[]> getUuidAndAccountNameAndRegtimeInUuids(Collection<String> uuids);
}
