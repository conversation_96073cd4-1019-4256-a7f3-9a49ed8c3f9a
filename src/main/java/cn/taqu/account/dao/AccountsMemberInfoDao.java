package cn.taqu.account.dao;

import java.util.List;

import cn.taqu.account.model.AccountsMemberInfo;

public interface AccountsMemberInfoDao extends cn.taqu.core.orm.base.BaseDao<java.lang.Long, cn.taqu.account.model.AccountsMemberInfo> {

    AccountsMemberInfo findByAccountUuid(String accountUuid);

    int updateLastActiveTime(String uuid, Long time);

    List<String> findTokenByAccountUuid(String accountUuid);

    List<String> findAccountUuidByToken(String token);

    List<AccountsMemberInfo> batchGetInfoByUuids(List<String> accountUuidList);

    Integer countByTime(Long startTime, Long endTime);

    /**
     * 获取当前id的最大值
     *
     * @return 当前表中id最大值
     */
    Long getMaxId();

}
