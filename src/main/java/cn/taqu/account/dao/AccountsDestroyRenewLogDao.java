package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsDestroyRenewLog;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface AccountsDestroyRenewLogDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsDestroyRenewLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "create_time", "update_time", "operate_name"
    );

    List<AccountsDestroyRenewLog> getListByUuid(String accountUuid);

}
