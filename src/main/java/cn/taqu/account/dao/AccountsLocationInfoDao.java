package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsLocationInfo;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 用户位置信息Dao
 * <AUTHOR>
 */
public interface AccountsLocationInfoDao extends NimbleDao<Long, AccountsLocationInfo> {
    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "ip", "longitude", "longitude_cipher", "latitude", "latitude_cipher",
            "city_id", "create_time", "update_time", "data_sources", "use_provincial_capital"
    );

    /**
     * 根据uuid查询用户位置信息
     * @param accountUuid
     * @return
     */
    AccountsLocationInfo selectOneByUuid(String accountUuid);

    /**
     * 更新位置信息
     * 
     * @param id
     * @param ip
     * @param longitude
     * @param longitudeCipher
     * @param latitude
     * @param latitudeCipher
     * @param cityId
     * @param createTime
     * @param updateTime
     * @param dataSources
     * @param useProvincialCapital
     */
    void updateLocationInfo(Long id, String ip, String longitude, String longitudeCipher, String latitude, String latitudeCipher, Long cityId,  Long createTime, Long updateTime, Integer dataSources, Integer useProvincialCapital);
}
