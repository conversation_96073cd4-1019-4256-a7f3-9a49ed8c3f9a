package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.VoiceSignInfo;

public interface VoiceSignInfoDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.VoiceSignInfo> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "voice_sign_url", "voice_sign_duration", "bucket", "status",
            "audit_reason", "operator", "create_time", "audit_time", "source"
    );

    VoiceSignInfo findLastestOneByUuidAndStatus(String accountUuid,Integer status);

    VoiceSignInfo getLastOneByUuid(String accountUuid);

    List<VoiceSignInfo> queryNotAudit(Long expireTime, Long startTime, Integer status, Integer limit);
}
