package cn.taqu.account.dao;

import cn.taqu.account.model.CareModel;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 用户关怀模式名单DAO
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
public interface CareModelDao extends NimbleDao<Long, CareModel> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "version", "create_time"
    );

    /**
     * 根据用户UUID查询关怀名单记录
     *
     * @param accountUuid 用户UUID
     * @return 关怀名单记录
     */
    CareModel findByAccountUuid(String accountUuid);

}
