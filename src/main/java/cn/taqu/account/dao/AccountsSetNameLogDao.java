package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsSetNameLog;

import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface AccountsSetNameLogDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsSetNameLog> {

    /**
     * 只获取用户的修改记录
     */
    long getNicknameLogByUuidCount(String account_uuid);

    /**
     * 只获取用户的修改记录
     */
    List<AccountsSetNameLog> getNicknameLogByUuid(String account_uuid, Integer start, Integer pageSize);

    List<AccountsSetNameLog> getByUuid(String accountUuid, int limit);

}
