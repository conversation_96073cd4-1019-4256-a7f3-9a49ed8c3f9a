package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsPhotoImgCheckLog;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-13 18:30
 */
public interface AccountsPhotoImgCheckLogDao extends NimbleDao<Long, AccountsPhotoImgCheckLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "photo_url","risk_level","risk_description","response_str","third_order_no","create_time","update_time"
    );

    AccountsPhotoImgCheckLog getLastByUuid(String accountUuid, String photoUrl);
}
