package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsLevel;

/**
 * 自动生成Dao接口类
 */
public interface AccountsLevelDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsLevel> {

    AccountsLevel getOneById(Long id);

    AccountsLevel getOneFromMasterDbById(Long id);

    AccountsLevel getOneByLevelNum(Integer levelNum, Integer appcode);

    AccountsLevel getOneFromMasterDbByLevelNum(Integer levelNum, Integer appcode);

}
