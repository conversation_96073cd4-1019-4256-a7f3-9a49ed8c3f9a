package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsMedalInfo;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

public interface AccountsMedalInfoDao extends NimbleDao<Long, AccountsMedalInfo> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "medal_id",
            "update_time", "end_time", "status");

    List<String> findUuidByMedalId(Long medalId);
    boolean accountExists(String accountUuid);
    int updateAccountMedal(String accountUuid, Long medalId, Integer status, Long endTime, Long updateTime);
    int batchUnbindAccountMedal(List<String> accountUuidList, Long updateTime);

    List< AccountsMedalInfo> getListByUuid(String accountUuid);
}
