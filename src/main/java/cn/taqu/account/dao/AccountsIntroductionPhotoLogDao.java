package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsIntroductionPhotoLog;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:24
 */
public interface AccountsIntroductionPhotoLogDao extends NimbleDao<Long, AccountsIntroductionPhotoLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "bucket", "photo_url", "height", "width", "introduction_log_id", "create_time", "update_time"
    );

    List<AccountsIntroductionPhotoLog> findByIntroductionLogId(Long introductionLogId);
}
