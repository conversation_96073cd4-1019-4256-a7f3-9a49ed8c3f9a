package cn.taqu.account.dao;

import java.util.Arrays;
import java.util.List;

import cn.taqu.account.model.AccountsCertWhiteList;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-15 15:55
 */
public interface AccountsCertWhiteListDao extends NimbleDao<Long, AccountsCertWhiteList> {

    List<String> FIELDS_ALL = Arrays.asList("id", "account_uuid", "white_list_from", "operator", "remark",
            "create_time", "update_time");

    /**
     * 获取分页数据
     * @param pageNum
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param accountUuid
     * @param whiteListFrom
     * @return
     */
    List<AccountsCertWhiteList> list(Integer pageNum, Integer pageSize, Long startTime, Long endTime, String accountUuid, Integer whiteListFrom);

    /**
     * 根据uuid计算数量
     * @param uuid
     * @return
     */
    Integer countByUuid(String uuid);

}
