package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.TokenNewOldRelation;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * 功能迁移至 j53
 * 
 * <AUTHOR>
 * 2024年12月18日上午9:51:57
 */
@Deprecated
public interface TokenNewOldRelationDao extends NimbleDao<Long, TokenNewOldRelation> {
    
    public static final List<String> FIELDS_ALL = Lists.newArrayList("id","new_token","old_token","create_time","update_time");

    
    TokenNewOldRelation getByNewOldToken(String newToken, String oldToken);


    /**
     * @param newToken
     * @param oldToken
     * @param createTime
     * @param updateTime
     */
    void insertIgnore(String newToken, String oldToken, Long createTime, Long updateTime);


    /**
     * @param newToken
     * @return
     */
    List<String> getOldTokenListLimit2(String newToken);

}
