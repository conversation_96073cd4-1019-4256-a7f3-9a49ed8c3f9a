package cn.taqu.account.dao;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import cn.taqu.account.model.Accounts;

/**
 * 自动生成Dao接口类
 */
public interface AccountsDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.Accounts> {

    List<Accounts> listByUuidOrNickname(String uuid, String nickname);

    Long getCreateTimeByUuid(String createTime);// 根据传入的uuid获取手机号
    
//    String getMobileByUuid(String uuid);// 根据传入的uuid获取手机号

    String getAccountNamebyUuid(String uuid);//根据uuid获取account_name

    int bindMobileByUuid(String mobile, String mobileCipher,String mobileDigest, String uuid);// 将mobile绑定到uuid上

    int setMobileNullByUuid(String mobile, String uuid);// 将mobile为当前号码的账号的mobile字段更新为空，注意不包含当前账号
    
    int setMobileNullByUuidDigest(String mobileDigest, String uuid);// 将mobile为当前号码的账号的mobile字段更新为空，注意不包含当前账号

    int updatePasswordForFirst(String password, String accountUuid);

    int updatePasswordForReset(String password, String accountUuid);

    int updateMemberByUuid(Long member_id, String account_uuid);

    long getCountByMemberId(Long member_id,Long todayBeginSecond,Long todayEndSecond);

    Integer getSexTypeByAccountUuid(String account_uuid);

    Integer getSexTypeFromMasterDBByAccountUuid(String account_uuid);

    Long getAccountIdByUuid(String uuid);

    int setNicknameByUuid(String nickname, String account_uuid);//根据account_uuid设置或更新昵称

    Accounts getByUuid(String accountUuid);

    List<Object[]> getUuidAndAccountNameInUuids(Collection<String> uuids);

    List<Object[]> getUuidAndAccountNameAndRegtimeInUuids(Collection<String> uuids);

    List<String> listUuidByAccountName(String accountName);

    @Deprecated
    String getUuidByEmail(String email);

    Object[] getByAccountKey(String accountKey);

	int updateAccountKeyByUuid(String accountKey, String account_uuid);

	int clearAccountKey(String accountKey);

    int updateByUuid(Map<String, Object> fieldValue, String accountUuid);

    int updateSexType(Integer param, String uuid);// 更新性别

    int updateDestroyStatus(String accountName, String avatar, String uuid);

    int updateMobilePlace(String uuid, String mobilePlace);

    /**
     * 昵称可重复，值查询返回某一个
     * @param accountName
     * @return
     */
    Accounts getUuidAndPwdByName(String accountName);

    Accounts getUuidAndPwdByUuid(String accountUuid);

//    Accounts getRegStyleByUuid(String uuid);

    /**
     * 获取用户状态
     *
     * @param accountUuid
     * @return
     */
    Integer getAccountStatusByUuid(String accountUuid);

    /**
     * @param accountUuid
     */
    Integer renewAccount(String accountUuid);

    /**
     * @param accountsUuidList
     * @return
     */
    int setMobileNullInUuid(List<String> accountsUuidList);

}
