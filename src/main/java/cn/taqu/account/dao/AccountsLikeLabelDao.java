package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.AccountsLikeLabel;
import cn.taqu.core.orm.base.NimbleDao;

public interface AccountsLikeLabelDao extends NimbleDao<Long, AccountsLikeLabel> {

	public static final List<String> FIELDS_ALL = Lists.newArrayList("id","account_uuid","type","label_id", "stick_type", "seq", "create_time","update_time");


	public List<Long> listIdByAccountUuid(String accountUuid, Integer type);


    List<AccountsLikeLabel> getListByUuid(String accountUuid);
}
