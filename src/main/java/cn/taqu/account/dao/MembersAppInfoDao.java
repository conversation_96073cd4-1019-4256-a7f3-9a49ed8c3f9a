package cn.taqu.account.dao;

import cn.taqu.account.model.MembersAppInfo;

import java.util.Collection;
import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface MembersAppInfoDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.MembersAppInfo> {

    // 根据member_id更新members_app_info表
    void updateByMemberId(Long appVersion, Integer appcode, Integer cloned, String place, Integer platformId, Long updateTime, Long memberId);

    MembersAppInfo findOneByMemberId(Long memberId, boolean master);

    List<MembersAppInfo> findByMemberIds(Collection<Long> memberIdList);

    int updateNoticeEnable(Integer noticeEnable, Long memberId);

}
