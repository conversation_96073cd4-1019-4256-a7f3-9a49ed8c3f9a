package cn.taqu.account.dao;

import cn.taqu.account.model.School;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/26 下午2:15
 */
public interface SchoolDao extends NimbleDao<Long, School> {

    default List<School> selectLike(String school, int limit) {
        Sql sql = Sql.build("select id, school_name, school_type from school where school_name like ? limit ?", school.concat("%"), limit);
        return this.query(sql, School.class);
    }

    default School getById(Long id) {
        Sql sql = Sql.build("select id, school_name, school_type from school where id = ?", id);
        return get(sql, School.class);
    }
}
