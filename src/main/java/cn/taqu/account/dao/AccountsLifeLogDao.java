package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.AccountsLifeLog;
import cn.taqu.core.orm.base.NimbleDao;

public interface AccountsLifeLogDao extends Ni<PERSON><PERSON>ao<Long, AccountsLifeLog> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "photo_url",
            "width", "height", "remark", "seq", "status", "verify_status", "error_msg",
            "audit_time", "create_time", "update_time", "risk_resp_data");

    /**
     * @param accountUuid
     * @param photoUrl
     * @param width
     * @param height
     * @param remark
     * @param seq
     * @param verifyStatus
     * @return
     */
    AccountsLifeLog addAccountsLifeLog(String accountUuid, String photoUrl, Integer width, Integer height,
        String remark, Integer seq, Integer verifyStatus);

    /**
     * @param expireTime
     * @param startTime
     * @param limit
     * @return
     */
    List<AccountsLifeLog> queryNotAudit(Long expireTime, Long startTime, Integer limit);
    

}
