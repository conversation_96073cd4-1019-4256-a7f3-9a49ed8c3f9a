package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsDress;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface AccountsDressDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsDress> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "dress_id", "status", "create_time", "update_time", "dress_type"
    );

    AccountsDress getByUuid(String uuid,Integer dressType);

}
