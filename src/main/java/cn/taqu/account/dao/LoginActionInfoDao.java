package cn.taqu.account.dao;

import cn.taqu.account.model.LoginActionInfo;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

public interface LoginActionInfoDao extends Nimble<PERSON>ao<Long, LoginActionInfo> {
    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "token", "action_ip", "appcode", "cloned",
            "platform_id", "channel", "app_version", "action_mode", "create_time", "platform_name",
            "access", "alias", "gender", "longitude", "longitude_cipher", "latitude", "latitude_cipher", "city"
    );

    Long getIdByAccountUuid(String accountUuid);

    LoginActionInfo getByUuid(String accountUuid);
}
