package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsIntroductionPhoto;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:24
 */
public interface AccountsIntroductionPhotoDao extends NimbleDao<Long, AccountsIntroductionPhoto> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "bucket", "photo_url", "height", "width", "introduction_id", "create_time", "update_time"
    );

    /**
     * 查询自我介绍id查询图片
     * @param introductionId
     * @return
     */
    List<AccountsIntroductionPhoto> findByIntroductionId(Long introductionId);

    /**
     * 根据自我介绍id集合 查找图片集合
     * @param deleteIntroductionIds
     * @return
     */
    List<AccountsIntroductionPhoto> findInIntroductionId(List<Long> deleteIntroductionIds);

}
