package cn.taqu.account.dao;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.function.Supplier;

/**
 * 事务包裹
 * <p>手动提交或回滚事务</p>
 *
 * <AUTHOR>
 * @date 2022/12/22 3:18 PM
 */
@Slf4j
@Component
public final class TransactionWrapper {

    private final PlatformTransactionManager transactionManager;

    private static TransactionWrapper                  instance;

    private TransactionWrapper(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
        TransactionWrapper.instance = this;
    }

    public static TransactionWrapper me() {
        return TransactionWrapper.instance;
    }

    public void wrap(Runnable runnable) {
        TransactionStatus transactionStatus = withRequiredTransaction();
        try {
            runnable.run();
            commit(transactionStatus);
        } catch (Exception e) {
            log.warn("手动提交事务异常:{}", e.getMessage(), e);
            rollback(transactionStatus);
            throw e;
        }
    }

    public void wrap(Runnable runnable, Integer propagation) {
        TransactionStatus transactionStatus = withTransaction(propagation);
        try {
            runnable.run();
            commit(transactionStatus);
        } catch (Exception e) {
            log.warn("手动提交事务异常:{}", e.getMessage(), e);
            rollback(transactionStatus);
            throw e;
        }
    }

    public void wrapQuietly(Runnable runnable) {
        try {
            wrap(runnable);
        } catch (Exception e) {
            log.warn("Transaction error, this block would be rollback.", e);
        }
    }

    public void wrapQuietly(Runnable runnable, Integer propagation) {
        try {
            wrap(runnable, propagation);
        } catch (Exception e) {
            log.warn("Transaction error, this block would be rollback.", e);
        }
    }

    public <T> T wrap(Supplier<T> supplier) {
        TransactionStatus transactionStatus = withRequiredTransaction();
        try {
            T result = supplier.get();
            commit(transactionStatus);
            return result;
        } catch (Exception e) {
            rollback(transactionStatus);
            throw e;
        }
    }

    public <T> T wrapQuietly(Supplier<T> supplier) {
        try {
            return wrap(supplier);
        } catch (Exception e) {
            log.warn("Transaction error, this block would be rollback.", e);
            return null;
        }
    }

    public TransactionStatus withRequiredTransaction() {
        return withTransaction(TransactionDefinition.PROPAGATION_REQUIRED);
    }

    public TransactionStatus withTransaction(int propagation) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(propagation);
        return transactionManager.getTransaction(definition);
    }

    public void commit(TransactionStatus transactionStatus) {
        transactionManager.commit(transactionStatus);
    }

    public void rollback(TransactionStatus transactionStatus) {
        transactionManager.rollback(transactionStatus);
    }
}
