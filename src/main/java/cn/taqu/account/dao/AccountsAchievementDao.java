package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsAchievement;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

public interface AccountsAchievementDao extends NimbleDao<Long, AccountsAchievement> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "achievement_id", "achievement_level", "is_adorn","create_time"
    );

    boolean achievementExists(String accountUuid, Long achievementId, Integer level);
    int setAdornByIdList(int isAdorn, List<Long> idList);
    int setAdornByUuid(int isAdorn, String accountUuid, List<Long> excludeAchievementIdList);
    int setAdorn(int isAdorn, String accountUuid, Long achievementId, Integer level);
    int setAdorn(int isAdorn, String accountUuid, Long achievementId);
    int delById(Long id);
    List<AccountsAchievement> getAccountByAchievementId(String accountUuid, Long achievementId);

    List<AccountsAchievement> getListByUuids(String accountUuid);
}
