package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsIdealTarget;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * <AUTHOR>
 * @date 2024/11/26 下午2:15
 */
public interface AccountsIdealTargetDao extends NimbleDao<Long, AccountsIdealTarget> {

    default AccountsIdealTarget getByUuid(String uuid) {
        Sql sql = Sql.build("SELECT  * FROM accounts_ideal_target WHERE account_uuid = ?", uuid);
        return get(sql);
    }

    void upsert(AccountsIdealTarget target);
}
