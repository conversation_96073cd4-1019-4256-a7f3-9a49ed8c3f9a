package cn.taqu.account.dao;

import cn.taqu.account.model.UnbindWhitelist;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.List;


public interface UnbindWhitelistDao extends NimbleDao<Long, UnbindWhitelist> {

    List<UnbindWhitelist> getUnbindWhitelists(String uuid);

    UnbindWhitelist getUnbindWhitelistById(Long id);

    void updateUnbindWhitelistStatusById(Long id,Integer status);

}
