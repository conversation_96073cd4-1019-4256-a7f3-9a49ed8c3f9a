package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsIntroduction;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:07
 */
public interface AccountsIntroductionDao extends NimbleDao<Long, AccountsIntroduction> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "content", "status", "operator", "create_time", "update_time", "audit_time"
    );

    /**
     * 根据uuid查询
     * @param accountUuid
     * @return
     */
    AccountsIntroduction findByUuid(String accountUuid);

}
