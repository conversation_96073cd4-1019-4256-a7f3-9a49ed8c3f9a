package cn.taqu.account.dao;

import cn.taqu.account.model.ImageCompareRecord;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * <AUTHOR>
 * @date 2025/4/7 上午10:16
 */
public interface ImageCompareRecordDao extends NimbleDao<Long, ImageCompareRecord> {

    default String getBizId(Long originId, Integer bizType) {
        Sql sql = Sql.build("SELECT biz_id FROM image_compare_record WHERE origin_id = ? AND biz_type = ? ORDER BY id DESC LIMIT 1", originId, bizType);
        return queryForString(sql);
    }
}
