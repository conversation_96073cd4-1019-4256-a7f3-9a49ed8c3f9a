package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.AccountsPhotoImgCompareLog;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-13 18:30
 */
public interface AccountsPhotoImgCompareLogDao extends NimbleDao<Long, AccountsPhotoImgCompareLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "base_photo_url", "verify_photo_url", "face_model_version", "score",
            "response_str", "third_order_no", "create_time", "update_time"
    );

    AccountsPhotoImgCompareLog getLastByUuid(String accountUuid, String photoUrl);
}
