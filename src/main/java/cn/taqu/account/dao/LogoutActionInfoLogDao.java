package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.LogoutActionInfoLog;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * 登出日志
 * <AUTHOR>
 */
public interface LogoutActionInfoLogDao extends NimbleDao<Long, LogoutActionInfoLog> {
    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "token", "action_ip", "appcode", "cloned",
            "platform_id", "channel", "app_version", "logout_type", "create_time", "update_time", "platform_name",
            "access", "alias", "gender", "longitude", "longitude_cipher", "latitude", "latitude_cipher", "city"
    );
}
