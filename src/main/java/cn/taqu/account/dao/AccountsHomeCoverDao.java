package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsHomeCover;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 09:41
 */
@Deprecated
public interface AccountsHomeCoverDao extends NimbleDao<Long, AccountsHomeCover> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "photo_url", "pass_photo_url", "risk_level", "risk_description", "img_check_order_no",
            "img_quality_order_no", "home_cover_status", "check_time", "operator", "create_time", "update_time"
    );

    List<AccountsHomeCover> getHomeCoverList(Integer pageNumber, Integer pageSize, Long startTime, Long endTime, Integer homeCoverStatus, String accontUuid, String operatorToken);

    void deleteByAccountUuid(String accountUuid);

    List<AccountsHomeCover> listByUuid(String accountUuid);

    AccountsHomeCover getById(Long id);

    void updateCheckPass(Long id, String photoUrl, Long now, String operator);
}
