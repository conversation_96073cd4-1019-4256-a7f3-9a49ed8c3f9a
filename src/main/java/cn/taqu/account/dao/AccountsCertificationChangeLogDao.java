package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsCertificationChangeLog;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 14:09
 */
public interface AccountsCertificationChangeLogDao extends NimbleDao<Long, AccountsCertificationChangeLog> {

    List<String> FIELDS_ALL = Arrays.asList("id", "account_uuid", "real_name", "real_name_cipher","real_name_digest",
        "identity_no","identity_no_cipher","identity_no_digest" , "log_type", "operator", "operator_time", "operator_type", 
        "base_photo_url" , "create_time","update_time");

    /**
     * 
     * @param identityNo
     * @param logType
     * @param limit
     * @return
     */
    List<String> listAccountUuidByIdentityNo(String identityNo, Integer logType, Integer limit);
    
    /**
     * 
     * @param identityNoDigest
     * @param logType
     * @param limit
     * @return
     */
    List<String> listAccountUuidByIdentityNoDigest(String identityNoDigest, Integer logType, Integer limit);
}
