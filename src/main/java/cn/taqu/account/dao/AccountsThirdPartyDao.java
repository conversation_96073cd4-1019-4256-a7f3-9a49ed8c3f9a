package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsThirdParty;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface AccountsThirdPartyDao extends NimbleDao<Long, AccountsThirdParty> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_id", "type",
            "open_id", "union_id", "status", "create_time", "update_time", "account_uuid");

    int updateForDebind(Integer debindStatus, Long updateTime, String account_uuid, String type, Integer bindStatus);

    Integer countByOpenIdAndType(String openId, String type);

}
