package cn.taqu.account.dao;

import cn.taqu.account.model.AccountLabel;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 用户标签
 *
 * <AUTHOR>
 * @date 2024/11/25 10:56
 */
public interface AccountLabelDao extends cn.taqu.core.orm.base.NimbleDao<Long, AccountLabel> {

    /**
     * 默认字段
     */
    List<String> DEFAULT_FILED = Lists.newArrayList("id", "account_uuid", "label_ids", "create_time", "update_time");

    /**
     * 获取用户标签
     *
     * @param accountUuid
     * @return
     */
    AccountLabel getByUuid(String accountUuid);

}
