package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsCallerDao;
import cn.taqu.account.model.AccountsCaller;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

@Repository
public class AccountsCallerDaoImpl extends NimbleDaoImpl<Long, AccountsCaller> implements AccountsCallerDao {
    @Override
    public boolean uuidExists(String accountUuid) {
        Sql sql = Sql.build("select count(*) from accounts_caller where account_uuid=?", accountUuid);
        Integer count = this.queryForInteger(sql);
        return count != null && count > 0;
    }

    @Override
    public AccountsCaller findByUuid(String accountUuid) {
        Sql sql = Sql.build("select id, account_uuid, voice_status, video_status, create_time from accounts_caller where account_uuid=?", accountUuid);
        return this.get(sql);
    }
}
