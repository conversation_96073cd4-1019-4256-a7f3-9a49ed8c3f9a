package cn.taqu.account.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsActiveLogDao;
import cn.taqu.account.model.AccountsActiveLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

/**
 * <AUTHOR>
 * @Classname AccountsActiveLogDaoImpl
 * @Description 用户活跃记录表
 * @Date 2020/12/14 上午9:44
 */
@Repository
public class AccountsActiveLogDaoImpl extends NimbleDaoImpl<Long, AccountsActiveLog> implements AccountsActiveLogDao {
    @Override
    public void insertIgnore(String accountUuid, Integer activeTime) {
        String sqlStr = "insert ignore into accounts_active_log (account_uuid, active_time) values(?, ?)";
        Sql sql = Sql.build(sqlStr, accountUuid, activeTime);
        this.update(sql);
    }

    @Override
    public List<Integer> findByUuid(String accountUuid) {
        String sqlStr = "select active_time from accounts_active_log where account_uuid = ?";
        Sql sql = Sql.build(sqlStr, accountUuid);
        return this.queryForList(sql, Integer.class);
    }

    @Override
    public List<Long> findIdByUuid(String uuid) {
        String sqlStr = "select id from accounts_active_log where account_uuid = ? order by active_time desc";
        Sql sql = Sql.build(sqlStr, uuid);
        return this.queryForList(sql, Long.class);
    }

    @Override
    public List<AccountsActiveLog> getAccountsActive(Long activeTime, Long startId, Integer size) {
        String sqlStr = "select id,account_uuid,active_time from accounts_active_log where active_time = ? and id >= ? order by active_time desc, id asc limit ?";
        Sql sql = Sql.build(sqlStr, activeTime, startId, size);
        return this.query(sql);
    }


    @Override
    public Long getAccountsActiveStartIdInDay(Long activeTime) {
        Sql sql = Sql.build("select id from accounts_active_log where active_time = ? order by active_time desc, id asc limit 1", activeTime);
        return this.queryForLong(sql);
    }
}
