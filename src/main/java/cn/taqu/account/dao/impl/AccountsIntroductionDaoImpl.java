package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsIntroductionDao;
import cn.taqu.account.model.AccountsIntroduction;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:35
 */
@Repository
public class AccountsIntroductionDaoImpl extends NimbleDaoImpl<Long, AccountsIntroduction> implements AccountsIntroductionDao {

    @Override
    public AccountsIntroduction findByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsIntroduction.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid);
        sql.limit(1);
        List<AccountsIntroduction> introductions = this.query(sql);
        return null == introductions || introductions.size() == 0 ? null: introductions.get(0);
    }

}
