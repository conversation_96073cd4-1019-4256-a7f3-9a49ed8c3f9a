package cn.taqu.account.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsLocationInfoDao;
import cn.taqu.account.model.AccountsLocationInfo;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

/**
 * 用户位置信息Dao 实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-10-12 15:16
 */
@Repository
public class AccountsLocationInfoDaoImpl extends NimbleDaoImpl<Long, AccountsLocationInfo> implements AccountsLocationInfoDao {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public AccountsLocationInfo selectOneByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsLocationInfo.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid)
                .limit(1);
        List<AccountsLocationInfo> locationInfo = this.query(sql);
        return CollectionUtils.isEmpty(locationInfo) ? null : locationInfo.get(0);
    }

    @Override
    public void updateLocationInfo(Long id, String ip, String longitude, String longitudeCipher, String latitude, String latitudeCipher, Long cityId, Long createTime, Long updateTime, Integer dataSources, Integer useProvincialCapital) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_location_info                         \n");
        sb.append("    SET                                      \n");
        sb.append("    ip = :ip,                       \n");
        sb.append("    longitude = :longitude,                       \n");
        sb.append("    longitude_cipher = :longitudeCipher,                       \n");
        sb.append("    latitude = :latitude,                       \n");
        sb.append("    latitude_cipher = :latitudeCipher,                       \n");
        sb.append("    city_id = :cityId,                       \n");
        sb.append("    create_time = :createTime,                       \n");
        sb.append("    update_time = :updateTime,                       \n");
        sb.append("    data_sources = :dataSources,                       \n");
        sb.append("    use_provincial_capital = :useProvincialCapital                       \n");
        sb.append("    WHERE                                    \n");
        sb.append("    id= :id                                  \n");
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("ip", ip);
        params.put("longitude", longitude);
        params.put("longitudeCipher", longitudeCipher);
        params.put("latitude", latitude);
        params.put("latitudeCipher", latitudeCipher);
        params.put("cityId", cityId);
        params.put("createTime", createTime);
        params.put("updateTime", updateTime);
        params.put("dataSources", dataSources);
        params.put("useProvincialCapital", useProvincialCapital);
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }
}
