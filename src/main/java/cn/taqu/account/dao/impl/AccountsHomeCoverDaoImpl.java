package cn.taqu.account.dao.impl;

import cn.taqu.account.common.HomeCoverStatusEnum;
import cn.taqu.account.dao.AccountsHomeCoverDao;
import cn.taqu.account.model.AccountsHomeCover;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 09:55
 */
@Repository
public class AccountsHomeCoverDaoImpl extends NimbleDaoImpl<Long, AccountsHomeCover> implements AccountsHomeCoverDao {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public List<AccountsHomeCover> getHomeCoverList(Integer pageNum, Integer pageSize, Long startTime, Long endTime, Integer homeCoverStatus, String accontUuid, String operatorToken) {
        Sql sql = Sql.build(AccountsHomeCover.class, FIELDS_ALL);

        if(StringUtils.isNotBlank(accontUuid)) {
            sql.eq("account_uuid", accontUuid);
        }

        sql.ge("create_time", startTime)
                .le("create_time", endTime);
        if (homeCoverStatus != null) {//查询待审核的
            sql.eq("home_cover_status", homeCoverStatus);
        }
        if(StringUtils.isNotBlank(operatorToken)){
            sql.eq("operator", operatorToken);
        }

        sql.orderBy(" create_time asc ");
        sql.limit((pageNum - 1) * pageSize, pageSize);
        return this.query(sql);
    }

    @Override
    public void deleteByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("delete from accounts_home_cover where account_uuid = ? ", accountUuid);
        this.update(sql);
    }

    @Override
    public List<AccountsHomeCover> listByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsHomeCover.class, FIELDS_ALL);
        sql.eq("account_uuid", accountUuid);
        return this.query(sql);
    }

    @Override
    public AccountsHomeCover getById(Long id) {
        Sql sql = Sql.build(AccountsHomeCover.class, FIELDS_ALL);
        sql.eq("id", id);
        sql.limit(1);
        List<AccountsHomeCover> accountsHomeCovers = this.query(sql);
        return CollectionUtils.isEmpty(accountsHomeCovers) ? null : accountsHomeCovers.get(0);
    }

    @Override
    public void updateCheckPass(Long id, String photoUrl, Long now, String operator) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_home_cover                         \n");
        sb.append("    SET                                      \n");
        sb.append("    home_cover_status = :home_cover_status,   \n");
        sb.append("    pass_photo_url = :pass_photo_url,   \n");
        sb.append("    check_time = :check_time,   \n");
        sb.append("    operator = :operator   \n");
        sb.append("    WHERE id = :id                          \n");
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("home_cover_status", HomeCoverStatusEnum.PASS.getValue());
        params.put("pass_photo_url", photoUrl);
        params.put("check_time", now);
        params.put("operator", operator);
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }
}
