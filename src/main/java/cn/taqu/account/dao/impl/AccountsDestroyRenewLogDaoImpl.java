package cn.taqu.account.dao.impl;

import cn.taqu.account.model.AccountsDestroyRenewLog;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsDestroyRenewLogDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsDestroyRenewLog> implements cn.taqu.account.dao.AccountsDestroyRenewLogDao {

    @Override
    public List<AccountsDestroyRenewLog> getListByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsDestroyRenewLog.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid);
        return this.query(sql);
    }
}
