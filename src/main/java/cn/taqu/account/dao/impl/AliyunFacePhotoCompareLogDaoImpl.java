package cn.taqu.account.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.taqu.core.orm.Sql;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import cn.taqu.account.model.AliyunFacePhotoCompareLog;
import cn.taqu.account.search.AliyunFacePhotoCompareLogSearch;

@Repository
public class AliyunFacePhotoCompareLogDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AliyunFacePhotoCompareLog> implements cn.taqu.account.dao.AliyunFacePhotoCompareLogDao {


    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public List<AliyunFacePhotoCompareLog> getAliyunFacePhotoCompareLogByPage(AliyunFacePhotoCompareLogSearch search, Integer start, Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT     *            \n");
        sb.append("FROM aliyun_face_photo_compare_log  \n");
        sb.append("WHERE 1=1       \n");
        if (null != search.getStart_time()) {
            sb.append("AND create_time >= :startTime \n");
            params.put("startTime", search.getStart_time());
        }
        if (null != search.getEnd_time()) {
            sb.append("AND create_time <= :endTime \n");
            params.put("endTime", search.getEnd_time());
        }
        if (null != search.getVerify_status()){
            sb.append("AND status = :verifyStatus \n");
            params.put("verifyStatus",search.getVerify_status());
        }
        if(StringUtils.isNotEmpty(search.getAccount_uuid())){
            sb.append("AND account_uuid = :accountUuid \n");
            params.put("accountUuid",search.getAccount_uuid());
        }
        if (null != search.getGender_compare_result()){
            sb.append("AND gender_compare_result = :gender_compare_result \n");
            params.put("gender_compare_result", search.getGender_compare_result());
        }
        sb.append("ORDER BY create_time DESC \n");
        sb.append("LIMIT :start, :pageSize");
        params.put("start",start);
        params.put("pageSize",pageSize);

        return namedParameterJdbcTemplate.query(sb.toString(), params, new BeanPropertyRowMapper<>(AliyunFacePhotoCompareLog.class));
    }

    @Override
    public List<AliyunFacePhotoCompareLog> findListByAccountUuidAndCreateTime(String accountUuid, Long createTime) {
        Sql sql = Sql.build(AliyunFacePhotoCompareLog.class, FIELDS_ALL).eq("account_uuid", accountUuid).ge("create_time", createTime).orderBy("create_time desc");
        return this.query(sql);
    }

    @Override
    public void batchUpdateGender(List<Long> updateCompareLogIds, Integer accountGender, Integer basePhotoGender, Integer genderCompareResult) {
        Sql sql = Sql.build(" UPDATE aliyun_face_photo_compare_log set account_gender = ? , base_photo_gender = ? , gender_compare_result = ? where id in(" + StringUtils.join(updateCompareLogIds, ",") + ")", accountGender, basePhotoGender, genderCompareResult);
        this.update(sql);
    }

}
