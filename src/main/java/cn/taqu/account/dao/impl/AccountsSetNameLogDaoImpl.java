package cn.taqu.account.dao.impl;

import cn.taqu.account.model.AccountsSetNameLog;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsSetNameLogDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsSetNameLog> implements cn.taqu.account.dao.AccountsSetNameLogDao {

    @Override
    public long getNicknameLogByUuidCount(String account_uuid) {
        return this.queryForLong(Sql.build("select count(1) from accounts_set_name_log where account_uuid = ? and type = 2", account_uuid));
    }

    @Override
    public List<AccountsSetNameLog> getNicknameLogByUuid(String account_uuid, Integer start, Integer pageSize) {
        return this.query(Sql.build("select id, account_id, account_uuid, account_name_old, account_name_new, default_nick_name, old_default_nick_name, type, oper, oper_type, create_time from accounts_set_name_log where account_uuid = ? and type = 2 order by create_time desc limit ?, ?", account_uuid, start, pageSize));
    }

    @Override
    public List<AccountsSetNameLog> getByUuid(String accountUuid, int limit) {
        Sql sql = Sql.build(AccountsSetNameLog.class, Arrays.asList("account_name_new, account_name_old"))
                .eq("account_uuid", accountUuid)
                .orderBy("create_time desc")
                .limit(limit);
        return this.query(sql);
    }
}
