package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsMedalInfoDao;
import cn.taqu.account.model.AccountsMedalInfo;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AccountsMedalInfoDaoImpl extends NimbleDaoImpl<Long, AccountsMedalInfo> implements AccountsMedalInfoDao {
    @Override
    public List<String> findUuidByMedalId(Long medalId) {
        Sql sql = Sql.build("select account_uuid from accounts_medal_info where medal_id=?", medalId);
        return this.queryForList(sql, String.class);
    }

    @Override
    public boolean accountExists(String accountUuid) {
        Sql sql = Sql.build("select count(*) from accounts_medal_info where account_uuid=?", accountUuid);
        Integer count =  this.queryForInteger(sql);
        return count == null ? false : count > 0;
    }

    @Override
    public int updateAccountMedal(String accountUuid, Long medalId, Integer status, Long endTime, Long updateTime) {
        Sql sql = Sql.build("update accounts_medal_info set medal_id=?, status=?, end_time=?, update_time=? where account_uuid=?", medalId, status, endTime, updateTime, accountUuid);
        return this.update(sql);
    }

    @Override
    public int batchUnbindAccountMedal(List<String> accountUuidList, Long updateTime) {
        int step = 200;
        int size = accountUuidList.size();
        int count = 0;
        for(int i=0; i<size;) {
            List<String> subAccountUuidList = accountUuidList.subList(i, Math.min(i+=step, size));
            String sql = "update accounts_medal_info set status=?, update_time=? where account_uuid in ('" + String.join("','", subAccountUuidList) + "')";
            count += this.update(Sql.build(sql, 2, updateTime));
        }
        return count;
    }

    @Override
    public List<AccountsMedalInfo> getListByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsMedalInfo.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid);
        return this.query(sql);
    }
}
