package cn.taqu.account.dao.impl;

import cn.taqu.account.model.PersonalityLabel;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class PersonalityLabelDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.PersonalityLabel> implements cn.taqu.account.dao.PersonalityLabelDao {
    @Override
    public List<PersonalityLabel> findByIds(Collection<Long> ids) {
        Sql sql = Sql.build(PersonalityLabel.class, Arrays.asList("id", "content", "description", "type", "sex", "status", "second_type", "sort")).in("id", ids);
        return this.query(sql);
    }

    @Override
    public PersonalityLabel getByIdFromDb(Long id) {
        Sql sql = Sql.build("select id, content, description, type, sex, status, second_type, sort from personality_label where id = ?", id);
        return this.get(sql);
    }

    @Override
    public int updateStatusById(Integer status, Long id) {
        Sql sql = Sql.build("update personality_label set status = ? where id = ?", status, id);
        return this.update(sql);
    }

    @Override
    public List<PersonalityLabel> getSimpleByTypeAndSex(Integer type, Integer sex) {
        Sql sql = Sql.build("select id, content from personality_label where status=0 and type=? and (sex=? or sex=3)", type, sex);
        return this.query(sql, PersonalityLabel.class);
    }

    @Override
    public List<Long> getAllId() {
        Sql sql = Sql.build("select id from personality_label");
        return this.queryForList(sql, Long.class);
    }

    @Override
    public List<Long> getAllByType(Integer type) {
        Sql sql = Sql.build("select id from personality_label where type = ?", type);
        return this.queryForList(sql, Long.class);
    }

    @Override
    public Long sortIsExist(Integer type, Integer sort) {
        Sql sql = Sql.build("select id from personality_label where type = ? and sort = ?", type, sort);
        return this.queryForLong(sql);
    }
}
