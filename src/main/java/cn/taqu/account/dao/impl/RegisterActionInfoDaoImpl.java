package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.RegisterActionInfoDao;
import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

@Repository
public class RegisterActionInfoDaoImpl extends NimbleDaoImpl<Long, RegisterActionInfo> implements RegisterActionInfoDao {
    @Override
    public RegisterActionInfo getByUuid(String accountUuid) {
        return this.get(Sql.build(RegisterActionInfo.class, FIELDS_ALL).eq("account_uuid", accountUuid, false));
    }
}
