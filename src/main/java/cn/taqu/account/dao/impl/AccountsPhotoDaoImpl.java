package cn.taqu.account.dao.impl;

import java.util.*;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.core.orm.Sql;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsPhotoDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsPhoto> implements cn.taqu.account.dao.AccountsPhotoDao {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public List<AccountsPhoto> findAvatarByUuid(String accountUuid) {
        return this.query(Sql.build("select id, account_uuid, photo_url, bucket, seq, status, verify_status,error_msg, create_time, update_time, like_num from accounts_photo where account_uuid = ? and seq = 1", accountUuid));
    }

    @Override
    public List<AccountsPhoto> findByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsPhoto.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid)
                .orderBy("account_uuid, seq asc");
        return this.query(sql);
    }
    
    @Override
    public AccountsPhoto findByUuidAndSeq(String accountUuid, Integer seq) {
        Sql sql = Sql.build(AccountsPhoto.class, FIELDS_ALL)
            .eq("account_uuid", accountUuid)
            .eq("seq", seq).limit(1);
        return this.get(sql);
    }

    @Override
    public List<AccountsPhoto> findAccountsPhotoInIds(Collection<Long> ids) {
        return this.query(Sql.build(AccountsPhoto.class, FIELDS_ALL).in("id", ids));
    }

    @Override
    public List<AccountsPhoto> findByUuidList(List<String> uuidList) {
        Sql sql = Sql.build(AccountsPhoto.class, FIELDS_ALL)
                .in("account_uuid", uuidList)
                .orderBy("account_uuid, seq asc");
        return this.query(sql);
    }

    @Override
    public List<AccountsPhoto> getNoCheckPhoto(String accountUuid) {
        Sql sql = Sql.build("select id, photo_url from accounts_photo where account_uuid=? and " +
                        "(status = ? or status = ? or status = ? or status = ? or status = ?) order by seq desc", accountUuid,
                AccountsPhoto.Status.NO_CHECK.getValue(),
                AccountsPhoto.Status.RE_CHECK.getValue(),
                AccountsPhoto.Status.AUTO_CHECK.getValue(),
                AccountsPhoto.Status.NO_CHECK_ACCOUNT.getValue(),
                AccountsPhoto.Status.REVIEW_90.getValue());
        return this.query(sql);
    }

    @Override
    public Long getCountByVerifyStatus(String accountUuid,Integer verifyStatus) {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT count(*)                     \n");
        sb.append("FROM accounts_photo  \n");
        sb.append("WHERE 1=1       \n");
        sb.append("AND account_uuid = :accountUuid  \n");
        sb.append("AND verify_status = :verifyStatus \n");

        Map<String, Object> params = new HashMap<>();
        params.put("verifyStatus", verifyStatus);
        params.put("accountUuid",accountUuid);

        return namedParameterJdbcTemplate.queryForObject(sb.toString(), params, Long.class);
    }

    @Override
    public int getCountByStatus(String accountUuid, int status, boolean containAvatar){
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT count(*)                     \n");
        sb.append("FROM accounts_photo  \n");
        sb.append("WHERE 1=1       \n");
        sb.append("AND account_uuid = :accountUuid  \n");
        sb.append("AND status = :status \n");
        if (!containAvatar) {
            sb.append("AND seq > 1 \n");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("status", status);
        params.put("accountUuid",accountUuid);

        return namedParameterJdbcTemplate.queryForObject(sb.toString(), params, Integer.class);
    }

    @Override
    public void updateVerifyStatusById(Long id,Integer verifyStatus,String errorMsg){
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_photo                         \n");
        sb.append("    SET                                      \n");
        sb.append("    verify_status = :verifyStatus,                       \n");
        sb.append("    error_msg = :errorMsg                       \n");
        sb.append("    WHERE                                    \n");
        sb.append("    id= :id                                  \n");
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("verifyStatus",verifyStatus);
        params.put("errorMsg",errorMsg);
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }

    @Override
    public void updateVerifyStatusByIdAndPhotoUrl(Long id, Integer verifyStatus, String errorMsg, String photoUrl) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_photo                         \n");
        sb.append("    SET                                      \n");
        sb.append("    verify_status = :verifyStatus,                       \n");
        sb.append("    error_msg = :errorMsg                       \n");
        sb.append("    WHERE                                    \n");
        sb.append("    id= :id                                  \n");
        sb.append("    AND photo_url= :photoUrl                                  \n");
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("verifyStatus",verifyStatus);
        params.put("errorMsg",errorMsg);
        params.put("photoUrl", photoUrl);
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }

    @Override
    public void updateVerifyStatusAndStatusByUuid(String accountUuid, Integer verifyStatus, String errorMsg) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_photo                         \n");
        sb.append("    SET                                      \n");
        sb.append("    verify_status = :verifyStatus,                       \n");
        sb.append("    status = :status,                       \n");
        sb.append("    error_msg = :errorMsg                       \n");
        sb.append("    WHERE                                    \n");
        sb.append("    account_uuid= :accountUuid                                  \n");
        Map<String, Object> params = new HashMap<>();
        params.put("accountUuid", accountUuid);
        params.put("verifyStatus", verifyStatus);
        params.put("status", AccountsPhoto.Status.ONLY_AUTO_CHECK.getValue());
        params.put("errorMsg", errorMsg);
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }

    @Override
    public AccountsPhoto findById(Long id) {
        return this.get(Sql.build("select id, account_uuid, photo_url, bucket, seq, status, verify_status, face_score, error_msg, create_time, update_time, risk_description, risk_photo_url, like_num from accounts_photo where id = ? limit 1", id));
    }

    @Override
    public List<AccountsPhoto> getPhotoListInStatus(String accountUuid, List<Integer> statusList) {
        Sql sql = Sql.build(AccountsPhoto.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid)
                .in("status", statusList)
                .orderBy("update_time desc")
                .limit(100);
        return this.query(sql);
    }

    @Override
    public void updateRiskUrlAndRiskDesc(Long photoId, String riskPhotoUrl, String riskDescription) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_photo                         \n");
        sb.append("    SET                                      \n");
        sb.append("    risk_photo_url = :riskPhotoUrl,                       \n");
        sb.append("    risk_description = :riskDescription                       \n");
        sb.append("    WHERE                                    \n");
        sb.append("    id= :id                                  \n");
        Map<String, Object> params = new HashMap<>();
        params.put("id", photoId);
        params.put("riskPhotoUrl", riskPhotoUrl);
        params.put("riskDescription", riskDescription);
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }

    @Override
    public AccountsPhoto getAccountAvatar(String accountUuid, Integer verifyStatus, boolean ifPassCheck) {
        Sql sql = Sql.build(AccountsPhoto.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid)
                .eq("seq", 1);
        if(null != verifyStatus){
            sql.eq("verify_status", verifyStatus);
        }
        if(ifPassCheck){
            sql.eq("status", AccountsPhoto.Status.PASS.getValue());
        }
        sql.limit(1);
        List<AccountsPhoto> photos = this.query(sql);
        return null == photos || photos.size() == 0? null: photos.get(0);
    }

    @Override
    public void updateCoverOnlyAutoCheck(Long id) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_photo                         \n");
        sb.append("    SET                                      \n");
        sb.append("    status = :onlyAutoCheck                       \n");
        sb.append("    WHERE                                    \n");
        sb.append("    id= :id  and seq != 1                              \n");
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("onlyAutoCheck", AccountsPhoto.Status.ONLY_AUTO_CHECK.getValue());
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }

    @Override
    public void updateReviewCoverStatus(Long id) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_photo                         \n");
        sb.append("    SET                                      \n");
        sb.append("    status = :onlyAutoCheck                       \n");
        sb.append("    WHERE                                    \n");
        sb.append("    id= :id                                  \n");
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("onlyAutoCheck", AccountsPhoto.Status.NO_CHECK.getValue());
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }

    @Override
    public List<AccountsPhoto> queryNotAudit(Long expireTime, Long startTime, Integer seq, Integer status, Integer limit) {
        Sql sql = Sql.build(AccountsPhoto.class, FIELDS_ALL)
            .eq("status", status)
            .lt("create_time", expireTime)
            .gt("create_time", startTime)
            .eq("seq", seq)
            .orderBy("create_time desc")
            .limit(limit);
        return this.query(sql);
    }

    @Override
    public void batchUpdateStatus(List<Long> ids, Integer status, Integer verifyStatus) {
        Sql sql = Sql.build("update accounts_photo set status = ? , verify_status = ? where id in (" + StringUtils.join(ids, ",") + ")", status, verifyStatus);
        update(sql);
    }

}
