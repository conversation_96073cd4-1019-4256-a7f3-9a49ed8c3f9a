package cn.taqu.account.dao.impl;

import cn.taqu.account.model.MembersAppInfo;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class MembersAppInfoDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.MembersAppInfo> implements cn.taqu.account.dao.MembersAppInfoDao {

    // 根据member_id更新members_app_info表
    @Override
    public void updateByMemberId(Long appVersion, Integer appcode, Integer cloned, String place, Integer platformId, Long updateTime, Long memberId) {
        this.update(Sql.build("update members_app_info set app_version=?, appcode =?, cloned=?, place=?, platform_id=?, update_time=? where member_id=?", appVersion, appcode, cloned, place, platformId, updateTime, memberId));
    }

    @Override
    public MembersAppInfo findOneByMemberId(Long memberId, boolean master) {
        Collection<String> fields = Arrays.asList("id","member_id","app_version","appcode","place","platform_id","create_time","update_time","is_jailbroken", "cloned", "notice_enable");
        Sql sql = Sql.build(MembersAppInfo.class, fields).eq("member_id", memberId, false);
        if(master) {
            sql = sql.masterDB();
        }
        return this.get(sql);
    }

    @Override
    public List<MembersAppInfo> findByMemberIds(Collection<Long> memberIdList) {
        Collection<String> fields = Arrays.asList("id","member_id","app_version","appcode","place","platform_id","create_time","update_time","is_jailbroken", "cloned", "notice_enable");
        return this.query(Sql.build(MembersAppInfo.class, fields).in("member_id", memberIdList));
    }

    @Override
    public int updateNoticeEnable(Integer noticeEnable, Long memberId) {
        return this.update(Sql.build("update members_app_info set notice_enable=? where member_id=?", noticeEnable
        , memberId));
    }
}
