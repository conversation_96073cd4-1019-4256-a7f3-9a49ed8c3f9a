package cn.taqu.account.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.NickNameRiskVerifyDao;
import cn.taqu.account.model.NickNameRiskVerify;
import cn.taqu.core.orm.Sql;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/23.
 */
@Repository
public class NickNameRiskVerifyDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, NickNameRiskVerify> implements NickNameRiskVerifyDao {
    

    @Override
    public void passVerify(Long[] ids, Integer status, String operator) {
        Sql sql = Sql.build("update nick_name_risk_verify set status = ?,operator = ?", status, operator).in("id", ids);
        update(sql);
    }
    
    @Override
    public void passVerify(Long id, Integer status, String operator) {
        Sql sql = Sql.build("update nick_name_risk_verify set status = ?,operator = ?", status, operator).eq("id", id);
        update(sql);
    }

    @Override
    public List<NickNameRiskVerify> findListByIds(Long[] ids) {
        Sql sql = Sql.build(NickNameRiskVerify.class, FIELDS_ALL).in("id", ids);
        return query(sql);
    }

    @Override
    public void deleteByUuid(String uuid) {
        Sql sql = Sql.build("delete from nick_name_risk_verify where account_uuid = ? ", uuid);
        update(sql);
    }

    @Override
    public NickNameRiskVerify findByUuid(String uuid) {
        Sql sql = Sql.build(NickNameRiskVerify.class, FIELDS_ALL).eq("account_uuid", uuid);
        return this.get(sql);
    }

}
