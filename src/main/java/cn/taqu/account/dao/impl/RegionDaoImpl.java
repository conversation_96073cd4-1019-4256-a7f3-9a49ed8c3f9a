package cn.taqu.account.dao.impl;

import org.springframework.stereotype.Repository;

import cn.taqu.account.model.Region;
import cn.taqu.core.orm.Sql;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class RegionDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.Region> implements cn.taqu.account.dao.RegionDao {

    @Override
	public Region getById(Long regionId) {
        Sql sql = Sql.build("select id,sign,parent_id,region_path,level,region_name,is_unavailable,sort,postcode,longitude,latitude from region where id = ?", regionId);
        return this.get(sql);
	}

    @Override
    public List<Region> getByIdList(Collection<Long> idList) {
	    Sql sql = Sql.build(Region.class, Arrays.asList("id", "sign", "parent_id", "region_path", "level", "region_name", "is_unavailable", "sort", "postcode", "longitude", "latitude")).in("id", idList);
        return this.query(sql);
    }

    @Override
    public Region getByRegionName(String regionName) {
        Sql sql = Sql.build("select id,sign,parent_id,region_path,level,region_name,is_unavailable,sort,postcode,longitude,latitude from region where region_name = ? order by id limit 1", regionName);
        return this.get(sql);
    }
    
}
