package cn.taqu.account.dao.impl;


import cn.taqu.account.model.AliyunLiveFaceDetect;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AliyunLiveFaceDetectDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AliyunLiveFaceDetect> implements cn.taqu.account.dao.AliyunLiveFaceDetectDao {

    @Override
    public AliyunLiveFaceDetect getInfoByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("select  * from aliyun_live_face_detect where account_uuid = ?", accountUuid);
        return this.get(sql);
    }

    @Override
    public AliyunLiveFaceDetect findById(Long id) {
        Sql sql = Sql.build("select  * from aliyun_live_face_detect where id = ?", id);
        return this.get(sql);
    }

    @Override
    public List<AliyunLiveFaceDetect> getByPage(Integer offset, Integer limit) {
        Sql sql = Sql.build("select id,account_uuid from aliyun_live_face_detect  order by id limit ?,?",offset,limit);
        return this.query(sql);
    }

    @Override
    public void updateLiveFaceDetectGenderById(Long id, Integer accountGender, Integer basePhotoGender, Integer genderCompareResult) {
        this.update(Sql.build("update aliyun_live_face_detect set account_gender = ? , base_photo_gender = ? , gender_compare_result = ? where id=? ",accountGender, basePhotoGender, genderCompareResult, id));
    }

    @Override
    public AliyunLiveFaceDetect findOneByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("select  * from aliyun_live_face_detect where account_uuid = ? limit 1", accountUuid);
        return this.get(sql);
    }

    @Override
    public String getBasePhotoUrlByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("select base_photo_url from aliyun_live_face_detect where account_uuid = ? order by id desc limit 1", accountUuid);
        return this.queryForString(sql);
    }

}
