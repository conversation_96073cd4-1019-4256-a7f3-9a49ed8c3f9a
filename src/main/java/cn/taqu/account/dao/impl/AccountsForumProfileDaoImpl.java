package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsForumProfileDao;
import cn.taqu.account.model.AccountsForumProfile;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsForumProfileDaoImpl extends NimbleDaoImpl<Long, AccountsForumProfile> implements AccountsForumProfileDao {

    @Override
    public Integer getAccountLevelByAccountUuid(String accountUuid) {
        return this.queryForInteger(Sql.build("select account_level from accounts_forum_profile where account_uuid=?", accountUuid));
    }

    @Override
    public AccountsForumProfile findByAccountUuid(String accountUuid) {
        return this.get(Sql.build("select id, account_id, account_uuid, account_level, account_actor, experience, total_posts, invaild_posts, unvaild_posts, total_reviews, invaild_reviews, unvaild_reviews, total_reply_num, invaild_reply_num, unvaild_reply_num, total_mail_num, total_mail_letter_num, total_warnings, total_forbid_token, total_forbid_account, last_speak_time, total_receive_reviews, total_receive_replys, last_online_time, browse_posts, peipei_experience, peipei_level, driver_level, kilometer  from accounts_forum_profile where account_uuid=?", accountUuid));
    }

    @Override
    public void batchUpdateActor(List<String> accountUuidList, int accountActor) {
        int step = 200;
        int size = accountUuidList.size();
        for(int i=0; i<size; ) {
            List<String> subAccountUuidList = accountUuidList.subList(i, Math.min(i+=step, size));
            String sql = "update accounts_forum_profile set account_actor = " + accountActor + " where account_uuid in ('" + String.join("','", subAccountUuidList) + "')";
            this.update(Sql.build(sql));
        }
    }

}
