package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.RandomNicknameDao;
import cn.taqu.account.model.RandomNickname;
import cn.taqu.account.search.RandomNicknameSearch;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class RandomNicknameDaoImpl extends NimbleDaoImpl<Long, RandomNickname> implements RandomNicknameDao {


    @Override
    public PageData<RandomNickname> findPageList(RandomNicknameSearch search) {
        Sql sql = Sql.build("select rg.random_group_name,rg.parts_of_speech,rn.id, rn.random_nickname, rn.sex_type, rn.status, rn.group_id,rn.cloned, rn.create_time, rn.update_time from random_nickname rn left join random_group rg on rn.group_id=rg.id")
                .eq("rg.id", search.getRandomGroupId())
                .eq("rn.sex_type", search.getSexType())
                .eq("rg.parts_of_speech", search.getPartsOfSpeech()).eq("rn.cloned", search.getCloned());
        return queryForPage(sql, search.getPage(), search.getRows());
    }

    @Override
    public List<RandomNickname> findByGroupId(Long groupId) {
        Sql sql = Sql.build(RandomNickname.class, FIELDS_ALL).eq("group_id", groupId);
        return query(sql);
    }

    @Override
    public RandomNickname findById(Long id) {
        Sql sql = Sql.build(RandomNickname.class, FIELDS_ALL).eq("id", id);
        return get(sql);
    }

    @Override
    public List<RandomNickname> countNum() {
        Sql sql = Sql.build("SELECT group_id,COUNT(group_id) as countNum FROM random_nickname GROUP BY group_id");
        return query(sql);
    }
}
