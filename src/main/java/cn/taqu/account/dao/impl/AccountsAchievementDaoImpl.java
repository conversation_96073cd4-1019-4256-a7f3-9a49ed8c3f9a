package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsAchievementDao;
import cn.taqu.account.model.AccountsAchievement;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

@Repository
public class AccountsAchievementDaoImpl extends NimbleDaoImpl<Long, AccountsAchievement> implements AccountsAchievementDao {

    @Override
    public boolean achievementExists(String accountUuid, Long achievementId, Integer level) {
        Sql sql = Sql.build("select count(*) from accounts_achievement where account_uuid = ? and achievement_id = ? and achievement_level = ?", accountUuid, achievementId, level);
        Integer count = this.queryForInteger(sql);
        return count != null && count > 0;
    }

    @Override
    public int setAdornByIdList(int isAdorn, List<Long> idList) {
        Sql sql = Sql.build("update accounts_achievement set is_adorn = ? ", isAdorn).in("id", idList);
        return this.update(sql);
    }

    @Override
    public int setAdornByUuid(int isAdorn, String accountUuid, List<Long> excludeAchievementIdList) {
        String sql = "update accounts_achievement set is_adorn = ? where account_uuid = ?";

        int excludeSize = excludeAchievementIdList == null ? 0 : excludeAchievementIdList.size();
        final Object[] values = new Object[excludeSize + 2];
        values[0] = isAdorn;
        values[1] = accountUuid;
        if(excludeSize > 0) {
            for(int i=0; i<excludeSize; i++) {
                values[i+2] = excludeAchievementIdList.get(i);
            }
            sql += " and achievement_id not in (" + StringUtils.repeat("?", ",", excludeSize) + ")";
        }

        return this.update(Sql.build(sql, values));
    }

    @Override
    public int setAdorn(int isAdorn, String accountUuid, Long achievementId, Integer level) {
        Sql sql = Sql.build("update accounts_achievement set is_adorn = ? where account_uuid = ? and achievement_id = ? and achievement_level = ?", isAdorn, accountUuid, achievementId, level);
        return this.update(sql);
    }

    @Override
    public int setAdorn(int isAdorn, String accountUuid, Long achievementId) {
        Sql sql = Sql.build("update accounts_achievement set is_adorn = ? where account_uuid = ? and achievement_id = ?", isAdorn, accountUuid, achievementId);
        return this.update(sql);
    }

    @Override
    public int delById(Long id) {
        Sql sql = Sql.build("delete from accounts_achievement where id=?", id);
        return this.update(sql);
    }

    @Override
    public List<AccountsAchievement> getAccountByAchievementId(String accountUuid, Long achievementId) {
        Sql sql = Sql.build(AccountsAchievement.class, Arrays.asList("id", "achievement_id", "achievement_level", "is_adorn"))
                .eq("account_uuid", accountUuid)
                .eq("achievement_id", achievementId);
        return this.query(sql);
    }

    @Override
    public List<AccountsAchievement> getListByUuids(String accountUuid) {
        Sql sql = Sql.build(AccountsAchievement.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid);
        return this.query(sql);
    }
}
