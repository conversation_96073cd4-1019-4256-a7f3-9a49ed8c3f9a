package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.ReRegisterWhitelistDao;
import cn.taqu.account.model.ReRegisterWhitelist;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

@Repository
public class ReRegisterWhitelistDaoImpl extends NimbleDaoImpl<Long, ReRegisterWhitelist> implements ReRegisterWhitelistDao {

    @Override
    public void updateStatusByTime(long startTime, long endTime) {
        String sqlStr = "update reregister_whitelist set status = 0 where create_time >= ? and create_time<= ? and status = 1";
        Sql sql = Sql.build(sqlStr,startTime,endTime);
        this.update(sql);
    }

    @Override
    public Integer getOneByMobile(String mobile) {
        String sqlStr = "select count(*) from reregister_whitelist where status = 1 and mobile = ?  order by id desc limit 1 ";
        Sql sql = Sql.build(sqlStr,mobile);
        return this.queryForInteger(sql);
    }
    
    @Override
    public Integer getOneByMobileDigest(String mobileDigest) {
        String sqlStr = "select count(*) from reregister_whitelist where status = 1 and mobile_digest = ?  order by id desc limit 1 ";
        Sql sql = Sql.build(sqlStr,mobileDigest);
        return this.queryForInteger(sql);
    }

    @Override
    public Integer getOneByOpenid(Integer type, String openid) {
        String sqlStr = "select count(*) from reregister_whitelist where status = 1 and openid_type = ? and openid = ? order by id desc limit 1 ";
        Sql sql = Sql.build(sqlStr, type, openid);
        return this.queryForInteger(sql);
    }
}
