package cn.taqu.account.dao.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import cn.taqu.account.model.PersonalProfileInfo;
import cn.taqu.core.orm.Sql;

@Repository
public class PersonalProfileInfoDaoImple extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.PersonalProfileInfo> implements cn.taqu.account.dao.PersonalProfileInfoDao {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;


    @Override
    public List<PersonalProfileInfo> getByUuid(String accountUuid) {
        Sql sql = Sql.build(PersonalProfileInfo.class, FIELDS_ALL).eq("account_uuid", accountUuid);
        return this.query(sql);
    }

}
