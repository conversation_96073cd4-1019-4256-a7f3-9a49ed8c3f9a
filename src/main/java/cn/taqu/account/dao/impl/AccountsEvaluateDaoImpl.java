package cn.taqu.account.dao.impl;

import java.util.Arrays;
import java.util.Collection;

import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsEvaluateDao;
import cn.taqu.account.model.AccountsEvaluate;
import cn.taqu.core.orm.base.NimbleDaoImpl;

@Repository
public class AccountsEvaluateDaoImpl extends NimbleDaoImpl<Long, AccountsEvaluate> implements AccountsEvaluateDao {
    private static Collection<String> fieldList = Arrays.asList("from_account_uuid", "to_account_uuid", "type", "label_ids", "create_time", "update_time");

}
