package cn.taqu.account.dao.impl;

import cn.taqu.account.common.HomeCoverStatusEnum;
import cn.taqu.account.dao.AccountsHomeCoverLogDao;
import cn.taqu.account.model.AccountsHomeCoverLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 09:55
 */
@Repository
public class AccountsHomeCoverLogDaoImpl extends NimbleDaoImpl<Long, AccountsHomeCoverLog> implements AccountsHomeCoverLogDao {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public List<AccountsHomeCoverLog> listByUuid(String accountUuid, Integer homeCoverStatus) {
        Sql sql = Sql.build(AccountsHomeCoverLog.class, FIELDS_ALL);
        sql.eq("account_uuid", accountUuid);
        if(homeCoverStatus != null){
            sql.eq("home_cover_status", homeCoverStatus);
        }
        return this.query(sql);
    }

    @Override
    public List<AccountsHomeCoverLog> listByCoverId(Long coverId) {
        Sql sql = Sql.build(AccountsHomeCoverLog.class, FIELDS_ALL);
        sql.eq("home_cover_id", coverId);
        sql.limit(10);
        return this.query(sql);
    }

    @Override
    public void updateSkipStatusByIds(List<Long> ids) {
        StringBuilder sb = new StringBuilder();
        sb.append("    UPDATE                                   \n");
        sb.append("    accounts_home_cover_log                         \n");
        sb.append("    SET                                      \n");
        sb.append("    home_cover_status = :home_cover_status   \n");
        sb.append("    WHERE id in (:ids)                          \n");
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("home_cover_status", HomeCoverStatusEnum.SKIP.getValue());
        namedParameterJdbcTemplate.update(sb.toString(), params);
    }

    @Override
    public List<AccountsHomeCoverLog> getHomeCoverLogList(Integer pageNum, Integer pageSize, Long startTime, Long endTime, Integer homeCoverStatus, String accontUuid, String operatorToken) {
        Sql sql = Sql.build(AccountsHomeCoverLog.class, FIELDS_ALL);

        if(StringUtils.isNotBlank(accontUuid)) {
            sql.eq("account_uuid", accontUuid);
        }

        sql.ge("check_time", startTime)
                .le("check_time", endTime);
        if (homeCoverStatus != null) {//查询待审核的
            sql.eq("home_cover_status", homeCoverStatus);
        }
        if(StringUtils.isNotBlank(operatorToken)){
            sql.eq("operator", operatorToken);
        }

        sql.orderBy(" check_time asc ");
        sql.limit((pageNum - 1) * pageSize, pageSize);
        return this.query(sql);
    }

    @Override
    public List<AccountsHomeCoverLog> getHomeCoverLogListReviewCheck(String accontUuid, List<Integer> status) {
        Sql sql = Sql.build(AccountsHomeCoverLog.class, FIELDS_ALL);
        sql.eq("account_uuid", accontUuid);
        sql.in("home_cover_status", status);
        sql.orderBy("id desc");
        sql.limit(100);
        return this.query(sql);
    }

}
