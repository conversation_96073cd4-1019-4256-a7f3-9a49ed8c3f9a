package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountFreezeAdminLogoutLogDao;
import cn.taqu.account.model.AccountFreezeAdminLogoutLog;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 15:27
 */
@Repository
public class AccountFreezeAdminLogoutLogDaoImpl extends NimbleDaoImpl<Long, AccountFreezeAdminLogoutLog> implements AccountFreezeAdminLogoutLogDao {
    @Override
    public PageData<AccountFreezeAdminLogoutLog> getFreezeAdminLogoutLogList(String uuid, String operator, Long startTime, Long endTime, Integer pageNo, Integer pageSize) {
        Sql sql = Sql.build(AccountFreezeAdminLogoutLog.class, FIELDS_ALL);
        if (StringUtils.isNotBlank(uuid)) {
            sql = sql.eq("account_uuid", uuid);
        }

        if (StringUtils.isNotBlank(operator)) {
            sql = sql.eq("operator", operator);
        }

        if (startTime != null && startTime > 0) {
            sql = sql.ge("create_time", startTime);
        }

        if (endTime != null && endTime > 0) {
            sql = sql.lt("create_time", endTime);
        }

        sql = sql.orderBy("create_time desc, id desc");
        return queryForPage(sql, pageNo, pageSize);
    }
}
