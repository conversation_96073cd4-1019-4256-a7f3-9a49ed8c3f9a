package cn.taqu.account.dao.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import cn.taqu.account.model.VoiceSignInfo;
import cn.taqu.core.orm.Sql;

@Repository
public class VoiceSignInfoDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.VoiceSignInfo> implements cn.taqu.account.dao.VoiceSignInfoDao {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;


    @Override
    public VoiceSignInfo findLastestOneByUuidAndStatus(String accountUuid,Integer status) {
        Sql sql = Sql.build("select  * from voice_sign_info where account_uuid = ? and status = ? order by id desc limit 1", accountUuid,status);
        return this.get(sql);
    }

    @Override
    public VoiceSignInfo getLastOneByUuid(String accountUuid) {
        Sql sql = Sql.build(VoiceSignInfo.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid).orderBy("id desc").limit(1);
        return this.get(sql);
    }

    @Override
    public List<VoiceSignInfo> queryNotAudit(Long expireTime, Long startTime, Integer status, Integer limit) {
        Sql sql = Sql.build(VoiceSignInfo.class, FIELDS_ALL)
            .eq("status", status)
            .lt("create_time", expireTime)
            .gt("create_time", startTime)
            .orderBy("create_time desc")
            .limit(limit);
        return this.query(sql);
    }
}
