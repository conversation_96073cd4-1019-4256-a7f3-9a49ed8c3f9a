package cn.taqu.account.dao.impl;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsGrayListDao;
import cn.taqu.account.model.AccountsGrayList;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsGrayListDaoImpl extends NimbleDaoImpl<Long, AccountsGrayList> implements AccountsGrayListDao {

	@Autowired
	private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

	@Override
	public List<AccountsGrayList> getByUuid(String uuid) {
		return this.query((Sql.build(
				"select id, account_uuid, token, account_name, remark, operate_name, create_time, update_time from accounts_gray_list where account_uuid = ? ",
				uuid)), AccountsGrayList.class);
	}

	@Override
	public void deleteInToken(Collection<String> tokenList) {
		if (CollectionUtils.isEmpty(tokenList)) {
			return;
		}

		Sql sql = Sql.build("delete from accounts_gray_list");
		sql.in("token", tokenList);

		this.update(sql);
	}

	@Override
	public List<AccountsGrayList> getByToken(String token) {
		return this.query((Sql.build(
				"select id, account_uuid, token, account_name, remark, operate_name, create_time, update_time from accounts_gray_list where token = ? ",
				token)), AccountsGrayList.class);
	}

	@Override
	public void deleteInUuid(Collection<String> uuidList) {
		if (CollectionUtils.isEmpty(uuidList)) {
			return;
		}

		Sql sql = Sql.build("delete from accounts_gray_list");
		sql.in("account_uuid", uuidList);

		this.update(sql);
	}

	@Override
	public AccountsGrayList findByAccountUuidAndToken(String accountUuid, String token) {
		return this.get((Sql.build(
				"select id, account_uuid, token, account_name, remark, operate_name, create_time, update_time from accounts_gray_list where account_uuid = ? and token = ? order by create_time desc limit 1",
				accountUuid, token)), AccountsGrayList.class);
	}

	@Override
	public AccountsGrayList getOneByToken(String token) {
		return this.get((Sql.build(
				"select id, account_uuid, token, account_name, remark, operate_name, create_time, update_time from accounts_gray_list where token = ? order by create_time desc limit 1",
				token)), AccountsGrayList.class);
	}

	@Override
	public AccountsGrayList getOneByUuid(String accountUuid) {
		return this.get((Sql.build(
				"select id, account_uuid, token, account_name, remark, operate_name, create_time, update_time from accounts_gray_list where account_uuid = ? order by create_time desc limit 1",
				accountUuid)), AccountsGrayList.class);
	}

	@Override
	public Integer countByToken(String token) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT count(*)                     \n");
		sb.append("FROM accounts_gray_list t  \n");
		sb.append("WHERE 1=1       \n");
		sb.append("AND t.token = :token \n");
		params.put("token", token);
		return namedParameterJdbcTemplate.queryForObject(sb.toString(), params, Integer.class);
	}

	@Override
	public Integer countByUuid(String accountUuid) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT count(*)                     \n");
		sb.append("FROM accounts_gray_list t  \n");
		sb.append("WHERE 1=1       \n");
		sb.append("AND t.account_uuid = :accountUuid \n");
		params.put("accountUuid", accountUuid);
		return namedParameterJdbcTemplate.queryForObject(sb.toString(), params, Integer.class);
	}
}
