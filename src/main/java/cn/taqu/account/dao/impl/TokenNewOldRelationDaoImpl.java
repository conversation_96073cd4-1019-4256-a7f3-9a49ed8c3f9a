package cn.taqu.account.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.TokenNewOldRelationDao;
import cn.taqu.account.model.TokenNewOldRelation;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

@Repository
public class TokenNewOldRelationDaoImpl extends NimbleDaoImpl<Long, TokenNewOldRelation>
    implements TokenNewOldRelationDao {

    @Override
    public TokenNewOldRelation getByNewOldToken(String newToken, String oldToken) {
        Sql sql = Sql.build(TokenNewOldRelation.class, FIELDS_ALL);
        sql.eq("new_token", newToken, false).eq("old_token", oldToken);
        return this.get(sql);
    }

    @Override
    public void insertIgnore(String newToken, String oldToken, Long createTime, Long updateTime) {
        Sql sql = Sql.build(
            "insert ignore into token_new_old_relation (new_token, old_token, create_time, update_time)  values( ?, ?, ?, ?)", newToken, oldToken, createTime, updateTime);
        this.update(sql);
    }

    @Override
    public List<String> getOldTokenListLimit2(String newToken) {
        Sql sql = Sql.build("select old_token from token_new_old_relation where new_token = ? limit 2 ", newToken);
        return this.queryForList(sql, String.class);
    }

}
