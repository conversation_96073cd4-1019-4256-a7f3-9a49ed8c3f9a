package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.ThirdAppInfoRecordDao;
import cn.taqu.account.model.ThirdAppInfoRecord;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

@Repository
public class ThirdAppInfoRecordDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.ThirdAppInfoRecord> implements ThirdAppInfoRecordDao {

    @Override
    public ThirdAppInfoRecord findOneByToken(String token) {
        return this.get(Sql.build("select id,token,third_app_info,create_time,update_time from third_app_info_record where token = ? limit 1", token));
    }

}
