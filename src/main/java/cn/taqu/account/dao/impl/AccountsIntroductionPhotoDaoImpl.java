package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsIntroductionPhotoDao;
import cn.taqu.account.model.AccountsIntroductionPhoto;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:35
 */
@Repository
public class AccountsIntroductionPhotoDaoImpl extends NimbleDaoImpl<Long, AccountsIntroductionPhoto> implements AccountsIntroductionPhotoDao {

    @Override
    public List<AccountsIntroductionPhoto> findByIntroductionId(Long introductionId) {
        if(introductionId == null){
            return new ArrayList<>();
        }
        Sql sql = Sql.build(AccountsIntroductionPhoto.class, FIELDS_ALL)
                .eq("introduction_id", introductionId);
        return this.query(sql);
    }

    @Override
    public List<AccountsIntroductionPhoto> findInIntroductionId(List<Long> deleteIntroductionIds) {
        if(CollectionUtils.isEmpty(deleteIntroductionIds)){
            return new ArrayList<>();
        }
        Sql sql = Sql.build(AccountsIntroductionPhoto.class, FIELDS_ALL)
                .in("introduction_id", deleteIntroductionIds);
        return this.query(sql);
    }
}
