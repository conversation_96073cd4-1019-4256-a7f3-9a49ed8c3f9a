package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.CareModelDao;
import cn.taqu.account.model.CareModel;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 用户关怀模式名单DAO实现
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Repository
public class CareModelDaoImpl extends NimbleDaoImpl<Long, CareModel> implements CareModelDao {

    @Override
    public CareModel findByAccountUuid(String accountUuid) {
        Sql sql = Sql.build(CareModel.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid)
                .limit(1);
        return this.get(sql);
    }

}
