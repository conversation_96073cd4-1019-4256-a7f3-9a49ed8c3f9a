package cn.taqu.account.dao.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Repository;

import cn.taqu.account.common.AliyunLiveFaceDetectStatus;
import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.dao.AccountsLifeLogDao;
import cn.taqu.account.model.AccountsIdealTargetLog;
import cn.taqu.account.model.AccountsLifeLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import cn.taqu.core.utils.DateUtil;

/**
 * 
 * 
 * <AUTHOR>
 * 2024年12月3日上午10:15:15
 */
@Repository
public class AccountsLifeLogDaoImpl extends NimbleDaoImpl<Long, AccountsLifeLog> implements AccountsLifeLogDao {

    
    @Override
    public AccountsLifeLog addAccountsLifeLog(String accountUuid, String photoUrl, Integer width, Integer height,
        String remark, Integer seq, Integer verifyStatus) {
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        String errorMsg = "";
        if(Objects.equals(verifyStatus, AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus())) {
            errorMsg = CodeStatus.ALIYUN_VERIFY_FALI_MSG.getReasonPhrase();
        }
        
        AccountsLifeLog accountsLifeLog = new AccountsLifeLog();
        accountsLifeLog.setAccountUuid(accountUuid);
        accountsLifeLog.setAuditTime(null);
        accountsLifeLog.setCreateTime(currentTimeSeconds);
        accountsLifeLog.setErrorMsg(errorMsg);
        accountsLifeLog.setHeight(height);
        accountsLifeLog.setPhotoUrl(photoUrl);
        accountsLifeLog.setRemark(remark);
        accountsLifeLog.setRiskRespData("");
        accountsLifeLog.setSeq(seq);
        accountsLifeLog.setStatus(CommonAuditStatus.AUDITING.getStatus());
        accountsLifeLog.setUpdateTime(currentTimeSeconds);
        accountsLifeLog.setVerifyStatus(verifyStatus);
        accountsLifeLog.setWidth(width);
        accountsLifeLog = this.merge(accountsLifeLog);
        
        return accountsLifeLog;
    }

    @Override
    public List<AccountsLifeLog> queryNotAudit(Long expireTime, Long startTime, Integer limit) {
        Sql sql = Sql.build(AccountsLifeLog.class, Arrays.asList("id", "account_uuid", "photo_url", "remark", "status", "seq", "verify_status"))
            .eq("status", CommonAuditStatus.AUDITING.getStatus())
            .lt("create_time", expireTime)
            .gt("create_time", startTime)
            .orderBy("id desc")
            .limit(limit);
        return this.query(sql);
    }
    
}
