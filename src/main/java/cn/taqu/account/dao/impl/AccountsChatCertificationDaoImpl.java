package cn.taqu.account.dao.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;

import cn.taqu.account.dao.AccountsChatCertificationDao;
import cn.taqu.account.model.AccountsChatCertification;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

/**
 * Created by cqa on 2017/11/6.
 */
@Repository
public class AccountsChatCertificationDaoImpl extends NimbleDaoImpl<Long, AccountsChatCertification> implements AccountsChatCertificationDao {

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Override
    public List<AccountsChatCertification> getAccountChatCertificationsByIdentityNo(String identityNo, Integer isChecked) {
        Sql sql = Sql.build(AccountsChatCertification.class, AccountsChatCertificationDao.FIELDS_ALL)
                .eq("identity_no", identityNo, false);
        if (isChecked != null) {
            sql.eq("is_checked", isChecked);
        }
        return this.query(sql, AccountsChatCertification.class);
    }

    @Override
    public List<AccountsChatCertification> getAccountChatCertificationsByIdentityNoDigest(String lowerIdentityNoDigest, String upperIdentityNoDigest, Integer isChecked) {
        Sql sql = Sql.build(AccountsChatCertification.class, AccountsChatCertificationDao.FIELDS_ALL)
                .in("identity_no_digest", new String[] {lowerIdentityNoDigest, upperIdentityNoDigest});
        if (isChecked != null) {
            sql.eq("is_checked", isChecked);
        }
        return this.query(sql, AccountsChatCertification.class);
    }

    @Override
    public Object[] findIdByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("select id, is_checked, real_name_cipher, identity_no_cipher, certification_photo_url from accounts_chat_certification where account_uuid=?", accountUuid);
        return this.get(sql, Object[].class);
    }

    @Override
    public Object[] findInfoByBizNo(String bizNo) {
        Sql sql = Sql.build("select id, account_uuid, real_name_cipher, identity_no_cipher, is_checked, identity_type from accounts_chat_certification where biz_no=?", bizNo);
        return this.get(sql, Object[].class);
    }

    @Override
    public AccountsChatCertification getByAccountUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsChatCertification.class, FIELDS_ALL).eq("account_uuid", accountUuid, false);
    	return this.get(sql);
    }

    @Override
    public void addKeyValue(Map<String, Object> keyValueMap) {
        String[] fields = new String[keyValueMap.size()];
        Object[] values = new Object[keyValueMap.size()];
        String[] placeHolder = new String[keyValueMap.size()];
        int index = 0;
        for(Map.Entry<String, Object> entry : keyValueMap.entrySet()) {
            fields[index] = entry.getKey();
            values[index] = entry.getValue();
            placeHolder[index] = "?";
            index ++;
        }
        Sql sql = Sql.build("insert ignore into accounts_chat_certification(" + String.join(", ", fields) + ") values(" + String.join(",", placeHolder) + ")", values);
        this.update(sql);
    }

    @Override
    public void updateById(Map<String, Object> keyValueMap, Long id) {
        String[] setFileds = new String[keyValueMap.size()];
        Object[] values = new Object[keyValueMap.size() + 1];
        int index = 0;
        for(Map.Entry<String, Object> entry : keyValueMap.entrySet()) {
            setFileds[index] = " " + entry.getKey() + "=?";
            values[index] = entry.getValue();
            index ++;
        }
        values[index] = id;
        Sql sql = Sql.build("update accounts_chat_certification set " + String.join(",", setFileds) + " where id=?", values);
        this.update(sql);
    }

    @Override
    public String getCertificationPhotoUrlByUuid(String accountUuid) {
        Sql sql = Sql.build("select certification_photo_url from accounts_chat_certification where account_uuid = ? and is_checked = 1 order by id desc limit 1", accountUuid);
        return this.queryForString(sql);
    }

    @Override
    public List<AccountsChatCertification> getAccountsChatCertificationByUuids(List<String> uuidList) {
        List<AccountsChatCertification> returnList= Lists.newArrayList();
        if(CollectionUtils.isEmpty(uuidList)) {
            logger.warn("getAccountsChatCertificationByUuids传入的uuidList为空");
            return returnList;
        }
//        List<AccountsChatCertification> accountsCertificationList=getAccountsCertificationFromRedis(uuidList);
//        if(!CollectionUtils.isEmpty(accountsCertificationList)){
//            returnList.addAll(accountsCertificationList);
//        }
//        List<String> noValueUuids= getNoValueUuids(accountsCertificationList, uuidList);
//        if(CollectionUtils.isEmpty(noValueUuids)){
//            return returnList;
//        }
        Sql sql = Sql.build(AccountsChatCertification.class, AccountsChatCertificationDao.FIELDS_ALL)
                .in("account_uuid", uuidList)
                .eq("is_checked", 1);
        List<AccountsChatCertification> listFromDb=this.query(sql, AccountsChatCertification.class);
        if(!CollectionUtils.isEmpty(listFromDb)){
            returnList.addAll(listFromDb);
        }
//        setAccountsCertificationCache(listFromDb);
        return returnList;
    }

//    private void setAccountsCertificationCache(List<AccountsChatCertification> accountsCertificationList){
//        if(CollectionUtils.isEmpty(accountsCertificationList)){
//            return;
//        }
//        for(AccountsChatCertification temp:accountsCertificationList){
//            String uuid=temp.getAccountUuid();
//            String accountsCertificationKey = RedisKeyConstant.ACCOUNT_CHAT_CERTIFICATION_INFO.setArg(uuid);
//            setAccountsCertificationCache(temp, temp.getAccountUuid(), accountsCertificationKey);
//        }
//    }

//    private void setAccountsCertificationCache(AccountsChatCertification accountsCertification, String accountUuid,String key){
//        //设置默认值
//        if(accountsCertification==null){
//            accountsCertification =new AccountsChatCertification();
//            accountsCertification.setAccountUuid(accountUuid);
//        }
//        accountStringRedisTemplate.opsForValue().set(key, JSON.toJSONString(accountsCertification));
//        accountStringRedisTemplate.expire(key,10, TimeUnit.MINUTES);
//    }


//    private List<AccountsChatCertification> getAccountsCertificationFromRedis(List<String> uuidList){
//        RedisSerializer<String> stringRedisSerializer = accountStringRedisTemplate.getStringSerializer();
//        List<Object> redisValues = accountStringRedisTemplate.executePipelined((RedisConnection connection) -> {
//            uuidList.forEach(id -> {
//                byte[] key = stringRedisSerializer.serialize(RedisKeyConstant.ACCOUNT_CHAT_CERTIFICATION_INFO.setArg(id));
//                connection.get(key);
//            });
//            return null;
//        }, stringRedisSerializer);
//        logger.info("getChatCertification redis uuidList:{}|values:{}",uuidList, JsonUtils.objectToString(redisValues));
//        List<AccountsChatCertification> accountsCertificationList=Lists.newArrayList();
//        if(CollectionUtils.isEmpty(redisValues)){
//            return accountsCertificationList;
//        }
//        for(Object obj: redisValues){
//            if(obj==null){
//                continue;
//            }
//            String objStr=(String)obj;
//            AccountsChatCertification temp=JsonUtils.stringToObject(objStr, AccountsChatCertification.class);
//            accountsCertificationList.add(temp);
//        }
//        return accountsCertificationList;
//    }

//    private List<String> getNoValueUuids(List<AccountsChatCertification> accountsCertificationList, List<String> uuidList){
//        if(CollectionUtils.isEmpty(accountsCertificationList)){
//            return uuidList;
//        }
//        List<String> valueUuids = accountsCertificationList.stream().map(AccountsChatCertification::getAccountUuid).collect(Collectors.toList());
//        List<String> noValueUuids= Lists.newArrayList();
//        for(String temp: uuidList){
//            if(!valueUuids.contains(temp)){
//                noValueUuids.add(temp);
//            }
//        }
//        return noValueUuids;
//    }



}
