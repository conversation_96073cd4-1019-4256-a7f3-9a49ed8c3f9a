package cn.taqu.account.dao.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import cn.taqu.account.model.Accounts;
import cn.taqu.core.orm.Sql;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.Accounts> implements cn.taqu.account.dao.AccountsDao {

    @Override
    public List<Accounts> listByUuidOrNickname(String uuid, String nickname) {
        Sql sql = Sql.build("select " + String.join(",", Accounts.fields) + " from accounts where (uuid = ? or account_name = ? ) ", uuid, nickname);
        return this.query(sql);
    }

    @Override
    public Long getCreateTimeByUuid(String uuid) { 
        Sql sql = Sql.build("select create_time from accounts where uuid = ?", uuid);
        return this.queryForLong(sql);
    }
    
//    @Override
//    public String getMobileByUuid(String uuid) { // 根据传入的uuid获取手机号
//        Sql sql = Sql.build("select mobile from accounts where uuid = ?", uuid);
//        return this.queryForString(sql);
//    }

    @Override
    public String getAccountNamebyUuid(String uuid) {//根据uuid获取account_name
        Sql sql = Sql.build("select account_name from accounts where uuid = ?", uuid);
        return this.queryForString(sql);
    }

    @Override
    public int bindMobileByUuid(String mobile, String mobileCipher, String mobileDigest, String uuid) {// 将mobile绑定到uuid上
        Sql sql = Sql.build("update accounts set mobile = ?, mobile_cipher = ?, mobile_digest = ?  where uuid = ?", mobile, mobileCipher, mobileDigest, uuid);
        return this.update(sql);
    }

    @Override
    public int setMobileNullByUuid(String mobile, String uuid) {// 将mobile为当前号码的账号的mobile字段更新为空，注意不包含当前账号
        Sql sql = Sql.build("update accounts set mobile = null, mobile_cipher = null,mobile_digest = null where mobile = ? and uuid !=  ?", mobile, uuid);
        return this.update(sql);
    }
    @Override
    public int setMobileNullByUuidDigest(String mobileDigest, String uuid) {// 将mobile为当前号码的账号的mobile字段更新为空，注意不包含当前账号
        Sql sql = Sql.build("update accounts set mobile = null, mobile_cipher = null,mobile_digest = null where mobile_digest = ? and uuid !=  ?", mobileDigest, uuid);
        return this.update(sql);
    }

    @Override
    public int setMobileNullInUuid(List<String> accountsUuidList) {// 将mobile为当前号码的账号的mobile字段更新为空，注意不包含当前账号
        Sql sql = Sql.build("update accounts set mobile = null, mobile_cipher = null,mobile_digest = null ").in("uuid", accountsUuidList);
        return this.update(sql);
    }

    @Override
    public int updatePasswordForFirst(String password, String accountUuid) {
        Sql sql = Sql.build("update accounts set account_password = ? where uuid = ? and (account_password is null or account_password = '')", password, accountUuid);
        return this.update(sql);
    }

    @Override
    public int updatePasswordForReset(String password, String accountUuid) {
        Sql sql = Sql.build("update accounts set account_password = ? where uuid = ?", password, accountUuid);
        return this.update(sql);
    }

    @Override
    public int updateMemberByUuid(Long member_id, String accountUuid) {
        Sql sql = Sql.build("update accounts set member_id = ? where uuid = ?", member_id, accountUuid);
        return this.update(sql);
    }

    @Override
    public long getCountByMemberId(Long member_id, Long todayBeginSecond, Long todayEndSecond) {
        Sql sql = Sql.build("select count(uuid) from accounts")
                .eq("member_id", member_id)
                .ge("create_time", todayBeginSecond)
                .le("create_time", todayEndSecond);
        return this.queryForLong(sql);
    }

    @Override
    public Integer getSexTypeByAccountUuid(String accountUuid) {
        return this.queryForInteger(Sql.build("select sex_type from accounts where uuid = ?", accountUuid));
    }

    @Override
    public Integer getSexTypeFromMasterDBByAccountUuid(String accountUuid) {
        return this.queryForInteger(Sql.build("select sex_type from accounts where uuid = ?", accountUuid).masterDB());
    }

    @Override
    public Long getAccountIdByUuid(String uuid) {
        return this.queryForLong(Sql.build("select account_id from accounts where uuid = ?", uuid));
    }

    @Override
    public int setNicknameByUuid(String nickname, String accountUuid) {
        Sql sql = Sql.build("update accounts set account_name = ? where uuid = ?", nickname, accountUuid);
        return this.update(sql);
    }

    @Override
    public Accounts getByUuid(String accountUuid) {
        return this.get(Sql.build(Accounts.class, Accounts.fields).eq("uuid", accountUuid, false));
    }

    @Override
    public List<Object[]> getUuidAndAccountNameInUuids(Collection<String> uuids) {
        Sql sql = Sql.build("select uuid, account_name from accounts").in("uuid", uuids);
        return this.query(sql, Object[].class);
    }

    @Override
    public List<Object[]> getUuidAndAccountNameAndRegtimeInUuids(Collection<String> uuids) {
        Sql sql = Sql.build("select uuid, account_name, create_time from accounts").in("uuid", uuids);
        return this.query(sql, Object[].class);
    }

    @Override
    public List<String> listUuidByAccountName(String accountName) {
        return this.queryForList(Sql.build("select uuid from accounts where account_name = ? limit 200", accountName), String.class);
    }

    @Override
    public String getUuidByEmail(String email) {
        return this.queryForString(Sql.build("select uuid from accounts where email = ?", email));
    }

    @Override
    public Object[] getByAccountKey(String accountKey) {
        return this.get(Sql.build("select uuid, account_name, account_status, account_type from accounts where account_key = ? limit 1", accountKey), Object[].class);
    }

    @Override
    public Integer getAccountStatusByUuid(String accountUuid) {
        return this.queryForInteger(Sql.build("select account_status from accounts where uuid = ? ", accountUuid));
    }

    @Override
    public int updateAccountKeyByUuid(String account_key, String accountUuid) {
        Sql sql = Sql.build("update accounts set account_key = ? where uuid = ?", account_key, accountUuid);
        return this.update(sql);
    }

    @Override
    public int clearAccountKey(String accountKey) {
        Sql sql = Sql.build("update accounts set account_key = '' where account_key = ?", accountKey);
        return this.update(sql);
    }

    @Override
    public int updateByUuid(Map<String, Object> fieldValue, String accountUuid) {
        int index = 0;
        int size = fieldValue.size();

        StringBuilder updateSql = new StringBuilder("update accounts set ");
        Object[] updateValues = new Object[size + 1];

        for (Map.Entry<String, Object> entry : fieldValue.entrySet()) {
            String fieldName = StringUtils.trimToEmpty(entry.getKey());
            Object value = entry.getValue();
            updateSql.append(fieldName).append("=?");
            updateValues[index++] = value;
            if (index < size) {
                updateSql.append(", ");
            }
        }
        updateSql.append(" where uuid=?");
        updateValues[index] = accountUuid;

        Sql sql = Sql.build(updateSql.toString(), updateValues);
        return this.update(sql);
    }

    @Override
    public int updateSexType(Integer param, String uuid) {
        Sql sql = Sql.build("update accounts set sex_type  = ? where uuid = ?", param, uuid);
        return this.update(sql);
    }

    @Override
    public int updateDestroyStatus(String accountName, String avatar, String uuid) {
        Sql sql = Sql.build("update accounts set account_name = ?, avatar = ?, account_status = 0, mobile = '', mobile_cipher = '' , mobile_digest = '' where uuid = ?", accountName, avatar, uuid);
        return this.update(sql);
    }

    @Override
    public int updateMobilePlace(String uuid, String mobilePlace) {
        Sql sql = Sql.build("update accounts set mobile_place=? where uuid=?", mobilePlace, uuid);
        return this.update(sql);
    }

    @Override
    public Accounts getUuidAndPwdByName(String accountName) {
        return this.get(Sql.build("select uuid, account_password, create_time from accounts where account_name = ? and account_type = 1 order by create_time desc limit 1", accountName).masterDB());
    }

    @Override
    public Accounts getUuidAndPwdByUuid(String accountUuid) {
        return this.get(Sql.build("select uuid, account_password, create_time from accounts where uuid = ? and account_type = 1 order by create_time desc limit 1", accountUuid).masterDB());
    }

//    @Override
//    public Accounts getRegStyleByUuid(String uuid) {
//        Sql sql = Sql.build("select uuid,account_name,appcode,reg_style,sex_type,mobile,mobile_cipher from accounts where uuid = ? limit 0,1 ", uuid);
//        return get(sql);
//    }

    @Override
    public Integer renewAccount(String accountUuid) {
        Sql sql = Sql.build("update accounts set account_status = 1 where uuid = ? ",  accountUuid);
        return this.update(sql);
    }

}
