package cn.taqu.account.dao.impl;

import cn.taqu.account.model.AccountsMedal;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsMedalDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsMedal> implements cn.taqu.account.dao.AccountsMedalDao {

    @Override
    public AccountsMedal getOneById(Long id) {
        return this.get(Sql.build("select id, medal_name, pic_url, privilege_id, create_time, update_time from accounts_medal where id = ?", id));
    }

    @Override
    public void setInvalid(Long id) {
        this.update(Sql.build("update accounts_medal set status = 0 where id=?", id));
    }
}
