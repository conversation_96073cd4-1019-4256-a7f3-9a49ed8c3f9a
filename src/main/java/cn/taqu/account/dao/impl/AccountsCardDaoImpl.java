package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsCardDao;
import cn.taqu.account.model.AccountsCard;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

@Repository
public class AccountsCardDaoImpl extends NimbleDaoImpl<Long, AccountsCard> implements AccountsCardDao {
    @Override
    public List<AccountsCard> getAllByUuidList(List<String> uuidList) {
        Sql sql = Sql.build(AccountsCard.class, Arrays.asList("id", "account_uuid", "card_id", "end_time", "status", "level", "is_used")).in("account_uuid", uuidList);
        return this.query(sql);
    }

    @Override
    public List<Long> getUsedAndNormalCardByUuid(String accountUuid) {
        Sql sql = Sql.build("select card_id from accounts_card where account_uuid=? and (level=? or status=?)", accountUuid, AccountsCard.Level.N, 1);
        return this.queryForList(sql, Long.class);
    }

    @Override
    public AccountsCard getByCardId(Long cardId) {
        Sql sql = Sql.build("select * from accounts_card where card_id=?", cardId);
        return this.get(sql);
    }

    @Override
    public AccountsCard getByUuidAndCardId(String accountUuid, Long cardId) {
        Sql sql = Sql.build("select * from accounts_card where account_uuid=? and card_id=?", accountUuid, cardId);
        return this.get(sql);
    }

    @Override
    public List<AccountsCard> getByCardIdList(List<Long> cardIdList, List<String> fields) {
        Sql sql = Sql.build(AccountsCard.class, fields).in("card_id", cardIdList);
        return this.query(sql);
    }

    @Override
    public String getUuidByCardId(Long cardId) {
        Sql sql = Sql.build("select account_uuid from accounts_card where card_id=?", cardId);
        return this.queryForString(sql);
    }

    @Override
    public String getUuidByCardId(Long cardId, String level) {
    	Sql sql = Sql.build("select account_uuid from accounts_card where card_id= ? and level= ?", cardId, level);
    	return this.queryForString(sql);
    }

    @Override
    public List<AccountsCard> listUuidByCardId(List<Long> cardIds, String level) {
    	Sql sql = Sql.build("select card_id, account_uuid from accounts_card");
    	sql.in("card_id", cardIds);
    	sql.eq("level", level);
    	return this.query(sql);
    }

    @Override
    public Long getCardByUuid(String accountUuid, String level) {
        Sql sql = Sql.build("select card_id from accounts_card where account_uuid=? and level=? order by id desc limit 1", accountUuid, level);
        return this.queryForLong(sql);
    }

    @Override
    public AccountsCard getByUuid(String accountUuid, String level) {
        Sql sql = Sql.build("select * from accounts_card where account_uuid=? and level=? order by id desc limit 1", accountUuid, level);
        return this.get(sql);
    }

    @Override
    public Integer updateOtherCardStatusByUuid(String accountUuid, Long cardId, Integer status) {
        Sql sql = Sql.build("update accounts_card set status=? where account_uuid=? and card_id!=?", status, accountUuid, cardId);
        return this.update(sql);
    }

    @Override
    public List<AccountsCard> getByUuid(String accountUuid, int page, int limit) {
        Sql sql = Sql.build(AccountsCard.class, Arrays.asList("card_id, level, end_time, status, is_used, source"))
                .eq("account_uuid", accountUuid)
                .orderBy(" field(level, 'N', 'SSS', 'SS', 'S', 'A', 'B', 'C'), end_time asc")
                .paging(page, limit);
        return this.query(sql);
    }

    @Override
    public int useCardId(String accountUuid, Long cardId) {
        Sql sql = Sql.build("update accounts_card set status=?, is_used=? where account_uuid=? and card_id=?", 1, 1, accountUuid, cardId);
        return this.update(sql);
    }

    @Override
    public String getCardUuidInUseStatusByDB(Long cardId) {
        Sql sql = Sql.build("select account_uuid from accounts_card where card_id=? and status=1", cardId);
        sql.limit(1);
        return this.queryForString(sql);
    }
}
