package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsIdealTargetDao;
import cn.taqu.account.model.AccountsIdealTarget;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import cn.taqu.core.utils.DateUtil;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/11/27 下午6:04
 */
@Repository
public class AccountsIdealTargetDaoImpl extends NimbleDaoImpl<Long, AccountsIdealTarget> implements AccountsIdealTargetDao {

    @Override
    public void upsert(AccountsIdealTarget target) {
        long now = DateUtil.currentTimeSeconds();
        Sql sql = Sql.build(
            "insert into accounts_ideal_target(account_uuid, ideal_target_origin, ideal_target, create_time, update_time) values (?, ?, ?, ?, ?) " +
                "on duplicate key update ideal_target_origin = ?, ideal_target = ?, update_time = ?",
            target.getAccountUuid(), target.getIdealTargetOrigin(), target.getIdealTarget(), now, now,
            target.getIdealTargetOrigin(), target.getIdealTarget(), now
        );
        this.update(sql);
    }
}
