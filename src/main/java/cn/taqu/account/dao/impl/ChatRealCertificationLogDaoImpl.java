package cn.taqu.account.dao.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.ChatRealCertificationLogDao;
import cn.taqu.account.model.ChatRealCertificationLog;
import cn.taqu.account.search.ChatRealCertificationLogSearch;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

@Repository
public class ChatRealCertificationLogDaoImpl extends NimbleDaoImpl<Long, ChatRealCertificationLog> implements ChatRealCertificationLogDao {

    @Override
    public List<ChatRealCertificationLog> getChatRealCertificationLogByPage(ChatRealCertificationLogSearch search, Integer start, Integer pageSize) {
        Sql sql = Sql.build(ChatRealCertificationLog.class, FIELDS_ALL);

        if (null != search.getStart_time()) {
            sql.ge("create_time", search.getStart_time());
        }

        if (null != search.getEnd_time()) {
            sql.le("create_time", search.getEnd_time());
        }

        if (null != search.getVerify_status()) {
            sql.eq("status", search.getVerify_status());
        }
        if (StringUtils.isNotEmpty(search.getAccount_uuid())) {
            sql.eq("account_uuid", search.getAccount_uuid());
        }
        sql.orderBy("create_time desc");

        logger.info("chat real certification sql: {}", sql.getSql());

        return this.queryForPage(sql, start, pageSize).getData();
    }


}
