package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsCardLogDao;
import cn.taqu.account.model.AccountsCardLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

@Repository
public class AccountsCardLogDaoImpl extends NimbleDaoImpl<Long, AccountsCardLog> implements AccountsCardLogDao {
    @Override
    public List<AccountsCardLog> getGifeLogByUuid(String accountUuid, int page, int limit) {
        Sql sql = Sql.build(AccountsCardLog.class, Arrays.asList("card_id, level, valid_day, create_time, operate_name, type"))
                .eq("account_uuid", accountUuid)
                .ins("type", AccountsCardLog.Type.GIVEN, AccountsCardLog.Type.GET)
                .orderBy("create_time desc, id desc")
                .paging(page, limit);
        return this.query(sql);
    }
}
