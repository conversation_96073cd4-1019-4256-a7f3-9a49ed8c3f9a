package cn.taqu.account.dao.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsLifeDao;
import cn.taqu.account.model.AccountsLife;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

/**
 * 
 * 
 * <AUTHOR>
 * 2024年12月3日上午10:15:15
 */
@Repository
public class AccountsLifeDaoImpl extends NimbleDaoImpl<Long, AccountsLife> implements AccountsLifeDao {

    @Override
    public AccountsLife getByAccountUuidAndSeq(String accountUuid, Integer seq) {
        return this.get(Sql.build(AccountsLife.class, FIELDS_ALL).eq("account_uuid", accountUuid).eq("seq", seq));
    }

    @Override
    public List<AccountsLife> getByAccountUuid(String accountUuid) {
        return this.query(Sql.build(AccountsLife.class, FIELDS_ALL).eq("account_uuid", accountUuid));
    }

    @Override
    public void updateStatus(AccountsLife life) {
        Sql sql = Sql.build("update accounts_life set status = ?, verify_status = ? where account_uuid = ? and seq = ?", life.getStatus(), life.getVerifyStatus(), life.getAccountUuid(), life.getSeq());
        update(sql);
    }


}
