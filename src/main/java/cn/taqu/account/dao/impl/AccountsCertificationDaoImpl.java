package cn.taqu.account.dao.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;

import cn.taqu.account.dao.AccountsCertificationDao;
import cn.taqu.account.model.AccountsCertification;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

/**
 * Created by cqa on 2017/11/6.
 */
@Repository
public class AccountsCertificationDaoImpl extends NimbleDaoImpl<Long, AccountsCertification> implements AccountsCertificationDao {

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;


    @Override
    public List<AccountsCertification> getAccountCertificationsByIdentityNo(String identityNo, Integer isChecked) {
        Sql sql = Sql.build(AccountsCertification.class, AccountsCertificationDao.FIELDS_ALL)
                .eq("identity_no", identityNo, false);
        if (isChecked != null) {
            sql.eq("is_checked", isChecked);
        }
        return this.query(sql, AccountsCertification.class);
    }

    @Override
    public List<AccountsCertification> getAccountCertificationsByIdentityNoDigest(String lowerIdentityNoDigest, String upperIdentityNoDigest, Integer isChecked) {
        Sql sql = Sql.build(AccountsCertification.class, AccountsCertificationDao.FIELDS_ALL)
                .in("identity_no_digest", new String[]{lowerIdentityNoDigest, upperIdentityNoDigest});
        if (isChecked != null) {
            sql.eq("is_checked", isChecked);
        }
        return this.query(sql, AccountsCertification.class);
    }

    @Override
    public Object[] findIdByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("select id, is_checked, real_name_cipher, identity_no_cipher, certification_photo_url, reward_account_cipher, reward_account_status from accounts_certification where account_uuid=?", accountUuid);
        return this.get(sql, Object[].class);
    }

    @Override
    public AccountsCertification getIdentityCipherByUuid(String accountUuid) {
        Sql sql = Sql.build("select identity_no_cipher from accounts_certification where account_uuid=? limit 1", accountUuid);
        return this.get(sql, AccountsCertification.class);
    }

    @Override
    public Object[] findInfoByBizNo(String bizNo) {
        Sql sql = Sql.build("select id, account_uuid, real_name_cipher, identity_no_cipher, is_checked, identity_type from accounts_certification where biz_no=?", bizNo);
        return this.get(sql, Object[].class);
    }

    @Override
    public List<String> listAccountUuidByRewardAccountDigest(String rewardAccountDigest, Integer rewardAccountStatus) {
        Sql sql = Sql.build("select account_uuid from accounts_certification where reward_account_digest = ? and reward_account_status = ?", rewardAccountDigest, rewardAccountStatus);
        return this.queryForList(sql, String.class);
    }

    @Override
    public Object[] findByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("select account_uuid, real_name, identity_no, biz_no, is_checked, reward_account, real_name_cipher, identity_no_cipher"
            + ", reward_account_cipher, identity_no_birth, reward_account_checked, reward_account_status "
            + " from accounts_certification where account_uuid=?", accountUuid);
        return this.get(sql, Object[].class);
    }

    @Override
    public AccountsCertification getByAccountUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsCertification.class, FIELDS_ALL).eq("account_uuid", accountUuid, false);
    	return this.get(sql);
    }

    @Override
    public void addKeyValue(Map<String, Object> keyValueMap, String uuid) {
        String[] fields = new String[keyValueMap.size()];
        Object[] values = new Object[keyValueMap.size()];
        String[] placeHolder = new String[keyValueMap.size()];
        int index = 0;
        for(Map.Entry<String, Object> entry : keyValueMap.entrySet()) {
            fields[index] = entry.getKey();
            values[index] = entry.getValue();
            placeHolder[index] = "?";
            index ++;
        }
        Sql sql = Sql.build("insert ignore into accounts_certification(" + String.join(", ", fields) + ") values(" + String.join(",", placeHolder) + ")", values);
        this.update(sql);
    }

    @Override
    public void updateById(Map<String, Object> keyValueMap, Long id, String uuid) {
        String[] setFileds = new String[keyValueMap.size()];
        Object[] values = new Object[keyValueMap.size() + 1];
        int index = 0;
        for(Map.Entry<String, Object> entry : keyValueMap.entrySet()) {
            setFileds[index] = " " + entry.getKey() + "=?";
            values[index] = entry.getValue();
            index ++;
        }
        values[index] = id;
        Sql sql = Sql.build("update accounts_certification set " + String.join(",", setFileds) + " where id=?", values);
        this.update(sql);
    }

    @Override
    public void updateRewardAccount(Integer rewardAccountChecked, Integer rewardAccountStatus, String rewardAccountCipher,String rewardAccountDigest, String accountUuid) {
        Sql sql = Sql.build("update accounts_certification set reward_account_checked=?, reward_account_status=?, reward_account_cipher=?,reward_account_digest=? where account_uuid=?"
            , rewardAccountChecked, rewardAccountStatus, rewardAccountCipher, rewardAccountDigest, accountUuid);
        this.update(sql);
    }

    @Override
    public void updateRewardAccountChecked(Integer rewardAccountChecked, String accountUuid){
        Sql sql = Sql.build("update accounts_certification set reward_account_checked=? where account_uuid=?", rewardAccountChecked, accountUuid);
        this.update(sql);
    }

    @Override
    public String getCertificationPhotoUrlByUuid(String accountUuid) {
        Sql sql = Sql.build("select certification_photo_url from accounts_certification where account_uuid = ? and is_checked=1 order by id desc limit 1", accountUuid);
        return this.queryForString(sql);
    }

    @Override
    public List<AccountsCertification> getAccountsCertificationByUuids(List<String> uuidList) {
        List<AccountsCertification> returnList=Lists.newArrayList();
        if(CollectionUtils.isEmpty(uuidList)) {
            logger.warn("getAccountsCertificationByUuids传入的uuidList为空");
            return returnList;
        }
        Sql sql = Sql.build(AccountsCertification.class, AccountsCertificationDao.FIELDS_ALL)
                .in("account_uuid", uuidList)
                .eq("is_checked", 1);
        List<AccountsCertification> listFromDb=this.query(sql, AccountsCertification.class);
        if(!CollectionUtils.isEmpty(listFromDb)){
            returnList.addAll(listFromDb);
        }
        return returnList;
    }

    @Override
    public int updateRewardAccountStatus(String accountUuid, int newRewardAccountStatus, int oldRewardAccountStatus) {
        Sql sql = Sql.build("update accounts_certification set reward_account_status=? where account_uuid=? and reward_account_status=?", newRewardAccountStatus, accountUuid, oldRewardAccountStatus);
        return this.update(sql);
    }


}
