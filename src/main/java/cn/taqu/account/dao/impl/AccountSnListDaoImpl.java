package cn.taqu.account.dao.impl;

import cn.taqu.account.model.AccountSnList;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountSnListDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountSnList> implements cn.taqu.account.dao.AccountSnListDao {

    @Override
    public AccountSnList findByAccountUuid(String accountUuid) {
        return this.get(Sql.build(AccountSnList.class, Arrays.asList("id", "account_uuid", "account_sn")).eq("account_uuid", accountUuid));
    }

    @Override
    public List<AccountSnList> getBySnList(List<String> accountSnList) {
        List<AccountSnList> resultList = new ArrayList<>();

        int size = accountSnList.size();
        int batch = 50;
        int index = 0;

        while(size > 0 && index < size) {
            Sql sql = Sql.build(AccountSnList.class, Arrays.asList("account_uuid", "account_sn")).in("account_sn", accountSnList.subList(index, Math.min(index+=batch, size)));
            List<AccountSnList> accountSnListList = this.query(sql);
            resultList.addAll(accountSnListList);
        }

        return resultList;
    }
}
