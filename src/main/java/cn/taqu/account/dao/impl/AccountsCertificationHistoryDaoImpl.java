package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsCertificationHistoryDao;
import cn.taqu.account.model.AccountsCertificationHistory;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-29 14:21
 */
@Repository
public class AccountsCertificationHistoryDaoImpl extends NimbleDaoImpl<Long, AccountsCertificationHistory> implements AccountsCertificationHistoryDao {

    @Override
    public AccountsCertificationHistory getByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsCertificationHistory.class, FIELDS_ALL).eq("account_uuid", accountUuid);
        sql.limit(1);
        List<AccountsCertificationHistory> list = this.query(sql);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public Integer getIdentityType(String accountUuid) {
        Sql sql = Sql.build(AccountsCertificationHistory.class, "identity_type").eq("account_uuid", accountUuid);
        sql.limit(1);
        AccountsCertificationHistory history = this.get(sql);
        return history != null ? history.getIdentityType() : 0;
    }
}
