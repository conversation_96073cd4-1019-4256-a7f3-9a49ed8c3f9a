package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsCertificationChangeLogDao;
import cn.taqu.account.model.AccountsCertificationChangeLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 14:21
 */
@Repository
public class AccountsCertificationChangeLogDaoImpl extends NimbleDaoImpl<Long, AccountsCertificationChangeLog> implements AccountsCertificationChangeLogDao {

    @Override
    public List<String> listAccountUuidByIdentityNo(String identityNo, Integer logType, Integer limit) {
        Sql sql = Sql.build(AccountsCertificationChangeLog.class, Arrays.asList("account_uuid"));
        sql.eq("identity_no", identityNo).eq("log_type", logType);
        sql.orderBy(" operator_time desc");
        sql.limit(limit);
        return this.queryForList(sql, String.class);
    }
    
    @Override
    public List<String> listAccountUuidByIdentityNoDigest(String identityNoDigest, Integer logType, Integer limit) {
        Sql sql = Sql.build(AccountsCertificationChangeLog.class, Arrays.asList("account_uuid"));
        sql.eq("identity_no_digest", identityNoDigest).eq("log_type", logType);
        sql.orderBy(" operator_time desc");
        sql.limit(limit);
        return this.queryForList(sql, String.class);
    }
    
}
