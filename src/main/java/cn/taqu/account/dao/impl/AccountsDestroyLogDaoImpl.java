package cn.taqu.account.dao.impl;

import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Repository;

import cn.taqu.account.model.AccountsDestroyLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsDestroyLogDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsDestroyLog> implements cn.taqu.account.dao.AccountsDestroyLogDao {
    @Override
    public int updateStatusByUuid(String uuid, Integer oldStatus, Integer newStatus) {
        Sql sql = Sql.build("update accounts_destroy_log set update_time = ?, status = ? where account_uuid = ? and status = ?", DateUtil.currentTimeSeconds(), newStatus, uuid, oldStatus);
        return this.update(sql);
    }

    @Override
    public Long getLastCreateTime(List<String> uuidList, Integer status) {
        Sql sql = Sql.build(AccountsDestroyLog.class, Arrays.asList("create_time")).in("account_uuid", uuidList).eq("status", status).orderBy("create_time desc").limit(1);
        return this.queryForLong(sql);
    }

    @Override
    public AccountsDestroyLog getByQqId(String qqid) {
        Sql sql = Sql.build("select need_to_limit_state,charm_rating,create_time from accounts_destroy_log where qq_id = ? and status = 1 order by id desc limit 1",qqid);
        return this.get(sql);
    }

    @Override
    public AccountsDestroyLog getByWechatId(String wechatId) {
        Sql sql = Sql.build("select need_to_limit_state,charm_rating,create_time from accounts_destroy_log where wechat_id = ?  and status = 1 order by id desc limit 1",wechatId);
        return this.get(sql);
    }

    @Override
    public AccountsDestroyLog getByAppleId(String AppleId) {
        Sql sql = Sql.build("select need_to_limit_state,charm_rating,create_time from accounts_destroy_log where apple_id = ?  and status = 1 order by id desc limit 1",AppleId);
        return this.get(sql);
    }

    @Override
    public AccountsDestroyLog getByVisitorId(String token) {
        Sql sql = Sql.build("select need_to_limit_state,charm_rating,create_time from accounts_destroy_log where visitor_id = ?  and status = 1 order by id desc limit 1",token);
        return this.get(sql);
    }

    @Override
    public AccountsDestroyLog getByWeiboId(String weiboId) {
        Sql sql = Sql.build("select need_to_limit_state,charm_rating,create_time from accounts_destroy_log where weibo_id = ?  and status = 1 order by id desc limit 1",weiboId);
        return this.get(sql);
    }

    @Override
    public List<AccountsDestroyLog> getListByMobile(List<String> mobileList, Integer status) {
        Sql sql = Sql.build(AccountsDestroyLog.class, FIELDS_ALL)
                .in("mobile", mobileList);
        if (status != null) {
            sql.eq("status", status);
        }
        return this.query(sql);
    }
    
    @Override
    public List<AccountsDestroyLog> getListByMobileDigest(List<String> mobileDigestList, Integer status) {
        Sql sql = Sql.build(AccountsDestroyLog.class, FIELDS_ALL)
            .in("mobile_digest", mobileDigestList);
        if (status != null) {
            sql.eq("status", status);
        }
        return this.query(sql);
    }


}
