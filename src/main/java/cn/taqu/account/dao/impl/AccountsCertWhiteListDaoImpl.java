package cn.taqu.account.dao.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsCertWhiteListDao;
import cn.taqu.account.model.AccountsCertWhiteList;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-15 15:59
 */
@Repository
public class AccountsCertWhiteListDaoImpl extends NimbleDaoImpl<Long, AccountsCertWhiteList> implements AccountsCertWhiteListDao {

    @Override
    public List<AccountsCertWhiteList> list(Integer pageNum, Integer pageSize, Long startTime, Long endTime, String accountUuid, Integer whiteListFrom) {
        Sql sql = Sql.build(AccountsCertWhiteList.class, FIELDS_ALL);
        if(StringUtils.isNotBlank(accountUuid)){
            sql.eq("account_uuid", accountUuid);
        }
        if(whiteListFrom != null && whiteListFrom > 0){
            sql.eq("white_list_from", whiteListFrom);
        }
        if(startTime != null && startTime > 0L && endTime != null && endTime > 0L){
            sql.ge("create_time", startTime);
            sql.le("create_time", endTime);
        }
        sql.orderBy(" create_time desc");
        sql.limit((pageNum - 1) * pageSize, pageSize);
        return this.query(sql);
    }

    @Override
    public Integer countByUuid(String accountUuid) {
        return this.queryForInteger(Sql.build("select count(1) from accounts_cert_white_list where account_uuid = ? ", accountUuid));
    }

}
