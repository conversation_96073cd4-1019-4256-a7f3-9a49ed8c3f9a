package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsCardExpireDao;
import cn.taqu.account.model.AccountsCardExpire;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import cn.taqu.core.utils.DateUtil;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

@Repository
public class AccountsCardExpireDaoImpl extends NimbleDaoImpl<Long, AccountsCardExpire> implements AccountsCardExpireDao {
    @Override
    public int insert(String accountUuid, Long cardId, String level, Long endTime) {
        Sql sql = Sql.build("insert into accounts_card_expire(account_uuid, card_id, level, end_time, create_time) values(?, ?, ?, ?, ?)", accountUuid, cardId, level, endTime, DateUtil.currentTimeSeconds());
        return this.update(sql);
    }

    @Override
    public List<AccountsCardExpire> getByUuid(String accountUuid, Integer page, Integer limit) {
        Sql sql = Sql.build(AccountsCardExpire.class, Arrays.asList("card_id, level, end_time"))
                .eq("account_uuid", accountUuid)
                .orderBy("end_time desc")
                .paging(page, limit);
        return this.query(sql);
    }
}
