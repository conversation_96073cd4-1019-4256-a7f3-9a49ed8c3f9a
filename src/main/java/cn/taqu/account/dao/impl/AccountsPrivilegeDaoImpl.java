package cn.taqu.account.dao.impl;

import cn.taqu.account.model.AccountsPrivilege;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsPrivilegeDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsPrivilege> implements cn.taqu.account.dao.AccountsPrivilegeDao {

    @Override
    public AccountsPrivilege getOneById(Long id) {
        return this.get(Sql.build("select id, privilege_config, create_time, update_time from accounts_privilege where id = ?", id));
    }

}
