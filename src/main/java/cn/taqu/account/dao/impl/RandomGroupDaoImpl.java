package cn.taqu.account.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.RandomGroupDao;
import cn.taqu.account.model.RandomGroup;
import cn.taqu.account.search.RandomGroupSearch;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class RandomGroupDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<Long, RandomGroup> implements RandomGroupDao {

    @Override
    public PageData<RandomGroup> findPageList(RandomGroupSearch search) {
        Sql sql = Sql.build(RandomGroup.class, FIELDS_ALL);
        return queryForPage(sql, search.getPage(), search.getRows());
    }

    @Override
    public List<RandomGroup> findListByStatus(Integer status) {
        Sql sql = Sql.build(RandomGroup.class, FIELDS_ALL).eq("status", status);
        return query(sql);
    }

    @Override
    public RandomGroup getById(Long id) {
        Sql sql = Sql.build(RandomGroup.class, FIELDS_ALL).eq("id", id);
        return get(sql);
    }
}
