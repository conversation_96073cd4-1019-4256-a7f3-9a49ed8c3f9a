package cn.taqu.account.dao.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsMemberInfoDao;
import cn.taqu.account.model.AccountsMemberInfo;
import cn.taqu.core.orm.Sql;

@Repository
public class AccountsMemberInfoDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsMemberInfo> implements AccountsMemberInfoDao {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public AccountsMemberInfo findByAccountUuid(String accountUuid) {
        Sql sql = Sql.build("select id, account_uuid, token, app_version, appcode, cloned, channel, platform_id, create_time, last_open_time, last_active_time, update_time from accounts_member_info where account_uuid = ? order by id desc limit 1", accountUuid);
        return this.get(sql);
    }

    @Override
    public int updateLastActiveTime(String uuid, Long time) {
        Sql sql = Sql.build("update accounts_member_info set last_active_time=?, update_time=? where account_uuid=?", time, time, uuid);
        Integer count = this.update(sql);
        return count == null ? 0 : count;
    }
    
    @Override
    public List<String> findTokenByAccountUuid(String accountUuid) {
    	Sql sql = Sql.build("SELECT token FROM accounts_member_info WHERE account_uuid = ?", accountUuid);
    	return this.queryForList(sql, String.class);
    }
    
    @Override
    public List<String> findAccountUuidByToken(String token) {
    	Sql sql = Sql.build("SELECT account_uuid FROM  accounts_member_info WHERE token = ?", token);
    	return this.queryForList(sql, String.class);
    }

    @Override
    public List<AccountsMemberInfo> batchGetInfoByUuids(List<String> accountUuidList) {
        if (CollectionUtils.isEmpty(accountUuidList)) {
            return new ArrayList<>();
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT  *                              \n");
        sb.append("FROM accounts_member_info              \n");
        sb.append("WHERE account_uuid IN (:accountUuidList)       \n");

        Map<String, Object> params = new HashMap<>();
        params.put("accountUuidList", accountUuidList);

        return namedParameterJdbcTemplate.query(sb.toString(), params, new BeanPropertyRowMapper<>(AccountsMemberInfo.class));
    }

    @Override
    public Integer countByTime(Long startTime,Long endTime){
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT  count(*)                           \n");
        sb.append("FROM accounts_member_info                  \n");
        sb.append("WHERE 1=1                                  \n");
        sb.append("AND last_active_time >= :startTime         \n");
        sb.append("AND last_active_time < :endTime           \n");


        Map<String, Object> params = new HashMap<>();
        params.put("startTime", startTime);
        params.put("endTime", endTime);

        return namedParameterJdbcTemplate.queryForObject(sb.toString(), params, Integer.class);
    }

    @Override
    public Long getMaxId() {
        Sql sql = Sql.build("SELECT max(id) FROM accounts_member_info");
        return this.queryForLong(sql);
    }

}
