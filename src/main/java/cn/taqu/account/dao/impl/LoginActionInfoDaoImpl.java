package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.LoginActionInfoDao;
import cn.taqu.account.model.LoginActionInfo;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

@Repository
public class LoginActionInfoDaoImpl extends NimbleDaoImpl<Long, LoginActionInfo> implements LoginActionInfoDao {

    @Override
    public Long getIdByAccountUuid(String accountUuid) {
        return this.queryForLong(Sql.build("SELECT id from login_action_info WHERE account_uuid = ?", accountUuid));
    }

    @Override
    public LoginActionInfo getByUuid(String accountUuid) {
        return this.get(Sql.build(LoginActionInfo.class, FIELDS_ALL).eq("account_uuid", accountUuid, false));
    }
}
