package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AvatarGuidePopupRecordDao;
import cn.taqu.account.model.AvatarGuidePopupRecord;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 头像指导弹窗记录DAO实现
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Repository
public class AvatarGuidePopupRecordDaoImpl extends NimbleDaoImpl<Long, AvatarGuidePopupRecord>
        implements AvatarGuidePopupRecordDao {

    @Override
    public AvatarGuidePopupRecord find(String accountUuid) {
        Sql sql = Sql.build(AvatarGuidePopupRecord.class, FIELDS_ALL)
                .eq("account_uuid", accountUuid)
                .limit(1);
        return this.get(sql);
    }

    @Override
    public List<AvatarGuidePopupRecord> findNeverPopupBeforeDay(Integer startDate, Long id, Integer size) {
        Sql sql = Sql.build(AvatarGuidePopupRecord.class, "id", "account_uuid");
        sql.le("popup_date", startDate);
        sql.neq("popup_date", 0);
        sql.gt("id", id);
        sql.orderBy("id asc");
        sql.limit(size);
        return this.query(sql);
    }

}
