package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsPhotoImgCompareLogDao;
import cn.taqu.account.model.AccountsPhotoImgCompareLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-13 18:49
 */
@Repository
public class AccountsPhotoImgCompareLogDaoImpl extends NimbleDaoImpl<Long, AccountsPhotoImgCompareLog> implements AccountsPhotoImgCompareLogDao {

    @Override
    public AccountsPhotoImgCompareLog getLastByUuid(String accountUuid, String photoUrl) {
        Sql sql = Sql.build(AccountsPhotoImgCompareLog.class, FIELDS_ALL);
        sql.eq("account_uuid", accountUuid).eq("verify_photo_url", photoUrl).orderBy("id desc").limit(1);
        List<AccountsPhotoImgCompareLog> list = this.query(sql);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
