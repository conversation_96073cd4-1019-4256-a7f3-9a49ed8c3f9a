package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsPhotoCheckFailDao;
import cn.taqu.account.model.AccountsPhotoCheckFail;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

@Repository
public class AccountsPhotoCheckFailDaoImpl extends NimbleDaoImpl<Long,AccountsPhotoCheckFail> implements AccountsPhotoCheckFailDao {
    @Override
    public AccountsPhotoCheckFail findCheckFailAvatarByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsPhotoCheckFail.class, FIELDS_ALL).eq("account_uuid", accountUuid, true)
                .orderBy("create_time desc, id desc")
                .limit(1);
        return this.get(sql);
    }
}
