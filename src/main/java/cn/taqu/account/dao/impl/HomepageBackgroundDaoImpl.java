package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.HomepageBackgroundDao;
import cn.taqu.account.model.HomepageBackground;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 首页
 *
 * <AUTHOR>
 * @date 2024/11/25 11:02
 */
@Repository
public class HomepageBackgroundDaoImpl extends NimbleDaoImpl<Long, HomepageBackground> implements HomepageBackgroundDao {

    @Override
    public HomepageBackground getByUuid(String accountUuid) {
        Sql sql = Sql.build(HomepageBackground.class, DEFAULT_FILED);
        sql.eq("account_uuid", accountUuid);
        sql.limit(1);
        return this.get(sql);
    }

}
