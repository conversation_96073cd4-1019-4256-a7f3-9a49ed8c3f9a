package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountLabelCfgDao;
import cn.taqu.account.model.AccountLabelCfg;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:02
 */
@Repository
public class AccountLabelCfgDaoImpl extends NimbleDaoImpl<Long, AccountLabelCfg> implements AccountLabelCfgDao {

    @Override
    public AccountLabelCfg isExistLabelSort(Integer sort) {
        String sql = "select id from account_label_cfg where parent_id != 0 and sort = ? limit 1";
        return this.get(Sql.build(sql, sort));
    }

    @Override
    public AccountLabelCfg isExistCategorySort(Integer sort) {
        String sql = "select id from account_label_cfg where parent_id = 0 and sort = ? limit 1";
        return this.get(Sql.build(sql, sort));
    }

    @Override
    public AccountLabelCfg isExistCategoryContent(String content) {
        String sql = "select id from account_label_cfg where parent_id = 0 and content = ? limit 1";
        return this.get(Sql.build(sql, content));
    }

    @Override
    public AccountLabelCfg isExistLabelContent(String content) {
        String sql = "select id from account_label_cfg where parent_id != 0 and content = ? limit 1";
        return this.get(Sql.build(sql, content));
    }

    @Override
    public List<AccountLabelCfg> listCategory(List<Long> categoryIds, Integer status) {
        String sqlStr = "select id, content, sort, data_status from account_label_cfg where parent_id = 0";
        Sql sql = Sql.build(sqlStr);
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            sql.in("id", categoryIds);
        }
        if (status != null) {
            sql.eq("data_status", status);
        }
        sql.orderBy("sort desc");
        return this.query(sql, AccountLabelCfg.class);
    }

    @Override
    public List<AccountLabelCfg> listLabel(List<Long> labelIds, Integer status, Integer gender) {
        String sqlStr = "select id, parent_id, content, icon_url, sort, data_status, gender from account_label_cfg";
        Sql sql = Sql.build(sqlStr);
        if (CollectionUtils.isNotEmpty(labelIds)) {
            sql.in("id", labelIds);
        }
        if (status != null) {
            sql.eq("data_status", status);
        }
        if (gender != null) {
            sql.in("gender", Lists.newArrayList(gender, 0));
        }

        sql.orderBy("sort desc");

        return this.query(sql, AccountLabelCfg.class);
    }

    @Override
    public void closeLabel(Long categoryId, long milli) {
        String sqlStr = "update account_label_cfg set data_status = 0, update_time = ? where parent_id = ?";
        this.update(Sql.build(sqlStr, milli, categoryId));
    }
}
