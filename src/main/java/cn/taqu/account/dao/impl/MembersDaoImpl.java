package cn.taqu.account.dao.impl;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.springframework.stereotype.Repository;

import cn.taqu.account.model.Members;
import cn.taqu.core.orm.Sql;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class MembersDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.Members> implements cn.taqu.account.dao.MembersDao {

    @Override
    public Long getIdByToken(String token) {
        return this.queryForLong(Sql.build("select id from members where token = ? order by id desc limit 1", token));
    }

    @Override
    public Members findOneByToken(String token) {
        return this.get(Sql.build("select id, token, create_time from members where token = ? order by id desc limit 1", token));
    }

    @Override
    public List<Members> findByTokens(Collection<String> tokens) {
        return this.query(Sql.build(Members.class, Arrays.asList("id", "token", "create_time")).in("token", tokens));
    }

    @Override
    public List<Members> findByMemberIds(Collection<Long> memberIds) {
        return this.query(Sql.build(Members.class, Arrays.asList("id", "token", "create_time")).in("id", memberIds));
    }

    @Override
    public List<Object[]> mGetTokenByIdList(Collection<Long> idList) {
        return this.query(Sql.build("select id, token from members").in("id", idList), Object[].class);
    }

    @Override
    public String getTokenById(Long id, boolean master) {
        if(id == null || id <= 0) {
            return "";
        }
        Sql sql = Sql.build("select token from members").eq("id", id, false);
        if(master) {
            sql = sql.masterDB();
        }
        return this.queryForString(sql);
    }

    @Override
    public Long getCreateTimeByToken(String token) {
        return this.queryForLong(Sql.build("select create_time from members where token = ? order by id desc limit 1", token));
    }

}
