package cn.taqu.account.dao.impl;

import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsThirdPartyDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsThirdParty> implements cn.taqu.account.dao.AccountsThirdPartyDao {

    @Override
    public int updateForDebind(Integer debindStatus, Long updateTime, String account_uuid, String type, Integer bindStatus) {
        return this.update(Sql.build("update accounts_third_party set status=?, update_time=? where account_uuid=? and type=? and status=?", debindStatus, updateTime, account_uuid, type, bindStatus));
    }

    @Override
    public Integer countByOpenIdAndType(String openId,String type) {
        return this.queryForInteger(Sql.build("select count(*) from accounts_third_party where open_id = ?  and type = ? ", openId, type));
    }

}
