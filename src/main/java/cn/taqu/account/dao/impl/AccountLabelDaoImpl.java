package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountLabelDao;
import cn.taqu.account.model.AccountLabel;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:02
 */
@Repository
public class AccountLabelDaoImpl extends NimbleDaoImpl<Long, AccountLabel> implements AccountLabelDao {

    @Override
    public AccountLabel getByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountLabel.class, DEFAULT_FILED)
                .eq("account_uuid", accountUuid)
                .limit(1);
        return this.get(sql);
    }
}
