package cn.taqu.account.dao.impl;

import cn.taqu.account.model.AccountsLevel;
import cn.taqu.core.orm.Sql;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsLevelDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsLevel> implements cn.taqu.account.dao.AccountsLevelDao {

    @Override
    public AccountsLevel getOneById(Long id) {
        return this.get(Sql.build("select id, level_num, min_level_score, max_level_score, honor_man, honor_women, honor_unsex, `desc`, privilege_id, create_time, update_time, privilege_desc, appcode from accounts_level where id = ?", id));
    }

    @Override
    public AccountsLevel getOneFromMasterDbById(Long id) {
        return this.get(Sql.build("select id, level_num, min_level_score, max_level_score, honor_man, honor_women, honor_unsex, `desc`, privilege_id, create_time, update_time, privilege_desc, appcode from accounts_level where id = ?", id).masterDB());
    }

    @Override
    public AccountsLevel getOneByLevelNum(Integer levelNum, Integer appcode) {
        Collection<String> fields = Arrays.asList("id","level_num","min_level_score","max_level_score","honor_man","honor_women","honor_unsex","`desc`","privilege_id","create_time","update_time","privilege_desc", "appcode");
        return this.get(Sql.build(AccountsLevel.class, fields).eq("level_num", levelNum).eq("appcode", appcode, false));
    }

    @Override
    public AccountsLevel getOneFromMasterDbByLevelNum(Integer levelNum, Integer appcode) {
        Collection<String> fields = Arrays.asList("id","level_num","min_level_score","max_level_score","honor_man","honor_women","honor_unsex","`desc`","privilege_id","create_time","update_time","privilege_desc", "appcode");
        return this.get(Sql.build(AccountsLevel.class, fields).eq("level_num", levelNum).eq("appcode", appcode, false).masterDB());
    }

}
