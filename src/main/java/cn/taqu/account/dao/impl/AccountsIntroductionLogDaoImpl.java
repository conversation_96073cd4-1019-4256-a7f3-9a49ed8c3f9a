package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsIntroductionLogDao;
import cn.taqu.account.model.AccountsIntroductionLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:35
 */
@Repository
public class AccountsIntroductionLogDaoImpl extends NimbleDaoImpl<Long, AccountsIntroductionLog> implements AccountsIntroductionLogDao {

    @Override
    public AccountsIntroductionLog getById(Long introductionLogId) {
        Sql sql = Sql.build(AccountsIntroductionLog.class, FIELDS_ALL)
                .eq("id", introductionLogId);
        sql.limit(1);
        List<AccountsIntroductionLog> introductions = this.query(sql);
        return null == introductions || introductions.size() == 0 ? null: introductions.get(0);
    }

    @Override
    public List<AccountsIntroductionLog> getIntroductionList(Integer pageNum, Integer pageSize, Integer status, Long startTime, Long endTime, String accontUuid, String operatorToken, String content) {
        Sql sql = Sql.build(AccountsIntroductionLog.class, FIELDS_ALL);

        if(StringUtils.isNotBlank(accontUuid)) {
            sql.eq("account_uuid", accontUuid);
        }

        sql.ge("create_time", startTime)
                .le("create_time", endTime);
        if (status != null) {//查询待审核的
            sql.eq("status", status);
        }
        if(StringUtils.isNotBlank(operatorToken)){
            sql.eq("operator", operatorToken);
        }

        if(StringUtils.isNotBlank(content)){
            sql.like("content", content);
        }
        sql.orderBy(" create_time desc ");
        sql.limit((pageNum - 1) * pageSize, pageSize);
        return this.query(sql);
    }

    @Override
    public List<AccountsIntroductionLog> queryNotAudit(Long expireTime, Long startTime, Integer status, Integer limit) {
        Sql sql = Sql.build(AccountsIntroductionLog.class, FIELDS_ALL)
            .eq("status", status)
            .lt("create_time", expireTime)
            .gt("create_time", startTime)
            .orderBy("create_time desc")
            .limit(limit);
        return this.query(sql);
    }
}
