package cn.taqu.account.dao.impl;

import cn.taqu.account.model.AccountsLabel;
import org.springframework.stereotype.Repository;

import java.util.Arrays;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsLabelDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsLabel> implements cn.taqu.account.dao.AccountsLabelDao {

    @Override
    public AccountsLabel findByAccountUuid(String account_uuid) {
        return this.querySingleByProp("account_uuid", account_uuid, Arrays.asList("id", "account_uuid", "like_type_label", "personality_label", "friendship_prefer_label"));
    }

}
