package cn.taqu.account.dao.impl;

import org.springframework.stereotype.Repository;

import cn.taqu.account.model.AccountsRegAttribution;
import cn.taqu.core.orm.Sql;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsRegAttributionDaoImpl
    extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsRegAttribution>
    implements cn.taqu.account.dao.AccountsRegAttributionDao {

    @Override
    public AccountsRegAttribution getByAccountUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsRegAttribution.class, FIELDS_ALL).eq("account_uuid", accountUuid);
        return this.get(sql);
    }

}
