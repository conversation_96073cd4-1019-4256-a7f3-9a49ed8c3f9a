package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsIntroductionPhotoLogDao;
import cn.taqu.account.model.AccountsIntroductionPhotoLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:35
 */
@Repository
public class AccountsIntroductionPhotoLogDaoImpl extends NimbleDaoImpl<Long, AccountsIntroductionPhotoLog> implements AccountsIntroductionPhotoLogDao {

    @Override
    public List<AccountsIntroductionPhotoLog> findByIntroductionLogId(Long introductionLogId) {
        if(introductionLogId == null){
            return new ArrayList<>();
        }
        Sql sql = Sql.build(AccountsIntroductionPhotoLog.class, FIELDS_ALL)
                .eq("introduction_log_id", introductionLogId);
        return this.query(sql);
    }
}
