package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.UnbindWhitelistDao;
import cn.taqu.account.model.UnbindWhitelist;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UnbindWhitelistDaoImpl extends NimbleDaoImpl<Long, UnbindWhitelist>  implements UnbindWhitelistDao {

    @Override
    public List<UnbindWhitelist> getUnbindWhitelists(String uuid) {
        if(StringUtils.isBlank(uuid)){
            return Lists.newArrayList();
        }
        Sql sql = Sql.build(UnbindWhitelist.class);
        sql = sql.eq("account_uuid",uuid);
        sql = sql.eq("is_delete", 0);
        List<UnbindWhitelist> list = this.query(sql);
        return list;
    }

    @Override
    public UnbindWhitelist getUnbindWhitelistById(Long id) {
        if(id==null){
            return null;
        }
        Sql sql = Sql.build(UnbindWhitelist.class);
        sql = sql.eq("id",id);
        sql = sql.eq("is_delete", 0);
        UnbindWhitelist unbindWhitelist = this.get(sql);
        return unbindWhitelist;
    }




    @Override
    public void updateUnbindWhitelistStatusById(Long id, Integer status) {
        if(id==null || status==null){
            return;
        }
        String sqlStr = "update unbind_whitelist set status = ? where id = ? ";
        Sql sql = Sql.build(sqlStr,status,id);
        this.update(sql);
    }
}
