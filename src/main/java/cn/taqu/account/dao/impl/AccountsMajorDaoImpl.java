package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsMajorDao;
import cn.taqu.account.model.AccountsMajor;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import cn.taqu.core.utils.DateUtil;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/11/27 上午10:09
 */
@Repository
public class AccountsMajorDaoImpl extends NimbleDaoImpl<Long, AccountsMajor> implements AccountsMajorDao {

    @Override
    public void upsert(AccountsMajor accountsMajor) {
        long now = DateUtil.currentTimeSeconds();
        Sql sql = Sql.build(
            "insert into accounts_major(account_uuid, school_id, major_origin, major, create_time, update_time) values (?, ?, ?, ?, ?, ?) " +
                "on duplicate key update school_id = ?, major_origin = ?, major = ?, update_time = ?",
            accountsMajor.getAccountUuid(), accountsMajor.getSchoolId(), accountsMajor.getMajorOrigin(), accountsMajor.getMajor(), now, now,
            accountsMajor.getSchoolId(), accountsMajor.getMajorOrigin(), accountsMajor.getMajor(), now
        );
        this.update(sql);
    }
}
