package cn.taqu.account.dao.impl;

import org.springframework.stereotype.Repository;

import cn.taqu.account.model.AccountsInfo;
import cn.taqu.core.orm.Sql;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsInfoDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsInfo> implements cn.taqu.account.dao.AccountsInfoDao {

    @Override
    public Long getTqcoinByAccountUuid(String account_uuid) {
    	return this.queryForLong(Sql.build("select tqcoin from accounts_info where account_uuid = ? order by id limit 1", account_uuid));
    }

    @Override
    public AccountsInfo getByAccountUuid(String account_uuid) {
        Sql sql = Sql.build(AccountsInfo.class, FIELDS_ALL).eq("account_uuid", account_uuid, false).orderBy("id").limit(1);
        return this.get(sql);
    }

    @Override
    public int addQuCoinNumByAccountUuid(Long addCoinNum, String account_uuid) {
    	return this.update(Sql.build("update accounts_info set tqcoin = tqcoin + ? where account_uuid = ?", addCoinNum, account_uuid));
    }

    @Override
    public int subQuCoinNumByAccountUuid(Long subCoinNum, String account_uuid) {
    	return this.update(Sql.build("update accounts_info set tqcoin = tqcoin-? where account_uuid=? and tqcoin>=?", subCoinNum, account_uuid, subCoinNum));
    }

    @Override
    public int updateRealPhotoCertByUuid(String accountUuid,Integer status){
        return this.update(Sql.build("update accounts_info set real_photo_certification = ? where account_uuid=? ",status, accountUuid));
    }

    @Override
    public int updateAccountPhotoNumber(String uuid, int photoNumber) {
        return this.update(Sql.build("update accounts_info set photo_number = ? where account_uuid=? ",photoNumber, uuid));
    }

    @Override
    public int updateAccountPhotoNumberById(Long id, Integer photoNumber) {
        return this.update(Sql.build("update accounts_info set photo_number = ? where id=? ",photoNumber, id));
    }

    @Override
    public int updateAccountsLifeSeq(String accountUuid, String seq) {
        return this.update(Sql.build("update accounts_info set accounts_life_seq = ? where account_uuid=? ", seq, accountUuid));
        
    }
    
    @Override
    public String getAccountsLifeSeq(String accountUuid) {
        return this.queryForString(Sql.build("select accounts_life_seq from accounts_info where account_uuid = ? order by id limit 1", accountUuid));
        
    }

}
