package cn.taqu.account.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;

import cn.taqu.account.dao.AccountsLikeLabelDao;
import cn.taqu.account.model.AccountsLikeLabel;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

@Repository
public class AccountsLikeLabelDaoImpl extends NimbleDaoImpl<Long, AccountsLikeLabel> implements AccountsLikeLabelDao {


	@Override
	public List<Long> listIdByAccountUuid(String accountUuid, Integer type) {
		Sql sql = Sql.build(AccountsLikeLabel.class, Lists.newArrayList("label_id"));
		sql.eq("account_uuid", accountUuid).eq("type", type);
		return this.queryForList(sql, Long.class);
	}

    @Override
    public List<AccountsLikeLabel> getListByUuid(String accountUuid) {
		Sql sql = Sql.build(AccountsLikeLabel.class, FIELDS_ALL)
				.eq("account_uuid", accountUuid);
		return this.query(sql);
    }


}
