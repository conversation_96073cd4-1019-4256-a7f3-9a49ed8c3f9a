package cn.taqu.account.dao.impl;

import java.util.Collection;
import java.util.List;

import org.springframework.stereotype.Repository;

import cn.taqu.account.dao.AccountsGuestDao;
import cn.taqu.account.model.AccountsGuest;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;

@Deprecated
@Repository
public class AccountsGuestDaoImpl extends NimbleDaoImpl<Long, AccountsGuest> implements AccountsGuestDao {

    @Override
    public AccountsGuest getByUuid(String accountUuid) {
        Sql sql = Sql.build(AccountsGuest.class, AccountsGuest.fields).eq("uuid", accountUuid);
        return this.get(sql);
    }

    @Override
    public AccountsGuest getByAccountName(String accountName) {
        Sql sql = Sql.build(AccountsGuest.class, AccountsGuest.fields).eq("account_name", accountName);
        return this.get(sql);
    }

    @Override
    public AccountsGuest getByMemberId(Long memberId) {
        Sql sql = Sql.build(AccountsGuest.class, AccountsGuest.fields).eq("member_id", memberId).orderBy("account_id desc").limit(1);
        return this.get(sql);
    }

    @Override
    public int updateTicket(String ticket, String accountUuid) {
        Sql sql = Sql.build("update accounts_guest set account_ticket=? where uuid=?", ticket, accountUuid);
        return this.update(sql);
    }

    @Override
    public List<AccountsGuest> getByUuidAndMemberIdAndName(String accountUuid, Long memberId, String accountName) {
        Sql sql = Sql.build(AccountsGuest.class, AccountsGuest.fields).eq("uuid", accountUuid).eq("member_id", memberId).rLike("account_name", accountName).orderBy("account_id desc").limit(50);
        return this.query(sql);
    }

    @Override
    public List<Object[]> getUuidAndAccountNameInUuids(Collection<String> uuids) {
        Sql sql = Sql.build("select uuid, account_name from accounts_guest").in("uuid", uuids);
        return this.query(sql, Object[].class);
    }

    @Override
    public List<Object[]> getUuidAndAccountNameAndRegtimeInUuids(Collection<String> uuids) {
        Sql sql = Sql.build("select uuid, account_name, create_time from accounts_guest").in("uuid", uuids);
        return this.query(sql, Object[].class);
    }
}
