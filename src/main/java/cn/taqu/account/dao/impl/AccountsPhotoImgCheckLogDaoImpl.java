package cn.taqu.account.dao.impl;

import cn.taqu.account.dao.AccountsPhotoImgCheckLogDao;
import cn.taqu.account.model.AccountsPhotoImgCheckLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDaoImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-13 18:49
 */
@Repository
public class AccountsPhotoImgCheckLogDaoImpl extends NimbleDaoImpl<Long, AccountsPhotoImgCheckLog> implements AccountsPhotoImgCheckLogDao {

    @Override
    public AccountsPhotoImgCheckLog getLastByUuid(String accountUuid, String photoUrl) {
        Sql sql = Sql.build(AccountsPhotoImgCheckLog.class, FIELDS_ALL);
        sql.eq("account_uuid", accountUuid).eq("photo_url", photoUrl).orderBy("id desc").limit(1);
        List<AccountsPhotoImgCheckLog> list = this.query(sql);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
