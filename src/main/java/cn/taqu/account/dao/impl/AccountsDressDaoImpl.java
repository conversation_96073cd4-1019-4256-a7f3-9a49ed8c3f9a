package cn.taqu.account.dao.impl;

import org.springframework.stereotype.Repository;

import cn.taqu.account.model.AccountsDress;
import cn.taqu.core.orm.Sql;

/**
 * 自动生成Dao接口实现类
 */
@Repository
public class AccountsDressDaoImpl extends cn.taqu.core.orm.base.NimbleDaoImpl<java.lang.Long, cn.taqu.account.model.AccountsDress> implements cn.taqu.account.dao.AccountsDressDao {

    @Override
    public AccountsDress getByUuid(String uuid, Integer dressType) {
        return this.get(Sql.build("select id, account_uuid, dress_id, status, dress_type, create_time, update_time from accounts_dress where account_uuid=? and dress_type=? limit 1", uuid, dressType));
    }

}
