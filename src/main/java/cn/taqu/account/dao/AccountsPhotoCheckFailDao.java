package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsPhotoCheckFail;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

public interface AccountsPhotoCheckFailDao extends NimbleDao<Long, AccountsPhotoCheckFail> {

    List<String> FIELDS_ALL = Lists.newArrayList("id","account_uuid","photo_url","bucket","seq","status","verify_status","error_msg","face_score","create_time","update_time", "risk_description", "risk_photo_url", "like_num", "type");

    /**
     * 根据用户ID获取最新审核失败头像
     * @param accountUuid
     * @return
     */
    AccountsPhotoCheckFail findCheckFailAvatarByUuid(String accountUuid);



}
