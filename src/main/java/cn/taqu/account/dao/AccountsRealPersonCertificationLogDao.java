package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsRealPersonCertificationLog;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-20 10:13
 */
public interface AccountsRealPersonCertificationLogDao extends NimbleDao<Long, AccountsRealPersonCertificationLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "live_face_detect_time", "real_person_certification_time",
            "source", "create_time", "update_time"
    );
}
