package cn.taqu.account.dao;

import cn.taqu.account.model.AccountFreezeAdminLogoutLog;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 图片对比日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 15:24
 */
public interface AccountFreezeAdminLogoutLogDao extends NimbleDao<Long, AccountFreezeAdminLogoutLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "operator",
            "create_time", "update_time"
    );

    PageData<AccountFreezeAdminLogoutLog> getFreezeAdminLogoutLogList(String uuid, String operator, Long startTime, Long endTime, Integer pageNo, Integer pageSize);

}
