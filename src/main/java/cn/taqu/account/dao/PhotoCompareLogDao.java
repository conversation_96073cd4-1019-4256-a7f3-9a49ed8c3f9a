package cn.taqu.account.dao;

import cn.taqu.account.model.PhotoCompareLog;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 图片对比日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 15:24
 */
public interface PhotoCompareLogDao extends Nimble<PERSON>ao<Long, PhotoCompareLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "base_photo_url", "verify_photo_url", "similarity_status", "similarity_score",
            "error_msg", "create_time", "update_time", "compare_type"
    );

}
