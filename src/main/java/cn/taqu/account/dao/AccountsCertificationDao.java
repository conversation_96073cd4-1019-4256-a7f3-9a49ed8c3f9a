package cn.taqu.account.dao;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.taqu.account.model.AccountsCertification;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * Created by cqa on 2017/11/6.
 */
public interface AccountsCertificationDao extends NimbleDao<Long, AccountsCertification> {

    List<String> FIELDS_ALL = Arrays.asList("id", "account_uuid", "real_name", "real_name_cipher", "real_name_digest", "identity_no"
        , "identity_no_cipher", "identity_no_digest", "identity_no_birth", "identity_type", "biz_no", "reward_account", "reward_account_checked"
        , "reward_account_status", "reward_account_cipher", "reward_account_digest", "is_checked", "certification_photo_url"
        , "identity_no_status", "create_time", "update_time");

    List<AccountsCertification> getAccountCertificationsByIdentityNo(String identityNo, Integer isChecked);

    List<AccountsCertification> getAccountCertificationsByIdentityNoDigest(String lowerIdentityNoDigest, String upperIdentityNoDigest, Integer isChecked);

    Object[] findIdByAccountUuid(String accountUuid);

    AccountsCertification getIdentityCipherByUuid(String accountUuid);

    Object[] findInfoByBizNo(String bizNo);

    Object[] findByAccountUuid(String accountUuid);

    void addKeyValue(Map<String, Object> keyValueMap, String uuid);

    void updateById(Map<String, Object> keyValueMap, Long id, String uuid);

    void updateRewardAccount(Integer rewardAccountChecked, Integer rewardAccountStatus, String rewardAccountCipher,String rewardAccountDigest, String accountUuid);

    /**
     * 更新提现账号验证状态
     * @param rewardAccountChecked
     * @param accountUuid
     */
    void updateRewardAccountChecked(Integer rewardAccountChecked, String accountUuid);

	AccountsCertification getByAccountUuid(String accountUuid);

    /**
     * 获取认证时的人脸图片
     * @param accountUuid
     * @return
     */
    String getCertificationPhotoUrlByUuid(String accountUuid);

    /**
     * 批量获取用户认证信息
     * @param uuidList
     * @return
     */
    List<AccountsCertification> getAccountsCertificationByUuids(List<String> uuidList);

    /**
     * @param rewardAccountDigest
     * @param rewardAccountStatus
     * @return
     */
    List<String> listAccountUuidByRewardAccountDigest(String rewardAccountDigest, Integer rewardAccountStatus);

    /**
     * @param accountUuid
     * @param newRewardAccountStatus
     * @param oldRewardAccountStatus
     */
    int updateRewardAccountStatus(String accountUuid, int newRewardAccountStatus, int oldRewardAccountStatus);


}
