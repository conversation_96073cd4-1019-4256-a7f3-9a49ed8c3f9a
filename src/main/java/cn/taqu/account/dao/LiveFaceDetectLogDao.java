package cn.taqu.account.dao;

import cn.taqu.account.model.LiveFaceDetectLog;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 活体认证日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-18 12:02
 */
public interface LiveFaceDetectLogDao extends NimbleDao<Long, LiveFaceDetectLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "base_photo_url", "verify_photo_url", "create_time", "update_time",
            "live_face_detect_type", "live_face_detect_status", "order_no", "result_status", "result_type", "remark"
    );

}
