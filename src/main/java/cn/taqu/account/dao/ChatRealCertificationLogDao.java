package cn.taqu.account.dao;

import cn.taqu.account.model.ChatRealCertificationLog;
import cn.taqu.account.search.ChatRealCertificationLogSearch;
import com.google.common.collect.Lists;

import java.util.List;

public interface ChatRealCertificationLogDao extends cn.taqu.core.orm.base.NimbleDao<Long, ChatRealCertificationLog> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "face_photo_url", "certification_photo_url", "similarity_score", "status", "create_time", "error_msg", "source");

    List<ChatRealCertificationLog> getChatRealCertificationLogByPage(ChatRealCertificationLogSearch search, Integer start, Integer pageSize);

}
