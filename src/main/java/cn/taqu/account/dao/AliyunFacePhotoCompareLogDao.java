package cn.taqu.account.dao;

import cn.taqu.account.model.AliyunFacePhotoCompareLog;
import cn.taqu.account.search.AliyunFacePhotoCompareLogSearch;
import com.google.common.collect.Lists;

import java.util.List;

public interface AliyunFacePhotoCompareLogDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AliyunFacePhotoCompareLog> {

    public static final List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "verify_photo_url", "base_photo_url",
            "similarity_score", "status", "accounts_photo_id", "create_time",
            "error_msg", "cancel_update_time", "account_gender", "base_photo_gender", "gender_compare_result");
    @Deprecated
    List<AliyunFacePhotoCompareLog> getAliyunFacePhotoCompareLogByPage(AliyunFacePhotoCompareLogSearch search, Integer start, Integer pageSize);

    /**
     * 根据uuid和起始时间查询时间
     * @param accountUuid
     * @param createTime
     * @return
     */
    List<AliyunFacePhotoCompareLog> findListByAccountUuidAndCreateTime(String accountUuid, Long createTime);

    /**
     * 批量更新性别根据id
     * @param updateCompareLogIds
     * @param accountSexType
     * @param gender
     * @param genderCompareResult
     */
    void batchUpdateGender(List<Long> updateCompareLogIds, Integer accountSexType, Integer gender, Integer genderCompareResult);

}
