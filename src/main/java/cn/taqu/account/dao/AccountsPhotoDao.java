package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsPhoto;

import java.util.Collection;
import java.util.List;

import com.google.common.collect.Lists;

/**
 * 自动生成Dao接口类
 */
public interface AccountsPhotoDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsPhoto> {
	List<String> FIELDS_ALL = Lists.newArrayList("id","account_uuid","photo_url","bucket","seq","status","verify_status","error_msg","face_score","create_time","update_time", "risk_description", "risk_photo_url", "like_num");

    List<AccountsPhoto> findAvatarByUuid(String accountUuid);

    List<AccountsPhoto> findByUuid(String accountUuid);
    
    AccountsPhoto findByUuidAndSeq(String accountUuid, Integer seq);

    List<AccountsPhoto> findAccountsPhotoInIds(Collection<Long> ids);

    List<AccountsPhoto> findByUuidList(List<String> uuidList);

	List<AccountsPhoto> getNoCheckPhoto(String accountUuid);

	Long getCountByVerifyStatus(String accountUuid,Integer verifyStatus);

    int getCountByStatus(String accountUuid, int status, boolean containAvatar);

	void updateVerifyStatusById(Long id,Integer verifyStatus,String errorMsg);

    /**
     * 根据图片id和url更新真人状态
     * @param id
     * @param verifyStatus
     * @param errorMsg
     * @param photoUrl
     */
    void updateVerifyStatusByIdAndPhotoUrl(Long id,Integer verifyStatus,String errorMsg, String photoUrl);

    /**
     * 更新
     * @param accountUuid
     * @param verifyStatus
     * @param errorMsg
     */
    void updateVerifyStatusAndStatusByUuid(String accountUuid, Integer verifyStatus, String errorMsg);

    /**
     * 根据主键id查找
     * @param photoId
     * @return
     */
    AccountsPhoto findById(Long photoId);


    /**
     * 根据uuid和状态数组获取图片列表
     * @param accountUuid
     * @param statusList
     * @return
     */
    List<AccountsPhoto> getPhotoListInStatus(String accountUuid, List<Integer> statusList);

    /**
     * 更新风险标识
     * @param id
     * @param riskPhotoUrl
     * @param riskDescription
     */
    @Deprecated
    void updateRiskUrlAndRiskDesc(Long id, String riskPhotoUrl, String riskDescription);


    /**
     * 获取用户
     * @param accountUuid
     * @param verifyStatus
     * @param ifCheck
     * @return
     */
    AccountsPhoto getAccountAvatar(String accountUuid, Integer verifyStatus, boolean ifCheck);


    /**
     * 更新相册只过机审
     * @param id
     */
    void updateCoverOnlyAutoCheck(Long id);

    /**
     * 更新未审核状态
     * @param id
     */
    void updateReviewCoverStatus(Long id);

    /**
     * 获取1小时未审核数据
     *
     * @param expireTime
     * @param startTime
     * @param seq
     * @param status
     * @param limit
     * @return
     */
    List<AccountsPhoto> queryNotAudit(Long expireTime, Long startTime, Integer seq, Integer status, Integer limit);

    /**
     * 批量更新状态
     */
    void batchUpdateStatus(List<Long> ids, Integer status, Integer verifyStatus);

}
