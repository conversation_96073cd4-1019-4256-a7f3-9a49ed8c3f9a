package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsMajor;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * <AUTHOR>
 * @date 2024/11/27 上午9:34
 */
public interface AccountsMajorDao extends NimbleDao<Long, AccountsMajor> {

    void upsert(AccountsMajor accountsMajor);

    default AccountsMajor getByUuid(String uuid) {
        Sql sql = Sql.build("SELECT  * FROM accounts_major WHERE account_uuid = ?", uuid);
        return get(sql, AccountsMajor.class);
    }
}
