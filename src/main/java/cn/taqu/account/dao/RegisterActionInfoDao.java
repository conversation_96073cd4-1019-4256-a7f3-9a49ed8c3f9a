package cn.taqu.account.dao;

import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;

public interface RegisterActionInfoDao extends NimbleDao<Long, RegisterActionInfo> {
    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "token", "action_ip", "appcode", "cloned",
            "platform_id", "channel", "app_version", "action_mode", "create_time",
            "platform_name", "access", "alias", "gender", "longitude", "longitude_cipher", "latitude", "latitude_cipher", "city"
    );
   List<String> FIELDS_PID_Version = Lists.newArrayList( "platform_id", "app_version");

    RegisterActionInfo getByUuid(String accountUuid);


    /**
     * 根据uuid获取注册平台及版本号
     *
     * @param accountUuid 账户uuid
     * @return {@link RegisterActionInfo}
     */
    default RegisterActionInfo getRegisterPidVersionByUuid(String accountUuid) {
        return this.get(Sql.build(RegisterActionInfo.class, FIELDS_PID_Version).eq("account_uuid", accountUuid, false));
    }

    /**
     * 批量获取
     *
     * @param uuids
     * @return
     */
    default List<RegisterActionInfo> listInfo(Collection<String> uuids) {
        String sqlStr = "select account_uuid, platform_id, app_version, create_time from register_action_info";
        Sql sql = Sql.build(sqlStr).in("account_uuid", uuids);
        return this.query(sql);
    }
}
