package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsCertificationHistory;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-29 14:09
 */
public interface AccountsCertificationHistoryDao extends NimbleDao<Long, AccountsCertificationHistory> {

    List<String> FIELDS_ALL = Arrays.asList("id", "account_uuid",
            "real_name", "identity_no", "identity_type", "reward_account", "reward_account_checked",
            "operator", "operator_time",
            "create_time", "update_time");

    AccountsCertificationHistory getByUuid(String accountUuid);

    /**
     * 获取身份认证类型
     *
     * @param accountUuid
     * @return
     */
    Integer getIdentityType(String accountUuid);

}
