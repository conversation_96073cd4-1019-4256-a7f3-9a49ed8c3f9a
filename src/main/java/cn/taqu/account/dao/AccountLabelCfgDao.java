package cn.taqu.account.dao;

import cn.taqu.account.model.AccountLabelCfg;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 用户标签配置
 *
 * <AUTHOR>
 * @date 2024/11/25 10:56
 */
public interface AccountLabelCfgDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, AccountLabelCfg> {

    /**
     * 默认字段
     */
    List<String> DEFAULT_FIELD =
            Lists.newArrayList("id", "content", "parent_id", "icon_url", "data_status", "sort", "create_time", "update_time", "gender", "introduce");

    /**
     * API字段
     */
    List<String> API_FIELD =
            Lists.newArrayList("id", "content", "icon_url", "sort");

    /**
     * 标签排序是否存在
     *
     * @param sort
     * @return
     */
    AccountLabelCfg isExistLabelSort(Integer sort);

    /**
     * 分类排序是否存在
     *
     * @param sort
     * @return
     */
    AccountLabelCfg isExistCategorySort(Integer sort);

    /**
     * 分类内容是否存在
     *
     * @param content
     * @return
     */
    AccountLabelCfg isExistCategoryContent(String content);

    /**
     * 标签内容是否存在
     *
     * @param content
     * @return
     */
    AccountLabelCfg isExistLabelContent(String content);

    /**
     * 批量获取分类信息
     *
     * @param categoryIds
     * @return
     */
    List<AccountLabelCfg> listCategory(List<Long> categoryIds, Integer status);

    /**
     * 批量获取标签
     *
     * @param labelIds
     * @return
     */
    List<AccountLabelCfg> listLabel(List<Long> labelIds, Integer status, Integer gender);

    /**
     * 关闭标签
     *
     * @param categoryId
     * @param milli
     */
    void closeLabel(Long categoryId, long milli);
}
