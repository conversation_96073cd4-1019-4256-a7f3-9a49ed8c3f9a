package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsIdealSample;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/2 上午10:24
 */
public interface AccountsIdealSampleDao extends NimbleDao<Long, AccountsIdealSample> {

    default List<AccountsIdealSample> listBySex(Integer sexType) {
        Sql sql = Sql.build("select id, sample from accounts_ideal_sample where sex_type = ?", sexType);
        return this.query(sql, AccountsIdealSample.class);
    }
}
