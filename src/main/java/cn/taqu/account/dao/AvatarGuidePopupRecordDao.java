package cn.taqu.account.dao;

import cn.taqu.account.model.AvatarGuidePopupRecord;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 头像指导弹窗记录DAO
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
public interface AvatarGuidePopupRecordDao extends NimbleDao<Long, AvatarGuidePopupRecord> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "popup_type", "popup_date", "check_date","avatar_url", "create_time", "update_time"
    );

    /**
     * 查询用户是否有弹窗记录
     *
     * @param accountUuid 用户UUID
     * @return 弹窗记录
     */
    AvatarGuidePopupRecord find(String accountUuid);

    /**
     * 查询指定日期范围内没有弹窗记录的用户UUID列表
     *
     * @param startDate 开始日期（YYYYMMDD格式）
     * @param id        查询时间
     * @param size      批次数量
     * @return 用户UUID列表
     */
    List<AvatarGuidePopupRecord> findNeverPopupBeforeDay(Integer startDate, Long id, Integer size);

}
