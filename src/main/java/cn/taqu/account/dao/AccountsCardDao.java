package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsCard;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.List;

public interface AccountsCardDao extends NimbleDao<Long, AccountsCard> {

    List<AccountsCard> getAllByUuidList(List<String> uuidList);
    List<Long> getUsedAndNormalCardByUuid(String accountUuid);
    List<AccountsCard> getByCardIdList(List<Long> cardIdList, List<String> fields);
    AccountsCard getByCardId(Long cardId);
    AccountsCard getByUuidAndCardId(String accountUuid, Long cardId);
    AccountsCard getByUuid(String accountUuid, String level);
    String getUuidByCardId(Long cardId);
    String getUuidByCardId(Long cardId, String level);
    List<AccountsCard> listUuidByCardId(List<Long> cardIds, String level);
    Long getCardByUuid(String accountUuid, String level);
    Integer updateOtherCardStatusByUuid(String accountUuid, Long cardId, Integer status);
    List<AccountsCard> getByUuid(String accountUuid, int page, int limit);
    int useCardId(String accountUuid, Long cardId);

    String getCardUuidInUseStatusByDB(Long cardId);
}
