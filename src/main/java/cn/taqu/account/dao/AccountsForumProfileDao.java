package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsForumProfile;

import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface AccountsForumProfileDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsForumProfile> {
    Integer getAccountLevelByAccountUuid(String accountUuid);
    AccountsForumProfile findByAccountUuid(String accountUuid);
    void batchUpdateActor(List<String> accountUuidList, int accountActor);
}
