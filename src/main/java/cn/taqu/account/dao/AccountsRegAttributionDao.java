package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.AccountsRegAttribution;

/**
 * 自动生成Dao接口类
 */
public interface AccountsRegAttributionDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsRegAttribution> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "token", "app_version", "appcode", "cloned", "channel", "platform_id", "media_code", "creative_label", "gid", "pid", "create_time", "update_time");
    
    /**
     * @param accountUuid
     * @return
     */
    AccountsRegAttribution getByAccountUuid(String accountUuid);

}
