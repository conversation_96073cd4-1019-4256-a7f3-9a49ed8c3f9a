package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsActiveLog;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname AccountsActiveLogDao
 * @Description 用户活跃记录表
 * @Date 2020/12/14 上午9:42
 */
public interface AccountsActiveLogDao extends NimbleDao<Long, AccountsActiveLog> {
    void insertIgnore(String accountUuid, Integer activeTime);

    List<Integer> findByUuid(String accountUuid);

    List<Long> findIdByUuid(String uuid);

    /**
     * 获取某一天的活跃用户uuid
     * @param activeTime    活跃时间yyyyMMdd
     * @param startId       起始id
     * @param size          数量
     * @return
     */
    List<AccountsActiveLog> getAccountsActive(Long activeTime, Long startId, Integer size);

    /**
     * 获取某一天的起始id
     * @param activeTime
     * @return
     */
    Long getAccountsActiveStartIdInDay(Long activeTime);
}
