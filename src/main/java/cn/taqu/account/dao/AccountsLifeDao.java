package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsLife;
import cn.taqu.core.orm.base.NimbleDao;
import com.google.common.collect.Lists;

import java.util.List;

public interface AccountsLifeDao extends NimbleDao<Long, AccountsLife> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "account_uuid", "photo_url_origin",
            "width_origin", "height_origin", "remark_origin", "photo_url",
            "width", "height", "remark", "seq", "status", "verify_status", 
            "create_time", "update_time");

    /**
     * @param accountUuid
     * @param seq
     * @return
     */
    AccountsLife getByAccountUuidAndSeq(String accountUuid, Integer seq);

    /**
     * @param accountUuid
     * @return
     */
    List<AccountsLife> getByAccountUuid(String accountUuid);

    /**
     * 更新状态
     *
     * @param life
     */
    void updateStatus(AccountsLife life);

}
