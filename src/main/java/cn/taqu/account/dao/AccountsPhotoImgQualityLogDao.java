package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.AccountsPhotoImgQualityLog;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-13 18:30
 */
public interface AccountsPhotoImgQualityLogDao extends NimbleDao<Long, AccountsPhotoImgQualityLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid","photo_url","long_image","black_and_white","small_image","big_image",
            "pure_image","clarity_score","aesthetic_score","response_str","third_order_no","create_time","update_time"
            );

    AccountsPhotoImgQualityLog getLastByUuid(String accountUuid, String photoUrl);
}
