package cn.taqu.account.dao;

import cn.taqu.account.model.RandomGroup;
import cn.taqu.account.search.RandomGroupSearch;
import cn.taqu.core.orm.PageData;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface RandomGroupDao extends cn.taqu.core.orm.base.NimbleDao<Long, RandomGroup> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "random_group_name", "status", "parts_of_speech", "create_time", "update_time");


    PageData<RandomGroup> findPageList(RandomGroupSearch search);

    List<RandomGroup> findListByStatus(Integer status);

    RandomGroup getById(Long id);
}
