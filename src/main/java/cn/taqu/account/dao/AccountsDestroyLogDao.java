package cn.taqu.account.dao;

import java.util.List;

import cn.taqu.account.model.AccountsDestroyLog;
import com.google.common.collect.Lists;

/**
 * 自动生成Dao接口类
 */
public interface AccountsDestroyLogDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsDestroyLog> {

    List<String> FIELDS_ALL = Lists.newArrayList(
            "id", "account_uuid", "mobile", "mobile_cipher", "mobile_digest", "account_name",
            "reason", "create_time", "status", "update_time", "wechat_id", "apple_id", "qq_id",
            "visitor_id", "weibo_id", "charm_rating", "need_to_limit_state"
    );

    int updateStatusByUuid(String uuid, Integer oldStatus, Integer newStatus);
    Long getLastCreateTime(List<String> uuidList, Integer status);


    /**
     * 通过qq open_id查询
     *
     * @param QQid qqid
     * @return {@link AccountsDestroyLog}
     */
    AccountsDestroyLog getByQqId(String QQid);

    /**
     * 通过微信号 查询
     *
     * @param wechatId 微信号
     * @return {@link AccountsDestroyLog}
     */
    AccountsDestroyLog getByWechatId(String wechatId);

    /**
     * @param AppleId
     * @return {@link AccountsDestroyLog}
     */
    AccountsDestroyLog getByAppleId(String AppleId);

    /**
     * 通过token查询游客注册
     *
     * @param token 令牌
     * @return {@link AccountsDestroyLog}
     */
    AccountsDestroyLog getByVisitorId(String token);

    /**
     * 通过微博openid查询
     * @param WeiboId
     * @return {@link AccountsDestroyLog}
     */
    AccountsDestroyLog getByWeiboId(String WeiboId);

    List<AccountsDestroyLog> getListByMobile(List<String> mobile, Integer status);
    
    /**
     * @param mobileDigestList
     * @param status
     * @return
     */
    List<AccountsDestroyLog> getListByMobileDigest(List<String> mobileDigestList, Integer status);
}
