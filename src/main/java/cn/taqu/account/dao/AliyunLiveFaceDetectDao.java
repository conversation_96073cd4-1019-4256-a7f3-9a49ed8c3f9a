package cn.taqu.account.dao;

import cn.taqu.account.model.AliyunLiveFaceDetect;

import java.util.List;

public interface AliyunLiveFaceDetectDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AliyunLiveFaceDetect> {

    AliyunLiveFaceDetect getInfoByAccountUuid(String accountUuid);

    AliyunLiveFaceDetect findById(Long id);

    List<AliyunLiveFaceDetect> getByPage(Integer offset,Integer limit);

    /**
     * 更新活体性别数据
     * @param id
     * @param accountGender
     * @param basePhotoGender
     * @param genderCompareResult
     */
    void updateLiveFaceDetectGenderById(Long id, Integer accountGender, Integer basePhotoGender, Integer genderCompareResult);

    /**
     * 根据用户uuid查找
     * @param accountUuid
     * @return
     */
    AliyunLiveFaceDetect findOneByAccountUuid(String accountUuid);
    
    /**
     * 获取用户活体底图
     * 
     * @param accountUuid
     * @return
     */
    String getBasePhotoUrlByAccountUuid(String accountUuid);

}
