package cn.taqu.account.dao;

import cn.taqu.account.model.HisImageSimilarRecord;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;
import cn.taqu.core.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/12 下午4:54
 */
public interface HisImageSimilarRecordDao extends NimbleDao<Long, HisImageSimilarRecord> {

    /**
     * 取未处理的相册相同照片
     *
     * @param id
     * @param limit
     * @return
     */
    default List<HisImageSimilarRecord> listPage(Long id, Integer limit) {
        Sql sql = Sql.build("SELECT id, origin_id, account_uuid, biz_id, photo_url, biz_type FROM his_image_similar_record WHERE state = 0 and id > ? order by id asc limit ?", id, limit);
        List<Map<String, Object>> maps = this.queryForList(sql);
        return maps.stream().map(item -> JsonUtils.mapper().convertValue(item, HisImageSimilarRecord.class)).collect(Collectors.toList());
    }

    /**
     * 批量状态更新
     *
     * @param ids
     */
    default void batchUpdateStatus(List<Long> ids, Integer status) {
        Sql sql = Sql.build("update his_image_similar_record set state = ? where id in (" + StringUtils.join(ids, ",") + ")", status);
        update(sql);
    }
}
