package cn.taqu.account.dao;

import cn.taqu.account.model.Members;

import java.util.Collection;
import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface MembersDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.Members> {

    Long getIdByToken(String token);

    Members findOneByToken(String token);

    List<Members> findByTokens(Collection<String> tokens);

    List<Members> findByMemberIds(Collection<Long> memberIds);

    List<Object[]> mGetTokenByIdList(Collection<Long> idList);

    String getTokenById(Long id, boolean master);

    Long getCreateTimeByToken(String token);

}
