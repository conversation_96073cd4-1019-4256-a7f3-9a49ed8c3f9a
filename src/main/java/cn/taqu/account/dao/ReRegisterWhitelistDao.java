package cn.taqu.account.dao;

import cn.taqu.account.model.ReRegisterWhitelist;
import cn.taqu.core.orm.base.NimbleDao;

/**
 * 重新注册白名单Dao
 *
 * <AUTHOR>
 * @date 2021/07/21
 */
public interface ReRegisterWhitelistDao extends NimbleDao<Long, ReRegisterWhitelist> {

    /**
     * 更新状态
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    void updateStatusByTime(long startTime,long endTime);


    /**
     * 根据手机查询是否存在白名单中
     *
     * @param mobile 手机号
     * @return
     */
    Integer getOneByMobile(String mobile);
    
    /**
     * 根据手机查询是否存在白名单中
     *
     * @param mobileDigest 手机号加密摘要
     * @return
     */
    Integer getOneByMobileDigest(String mobileDigest);

    Integer getOneByOpenid(Integer type, String openid);

}
