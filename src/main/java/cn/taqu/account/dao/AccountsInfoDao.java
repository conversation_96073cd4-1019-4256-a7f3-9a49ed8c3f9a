package cn.taqu.account.dao;

import java.util.List;

import com.google.common.collect.Lists;

import cn.taqu.account.model.AccountsInfo;

/**
 * 自动生成Dao接口类
 */
public interface AccountsInfoDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsInfo> {

    public static final List<String> FIELDS_ALL = Lists.newArrayList("id", "account_id", "account_uuid", "age", "birth", "constellation",
            "baseaddr", "hometown", "education", "income", "job", "sexual", "dating_intention", "tqcoin", "affectivestatus", "personal_profile",
            "personal_profile_status", "create_time", "update_sex_number", "update_time", "is_check_mobile", "sex_type_is_secret", "age_is_secret",
            "affectivestatus_is_secret", "baseaddr_is_secret", "hometown_is_secret", "sexual_is_secret", "personal_profile_be_allow", "gender_certification",
            "voice_certification", "zhima_certification", "aliyun_final_decision", "sm_type", "education_level", "trade", "profession", "height", "weight", "real_person_certification",
            "real_photo_certification", "photo_number", "chat_real_certification", "person_face_avatar",
            "make_friend_objective", "make_friend_objective_id", "change_age_status","register_avatar_status", "accounts_life_seq");

    Long getTqcoinByAccountUuid(String account_uuid);

    AccountsInfo getByAccountUuid(String account_uuid);

    int addQuCoinNumByAccountUuid(Long addCoinNum, String account_uuid);

    int subQuCoinNumByAccountUuid(Long subCoinNum, String account_uuid);

    int updateRealPhotoCertByUuid(String accountUuid,Integer status);

    int updateAccountPhotoNumber(String uuid, int photoNumber);

    /**
     * 根据主键更新图片数量
     * @param id
     * @param photoNumber
     * @return
     */
    int updateAccountPhotoNumberById(Long id, Integer photoNumber);

    /**
     * 更新我的生活排序
     * 
     * @param accountUuid
     * @param seq
     */
    int updateAccountsLifeSeq(String accountUuid, String seq);

    /**
     * @param accountUuid
     * @return
     */
    String getAccountsLifeSeq(String accountUuid);
}
