package cn.taqu.account.dao;

import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.model.AccountsMajorLog;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27 上午9:34
 */
public interface AccountsMajorLogDao extends NimbleDao<Long, AccountsMajorLog> {

    default AccountsMajorLog getById(Long id) {
        return querySingleByProp("id", id, Arrays.asList("id", "account_uuid", "major", "status", "risk_resp_data", "audit_time", "create_time", "update_time"));
    }

    default Integer count(String uuid, Integer status) {
        Sql sql = Sql.build("select count(id) from accounts_major_log where account_uuid = ? and status = ?", uuid, status);
        return queryForInteger(sql);
    }

//    default AccountsMajorLog getLastPass(String uuid, Long auditTime) {
//        Sql sql = Sql.build(AccountsMajorLog.class, Arrays.asList("id", "account_uuid", "major", "status"))
//            .eq("account_uuid", uuid)
//            .eq("status", CommonAuditStatus.AUDIT_SUCCESS.getStatus())
//            .lt("audit_time", auditTime)
//            .orderBy("audit_time desc")
//            .limit(1);
//        return this.get(sql, AccountsMajorLog.class);
//    }

    default List<AccountsMajorLog> queryNotAudit(Long expireTime, Long startTime, Integer limit) {
        Sql sql = Sql.build(AccountsMajorLog.class, Arrays.asList("id", "account_uuid", "major", "status"))
            .eq("status", CommonAuditStatus.AUDITING.getStatus())
            .lt("create_time", expireTime)
            .gt("create_time", startTime)
            .orderBy("id desc")
            .limit(limit);
        return this.query(sql);
    }
}
