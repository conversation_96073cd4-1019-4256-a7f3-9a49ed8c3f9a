package cn.taqu.account.dao;

import cn.taqu.account.model.RandomNickname;
import cn.taqu.account.search.RandomNicknameSearch;
import cn.taqu.core.orm.PageData;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 自动生成Dao接口类
 */
public interface RandomNicknameDao extends cn.taqu.core.orm.base.NimbleDao<Long, RandomNickname> {

    List<String> FIELDS_ALL = Lists.newArrayList("id", "random_nickname", "sex_type", "status", "group_id", "cloned", "create_time", "update_time");

    PageData<RandomNickname> findPageList(RandomNicknameSearch search);

    List<RandomNickname> findByGroupId(Long groupId);

    RandomNickname findById(Long id);

    List<RandomNickname> countNum();
}
