package cn.taqu.account.dao;

import java.util.Collection;
import java.util.List;

import cn.taqu.account.model.AccountsGrayList;

/**
 * 自动生成Dao接口类
 */
public interface AccountsGrayListDao extends cn.taqu.core.orm.base.NimbleDao<java.lang.Long, cn.taqu.account.model.AccountsGrayList> {

	List<AccountsGrayList> getByUuid(String uuid);
	
	void deleteInToken(Collection<String> tokenList);

	List<AccountsGrayList> getByToken(String token);

	void deleteInUuid(Collection<String> uuidList);

	AccountsGrayList findByAccountUuidAndToken(String accountUuid, String token);

	AccountsGrayList getOneByToken(String token);

	AccountsGrayList getOneByUuid(String accountUuid);

	Integer countByToken(String token);

	Integer countByUuid(String accountUuid);

}
