package cn.taqu.account.dao;

import cn.taqu.account.model.HomepageBackground;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 首页背景
 *
 * <AUTHOR>
 * @date 2024/11/25 10:56
 */
public interface HomepageBackgroundDao extends cn.taqu.core.orm.base.NimbleDao<Long, HomepageBackground> {

    /**
     * 默认字段
     */
    List<String> DEFAULT_FILED = Lists.newArrayList("id", "account_uuid", "background_id", "create_time", "update_time");

    /**
     * 获取背景
     *
     * @param accountUuid
     * @return
     */
    HomepageBackground getByUuid(String accountUuid);

}
