package cn.taqu.account.dao;

import cn.taqu.account.model.AccountsIntroductionSample;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.orm.base.NimbleDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/2 上午10:24
 */
public interface AccountsIntroductionSampleDao extends NimbleDao<Long, AccountsIntroductionSample> {

    default List<AccountsIntroductionSample> listBySex(Integer sexType) {
        Sql sql = Sql.build("select id, sample, images, sex_type from accounts_introduction_sample where sex_type = ?", sexType);
        return this.query(sql, AccountsIntroductionSample.class);
    }
}
