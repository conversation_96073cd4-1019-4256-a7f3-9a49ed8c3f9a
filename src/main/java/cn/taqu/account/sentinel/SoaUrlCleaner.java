package cn.taqu.account.sentinel;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.callback.UrlCleaner;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;


public class SoaUrlCleaner implements UrlCleaner {

    @Override
    public String clean(String originUrl) {
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        if (requestAttributes == null) {
            return originUrl;
        }
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            return originUrl;
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes)requestAttributes;
        HttpServletRequest request = servletRequestAttributes.getRequest();
        if (request == null) {
            return originUrl;
        }
        String service = request.getParameter("service");
        String method = request.getParameter("method");
        if (StringUtils.isEmpty(service) || StringUtils.isEmpty(method)) {
            return originUrl;
        }
        String source=originUrl + "/" + service + "/" + method;
        int i=0;
        while (source.contains("//") && i<10){
            i++;
            source=source.replace("//","/");
        }
        return source;
    }
}
