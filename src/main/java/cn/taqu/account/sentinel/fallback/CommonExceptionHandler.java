package cn.taqu.account.sentinel.fallback;

import cn.taqu.account.dto.RiskPictureCheckReqDTO;
import cn.taqu.account.dto.RiskPictureCheckRespDTO;
import cn.taqu.account.dto.RiskSafeCheckRequestDTO;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.UndeclaredThrowableException;

@Slf4j
public class CommonExceptionHandler {

    public static JsonResult commonFallBack(RequestParams params, Throwable ex){
        if(ex instanceof UndeclaredThrowableException){
            UndeclaredThrowableException ue=(UndeclaredThrowableException)ex;
            ex=ue.getUndeclaredThrowable();
        }
        if(ex instanceof BlockException){
            return blockExceptionHandler((BlockException)ex, params);
        }
        log.warn("commonFallBack error params:{}",JsonUtils.objectToString(params),ex);
        return JsonResult.failed("系统异常，请稍后再试!");
    }

    public static JsonResult blockExceptionHandler(BlockException e, RequestParams params){
        String rule= JSON.toJSONString(e.getRule());
        log.warn("blockExceptionHandler params:{}:rule:{}",JsonUtils.objectToString(params),rule);
        return JsonResult.failed("活动太火爆了，请稍后再试!");
    }

    public static JsonResult blockExceptionHandler(BlockException e, String params){
        String rule= JSON.toJSONString(e.getRule());
        log.warn("blockExceptionHandler params:{}:rule:{}",params,rule);
        return JsonResult.failed("活动太火爆了，请稍后再试!");
    }

    public static JsonResult getInfoByUuidBlockHandler(RequestParams params, BlockException be){
        String[] uuids = params.getFormStringArray(0);
        String rule= JSON.toJSONString(be.getRule());
        log.warn("blockExceptionHandler uuids:{}|rule:{}",uuids,rule);
        return JsonResult.failed("活动太火爆了，请稍后再试!");
    }

    public static Boolean checkOpenIdRiskBlockHandler(String type, String openId, BlockException be){
        String rule= JSON.toJSONString(be.getRule());
        log.warn("checkOpenIdRiskBlockHandler type:{}|openId:{}|rule:{}",type,openId,rule);
        return false;
    }

    public static RiskSafeCheckResponseDTO checkBlockHandler(RiskSafeCheckRequestDTO request, BlockException be) {
        String rule= JSON.toJSONString(be.getRule());
        String safeId=request.getSafeId();
        String bizId=request.getBizId();
        String senderUuid=request.getSenderUuid();
        String receiverUuid=request.getReceiverUuid();
        log.warn("checkBlockHandler safeId:{}|bizId:{}|senderUuid:{}|receiverUuid:{}|rule:{}",safeId,bizId,senderUuid,receiverUuid,rule);
        // 返回待审核数据
        RiskSafeCheckResponseDTO dto = new RiskSafeCheckResponseDTO();
        dto.setRequestId(SoaBaseParams.fromThread().getDistinctRequestId());
        dto.setSafeId(request.getSafeId());
        dto.setBizId(request.getBizId());
        dto.setSenderUuid(request.getSenderUuid());
        dto.setReceiverUuid(request.getReceiverUuid());
        dto.setHitTypeCode("wait");
        dto.setLabel1("");
        dto.setLabel2("");
        dto.setLabel3("");
        return dto;
    }

    public static RiskPictureCheckRespDTO pictureCheckBlockHandler(RiskPictureCheckReqDTO request, BlockException be) {
        // 返回待审核数据
        RiskPictureCheckRespDTO dto = new RiskPictureCheckRespDTO();
        dto.setPermitStatus(-1);
        return dto;
    }
}
