package cn.taqu.account.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 风控认证类型
 *
 * <AUTHOR>
 * @date 2025/7/14 13:50
 */
@Getter
@AllArgsConstructor
public enum RiskCertificationTypeEnum {

    CERTIFICATION("CERTIFICATION", "应用级实名认证"),

    BUSINESS_CERTIFICATION("BUSINESS_CERTIFICATION", "业务级实名认证"),

    REAL_PEOPLE("REAL_PEOPLE", "真人认证"),

    LIVING_ORGANISM("LIVING_ORGANISM", "活体认证"),

    UNKNOWN("UNKNOWN", "历史未知类型认证"),

    PHOTO_ALBUM("PHOTO_ALBUM", "更新头像/相册"),

    WITHDRAWAL_CERTIFICATION("WITHDRAWAL_CERTIFICATION", "提现实名比对"),

    ACCOUNT_LIFE("ACCOUNT_LIFE", "更新生活照"),

    ;

    private final String type;

    private final String desc;

    public static RiskCertificationTypeEnum getBy(String code) {
        for (RiskCertificationTypeEnum value : RiskCertificationTypeEnum.values()) {
            if (Objects.equals(code, value.type)) {
                return value;
            }
        }

        return UNKNOWN;
    }

}
