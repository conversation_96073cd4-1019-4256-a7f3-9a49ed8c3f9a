package cn.taqu.account.constant;

/**
 * <AUTHOR> Wu.D.J
 */
public class AvatarConst {

    /**
     * 默认男性头像
     */
    public static String DEFAULT_MALE_AVATAR = "avatar/newavatarmale.jpg";

    /**
     * 2024.04.18 产品确认不会使用该头像
     * 新版默认男性头像(2023.06)
     */
    @Deprecated
    public static String DEFAULT_MALE_AVATAR_V2 = "avatar/new_avatar_male_v2.jpg";

    /**
     * @param newRegister202306 是否2023.06新版本注册用户
     * @return 默认男性头像
     */
    @Deprecated
    public static String getDefaultMaleAvatar(boolean newRegister202306) {
        return newRegister202306 ? DEFAULT_MALE_AVATAR_V2 : DEFAULT_MALE_AVATAR;
    }

    /**
     * 默认女性头像
     */
    public static String DEFAULT_FEMALE_AVATAR = "avatar/newavatarfemale.jpg";

    /**
     * 2024.04.18 产品确认不会使用该头像
     * 新版默认女性头像(2023.06)
     */
    @Deprecated
    public static String DEFAULT_FEMALE_AVATAR_V2 = "avatar/new_avatar_female_v2.jpg";

    @Deprecated
    public static String getDefaultFemaleAvatar(boolean newRegister202306) {
        return newRegister202306 ? DEFAULT_FEMALE_AVATAR_V2 : DEFAULT_FEMALE_AVATAR;
    }

    /**
     * 2024.04.18 存储空间上都没有这张图
     * 默认无性别头像
     */
    @Deprecated
    public static String DEFAULT_ASEXUAL_AVATAR = "avatar/new_avatar_asexual.png";
}
