package cn.taqu.account.constant;

import java.text.MessageFormat;

/**
 * 定义account redis缓存key
 * @ClassName AccountRedisKeyConstant.java
 * <AUTHOR>
 * @date 2015年9月21日 上午11:16:16
 */
public class RedisKeyConstant {

	/**********************************************
	 *           account redis相关的key             *
	 **********************************************/

	/**
	 * java用,存放的数据与ACCOUNT_TICKET相同
	 */
    @Deprecated
	public static final Formater ACCOUNT_TICKET_JAVA = new Formater("account:ticket:java:{0}");

	public static final Formater ACCOUNT_KEY_JAVA = new Formater("account:key:{0}");

	/**
	 * 重复照片治理相关
	 */
	public static final String USER_PHOTO_DETECT_VERSION_SET = "user:photo:similar:version:set";
	public static final String LAST_ACTIVE_USER_QUEUE_30 = "account:active:queue:v2:30";
	public static final String HIS_PHOTO_DETECT_STATS = "user:his:photo:detect:stats:v2";
	public static final String USER_PHOTO_DETECT_CUTOFF = "user:photo:detect:cutoff";

	/**
	 * 0: 当日
	 */
	public static final Formater AVATAR_MODIFY_TIME = new Formater("avatar:modify:{0}");

	/**
	 * 三方登陆方式
	 */
	public static final Formater ACCOUNT_THIRD_LOGIN_CHANNEL = new Formater("account:third:login:channel:{0}");

	/**
	 * 网安要求 未活跃天数 任务=》绑定手机
	 */
	public static final Formater ACCOUNT_NO_ACTIVE_TASK_WA_BIND_MOBILE = new Formater("account:noactive:task:wa:bind:mobile:{0}");

	/**
	 * 2017.6.27	avatar，nickname，uuid，sex_type，is_check_mobile，create_time，level
	 * 暂时缓存{直播im}数据的key，key的完整格式为：account:infos:uuid:%s，%s为accountUuid<br/>
	 */
	public static final Formater ACCOUNT_INFOS_UUID = new Formater("account:infos:uuid:{0}");
	public static final Formater ACCOUNT_CARD_UUID = new Formater("account:cd:uuid:{0,Number,#}");

	public static final Formater CARD_TTL_SET = new Formater("card:ttl:set:{0}");
	public static final Formater PRE_GRANT_CARD = new Formater("pre:grant:card");

	/**
	 * 用户位置信息保存锁
	 */
	public static final Formater ACCOUNT_LOCAITON_INFO_ADD = new Formater("account:location:info:add:{0}");

	/**
	 * 用户位置信息 map
	 */
	@Deprecated
	public static final Formater ACCOUNT_LOCAITON_INFO = new Formater("account:location:info:{0}");
	/**
	 * 用户位置信息 map
	 */
	public static final Formater ACCOUNT_LOCATION_INFO = new Formater("account:loc:info:{0}");

	/**
	 * 用户相册缓存，key的完整格式为：account:photo:%s，%s为account_uuid<br/>
	 */
	public static final Formater ACCOUNT_PHOTO = new Formater("account:photo:{0}");
	/**
	 * 我的生活缓存，key的完整格式为：accounts:life:pic:%s，%s为account_uuid<br/>
	 */
	public static final Formater ACCOUNTS_LIFE_PIC = new Formater("accounts:life:pic:{0}");
	/**
	 * 我的生活排序缓存，key的完整格式为：accounts:life:seq:%s，%s为account_uuid<br/>
	 */
	public static final Formater ACCOUNTS_LIFE_SEQ = new Formater("accounts:life:seq:{0}");
	/**
	 * 图片违规检测结果
	 * md5(uuid + url)
	 */
	public static final Formater ACCOUNT_PHOTO_IMG_CHECK_RESULT = new Formater("account:photo:img:check:result:{0}");
	/**
	 * 图片质量检测结果
	 * md5(uuid + url)
	 */
	public static final Formater ACCOUNT_PHOTO_IMG_QUALITY_RESULT = new Formater("account:photo:img:quality:result:{0}");
	/**
	 * 图片比对检测结果
	 * md5(uuid + base_url + verify_url)
	 */
	public static final Formater ACCOUNT_PHOTO_IMG_COMPARE_RESULT = new Formater("account:photo:img:compare:result:{0}");

	/**
	 * 封面审核队列
	 */
	public static final Formater ACCOUNT_HOME_COVER_CHECK = new Formater("account:home_cover:check");

	/**
	 * 新用户上传图片 zset value是jsonstr，score是按照优先级后的分值
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_NEW = new Formater("account:photo:check:new");

	/**
	 * 老用户上传图片 zset value是jsonstr，score是按照优先级后的分值
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_OLD = new Formater("account:photo:check:old");


	/**
	 * 当前正在审核的图片id 相当于给图片上锁3分钟
	 * KEY:	account:photo:check:current:{coverId} VALUE: {自增锁}
	 *
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_CURRENT_ID = new Formater("account:photo:check:current:id:{0}");

	/**
	 * 当前正在审核的图片的用户 默认3分钟过期
	 * KEY: account:home_cover:check:current:{home_cover_id} VALUE：{operatorToken}
	 * 当前正在审核 home_cover_id 的操作员token
	 */
	public static final Formater ACCOUNT_HOME_COVER_CHECK_CURRENT  = new Formater("account:home_cover:check:current:{0}");

	/**
	 * 当前操作员正在审核的数据 默认3分钟过期
	 * KEY: account:home_cover:check:operator:{operatorToken} VALUE: {home_cover_id}
	 * 操作员 operatorToken 正在审核的 home_cover_id
	 */
	public static final Formater ACCOUNT_HOME_COVER_CHECK_OPERATOR = new Formater("account:home_cover:check:operator:{0}");

	// ====== 1118需求 拆分审核队列
	// 非真人头像审核
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_NEW_AVATAR_FZR = new Formater("account:photo:check:new:avatar:fzr");
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_OLD_AVATAR_FZR = new Formater("account:photo:check:old:avatar:fzr");

	// 真人头像审核
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_NEW_AVATAR_ZR = new Formater("account:photo:check:new:avatar:zr");
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_OLD_AVATAR_ZR = new Formater("account:photo:check:old:avatar:zr");

	// 真人认证 相册 审核
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_NEW_COVER_ZR = new Formater("account:photo:check:new:cover:zr");
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_OLD_COVER_ZR = new Formater("account:photo:check:old:cover:zr");

	/**
	 * 当前正在审核的图片id 相当于给图片上锁3分钟
	 * KEY:	account:photo:check:current:{photoId} VALUE: {自增锁}
	 *
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_CURRENT  = new Formater("account:photo:check:current:{0}");

	/**
	 * 推送记录 md5(type+id+url)
	 */
	public static final Formater ACCOUNT_PHOTO_PUSH_REVIEW = new Formater("account:photo:review:push:{0}");

	/**
	 * 当前操作删除用户的uuid
	 */
	public static final Formater ACCOUNT_PHOTO_DOING_DELETE_UUID = new Formater("account:photo:doing:delete:uuid:{0}");

	/**
	 * 当前正在审核的图片的uuid 默认5分钟过期
	 * KEY: account:photo:check:current:uuid:{account_uuid} VALUE：{operatorToken}
	 * 当前正在审核 account_uuid 的操作员token
	 * PS：这个锁是为了防止不同操作员拉到 同一个人的不同图片，同时操作，可能引发的死锁(对同一个uuid下的图片全量merge)
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_CURRENT_UUID  = new Formater("account:photo:check:current:uuid:{0}");

	/**
	 * 当前正在审核的图片的用户 默认3分钟过期
	 * KEY: account:photo:check:current:photo:{photoId} VALUE：{operatorToken}
	 * 当前正在审核 photoId 的操作员token
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_CURRENT_PHOTO  = new Formater("account:photo:check:current:photo:{0}");

	/**
	 * 当前正在正在审核的操作员列表Set 默认3分钟过期
	 * KEY: account:photo:check:operator:{operatorToken} VALUE: {photoId}
	 * 操作员 operatorToken 正在审核的图片id
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_OPERATOR = new Formater("account:photo:check:operator:{0}");

	/**
	 * 当前操作员
	 * KEY: account:photo:check:operator:list VALUE: ['operatorToken1', 'operatorToken2', 'operatorToken3']
	 * 当前操作员列表，set，set里面的值不会过期，因此 返回此列表的时候要循环判断下ACCOUNT_PHOTO_CHECK_OPERATOR 的key是否还存在
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_OPERATOR_LIST = new Formater("account:photo:check:operator:list");

	/**
	 * 数美审核
	 * KEY：account:photo:check:shumei:{图片url} VALUE：风险描述
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_SHUMEI = new Formater("account:photo:check:shumei:{0}");

	/**
	 * 图片删除url md5 保存30分钟
	 */
	public static final Formater ACCOUNT_DELETE_PHOTO_URL_MD5 = new Formater("account:delete:photo:url:md5:{0}");

	/**
	 * 近期图片回捞
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHOTO_CHECK_LIST_PULL_LASTTIME = new Formater("account:photo:check:list:pull:lasttime");

    /**
	 * 高颜值图片审核锁(更新)
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHTOT_FACE_LEVEL_UPDAET = new Formater("account:photo:face:level:update:{0}");


	/**
	 * 高颜值图片审核锁(插入)
	 */
	@Deprecated
	public static final Formater ACCOUNT_PHTOT_FACE_LEVEL_INSERT = new Formater("account:photo:face:level:insert:{0}:{1}");

	/**
	 * 点赞 当天23：59：59过期
	 * {0} = md5(图片id+url+点赞人uuid)
	 */
	public static final Formater ACCOUNT_PHOTO_THUMBS_UP = new Formater("account:photo:thumps:up:{0}");

	public static final Formater ACCOUNT_SCRIPT_RUN = new Formater("account:script:run:{0}");
	public static final Formater ACCOUNT_SCRIPT_RUN_AVATAR_SCRIPT_COMMIT = new Formater("account:script:run:avatar:script:commit:{0}");
	public static final Formater ACCOUNT_SCRIPT_RUN_COVER_SCRIPT_COMMIT = new Formater("account:script:run:cover:script:commit:{0}");
	/**
	 * 活体性别 account:live:face:detect:gender:{accountUuid} HASH 1小时过期
	 * value:
	 * 	gender 字符串 0 1 2
	 * 	baseUrl 字符串 去前缀
	 */
	@Deprecated
	public static final Formater ACCOUNT_LIVE_FACE_DETECT_GENDER = new Formater("account:live:face:detect:gender:{0}");

	/**
	 * 用户审核信息保存，目前有语音签名和个人简介
	 */
	public static final Formater ACCOUNT_AUDIT_INFO = new Formater("account:audit:{0}");


	/**
	 * 用户个人简介次数
	 */
	public static final Formater ACCOUNT_AUDIT_PERSONALPROFILE_TIMES_INFO = new Formater("account:audit:personalprofile:times:{0}");

	/**
	 * 用户个人简介次数
	 */
	public static final Formater ACCOUNT_SUBMIT_INTRODUCTION_TIMES = new Formater("account:submit:introduction:times:{0}");

	/**
	 * 异步超时锁 UUID
	 */
	public static final Formater ACCOUNT_SUBMIT_INTRODUCTION_TIMEOUT = new Formater("account:submit:introduction:timeout:{0}");

	/**
	 *用于在redis中保存积分等级及等级对应积分<br/>
	 * 由于account:level:syn:all这个key已被php使用，且其缓存的value经过php序列化，因此在java中，将key中增加一个java以与php的key区分开来
	 * 2016.10.24 将此key在reids的名称替换为————“account:level:all:java” {0}appcode
	 */
	public static final Formater ACCOUNT_LEVEL_SYN_ALL = new Formater("account:level:own:java:{0,Number,#}");

	public static final Formater LEVEL_INFO = new Formater("level:info:{0,Number,#}");
	/**
	 * 缓存单个等级信息 {0}appcode {1}为等级
	 */
	public static final Formater LEVEL_NUM_INFO = new Formater("level:num:info:{0,Number,#}:{1,Number,#}");

	/**
	 * 缓存手机验证码的key,key的完整格式为：verifycode:mobile:手机号码:%s，(%s: 0代表重置密码，1代表注册，2代表绑定手机号码，3代表修改手机号码)
	 */
	@Deprecated
	public static final Formater VERIFYCODE_MOBILE = new Formater("verifycode:mobile:{0}:{1}");
	/**
	 * 是否有过注册校验, {0}为手机号
	 */
	public static final Formater IS_REG_VERIFY = new Formater("is:reg:verify:{0}");

	/**
	 * 手机验证码注册缓存，{0}为手机号
	 */
	public static final Formater MOBILE_REG_VERIFY = new Formater("mobile:reg:verify:{0}");

	/**
	 * 手机验证码注册成功后缓存，{0}为验证码
	 */
	public static final Formater MOBILE_REG_SUCCESS = new Formater("mobile:reg:success:{0}");

	/**
	 * 缓存等级勋章配置的key，%s为medalId的值<br/>
	 */
	public static final Formater ACCOUNT_MEDAL = new Formater("account:medal:{0,Number,#}");

	/**
	 * 缓存用户保密设置的key,key的完整格式为：account:secret:%s，%s为accountUuid<br/>
	 */
	@Deprecated
	public static final Formater ACCOUNT_SECRET_UUID = new Formater("account:secret:uuid:{0}");

	/**
	 * 缓存每个手机号每天发送的短信数量,key的完整格式为：verify:mobile:info:%s，%s为手机号
	 */
	@Deprecated
	public static final Formater VERIFY_MOBILE_INFO = new Formater("verify:mobile:info:{0}");

	/**
	 * 缓存每台设备每天发送的短信数量,key的完整格式为：verify:mobile:info:%s，%s为设备号
	 */
	@Deprecated
	public static final Formater VERIFY_DEVICE_INFO = new Formater("verify:device:info:{0}");

	/**
	 * 缓存（ 0:重置密码;1:注册;2:绑定手机;3:修改手机;4:社区发帖语音认证）操作中能够发送验证码的次数，
	 * key的完整格式为：verifycode:send:limit:%s,%s为手机号
	 */
	@Deprecated
	public static final Formater VERIFYCODE_SEND_LIMIT = new Formater("verifycode:send:limit:{0}");

	/**
	 * 昵称最后修改时间，{0}为uuid
	 */
	public static final Formater NICKNAME_UPD_TIME = new Formater("nickname:upd:time:{0}");

	/**
	 * 个人简介最后修改时间，{0}为用户uuid
	 */
	public static final Formater PROFILE_UPD_TIME = new Formater("profile:upd:time:{0}");

	/**
	 * 2016.11.28
	 * 冻结昵称,	昵称一旦被注册了，在一天内即使该用户改名了也不让别人注册同样的昵称，也就是冻结一天
	 */
	@Deprecated
	public static final Formater ACCOUNT_NICKNAME_FROZEN = new Formater("account:nickname:frozen:{0}");

	/**
	 * 记录设备创建时间，{0}为设备token
	 */
	public static final Formater MEMBER_CREATE_TIME = new Formater("member:create:time:{0}");

	/**
	 * 15天内活跃用户uuid记录
	 */
	@Deprecated
	public static final Formater MEMBER_ACTIVE_15DAY_UUID_LIST = new Formater("member:active:15day:uuid:list:{0}");

	/**
	 * 用户活跃日期记录
	 */
	public static final Formater ACCOUNT_ACTIVE_DATE_LIST = new Formater("account:active:date:list:{0}");

	/**
	 * 每天获取未活跃日期记录
	 */
	public static final Formater NO_ACTIVE_DAY_GET = new Formater("no:active:day:get:{0}:{1}");

	/**
	 * 设备token活跃日期记录
	 */
	@Deprecated
	public static final Formater TOKEN_ACTIVE_DATE_LIST = new Formater("token:active:date:list:{0}");
	public static final Formater TOKEN_ACTIVE_DT = new Formater("token:active:dt:{0}");

	/**
	 * 最近打开设备的账号
	 */
	public static final Formater RECENT_OPEN_ACCOUNT = new Formater("recent:open:account");
	/**
	 * 用户最后打开设备时间
	 */
	public static final Formater ACCOUNT_ACTIVE_TIME = new Formater("account:active:time:{0}");
	/**
	 * 活跃用户UUID
	 */
	public static final Formater ACCOUNT_ACTIVE_UUID = new Formater("account_active_uuid:{0}");

	/**
	 * 已注册的昵称
	 */
	@Deprecated
	public static final Formater REGISTERED_NICKNAME = new Formater("registered:nickname:{0}");

	/**
	 * 限制登录次数 ：记录（token）访问登录次数
	 */
	public static final Formater VERIFY_LOGIN_TOKEN = new Formater("verify:login:token:{0}");

	/**
	 * 限制登录次数 ：记录（ip）访问登录次数
	 */
	public static final Formater VERIFY_LOGIN_IP = new Formater("verify:login:ip:{0}");

	/**
	 * 限制登录次数 ：记录（account）访问登录次数
	 */
	public static final Formater VERIFY_LOGIN_ACCOUNT = new Formater("verify:login:account:{0}");

	public static final Formater DATA_CENTER_BURY = new Formater("data_center_bury");
	public static final Formater ANTISPAM_BURY = new Formater("antispam_bury");

	@Deprecated
	public static final Formater CONTENT_FILTER_QUEUE_NICKNAME = new Formater("content_filter_queue_nickname");

	@Deprecated
	public static final Formater TICKET_REGISTER_TIMES = new Formater("ticket:register:times:{0}");

	/**
	 * 缓存标签id集合,key的完整格式为：personality:label:id:set
	 */
	public static final Formater PERSONALITY_LABEL_ID_SET = new Formater("personality:label:id:set");

	/**
	 * 分类一下比较合理，较少大key和带宽压力
	 * 缓存标签id集合
	 */
	public static final Formater PERSONALITY_LABEL_TYPE_ID = new Formater("py:lab:id:type:{0}");

	/**
	 * 缓存标签数据,key的完整格式为：personality:label:data:%s，%s为标签id
	 */
	public static final Formater PERSONALITY_LABEL_DATA = new Formater("personality:label:data:{0}");

	/**
	 * 缓存标签数据,key的完整格式为：like:label:data:%s，%s为标签id StringKey
	 */
	public static final Formater LIKE_LABEL_DATA = new Formater("like:label:data:{0}");

	/**
	 * 缓存用户标签数据,key的完整格式为：account:like:label:data:%s:%s，%s为用户uuid ，type类型  setKey 有过期时间
	 */
	public static final Formater ACCOUNT_LIKE_LABEL = new Formater("account:like:label:{0}:{1}")  ;

	/**
	 * 缓存标签使用次数,key的完整格式为：label:use:count:%s，%s为标签id
	 */
	public static final Formater LABEL_USE_COUNT = new Formater("label:use:count:{0}");

	/**
	 * 用户更新头像redis校验
	 */
	public static final Formater UPDATE_AVATAR_TIMES = new Formater("account:avatar:update:{0}");

	public static final Formater PRIVILEGE_INFO = new Formater("privi:{0}:{1,Number,#}");

	/**
	 * 用于在redis中保存身份标识信息<br/>
	 */
	@Deprecated
	public static final Formater IDENTITY_INFO_ALL = new Formater("identity:info:all");

	/**
	 * 用于在redis中保存积分等级及等级对应积分<br/>
	 */
	@Deprecated
	public static final Formater IDENTITY_INFO_SEX = new Formater("identity:info:sex:{0}");

	/**
	 * 用户成就勋章缓存key {0}为用户uuid
	 */
	public static final Formater ACCOUNT_ACHIEVEMENT = new Formater("account:achievement:{0}");

	/**
	 * 缓存accounts_exper_options表中的配置, {0}为appcode
	 */
	@Deprecated
	public static final Formater EXPERIENCE_INFO = new Formater("experience:info:{0,Number,#}");
	/**
	 * 用户账号sn序列号缓存，{0}为用户uuid
	 */
	public static final Formater ACCOUNT_SN_INFO = new Formater("account:sn:info:{0}");
	/**
	 * 地区信息缓存, {0}为地区id
	 */
	public static final Formater REGION = new Formater("region:{0,Number,#}");
	/**
	 * 微信access_token缓存
	 */
	public static final Formater WECHAT_ACCESS_TOKEN = new Formater("wechat:access:token:{0}");

	/**
	 * 微信openid缓存 {0}为用户uuid
	 */
	@Deprecated
	public static final Formater WECHAT_ACCESS_OPEN_ID = new Formater("wechat:openid:token:{0}");

	/**
	 * 微信openid缓存 {0}openId
	 */
	public static final Formater WECHAT_USER_INFO = new Formater("wechat:user:info:{0}");

	/**
	 * 用户认证信息 IOS sdk审核，{0}为用户uuid
	 */
	public static final Formater ACCOUNT_CERTIFICATION_SDK_CHECK_TEMP = new Formater("account:certification:sdk:check:temp:{0}");

	/**
	 * 用户认证信息缓存，{0}为用户uuid
	 * 2024.02.06 不要使用
	 */
	@Deprecated
	public static final Formater ACCOUNT_CERTIFICATION = new Formater("account:certification:{0}");

	/**
	 * 初始化验证比对
	 */
	public static final Formater ACCOUNT_CERTIFICATION_REWARD_ACCOUNT_INIT = new Formater("account:certification:reward:account:init:{0}");
	/**
	 * 取消实名次数
	 * 代码无使用
	 */
	@Deprecated
	public static final Formater ACCOUNT_CANCEL_CERT_TIMES = new Formater("account:cancel:cert:times:{0}");
	/**
	 * 权限显示配置
	 */
	@Deprecated
	public static final Formater PRIV_SHOW_CONFIG = new Formater("priv:show:config");
	/**
	 * 缓存所有的驾照公里数信息
	 */
	@Deprecated
	public static final Formater DRIVER_KM_INFO = new Formater("driver:km:info");
	/**
	 * 驾照公里数变更项缓存 {0}为变更项类型
	 */
	@Deprecated
	public static final Formater DRIVER_KM_RULES_SINGLE = new Formater("driver:km:rules:single:{0,Number,#}");
	/**
	 * 驾照公里数变更项缓存(所有变更项的json)
	 */
	@Deprecated
	public static final Formater DRIVER_KM_RULES_ALL = new Formater("driver:km:rules:all");
	/**
	 * 周公里数排序行 {0}为每周最后一天日期
	 */
	@Deprecated
	public static final Formater WEEK_KM_RANK = new Formater("week:km:rank:{0}");

	/**********************************************
	 *             uuid redis相关的key              *
	 **********************************************/

	/**
	 * 缓存所有已生成的用户uuid
	 */
	public static final Formater UUID_UNIQUE_USER = new Formater("uuid.unique.user.{0}");
	public static final Formater SN_UNIQUE_USER = new Formater("sn.unique.user.{0}");

	@Deprecated
	public static final Formater ALIYUN_PREVENTION_ALL = new Formater("aliyun:prevention:all");

	/**
	 * 缓存accountKey信息。key的完整格式为：account:key:%s，%s为accountKey,hash结构，存储account_id和uuid<br/>
	 */
	public static final Formater ACCOUNT_KEY = new Formater("account:key:{0}");
	/**
	 * 缓存accountKey信息。key的完整格式为：account:label:%s，%s为accountKey,hash结构，存储用户标签<br/>
	 */
	public static final Formater ACCOUNT_LABEL = new Formater("account:label:{0}");
	/**
	 * 用户评价,{0}评价人uuid,{1}:被评价人uuid
	 */
	public static final Formater ACCOUNT_EVALUATE = new Formater("account:evaluate:{0}:{1}");
	/**
	 * 用户获得的评价排序缓存，{0}用户uuid
	 */
	public static final Formater ACCOUNT_EVALUATE_SORT = new Formater("account:evaluate:sort:{0}");
	/**
	 * 用户获得评价类型缓存
	 */
	public static final Formater ACCOUNT_GET_EVALUATE_TYPE = new Formater("account:get:evaluate:type:{0}");
	/**
	 * 注销用户队列
	 */
	@Deprecated
	public static final Formater ACCOUNT_DESTORY = new Formater("account:destroy");

	/**
	 * 小编、版主缓存key
	 */
	@Deprecated
	public static final Formater GROUP_ACCOUNT_LIST = new Formater("group:account:list");

	/**
	 * 用于多次点击点击过滤key, {0}为类型表示, {1}为用户区别标识，可用uuid，ticket，token这类唯一标识标记
	 */
	public static final Formater CLICK_FILTER_TYPE_KEY = new Formater("click:filter:{0}:{1}");

	public static final Formater MOBILE_PLACE_LIST = new Formater("mobile:place:list");
	/**
	 * 白名单uuid集合; {0}为白名单类型
	 */
	@Deprecated
	public static final Formater WHITE_UUID_SET = new Formater("white:uuid:set:{0}");
	/**
	 * 设备请求闪验次数
	 */
	public static final Formater TOKEN_FLASH_REQ_COUNT = new Formater("token:flash:req:count:{0}");
	/**
	 * 设备请求个验次数
	 */
	@Deprecated
	public static final Formater TOKEN_GY_REQ_COUNT = new Formater("token:gy:req:count:{0}");
	/**
	 * 锁key  {0}为业务标识, {1}为唯一标识，可用uuid，ticket，token这类唯一标识标记
	 */
	public static final Formater REDIS_LOCK_KEY = new Formater("lock:key:{0}:{1}") ;

	/**
	 * 第三方手机号被绑定后,继续登录key
	 */
	public static final Formater THIRD_LOGIN_CONTINUE_LOGIN = new Formater("third:login:continue:login:{0}");

	/**
	 * 闪验登录后利用accessToken作为key来缓存mobile
	 */
	public static final Formater THIRD_LOGIN_QUICK_ACCESS_TOKEN_MOBILE = new Formater("third:login:quick:access:token:mobile:{0}");

	/**
	 * hash 记录用户每周昵称修改次数  {0}用户uuid   key-每周一0点时间戳，value-修改昵称次数
	 */
	public static final Formater ACCOUNT_CHANGE_NICKNAME_NUM = new Formater("account:change:nickname:num:{0}") ;
	/**
	 * hash 记录用户每天昵称修改次数
	 */
	public static final Formater ACCOUNT_CHANGE_NICKNAME_TODAY_NUM = new Formater("account:change:nickname:today:num:{0}") ;

	/**
     * String 记录用户上一次真人认证状态  {0}用户uuid  值 1/0  有设置过期时间
     */
    public static final Formater ACCOUNT_HAS_VALID_AVATAR = new Formater("account:hasValidAvatar:{0}") ;

    /**
     * String json 腾讯云凭证  {0} appid
     */
    public static final Formater TENGXUNYUN_VOUCHER = new Formater("tengxunyun:voucher:{0}") ;

    /**
     * hash 用户设备信息 {0} uuid {1} token
     */
    public static final Formater ACCOUNT_TOKEN_INFO = new Formater("account:token:info:{0}:{1}")  ;

    /**
     * hash 用户设备信息 {0} uuid
     */
    public static final Formater ACCOUNT_TOKEN_RELATION = new Formater("account:token:relation:{0}")  ;

    /**
     * string 设备ios {0} token
     */
    public static final Formater TOKEN_IOS_SYS_VERSION = new Formater("token:iosSysVersion:{0}")  ;

	/**
	 * 用户注册mobile唯一锁
	 */
	public static final Formater ACCOUNT_REGISTER_MOBILE_LOCK = new Formater("account:register:mobile:lock:{0}");


	/**
	 * 用户第三方注册openid唯一锁
	 */
	public static final Formater ACCOUNT_THIRD_REGISTER_OPENID_LOCK = new Formater("account:third_register:openid:lock:{0}");

	/**
	 * 修改手机号随机码，{0}为uuid
	 */
	public static final Formater ACCOUNT_MODIFY_MOBILE_RANDOM = new Formater("account:modify:mobile:random:{0}");

	/**
	 * 历史头像存储
	 */
	public static final Formater ACCOUNT_BEFORE_AVATAR_PHOTO = new Formater("account:before:avatar:photo:{0}");

	/**
	 * 审核失败头像
	 */
	public static final Formater ACCOUNT_AVATAR_PHOTO_CHECK_FAIL = new Formater("account:avatar:photo:check:fail:{0}");

	/**
	 * 审核失败头像查询锁
	 */
	public static final Formater ACCOUNT_AVATAR_PHOTO_CHECK_FAIL_LOCK = new Formater("account:avatar:photo:check:fail:lock:{0}");

	/**
	 * 用户认证信息
	 */
	@Deprecated
	public static final Formater ACCOUNT_CERTIFICATION_INFO = new Formater("account:certification:info:{0}");

	/**
	 * 用户业务认证信息
	 */
	@Deprecated
	public static final Formater ACCOUNT_CHAT_CERTIFICATION_INFO = new Formater("account:chat:certification:info:{0}");

	/**
	 * 用户认证信息锁
	 */
	@Deprecated
	public static final Formater ACCOUNT_CERTIFICATION_INFO_LOCK = new Formater("account:certification:lock:{0}");

	/**
	 * 手机找回次数【TOKEN限制】
	 * {0}: token
	 * {1}: timeKey
	 */
	public static final Formater RETRIEVE_TOKEN_LIMIT = new Formater("account:tk:lm:{0}:{1}");

	/**
	 * 手机找回次数【TOKEN+uuid限制】
	 * {0}: 用户uuid
	 * {1}: timeKey
	 * {2}: token
	 */
	public static final Formater RETRIEVE_ACCOUNT_TOKEN_LIMIT = new Formater("account:ret:tk:uuid:lm:{0}:{1}:{2}");

	/**
	 * 手机找回次数【身份证限制】
	 * {0}: 用户uuid
	 * {1}: timeKey
	 * {2}: idNo
	 */
	public static final Formater RETRIEVE_ACCOUNT_IDENTIFY_LIMIT = new Formater("account:ret:id:lm:{0}:{1}:{2}");

	/**
	 * 手机找回次数【手机号限制】
	 * {0}: timeKey
	 * {1}: 手机号
	 */
	public static final Formater RETRIEVE_ACCOUNT_MOBILE_LIMIT = new Formater("account:ret:mb:lm:{0}:{1}");

	/**
	 * 手机找回次数【ip限制】
	 * {0}: timeKey
	 * {1}: ip
	 */
	public static final Formater RETRIEVE_ACCOUNT_IP_LIMIT = new Formater("ret:ip:lm:{0}:{1}");

	/**
	 * 支付宝账号失效提醒频控，有过期
	 * {0}: 用户uuid
	 */
	public static final Formater CONTROL_REWARD_ACCOUNT_INVALID_NOTICE = new Formater("control:rewardAccountInvalidNotice:{0}");

	/**
	 * 成功完成提现扫脸状态，有过期
	 * {0}: 用户uuid
	 */
	public static final Formater CONTROL_DETECT_FACE_FOR_WITHDRAW = new Formater("control:detectFaceForWithdraw:{0}");

	/**
	 * 用户标签v2
	 */
	public static final Formater ACCOUNT_LABEL_V2 = new Formater("account:label:v2:{0}");

	/**
	 * 用户吸引力
	 */
	public static final Formater ACCOUNT_AllURE = new Formater("account:allure:{0}:{1}");

	public static final Formater ACCOUNT_AllURE_V2 = new Formater("account:allure:v2:{0}");

	public static final Formater ACCOUNT_CARD_PROGRESS = new Formater("account:card:progress:{0}");

	/**
	 * 用户学校专业
	 */
	public static final Formater ACCOUNT_MAJOR = new Formater("account:school:major:{0}");

	/**
	 * 用户学校专业
	 */
	public static final Formater ACCOUNT_IDEAL = new Formater("account:ideal:target:{0}");

	/**
	 * 用户背景图
	 */
	public static final Formater ACCOUNT_BACKGROUND = new Formater("account:bg:{0}");

	/**
	 * 吸引力锁
	 */
	public static final Formater ALLURE_LOCK = new Formater("allure:lock:{0}");

	/**
	 * 用户自己的城市实验
	 * {0}:用户uuid
	 * {1}:实验code expCode
	 */
	public static final Formater AB_TEST_CITY_CACHE = new Formater("ab:t:c:{0}:{1}");

	public static final Formater PHOTO_VECTOR_BIZ_ID_MAPPING = new Formater("pho:vec:bid:map:{0}");

	/**
	 * 获取别人的城市实验
	 * {0}:用户uuid
	 * {1}:实验code expCode
	 */
	public static final Formater OTHERS_AB_TEST_CITY_CACHE = new Formater("ot:ab:t:c:{0}:{1}:{2}:{3}:{4}:{5}:{6}");

	/**
	 * 设置注销原因
	 * {0}:用户uuid
	 */
	public static final Formater DESTROY_REASON = new Formater("destroy:reason:{0}");

	/**
	 * 关怀模式
	 * <p>
	 * {0}:用户uuid
	 * return： app版本号
	 */
	public static final Formater CARE_MODEL = new Formater("care:model:{0}");


	/**
	 * 头像指导错误队列
	 */
	public static final String AVATAR_GUIDE_ERROR_QUEUE = "avatar:guide:err:queue";

	/**
	 * 头像指导弹窗
	 */
	public static final Formater AVATAR_GUIDE_POPUP = new Formater("avatar:guide:popup:{0}");

	/**
	 * 头像一键美颜过
	 * {0}：用户uuid
	 */
	public static final Formater AVATAR_HAS_BEAUTY = new Formater("avatar:has:beauty:{0}");


	public static class Formater {
		private String pattern;
		private MessageFormat format;

		private Formater(String pattern) {
			this.pattern = pattern;
			this.format = new MessageFormat(this.pattern);
		}

		public String setArg(Object...args) {
			return this.format.format(args);
		}

		public String getPattern() {
			return this.pattern;
		}

		@Override
		public String toString() {
			return this.format.format(null);
		}
	}
}
