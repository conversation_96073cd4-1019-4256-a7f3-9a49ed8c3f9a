package cn.taqu.account.constant;

import cn.taqu.account.common.RiskDetectEnum;

import java.util.Arrays;
import java.util.List;

/**
 * 风控检测类型（https://o15vj1m4ie.feishu.cn/wiki/Mfigwy61YiMl0VkmsXYctDpcnQG）
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
public interface RiskSafeIdConstant {

    // 自我介绍
    String INTRODUCTION = "1_1_1_1_6";

    @Deprecated
    // 真人认证头像（机审+人审，废弃）
    String REAL_PERSON_AVATAR_OLD = "1_1_1_1_7";

    @Deprecated
    // 非真人认证头像（机审+人审，废弃）
    String NOT_REAL_PERSON_AVATAR_OLD = "1_1_1_1_8";

    // 照片机审
    String PHOTO = "1_1_1_1_9";

    // 真人认证相册
    String REAL_PERSON_COVER = "1_1_1_1_14";
    // 重复图片治理
    String REAL_PERSON_HIS_PHOTO = "1_1_1_1_14_1";

    // 非真人认证相册
    String NOT_REAL_PERSON_COVER = "1_1_1_1_15";

    // 头像机审
    String IMAGE_AVATAR = "1_1_1_1_16";

    // 真人认证头像（人审）
    String REAL_PERSON_AVATAR = "1_1_1_1_17";

    // 非真人认证头像（人审）
    String NOT_REAL_PERSON_AVATAR = "1_1_1_1_18";

    // 语音签名
    String AUDIO_SIGN = "1_1_1_1_10";

    // 昵称
    String TEXT_NICKNAME = "1_1_1_1_11";

    // 个人简介
    String TEXT_PERSONAL_PROFILE = "1_1_1_1_12";

    @Deprecated
    // 个人封面
    String IMAGE_COVER = "1_1_1_1_13";

    // 专业
    String TEXT_MAJOR = "1_1_1_1_20";

    // 理想型
    String TEXT_IDEAL = "1_1_1_1_21";
    
    // 我的生活
    String ACCOUNTS_LIFE = "1_1_1_1_22";
    // 我的生活真人
    String ACCOUNTS_LIFE_REAL_PERSON = "1_1_1_1_23";

    // 重复图片治理（统一场景）
    String SIMILAR_PHOTO_DETECT = "1_1_1_1_7_1";

    
    static String getSaveId(String type) {
        if (RiskDetectEnum.TEXT_INTRODUCTION.name().equals(type)) {
            // 自我介绍
            return RiskSafeIdConstant.INTRODUCTION;
        } else if (RiskDetectEnum.TEXT_PERSONAL_PROFILE.name().equals(type)) {
            // 个人简介
            return RiskSafeIdConstant.TEXT_PERSONAL_PROFILE;
        } else if (RiskDetectEnum.TEXT_NICKNAME.name().equals(type)) {
            // 昵称
            return RiskSafeIdConstant.TEXT_NICKNAME;
        } else if (RiskDetectEnum.AUDIO_SIGN.name().equals(type)) {
            // 语音签名
            return RiskSafeIdConstant.AUDIO_SIGN;
        } else if (RiskDetectEnum.IMAGE_COVER.name().equals(type)) {
            // 个人封面
            return RiskSafeIdConstant.IMAGE_COVER;
        } else if (RiskDetectEnum.PHOTO.name().equals(type)) {
            // 照片机审
            return RiskSafeIdConstant.PHOTO;
        } else if (RiskDetectEnum.IMAGE_AVATAR.name().equals(type)) {
            // 头像机审
            return RiskSafeIdConstant.IMAGE_AVATAR;
        } else if (RiskDetectEnum.REAL_PERSON_AVATAR.name().equals(type)) {
            // 真人认证头像（机审+人审）
            return RiskSafeIdConstant.REAL_PERSON_AVATAR;
        } else if (RiskDetectEnum.NOT_REAL_PERSON_AVATAR.name().equals(type)) {
            // 非真人认证头像（机审+人审）
            return RiskSafeIdConstant.NOT_REAL_PERSON_AVATAR;
        } else if (RiskDetectEnum.REAL_PERSON_COVER.name().equals(type)) {
            // 真人认证相册
            return RiskSafeIdConstant.REAL_PERSON_COVER;
        } else if (RiskDetectEnum.NOT_REAL_PERSON_COVER.name().equals(type)) {
            // 非真人认证相册
            return RiskSafeIdConstant.NOT_REAL_PERSON_COVER;
        } else if (RiskDetectEnum.TEXT_MAJOR.name().equals(type)) {
            return RiskSafeIdConstant.TEXT_MAJOR;
        } else if (RiskDetectEnum.TEXT_IDEAL.name().equals(type)) {
            return RiskSafeIdConstant.TEXT_IDEAL;
        } else if (RiskDetectEnum.ACCOUNTS_LIFE.name().equals(type)) {
            return RiskSafeIdConstant.ACCOUNTS_LIFE;
        } else if (RiskDetectEnum.ACCOUNTS_LIFE_REAL_PERSON.name().equals(type)) {
            return RiskSafeIdConstant.ACCOUNTS_LIFE_REAL_PERSON;
        }
        return "";
    }

    static List<String> getRealPersonId() {
        return Arrays.asList(RiskSafeIdConstant.REAL_PERSON_AVATAR, RiskSafeIdConstant.NOT_REAL_PERSON_AVATAR, RiskSafeIdConstant.REAL_PERSON_COVER);
    }
}
