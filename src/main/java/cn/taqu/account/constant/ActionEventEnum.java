package cn.taqu.account.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 行为事件枚举
 *
 * <AUTHOR>
 * @date 2024/10/15 6:19 下午
 */
@Getter
@AllArgsConstructor
public enum ActionEventEnum {

    /**
     * 标签类型
     */
    AVATAR_UPLOAD("avatar_upload", "上传头像（过机审，忽略默认头像）"),
    AVATAR_UPLOAD_AUDIT_PASS("avatar_upload_audit_pass", "上传头像（过人审）"),
    FRIENDSHIP_LABEL_SETTING("label_setting", "交友偏好标签设置"),
    VOICE_PASS("voice_pass", "语音审核通过"),

    ;

    /**
     * 标识
     */
    private final String code;

    /**
     * 描述说明
     */
    private final String desc;
}
