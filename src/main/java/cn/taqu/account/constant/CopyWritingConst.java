package cn.taqu.account.constant;

/**
 * 提示文案
 * <AUTHOR> Wu.D.J
 */
public interface CopyWritingConst {

    /**
     * application real name certification use chatroom certification information
     */
    String REAL_NAME_CERTIFICATION_USE_CHATROOM_INFO = "系统检测到您之前已实名认证，将帮您使用之前的认证信息进行自动认证。";
    
    /**
     * 真人头像与实名不一致，文案提示
     */
    String REAL_PERSON_AVATAR_DIFFERENT_INFO = "系统检测到您当前进行认证的人像非上次实名认证人像，为保证您的账号安全，请以上次实名认证的人像进行真人认证。";
}
