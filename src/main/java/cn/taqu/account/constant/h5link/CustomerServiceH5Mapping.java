package cn.taqu.account.constant.h5link;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.utils.EnvUtil;
import cn.taqu.core.protocol.SoaBaseParams;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.stream.Stream;

/**
 * 注销H5
 * <AUTHOR>
 * @date 2025/4/11 14:33
 */
@Slf4j
@RequiredArgsConstructor
public enum CustomerServiceH5Mapping {
    /**/
    MAIN("https://a8-im.7x24cc.com/phone_webChat.html?accountId=N000000053020&chatId=65b326d4-85be-4b3a-94de-741c2fdf5a55"),
    GIRL("https://a8-im.7x24cc.com/phone_webChat.html?accountId=N000000053020&chatId=b278695b-e37b-4444-9915-6060778fe194"),
    YOUNG("https://a8-im.7x24cc.com/phone_webChat.html?accountId=N000000053020&chatId=e7e9dfa8-c3dc-4c8d-a106-c4addf4c8910");

    private final String link;

    public static String resolveLink(String visitorId, String nickname) {
        return resolve().getLink(visitorId, nickname);
    }

    private static CustomerServiceH5Mapping resolve() {
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        if (ClonedGroupEnum.MAIN_GROUP.contains(baseParams.getCloned())) {
            return MAIN;
        } else if (ClonedGroupEnum.GIRL_GROUP.contains(baseParams.getCloned())) {
            return GIRL;
        } else {
            return YOUNG;
        }
    }

    private String getLink(String visitorId, String nickname) {
        try {
            String url = URLEncoder.encode(link + "&visitorId=" + visitorId + "&nickName=" + nickname, "UTF-8");
            return "m=web&a=url&ul=" + url;
        } catch (UnsupportedEncodingException e) {
            log.warn("encode err", e);
            return "";
        }
    }
}

