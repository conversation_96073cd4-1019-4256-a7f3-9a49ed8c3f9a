package cn.taqu.account.constant.h5link;

import cn.taqu.account.utils.EnvUtil;
import lombok.RequiredArgsConstructor;

import java.util.stream.Stream;

/**
 * 注销H5
 * <AUTHOR>
 * @date 2025/4/11 14:33
 */
@RequiredArgsConstructor
public enum DestroyH5Mapping {
    /**/
    test("https://test-k8s.taqu.cn/vueiii/trunk/branch/community/promotion/account-destory/pre/index.html#/index/"),
    gray("https://web.whtaqu.cn/vueiii/trunk/branch/community/promotion/account-destory/pre/index.html#/index"),
    online("https://web.whtaqu.cn/vueiii/trunk/branch/community/promotion/account-destory/index.html#/index");

    private final String link;

    public static String resolveLink() {
        return resolve().getLink();
    }

    private static DestroyH5Mapping resolve() {
        String env = EnvUtil.getEnv();
        return Stream.of(DestroyH5Mapping.values())
                .filter(e -> env.contains(e.name()))
                .findAny().orElse(test);
    }

    private String getLink() {
        return "m=web&a=url&hide_nav=1&ul=" + link;
    }
}

