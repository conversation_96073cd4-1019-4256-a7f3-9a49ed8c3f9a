package cn.taqu.account.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 头像指导弹窗场景
 *
 * <AUTHOR>
 * @date 2025/6/27 10:28 上午
 */
@Getter
@AllArgsConstructor
public enum AvatarGuidePopupEnum {

    /**
     * 更新头像
     */
    UPDATE_AVATAR(1, "更新头像"),

    /**
     * 美颜
     */
    BEAUTY(2, "一键美颜");

    /**
     * 枚举
     */
    private final Integer type;

    /**
     * 描述说明
     */
    private final String desc;

    /**
     * 类型匹配
     *
     * @param type
     * @return
     */
    public boolean match(Integer type) {
        return Objects.equals(this.type, type);
    }

}
