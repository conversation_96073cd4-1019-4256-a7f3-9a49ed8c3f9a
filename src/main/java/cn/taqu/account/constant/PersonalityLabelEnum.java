package cn.taqu.account.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 用户标签配置
 *
 * <AUTHOR>
 * @date 2024/10/14 10:28 上午
 */
@Getter
@AllArgsConstructor
public enum PersonalityLabelEnum {

    /**
     * 标签类型
     */
    FAVORITE_TYPE(1, "喜欢类型"),

    SELF_DESCRIPTION(2, "自我描述"),

    HOBBY(3, "兴趣爱好"),

    FRIEND_REVIEW(4, "好友评价"),

    FRIENDSHIP_PREFERENCES(5, "交友偏好");

    /**
     * 枚举
     */
    private final Integer type;

    /**
     * 描述说明
     */
    private final String desc;

    /**
     * 类型匹配
     *
     * @param type
     * @return
     */
    public boolean match(Integer type) {
        return Objects.equals(this.type, type);
    }

}
