/**
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 * @Project Name:tq-account
 * @ClassName:CommConst.java
 * @Package:cn.taqu.account.web.api.constant
 * @Description:
 * @author:laik<PERSON><PERSON>
 * @date:2015年9月25日
 */
package cn.taqu.account.constant;

/**
 * @ClassName:CommConst.java
 * @Description:API通用常量
 * @author:laik<PERSON>zhen
 * @date:2015年9月25日
 */
public interface CommConst {

	String TQ_SEC_TIPS = "他趣小秘书";

	String SL_SEC_TIPS = "闪恋小秘书";

	/**
	 * 女包弹窗标题 cloned:"63", "64", "65", "67"
	 */
	String NVBAO_SEC_TIPS = "平台小秘书";
	
	String STRING_NULL = "null";

	/*-------------微信JSON常量---------------*/
	/**
	 * 微信错误码
	 */
	String WEIXIN_ERRCODE = "errcode";

	/**
	 * 微信错误信息描述
	 */
	String WEIXIN_ERRMSG = "errmsg";

	/**
	 * 微信接口调用凭证
	 */
	String WEIXIN_ACCESS_TOKEN = "access_token";

	/**
	 * 微信access_token接口调用凭证超时时间，单位（秒）
	 */
	String WEIXIN_EXPIRES_IN = "expires_in";

	/**
	 * 微信用户刷新access_token
	 */
	String WEIXIN_REFRESH_TOKEN = "refresh_token";

	/**
	 * 微信授权用户唯一标识
	 */
	String WEIXIN_OPENID = "openid";

	/**
	 * 微信用户授权的作用域，使用逗号（,）分隔
	 */
	String WEIXIN_SCOPE = "scope";

	/**
	 * 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
	 */
	String WEIXIN_UNIONID = "unionid";

	/**
	 * 解绑
	 */
	String THIRDPARTY_DEBIND_SUCCESS = "debind_success";

	/**
	 * 绑定
	 */
	String THIRDPARTY_REBIND_SUCCESS = "rebind_success";

	/**
	 * yes(是)
	 */
	Integer YES_1 = 1;

	/**
	 * no(否)
	 */
	Integer NO_0 = 0;
	
	/**
	 * 默认自我介绍图片宽度
	 */
	String DEFAULT_INTRODUCTION_IMG_WIDTH = "1050";

	/**
	 * 默认自我介绍图片高度
	 */
	String DEFAULT_INTRODUCTION_IMG_HEIGHT = "1050";

	/**
	 * HTTP常量
	 */
	String HTTP = "http";

	/**
	 * 头像域名
	 */
	String AVATAR_HOST = "https://avatar01.jiaoliuqu.com/";

	/**
	 * 头像源站域名
	 */
	String AVATAR_SRC_HOST = "https://avatar01-src.jiaoliuqu.com/";
	
	/**
	 * 私有bucket源站域名
	 */
	String ACCOUNT_PRIVATE_SRC_HOST = "https://account-private-src.jiaoliuqu.com/";
	
	/**
	 * 音频源站域名
	 */
	String AUDIO_SRC_HOST = "https://vox01-src.jiaoliuqu.com/";
	
	/**
	 * j3图片bucket
	 */
	String AVATAR_BUCKET_TYPE = "avatar";
	
	/**
	 * 头像域名
	 */
	String AVATAR_BUCKET = "avatar01";
	
	/**
	 * 活体、实名 bucketType
	 */
	public static String PRIVATE_AVATAR_BUCKET_TYPE = "account-private";
	
	/**
	 * 活体、实名源站域名
	 */
	public static String PRIVATE_AVATAR_SRC_BUCKET = "account-private-src";
	
	/**
	 * j3图片bucket
	 */
	String AUDIO_BUCKET_TYPE = "audio";
	
	/**
	 * j3图片bucket
	 */
	String AUDIO_BUCKET = "vox01";
	
	/**
	 * 文件夹名称（活体）liveFace
	 * 2024年7月启用
	 */
	String FOLDER_LIVE_FACE = "liveFace";
	
	/**
	 * 文件夹名称（实名）certification
	 * 2024年7月启用
	 */
	String FOLDER_CERTIFICATION = "certification";

	/**
	 * 默认头像
	 */
	String DEFAULT_HOME_COVER = "default_home_cover.png";

	/**
	 * 可用
	 */
	public static Integer QINIU_FILE_STATUS_ENABLE = 0;
	/**
	 * 禁用
	 */
	public static Integer QINIU_FILE_STATUS_DISENABLE = 1;
	/**
	 * 他趣 appcode
	 */
	Integer APPCODE_TAQU = 1;
	/**
	 * 他趣 cloned
	 */
	Integer CLONED_TAQU = 1;
	/**
	 * 批量执行1000条
	 */
	Integer PER_BATCH_EXEC_1000 = 1000;

	/**
	 * 3个占位符 %s
	 * 依次传入 appId、verify_id、state
	 * state base64 唯一值(考虑uuid + 毫秒级时间戳)
	 */
	String ALIPAY_ID_VERIFY_URL_TEMPLATE= "https://authweb.alipay.com/auth?auth_type=PURE_OAUTH_SDK&app_id=%s&scope=id_verify&cert_verify_id=%s&state=%s";

	/**
	 * 调用风控检测中台，旧包无数美id的传参
	 */
	String OLD_CLIENT_PACKAGE = "oldClientPackage";

	// =================== 队列分界线
    /**
     * 队列名称 - 用户头像/相册修改
     */
    public static final String MQ_ACCOUNT_AVATAR_PHOTO_UPDATE = "account_avatar_photo_update";

    /**
     * 队列名称 - 用户系统消费推入的业务数据
     */
    public static final String MQ_ACCOUNT_BIZ_DATA = "account_biz_data";

    /**
     * 队列名称 - 手机号修改 （多消费）
     */
    @Deprecated
    public static final String MQ_MULTI_ACCOUNT_UPDATE_MOBILE = "multi_account_update_mobile";

    /**
     * 队列名称 - 超过90天未活跃 （多消费）
     */
    public static String NOT_ACTIVE_FOR_90_DAYS = "multi_account_active_time";


	/**
	 * 队列名称 - 异步消息队列
	 */
	public static String PUSH_ASYNC_INVOKE_QUEUE = "push_async_invoke_queue";


	/**
	 * 队列名称 - 设备激活
	 */
	public static String MQ_EQUIPMENT_ACTIVATION = "mq_equipment_activation";
	/**
	 * 昵称违规
	 */
	public static String MQ_ACCOUNT_NAME_VIOLATION = "account_name_violation";

	/**
	 * 他趣匹配临时时间线交互
	 */
	public static String MQ_FAKE_MATCH_TRIGGER_UP_PREPARE="fake_match_trigger_up_prepare";

	/**
	 * 头像违规
	 */
	public static String MQ_ILLEGAL_AVATAR = "illegal_avatar";


	/**
	 * 头像变更
	 */
	public static String MQ_CHANGE_AVATAR = "change_avatar";

	/**
	 * 头像变更
	 */
	public static String MQ_POST_REGISTER = "account_post_register";

	/**
	 * 头像脚本
	 */
	public static String MQ_SRCIPT_AVATAR="account_script_avatar";

	/**
	 * 相册脚本
	 */
	public static String MQ_SRCIPT_COVER="account_script_cover";

	/**
	 * 统计相关
	 */
	@Deprecated
	public static String MQ_POST_SEARCH_STATISTICS = "post_search_statistics";

	/**
	 * 真人认证通过数据下发
	 */
	public static String MQ_ANTISPAM_TAG_REAL_PERSON = "mq_antispam_tag_real_person";

	/**
	 * 实名认证通过数据下发（需加密存储）
	 */
	public static String MQ_ANTISPAM_TAG_REAL_NAME = "mq_antispam_tag_real_name";

	/**
	 * 真人认证成功
	 */
	public static String MQ_ACCOUNT_REAL_PERSON_CERTIFICATION = "account_real_person_certification";
	/**
	 * 预注册 handleAdmpReadyReg
	 */
	public static String MQ_HANDLE_ADMP_READY_REG = "handleAdmpReadyReg";

	/**
	 * 推送审核平台 - 自我介绍
	 */
	@Deprecated
	public static String MQ_USER_INTRODUCTION = "mq_user_introduction";

	/**
	 * 推送审核平台 - 图片审核
	 */
	@Deprecated
	public static String MQ_AVATAR_PICTURE_REVIEW = "mq_avatar_picture_review";

	/**
	 * 推送审核平台 - 语音签名审核
	 */
	@Deprecated
	public static String MQ_VOICE_SIGN_REVIEW = "mq_voice_sign_review";

	/**
	 * 推送审核平台 - 昵称审核
	 */
	@Deprecated
	public static String MQ_NICK_NAME_REVIEW = "mq_nick_name_review";

	/**
	 * 推送审核平台 - 个人简介审核
	 */
	@Deprecated
	public static String MQ_PERSONAL_PROFILE_INFO_REVIEW = "mq_personal_profile_info_review";

	/**
	 * 图片检测记录
	 */
	public static String MQ_ACCOUNT_PHOTO_DETECT_RECORD = "account_photo_detect_record";

	 /**
     * 高风险人像延时队列
     */
	@Deprecated
	public static String HIGH_RISK_FACE_MQ = "mq_tqAntiSpam_highRiskFace";
	@Deprecated
	public static String HIGH_RISK_FACE_MQ_2 = "mq_risk_highRiskFace";

	/**
	 * 认证日志
	 */
	String MQ_CERT_LOG = "account_cert_log";

	/**
	 * 高风险用户活体队列
	 */
	String MQ_ACCOUNT_LIVING_CERT_RESULT = "mq_account_living_cert_result";

	/**
	 * 推送审核平台 - 审核任务
	 */
	@Deprecated
	String MQ_TO_REVIEW_TASK = "mq_to_review_task";

	/**
	 * 解绑银行卡 - 用户注销或解除实名认证
	 */
	String MQ_HANDLE_UNBIND_BANK_CARD = "handleUnbindBankCard";

	/**
	 * 用户注销操作
	 */
	String MQ_ACCOUNT_HANDLER_AFTER_DESTROY = "account_handler_after_destroy";
	
	/**
	 * 业务有效文件入库使用队列。推入-各业务方，消费-j3
	 */
	String MQ_PIC_SYS_USED_FILE_BIZ = "pic_sys_used_file_biz";
	/**
	 * 业务禁用/启用文件队列。推入-各业务方，消费-j3
	 */
	String MQ_PIC_SYS_UPD_FILE_STATUS = "pic_sys_upd_file_status";

	@Deprecated // 业务就没开启过，不需要
	String MQ_ACCOUNT_NEW_REGISTER_AVATAR = "account_new_register_avatar";

	/**
	 * 第三方厂商服务调用记录
	 */
	String KAFKA_ACCOUNT_MANUFACTURER_CALL = "account_manufacturer_call";

	@Deprecated
	String KAFKA_TQ_USER_APP_START_EVENT = "tq_user_app_start_event";

	/**
	 * 活动平台事件上报kafka topic
	 */
	String MP_TAQU_EVENT_NOTIFY="mp_taqu_event_notify";

	/**
	 * 头像机审通过事件
	 */
	String REAL_PERSON_MACHINE_VERIFY="real_person_machine_verify";
	
	/**
	 * php系统通知
	 */
	String MQ_SYSTEM_NOTICE = "system_notice";
	
	/**
	 * 钉钉通知
	 */
	String MQ_ASYNC_DINGTALK_MSG = "async_dingtalk_msg";
	
	/**
	 * 中台短信
	 */
	String MQ_MP_SMS_ASYNC_INVOKE_QUEUE = "mp_sms_async_invoke_queue";
	
    /**
     * 其他行为推送的队列
     */
    String MQ_MODIFY_GROW_SCORE = "modify_grow_score";

    /**
     * 注册行为推送的队列
     */
    String MQ_RESUME_COMPUTING_GROW_SCORE = "resume_computing_grow_score";
	
	/**
	 * 社区完成任务
	 */
	String MQ_COMPLETE_FOURM_TASK = "complete_fourm_task";



}
