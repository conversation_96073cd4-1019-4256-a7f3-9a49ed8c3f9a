package cn.taqu.account.constant;

import com.google.common.collect.Sets;

import java.util.Objects;
import java.util.Set;

/**
 * ab测试code
 *
 * <AUTHOR>
 * @time 2021年1月19日 下午4:39:16
 */
public interface AbRuleCode {

	/**
	 * 新人礼包
	 */
    @Deprecated
	String NEWUSER_GIFT = "newuser_gift";

	/**
	 *  https://o15vj1m4ie.feishu.cn/wiki/CEFYwjPeViJoCFkF1Enckgv1nzd
	 *  2024.1月 已经固化实验，实验组推全
	 */
	@Deprecated
	String DISTANCE_PRIVACY_SETUP = "distance_privacy_setup";

	/**
	 * https://o15vj1m4ie.feishu.cn/docx/OdrNdtiFSo8dXgxtahLcgdUyngh
	 * 实验别名
	 * 目标：提升女用户的真人认证率，主要看首日的真人认证率
	 * <p>
	 * 2024.2月 已回滚
	 */
	@Deprecated
	String FEMALE_DEMAND_REAL_PERSON = "female_demand_real_person";

	/**
	 * https://o15vj1m4ie.feishu.cn/wiki/VFsXwMYgeixaV6kAShPcrNTXnkh
	 * 客户端实验。
	 * 判断客户端版本后，新版才调用查询，不要查多了，影响实验用户量
	 *
	 * <AUTHOR>
	 * 2024年11月29日下午2:50:21
	 */
	abstract class ProfileOptimizationUi {
		// 实验key
		public static final String KEY = "Profile_optimization_ui";

		// 对照组
		public static final String GROUP_CONTROL = "profile_center";

		public static final String GROUP_A = "profile_A_treatment";
		public static final String GROUP_B = "profile_B_treatment";

		// 实验组，两个实验组逻辑一样，客户端样式不同而已
		public static final Set<String> GroupExperimentalSet = Sets.newHashSet("profile_A_treatment", "profile_B_treatment");

		/**
		 * 是否属于实验组
		 *
		 * @param value
		 * @return
		 */
		public static boolean isExperimentalGroup(String value) {
			return GroupExperimentalSet.contains(value);
		}

	}

	/**
	 * https://o15vj1m4ie.feishu.cn/wiki/PfN3woreSiJFLWkcNCKcMGP9nrd
	 * ab实验。
	 * 用户实验用户提现率、留存
	 *
	 * <AUTHOR>
	 * 2025年1月2日下午2:50:21
	 */
	abstract class WithdrawalTest {
		// 实验key
		public static final String KEY = "binding_restrictions_of_alipay";

		// 对照组
		public static final String NO_LIMITATION = "no_limitation";
		// 实验组
		public static final String HAVE_LIMITATION = "have_limitation";

		/**
		 * 是否属于实验组
		 *
		 * @param value
		 * @return
		 */
		public static boolean isExpGroup(String value) {
			return Objects.equals(value, HAVE_LIMITATION);
		}

	}

	/**
	 * https://o15vj1m4ie.feishu.cn/wiki/EbgowwIM7ikomCkOLUsccXXtnAc
	 * ab实验。
	 * 用户头像指导
	 *
	 * <AUTHOR>
	 * 2025年1月2日下午2:50:21
	 */
	abstract class AvatarGuideTest {
		// 实验key
		public static final String KEY = "profile_beauty_filter_test";

		// 对照组
		public static final String NO_LIMITATION = "no_beauty_filter";
		// 实验组
		public static final String HAVE_LIMITATION = "have_beauty_filter";

		/**
		 * 是否属于实验组
		 *
		 * @param value
		 * @return
		 */
		public static boolean isExpGroup(String value) {
			return Objects.equals(value, HAVE_LIMITATION);
		}
	}

}
