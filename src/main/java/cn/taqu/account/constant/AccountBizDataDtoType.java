package cn.taqu.account.constant;


/**
 * AccountBizDataDto type
 *
 * <AUTHOR>
 * @date 2021/03/09
 */
public interface AccountBizDataDtoType {

	/**
	 * 注册归因
	 */
	String REG_ATTRIBUTION = "reg_attribution";

	/**
	 * 个人简介自动审核
	 */
	String PERSONAL_PROFILE_AUTO_AUDIT = "personal_profile_auto_audit";

	/**
	 * 昵称自动审核
	 */
	String NICKNAME_AUTO_AUDIT = "nickname_auto_audit";

	/**
	 * 黑名单用户自动踢出
	 */
	String AUTO_LOGOUT = "auto_logout";
	String AUTO_LOGOUT_V2 = "auto_logout_v2";
	/**
	 * 强制退出
	 */
	String FORCE_LOGOUT = "force_logout";
	/**
	 * 重新审核业务
	 */
	String REVIEW_INFO = "review_info";
	/**
	 * 昵称人审回调
	 */
	String NICKNAME_CALLBACK_AUDIT = "nickname_callback_audit";

}
