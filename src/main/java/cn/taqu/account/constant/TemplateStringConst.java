package cn.taqu.account.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-31 10:13
 */
public class TemplateStringConst {
    /**
     * 认证日志相关remark
     */
    public static final String CERT_LOG_REMARK_REAL_NAME = "用户[%s]于[%s]%s实名状态.操作人[%s]";
    public static final String CERT_LOG_REMARK_REAL_PERSON_CANCEL = "用户[%s]于[%s]自行解除了真人认证.";
    public static final String CERT_LOG_REMARK_REAL_PERSON_BIND = "用户[%s]于[%s]完成了真人认证.";
    public static final String CERT_LOG_REMARK_REWARD_ACCOUNT_CANCEL = "用户[%s]于[%s]自行解除了提现账号认证.原账号[%s].账号验证状态[%s]";
    public static final String CERT_LOG_REMARK_REWARD_ACCOUNT_BIND = "用户[%s]于[%s]绑定了提现账号认证.原账号[%s].新账号[%s].账号验证状态[%s]";
    public static final String CERT_LOG_REMARK_REWARD_ACCOUNT_CHECKED = "用户[%s]于[%s]验证了提现账号认证.账号[%s]";

    /**
     * 获取文本内容
     * @param template
     * @param args
     * @return
     */
    public static String getContent(String template, Object... args){
        return String.format(template, args);
    }
}
