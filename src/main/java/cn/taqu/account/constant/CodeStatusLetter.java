package cn.taqu.account.constant;

import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * etcd配置，覆盖代码中配置的错误码提示
 * 
 * <AUTHOR>
 * @date 2020/06/19 16:47
 */
public class CodeStatusLetter {

    public static void paserLetter(String letterConfigJson) {
        if(StringUtils.isBlank(letterConfigJson)) {
            return;
        }

        Map<String, String> letterMap = JsonUtils.stringToObject(letterConfigJson, new TypeReference<Map<String, String>>() {});
        for(CodeStatus codeStatus : CodeStatus.values()) {
            String value = codeStatus.value();
            String letter = MapUtils.getString(letterMap, value);
            if(StringUtils.isNotBlank(letter)) {
                codeStatus.setReasonPhrase(letter);
            }
        }
    }
}
