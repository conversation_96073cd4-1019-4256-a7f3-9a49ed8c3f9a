package cn.taqu.account.constant;

import cn.taqu.core.common.constant.ICodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;

/**
 * 客户端通过value来判断是否返回了错误当value为0时，表示有正常，否则表示有错误，此时需要去读取相应的reasonPhrase来查看具体的错误原因， 错误原因中的错误码（axxxx）是用来当用户反馈错误时，能够及时快速的定位到相应的错误位置，因此需要在定义错误码时确保不重复，
 * 而且需要将错误码进行分类，具体的分类规则如下:<br/>
 * <AUTHOR> 2015年9月22日
 */
public enum CodeStatus implements ICodeStatus {

	/**
	 * 登录凭证过期
	 */
	TICKET_EXPIRE("ticket_expire", "登陆已过期，请重新登陆[j21003]"),
	/**
	 * 登录凭证过期  区分是因为游客账号导致（查缓存account:ticket:java:）
	 */
	TICKET_EXPIRE_GUEST1("ticket_expire", "登陆已过期，请重新登陆[j21004]"),
	/**
	 * 登录凭证过期  区分是因为游客账号导致（查缓存account:infos:uuid:）
	 */
	TICKET_EXPIRE_GUEST2("ticket_expire", "登陆已过期，请重新登陆[j21005]"),

	/**
	 * 接口下线提示1
	 */
	API_OFFLINE_TIPS1("api_offline_tips1", "您当前版本过低，为了您更好的体验本产品，请升级最新版本~"),

	/**
	 * 未查到相关账号信息
	 */
	ACCOUNT_INFO_NO_FOUND("account_info_no_found", "未查到相关账号信息[j21101]"),

	/**
	 * 用户信息未找到
	 */
	PERSON_INFO_NOT_FOUND("person_info_not_found", "用户信息未找到[j21102]"),

	/**
	 * 账号已注销
	 */
	ACCOUNT_DESTROY("account_destroy", "该用户已注销，无法查看"),

	/**
	 * account_key不能为空
	 */
	ACCOUNT_KEY_EMPTY("account_key_empty", "account_key不能为空[j21105]"),
	ACCOUNT_IN_BLACKLIST("account_in_blacklist", "您的账号存在异常，已被限制登录\n有疑问通过微信公众号: newtaqu，进行申诉"),
	/**
	 * 黄瓜直播黑名单文案
	 **/
	HG_ACCOUNT_IN_BLACKLIST("hg_account_in_blacklist", "您的账号登陆异常，如有疑问请咨询在线客服"),
	/**
	 * 设备黑名单
	 **/
	TOKEN_IN_BLACKLIST("token_in_blacklist", "您的设备已被封禁，若有疑问请电话咨询："),
	TOKEN_IN_BLACKLIST_V2("token_in_blacklist_v2","您的设备已被封禁，若有疑问请关注“他趣Touch“公众号联系客服咨询"),
	TOKEN_IN_BLACKLIST_V3("token_in_blacklist_v3","您的设备已被封禁，无法再次登录平台"),
	SEARCH_CARD_ID_IN_BLACKLIST("search_card_id_in_blacklist", "此用户违规已被封禁"),
	SEARCH_CARD_ID_NOT_EXIST("search_card_id_err", "搜索的用户不存在"),

	EVALUATE_TYPE_ERROR("evaluate_type_error", "评价失败[j21107]"),
	EVALUATE_TYPE_NO_MATCH("evaluate_type_no_match", "评价失败[j21108]"),
	EVALUATE_MISS("evaluate_miss", "评价失败[j21109]"),
	EVALUATE_ID_EMPTY("evaluate_id_empty", "还未选择评价[j21110]"),
	DRIVER_ADD_KM_FANS_ERROR("driver_add_km_fans_error", "添加经验失败[j21111]"),
	IDENTITY_INVALID("identity_invalid", "证件号输入错误"),
	BIZ_NO_EMPTY("biz_no_empty", "认证标识为空，认证失败[j21113]"),
	CLONE_MISS("clone_miss", "认证标识为空，认证失败[j21118]"),
	BIZ_NO_NO_EXISTS("biz_no_no_exists", "认证信息不存在，认证失败[j21114]"),
	PHOTO_NO_EXISTS("photo_no_exists", "认证信息不存在，认证失败[j211141]"),
	ACCOUNT_NO_CERTIFICATION("account_no_certification", "该用户还未认证，不能取消认证[j21115]"),
	NO_CERTIFICATION("no_certification", "该用户还未实名认证，无法修改其支付宝账号[j21116]"),
	NO_CERTIFICATION_2("no_certification", "您还未实名认证，无法进行后续操作哦~"),
	NO_REWARD_ACCOUNT("no_reward_account", "支付宝账号异常，请联系客服"),
	CERTIFICATION_NO_CHANGE("certification_no_change", "新支付宝账号不能与原支付宝账号一样[j21115]"),

	CERTIFICATION_TIMES_LIMIT("certification_times_limit","今日提交已达上限，请明天再提交"),
	CERTIFICATION_NO_REAL_PERSON("certification_no_real_person", "通过真人认证后才能进行实名认证哦~"),
	CERTVERIFY_CONSULT_BUSY("certverify_consult_busy", "服务正忙，请稍后再试"),
	CERTVERIFY_CONSULT_TIMEOUT("certverify_consult_busy", "验证超时，请重新完成授权"),
	CERTVERIFY_CONSULT_AUTH_FAIL("certverify_consult_auth_fail", "授权失败，请稍后重试"),
	CERTVERIFY_CONSULT_RESULT_F("certverify_consult_result_f", "你填写的支付宝账号与实名人不一致，请重新输入"),
	CERTVERIFY_CONSULT_RESULT_F_EMPTY("certverify_consult_result_f_empty", ""),

	CANCEL_CERT_CLOSE("cancel_cert_close", "你已认证"),
	CERTIFICATION_DEVELOPING("certification_developing", "服务暂未开通"),

	CANCEL_CERT_REAL_PERSON_WITHOUT_REAL_NAME("cancel_cert_real_person_without_real_name", "平台提倡真实交友，带有真人认证标识的用户更容易找到对象哦~"),
	CANCEL_CERT_REAL_PERSON_WITH_REAL_NAME_WITHOUT_REWARD_ACCOUNT("cancel_cert_real_person_with_real_name_without_reward_account", "有真人认证标识的用户更容易找到对象哦~且解除真人认证后，实名认证将同时解除"),
	CANCEL_CERT_REAL_PERSON_WITH_REAL_NAME_AND_REWARD_ACCOUNT("cancel_cert_real_person_with_real_name_and_reward_account", "有真人认证标识的用户更容易找到对象哦~且解除真人认证后，实名认证和支付宝账号将同时解除"),

	CANCEL_CERT_REAL_NAME_WITH_REWARD_ACCOUNT("cancel_cert_real_name_with_reward_account", "解除实名认证会对您账户的提现造成不便且绑定的支付宝账号将同时解除"),
	CANCEL_CERT_REAL_NAME_WITHOUT_REWARD_ACCOUNT("cancel_cert_real_name_without_reward_account", "解除实名认证会对您账户的提现造成不便请慎重考虑~"),

	CANCEL_CERT_WHITE_LIST_REAL_PERSON("cancel_cert_white_list_real_person", "平台提倡真实交友，带有真人认证标识的用户更容易找到对象哦~"),
	CANCEL_CERT_WHITE_LIST_REAL_NAME("cancel_cert_white_list_real_name", "解除实名认证会对您账户的提现造成不便请慎重考虑~"),
	CANCEL_CERT_WHITE_LIST_REAL_NAME_WITH_REWARD_ACCOUNT("cancel_cert_white_list_real_name_with_reward_account", "解除实名认证会对您账户的提现造成不便且绑定的支付宝账号将同时解除"),

	DEL_CERT_WHITE_LIST_UUID_LIMIT("del_cert_white_list_uuid_limit", "批量取消认证白名单超过100条"),
	/**
	 * 上传文件格式错误
	 */
	UPLOAD_FILE_TYPE_ERROR("upload_file_type_error","上传文件格式错误"),
	/**
	 * 解析文件失败
	 */
	PARSE_FILE_FAILED("parse_file_failed","解析文件失败"),
	/**
	 * 无效数据
	 */
	INVALID_DATA("invalid_data","无效数据"),
	INVALID_SCHOOL("invalid_school","请选择正确的学校"),
	INVALID_HEIGHT_DATA("invalid_height_data","无效身高数据"),
	INVALID_WEIGHT_DATA("invalid_weight_data","无效体重数据"),

	/**
	 * 用户不存在
	 */
	USER_NO_EXISTS("user_no_exists", "账号是无效的哦，请骚候再试[j21200]"),

	/**
	 * 原密码不正确
	 */
	OLD_PASS_ERROR("old_pass_error", "原密码不对哦，来，再试一发！[j21201]"),

	/**
	 * 设置密码失败
	 */
	SET_PWD_FAILURE("set_pwd_failure", "设置密码失败[j21202]"),

	/**
	 * 重置密码失败
	 */
	RESET_PWD_FAILURE("reset_pwd_failure", "重置密码失败[j21203]"),

	/**
	 * 重置密码成功
	 */
	RESET_PWD_SUCCESS("reset_pwd_success", "重置密码成功[j21204]"),

	REG_FAIL("reg_fail", "注册失败，请骚候再试[j21205]"),
	/**
	 * openid 检测
	 */
	OPENID_BAN_SOA_FAIL("openid_ban_soa_fail", "服务正忙，请稍后再试"),
	OPENID_ALREADY_BAN("openid_already_ban", "该手机号/微信/QQ/身份信息存在严重违规行为已被限制使用"),
	OPENID_ALREADY_BAN_MOBILE("openid_already_ban_mobile", "该手机号存在严重违规行为已被限制使用"),
	OPENID_ALREADY_BAN_ALIPAY("openid_already_ban_alipay", "该身份信息存在严重违规行为已被限制使用"),
	ALIPAY_CHECK_ACCOUNT_FAIL("alipay_check_account_fail", "实名信息不一致"),
	BIND_SYSTEM_ERROR("bind_system_error", "绑定失败，请稍后重试"),
	ALIPAY_ACCOUNT_IS_RISK("alipay_account_is_risk", "该支付宝已绑定平台其它实名账号"),

	/**
	 * 设备还未注册
	 */
	TOKEN_NO_REG("token_no_reg", "设备还未注册[j21206]"),

	/**
	 * 您还未登录
	 */
	NO_LOGIN("no_login", "您还未登录，登录以后才能继续操作哦[j21207]"),

	/**
	 * 账号或密码错误 正常
	 */
	USERNAME_OR_PASSWORD_ERROR("username_or_password_error", "用户名或者密码输错啦，好好想想"),
	/**
	 * 防异常请求
	 */
	@Deprecated
	USERNAME_0R_PASSWORD_ERROR_1("username_0r_password_error", "用户名或者密码输错啦，好好想想[j22001]"),
	@Deprecated
	USERNAME_0R_PASSWORD_ERROR_2("username_0r_password_error", "用户名或者密码输错啦，好好想想[j22002]"),

	WRONG_MOBILE_NUMBER_FORMAT("wrong_mobile_num_format","输入的手机号格式错误[j21318]"),

	/**
	 * 重置密码失败
	 */
	MODIFY_PWD_FAILURE("modify_pwd_failure", "修改密码失败[j21209]"),

	/**
	 * 重置密码成功
	 */
	MODIFY_PWD_SUCCESS("modify_pwd_success", "修改密码成功[j21210]"),

	/**
	 * 注册游客账号失败
	 */
	REG_GUEST_FAILURE("reg_guest_failure", "注册游客账号失败，请骚候再试[j21211]"),

	/**
	 * 登录失败
	 */
	LOGIN_FAILURE("login_failure", "登录失败，请稍后再试[j21212]"),

	/**
	 * 登录失败
	 */
	LOGIN_THIRD_FAILURE("login_third_failure", "登陆失败，请稍后再试[j21213]"),

	/**
	 * 用户名格式错误
	 */
	USERNAME_INVALIDATE("username_invalidate", "为了帐号安全，用户名请使用手机号[j21214]"),

	/**
	 * 获取uuid失败
	 */
	GET_UUID_FAILURE("get_uuid_failure", "注册失败了，请再试一次[j21215]"),

	/**
	 * 注册设备失败
	 */
	REGISTER_MEMBER_FAILURE("register_member_failure", "设备注册失败啦，请骚候再试[j21216]"),

	/**
	 * 手机号已经注册过返回的注册失败，因为手机号是否注册关系到用户的隐私，因此不直接提示手机号已注册，而返回注册失败
	 */
	 REGISTER_MOBILE_EXISTS("register_fail", "注册失败了，等下再试试[j21217]"),
	REGISTER_MOBILE_EXISTS_CHECKED("register_mobile_exists", "手机号已被注册"),
	CREATE_SN_FAIL("create_sn_fail", "不好意思发生了点小意外，请重试一下吧[j21218]"),

	/**
	 * 注册验证已过期
	 */
	REG_VERIFY_EXPIRE("reg_verify_expire", "注册验证已过期，请返回重新注册[j21219]"),

	/**
	 * 绑定第三方失败
	 */
	BIND_THIRD_FAILURE("bind_third_failure", "绑定失败，请稍微再试[j21220]"),

	/**
	 * 登录失败
	 */
	LOGIN_TIMES_OVER_LIMIT("login_times_over_limit", "您登录太频繁了，稍微歇歇吧[j21221]"),

	/**
	 * 账号或密码错误
	 */
	PASSWORD_ENCRYPTION_ERROR("password_encryption_error", "密码加密失败[j21222]"),

	/**
	 * 已达到每日注册上限
	 */
	REG_TOO_MANY_TODAY("reg_too_many_today", "你今天注册太多账号啦，明天再来吧[j21223]"),

	/**
	 * 注册失败
	 */
	REG_FAILURE("reg_failure", "注册失败了，等下再试试[j21224]"),

	ACCOUNT_BIRTH_ERROR("account_birth_error", "年龄不满18岁，禁止注册！"),

	ACCOUNT_NAME_FROZEN("account_name_frozen", "昵称不可用，换一个吧！[j21225]"),
	/**
	 * 昵称已存在
	 */
	ACCOUNT_NAME_EXISTS("account_name_exists", "╮(╯▽╰)╭唉，昵称被别人抢了[j21226]"),

	ACCOUNT_NAME_NOT_EXISTS("account_name_not_exists", "该昵称不存在哟[j21227]"),

	MOBILE_NOT_EXISTS("mobile_not_exists", "账号错误[j21228]"),

	DESTROY_NOTICE("destroy_notice", "提交注销后90天未操作自动注销，若90天有操作则视为放弃注销"),

	/**
	 * 昵称格式错误
	 */
	NICKNAME_INVALID("nickname_invalid", "昵称不能包含数字或字母"),
	NICKNAME_INVALID_BACKSTAGE("nickname_invalid_backstage", "昵称不能包含数字或字母，且不能超过10个字符"),
	SET_NICKNAME_NO_BIND_MOBILE("set_nickname_no_bind_mobile", "绑定手机号以后才能修改昵称哦[j21231]"),
	SET_PROFILE_NO_BIND_MOBILE("set_profile_no_bind_mobile", "绑定手机号以后才能设置个人简介哦[j21232]"),

	/**
	 * 昵称包含敏感词
	 */
	NICKNAME_SENSITIVE_WORDS("nickname_sensitive_words", "你的昵称包含敏感词哦，请修改一下[j21233]"),
	/**
	 * 昵称审核
	 */
	AUDIT_NICKNAME_NOT_EXIST("audit_nickname_not_exist", "审核记录不存在"),
	AUDIT_NICKNAME_NOT_AUDITING("audit_nickname_not_auditing", "请选择待审核数据进行审核"),
	AUDIT_NICKNAME_ALREADY_AUDIT_FAIL("audit_nickname_already_fail", "无法对已拒绝的昵称进行审核"),
	/**
	 * 个人简介
	 */
	AUDIT_PERSONAL_PROFILE_INFO_NOT_AUDITING("audit_personal_profile_info_not_auditing", "请选择待审核数据进行审核"),
	AUDIT_PERSONAL_PROFILE_INFO_ALREADY_AUDIT_FAIL("audit_personal_profile_info_already_fail", "无法对已拒绝的个人简介进行审核"),
	AUDIT_PERSONAL_PROFILE_INFO_ERROR_STATUS("audit_personal_profile_info_error_status", "状态错误"),
	/**
	 * 刚刚注册过了
	 */
	REGISTER_RECENTLY("register_recently", "你已经注册过了哦[j21234]"),

	WECHAT_UNAUTHORIZED("wechat_unauthorized", "鉴权失败[j21411]"),
	QQ_UNAUTHORIZED("qq_unauthorized", "鉴权失败[j21412]"),
	WEIBO_UNAUTHORIZED("weibo_unauthorized", "鉴权失败[j21413]"),
	APPLE_UNAUTHORIZED("apple_unauthorized", "鉴权失败[j21414]"),
	VISITOR_UNAUTHORIZED("visitor_unauthorized","注册失败，请稍后再试[j21415]"),

	/**
	 * 无效的手机号码
	 */
	MOBILE_EMPTY("mobile_empty", "手机号码为空[j21235]"),
	MOBILE_INVALID("mobile_invalid", "请输入正确的手机号码[j21236]"),

	/**
	 * 手机号已被使用
	 */
	MOBILE_EXISTS("mobile_exists", "该手机号已被绑定，请先解绑[j21237]"),

	/**
	 * 手机号关联错误
	 */
	MOBILE_RELATED_FAILURE("mobile_related_failure", "系统出错，请骚后再试[j21238]"),

	DEVICE_REGISTER_RECENTLY("device_register_recently", "设备刚刚注册过了[j21239]"),

	/**
	 * 验证码发送类型错误，只能为"sms":短信验证码 或 "voice":语音验证码
	 */
	VERIFYCODE_SEND_TYPE_FAILURE("verifycode_send_type_failure","验证码发送类型错误，只能为'sms':短信验证码或'voice':语音验证码[j21240]"),

	/**
	 * 重置密码时超过了验证码发送次数限制
	 */
	OUT_OF_RESET_VERIFY_LIMIT("out_of_reset_verify_limit","重置密码，超过验证码的发送次数限制[j21241]"),
	/**
	 * 注册时超过了验证码发送次数限制
	 */
	OUT_OF_REGISTER_VERIFY_LIMIT("out_of_register_verify_limit","注册时，超过验证码的发送次数限制[j21242]"),

	/**
	 * 修改密码时超过了验证码发送次数限制
	 */
	OUT_OF_MODIFY_VERIFY_LIMIT("out_of_modify_verify_limit","修改密码，超过验证码的发送次数限制[j21243]"),
	/**
	 * 语音认证超过了验证码发送次数限制
	 */
	OUT_OF_CERTIFICATION_VERIFY_LIMIT("out_of_certification_verify_limit","语音认证，超过验证码发送次数限制[j21244]"),
	/**
	 * 登录时超过了验证码发送次数限制
	 */
	OUT_OF_LOGIN_VERIFY_LIMIT("out_of_login_verify_limit","获取验证码次数过多，请明日重试[j21245]"),

	/**
	 * 绑定时超过了验证码发送次数限制
	 */
	OUT_OF_BIND_VERIFY_LIMIT("out_of_bind_verify_limit","绑定时，超过验证码的发送次数限制[j21246]"),
	/**
	 * 登录时验证码校验失败
	 */
	VERIFYCODE_FAILURE("verifycode_failure", "验证码错误，请重新输入"),

	/**
	 * 验证码发送成功
	 */
	VERIFY_CODE_SEND_SUCCESS("verify_code_send_success", "验证码发送成功[j21250]"),

	/**
	 * 手机号关联成功
	 */
	MOBILE_RELATED_SUCCESS("mobile_related_success", "更新成功[j21251]"),

	/**
	 * 手机号更新成功
	 */
	UPDATE_MOBILE_SUCCESS("update_mobile_success", "手机号更新成功[j21252]"),

	/**
	 * 更新头像成功
	 */
	AVATAR_SUCCESS("avatar_success", "更新头像成功，新头像好赞[j21253]"),

	/**
	 * 登记设备信息失败
	 */
	REGISTER_DEVICE_FAILURE("exists", "设备已登记过[j21254]"),

	/**
	 * 无效的验证码
	 */
	UNVAILD_VERIFY_CODE("unvaild_verify_code", "无效的验证码[j21255]"),

	/**
	 * 手机号验证错误
	 */
	MOBILE_CHECK_FAILURE("mobile_check_failure", "手机号验证错误[j21256]"),

	/**
	 * 发送手机短信失败
	 */
	SEND_SMS_FAILURE("send_sms_failure", "发送手机短信失败[j21257]"),

	/**
	 * 同个手机号当天发送短信次数超限
	 */
	DAILY_MOBILE_VERIFY_CODE_LIMIT("daily_mobile_verify_code_limit", "你的手机今天发了太多短信啦，明天再来吧[j21258]"),

	/**
	 * 同台设备当天发送短信次数超限
	 */
	DAILY_DEVICE_VERIFY_CODE_LIMIT("daily_device_verify_code_limit", "你的设备今天发了太多短信啦，明天再来吧[j21259]"),

	/**
	 * 更新绑定的设备信息失败
	 */
	UPDATE_MEMBER_ID_FAILURE("update_member_id_failure", "更新绑定的设备信息失败[j21260]"),

	/**
	 * 未找到相关设备
	 */
	NO_MEMBER_FOUND("no_member_found", "未找到相关设备[j21261]"),

	/**
	 * 趣币数量不能小于０
	 */
	QU_COIN_NO_LE_ZEOR("qu_coin_no_le_zeor", "趣币数量不能小于０[j21262]"),

	/**
	 * 趣币数量不足
	 */
	QU_COIN_NO_ENOUGH("qu_coin_no_enough", "趣币数量不足[j21263]"),

	DEPRECIATION_ERROR("depreciation_error", "折损应为1-100的整数[j21264]"),

	/**
	 * 需要绑定手机和密码
	 */
	NEED_BIND_MOBILE_AND_PASSWORD("need_bind_mobile_and_password", "需要绑定手机和密码[j21265]"),

	/**
	 * 需要绑定手机
	 */
	NEED_CHECK_MOBILE("need_check_mobile", "你还没有绑定手机哦[j21266]"),

	/**
	 * 需要验证码登录
	 * 2023.10.7备注 客户端有特殊处理，会跳转到验证码登录页面
	 */
	NEED_LOGIN_BY_VERIFICATION_CODE("need_verification_code","您的账号存在风险，为了账号安全请通过「验证码」登录"),
	/**
	 * 需要设置密码
	 */
	NEED_SET_PASSWORD("need_set_password", "你还没有设置密码哦[j21267]"),

	/**
	 * 已过期
	 */
	ACCOUNTS_EXPIRE("accounts_expire", "已过期[j21268]"),

	/**
	 * 已经修改过性别，不允许再次修改
	 */
	UPDATE_SEX_NUMBER_LIMIT("update_sex_number_limit", "你已经修改过性别，不能再次修改了[j21269]"),
	UPDATE_SEX_REFUSE("update_sex_refuse", "性别是不能修改的哦[j21270]"),

	/**
	 * 昵称1周内只能修改一次
	 */
	CAN_NOT_SET_NICKNAME("can_not_set_nickname", "昵称1周内只能修改一次[j21271]"),
	/**
	 * 签名一周只能修改一次
	 */
	CAN_NOT_SET_PROFILE("can_not_set_profile", "签名一周只能修改一次[j21272]"),

	/**
	 * 签名 纯数字或英文
	 */
	PROFILE_PURE_NUM_OR_WORD("profile_pure_num_or_word","签名不能包含数字或字母"),

	/**
	 * 签名 全匹配敏感字符
	 */
	PROFILE_ALL_SENSITIVE_WORDS("profile_all_sensitive_words", "个人签名包含敏感词不能保存"),

	/**
	 * 密码验证没有通过
	 */
	PASSWORD_NOT_PASS("password_not_pass", "密码验证没有通过[j21273]"),

	/**
	 * 你还没有设置密码，请先设置后操作
	 */
	PASSWORD_NOT_SET("password_not_set", "你还没有设置密码，请先设置后操作[j21274]"),

	/**
	 * 设置的日期太早
	 */
	BIRTH_IS_LOW("birth_is_low", "╮(╯▽╰)╭设置的日期太早了，请使用真实年龄吧[j21275]"),
	/**
	 * 设置的日期大于指定日期
	 */
	BIRTH_IS_HIGH("birth_is_high", "╮(╯▽╰)╭设置的日期太大了，请使用真实年龄吧[j21276]"),

	/**
	 * 调用社区接口返回:2级才可以自定义头像哦，快去升级吧~
	 */
	NO_PRIV_MODIFY("no_priv_modify", "2级才可以自定义头像哦，快去升级吧[j21277]"),

	NO_PRIV("no_priv", "权限不足，快去升级获取权限吧[j21278]"),
	NO_KM_PRIV("no_km_priv", "你的经验数不足，暂时无法使用该功能[j21279]"),
	NO_V_PRIV("no_v_priv", "你还未解锁该权限，暂时无法使用该功能[j21280]"),

	/**
	 * 修改头像失败
	 */
	UPDATE_AVATAR_FAIL("update_avatar_fail", "更新头像失败了，再来一次[j21281]"),

	/**
	 * 未查到相关的经验等级配置
	 */
	LEVEL_INFO_NO_FOUND("level_info_no_found", "未查到相关的经验等级配置[j21281]"),

	/**
	 * 未查到相关的等级勋章配置
	 */
	MEDAL_INFO_NO_FOUND("medal_info_no_found", "未查到相关的等级勋章配置[j21282]"),

	/**
	 * 没有获取到组类型group_type
	 */
	NO_GET_GROUP_TYPE("no_get_group_type","没有获取到组类型group_typ[j21283]"),

	/**
	 * soa获取到组类型失败
	 */
	SOA_GET_GROUP_ERROR("soa_get_group_error","soa获取到组类型失败[j21284]"),

	MODIFY_MOBILE_SAME("modify_mobile_same","该手机号已与当前账号绑定，请更换手机号[j21285]"),
	/**
	 * 更换手机号发送验证码操作中，输入的密码和当前帐号的密码不匹配
	 */
	MODIFY_MOBILE_PW_NO_PASS("Modify_Mobile_pw_no_pass","输入的密码和当前帐号的密码不匹配[j21286]"),

	/**
	 * 验证码验证没有通过
	 */
	MODIFY_NO_PASS("modify_no_pass","验证码验证没有通过[j21287]"),

	/**
	 * 调用社区接口没有返回值
	 */
	RETURN_NUL_FROM_NICKNAME_PRIV("return_nul_from_nickname_priv","调用社区是否能够修改昵称接口没有返回值[j21288]"),

	/**
	 * 分页参数必须是整数
	 */
	PAGE_LIMIT_NOT_INTEGER_ERROR("page_limit_not_integer_error", "分页参数必须是整数[j21289]"),

	/**
	 * 调用搜索接口失败
	 */
	CALL_SOA_SEARCH_RESPONSE_FAIL("call_soa_search_response_fail","调用搜索接口失败[j21290]"),

	/**
	 * 该他趣帐号已绑定
	 */
	HAD_BIND_THIRD_LOGIN("had_bind_third_login","该他趣帐号已被绑定，请先解绑[j21291]"),

	/**
	 * 未注册的手机号（用未注册的手机号重置密码时返回）
	 */
	UNREG_MOBILE("unreg_mobile","未注册的手机号[j21292]"),

	ACCOUNT_PHOTO_EMPTY("account_photo_empty", "用户相册没有图片[j21293]"),

	ACCOUNT_PHOTO_CHANGE("account_photo_change", "该用户图片已更新"),
	ACCOUNT_PHOTO_HAS_THUMBS_UP("account_photo_has_thumbs_up", "您今日已对该相册点赞，请明日再来～"),

	ACCOUNT_ALREADY_CERTIFICATION("account_already_certification", "您已经认证过了[j21294]"),
	IDENTITY_NO_ALREADY_CERTIFICATION("identity_no_already_certification", "该证件号已经绑定过[j21295]"),
	IDENTITY_NO_ALREADY_CERTIFICATION_IN_CHAT("identity_no_already_certification", "该证件号已经绑定业务级实名认证[j21295]"),
	IDENTIFY_NO_STATUS_IN_USE("identify_no_status_in_use", "该证件号绑定过的账号暂未注销"),
	ACCOUNT_UNBIND_IDENTITY_NO("account_unbind_identity_no", "当前账号未绑定证件号信息"),

	CERTIFY_FAIL("certify_fail", "认证失败[j21296]"),
	CERTIFY_FAIL_INCONSISTENT("certify_fail_inconsistent", "当前人像与真人认证人像非同一人"),

	IDENTITY_INFO_NO_EXIST("identity_info_no_exist","身份标识详情不存在[j21297]"),
	IDENTYTI_TYPE_TO_LONG("identity_type_to_long","最多只能佩戴三个身份标志哦[j21298]"),

	ALREADY_REAL_PERSON_CERITIFICATION("already_real_person_certification", "您已完成真人认证"),

	OPER_TIMES_TOO_OFTEN("oper_times_too_often", "您操作太频繁了，稍微歇歇吧[j21299]"),

	ACHIEVE_EXISTS("achieve_exists", "已获取过该成就勋章，不能再次获取[j21300]"),
	ADORN_ACHIEVE_ERROR("adorn_achieve_error", "佩戴勋章失败，请再试一次[j21301]"),
	ADORN_EXCEED("adorn_exceed", "最多只能佩戴三个成就勋章哦[j21302]"),
	ADORN_NO_EXISTS("adorn_no_exists", "不能佩戴还未获取的成就勋章哦[j21303]"),
	UNADORN_NO_EXISTS("unadorn_no_exists", "还未佩戴该成就勋章，无法卸下[j21304]"),
	CANCEL_NO_EXISTS_ACHIEVE("cancel_no_exists_achieve", "还未获得该勋章，无法回收[j21305]"),
	ADORN_EXPER_ACHEIVE("adorn_exper_acheive", "经验成就勋章无无法手动佩戴[j21306]"),
	UNADORN_EXPER_ACHEIVE("unadorn_exper_acheive", "经验成就勋章无无法手动卸下[j21307]"),
	WHITE_TPYE_ERROR("white_type_error", "白名单类型错误[j21308]"),
	SCHEMA_NO_FOUND("schema_no_found", "未找到schema配置[j21309]"),
	VERIFY_VCODE_BUSY("verify_vcode_busy", "服务忙，请稍候再试[j21310]"),
	MOBILE_DESTROYED("mobile_destroyed", "该手机号已注销，无法注册[j21311]"),
	RESET_PWD_MOBILE_DESTROYED("reset_pwd_mobile_destroyed", "该手机号已注销，无法使用[j21312]"),
	WECHAT_DESTROYED("wechat_destroyed", "此微信账号已注销，无法注册[j21313]"),
	WECHAT_BEEN_USED("wechat_been_used", "当前登录的微信号已被绑定，请更换微信号"),
	QQ_DESTROYED("qq_destroyed", "此QQ账号已注销，无法注册[j21314]"),
	WEIBO_DESTROYED("weibo_destroyed", "此微博账号已注销，无法注册[j21315]"),
	BIND_MOBILE_DESTROYED("mobile_destroyed", "该手机号已注销，无法绑定[j21316]"),
	MOBILE_WRONG("mobile_wrong", "请输入正确的手机号[j21317]"),
	CERTIFICATION_REAL_NAME_ERROR("certification_real_name_error", "姓名与证件号不一致[j21319]"),
	APPLE_DESTROYED("apple_destroyed", "此苹果账号已注销，无法注册[j21320]"),
	VISITOR_DESTROYED("visitor_destroyed", "此账号已注销，无法注册[j21321]"),
	IDENTITY_NONAGE("identity_nonage", "经核实您未满18周岁，无法正常使用他趣，请自觉遵守相关法律"),
	IDENTITY_FAIL("identity_fail", "认证失败，请重新尝试（目前暂支持福建省内证件）"),
	REWARD_ACCOUNT_ERROR("reward_account_error", "账号格式错误，请重新输入"),
	HAS_PARAMETER_ANY("has_parameter_any", "至少需要填写一个筛选条件[j21322]"),
	DESTROYED_GET_INFO_ERR("destroy_get_info", "系统繁忙，请骚后再试"),
	DESTROYED_MOBILE_BLANK("destroy_mobile_blank", "短信验证手机号不能为空"),
	DESTROYED_VERIFY_BLANK("destroy_verify_blank", "验证码不能为空"),

	CHAT_CERTIFICATION_ADULT_VERIFY_ERROR("chat_adult_verify", "未满18岁禁止使用他趣"),
	CHAT_CERTIFICATION_AGED_VERIFY_ERROR("chat_aged_verify", "60岁以上用户暂不支持实名"),

	CERT_WHITE_LIST_ILL_PARAMS("cert_white_list_ill_params", "添加白名单失败，参数错误"),
	CERT_WHITE_LIST_UUID_NOT_EXIST("cert_white_list_uuid_not_exist", "添加白名单失败，uuid不存在"),
	CERT_WHITE_LIST_UNKNOW_FROM("cert_white_list_unknow_from", "添加白名单失败，未知类型"),
	CERT_WHITE_LIST_EXIST("cert_white_list_exist", "添加白名单失败，该uuid已存在白名单中"),
	/**
	 * 请上传头像
	 * 2023.09.22 确认客户端没特殊处理这个错误码
	 */
	NEED_AVATAR("need_avatar", "请上传头像"),

	NEED_BASE_PHOTO("need_base_photo", "待解绑帐号没有底图"),
	/**
	 * 客户端特殊处理该错误码
	 * 需求：https://o15vj1m4ie.feishu.cn/wiki/S0IUwgrebiyqN0kgotAcqi2BnDg
	 */
	NEED_UPLOAD_AVATAR("need_upload_avatar", "请先上传本人头像"),
	LOCATION_INFO_NOT_FIND_ERROR("location_info_error_ip_find", "用户位置信息查询失败"),

	FLASH_APPID_EMPTY("flash_appid_empty", "appId配置不存在(j20507)"),
	FLASH_ERROR("flash_error", "操作失败，请稍候再试或使用验证码登录(j20510)"),
	FLASH_MOBILE_ERROR("flash_mobile_error", "请求失败，请稍候再试(j20512)"),
	FLASH_REQ_LIMIT("flash_req_limit", "您使用一键登录过于频繁，请使用其他方式登录(j20511)"),
	GY_ERROR("gy_error", "操作失败，请稍候再试或使用验证码登录(j20510)"),
	GY_MOBILE_ERROR("gy_mobile_error", "请求失败，请稍候再试(j20512)"),
	GY_REQ_LIMIT("gy_req_limit", "您使用一键登录过于频繁，请使用其他方式登录(j20511)"),
	NEW_PWD_NO_CHG("new_pwd_no_chg", "新密码不能与原密码相同(j20511)"),
	/**
	 * 2023.10.7备注 客户端无特殊处理，直接弹出提示
	 */
	RISK_PASSWORD_LOGIN("risk_password_login", "由于您长时间未登录，为了保证您的账号安全，请使用验证码登录(j20512)"),
	ZHIMA_CERTIFIC_EXPIRE("zhima_certific_expire", "当前版本过低无法完成认证，请升级至最新版本(j20513)"),
	SEX_TYPE_ERROR("sex_type_error", "性别只能为男或女(j20514)"),
	LOGIN_VERSION_LIMIT("login_version_limit","当前版本过低，请升级至最新版本(j20515)"),
	UPLOAD_AVATAR_FAIL_ILLEGAL("update_avatar_fail_illegal", "您提交的头像涉嫌违规，请重新上传"),
	UPLOAD_AVATAR_FAIL_QUALIITY("update_avatar_fail_quality", "您提交的照片太模糊，请选择更清晰的照片上传"),
	UPLOAD_AVATAR_FAIL_INCONSISTENT("update_avatar_fail_inconsistent", "头像检测非真人，请重新上传"),
	UPLOAD_AVATAR_FAIL_TIMEOUT("upload_avatar_fail_timeout", "服务正忙，请稍后再试"),
	UPLOAD_AVATAR_FAIL_LIVE_FACE("upload_avatar_fail_live_face", "活体数据异常"),

	UPDATE_AVATAR_FAIL_DETECT_FACE("update_avatar_fail_detect_face", "头像检测非人像，请重新上传"),


	UPLOAD_COVER_FAIL_ILLEGAL("update_cover_fail_illegal", "您提交的图片涉嫌违规，请重新上传"),
	UPLOAD_COVER_FAIL_QUALIITY("update_cover_fail_quality", "您提交的照片太模糊，请选择更清晰的照片进行上传~"),
	UPLOAD_COVER_FAIL_TIMEOUT("upload_cover_fail_timeout", "服务正忙，请稍后再试"),
	UPLOAD_COVER_FAIL_LIVE_FACE("upload_cover_fail_live_face", "请先完成真人认证"),

	HOME_COVER_RECHECK_ALREADY_CHANGE_ERROR("home_cover_recheck_already_change_error", "封面数据已变更，无法进行操作"),
	HOME_COVER_CHECK_STATUS_ERROR("home_cover_check_status_error", "审核状态错误"),
	UPLOAD_HOME_COVER_FAIL_ILLEGAL("update_home_cover_fail_illegal", "您提交的封面涉嫌违规，请重新上传"),
	UPLOAD_HOME_COVER_FAIL_QUALIITY("update_home_cover_fail_quality", "您提交的照片太模糊，请选择更清晰的照片上传"),

	PHOTO_DETECT_BUSY("photo_detect_busy", "服务正忙，请稍后再试"),
	PHOTO_DETECT_Ill_BUSY("photo_detect_ill_busy", "服务正忙，请稍后再试[j20601]"),
	PHOTO_DETECT_QUALITY_BUSY("photo_detect_quality_busy", "服务正忙，请稍后再试[j20602]"),
	PHOTO_DETECT_COMPARE_BUSY("photo_detect_compare_busy", "服务正忙，请稍后再试[j20603]"),
	PHOTO_DETECT_FACE_BUSY("photo_detect_face_busy", "服务正忙，请稍后再试[j20604]"),

	PHTO_CHECK_BUSY("photo_check_busy", "当前头像/相册审核繁忙，请重试"),

	/**
	 * 阿里云活体真人相关
	 */
	ALIYUN_NON_FACE_DETECT("aliyun_non_face_detect","照片未检测到人脸，请更换照片"),
	ALIYUN_DETECT_OVER_TIMES_LIMIT("aliyun_detect_over_times_limit","你今日认证次数已达上限，明天再来吧～"),
	ALIYUN_VERIFY_SUCCESS_MSG("aliyun_verify_success_msg","恭喜你，已完成真人照片认证"),
	ALIYUN_VERIFY_FALI_MSG("aliyun_verify_fali_msg","认证失败，请上传本人照片"),
	ALIYUN_VERIFY_FALI_INCONSISTENT_MSG("aliyun_verify_fali_inconsistent_msg","认证失败:请保持真人与实名认证一致"),

	VOICE_SIGN_INFO_NOT_EXIST("voice_sign_info_not_exist", "通过失败，该记录不存在"),

	/**
	 * 活体真人相关
	 */
	NON_FACE_DETECT("non_face_detect","照片未检测到人脸，请更换照片"),
	DETECT_TOO_FAST("detect_too_fast","点击太快啦，请歇一歇再来"),
	DETECT_OVER_TIMES_LIMIT("detect_over_times_limit","您认证的次数过于频繁，请明天再试试"),
	DETECT_OVER_TIMES_LIMIT_2("detect_over_times_limit","您的操作过于频繁，请稍后重试"),
	DETECT_OVER_TIMES_LIMIT_3("detect_over_times_limit","您的操作过于频繁，请明天再试试"),
	VERIFY_SUCCESS_MSG("verify_success_msg","恭喜你，已完成真人照片认证"),
	DETECT_OVER_TIMES_LIMIT_WITHDRAW("detect_over_times_limit_withdraw","已达到今日实名扫脸失败上限，请明天重试"),
	WITHDRAW_VERIFY_SUCCESS_MSG("withdraw_verify_success_msg","实名校验通过，进入提现流程"),
	WITHDRAW_VERIFY_FALI_MSG("withdraw_verify_fail_msg","实名校验失败，请重试"),
	VERIFY_FALI_MSG("verify_fali_msg","认证失败，请上传本人照片"),
	VERIFY_FALI_INCONSISTENT_MSG("verify_fali_inconsistent_msg","认证失败:请保持真人与实名认证一致"),
	VERIFY_FALI_SDK_ERROR_MSG("verify_fali_sdk_error_msg","认证失败：SDK返回错误"),
	NOT_LIVE_FACE_DETECT("not_live_face_detect", "该用户未进行活体"),
	VERIFY_FALI_MOBILE_MSG("verify_fali_mobile_msg","认证失败，人脸与真人或实名认证不一致"),
	VERIFY_FALI_MOBILE_MSG_2("verify_fali_mobile_msg","比对失败"),
	VERIFY_FALI_MOBILE_MSG_3("verify_fali_mobile_msg","认证失败，人脸与实名认证不一致"),

	UNBIND_VERIFY_FALI_MSG("unbind_verify_fali_msg","认证失败，您可以再次尝试"),

	/**
	 * 自我介绍
	 */
	INTRODUCTION_EMPTY("introduction_empty", "该用户当前无自我介绍~"),
	INTRODUCTION_STATUS_AUDITING("introduction_status_audit", "自我介绍审核中，请您耐心等待~"),
	INTRODUCTION_COMMON_ERROR_MSG("introduction_common_error_msg", "请输入不少于60字的自我介绍，丰富的自我介绍更容易获得异性青睐哦~"),
	INTRODUCTION_CONTENT_EMOJI_ERROR_MSG("introduction_cotent_emoji_error_msg", "请勿提交含有表情的自我介绍"),
	INTRODUCTION_TIMES_LIMIT("introduction_times_limit", "今日已达上限，请明天再提交！"),
	INTRODUCTION_SUBMIT_TOO_FAST("introduction_submt_too_fast", "服务正忙，请稍后再试~"),
	INTRODUCTION_RETURN_TIMEOUT("introduction_return_timeout", "提交失败"),
	INTRODUCTION_PHOTO_WIDTH_HEIGHT_ERROR("introduction_photo_width_height_error", "图片宽高数据错误"),
	INTRODUCTION_PHOTO_ILLEGAL("introduction_photo_illegal", "您提交的照片涉嫌违规，请重新上传"),
	INTRODUCTION_CONTENT_SENSITIVE("introduction_content_sensitive","文本或图片内容涉嫌敏感，请重新编辑"),
	INTRODUCTION_PHOTO_SAME_WITH_AVATAR("introduction_photo_same_with_avatar", "您上传的照片和头像相同，请重新上传本人其它照片"),
	INTRODUCTION_PHOTO_SIMILAR_WITH_AVATAR("introduction_photo_similar_with_avatar", "该照片和头像相似度过高，请更换一张新的"),
	INTRODUCTION_PHOTO_FAIL_QUALIITY("introduction_photo_quality", "您提交的照片过于模糊，请选择更为清晰的照片进行上传"),
	INTRODUCTION_PHOTO_FAIL_INCONSISTENT("introduction_photo_fail_inconsistent", "您上传的照片非本人，请重新上传照片"),
	INTRODUCTION_FAIL_LIVE_FACE("introduction_fail_live_face", "请先完成真人认证"),
	INTRODUCTION_DETECT_FAIL_TIMEOUT("introduction_detect_fail_timeout", "服务正忙，请稍后再试"),
	INTRODUCTION_PHOTO_EMPTY("introduction_photo_empty", "请上传展现自我的照片"),
	INTRODUCTION_AVATAR_EMPTY("introduction_avatar_empty", "请先上传头像"),
	INTRODUCTION_NO_GOOD_SAMPLES_UUID("introduction_avatar_empty", "暂无数据"),

	/**
	 * 语音签名
	 */
	VOICE_SIGN_ILLEGAL("voice_sign_illegal","您提交的语音签名涉嫌违规，请重新上传"),

	// 配配历史数据处理失败
	PEIPEI_HISTORY_DATA_HANDLE_FAIL_MSG("peipei_history_data_handle_fail_msg", "配配历史数据处理失败"),
	PEIPEI_HISTORY_DATA_HANDLE_FAIL_MSG_NO_EXIST("peipei_history_data_handle_fail_msg_no_exit", "配配历史数据处理失败:无该用户"),
	PEIPEI_REG_LIMIT("peipei_reg_limit", "非常抱歉地通知您，配配APP已停止接受注册"),

	// =============微信错误码===============
	WEIXIN_40029("40029", "不合法的oauth_code[j240029]"),

	WEIXIN_40001("40001", "获取access_token时AppSecret错误，或者access_token无效[j240001]"),

	WEIXIN_40014("40014", "不合法的access_token[j240014]"),

	WEIXIN_41001("41001", "缺少access_token参数[j241001]"),

	WEIXIN_42001("42001", "access_token超时[j242001]"),

	WEIXIN_40031("40031", "不合法的openid列表[j240031]"),

	WEIXIN_40032("40032", "不合法的openid列表长度[j240032]"),

	WEIXIN_41009("41009", "缺少openid[j241009]"),

	WEIXIN_9001006("9001006", "获取OpenID失败[j29001006]"),

	/**
	 * 手机号换绑验证已过期
	 */
	CHANGE_MOBILE_VERIFY_EXPIRE("change_mobile_verify_expire", "换绑验证已过期，请返回重新进行验证[j21219]"),

	AVATAR_TYPE_ERR("avatar_type_err","您上传的图片格式有误，请换张图片试试"),

	CLONED_MISS_OR_ERR("cloned_miss_or_err","不合法的登录请求[j26001]"),

	GET_LOCK_FAIL("get_lock_fail","您操作得太频繁了，请稍后再试"),

	COMMON_ERROR_CODE("common_error_code", "系统异常，请稍后再试"),

	MAJOR_RISK_FAILED("major_risk_failed", "填写专业存在风险,请重新编辑"),

	CONTENT_RISK_FAILED("content_risk_failed", "填写内容存在风险,请重新编辑"),

	AUDIT_ONGOING("audit_ongoing", "审核中，请等待"),

	EMOJI_ERROR("emoji_error", "emoji不支持"),

	DESTROY_ERROR("destroy_error", "注销失败，请联系客服"),

	;

	private final String value;// code

	private String reasonPhrase;// message description

	CodeStatus(String value, String reasonPhrase) {
		this.value = value;
		this.reasonPhrase = reasonPhrase;
	}

	/**
	 * Return the integer value of this status code.
	 */
	@Override
	public String value() {
		return this.value;
	}

	/**
	 * Return the reason phrase of this status code.
	 */
	@Override
	public String getReasonPhrase() {
		return reasonPhrase;
	}

	void setReasonPhrase(String reasonPhrase) {
		this.reasonPhrase = reasonPhrase;
	}

	@Override
	public String toString() {
		return value;
	}

	public static ICodeStatus getCodeStatus(String statusCode) {
		for (CodeStatus status : values()) {
			if (status.value.equals(statusCode)) {
				return status;
			}
		}
		for (SysCodeStatus status : SysCodeStatus.values()) {
			if (status.value().equals(statusCode)) {
				return status;
			}
		}
		return SysCodeStatus.UNDEFINED;
	}
}
