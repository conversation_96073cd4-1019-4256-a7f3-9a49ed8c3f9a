package cn.taqu.account.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>  2019/5/31 10:18 AM
 */
public enum  RegStyle {
    /** 手机号注册 **/
    Mobile, 
    /** 手机号自动注册 **/
    Mobile_Auto,
    /** 闪验注册 **/
    Flash, 
    /** 闪验自动注册 **/
    Flash_Auto, 
    /** 微信注册 **/
    WeChat, 
    /** qq注册 **/
    QQ,
    /** 微博注册 **/
    WeiBo, 
    /** 苹果登录 **/
    Apple,
    /** 游客模式登录 **/
    Visitor,
    /** 微信公众号注册 **/
    WeChat_Official,
    /** H5注册 **/
    H5,
    /** 后台注册 **/
    Admin,
    ;

    public static RegStyle getByName(String name, RegStyle defaultRegStyle) {
        for(RegStyle style : RegStyle.values()) {
            if(StringUtils.equalsIgnoreCase(name, style.name())) {
                return style;
            }
        }

        return defaultRegStyle;
    }
}
