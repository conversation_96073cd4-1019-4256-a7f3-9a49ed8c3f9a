package cn.taqu.account.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 状态常量
 *
 * <AUTHOR>
 * @date 2022/11/15 10:45 上午
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {
;

    /**
     * 常见开关：启用、禁用
     */
    @Getter
    @AllArgsConstructor
    public enum Switch {
        /**
         * 禁用
         */
        DISABLE(0, "禁用"),
        /**
         * 启用
         */
        ENABLE(1, "启用"),

        ;

        /**
         * 业务标识
         */
        private final Integer code;
        /**
         * 描述
         */
        private final String desc;

        /**
         * 匹配
         *
         * @param code
         * @return
         */
        public boolean match(Integer code) {
            return Objects.equals(this.code, code);
        }
    }

    /**
     * 常见开关：启用、禁用
     */
    @Getter
    @AllArgsConstructor
    public enum AccountStatus {

        /**
         * 注销
         */
        DEREGISTER(0, "注销"),
        /**
         * 活跃
         */
        ACTIVE(1, "活跃"),
        /**
         * 冻结
         */
        FREEZE(2, "冻结"),

        ;

        /**
         * 业务标识
         */
        private final Integer code;
        /**
         * 描述
         */
        private final String desc;

        /**
         * 匹配
         *
         * @param code
         * @return
         */
        public boolean match(Integer code) {
            return Objects.equals(this.code, code);
        }
    }

}
