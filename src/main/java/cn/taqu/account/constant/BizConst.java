package cn.taqu.account.constant;

/**
 * 业务上的魔法值
 * <AUTHOR> Wu.D.J
 */
public interface BizConst {

    /**
     * already completed chatroom certification
     */
    int CHAT_CERTIFICATION = 1;

    /**
     * not yet completed chatroom certification
     */
    int NOT_CHAT_CERTIFICATION = 0;

    /**
     * need id<PERSON><PERSON>'s name and number when to accomplish the application real name certification,
     * because hasn't user information which stored in our system.
     */
    int NEED_CERTIFICATION_INFO = 1;

    /**
     * need id<PERSON>ard's name and number when to accomplish the application real name certification,
     * because user had already completed chatroom certification and
     * his identity information had stored in our system.
     */
    int NOT_NEED_CERTIFICATION_INFO = 0;
}
