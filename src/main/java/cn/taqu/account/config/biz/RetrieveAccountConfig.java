package cn.taqu.account.config.biz;

import cn.taqu.core.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 账号找回业务配置
 *
 * <AUTHOR>
 * @date 2024/9/11 10:28 上午
 */
@Slf4j
public class RetrieveAccountConfig {

    /**
     * 配置
     *
     * @param retrieveAccountByMobileValue
     */
    public static MobileRetrieveConfig retrieveConfig;

    /**
     * 配置赋值
     *
     * @param value
     */
    public static void setRetrieveConfig(String value) {
        try {
            log.info("account/mobile/retrieveAccount, value:{}", value);
            retrieveConfig = JsonUtils.stringToObject(value, MobileRetrieveConfig.class);
        } catch (Exception e) {
            log.error("找回账号配置错误, value:{}", value);
        }
    }

    /**
     * 手机找回配置类
     */
    @Data
    public static class MobileRetrieveConfig {

        /**
         * 设备次数限制模式
         */
        private String tokenUuidLimitMode;

        /**
         * 设备次数阈值
         */
        private Integer tokenUuidLimitThreshold;

        /**
         * 设备（总）次数限制模式
         */
        private String tokenLimitMode;

        /**
         * 设备（总）次数阈值
         */
        private Integer tokenLimitThreshold;

        /**
         * 身份证限制模式
         */
        private String identifyLimitMode;

        /**
         * 身份证限制阈值
         */
        private Integer identifyLimitThreshold;

        /**
         * 手机号次数限制模式
         */
        private String mobileLimitMode;

        /**
         * 手机号次数阈值
         */
        private Integer mobileLimitThreshold;

        /**
         * ip次数限制模式
         */
        private String ipLimitMode;

        /**
         * ip次数阈值
         */
        private Integer ipLimitThreshold;
    }
}
