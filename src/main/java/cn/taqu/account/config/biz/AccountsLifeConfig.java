package cn.taqu.account.config.biz;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.Data;


/**
 * 我的生活配置对象
 * 
 * <AUTHOR>
 * 2024年12月10日上午10:23:24
 */
@Data
public class AccountsLifeConfig {

    public static final String EXAMPLE_HTML_DEFAULT = "m=web&a=url&ul=https://h5.whtaqu.cn/topic/20241206100827_dvYxXu.html";
    
    /**
     * 容器配置 key-排序，value-文案
     */
    private Map<Integer, String> containerConfigMap = Maps.newHashMap();
    
    /**
     * 我的生活h5地址模板配置  key-性别，value-跳转链接
     */
    private Map<Integer, String> exampleHtmlMap = Maps.newHashMap();

}
