package cn.taqu.account.config.biz;

import cn.taqu.core.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 新引力配置
 *
 * <AUTHOR>
 * @date 2024/9/11 10:28 上午
 */
@Slf4j
public class AllureConfig {

    /**
     * 分值配置
     */
    public static Point point;

    /**
     * 比例配置
     */
    public static Ratio ratio;

    /**
     * 初始化分配配置
     *
     * @param str
     */
    public static void setPointConf(String str) {
        point = JsonUtils.stringToObject(str, Point.class);
    }

    /**
     * 初始化比例配置
     *
     * @param str
     */
    public static void setRatioConf(String str) {
        ratio = JsonUtils.stringToObject(str, Ratio.class);
    }

    /**
     * 分值配置
     */
    @Data
    public static class Point {

        /**
         * 基础分
         */
        private Integer basePoint;

        /**
         * 各项分
         */
        private Integer itemPoint;

        /**
         * 系统分区间范围
         */
        private List<Integer> systemPointRange;

        /**
         * 最终区间范围
         */
        private List<Integer> finalPointRange;

    }

    /**
     * 比例配置
     */
    @Data
    public static class Ratio {

        /**
         * 基础分
         */
        private Integer ratioPoint;

        /**
         * 各项分区间范围
         */
        private List<Integer> itemRatioRange;

        /**
         * 系统分区间范围
         */
        private List<Integer> systemRatioRange;

        /**
         * 最终区间范围
         */
        private List<Integer> finalRatioRange;
    }

}
