package cn.taqu.account.config.biz;

import cn.taqu.account.vo.resp.HomepageBackgroundResp;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 首页背景图片
 *
 * <AUTHOR>
 * @date 2024/9/11 10:28 上午
 */
@Slf4j
public class HomepageBackgroundConfig {

    public static List<HomepageBackgroundResp> backgroundList = Lists.newArrayList();
    public static Map<Integer, String> backgroundMap = Maps.newHashMap();

    /**
     * 初始化配置
     *
     * @param str
     */
    public static void setConf(String str) {
        backgroundList = JsonUtils.stringToObject(str, new TypeReference<List<HomepageBackgroundResp>>() {
        });
        backgroundMap = backgroundList.stream().collect(Collectors.toMap(HomepageBackgroundResp::getBackgroundId, HomepageBackgroundResp::getBackgroundUrl));
    }

}
