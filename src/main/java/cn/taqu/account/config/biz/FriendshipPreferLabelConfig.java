package cn.taqu.account.config.biz;

import cn.taqu.core.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 偏好标签业务配置
 *
 * <AUTHOR>
 * @date 2024/9/11 10:28 上午
 */
@Slf4j
public class FriendshipPreferLabelConfig {

    /**
     * 配置
     *
     * @param retrieveAccountByMobileValue
     */
    public static LabelConfig labelConfig;

    /**
     * 配置赋值
     *
     * @param value
     */
    public static void setLabelConfig(String value) {
        try {
            log.info("account/label/friendshipPrefer, value:{}", value);
            labelConfig = JsonUtils.stringToObject(value, LabelConfig.class);
        } catch (Exception e) {
            log.error("交友偏好配置错误, value:{}", value);
        }
    }

    /**
     * 标签配置
     */
    @Data
    public static class LabelConfig {
        /**
         * 交友偏好标题
         */
        private String title;
        /**
         * 交友偏好子标题
         */
        private String subTitle;
        /**
         * 标签选中上限
         */
        private Integer upperLimit;
        /**
         * 标签选中下限
         */
        private Integer lowerLimit;
    }
}
