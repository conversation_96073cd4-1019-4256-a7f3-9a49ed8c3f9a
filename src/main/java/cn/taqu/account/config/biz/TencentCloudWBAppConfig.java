package cn.taqu.account.config.biz;

import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;

import java.util.Map;

/**
 * 腾讯云WBappid
 *
 * <AUTHOR>
 * @desc: 这里配置内容是高必凌给的，参考需求https://project.feishu.cn/hb_technology/story/detail/**********
 * @date 2025/4/2 10:28 上午
 */
public class TencentCloudWBAppConfig {

    /**
     * 配置内容
     * key： cloned
     * val： 具体配置
     */
    private static Map<Integer, Conf> conf;

    /**
     * 获取配置
     *
     * @return
     */
    public static void setConf(String etcdVal) {
        conf = JsonUtils.stringToObject(etcdVal, new TypeReference<Map<Integer, Conf>>() {
        });
    }

    /**
     * 获取配置
     *
     * @return
     */
    public static Conf getConf(Integer cloned) {
        return conf.get(cloned);
    }

    @Data
    public static class Conf {
        /**
         * id
         */
        public String id;

        /**
         * 秘钥
         */
        public String Secret;
    }


}
