package cn.taqu.account.config;

import cn.taqu.account.filter.SimpleHealthyFilter;
import cn.taqu.core.web.filter.CustomRequestFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/12/12 下午4:10
 */
@Configuration
public class FilterConfig {

    @Bean
    CustomRequestFilter customRequestFilter() {
        return new CustomRequestFilter();
    }

    @Bean
    SimpleHealthyFilter healthyFilter() {
        return new SimpleHealthyFilter();
    }
}
