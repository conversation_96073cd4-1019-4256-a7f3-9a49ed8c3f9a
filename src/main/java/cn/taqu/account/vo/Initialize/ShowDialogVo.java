package cn.taqu.account.vo.Initialize;

import lombok.Data;

/**
 * ShowDialog的数据结构
 * 
 * <AUTHOR>
 * 2024年11月12日上午9:25:23
 */
@Data
public class ShowDialogVo{

    /**
     * title 标题，加粗黑色，字体较大，用于显示重要信息；
     */
    private String title;
    
    /**
     * content 内容，普通字色，展示详细信息；
     */
    private String content;
    
    /**
     * confirmText 右边高亮按钮文本，一般是【确定】按钮，传空不展示；
     */
    private String confirmText;
    
    /**
     * confirmSchema 右边按钮的公共跳转，可传空；
     */
    private String confirmSchema;
    
    /**
     * cancelText 左边灰色按钮文本，一般是【取消】按钮，传空不展示；
     */
    private String cancelText;
    
    /**
     * cancelSchema 左边按钮的公共跳转，可传空；
     */
    private String cancelSchema;

    public ShowDialogVo(String title, String content, String confirmText, String confirmSchema, String cancelText,
        String cancelSchema) {
        super();
        this.title = title;
        this.content = content;
        this.confirmText = confirmText;
        this.confirmSchema = confirmSchema;
        this.cancelText = cancelText;
        this.cancelSchema = cancelSchema;
    }
}
