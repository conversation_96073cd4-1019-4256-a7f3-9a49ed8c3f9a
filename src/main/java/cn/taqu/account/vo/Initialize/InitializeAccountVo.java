package cn.taqu.account.vo.Initialize;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 * 2024年11月12日上午9:06:57
 */
@Data
public class InitializeAccountVo {

    /**
     * 业务类型
     */
    private String bizType;
    
    /**
     * 业务标识
     */
    private String bizMark;
    
    /**
     * 业务对象 支持 ShowDialogVo
     */
    private ShowDialogVo showDialog;
    
    
    
    public enum BizType{
        NONE,
        SHOW_DIALOG
    }
    
    public enum BizMark{
        NONE,
        ALIPAY_ACCOUNT_DISABLED
    }
    
    public static InitializeAccountVo buildDefaultVo(){
        InitializeAccountVo initializeAccountVo = new InitializeAccountVo();
        initializeAccountVo.setBizType(BizType.NONE.name());
        initializeAccountVo.setBizMark(BizMark.NONE.name());
        return initializeAccountVo;
    }
    
    public static InitializeAccountVo builAlipayAccountDisabledVo(ShowDialogVo showDialogVo){
        InitializeAccountVo initializeAccountVo = new InitializeAccountVo();
        initializeAccountVo.setBizType(BizType.SHOW_DIALOG.name());
        initializeAccountVo.setBizMark(BizMark.ALIPAY_ACCOUNT_DISABLED.name());
        initializeAccountVo.setShowDialog(showDialogVo);
        return initializeAccountVo;
    }

    
}
