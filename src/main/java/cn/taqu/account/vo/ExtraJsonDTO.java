package cn.taqu.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户系统额外参数【非业务性参数】
 *
 * <AUTHOR>
 * @date 2024/8/13 10:11 上午
 */
@Data
public class ExtraJsonDTO {

    /**
     * 手机品牌
     */
    @JsonProperty("mobile_brand")
    private String mobileBrand;

    /**
     * 手机系统版本
     */
    @JsonProperty("mobile_sys_version")
    private String mobileSysVersion;

    /**
     * 手机型号
     */
    @JsonProperty("mobile_model")
    private String mobileModel;

    /**
     * 网络类型
     */
    @JsonProperty("network_type")
    private String networkType;

    /**
     * 网络服务商，中国移动、中国联通、中国电信
     */
    @JsonProperty("network_service")
    private String networkService;

    /**
     * 性别
     */
    @JsonProperty("sex_type")
    private Integer sexType;

}

