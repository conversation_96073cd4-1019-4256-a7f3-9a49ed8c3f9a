package cn.taqu.account.vo;

public class AliyunSpamPreventionVo {
	private Long id;
	private String uuid;
	private String account_name;
	private String token;
	private String sex_type_title;
	private String style_title;
	private String mobile;
	private String ip;
	private Long create_time;
	private String source_title;
	private String type_title;
	//以下数据来源于阿里风险防控api返回
	private String final_decision_title;//风险等级。0：无风险；1：高风险；2：中风险
	private Integer final_score;//风险得分。0~999：分数越高，风险越低
	private String final_desc;//综合风险描述
	private String event_id;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getAccount_name() {
		return account_name;
	}

	public void setAccount_name(String account_name) {
		this.account_name = account_name;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getSex_type_title() {
		return sex_type_title;
	}

	public void setSex_type_title(String sex_type_title) {
		this.sex_type_title = sex_type_title;
	}

	public String getStyle_title() {
		return style_title;
	}

	public void setStyle_title(String style_title) {
		this.style_title = style_title;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Long getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Long create_time) {
		this.create_time = create_time;
	}

	public String getSource_title() {
		return source_title;
	}

	public void setSource_title(String source_title) {
		this.source_title = source_title;
	}

	public String getType_title() {
		return type_title;
	}

	public void setType_title(String type_title) {
		this.type_title = type_title;
	}

	public String getFinal_decision_title() {
		return final_decision_title;
	}

	public void setFinal_decision_title(String final_decision_title) {
		this.final_decision_title = final_decision_title;
	}

	public Integer getFinal_score() {
		return final_score;
	}

	public void setFinal_score(Integer final_score) {
		this.final_score = final_score;
	}

	public String getFinal_desc() {
		return final_desc;
	}

	public void setFinal_desc(String final_desc) {
		this.final_desc = final_desc;
	}

	public String getEvent_id() {
		return event_id;
	}

	public void setEvent_id(String event_id) {
		this.event_id = event_id;
	}
}
