package cn.taqu.account.vo.life;

import cn.taqu.account.service.AccountsLifeService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 下发给客户端的时候，要排序
 * 
 * <AUTHOR>
 * 2024年11月26日下午2:13:12
 */
@Data
public class AccountsLifeVo {

    /**
     *  0：要求真人认证 ，1：未进行真人比对 2：真人审核中 3：已真人认证 4：真人比对未通过
     */
    public static final Integer REAL_PERSON_REQUIRED = 0;

    public static final Integer TO_BE_COMPARED = 1;

    public static final Integer FACE_COMPARE_AUDITING = 2;

    public static final Integer FACE_COMPARE_PASS = 3;

    public static final Integer FACE_COMPARE_FAIL = 4;

    /**
     * 图片地址（相对）
     */
    private String photoUrl;

    /**
     * 图片描述
     */
    private String remark;
    
    /**
     * 图片宽
     */
    private Integer width;
    
    /**
     * 图片高
     */
    private Integer height;
    
    /**
     * 容器描述
     */
    private String containerText;
    
    /**
     * 0 - 不可编辑；1 - 可编辑
     */
    private Integer updateStatus;
    
    /**
     * 排序字段
     */
    private Integer seq;

    @JsonIgnore
    private Integer status;

    @JsonIgnore
    private Integer verifyStatus;

    private Integer authStatus;

    public Integer getSupportAuth() {
        return AccountsLifeService.REAL_PERSON_SEQ.contains(seq) ? 1 : 0;
    }
    
}
