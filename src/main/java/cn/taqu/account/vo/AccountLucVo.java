package cn.taqu.account.vo;

import lombok.Data;

import java.util.List;

/**
 * luc 用户
 */
@Data
public class AccountLucVo {
    /*
    因需要1000个luc，需批量上传luc资料
    1. 头像（设计批量ai，支持批量上传）
    2. 真人头像+真人认证
    3. 随机生成基础资料（开发需批量生产资料）
    1. 昵称:随机
    2. 年龄：18-55
    3. 星座：随机
    4. 情感状况：单身80%，离异20%
    5. 学历：随机
    6. 职业：随机
    7. 年收入：随机
    8. 身高：165-185
    9. 体重：55kg-80kg
    10. 家乡：随机
     */
    
    /** 用户uuid，传参不需要，接口返回 **/
    private String uuid;
    /** 手机号 **/
    private String mobile;
    /** 账号密码 **/
    private String account_password;
    /** 性别 0:未知; 1:男; 2:女; **/
    private Integer sex_type;
    /** 头像 **/
    private List<String> avatar_list;
    /** 用户昵称 **/
    private String nickname;
    /** 年龄 根据生日计算 接口返回 **/
    private Integer age;
    /** 生日(出生日期)时间戳 **/
    private Long birth;
    /** 星座 根据生日计算 **/
    private String constellation;
    /** 情感状况 **/
    private Integer affectivestatus;
    /** 学历 **/
    private String education_level;
    /** 行业 **/
    private String trade; 
    /** 职业 **/
    private String profession; 
    /** 年收入，0：未填写，1：5万以下，2：5～10万，3：10～20万，4：20～30万， 5：30～50万，6：50～100万，7：100万以上 **/
    private Integer income = 0;//
    /** 身高 **/
    private Integer height; 
    /** 体重 **/
    private Integer weight;
    /** 家乡 格式：省id,市id **/
    private String hometown;

}
