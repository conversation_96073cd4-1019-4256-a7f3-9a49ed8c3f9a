package cn.taqu.account.vo;

import lombok.Data;

import java.util.List;

/**
 * Created by Chenquanan on 2017/4/25.
 * 用于后台创建修改账号信息
 */
@Data
public class AccountVo {
    private String uuid;
    /** 用户昵称 **/
    private String nickname;
    /** 手机号 **/
    private String mobile;
//    /** 邮箱 **/
//    @Deprecated
//    private String email;
    /** 头像 **/
    private List<String> avatar_list;
    /** 账号类型 1:会员; 2:游客 **/
    private Integer account_type;
    /** 账号状态 **/
    private Integer account_status;
    /** 最后登录时间 **/
    private Long last_logintime;
    /** 账号密码 **/
    private String account_password;
    /** 性别 0:未知; 1:男; 2:女; **/
    private Integer sex_type;
    /** 手机号是否验证(绑定) 0:否; 1:是; **/ // 2021.04.30 通过mobile来判断，不通过传参
//    private String is_check_mobile;
    /** 婚恋状况 **/
    private Integer affectivestatus;
    /** 个人简介 **/
    private String personal_profile;
    /** 交友意向 **/
    private Integer dating_intention;
    /** 生日(出生日期)时间戳 **/
    private Long birth;
    /** 性取向 **/
    private Integer sexual;
    /** 所有地 格式：省id,市id **/
    private String baseaddr;
    /** 家乡 格式：省id,市id **/
    private String hometown;
//    @Deprecated
//    /** SM类型 可取值为空、S、M **/
//    private String sm_type;


}
