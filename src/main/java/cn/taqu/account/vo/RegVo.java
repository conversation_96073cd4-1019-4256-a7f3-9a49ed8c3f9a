package cn.taqu.account.vo;

import cn.taqu.account.common.RegisterLoginTypeEnum;

/**
 * 用户注册成功返回数据vo，目前该类直接继承于{@link LoginVo},让注册的vo与登录的vo保持一致,后期需要调整时再做修改
 *
 * <AUTHOR>
 * @ClassName RegVo.java
 * @date 2015年9月30日 下午3:47:11
 */
public class RegVo extends LoginVo {

    /**
     * 设备ID
     */
    private Long member_id;
    /**
     * 注册时间
     */
    private Long create_time;
    /**
     * appcode
     */
    private Integer appcode;

    /**
     * 数美风控SDK获取的deviceId
     */
    private String shumeiDeviceId;

    /**
     *  账户登录注册类型
     */
    private RegisterLoginTypeEnum registerLoginTypeEnumType;


    /**
     * 注册时的城市id，优先取客户端上报，如果客户端没有上报，则解析ip
     */
    private Integer cityId;
    
    /**
     * 是否主动设置了头像
     */
    private Boolean isSetAvatar;


    public Long getMember_id() {
        return member_id;
    }

    public void setMember_id(Long member_id) {
        this.member_id = member_id;
    }

    public Long getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Long create_time) {
        this.create_time = create_time;
    }

    public Integer getAppcode() {
        return appcode;
    }

    public void setAppcode(Integer appcode) {
        this.appcode = appcode;
    }

    @Override
    public String getShumeiDeviceId() {
        return shumeiDeviceId;
    }

    @Override
    public void setShumeiDeviceId(String shumeiDeviceId) {
        this.shumeiDeviceId = shumeiDeviceId;
    }

    public RegisterLoginTypeEnum getRegisterLoginTypeEnumType() {
        return registerLoginTypeEnumType;
    }

    public void setRegisterLoginTypeEnumType(RegisterLoginTypeEnum registerLoginTypeEnumType) {
        this.registerLoginTypeEnumType = registerLoginTypeEnumType;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Boolean getIsSetAvatar() {
        return isSetAvatar;
    }

    public void setIsSetAvatar(Boolean isSetAvatar) {
        this.isSetAvatar = isSetAvatar;
    }
    
    
}
