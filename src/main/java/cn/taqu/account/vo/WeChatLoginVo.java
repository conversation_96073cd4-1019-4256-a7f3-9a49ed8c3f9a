/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.vo;
/**
 * 第三方微信登陆 接口返回对象
 * @author:laikunzhen
 */
public class WeChatLoginVo {
	private String nickname;//昵称
	
	private Integer sex_type;//性别，1为男性，2为女性
	
	private String avatar;//用户头像
	
	private Integer bind_info;//0:绑定(固定 兼容之前客户端使用)
	
	private String union_id;//用户统一标识
	
	private String open_id;//普通用户的标识，对当前开发者帐号唯一
	
	private String uuid;
	private String ticket_id;
	
	public String getNickname() {
		return nickname;
	}
	public void setNickname(String nickname) {
		this.nickname = nickname;
	}
	public Integer getSex_type() {
		return sex_type;
	}
	public void setSex_type(Integer sex_type) {
		this.sex_type = sex_type;
	}
	public String getAvatar() {
		return avatar;
	}
	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}
	public Integer getBind_info() {
		return bind_info;
	}
	public void setBind_info(Integer bind_info) {
		this.bind_info = bind_info;
	}
	public String getUnion_id() {
		return union_id;
	}
	public void setUnion_id(String union_id) {
		this.union_id = union_id;
	}
	public String getOpen_id() {
		return open_id;
	}
	public void setOpen_id(String open_id) {
		this.open_id = open_id;
	}
	public String getUuid() {
		return uuid;
	}
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
	public String getTicket_id() {
		return ticket_id;
	}
	public void setTicket_id(String ticket_id) {
		this.ticket_id = ticket_id;
	}	
}