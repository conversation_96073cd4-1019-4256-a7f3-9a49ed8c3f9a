package cn.taqu.account.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/11/26 下午3:04
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AccountIdealTargetVo {

    /**
     * 客态
     */
    private String idealTarget;

    /**
     * 状态
     */
    private Integer idealTargetStatus;


}
