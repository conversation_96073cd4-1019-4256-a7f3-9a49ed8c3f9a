package cn.taqu.account.vo;

import lombok.Data;

/**
 * 用户推送配置
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class UserPushConfigWarmupVo {

	private Integer is_reviews_on = 1; //设置帖子被回复推送 1-开启 0-关闭
	private Integer is_replies_on = 1; //设置回帖被回复推送 1-开启 0-关闭
	private Integer is_re_replies_on = 1; //设置评论被回复推送 1-开启 0-关闭
	private Integer is_hot_post_on = 1; //设置圈子接收热帖推送 1-接收 0-不接收
	private Integer is_banana_boat_on = 1; //设置社区香蕉船提醒 1-提醒 0-不提醒
	private Integer is_start_show_on = 1; //设置主播开播提醒 1-提醒 0-不提醒
	private Integer is_stranger_on = 1; //设置陌生人打招呼推送 1-开启 0-关闭
	private Integer is_scheduled = 0; //是否开启免打扰设置 1-是 0-否
	private String schedule_from = "23:00"; //免打扰开始时间
	private String schedule_to = "07:00"; //免打扰结束时间
	private Integer is_letter_on = 1;//设置打开/关闭私信开关 1-开启 0-关闭

	private Long account_id;
	private Long create_time;
	private Long update_time;

	private Integer type; //1:帖子2:回帖3:评论
	private Long forum_id; // 帖子id,回帖id ,评论ID
	private String content_uuid; //内容uuid，上面类型的某个一个
	private Integer status = 1; // 设置是否接收推送1为接收 0为关闭

}
