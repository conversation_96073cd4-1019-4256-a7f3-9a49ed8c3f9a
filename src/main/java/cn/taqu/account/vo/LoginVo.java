package cn.taqu.account.vo;

import java.util.List;

import cn.taqu.account.constant.CommConst;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * @ClassName LoginVo.java
 * @Description 通过账号登录成功后返回的账号信息
 * <AUTHOR>
 * @date 2015年9月29日 上午11:06:36
 */
@Getter
@Setter
public class LoginVo {

	private String nickname;//昵称
	private String username;//用户名
	private Integer is_bind_mobile;//是否绑定手机;1:是;0:否;
	private Integer is_check_mobile;//是否校验手机号码[1:是，0:否]
	@Deprecated
	private Long tqcoin;//趣币数量
	@Deprecated
	private Long experience;//用户经验值
	private Integer account_actor;//用户特殊头衔0没有特殊头衔
	private String medal_url;//等级勋章图片
	@Deprecated
	private Integer account_level;//账号等级
	private String honor_name;//账号等级对应的头衔名称
	private Long account_id;//账号id
	private String ticket_id;//登录凭证
	private Integer user_type;//是否女神
	private Integer account_type;//账号类型
	private Integer sex_type;//性别1:男;2:女;其他:未知;
	private Integer age = 0;//年龄,由birth计算出
	private String avatar;//头像图片地址
	@Deprecated
	private String email;//邮箱
	private Integer account_status;//账号状态
	private String mobile;//手机号
	private String uuid;//唯一标识
	private Integer bind_info;//0未绑定
	private Integer group_type;// 组类型（1小编,2版主）
	private List<Long> category_ids;// 圈子ID
	private String old_ticket;// 旧的ticket
	@Deprecated
	private String driver_level;//驾照等级
	@Deprecated
	private Long kilometer;//驾照公里数
    private String deviceToken;
    private String shumeiDeviceId;//数美风险检测标识符
    private Integer aliyunFaceCertification;// 活体认证 1:认证；0:未认证
    private Integer profileVerifyStatus;// 头像认证状态，0-未认证，1-认证成功，2-认证失败
	private Long register_time; //注册时间
	private Integer isDoBindMobile = CommConst.NO_0;	//是否做了绑定手机的操作  1是0否 默认0否
	// 2021.04.28 封号实名认证 http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
	private Integer toCertification = CommConst.NO_0;   // 是否要实名认证  1是0否 默认0否
	private String dialogContent;   // 弹出框文本
	private String waSayToBindMobile; // 网安喊你绑定手机
	private Integer toRealPersonCertification = CommConst.NO_0; // 去绑定真人认证
	private String toRealPersonCertificationDialog;// 去真人认证 文案
	/**
	 * openID
	 */
	private String openId;

	/**
	 * unionId
	 */
	private String unionId;

	/**
	 * 注销冷静期状态：0-正常状态  1-注销冷静期状态
	 */
	private Integer freeze_flag = CommConst.NO_0;

	/**
	 * 风险用户状态：0-正常状态 1-低风险用户状态 2-高风险用户状态
	 */
	private Integer risk_status = CommConst.NO_0;

}
