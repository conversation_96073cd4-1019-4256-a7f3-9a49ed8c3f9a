package cn.taqu.account.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/3 下午5:16
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class IntroductionSampleVo {

    private Long sampleId;

    private String passIntroductionContent;

    private String accostCount;

    private List<?> passIntroductionImgs;

}
