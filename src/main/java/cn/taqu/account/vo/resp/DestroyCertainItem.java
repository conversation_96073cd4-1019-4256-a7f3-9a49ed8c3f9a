package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 注销确认信息
 *
 * <AUTHOR>
 * @date 2025/4/14 11:42
 */
@Data
public class DestroyCertainItem {

    private String title;

    /**
     * 内容，用数组是为了兼容分段
     */
    private List<String> content;

    @JsonProperty("btn_text")
    private String btnText;

    /**
     * 弹窗信息
     */
    private DestroyPop pop;

    @Data
    public static class DestroyPop {

        /**
         * 弹窗标题
         */
        @JsonProperty("pop_title")
        private String popTitle;

        /**
         * 弹窗内容
         */
        @JsonProperty("pop_content")
        private String popContent;

        @JsonProperty("pop_submit_btn")
        private Light popSubmitIcon;

        @JsonProperty("pop_cancel_btn")
        private Light popCancelIcon;
    }

    /**
     * 高亮按钮
     */
    @Data
    public static class Light {

        @JsonProperty("is_light")
        private Integer isLight;

        private String text;
    }
}
