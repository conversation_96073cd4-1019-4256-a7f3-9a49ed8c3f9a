package cn.taqu.account.vo.resp;

import lombok.Data;

/**
 * 标签分页
 *
 * <AUTHOR>
 * @date 2024/11/25 16:02
 */
@Data
public class LabelPageResp {

    /**
     * 标签id
     */
    private Long id;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签icon
     */
    private String labelIcon;

    /**
     * 标签状态
     */
    private Integer status;

    /**
     * 标签排序
     */
    private Integer labelSort;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 时间
     */
    private Long updateTime;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 说明
     */
    private String introduce;
}
