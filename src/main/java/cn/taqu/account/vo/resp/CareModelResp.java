package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 关怀模式
 *
 * <AUTHOR>
 * @date 2025/6/26 10:36
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CareModelResp {

    /**
     * 关怀模式：0-否，1-是
     */
    private Integer careModel;

    /**
     * 是否实名
     */
    private Integer isReal;

    /**
     * 生日时间戳
     */
    private Long birth;
}
