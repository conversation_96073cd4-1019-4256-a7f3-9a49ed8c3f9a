package cn.taqu.account.vo.resp;

import lombok.Data;

import java.util.List;

/**
 * 我的标签分类【客户端】
 *
 * <AUTHOR>
 * @date 2024/11/25 11:46
 */
@Data
public class MyCategoryLabelResp {

    /**
     * 分类项
     */
    private List<CategoryItem> list;

    /**
     * 我已选择的标签项
     */
    private List<LabelItem> mySelected;

    /**
     * 限制条数
     */
    private Integer limit;

    /**
     * 分类项
     */
    @Data
    public static class CategoryItem {

        /**
         * 分类id
         */
        private Long categoryId;

        /**
         * 分类名称
         */
        private String categoryName;
    }

    /**
     * 标签项
     */
    @Data
    public static class LabelItem {

        /**
         * 标签id
         */
        private Long labelId;

        /**
         * 标签名称
         */
        private String labelName;

        /**
         * 标签icon
         */
        private String labelIcon;
    }

}
