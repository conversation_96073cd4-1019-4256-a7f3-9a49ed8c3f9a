package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 获取他人标签
 *
 * <AUTHOR>
 * @date 2024/12/2 16:26
 */
@Data
public class GetTargetLabelResp {

    /**
     * 共同类目
     */
    @JsonInclude
    private List<String> sameCategory;

    /**
     * 他的标签
     */
    @JsonInclude
    private List<Label> labels;

    /**
     * 他的标签信息
     */
    @Data
    public static class Label {

        /**
         * 标签名称
         */
        private String labelName;

        /**
         * 是否高亮
         */
        private Integer isHighlight;
    }

}
