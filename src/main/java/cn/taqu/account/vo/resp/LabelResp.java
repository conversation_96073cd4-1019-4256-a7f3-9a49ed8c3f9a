package cn.taqu.account.vo.resp;

import lombok.Data;

/**
 * 标签分类
 *
 * <AUTHOR>
 * @date 2024/11/25 11:46
 */
@Data
public class LabelResp {

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签icon
     */
    private String labelIcon;

    /**
     * 排序
     */
    private Integer labelSort;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 说明
     */
    private String introduce;

}
