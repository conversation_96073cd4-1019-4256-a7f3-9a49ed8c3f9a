package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 获取注销接口vo
 *
 * <AUTHOR>
 * @date 2025/4/7 15:03
 */
@Data
public class DestroyInfoResp {

    /**
     * 是否绑定手机，0-否，1-是。为0时，走获取协议流程，反之走新流程（跳转“注销账号”页）
     */
    @JsonProperty(value = "is_bind_mobile")
    private String isBindMobile;

    /**
     * 绑定的手机号
     */
    private String mobile;

    /**
     * 注销方式，0-直接注销，1-短信注销
     */
    @JsonProperty(value = "destroy_process_type")
    private String destroyProcessType;

    private String confirmTip1;

    private String confirmTip2;

    /**
     * 提示内容
     */
    private TipContent info;

    /**
     * 提示内容
     */
    @Data
    public static class TipContent {

        private String title = "亲爱的用户，感谢你为平台带来的美好！";

        @JsonProperty(value = "sub_title")
        private String subTitle = "请注意，账号注销后，账号信息将被删除且无法恢复";

        /**
         * `
         * 注销方式，0-直接注销，1-短信注销
         */
        @JsonProperty(value = "sub_title_light")
        private String subTitleLight = "将被删除且无法恢复";

        /**
         * 关注数
         */
        private String follow;

        /**
         * 粉丝数
         */
        private String fans;

        /**
         * h5跳转地址
         */
        private String relation;
    }

}
