package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 标签分类
 *
 * <AUTHOR>
 * @date 2024/11/25 11:46
 */
@Data
public class LabelCategoryResp {

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类排序
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer categorySort;

    /**
     * 分类状态
     */
    private Integer categoryStatus;
}
