package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 头像指导弹窗
 *
 * <AUTHOR>
 * @date 2025/6/23 13:51
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AvatarGuilePopupResp {

    /**
     * 是否实验组
     */
    private Integer isExp;

    /**
     * 是否弹窗
     */
    private Integer isPop;

    /**
     * 弹窗类型：scene
     */
    private Integer type;

    private String avatar;

    private String titleBg;

    private String subTitle;

    private String titleDarkBg;

    /**
     * 默认美颜方案
     */
    private String defaultBeauty;

}
