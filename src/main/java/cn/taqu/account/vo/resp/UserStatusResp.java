package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 用户状态resp
 *
 * <AUTHOR>
 * @date 2024/10/14 10:52 上午
 */
@Data
public class UserStatusResp {

    /**
     * 用户uuid
     */
    private String uuid;

    /**
     * 账号状态
     */
    @JsonProperty("account_status")
    private Integer accountStatus;

    /**
     * 注销时间
     */
    @JsonProperty("destroy_time")
    private Long destroyTime;

}
