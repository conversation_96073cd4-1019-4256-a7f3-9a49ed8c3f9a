package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 注册行为
 *
 * <AUTHOR>
 * @date 2024/11/15 17:57
 */
@Data
public class RegisterActionInfoResp {

    @JsonProperty("account_uuid")
    private String accountUuid;

    @JsonProperty("platform_id")
    private Integer platformId;

    @JsonProperty("app_version")
    private Long appVersion;

    @JsonProperty("create_time")
    private Long createTime;

}
