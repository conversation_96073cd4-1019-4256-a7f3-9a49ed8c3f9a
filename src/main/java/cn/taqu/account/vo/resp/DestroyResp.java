package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 注销用户返回对象
 *
 * <AUTHOR>
 * @date 2025/02/14 10:52 上午
 */
@Data
public class DestroyResp {

    /**
     * 用户uuid
     */
    @JsonProperty("account_uuid")
    private String accountUuid;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 昵称
     */
    private String nickname;

}
