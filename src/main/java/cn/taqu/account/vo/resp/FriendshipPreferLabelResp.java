package cn.taqu.account.vo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 交友偏好标签
 *
 * @desc 用于返回给客户端交友偏好标签选择页场景：包含所有标签 + 已选择标签
 * <AUTHOR>
 * @date 2024/10/15 10:21 上午
 */
@Data
public class FriendshipPreferLabelResp {

    /**
     * 交友偏好标题
     */
    private String title;

    /**
     * 交友偏好子标题
     */
    @JsonProperty("sub_title")
    private String subTitle;

    /**
     * 所有偏好标签
     */
    @JsonProperty("prefer_tab_all")
    private List<LabelInfo> preferTabAll;

    /**
     * 历史已设置的偏好标签（可能为空，用来回显）
     */
    @JsonProperty("prefer_tab_history")
    private List<LabelInfo> selectedPreferTab;

    /**
     * 标签选中上限
     */
    @JsonProperty("upper_limit")
    private Integer upperLimit;

    /**
     * 标签选中下限
     */
    @JsonProperty("lower_limit")
    private Integer lowerLimit;

    /**
     * 标签对象
     */
    @Data
    public static class LabelInfo {
        /**
         * id标识
         */
        @JsonProperty("text_id")
        private Long id;

        /**
         * 标签内容
         */
        private String text;

        /**
         * 排序
         */
        private Integer sort;
    }

}
