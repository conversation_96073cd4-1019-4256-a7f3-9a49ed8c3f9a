package cn.taqu.account.vo;

import cn.taqu.account.service.AccountsService;

/**
 * 根据登录凭证ticket获取到的登录用户信息
 * @ClassName GetByTicketVo.java
 * <AUTHOR>
 * @date 2015年9月22日 下午4:29:05
 * @see AccountsService
 */
public class GetByTicketVo {
	
	//主键
	private Long account_id;
	//uuid的别名
	private String account_uuid;
	//用户账号uuid
	private String uuid;
	//账户状态，暂时无用，预留字段
	private Integer account_status;
	//类型，1:注册用户; 2:匿名用户
	private Integer account_type;
	
	public Long getAccount_id() {
		return account_id;
	}
	public void setAccount_id(Long account_id) {
		this.account_id = account_id;
	}
	public String getAccount_uuid() {
		return account_uuid;
	}
	public void setAccount_uuid(String account_uuid) {
		this.account_uuid = account_uuid;
	}
	public Integer getAccount_status() {
		return account_status;
	}
	public void setAccount_status(Integer account_status) {
		this.account_status = account_status;
	}
	public Integer getAccount_type() {
		return account_type;
	}
	public void setAccount_type(Integer account_type) {
		this.account_type = account_type;
	}
	public String getUuid() {
		return uuid;
	}
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
}
