package cn.taqu.account.vo;

import java.util.Collection;

/**
 * PHP后台使用的公共分页，参考cn.taqu.web.utils.PageInfo.
 * 如果php后台改为java后台，代码改动小。
 * <AUTHOR>
 * 2017年4月20日 上午9:26:41
 */
public class CommonPage<T> {
    
    public static Long TOTAL_DEFAULT_MAX = 99999999L;
    
    private Long total; // 总记录 
    private Collection<T> rows; //显示的记录  
    private Integer nowpage; // 当前页
    private Integer pagesize; // 每页显示的记录数 

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
	}

	public Collection<T> getRows() {
		return rows;
	}

	public void setRows(Collection<T> rows) {
		this.rows = rows;
	}

	public Integer getNowpage() {
		return nowpage;
	}

	public void setNowpage(Integer nowpage) {
		this.nowpage = nowpage;
	}

	public Integer getPagesize() {
		return pagesize;
	}

	public void setPagesize(Integer pagesize) {
		this.pagesize = pagesize;
	}
}
