package cn.taqu.account.vo;

/**
 *
 * @ClassName AccountProfileVo.java
 * @Description 根据accountId获取用户数据(主要用户登录返回)的vo
 * <AUTHOR>
 * @date 2015年9月29日 上午11:43:57
 */
public class AccountProfileVo {

	private Integer is_check_mobile;//是否[1:是，0:否]
	@Deprecated
	private Long tqcoin;//趣币数量
	@Deprecated
	private Long experience;//用户经验值
	private Integer account_actor;//用户特殊头衔0没有特殊头衔
	private String medal_url;//等级勋章图片
	@Deprecated
	private Integer account_level;//账号等级
	private Long min_level_score;//账号等级对应的最低分
	private Long max_level_score;//账号等级对应的最高分
	private String honor_name;//账号等级对应的头衔名称
	@Deprecated
	private String driver_level;//驾照等级
	@Deprecated
	private Long kilometer;//驾照公里数

	public Integer getIs_check_mobile() {
		return is_check_mobile;
	}
	public void setIs_check_mobile(Integer is_check_mobile) {
		this.is_check_mobile = is_check_mobile;
	}
	public Long getTqcoin() {
		return tqcoin;
	}
	public void setTqcoin(Long tqcoin) {
		this.tqcoin = tqcoin;
	}
	public Long getExperience() {
		return experience;
	}
	public void setExperience(Long experience) {
		this.experience = experience;
	}
	public Integer getAccount_actor() {
		return account_actor;
	}
	public void setAccount_actor(Integer account_actor) {
		this.account_actor = account_actor;
	}
	public String getMedal_url() {
		return medal_url;
	}
	public void setMedal_url(String medal_url) {
		this.medal_url = medal_url;
	}
	public Integer getAccount_level() {
		return account_level;
	}
	public void setAccount_level(Integer account_level) {
		this.account_level = account_level;
	}
	public Long getMin_level_score() {
		return min_level_score;
	}
	public void setMin_level_score(Long min_level_score) {
		this.min_level_score = min_level_score;
	}
	public Long getMax_level_score() {
		return max_level_score;
	}
	public void setMax_level_score(Long max_level_score) {
		this.max_level_score = max_level_score;
	}
	public String getHonor_name() {
		return honor_name;
	}
	public void setHonor_name(String honor_name) {
		this.honor_name = honor_name;
	}
	public String getDriver_level() {
		return driver_level;
	}
	public void setDriver_level(String driver_level) {
		this.driver_level = driver_level;
	}
	public Long getKilometer() {
		return kilometer;
	}
	public void setKilometer(Long kilometer) {
		this.kilometer = kilometer;
	}

}
