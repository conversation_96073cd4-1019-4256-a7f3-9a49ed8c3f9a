package cn.taqu.account.vo;

import cn.taqu.account.vo.life.AccountsLifeVo;
import cn.taqu.account.vo.resp.HomepageLabelResp;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
<<<<<<< HEAD
 * 接口返回vo 客态
 *
=======
 * 接口返回vo
 *
>>>>>>> branch 'feature/20241120_F4975037348_私信_Profile优化' of https://git.internal.taqu.cn/java/j2.git
 * <AUTHOR>
 * 2024年11月26日下午2:13:12
 */
@Data
public class GetHomepageInfoV3Vo {

    // 用户状态
    private String account_status;
    // 他趣id
    private String default_card_id;
    // 头像
    private String avatar;
//    private String avatar_status;

//    private String profile_error_msg;
    private String real_avatar_certification;

    // 语音签名
    private Map<String, Object> voice_sign_info;

    // 基础资料
    private String nickname;
    private String sex_type;
    private String birth;
    private String age;
    private String constellation;
    private String height;
    private String weight;
    private String baseaddr;
    private String hometown;
    private String affectivestatus;
    private String trade;
    private String profession;
    private String education;
    private String income;

    // 资料完整度
//    private String info_percent;

    // 自我介绍
    private String pass_introduction_status;
    private String pass_introduction_content;
    private List<JSONObject> pass_introduction_imgs;

    // 认证相关字段
    private String profile_verify_status;
    private String is_bind_mobile;
    private String face_certification;
    private String zhima_certification;
    private String is_bind_mobile_desc;
    private String face_certification_desc;
    private String identity_no_desc;

    // 我的理想型

    // 标签

    // 个性签名
    private String personal_profile;
//    private String personal_profile_status;
//    private String personal_profile_permission;
//    private String personal_profile_interest_score;

    // 我的生活
    private List<AccountsLifeVo> accounts_life_list;

    // 其他
//    private String register_avatar_status;
//    private String default_avatar_status;

    private List<String> obtain_achievement;
    private String account_actor;

//    private String dating_intention;
    private String distance;
    private String distance_unit;
    private String location;
    private String medal_name;
    private String avatar_dress_id;

    private String school_name;

    private String major;

    private Integer major_status;

    private String ideal_target;

    private Integer ideal_target_status;

    private Integer is_new_account;

    /**
     * 背景id
     */
    private Integer background_id;

    /**
     * 背景url
     */
    private String background_url;

    /**
     * 相同标签分类
     */
    @JsonInclude
    private List<String> same_category;

    /**
     * 标签列表
     */
    @JsonInclude
    private List<HomepageLabelResp> labels;


    private String c_city;

    private String age_doubt;

    private String age_doubt_msg;

}
