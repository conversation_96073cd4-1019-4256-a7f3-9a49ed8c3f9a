package cn.taqu.account.common;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/5/22 上午10:09
 */
@Getter
@RequiredArgsConstructor
public enum AllureSceneEnum {

    /**
     * 头像颜值
     */
    AVATAR(1, 2),

    /**
     * 自我介绍真诚度
     */
    INTRODUCTION(2, 4),

    /**
     * 资料卡完成度
     */
    CARD(3, 4);

    private final Integer scene;

    private final Integer factor;

    public static AllureSceneEnum valOf(Integer scene) {
        return Stream.of(AllureSceneEnum.values()).filter(e -> scene.equals(e.getScene()))
            .findAny().orElseThrow(IllegalArgumentException::new);
    }
}
