package cn.taqu.account.common;

/**
 * <AUTHOR>
 * @Classname RiskDetectRiskLevelEnum
 * @Description 风险级别
 * @Date 2021/1/28 上午10:21
 */
public enum RiskDetectRiskLevelEnum {
    /**
     * 正常内容，建议直接放行
     */
    PASS,
    /**
     * 可疑内容，建议人工审核
     */
    REVIEW,
    /**
     * 违规内容，建议直接拦截
     */
    REJECT,

    /**
     * 拒绝(新版检测中台返回的状态)
     */
    BLOCK,

    /**
     * 机审通过，等待人审（2024/1/11新增，需求：https://o15vj1m4ie.feishu.cn/wiki/Mfigwy61YiMl0VkmsXYctDpcnQG）
     */
    WAIT
}
