package cn.taqu.account.common;


import java.util.Locale;

public enum AliyunTextStatusEnum {

    //拒绝
    BLOCK(0),
    //通过
    PASS(1),
    //审核
    REVIEW(2),
    //机审通过
    WAIT(3);

    private Integer status;

    AliyunTextStatusEnum(Integer status){
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public static Integer getStatusByName(String name){
        for(AliyunTextStatusEnum statusEnum : AliyunTextStatusEnum.values()){
            if(statusEnum.name().equals(name.toUpperCase(Locale.ROOT))){
                return statusEnum.status;
            }
        }
        return AliyunTextStatusEnum.PASS.status;
    }
}
