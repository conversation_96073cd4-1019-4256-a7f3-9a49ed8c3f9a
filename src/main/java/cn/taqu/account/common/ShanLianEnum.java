package cn.taqu.account.common;

import cn.taqu.account.utils.ShowDialogUtil;
import cn.taqu.core.protocol.SoaBaseParams;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Wu.D.J
 */
public enum ShanLianEnum {

    C_60(60, "闪恋",
            "TIDAgnFt", "b3Bbky9vvG47VIsCg85uUXN7dTZh2OWWZZrjkdkGnO5k3c8AA2hW1wfy7bwuOa3y",
            "IDAZgMMG", "ji7CpJcptPZWXLUGY378cK1OrqiYfcG2s16Lp7qoSUAO80f4VvEkoe8nhnKGKdMi"),
    C_61(61, "脱单",
            "TIDAcmYn", "c2CLkQe0BnAbgWLKjodERtWpYc4C4OtNY0IZ1UGqvyiGyfmwp6w0CDI3Uo7ZAY0J",
            "IDAaPSE5", "RGbpxygVqF5ni25Ttw86BfPKEdTBZIU87mhGdrVjiVrHocb9ZVOGvbMSwJDdDSsv"),
    C_62(62, "同城相悦",
            "TIDAYBCc", "JPEN0IZRT6oEhIdSD2MojbEZGaUT2Vy1UIhS6LLWuGCrElaIbXnuQwFWS7IMdN1g",
            "IDA2xRlA", "8WKqNgHhibwSVaIBZmMkwZb1yx54ZSGcKLCIZMDqXYmTeK0Lx57G3TdxKZz5lwMW"),
    C_63(63, "风月",
            "TIDAxr2k", "tIqip2G89BVe47ZKHC3Qh2N9RvNXGvzgq5dthFKGjubuJI2cMZzqvCvE360MJEx1",
            "IDApUrlk", "2S6b2091CSjKbQinfdhDLCW72fAj38kdYXfjrey64SlL8OyV0GMLMUKASm6kywCE"),
    C_64(64, "寻欢",
            "TIDAZyCH", "O4azKzB7dq3TxIqFO6F7MPGzt7W1V7EiVCAyCITHaan9R3QlBdHNVuVD2yJWPthR",
            "IDA2OpcS", "RdkQeZFx1HN6dPOf3UTpynJvAm7LORxWYTMkJFGJ9Jjd9mkZSO5wSNkVGHUjmlJF"),
    C_65(65, "初见",
            "TIDA7VYN", "UbdvYfgEVc5IUmIYFS1JB92sKKAkMe6tBFnEso9O0pDlEqnrCsNJJk1UrYUqKsYq",
            "IDAza3Ap", "X4D4nBVOl7dUKJzAyHztngDiNyfFhuJSz3fQhLltmWYwJtNZ2cpwTrrXCKImx5ch"),
    C_66(66, "红豆",
            "TIDA5Ffy", "7y4npKc5HvSuqC7PiaiFEOH3pU154poz6ogbGeMKpUvuVkOxwkKsgSeYaCywD77d",
            "IDA1USer", "Ko68vVUBqyk6hGSdEAMHJD4I8dt04ES2D7Jr01nAKS0te46WxpuKXkWoZu24tvYK"),
    C_67(67, "觅友",
            "TIDAYqvi", "CfZAP0nZhGvEiIjMTZPH1t7YoJbmboTg4dchyiIxDvo27HNLS4iT6wlc6dktFYja",
            "IDAVBrIK", "7y85MeyrX9wyHplFXheEGnltcrd05QdGBbdiyX7ZbAiACXQsaYBSIWHMKUFhtOeH"),
    C_68(68, "他趣交友",
            "TIDAdkFK", "NtXYo3pQkNxAs3rjPxl24Xjs1JVSn0BJLUMTdkLZ1odjo6dWWvU4YPdjDnwUvmsG",
            "IDAny0MJ", "kblE4QNOYOSaQTWN5cz1A3lDNesZpm6N8YKmQ1x95gIcWOgUpojF1D8BVkRfLvSU"),
    C_69(69, "微聊",
            "TIDADeRB", "hZZcvqseoDn3Dj4eg0JFClagYiRozxfVwrIOuZz9KrwXaOjnLcQ9BKJqbwlSysra",
            "IDAntTQE", "DnV5ozeYjOejcCCegHwrXIEp9f4bBN39q3CS8vc9khUZFH4OcVsJ0GbBMJQDachI"),
    C_70(70, "相悦",
            "TIDAKZNN", "XFTPYwVEnfVPlelYV6QQZX6kLuACyuRDivIqF4vVStMIKTkO1r3k2w1vJH3XA1IA",
            "IDAZvLDV", "zBZeRFLYnkMRWtHjI3iPyro1MWoYVZzEyy7xKAM2OaJSqElwpzO1wHjMdbxXiUXY"),
    /**
     * 从这期开始配置迁移到etcd
     * {@link cn.taqu.account.config.biz.TencentCloudWBAppConfig}
     */
    C_80(80, "闪糖",
            "", "",
            "", ""),
    ;

    private Integer cloned;

    private String appName;

    private String tx_appid_test;
    private String tx_secret_test;

    private String tx_appid_online;
    private String tx_secret_online;

    ShanLianEnum(Integer cloned, String appName, String tx_appid_test, String tx_secret_test, String tx_appid_online, String tx_secret_online) {
        this.cloned = cloned;
        this.appName = appName;
        this.tx_appid_test = tx_appid_test;
        this.tx_secret_test = tx_secret_test;
        this.tx_appid_online = tx_appid_online;
        this.tx_secret_online = tx_secret_online;
    }

    public Integer getCloned() {
        return cloned;
    }

    public String getAppName() {
        return appName;
    }

    public String getTx_appid_test() {
        // 判断是否强制升级版本
        restrictVersionUpdate();

        return isSpecificVersion() ? tx_appid_online : tx_appid_test;
    }

    public String getTx_secret_test() {
        // 判断是否强制升级版本
        restrictVersionUpdate();

        return isSpecificVersion() ? tx_secret_online : tx_secret_test;
    }

    public String getTx_appid_online() {
        // 判断是否强制升级版本
        restrictVersionUpdate();

        return isSpecificVersion() ? tx_appid_test : tx_appid_online;
    }

    public String getTx_secret_online() {
        // 判断是否强制升级版本
        restrictVersionUpdate();

        return isSpecificVersion() ? tx_secret_test : tx_secret_online;
    }

    private static List<String> specificVersions = Arrays.asList("100", "101", "102", "103", "104", "105");

    private static Integer isRestrictUpdate = 0;

    private static String updateTips = "功能已升级，请更新到最新版本";

    public static void initUpdateConfig(String str) {
        isRestrictUpdate = Integer.parseInt(str);
    }

    private static boolean isSpecificVersion() {
        Integer currentCloned = SoaBaseParams.fromThread().getCloned();
        if (currentCloned == 61 || currentCloned == 62) {
            // 获取闪恋版本号，判断是否是指定需要调整配置的版本，指定的这边版本，线上读测试配置，测试读线上配置
            String userAgent = SoaBaseParams.fromThread().getUserAgent();
            return StringUtils.isNotBlank(userAgent) && specificVersions.contains(userAgent);
        }
        return false;
    }

    private static void restrictVersionUpdate() {
        if (isRestrictUpdate == 1 && isSpecificVersion()) {
            ShowDialogUtil.throwShowDialog(updateTips, "知道了", "", "咨询客服", "m=me&a=service");
        }
    }

    public static boolean isShanLianApp() {
        return isShanLianApp(SoaBaseParams.fromThread().getCloned());
    }

    public static boolean isShanLianApp(Integer cloned) {
        return cloned >= 60 && cloned <= 90;
    }

    public static ShanLianEnum getInstance() {
        return getInstance(SoaBaseParams.fromThread().getCloned());
    }

    public static ShanLianEnum getInstance(Integer cloned) {
        for (ShanLianEnum value : values()) {
            if (value.cloned.equals(cloned)) {
                return value;
            }
        }
        return null;
    }

    public static String tipTitle(Integer cloned) {
        for (ShanLianEnum value : values()) {
            if (value.cloned.equals(cloned)) {
                return value.appName + "小秘书";
            }
        }
        return "他趣小秘书";
    }

    /**
     * 女包cloned list
     * cloned: "63", "64", "65", "67"
     * @return
     */
    public static List<String> getNvBaoCloneList(){
        List<String> clonedList= Lists.newArrayList();
        clonedList.add(String.valueOf(C_63.cloned));
        clonedList.add(String.valueOf(C_64.cloned));
        clonedList.add(String.valueOf(C_65.cloned));
        clonedList.add(String.valueOf(C_67.cloned));
        return clonedList;
    }
}
