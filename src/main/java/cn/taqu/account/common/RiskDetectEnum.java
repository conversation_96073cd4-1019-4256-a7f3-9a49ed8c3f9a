package cn.taqu.account.common;

/**
 * <AUTHOR> Wu.D.J
 */
public enum RiskDetectEnum {

    // 文本检测 - 自我介绍
    TEXT_INTRODUCTION,

    // 文本检测 - 个人简介
    TEXT_PERSONAL_PROFILE,

    // 文本检测 - 用户昵称
    TEXT_NICKNAME,

    // 语音检测 - 语音签名
    AUDIO_SIGN,

    // 图片检测 - 头像
    IMAGE_AVATAR,

    // 图片检测 - 个人封面
    IMAGE_COVER,

    // 照片机审
    PHOTO,

    // 真人认证头像
    REAL_PERSON_AVATAR,

    // 非真人认证头像
    NOT_REAL_PERSON_AVATAR,

    // 真人认证相册
    REAL_PERSON_COVER,

    // 非真人认证相册
    NOT_REAL_PERSON_COVER,

    // 文本检测 - 我的学校 - 专业
    TEXT_MAJOR,

    // 文本检测 - 理想型
    TEXT_IDEAL,
    
    // 文本检测 + 图片 - 我的生活
    ACCOUNTS_LIFE,

    ACCOUNTS_LIFE_REAL_PERSON

    ;
}
