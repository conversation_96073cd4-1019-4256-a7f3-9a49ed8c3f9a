package cn.taqu.account.common;

/**
 * 推荐系统服务端埋点
 *
 * <AUTHOR>
 * @date 2021/03/06
 */
public enum RecsysReportServerEnum {
    /**
     * （推荐过滤-公共） 违规昵称变化 
     */
    NICKNAME_UPDATE("nickname_update"),
    /**
     * （推荐过滤-公共）头像为默认头像和违规头像的用户 
     */
    AVATAR_UPDATE("avatar_update"),
    /**
     * （推荐过滤-公共）用户注销    
     */
    ACCOUNT_DESTROY("account_destroy"),
    /**
     * （推荐过滤-公共） (非活体)或(有活体+无真人） 传1过滤   
     */
    REAL_AVATAR_UPDATE("real_avatar_update"),
    ;

    private String value;

    RecsysReportServerEnum(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }

}
