package cn.taqu.account.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-08 18:24
 */
public enum ChangeAvatarTypeEnum {

    // 1.无-》默认  2.无=》有   3.默认=》有 4.有=》有
    NONE_TO_DEFAULT(1),
    NONE_TO_HAVE(2),
    DEFAULT_TO_HAVE(3),
    HAVE_TO_HAVE(4),
    OTHER(5);

    private Integer value;

    ChangeAvatarTypeEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
