package cn.taqu.account.common;


/**
 * 收入枚举
 */
public enum IncomeEnum {

    /**
     *
     */
    UN_FILL("未填写",0),
    LESS_THAN_FIVE("5万以下",1),
    FIVE_TO_TEN("5-10万",2),
    TEN_TO_TWENTY("10-20万",3),
    TWENTY_TO_THIRTY("20-30万",4),
    THIRTY_TO_FIFTY("30-50万",5),
    FIFTY_TO_ONE_HUNDRED("50-100万",6),
    OVER_ONE_HUNDRED("100万以上",7);

    private String name;
    private Integer code;

    IncomeEnum(String name,Integer code){
        this.name = name;
        this.code = code;
    }

    public static String getNameByCode(Integer code){
        if(null == code){
            return "";
        }

        for(IncomeEnum incomeEnum : IncomeEnum.values()){
            if(incomeEnum.code.equals(code)){
                return incomeEnum.name;
            }
        }
        return "";
    }




}
