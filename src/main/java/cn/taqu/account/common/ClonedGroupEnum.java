package cn.taqu.account.common;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
public enum ClonedGroupEnum {
    /**
     * 主包
     */
    MAIN_GROUP(Arrays.asList(1, 7, 44, 45, 47, 50, 51, 60, 61, 62, 68)),
    /**
     * 女包
     */
    GIRL_GROUP(Arrays.asList(4, 63, 64, 65, 67)),
    /**
     * 年轻人包
     */
    YOUNG_GROUP(Arrays.asList(80)),

    ;

    private List<Integer> clonedList;

    ClonedGroupEnum(List<Integer> clonedList) {
        this.clonedList = clonedList;
    }

    public List<Integer> getClonedList() {
        return clonedList;
    }

    public boolean contains(Integer cloned) {
        return clonedList.contains(cloned);
    }

    public static ClonedGroupEnum getClonedGroup(Integer cloned) {
        if (cloned == null) {
            return null;
        }
        for (ClonedGroupEnum clonedGroupEnum : ClonedGroupEnum.values()) {
            if (clonedGroupEnum.contains(cloned)) {
                return clonedGroupEnum;
            }
        }

        log.warn("cloned {} not in any group, default as MAIN_GROUP", cloned);
        return MAIN_GROUP;
    }

}
