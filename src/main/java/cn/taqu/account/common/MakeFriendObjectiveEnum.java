package cn.taqu.account.common;

import cn.hutool.core.util.RandomUtil;
import cn.taqu.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Wu.<PERSON>.J
 */
@Deprecated
@Slf4j
public enum MakeFriendObjectiveEnum {

    FIRST_LOVE(1, "先谈恋爱", "first_love_pop.png", "first_love_page.png"),
    ACCEPT_DATE(2, "接受约会", "accept_date_pop.png", "accept_date_page.png"),
    SHORT_MARRIAGE(3, "短期想结婚", "short_marriage_pop.png", "short_marriage_page.png"),
    TALK_HEART(4, "找人谈谈心", "talk_heart_pop.png", "talk_heart_page.png"),
    ;

    private int id;
    private String title;

    /**
     * 弹窗图标
     */
    private String popIcon;

    /**
     * 主页图标
     */
    private String pageIcon;

    MakeFriendObjectiveEnum(int id, String title, String popIcon, String pageIcon) {
        this.id = id;
        this.title = title;
        this.popIcon = popIcon;
        this.pageIcon = pageIcon;
    }

    public static MakeFriendObjectiveEnum getById(int id) {
        for (MakeFriendObjectiveEnum makeFriendObjectiveEnum : MakeFriendObjectiveEnum.values()) {
            if (makeFriendObjectiveEnum.getId() == id) {
                return makeFriendObjectiveEnum;
            }
        }
        log.error("makeFriendObjectiveEnum not found by id:{}", id);
        throw new ServiceException("交友目的信息错误");
    }

    public int getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getPopIcon() {
        return popIcon;
    }

    public String getPageIcon() {
        return pageIcon;
    }

    public static String getSameObjectiveUserSize() {
        int threshold = 150 * 10000;
        double factor = 0.1;

        int space = (int) (threshold * factor);
        int res = (threshold - space) + RandomUtil.randomInt(0, 2 * space);
        return String.valueOf(res);
    }

}
