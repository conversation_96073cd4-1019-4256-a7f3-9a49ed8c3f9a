package cn.taqu.account.common;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2021/4/22 13:37
 */
public enum AccountActiveStatus {
    /**
     * "用户最近活跃日期"的accounts_active_log表保存个数
     * "用户最近活跃日期"的缓存个数
     */
    COUNT_OF_ACCOUNT_ACTIVE_CACHES(12);

    private Integer value;

    AccountActiveStatus(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
