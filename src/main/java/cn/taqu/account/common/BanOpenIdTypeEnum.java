package cn.taqu.account.common;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-22 14:37
 */
public enum BanOpenIdTypeEnum {

    // 微信
    WeChat("WeChat"),
    //    qq
    QQ("QQ"),
    //    Apple：苹果账号
    Apple("Apple"),
    //    AliPay：支付宝
    AliPay("AliPay"),
    //    Mobile：手机号
    Mobile("Mobile"),
    //    Identity：身份证件号
    Identity("Identity"),

    ;

    private String value;

    BanOpenIdTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * 是否包含值
     * @param data
     * @return
     */
    public static Boolean isContainValue(String data){
        if(StringUtils.isBlank(data)){
            return false;
        }
        for (BanOpenIdTypeEnum banOpenIdTypeEnum : BanOpenIdTypeEnum.values()) {
            if(banOpenIdTypeEnum.value.equals(data)){
                return true;
            }
        }
        return false;
    }
}
