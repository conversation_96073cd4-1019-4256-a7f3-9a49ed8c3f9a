package cn.taqu.account.common;

/**
 * 认证类型
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-14 14:46
 */
public enum CertTypeEnum {

    // 实名
    REAL_NAME(1),
    // 真人
    REAL_PERSON(2),
    // 提现账号
    REWARD_ACCOUNT(3),
    // 业务级实名
    CHAT_REAL_NAME(4),

    /**
     * 活体认证
     */
    LIVE_CERT(5),
    ;

    private Integer value;

    CertTypeEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
