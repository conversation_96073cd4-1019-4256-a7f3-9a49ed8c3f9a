package cn.taqu.account.common;

public enum ClonedEnum {

    /**
     * 他趣cloned
     */
    TAQU(1),

    /**
     * 他趣plus cloned ios
     */
    TAQU_PLUS(4),

    /**
     * 他趣交友 cloned 安卓
     */
    TAQU_FRIEND(7),

    /**
     * 配配cloned
     */
    PEIPEI(50),

    /**
     * 恰聊cloned
     */
    QIALIAO(51),

    /**
     * 闪恋cloned
     */
    SHANLIAN(60),
    ;

    private Integer code;

    ClonedEnum(Integer code){
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public static boolean isShanLian(int code) {
        return code == SHANLIAN.code;
    }
}
