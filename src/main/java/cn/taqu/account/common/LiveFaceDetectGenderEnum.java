package cn.taqu.account.common;

/**
 * 活体认证性别
 * 图片性别 -1 默认值 0-未知 1-男 2-女
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-26 15:07
 */
public enum LiveFaceDetectGenderEnum {

    // 默认
    DEFAULT(-1),
    // 未知
    UNKNOW(0),
    // 男
    MALE(1),
    // 女
    FEMALE(2);

    private Integer value;

    LiveFaceDetectGenderEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    /**
     * 是否包含value
     * @param v
     * @return
     */
    public static Boolean contains(Integer v) {
        for (LiveFaceDetectGenderEnum liveFaceDetectGenderEnum : LiveFaceDetectGenderEnum.values()) {
            if (liveFaceDetectGenderEnum.getValue().equals(v)) {
                return true;
            }
        }
        return false;
    }
}
