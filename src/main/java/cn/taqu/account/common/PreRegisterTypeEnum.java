package cn.taqu.account.common;

/**
 * 预注册类型 qq,wechat,flash,msg
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-11 12:07
 */
public enum PreRegisterTypeEnum {

    // QQ
    QQ("qq"),
    // 微信
    WECHAT("wechat"),
    // 闪验
    FLASH("flash"),
    // 短信
    MSG("msg");

    private String type;

    PreRegisterTypeEnum(String type){
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
