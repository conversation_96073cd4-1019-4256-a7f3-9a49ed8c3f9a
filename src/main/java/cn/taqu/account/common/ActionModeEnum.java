package cn.taqu.account.common;


/**
 * 登录、注册方式
 *
 * <AUTHOR>
 */
public enum ActionModeEnum {
    /**
     * 微信
     */
    WECHAT("WeChat"),
    /**
     * QQ
     */
    QQ("QQ"),
    /**
     * 微博
     */
    @Deprecated
    WEIBO("WeiBo"),
    /**
     * 微信公众号
     */
    @Deprecated
    WECHAT_OFFICIAL("WeChat_Official"),
    /**
     * 苹果登录
     */
    APPLE("Apple"),
    /**
     * 他趣
     */
    TAQU("TaQu"),
    /**
     * 游客
     */
    @Deprecated
    VISITOR("Visitor")
    ;

    private String value;

    ActionModeEnum(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }

}
