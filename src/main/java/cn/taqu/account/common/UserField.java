package cn.taqu.account.common;

/**
 * <AUTHOR> Wu.D.J
 */
public interface UserField {
    
    interface Def {
        /**
         * 性别
         */
        String GENDER = "sex_type";
    }

    interface Avatar {
        /**
         * 人像认证(机审) 0:未认证 1:已认证
         */
        String PERSON_FACE_AVATAR = "person_face_avatar";

        /**
         * 人像认证(人审) -1:未认证 0:审核 1:已认证
         */
        String PERSON_FACE_AVATAR_CERTIFICATION = "person_face_avatar_certification";
    }
    
}
