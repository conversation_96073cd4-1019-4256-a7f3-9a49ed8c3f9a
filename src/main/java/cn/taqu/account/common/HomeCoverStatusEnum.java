package cn.taqu.account.common;

/**
 * 封面状态(-1 待审核 0-默认 1-通过 2-处罚 3-删除 4-跳过)
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 10:01
 */
@Deprecated
public enum HomeCoverStatusEnum {

    /** -1待审核 */
    NO_CHECK(-1),
    /** 0-默认 */
    DEFULT_URL(0),
    /** 1-通过 */
    PASS(1),
    /** 2-处罚 */
    PUNISH(2),
    /** 3-删除 */
    DELETE(3),
    /** 4-已修改 */
    SKIP(4),
    /** 5-通过转处罚 */
    PASS_TO_PUNISH(5),
    /** 5-通过转删除 */
    PASS_TO_DELETE(6),

    ;

    private Integer value;

    HomeCoverStatusEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}
