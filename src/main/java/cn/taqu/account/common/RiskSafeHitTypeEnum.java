package cn.taqu.account.common;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@Getter
public enum RiskSafeHitTypeEnum {

    // 等待审核
    WAIT("wait"),

    // 通过
    PASS("pass"),

    // 拒绝
    BLOCK("block"),

    // 删除
    DELETE("delete");

    private String status;

    RiskSafeHitTypeEnum(String status) {
        this.status = status;
    }

    public static RiskSafeHitTypeEnum of(String status) {
        RiskSafeHitTypeEnum[] enums = RiskSafeHitTypeEnum.values();
        for (RiskSafeHitTypeEnum e : enums) {
            if (e.getStatus().equals(status)) {
                return e;
            }
        }
        return null;
    }
}
