package cn.taqu.account.common;

/**
 * 头像相册中的阿里云人脸验证状态
 */
public enum CertificationIdentityTypeEnum {

    /**
     * 身份证
     */
    IDENTITY(1),
    /**
     * 外国人
     */
    FOREIGNER(2),
    /**
     * 台湾居民
     */
    TAIWAN(3),
    /**
     * 香港居民
     */
    HONG_KONG_MACAO(4);


    private Integer value;

    CertificationIdentityTypeEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
