package cn.taqu.account.common;

/**
 * prometheus 监控元数据 枚举
 * prometheus metrics
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-09 17:25
 */
public enum PrometheusMetricsEnum {

    /** 腾讯 图片比对 Request */
    TENCENT_IMG_COMPARE_COUNTER("tencent_img_compare_counter", "tencent_img_compare_counter", PrometheusTypeEnum.COUNTER),
    /** 腾讯 图片比对 FAIL Request */
    TENCENT_IMG_COMPARE_FAIL_COUNTER("tencent_img_compare_fail_counter", "tencent_img_compare_fail_counter", PrometheusTypeEnum.COUNTER),
    /** 腾讯 图片比对 可忽略的失败*/
    TENCENT_IMG_COMPARE_IGNORE_FAIL_COUNTER("tencent_img_compare_ignore_fail_counter", "tencent_img_compare_ignore_fail_counter", PrometheusTypeEnum.COUNTER),
    /** 腾讯 图片质量 Request*/
    TENCENT_IMG_QUALITY_COUNTER("tencent_img_quality_counter", "tencent_img_quality_counter", PrometheusTypeEnum.COUNTER),
    /** 腾讯 图片质量 FAIL Request */
    TENCENT_IMG_QUALITY_FAIL_COUNTER("tencent_img_quality_fail_counter", "tencent_img_quality_fail_counter", PrometheusTypeEnum.COUNTER),

    /** 支付宝账号咨询 **/
    ALIPAY_REWARD_ACCOUNT_CERT_PRE_COUNTER("alipay_reward_account_cert_pre_counter", "alipay_reward_account_cert_pre_counter", PrometheusTypeEnum.COUNTER),
    ALIPAY_REWARD_ACCOUNT_CERT_PRE_FAIL_COUNTER("alipay_reward_account_cert_pre_fail_counter", "alipay_reward_account_cert_pre_fail_counter", PrometheusTypeEnum.COUNTER),
    ALIPAY_REWARD_ACCOUNT_CERT_COUNTER("alipay_reward_account_cert_counter", "alipay_reward_account_cert_counter", PrometheusTypeEnum.COUNTER),
    ALIPAY_REWARD_ACCOUNT_CERT_FAIL_COUNTER("alipay_reward_account_cert_fail_counter", "alipay_reward_account_cert_fail_counter", PrometheusTypeEnum.COUNTER),

    /** api - 设置头像v2 Request */
    HTTP_REQUEST_SET_COVER_V2_COUNTER("http_request_set_cover_v2", "http_request_set_cover_v2", PrometheusTypeEnum.COUNTER),
    /** api - 设置相册v3 Request */
    HTTP_REQUEST_SET_AVATAR_V3_COUNTER("http_request_set_avatar_v3", "http_request_set_avatar_v3", PrometheusTypeEnum.COUNTER),
    /** 真人认证相关 */
    INIT_FACE_DETECT_CONF_COUNTER("init_face_detect_conf_counter", "init_face_detect_conf_counter", PrometheusTypeEnum.COUNTER),
    CHECK_FACE_DETECT_RESULT_COUNTER("check_face_detect_result_counter", "check_face_detect_result_counter", PrometheusTypeEnum.COUNTER),
    /** 真人认证预校验 */
    PRECHECK_LIVE_FACE_DETECT_COUNTER("precheck_live_face_detect_counter", "precheck_live_face_detect_counter", PrometheusTypeEnum.COUNTER),

    /** 实名相关 */
    CERTIFICATION_INIT_CERTIFY_COUNTER("certification_init_certify_counter", "certification_init_certify_counter", PrometheusTypeEnum.COUNTER),
    CERTIFICATION_INIT_CERTIFY_V2_COUNTER("certification_init_certify_v2_counter", "certification_init_certify_v2_counter", PrometheusTypeEnum.COUNTER),
    CERTIFICATION_SYNC_CERTIFY_COUNTER("certification_sync_certify_counter", "certification_sync_certify_counter", PrometheusTypeEnum.COUNTER),
    CERTIFICATION_SYNC_CERTIFY_V2_COUNTER("certification_sync_certify_v2_counter", "certification_sync_certify_v2_counter", PrometheusTypeEnum.COUNTER),
    CERTIFICATION_INIT_CERTIFY_NONIDCARD_COUNTER("certification_init_certify_nonidcard_counter", "certification_init_certify_nonidcard_counter", PrometheusTypeEnum.COUNTER),
    
    /** 手机闪验 成功和失败 计数 */
    MOBILE_FLASH_SUCCESS_COUNTER("mobile_flash_success_counter", "mobile_flash_success_counter", PrometheusTypeEnum.COUNTER),
    MOBILE_FLASH_FAILURE_COUNTER("mobile_flash_failure_counter", "mobile_flash_failure_counter", PrometheusTypeEnum.COUNTER),

    /** 取消认证计数 */
    CANCEL_CERT_REAL_NAME_COUNTER("cancel_cert_real_name_counter", "cancel_cert_real_name_counter", PrometheusTypeEnum.COUNTER),
    CANCEL_CERT_REAL_PERSON_COUNTER("cancel_cert_real_person_counter", "cancel_cert_real_person_counter", PrometheusTypeEnum.COUNTER),
    CANCEL_CERT_REWARD_ACCOUNT_COUNTER("cancel_cert_reward_account_counter", "cancel_cert_reward_account_counter", PrometheusTypeEnum.COUNTER),

    // 腾讯 图片比对 RT
    TENCENT_IMG_COMPARE_RT_HISTOGRAM("tencent_img_compare_rt_histogram", "tencent_img_compare_rt_histogram", PrometheusTypeEnum.HISTOGRAM),
    // 腾讯 图片质量 RT
    TENCENT_IMG_QUALITY_RT_HISTOGRAM("tencent_img_quality_rt_histogram", "tencent_img_quality_rt_histogram", PrometheusTypeEnum.HISTOGRAM),

    /*全部做记录*/
    MP_TOKEN_ALL("j2_mp_token_all", "j2_mp_token_all",PrometheusTypeEnum.COUNTER)
    ;

    private String name;

    private String help;

    private PrometheusTypeEnum prometheusTypeEnum;

    PrometheusMetricsEnum(String name, String help, PrometheusTypeEnum prometheusTypeEnum){
        this.name = name;
        this.help = help;
        this.prometheusTypeEnum = prometheusTypeEnum;
    }

    public String getName() {
        return name;
    }

    public String getHelp() {
        return help;
    }

    public PrometheusTypeEnum getPrometheusTypeEnum() {
        return prometheusTypeEnum;
    }
}
