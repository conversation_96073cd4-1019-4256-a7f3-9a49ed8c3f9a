package cn.taqu.account.common;

/**
 * getLiveResult结果 0-无 1-成功 2-失败
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-18 13:58
 */
public enum LiveFaceDetectResultStatusEnum {

    // 默认
    DEFAULT(0),
    // 成功
    SUCCESS(1),
    // 失败
    FAILURE(2);

    private Integer value;

    LiveFaceDetectResultStatusEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
