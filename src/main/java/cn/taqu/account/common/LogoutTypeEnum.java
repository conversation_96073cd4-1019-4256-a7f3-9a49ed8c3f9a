package cn.taqu.account.common;

/**
 * 登出类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-12 13:44
 */
public enum LogoutTypeEnum {

    // 正常退出
    NORMAL_LOGOUT("normal_logout"),
    // 黑名单用户自动踢出
    AUTO_LOGOUT("auto_logout"),
    // 强制退出
    FORCE_LOGOUT("force_logout"),
    // 注销
    DESTROY("destroy");

    private String value;

    LogoutTypeEnum(String value){
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
