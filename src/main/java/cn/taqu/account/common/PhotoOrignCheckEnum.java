package cn.taqu.account.common;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-06 18:10
 */
public enum PhotoOrignCheckEnum {

    /**
     * 真人头像审核
     */
    REAL_PERSON_AVATAR(1),

    /**
     * 非真人头像审核
     */
    NOT_REAL_PERSON_AVATAR(2),

    /**
     * 真人相册审核
     */
    REAL_PERSON_COVER(3);

    private Integer value;

    PhotoOrignCheckEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static Boolean isContainValue(Integer v){
        return v != null & (v == 1 || v == 2|| v == 3);
    }

    public static RiskDetectEnum getByValue(Integer checkValue) {
        if (Objects.equals(REAL_PERSON_AVATAR.getValue(), checkValue)) {
            return RiskDetectEnum.REAL_PERSON_AVATAR;
        } else if (Objects.equals(NOT_REAL_PERSON_AVATAR.getValue(), checkValue)) {
            return RiskDetectEnum.NOT_REAL_PERSON_AVATAR;
        } else if (Objects.equals(REAL_PERSON_COVER.getValue(), checkValue)) {
            return RiskDetectEnum.REAL_PERSON_COVER;
        } else {
            // checkValue为空代表非真人认证相册
            return RiskDetectEnum.NOT_REAL_PERSON_COVER;
        }
    }
}
