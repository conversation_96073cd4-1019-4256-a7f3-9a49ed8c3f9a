package cn.taqu.account.common;

/**
 * 活体认证性别
 * 性别比对结果 -1 默认值 0-性别不一致 1-性别一致
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-26 15:07
 */
public enum LiveFaceDetectGenderCompareResultEnum {

    // 默认
    DEFAULT(-1),
    // 性别不一致
    FAILURE(0),
    // 性别一致
    SUCCESS(1),
    // 未知(sdk上报未知)
    UNKNOW(2);

    private Integer value;

    LiveFaceDetectGenderCompareResultEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
