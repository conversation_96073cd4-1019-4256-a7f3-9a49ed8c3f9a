package cn.taqu.account.common;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18 14:27
 */
@Deprecated
public enum RecordAdminLogActionEnum {

    /** 个人主页封面处罚, $data_list中 content_id 为用户uuid */
    HOME_COVER_PUNISH("home_cover_punish"),
    /** 个人主页封面忽略, $data_list中 content_id 为用户uuid */
    HOME_COVER_IGNORE("home_cover_ignore"),
    /** 个人主页封面删除, $data_list中 content_id 为用户uuid */
    HOME_COVER_DELETE("home_cover_delete"),
    /** 增加认证白名单, $data_list中 content_id 为用户uuid */
    CERT_WHITE_LIST_INSERT("cert_white_list_insert"),
    /** 删除认证白名单, $data_list中 content_id 为用户uuid */
    CERT_WHITE_LIST_DELETE("cert_white_list_delete"),
    /** 增加多身份证认证白名单, $data_list中 content_id 为用户uuid */
    IDENTITY_NO_WHITE_LIST_INSERT("identity_no_white_list_insert"),
    /** 删除多身份证认证白名单, $data_list中 content_id 为用户uuid */
    IDENTITY_NO_WHITE_LIST_DELETE("identity_no_white_list_delete"),

    ;

    private String value;

    RecordAdminLogActionEnum(String value){
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
