package cn.taqu.account.common;

/**
 * 新旧token转换相关 
 * 迁移到j53
 *  
 * <AUTHOR>
 * @date 2020/08/28
 */
@Deprecated
public enum TokenNewOldStatusEnum {

    /**
     * 正常
     */
    NORMAL("1"),
    /**
     * 传入关系已存在
     */
    HAS_RELATION("2"),
    /**
     * Java端生成新token
     */
    GENERATE_TOKEN("3"),
    
    ;

    public String value;

    TokenNewOldStatusEnum(String value){
        this.value = value;
    }

}
