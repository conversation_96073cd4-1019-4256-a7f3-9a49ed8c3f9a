package cn.taqu.account.common;

/**
 * 通用审核状态的枚举
 */
public enum CommonAuditStatus {

    /**
     * 未填写
     *   PS: -1只保存到accounts_info表中，缓存和personal_profile_info 不保存-1，
     *       缓存 -1情况下保存""
     *       personal_profile_info -1 情况下删除数据
     */
    /**
     * 默认 -1
     */
    NONE(-1),
    /**
     * 审核中 0 
     */
    AUDITING(0),
    /**
     * 审核通过 1
     */
    AUDIT_SUCCESS(1),
    /**
     * 审核拒绝 2
     */
    AUDIT_FAIL(2);

    private Integer status;

    CommonAuditStatus(Integer status){
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }}
