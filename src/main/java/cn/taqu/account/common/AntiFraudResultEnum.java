package cn.taqu.account.common;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>  2019/3/12 1:42 PM
 */
@Deprecated
@Slf4j
public enum AntiFraudResultEnum {
    /**
     * 无
     */
    NONE("none", "不需要验证"),
    /**
     * 短信认证
     */
    SMS("anti_fraud_sms", "需要短信验证"),
    /**
     * 语音认证
     */
    VOICE("anti_fraud_voice", "需要语音认证"),
    /**
     * 芝麻认证
     */
    ZHIMA("anti_fraud_zhima", "需要芝麻认证"),
    /**
     * 投篮验证
     */
    SHOOTING("anti_fraud_shooting", "需要投篮验证");

    public static AntiFraudResultEnum getByName(String name) {
        for(AntiFraudResultEnum e : AntiFraudResultEnum.values()) {
            if(e.name().equals(name)) {
                return e;
            }
        }

        log.warn("不支持的认证方式, {}", name);
        return null;
    }

    private String code;
    private String msg;
    public String getCode() {
        return this.code;
    }
    public String getMsg() {
        return this.msg;
    }

    AntiFraudResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
