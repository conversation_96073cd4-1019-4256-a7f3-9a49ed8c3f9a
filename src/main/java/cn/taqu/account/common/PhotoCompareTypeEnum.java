package cn.taqu.account.common;

/**
 * 图片对比日志 类型
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-30 16:09
 */
public enum PhotoCompareTypeEnum {

    /**
     * 实名认证时对比(底图真人)
     */
    CERTIFICATION(1),

    /**
     * 真人认证时对比(底图实名)
     */
    FACE_DETECT(2);

    private Integer value;

    PhotoCompareTypeEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return this.value;
    }
}
