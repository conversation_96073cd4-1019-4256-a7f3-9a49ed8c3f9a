package cn.taqu.account.common;

/**
 * 社区激励任务
 *
 * <AUTHOR>
 * @date 2020/03/17 15:15
 */
public enum FinishIncentiveTaskEnum {
	/**
	 * 真人认证
	 */
	REAL_AVATAR_CONFIRMED("real_avatar_confirmed"),
	/**
	 * 完善相册
	 */
	PERFECT_PHOTO("perfect_photo"),
	/**
	 * 资料100%
	 */
	INFO_HUNDRED_PERCENT("info_hundred_percent"),
	/**
	 * 实名认证
	 */
	REAL_NAME("real_name"),
	/**
	 * 语音签名
	 */
	VOICE_SIGN("voice_sign"),
	/**
	 * 注册
	 */
	NEWBIE_REGISTER("newbie_register"),
	/**
	 * 完成头像
	 */
	COMPLETED_AVATAR("completed_avatar"),
	/**
	 * 邀请完成任务
	 */
	COMPLETE_MATERIAL_FOR_INVITE_TASK("complete_material_for_invite_task"),
	/**
	 * 完成自我介绍任务
	 */
	SELF_INTRODUCTION("self_introduction"),
	/**
	 * 主页封面
	 */
	HOME_COVER("home_cover"),
	/**
	 * 真人认证任务（2024.05.24）
	 */
	NEW_REAL_AVATAR_CONFIRMED("new_real_avatar_confirmed"),

    /**
     * 我的理想型
     * 2024.12.02
     */
    MY_LOVE_TA("my_love_ta"),
    /**
     * 我的标签
     * 2024.12.02
     */
    MY_TAG("my_tag"),
    /**
     * 我的生活
     * 2024.12.02
     */
    MY_LIFE("my_life"),
    /**
     * 个性签名
     * 2024.12.02
     */
    STYLE_SIGN("style_sign"),
	;

	private String type;

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	private FinishIncentiveTaskEnum(String type) {
		this.type = type;
	}
}
