package cn.taqu.account.common;

/**
 * 注册限制提示语枚举
 */
public enum RegisterLimitDocEnum {

    /**
     * 微信
     */
    WECHAT("WeChat", "你的微信账号注册次数已超限制，请使用其他注册方式"),
    /**
     * QQ
     */
    QQ("QQ", "你的QQ账号注册次数已超限制，请使用其他注册方式"),
    /**
     * 微博
     */
    WEIBO("WeiBo", "你的微博账号注册次数已超限制，请使用其他注册方式"),
    /**
     * 游客
     */
    VISITOR("Visitor", "你的设备注册次数已超限制，请使用其他注册方式"),
    /**
     * 手机号注册
     */
    TAQU("TaQu", "你的手机号注册次数已超限制，请使用其他注册方式");

    private String type;
    private String doc;

    RegisterLimitDocEnum(String type, String doc) {
        this.type = type;
        this.doc = doc;
    }

    public static String getDocByType(String type) {
        for (RegisterLimitDocEnum registerLimitDocEnum : RegisterLimitDocEnum.values()) {
            if (registerLimitDocEnum.getType().equals(type)) {
                return registerLimitDocEnum.getDoc();
            }
        }
        return RegisterLimitDocEnum.VISITOR.getDoc();
    }

    public String getType() {
        return type;
    }

    public String getDoc() {
        return doc;
    }}
