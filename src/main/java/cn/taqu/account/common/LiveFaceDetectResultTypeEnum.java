package cn.taqu.account.common;

/**
 * 活体结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-18 13:58
 */
public enum LiveFaceDetectResultTypeEnum {

    /**
     * 默认 成功
     */
    DEFAULT(0),
    /**
     * 活体失败
     */
    LIVE_FACE_DETECT_API_FAILURE(1),
    /**
     * 比对失败
     */
    COMPARE_API_FAILURE(2),
    /**
     * 与实名比对失败(不是同一个人）
     */
    COMPARE_REAL_CERTIFICATION_FAILURE(3);

    private Integer value;

    LiveFaceDetectResultTypeEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}
