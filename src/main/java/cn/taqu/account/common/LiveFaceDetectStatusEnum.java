package cn.taqu.account.common;

/**
 * 活体认证状态 0-无 1-成功 2-失败
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-18 13:56
 */
public enum LiveFaceDetectStatusEnum {

    // 默认
    DEFAULT(0),
    // 成功
    SUCCESS(1),
    // 失败
    FAILURE(2);

    private Integer value;

    LiveFaceDetectStatusEnum(Integer value){
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}
