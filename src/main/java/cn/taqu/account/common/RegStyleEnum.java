package cn.taqu.account.common;

/**
 * 注册类型，从之前的类中拷贝出来
 * 
 * <AUTHOR>
 * 2024年6月18日下午1:49:14
 */
public enum RegStyleEnum {
        UNKNOW(-1, "未知"), MOBILE(1, "手机"), QQ(2, "QQ"), WECHAT(3, "微信"), WEIBO(4, "微博"),APPLE(5,"苹果"),VISITOR(6,"游客模式");

        private int value;
        private String title;

        RegStyleEnum(int value, String title) {
            this.value = value;
            this.title = title;
        }

        public int getValue() {
            return this.value;
        }

        public String getTitle() {
            return this.title;
        }

        public static RegStyleEnum getByValue(Integer value) {
            for (RegStyleEnum style : RegStyleEnum.values()) {
                if (value != null && value == style.value) {
                    return style;
                }
            }

            return UNKNOW;
        }

        public static RegStyleEnum getByName(String name) {
            String upperCase = name.toUpperCase();
            for (RegStyleEnum style : RegStyleEnum.values()) {
                if (name != null && upperCase.equals(style.name())) {
                    return style;
                }
            }

            return UNKNOW;
        }
    }