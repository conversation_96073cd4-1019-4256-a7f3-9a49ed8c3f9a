package cn.taqu.account.filter;

import com.alibaba.druid.filter.FilterEventAdapter;
import com.alibaba.druid.proxy.jdbc.ConnectionProxy;
import com.alibaba.druid.util.JdbcUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * <AUTHOR>
 * @date 2024/12/25 下午1:47
 */
@Slf4j
public class DruidCharsetFilter extends FilterEventAdapter {

    @Override
    public void connection_connectAfter(ConnectionProxy connection) {
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery("set names utf8mb4");
        } catch (SQLException e) {
            log.error("DruidCharsetFilter err", e);
        } finally {
            JdbcUtils.close(resultSet);
            JdbcUtils.close(statement);
        }
    }

}
