package cn.taqu.account.filter;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 健康检测接口
 * <p>替换原框架包健康监测，避免db抖动导致重启</p>
 * <AUTHOR>
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SimpleHealthyFilter extends OncePerRequestFilter {

    public SimpleHealthyFilter() {
        logger.info("Simple healthy enabled.");
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String healthyPath = "/tqHealthy";
        return !healthyPath.equals(request.getServletPath());
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.getWriter().append("success");
    }
}
