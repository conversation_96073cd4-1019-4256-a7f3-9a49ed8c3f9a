package cn.taqu.account.service.impl;

import cn.taqu.account.common.AllureSceneEnum;
import cn.taqu.account.common.FinishIncentiveTaskEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.StatusEnum;
import cn.taqu.account.dao.AccountLabelCfgDao;
import cn.taqu.account.dao.AccountLabelDao;
import cn.taqu.account.dto.PageParam;
import cn.taqu.account.etcd.LabelEtcdHook;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.model.AccountLabel;
import cn.taqu.account.model.AccountLabelCfg;
import cn.taqu.account.service.AccountLabelServiceV2;
import cn.taqu.account.service.AllureService;
import cn.taqu.account.service.BuryService;
import cn.taqu.account.service.ProfileOptimizationUiService;
import cn.taqu.account.vo.req.LabelAdminReq;
import cn.taqu.account.vo.req.LabelCategoryAdminReq;
import cn.taqu.account.vo.resp.*;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.protocol.SoaBaseParams;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户标签V2
 *
 * <AUTHOR>
 * @date 2024/11/25 10:51 上午
 */
@Slf4j
@Service
public class AccountLabelServiceImplV2 implements AccountLabelServiceV2 {

    /**
     * im标签展示限制
     */
    public static Integer imLabelLimit;

    @Resource
    private AccountLabelCfgDao accountLabelCfgDao;
    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;
    @Resource
    private AccountLabelDao accountLabelDao;
    @Resource
    @Lazy
    private AllureService allureService;
    @Resource
    AccountBaseInfoManager accountBaseInfoManager;
    @Resource
    BuryService buryService;
    @Resource
    private ProfileOptimizationUiService profileOptimizationUiService;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addCategory(LabelCategoryAdminReq req) {
        validLength(req.getCategoryName(), 4);

        AccountLabelCfg old = accountLabelCfgDao.isExistCategorySort(req.getCategorySort());
        if (old != null) {
            throw new ServiceException("已存在该排序值,id:" + old.getId());
        }
        old = accountLabelCfgDao.isExistCategoryContent(req.getCategoryName());
        if (old != null) {
            throw new ServiceException("已存在该名称,id:" + old.getId());
        }

        long milli = Instant.now().toEpochMilli();

        AccountLabelCfg entity = new AccountLabelCfg();
        entity.setContent(req.getCategoryName());
        entity.setSort(req.getCategorySort());
        entity.setCreateTime(milli);
        entity.setUpdateTime(milli);
        entity.setDataStatus(req.getCategoryStatus());
        accountLabelCfgDao.merge(entity);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateCategory(LabelCategoryAdminReq req) {
        validLength(req.getCategoryName(), 4);

        AccountLabelCfg label = accountLabelCfgDao.get(req.getCategoryId());
        if (label == null) {
            throw new ServiceException("数据不存在");
        }

        AccountLabelCfg old = accountLabelCfgDao.isExistCategorySort(req.getCategorySort());
        if (old != null && !Objects.equals(label.getId(), old.getId())) {
            throw new ServiceException("已存在该排序值,id:" + old.getId());
        }
        old = accountLabelCfgDao.isExistCategoryContent(req.getCategoryName());
        if (old != null && !Objects.equals(label.getId(), old.getId())) {
            throw new ServiceException("已存在该名称,id:" + old.getId());
        }

        Integer oldDataStatus = label.getDataStatus();
        label.setContent(StringUtils.isNoneBlank(req.getCategoryName()) ? req.getCategoryName() : label.getContent());
        label.setSort(req.getCategorySort() != null ? req.getCategorySort() : label.getSort());
        label.setDataStatus(req.getCategoryStatus());
        label.setUpdateTime(Instant.now().toEpochMilli());
        accountLabelCfgDao.merge(label);

        if (StatusEnum.Switch.ENABLE.match(oldDataStatus) && StatusEnum.Switch.DISABLE.match(req.getCategoryStatus())) {
            closeLabel(req.getCategoryId());
        }
    }

    @Override
    public LabelCategoryResp getCategoryInfo(Long id) {
        AccountLabelCfg label = accountLabelCfgDao.get(id);
        if (label == null) {
            throw new ServiceException("数据不存在");
        }

        LabelCategoryResp resp = new LabelCategoryResp();
        resp.setCategoryId(label.getId());
        resp.setCategoryName(label.getContent());
        resp.setCategorySort(label.getSort());
        resp.setCategoryStatus(label.getDataStatus());
        return resp;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void add(AccountLabelCfg label) {
        validLength(label.getContent());

        AccountLabelCfg old = accountLabelCfgDao.isExistLabelSort(label.getSort());
        if (old != null) {
            throw new ServiceException("已存在该排序值,id:" + old.getId());
        }
        old = accountLabelCfgDao.isExistLabelContent(label.getContent());
        if (old != null) {
            throw new ServiceException("已存在该名称,id:" + old.getId());
        }

        long milli = Instant.now().toEpochMilli();
        label.setCreateTime(milli);
        label.setUpdateTime(milli);
        AccountLabelCfg merge = accountLabelCfgDao.merge(label);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void update(AccountLabelCfg label) {
        validLength(label.getContent());

        AccountLabelCfg old = accountLabelCfgDao.get(label.getId());
        if (old == null) {
            throw new ServiceException("配置不存在");
        }

        AccountLabelCfg temp = accountLabelCfgDao.isExistLabelSort(label.getSort());
        if (temp != null && !Objects.equals(temp.getId(), old.getId())) {
            throw new ServiceException("已存在该排序值,id:" + old.getId());
        }

        temp = accountLabelCfgDao.isExistLabelContent(label.getContent());
        if (temp != null && !Objects.equals(temp.getId(), old.getId())) {
            throw new ServiceException("已存在该名称,id:" + old.getId());
        }

        // 需要查询上级类目是否已启用
        if (StatusEnum.Switch.ENABLE.match(label.getDataStatus())) {
            AccountLabelCfg parent = accountLabelCfgDao.get(label.getParentId());
            if (StatusEnum.Switch.DISABLE.match(parent.getDataStatus())) {
                throw new ServiceException("该标签分类已禁用，请先确认分类状态！");
            }
        }

        label.setCreateTime(old.getCreateTime());
        label.setUpdateTime(Instant.now().toEpochMilli());
        accountLabelCfgDao.merge(label);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateStatus(Long id, Integer status) {
        AccountLabelCfg old = accountLabelCfgDao.get(id);
        if (old == null) {
            throw new ServiceException("配置不存在");
        }
        // 需要查询上级类目是否已启用
        if (StatusEnum.Switch.ENABLE.match(status)) {
            AccountLabelCfg parent = accountLabelCfgDao.get(old.getParentId());
            if (StatusEnum.Switch.DISABLE.match(parent.getDataStatus())) {
                throw new ServiceException("该标签分类已禁用，请先确认分类状态！");
            }
        }

        old.setDataStatus(status);
        old.setUpdateTime(Instant.now().toEpochMilli());
        accountLabelCfgDao.merge(old);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateCategoryStatus(Long categoryId, Integer categoryStatus) {
        AccountLabelCfg old = accountLabelCfgDao.get(categoryId);
        if (old == null) {
            throw new ServiceException("配置不存在");
        }

        old.setDataStatus(categoryStatus);
        old.setUpdateTime(Instant.now().toEpochMilli());
        accountLabelCfgDao.merge(old);

        if (StatusEnum.Switch.DISABLE.match(categoryStatus)) {
            closeLabel(categoryId);
        }
    }

    /**
     * 关闭标签
     *
     * @param categoryId
     */
    private void closeLabel(Long categoryId) {
        log.info("关闭分类:{}", categoryId);
        long milli = Instant.now().toEpochMilli();
        accountLabelCfgDao.closeLabel(categoryId, milli);
    }

    @Override
    public LabelResp getInfo(Long id) {
        AccountLabelCfg old = accountLabelCfgDao.get(id);
        if (old == null) {
            throw new ServiceException("配置不存在");
        }
        AccountLabelCfg parent = accountLabelCfgDao.get(old.getParentId());

        LabelResp resp = new LabelResp();
        resp.setLabelId(old.getId());
        resp.setCategoryId(old.getParentId());
        resp.setCategoryName(parent != null ? parent.getContent() : "");
        resp.setLabelName(old.getContent());
        resp.setLabelIcon(old.getIconUrl());
        resp.setLabelSort(old.getSort());
        resp.setStatus(old.getDataStatus());
        resp.setGender(old.getGender());
        resp.setIntroduce(old.getIntroduce());
        return resp;
    }

    @Override
    public PageData<LabelPageResp> listPage(LabelAdminReq req) {
        Sql sql = Sql.build(AccountLabelCfg.class, AccountLabelCfgDao.DEFAULT_FIELD);
        if (req.getCategoryId() != null) {
            sql.eq("parent_id", req.getCategoryId());
        } else {
            sql.neq("parent_id", 0);
        }
        if (req.getLabelName() != null) {
            sql.like("content", req.getLabelName());
        }
        if (req.getStatus() != null) {
            sql.eq("data_status", req.getStatus());
        }
        sql.orderBy("id desc");
        PageData<AccountLabelCfg> page = accountLabelCfgDao.queryForPage(sql, req.getPageNum(), req.getPageSize());
        List<LabelPageResp> respList = Lists.newArrayList();
        PageData<LabelPageResp> resultPage = new PageData<>();
        resultPage.setPage(page.getPage());
        resultPage.setPageSize(page.getPageSize());
        resultPage.setTotal(page.getTotal());
        resultPage.setData(respList);

        List<Long> categoryIds = page.getData().stream().map(AccountLabelCfg::getParentId).distinct().collect(Collectors.toList());
        List<AccountLabelCfg> categoryList = accountLabelCfgDao.listCategory(categoryIds, null);
        Map<Long, String> categoryMap = categoryList.stream().collect(Collectors.toMap(AccountLabelCfg::getId, AccountLabelCfg::getContent));

        page.getData().forEach(item -> {
            LabelPageResp resp = new LabelPageResp();
            resp.setId(item.getId());
            resp.setCategoryId(item.getParentId());
            resp.setCategoryName(categoryMap.get(item.getParentId()));
            resp.setLabelName(item.getContent());
            resp.setLabelIcon(item.getIconUrl());
            resp.setLabelSort(item.getSort());
            resp.setStatus(item.getDataStatus());
            resp.setCreateTime(item.getCreateTime());
            resp.setUpdateTime(item.getUpdateTime());
            resp.setGender(item.getGender());
            resp.setIntroduce(item.getIntroduce());
            respList.add(resp);
        });
        return resultPage;
    }

    @Override
    public List<LabelCategoryResp> listCategory(LabelCategoryAdminReq req) {
        Integer categoryStatus = req != null ? req.getCategoryStatus() : null;
        List<AccountLabelCfg> accountLabelList = accountLabelCfgDao.listCategory(null, categoryStatus);
        return accountLabelList.stream().sorted(Comparator.comparing(AccountLabelCfg::getSort).reversed()).map(item -> {
            LabelCategoryResp resp = new LabelCategoryResp();
            resp.setCategoryId(item.getId());
            resp.setCategoryName(item.getContent());
            resp.setCategorySort(item.getSort());
            resp.setCategoryStatus(item.getDataStatus());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public MyCategoryLabelResp getCategoryLabel(String accountUuid) {
        // 分类
        List<AccountLabelCfg> categoryList = accountLabelCfgDao.listCategory(null, StatusEnum.Switch.ENABLE.getCode());
        if (CollectionUtils.isEmpty(categoryList)) {
            return null;
        }

        List<MyCategoryLabelResp.CategoryItem> categoryItems = categoryList.stream().map(cat -> {
            MyCategoryLabelResp.CategoryItem category = new MyCategoryLabelResp.CategoryItem();
            category.setCategoryId(cat.getId());
            category.setCategoryName(cat.getContent());
            return category;
        }).collect(Collectors.toList());

        // 标签
        List<AccountLabelCfg> labelList = listMyLabelInfo(accountUuid);
        List<MyCategoryLabelResp.LabelItem> labelItems = labelList.stream().map(lab -> {
            MyCategoryLabelResp.LabelItem label = new MyCategoryLabelResp.LabelItem();
            label.setLabelId(lab.getId());
            label.setLabelIcon(lab.getIconUrl());
            label.setLabelName(lab.getContent());
            return label;
        }).collect(Collectors.toList());

        MyCategoryLabelResp resp = new MyCategoryLabelResp();
        resp.setList(categoryItems);
        resp.setMySelected(labelItems);
        resp.setLimit(getLabelLimit());
        return resp;
    }

    @Override
    public List<PageLabelResp> pageLabel(String accountUuid, Long categoryId, PageParam pageParam) {
        Integer gender = accountBaseInfoManager.getGender(accountUuid);

        Sql sql = Sql.build(AccountLabelCfg.class, AccountLabelCfgDao.API_FIELD);
        sql.neq("parent_id", 0);
        sql.eq("data_status", 1);
        sql.eq("parent_id", categoryId);
        sql.in("gender", new Integer[]{gender, 0});
        sql.orderBy("sort desc");

        PageData<AccountLabelCfg> page = accountLabelCfgDao.queryForPage(sql, pageParam.getPageNum(), pageParam.getPageSize());
        List<AccountLabelCfg> data = page.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream().sorted(Comparator.comparing(AccountLabelCfg::getSort).reversed()).map(label -> {
            PageLabelResp resp = new PageLabelResp();
            resp.setLabelId(label.getId());
            resp.setLabelName(label.getContent());
            resp.setLabelIcon(label.getIconUrl());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void setLabel(String accountUuid, List<Long> labelIds) {
        if (labelIds.size() > getLabelLimit()) {
            throw new ServiceException("标签选择过多");
        }

        // 这里应该在做一层校验数据正确性
        Integer gender = SoaBaseParams.fromThread().getGender();
        List<AccountLabelCfg> labelCfg = accountLabelCfgDao.listLabel(labelIds, StatusEnum.Switch.ENABLE.getCode(), gender);
        if (CollectionUtils.isEmpty(labelCfg) || labelIds.size() != labelCfg.size()) {
            throw new ServiceException("标签不存在");
        }

        String key = RedisKeyConstant.ACCOUNT_LABEL_V2.setArg(accountUuid);
        String val = StringUtils.join(labelIds, ",");
        AccountLabel label = accountLabelDao.getByUuid(accountUuid);
        long milli = Instant.now().toEpochMilli();

        if (label == null) {
            label = new AccountLabel();
            label.setAccountUuid(accountUuid);
            label.setCreateTime(milli);
        }
        label.setLabelIds(val);
        label.setUpdateTime(milli);
        accountLabelDao.merge(label);
        // 设置标签id缓存
        accountBizStringRedisTemplate.opsForValue().set(key, val, 7, TimeUnit.DAYS);
        // 重新计算分值
        allureService.refreshAllure(accountUuid);
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);

        buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.MY_TAG.getType());

    }

    @Override
    public List<Long> listLabelId(String accountUuid) {
        String key = RedisKeyConstant.ACCOUNT_LABEL_V2.setArg(accountUuid);
        String val = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(val)) {
            // 查库
            AccountLabel label = accountLabelDao.getByUuid(accountUuid);
            if (label == null) {
                return new ArrayList<>();
            }
            val = label.getLabelIds();
            accountBizStringRedisTemplate.opsForValue().set(key, val, 7, TimeUnit.DAYS);
        }
        return Arrays.stream(val.split(",")).map(Long::parseLong).collect(Collectors.toList());
    }

    /**
     * 获取分类id
     *
     * @param labelIds
     * @return
     */
    public Set<Long> listCategoryId(List<Long> labelIds, Integer gender) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return new HashSet<>();
        }
        List<AccountLabelCfg> labelCfg = accountLabelCfgDao.listLabel(labelIds, StatusEnum.Switch.ENABLE.getCode(), gender);
        if (CollectionUtils.isEmpty(labelCfg)) {
            return new HashSet<>();
        }
        return labelCfg.stream().map(AccountLabelCfg::getParentId).collect(Collectors.toSet());
    }

    @Override
    public GetTargetLabelResp getTargetLabel(String accountUuid, String targetUuid) {
        GetTargetLabelResp resp = new GetTargetLabelResp();
        Integer targetGender = accountBaseInfoManager.getGender(targetUuid);

        // 获取对方标签分类
        List<Long> targetLabels = listLabelId(targetUuid);
        if (CollectionUtils.isEmpty(targetLabels)) {
            return resp;
        }
        List<AccountLabelCfg> targetLabelCfg = accountLabelCfgDao.listLabel(targetLabels, StatusEnum.Switch.ENABLE.getCode(), targetGender);
        if (CollectionUtils.isEmpty(targetLabelCfg)) {
            return resp;
        }

        Set<Long> targetCategory = targetLabelCfg.stream().map(AccountLabelCfg::getParentId).collect(Collectors.toSet());

        // 获取自己标签分类
        Integer accountGender = accountBaseInfoManager.getGender(accountUuid);
        List<Long> accountLabels = listLabelId(accountUuid);
        Set<Long> accountCategory = listCategoryId(accountLabels, accountGender);

        // 获取对方标签
        boolean notEmpty = CollectionUtils.isNotEmpty(accountLabels);
        List<GetTargetLabelResp.Label> labels = targetLabelCfg.stream().map(cfg -> {
            GetTargetLabelResp.Label label = new GetTargetLabelResp.Label();
            label.setLabelName(cfg.getContent());
            label.setIsHighlight(notEmpty && CollectionUtils.isNotEmpty(accountCategory) && accountCategory.contains(cfg.getParentId()) ? 1 : 0);
            return label;
        }).sorted(Comparator.comparing(GetTargetLabelResp.Label::getIsHighlight).reversed()).collect(Collectors.toList());
        resp.setLabels(labels);

        // 计算双方共同分类
        Map<Long, List<AccountLabelCfg>> group = targetLabelCfg.stream().collect(Collectors.groupingBy(AccountLabelCfg::getParentId));
        if (CollectionUtils.isNotEmpty(accountCategory)) {
            Sets.SetView<Long> inter = Sets.intersection(targetCategory, accountCategory);
            if (CollectionUtils.isNotEmpty(inter)) {
                List<AccountLabelCfg> categoryList = accountLabelCfgDao.listCategory(Lists.newArrayList(inter), StatusEnum.Switch.ENABLE.getCode());
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    resp.setSameCategory(categoryList.stream().sorted((o1, o2) -> group.get(o2.getId()).size() - group.get(o1.getId()).size()).map(AccountLabelCfg::getContent).collect(Collectors.toList()));
                }
            }
        }
        return resp;
    }

    @Override
    public GetTargetLabelResp getTargetLabelForSoa(String accountUuid, String targetUuid) {
        boolean isPass = profileOptimizationUiService.isProfileExp(accountUuid);
        if (!isPass) {
            return null;
        }

        isPass = profileOptimizationUiService.isProfileExp(targetUuid);
        if (!isPass) {
            return null;
        }

        GetTargetLabelResp targetLabel = this.getTargetLabel(accountUuid, targetUuid);
        List<GetTargetLabelResp.Label> labels = targetLabel.getLabels();
        if (CollectionUtils.isEmpty(labels)) {
            return targetLabel;
        }

        int min = Math.min(labels.size(), imLabelLimit);
        targetLabel.setLabels(labels.subList(0, min));
        return targetLabel;
    }

    /**
     * 获取用户分类
     *
     * @param accountUuid
     */
    @Override
    public List<AccountLabelCfg> listMyLabelInfo(String accountUuid) {
        Integer gender = accountBaseInfoManager.getGender(accountUuid);

        List<Long> labelIds = listLabelId(accountUuid);
        if (CollectionUtils.isEmpty(labelIds)) {
            return new ArrayList<>();
        }

        List<AccountLabelCfg> labelCfgList = accountLabelCfgDao.listLabel(labelIds, StatusEnum.Switch.ENABLE.getCode(), gender);
        return labelCfgList.stream().sorted(Comparator.comparing(AccountLabelCfg::getSort).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取选择限制条数
     *
     * @return
     */
    private Integer getLabelLimit() {
        return LabelEtcdHook.config.getMaxLimit();
    }

    /**
     * 长度校验
     * @param content
     */
    public void validLength(String content) {
        if (content.length() > 20) {
            throw new ServiceException("内容不能超过20个字");
        }
    }

    /**
     * 长度校验
     * @param content
     */
    public void validLength(String content, Integer size) {
        if (content.length() > size) {
            throw new ServiceException("内容不能超过" + size + "个字");
        }
    }
}

