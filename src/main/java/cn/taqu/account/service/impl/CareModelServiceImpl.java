package cn.taqu.account.service.impl;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.StatusEnum;
import cn.taqu.account.dao.CareModelDao;
import cn.taqu.account.dto.InfoFiledCacheDTO;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.model.CareModel;
import cn.taqu.account.service.CareModelService;
import cn.taqu.account.vo.resp.CareModelResp;
import cn.taqu.core.protocol.SoaBaseParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

/**
 * 用户关怀模式名单服务实现
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Slf4j
@Service
public class CareModelServiceImpl implements CareModelService {

    @Resource
    private CareModelDao careModelDao;

    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;

    @Resource
    private AccountBaseInfoManager accountBaseInfoManager;

    @Override
    public CareModelResp isCareModel(String accountUuid) {
        CareModelResp resp = new CareModelResp();
        // 是否实名认证、生日信息
        InfoFiledCacheDTO info = accountBaseInfoManager.getInfoByUuid(accountUuid, new String[]{UuidInfoField.IDENTITY_NO_BIRTH, UuidInfoField.ZHIMA_CERTIFICATION});
        resp.setIsReal(info.getZhimaCertification() == null ? 0 : Integer.parseInt(info.getZhimaCertification()));
        resp.setBirth(info.getIdentityNoBirth() == null ? 0 : info.getIdentityNoBirth());

        // 关怀名单获取， 老版本不展示关怀模式
        Long version = getCache(accountUuid);
        Long nowVersion = getAppVersion();
        if (version != null) {
            if (nowVersion >= version) {
                resp.setCareModel(StatusEnum.Switch.ENABLE.getCode());
                return resp;
            }

            resp.setCareModel(StatusEnum.Switch.DISABLE.getCode());
            return resp;
        }

        // 取库
        CareModel model = careModelDao.findByAccountUuid(accountUuid);
        if (model != null) {
            version = model.getVersion();
            addCache(accountUuid, version);
            if (nowVersion >= version) {
                resp.setCareModel(StatusEnum.Switch.ENABLE.getCode());
                return resp;
            }
        }

        resp.setCareModel(StatusEnum.Switch.DISABLE.getCode());
        return resp;
    }

    /**
     * 添加关怀模式
     *
     * @param accountUuid 添加请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void addCareList(String accountUuid) {
        // 检查是否已存在
        if (careModelDao.findByAccountUuid(accountUuid) != null) {
            log.warn("用户已在关怀模式:{}", accountUuid);
            return;
        }

        log.info("用户进入关怀模式:{}", accountUuid);
        long now = Instant.now().toEpochMilli();

        // 创建新记录
        Long appVersion = getAppVersion();
        CareModel careModel = new CareModel();
        careModel.setAccountUuid(accountUuid);
        careModel.setVersion(appVersion);
        careModel.setCreateTime(now);
        careModel.setUpdateTime(now);
        careModelDao.merge(careModel);

        // 添加缓存
        addCache(accountUuid, appVersion);
    }

    /**
     * 添加缓存
     *
     * @param accountUuid
     * @param appVersion
     */
    private void addCache(String accountUuid, Long appVersion) {
        String key = RedisKeyConstant.CARE_MODEL.setArg(accountUuid);
        accountBizStringRedisTemplate.opsForValue().set(key, appVersion.toString(), 7, TimeUnit.DAYS);
    }

    /**
     * 获取缓存
     *
     * @param accountUuid
     */
    private Long getCache(String accountUuid) {
        String key = RedisKeyConstant.CARE_MODEL.setArg(accountUuid);
        String value = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)) {
            return null;
        }

        return Long.parseLong(value);
    }

    /**
     * 获取版本号
     *
     * @return
     */
    private static Long getAppVersion() {
        Long appVersion = SoaBaseParams.fromThread().getApp_version();
        if (appVersion == null || appVersion == 0L) {
            appVersion = 0L;
        }
        return appVersion;
    }
}
