package cn.taqu.account.service.impl;

import cn.taqu.account.dao.AvatarGuidePopupRecordDao;
import cn.taqu.account.model.AvatarGuidePopupRecord;
import cn.taqu.account.service.AvatarGuidePopupService;
import cn.taqu.account.utils.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;

/**
 * 头像指导弹窗记录服务实现
 * 【ai写的，试试】
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Slf4j
@Service
public class AvatarGuidePopupServiceImpl implements AvatarGuidePopupService {

    @Resource
    private AvatarGuidePopupRecordDao avatarGuidePopupRecordDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordPopup(String accountUuid, String avatarUrl) {
        AvatarGuidePopupRecord record = avatarGuidePopupRecordDao.find(accountUuid);
        if (record == null) {
            return;
        }

        Integer today = DateHelper.getDt(LocalDate.now());
        record.setPopupDate(today);
        record.setUpdateTime(Instant.now().toEpochMilli());
        avatarGuidePopupRecordDao.merge(record);
    }

}
