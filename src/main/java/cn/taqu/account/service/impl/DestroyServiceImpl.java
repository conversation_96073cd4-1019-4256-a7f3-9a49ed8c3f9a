package cn.taqu.account.service.impl;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.DestroyReasonConfig;
import cn.taqu.account.service.DestroyService;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.soa.LiveSoaService;
import cn.taqu.account.soa.MpFinanceTradeSoaService;
import cn.taqu.account.soa.PrivilegeSoaService;
import cn.taqu.account.soa.TqFinanceTradeSoaService;
import cn.taqu.account.utils.DateHelper;
import cn.taqu.account.vo.req.SetDestroyReasonReq;
import cn.taqu.account.vo.resp.DestroyCertainItem;
import cn.taqu.account.vo.resp.FreezeToastResp;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.taqu.mp.account.client.MPAccountClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/7 15:52
 */
@Slf4j
@Service
public class DestroyServiceImpl implements DestroyService {

    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;
    public static String TIPS = "该账号处于注销流程中，将于%s注销，继续访问将撤回注销申请";
    @Resource
    private MPAccountClient mpAccountClient;
    @Resource
    PrivilegeSoaService privilegeSoaService;
    @Resource
    MpFinanceTradeSoaService mpFinanceTradeSoaService;
    @Resource
    TqFinanceTradeSoaService tqFinanceTradeSoaService;
    @Resource
    private LiveSoaService liveSoaService;

    /**
     * 配置项
     */
    public static List<DestroyReasonConfig> REASON_CONFIGS;
    public static Map<Integer, String> CONTENT_MAP;

    public static void setConf(String value) {
        REASON_CONFIGS = JsonUtils.stringToObject(value, new TypeReference<List<DestroyReasonConfig>>() {
        });
        CONTENT_MAP = new HashMap<>(REASON_CONFIGS.size());
        for (DestroyReasonConfig reasonConfig : REASON_CONFIGS) {
            CONTENT_MAP.put(reasonConfig.getId(), reasonConfig.getContent());
            if (CollectionUtils.isNotEmpty(reasonConfig.getChildren())) {
                Map<Integer, String> map = reasonConfig.getChildren().stream().collect(Collectors.toMap(DestroyReasonConfig::getId, DestroyReasonConfig::getContent, (k1, k2) -> k1));
                CONTENT_MAP.putAll(map);
            }
        }
    }

    /**
     * 原因配置列表
     *
     * @param uuid
     * @return
     */
    @Override
    public List<DestroyReasonConfig> reasonConfig(String uuid) {
        // 需要换个字段给客户端，他们不想要id
        REASON_CONFIGS.forEach(item -> {
            item.setReasonId();
            if (CollectionUtils.isNotEmpty(item.getChildren())) {
                item.getChildren().forEach(DestroyReasonConfig::setReasonId);
            }
        });
        return REASON_CONFIGS;
    }

    @Override
    public void submitReason(SetDestroyReasonReq req) {
        log.info("用户注销原因设置:{}", JsonUtils.objectToString(req));
        // 需要把注销原因写入缓存，方便后续落库读取
        if (req.getParentId().equals(0)) {
            // 其他类型,id=0
            req.setContent("其他-" + req.getContent());
        } else {
            StringBuilder content = new StringBuilder(CONTENT_MAP.get(req.getParentId()));
            if (req.getChildrenId() != null) {
                content.append("-" + CONTENT_MAP.get(req.getChildrenId()));
            }
            req.setContent(content.toString());
        }

        accountBizStringRedisTemplate.opsForValue().set(RedisKeyConstant.DESTROY_REASON.setArg(req.getUuid())
                , JsonUtils.objectToString(req)
                , 3
                , TimeUnit.HOURS);
    }

    @Override
    public FreezeToastResp freezeToast(String uuid) {
        // 获取冻结时间
        Map<String, Object> freezeMap = mpAccountClient.getFreezeTime(uuid);
        if (freezeMap == null) {
            return null;
        }
        Map map = (Map) freezeMap.getOrDefault("info", new HashMap<>());
        String expireTime = (String) map.getOrDefault("expireTime", "");
        if (StringUtils.isNotBlank(expireTime)) {
            FreezeToastResp resp = new FreezeToastResp();
            LocalDateTime dateTime = DateHelper.parseToDateTime2(expireTime);
            String format = DateHelper.format(dateTime);
            resp.setSubTitle(String.format(TIPS, format));
            return resp;
        }

        log.info("当前账号没有冷静期：{}", uuid);
        return null;
    }

    @Override
    public List<DestroyCertainItem> certain(String accountUuid) {
        ArrayList<DestroyCertainItem> resultList = Lists.newArrayList();

        // 金币
        DestroyCertainItem coinItem = certainCoin();
        resultList.add(coinItem);
        Boolean balance = mpFinanceTradeSoaService.existBalance(accountUuid);
        if (BooleanUtils.isFalse(balance)) {
            coinItem.setBtnText("");
        }

        // 蓝票现金
        DestroyCertainItem moneyItem = certainMoney();
        resultList.add(moneyItem);
        Boolean money = tqFinanceTradeSoaService.existRevenue(accountUuid);
        if (BooleanUtils.isFalse(money)) {
            moneyItem.setBtnText("");
        }

        // vip、贵族
        DestroyCertainItem vipItem = certainVip();
        resultList.add(vipItem);
        // vip
        Boolean vip = privilegeSoaService.isVip(accountUuid);
        // 贵族
        Map<String, Object> accountNoble = SoaService.getAccountNoble(accountUuid);
        // 商城vip
        Boolean mallVip = liveSoaService.isMallVip(accountUuid);

        String nobleCode = MapUtils.getString(accountNoble, "noble_code", "");
        if (BooleanUtils.isFalse(vip) && StringUtils.isBlank(nobleCode) && BooleanUtils.isFalse(mallVip)) {
            vipItem.setBtnText("");
        }

        // 背包支持
        DestroyCertainItem propItem = certainProp();
        resultList.add(propItem);
        Boolean prop = mpFinanceTradeSoaService.existBagBalance(accountUuid);
        if (BooleanUtils.isFalse(prop)) {
            propItem.setBtnText("");
        }

        // 信息资产
        DestroyCertainItem wealthItem = certainWealth();
        resultList.add(wealthItem);

        // 确认提醒
        DestroyCertainItem remindItem = certainRemind();
        resultList.add(remindItem);
        return resultList;
    }

    /**
     * 道具确认
     *
     * @return
     */
    public static DestroyCertainItem certainProp() {
        DestroyCertainItem resp = new DestroyCertainItem();

        resp.setTitle("背包内资产");
        resp.setContent(Lists.newArrayList("背包内未消费的礼物、以及未使用的道具将作废"));
        resp.setBtnText("确认舍弃");

        DestroyCertainItem.DestroyPop pop = new DestroyCertainItem.DestroyPop();
        pop.setPopTitle("放弃背包资产");
        pop.setPopContent("您的账号还有未消费的礼物、道具，确认是否舍弃礼物、道具注销账号");
        DestroyCertainItem.Light submit = new DestroyCertainItem.Light();
        submit.setIsLight(0);
        submit.setText("确认舍弃");
        pop.setPopSubmitIcon(submit);
        DestroyCertainItem.Light cancel = new DestroyCertainItem.Light();
        cancel.setIsLight(1);
        cancel.setText("我再想想");
        pop.setPopCancelIcon(cancel);

        resp.setPop(pop);
        return resp;
    }

    /**
     * 金币确认
     *
     * @return
     */
    public static DestroyCertainItem certainCoin() {
        DestroyCertainItem resp = new DestroyCertainItem();

        resp.setTitle("账号中是否有剩余资产");
        resp.setContent(Lists.newArrayList("资产不可提现，您可在消费后再注销"));
        resp.setBtnText("确认舍弃");

        DestroyCertainItem.DestroyPop pop = new DestroyCertainItem.DestroyPop();
        pop.setPopTitle("放弃剩余资产");
        pop.setPopContent("您的账号中有剩余资产，确认是否舍弃剩余资产来注销账号");

        DestroyCertainItem.Light submit = new DestroyCertainItem.Light();
        submit.setIsLight(0);
        submit.setText("确认舍弃");
        pop.setPopSubmitIcon(submit);
        DestroyCertainItem.Light cancel = new DestroyCertainItem.Light();
        cancel.setIsLight(1);
        cancel.setText("我再想想");
        pop.setPopCancelIcon(cancel);
        resp.setPop(pop);

        return resp;
    }

    /**
     * 金钱确认
     *
     * @return
     */
    public static DestroyCertainItem certainMoney() {
        DestroyCertainItem resp = new DestroyCertainItem();

        resp.setTitle("账号中是否有剩余奖励");
        resp.setContent(Lists.newArrayList("注销后数据清空，无法再次申请奖励提现，您可在页面提现后再进行账号注销"));
        resp.setBtnText("确认舍弃");

        DestroyCertainItem.DestroyPop pop = new DestroyCertainItem.DestroyPop();
        pop.setPopTitle("放弃剩余奖励");
        pop.setPopContent("您的账号中有剩余奖励，注销后无法再次申请奖励提现");

        DestroyCertainItem.Light submit = new DestroyCertainItem.Light();
        submit.setIsLight(0);
        submit.setText("确认舍弃");
        pop.setPopSubmitIcon(submit);
        DestroyCertainItem.Light cancel = new DestroyCertainItem.Light();
        cancel.setIsLight(1);
        cancel.setText("我再想想");
        pop.setPopCancelIcon(cancel);

        resp.setPop(pop);
        return resp;
    }

    /**
     * 金钱确认
     *
     * @return
     */
    public static DestroyCertainItem certainVip() {
        DestroyCertainItem resp = new DestroyCertainItem();

        resp.setTitle("会员服务是否到期");
        resp.setContent(Lists.newArrayList("若\"贵族、会员\"等服务已开通未到期则剩余天数将作废"));
        resp.setBtnText("确认舍弃");

        DestroyCertainItem.DestroyPop pop = new DestroyCertainItem.DestroyPop();
        pop.setPopTitle("放弃会员服务");
        pop.setPopContent("您的账号贵族/会员服务未到期，注销后剩余天数将作废");

        DestroyCertainItem.Light submit = new DestroyCertainItem.Light();
        submit.setIsLight(0);
        submit.setText("确认舍弃");
        pop.setPopSubmitIcon(submit);
        DestroyCertainItem.Light cancel = new DestroyCertainItem.Light();
        cancel.setIsLight(1);
        cancel.setText("我再想想");
        pop.setPopCancelIcon(cancel);
        resp.setPop(pop);
        return resp;
    }

    /**
     * 道具确认
     *
     * @return
     */
    public static DestroyCertainItem certainWealth() {
        DestroyCertainItem resp = new DestroyCertainItem();

        resp.setTitle("信息资产");
        resp.setContent(Lists.newArrayList("账号注销后，无法找回或恢复该账号任何内容，信息；包括但不限于：已认证真人/实名信息、账号信息、头像、昵称、已发布的动态、点赞、评论、转发、收藏、关注、粉丝、好友、聊天等互动信息"));
        resp.setBtnText("确认舍弃");

        DestroyCertainItem.DestroyPop pop = new DestroyCertainItem.DestroyPop();
        pop.setPopTitle("放弃信息资产");
        pop.setPopContent("您在平台内创造了大量信息资产，是否确认舍弃？");

        DestroyCertainItem.Light submit = new DestroyCertainItem.Light();
        submit.setIsLight(0);
        submit.setText("确认舍弃");
        pop.setPopSubmitIcon(submit);
        DestroyCertainItem.Light cancel = new DestroyCertainItem.Light();
        cancel.setIsLight(1);
        cancel.setText("我再想想");
        pop.setPopCancelIcon(cancel);
        resp.setPop(pop);
        return resp;
    }

    /**
     * 提醒确认
     *
     * @return
     */
    public static DestroyCertainItem certainRemind() {
        DestroyCertainItem resp = new DestroyCertainItem();
        resp.setTitle("关于账号注销后的重要提醒：");
        resp.setContent(Lists.newArrayList("1.注销需等待14天：提交申请后，系统将进入14天冷静期（无法跳过或提前注销）",
                "2.实名信息自动解绑：注销成功后（14天后），您的实名信息会自动解绑，可重新绑定至其他账号",
                "3.同一手机号90天内不可重复注册：注销后，该手机号需等待90天才能再次注册（每个手机号最多注册3次）"));
        resp.setBtnText("确认知悉");

        DestroyCertainItem.DestroyPop pop = new DestroyCertainItem.DestroyPop();
        pop.setPopTitle("重要提醒");
        pop.setPopContent("注销冷静期无法跳过以及同一手机号90天不可重复注册");

        DestroyCertainItem.Light submit = new DestroyCertainItem.Light();
        submit.setIsLight(1);
        submit.setText("确认知悉");
        pop.setPopSubmitIcon(submit);
        resp.setPop(pop);
        return resp;
    }

}
