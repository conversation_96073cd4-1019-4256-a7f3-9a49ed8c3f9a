package cn.taqu.account.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.taqu.account.common.AllureSceneEnum;
import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.config.biz.AllureConfig;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.AccountSchoolDto;
import cn.taqu.account.dto.AllureCacheDTO;
import cn.taqu.account.dto.InfoFiledCacheDTO;
import cn.taqu.account.etcd.TouchRankClient;
import cn.taqu.account.event.EventTrackReporter;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.model.AccountsLife;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.service.*;
import cn.taqu.account.utils.DateHelper;
import cn.taqu.account.utils.LogUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.account.vo.resp.AllureResp;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import static cn.taqu.account.etcd.TouchRankClient.PROCESSING;

/**
 * 新引力
 *
 * <AUTHOR>
 * @date 2024/11/26 17:41
 */
@Slf4j
@Service
public class AllureServiceImpl implements AllureService {

    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;
    @Resource
    private AccountBaseInfoManager accountBaseInfoManager;
    @Resource
    private AccountLabelServiceV2 accountLabelServiceV2;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private SchoolService schoolService;
    @Resource
    private AccountsLifeService accountsLifeService;
    @Resource
    private ProfileOptimizationUiService profileOptimizationUiService;
    @Resource
    private RedisLockUtil redisLockUtil;
    @Resource
    private TouchRankClient touchRankClient;
    @Resource
    private AvatarHandleService avatarHandleService;
    @Resource
    private EventTrackReporter trackReporter;

    public static final String content = "当前吸引力%s/100分，击败%s的用户";

    private static final String TOTAL_KEY = "TOTAL";

    @Override
    public AllureResp getAllure(String accountUuid) {
        // 判断积分是否存在
        Integer dt = DateHelper.getDt(LocalDate.now());
        String key = RedisKeyConstant.ACCOUNT_AllURE.setArg(accountUuid, dt.toString());
        String val = accountBizStringRedisTemplate.opsForValue().get(key);

        // 获取
        AllureCacheDTO allureInfo = StringUtils.isNotBlank(val) ?
                JsonUtils.stringToObject(val, AllureCacheDTO.class) :
                initAllure(accountUuid, key);

        AllureResp resp = new AllureResp();
        resp.setAllurePoint(allureInfo.getPoint());
        resp.setAllureRatio(allureInfo.getRatio() + "%");
        resp.setContent(content);
        return resp;
    }

    @Override
    public AllureResp getAllureV2(String accountUuid) {
        String key = RedisKeyConstant.ACCOUNT_AllURE_V2.setArg(accountUuid);
        int score = PROCESSING;
        if (accountBizStringRedisTemplate.hasKey(key)) {
            val hashOps = accountBizStringRedisTemplate.<String, String>opsForHash();
            String val = hashOps.get(key, TOTAL_KEY);
            if (val != null) {
                score = Integer.parseInt(val);
                // 续期
                accountBizStringRedisTemplate.expire(key, 1, TimeUnit.HOURS);
            }
        } else {
            AtomicInteger sum = new AtomicInteger(0);
            AtomicBoolean negative = new AtomicBoolean(false);
            Consumer<Integer> consumer = i -> {
                if (i <= 0) {
                    negative.set(true);
                } else {
                    sum.addAndGet(i);
                }
            };
            InfoFiledCacheDTO info = accountBaseInfoManager.getInfoByUuid(accountUuid, new String[] { UuidInfoField.AVATAR, UuidInfoField.PASS_INTRODUCTION_CONTENT});
            CompletableFuture<Void> f1 = refreshAllureV2(accountUuid, AllureSceneEnum.AVATAR, info.getAvatar()).thenAccept(consumer);
            CompletableFuture<Void> f2 = refreshAllureV2(accountUuid, AllureSceneEnum.INTRODUCTION, info.getIntroduction()).thenAccept(consumer);
            CompletableFuture<Void> f3 = refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null).thenAccept(consumer);
            CompletableFuture.allOf(f1, f2, f3).join();
            if (negative.get()) {
                score = PROCESSING;
            } else {
                score = sum.get();
            }
        }
        AllureResp resp = new AllureResp();
        resp.setAllurePoint(score);
        resp.setAllureRatio(StringUtils.EMPTY);
        resp.setContent(resp.getAllurePoint() >= 0 ? "当前吸引力%s/100分，吸引力越高，曝光越多" : "吸引力分系统统计中，请耐心等待");
        return resp;
    }

    @Override
    public AllureCacheDTO refreshAllure(String accountUuid) {
        String lock = RedisKeyConstant.ALLURE_LOCK.setArg(accountUuid);
        try {
            return redisLockUtil.executeWithLock(lock, 30000, () -> {
                // 判断积分是否存在
                Integer dt = DateHelper.getDt(LocalDate.now());
                String key = RedisKeyConstant.ACCOUNT_AllURE.setArg(accountUuid, dt.toString());
                String val = accountBizStringRedisTemplate.opsForValue().get(key);

                // 系统积分、比例
                AllureCacheDTO cache;
                if (StringUtils.isNotBlank(val)) {
                    cache = JsonUtils.stringToObject(val, AllureCacheDTO.class);
                } else {
                    cache = new AllureCacheDTO();
                    cache.setDailySystemPoint(getSystemPoint());
                    cache.setDailySystemRatio(getSystemRatio());
                }

                // 计算吸引力
                return culAllureHandle(accountUuid, key, cache);
            });
        } catch (RuntimeException e) {
            log.warn("吸引力刷新失败", e);
        }
        return null;
    }

    @Override
    public CompletableFuture<Integer> refreshAllureV2(String uuid, AllureSceneEnum scene, String content) {
        LogUtil.info4Gray("吸引力v2开始刷新：{} {} {}", uuid, scene, content);
        String key = RedisKeyConstant.ACCOUNT_AllURE_V2.setArg(uuid);
        return CompletableFuture.supplyAsync(() -> {
            BigDecimal score;
            val hashOps = accountBizStringRedisTemplate.<String, String>opsForHash();
            switch (scene) {
                case AVATAR:
                    if (StringUtils.isBlank(content)) {
                        score = BigDecimal.ZERO;
                    } else if (AvatarHandleService.isDefAvatar(content)) {
                        // 默认头像
                        score = BigDecimal.ZERO;
                    } else {
                        String avatar = avatarHandleService.getAvatarByVersion(content, null);
                        TouchRankClient.AiResult result = touchRankClient.appearanceEval(uuid, avatar, accountBaseInfoManager.getGender(uuid));
                        if (result.successful() && result.isTrue()) {
                            score = BigDecimal.valueOf(result.getScore());
                            score = score.multiply(new BigDecimal(scene.getFactor()));
                        } else {
                            score = BigDecimal.valueOf(PROCESSING);
                        }
                    }
                    break;
                case INTRODUCTION:
                    if (StringUtils.isBlank(content) || content.length() < 40) {
                        score = BigDecimal.ZERO;
                    } else {
                        TouchRankClient.AiResult result = touchRankClient.sincereEval(uuid, content, accountBaseInfoManager.getGender(uuid));
                        if (result.successful() && result.isTrue()) {
                            score = BigDecimal.valueOf(result.getScore());
                            score = score.multiply(new BigDecimal(scene.getFactor()));
                        } else {
                            score = BigDecimal.valueOf(PROCESSING);
                        }
                    }
                    break;
                default:
                    score = SpringContextHolder.getBean(AccountsPersonalInfoService.class).accountCardProgress(uuid);
                    trackReporter.report(uuid, "tq_profile_score", ImmutableMap.of("complete_score", score.multiply(BigDecimal.valueOf(100)).intValue()));
                    String total = accountBizStringRedisTemplate.<String, String>opsForHash().get(key, TOTAL_KEY);
                    if (StringUtils.isNotBlank(total) && !PROCESSING.toString().equals(total) && NumberUtil.isNumber(total)) {
                        trackReporter.report(uuid, "tq_profile_score", ImmutableMap.of("appeal_score", Integer.parseInt(total)));
                    }
                    score = score.multiply(BigDecimal.TEN).multiply(new BigDecimal(scene.getFactor()));

            }
            int sc = score.intValue();
            hashOps.put(key, scene.name(), String.valueOf(sc));
            // -1分代表算法没返回算分结果 待pulsar推入分数进行更新
            if (sc < 0) {
                hashOps.put(key, TOTAL_KEY, PROCESSING.toString());
                accountBizStringRedisTemplate.expire(key, 5, TimeUnit.MINUTES);
                return PROCESSING;
            }
            LogUtil.info4Gray("吸引力v2刷新：{} {} {} {}", uuid, scene, content, sc);
            accumulate(uuid);
            return sc;
        }, threadPoolTaskExecutor).exceptionally(e -> {
            String msg = String.format("用户%s吸引力刷新异常", uuid);
            log.warn(msg, e);
            accountBizStringRedisTemplate.expire(key, 1, TimeUnit.MINUTES);
            return 0;
        });
    }

    @Override
    public void algorithmScoreCallback(String uuid, AllureSceneEnum scene, BigDecimal score) {
        String key = RedisKeyConstant.ACCOUNT_AllURE_V2.setArg(uuid);
        val hashOps = accountBizStringRedisTemplate.<String, String>opsForHash();
        int sc = score.multiply(BigDecimal.valueOf(scene.getFactor())).intValue();
        hashOps.put(key, scene.name(), String.valueOf(sc));
        accumulate(uuid);
    }

    private void accumulate(String uuid) {
        String key = RedisKeyConstant.ACCOUNT_AllURE_V2.setArg(uuid);
        val hashOps = accountBizStringRedisTemplate.<String, String>opsForHash();
        redisLockUtil.executeWithLock("account:allure:accumulate:lock:" + uuid, () -> {
            Map<String, String> map = hashOps.entries(key);
            map.remove(TOTAL_KEY);
            Collection<String> values = map.values();
            if (values.contains(PROCESSING.toString())) {
                return PROCESSING;
            }
            Integer sum = values.stream().map(c -> Optional.ofNullable(c).orElse("0")).map(Integer::parseInt).reduce(0, Integer::sum);
            hashOps.put(key, TOTAL_KEY, sum.toString());
            accountBizStringRedisTemplate.expire(key, 8, TimeUnit.HOURS);
            trackReporter.report(uuid, "tq_profile_score", ImmutableMap.of("appeal_score", sum));
            return sum;
        });
    }

    @Override
    public AllureCacheDTO refreshAllureValidVersion(String accountUuid) {
        if (profileOptimizationUiService.isProfileExp(accountUuid)) {
            this.refreshAllure(accountUuid);
        }

        return null;
    }

    @Override
    public void deleteAllureCache(String uuid) {
        // 判断积分是否存在
        Integer dt = DateHelper.getDt(LocalDate.now());
        String key = RedisKeyConstant.ACCOUNT_AllURE.setArg(uuid, dt.toString());
        accountBizStringRedisTemplate.delete(key);
    }

    /**
     * 初始化
     *
     * @param accountUuid
     * @param key
     */
    private AllureCacheDTO initAllure(String accountUuid, String key) {
        // 计算吸引力
        AllureCacheDTO cache = new AllureCacheDTO();
        // 系统区间分
        cache.setDailySystemPoint(getSystemPoint());
        // 系统区间比例
        cache.setDailySystemRatio(getSystemRatio());
        cache.setPoint(0);
        return culAllureHandle(accountUuid, key, cache);
    }

    /**
     * 吸引力计算
     *
     * @param accountUuid
     * @param key
     * @return
     */
    public AllureCacheDTO culAllureHandle(String accountUuid, String key, AllureCacheDTO cache) {
        // 总的完成项
        int completedItems = culCompletedItems(accountUuid);
        // ------------------ 分值获取 ------------------
        // 基础分
        Integer basePoint = getBasePoint();
        LogUtil.info4Dev("基础分:{}", basePoint);
        // 各项积分
        Integer itemsPoint = getItemPoint(completedItems);
        LogUtil.info4Dev("各项分值:{}", itemsPoint);
        // 最终分值管控
        Integer finalPoint = finalPointHandler(basePoint + itemsPoint + cache.getDailySystemPoint());
        LogUtil.info4Dev("最终分值:{}", finalPoint);
        // 分支不变，则比例不需要修改
        if (Objects.equals(cache.getPoint(), finalPoint)) {
            if (!Objects.equals(completedItems, cache.getFinishItem())) {
                cache.setFinishItem(completedItems);
            }
            return cache;
        }

        // ------------------ 比例管控 ------------------
        // 基础比例
        Integer baseRatio = getBaseRatio();
        LogUtil.info4Dev("基础比例:{}", baseRatio);
        // 各项比例
        Integer itemsRatio = getItemRatio(completedItems);
        LogUtil.info4Dev("各项比例:{}", itemsRatio);
        // 最终比例管控
        Integer totalRatio = baseRatio + itemsRatio + cache.getDailySystemRatio();
        Integer oldFinishItem = Optional.ofNullable(cache.getFinishItem()).orElse(0);
        Integer oldRatio = Optional.ofNullable(cache.getRatio()).orElse(0);
        Integer finalRatio = finalRatioHandler(totalRatio, finalPoint, completedItems >= oldFinishItem, oldRatio);
        LogUtil.info4Dev("最终分值:{}", finalRatio);

        // 缓存处理
        cache.setPoint(finalPoint);
        cache.setRatio(finalRatio);
        cache.setFinishItem(completedItems);
        accountBizStringRedisTemplate.opsForValue().set(key, JsonUtils.objectToString(cache), 1, TimeUnit.DAYS);
        return cache;
    }

    /**
     * 基础分
     *
     * @return
     */
    private Integer getBasePoint() {
        return Optional.ofNullable(AllureConfig.point.getBasePoint()).orElse(0);
    }

    /**
     * 基础分
     *
     * @return
     */
    private Integer getBaseRatio() {
        return Optional.ofNullable(AllureConfig.ratio.getRatioPoint()).orElse(0);
    }

    /**
     * 各项积分
     *
     * @param completedItems
     * @return
     */
    private Integer getItemPoint(Integer completedItems) {
        return AllureConfig.point.getItemPoint() * completedItems;
    }

    /**
     * 各项比例
     *
     * @param completedItems
     * @return
     */
    private Integer getItemRatio(Integer completedItems) {
        List<Integer> config = AllureConfig.ratio.getItemRatioRange();
        int index = ThreadLocalRandom.current().nextInt(0, config.size());
        Integer element = config.get(index);
        return element * completedItems;
    }

    /**
     * 统计完成项
     *
     * @param accountUuid
     * @return
     */
    private int culCompletedItems(String accountUuid) {
        AtomicInteger num = new AtomicInteger(0);
        // 基础信息
        calBaseInfo(accountUuid, num);
        // 个人标签
        calLabel(accountUuid, num);
        // 学校
        calSchool(accountUuid, num);
        // 我的生活
        culLife(accountUuid, num);
        return num.get();
    }

    /**
     * 计算我的生活
     *
     * @param accountUuid
     * @param num
     * @return
     */
    private void culLife(String accountUuid, AtomicInteger num) {
        List<AccountsLife> life = accountsLifeService.getAccountsLifeOrAccountsPhoto(accountUuid, false, false);
        log.info("获取我的生活,uuid:{}, list:{}", accountUuid, life != null ? life.size() : 0);
        if (CollectionUtils.isNotEmpty(life)) {
            num.addAndGet(AccountsLifeService.LIFE_TASK_COMPLETE_THRESHOLD);
        }
    }

    /**
     * 计算学校
     *
     * @param accountUuid
     * @param num
     */
    private void calSchool(String accountUuid, AtomicInteger num) {
        CompletableFuture<AccountSchoolDto> schoolFuture = CompletableFuture.supplyAsync(() ->
                        schoolService.info(accountUuid, false),
                threadPoolTaskExecutor).exceptionally(e -> {
            log.warn("计算魅力值学校获取异常, uuid:{}", accountUuid, e);
            return null;
        });
        AccountSchoolDto school = schoolFuture.join();
        if (school != null && StringUtils.isNotBlank(school.getSchoolName())) {
            LogUtil.info4Dev("我的学校+1");
            num.incrementAndGet();
        }
    }

    /**
     * 个人标签
     *
     * @param accountUuid
     * @param num
     */
    private void calLabel(String accountUuid, AtomicInteger num) {
        CompletableFuture<List<Long>> labelFuture = CompletableFuture.supplyAsync(() ->
                        accountLabelServiceV2.listLabelId(accountUuid)
                , threadPoolTaskExecutor).exceptionally(e -> {
            log.warn("计算魅力值个性标签获取异常, uuid:{}", accountUuid, e);
            return null;
        });
        List<Long> list = labelFuture.join();
        if (CollectionUtils.isNotEmpty(list)) {
            LogUtil.info4Dev("个人标签+1");
            num.incrementAndGet();
        }
    }

    /**
     * 基础信息
     *
     * @param accountUuid
     * @param num
     */
    private void calBaseInfo(String accountUuid, AtomicInteger num) {
        // 基础信息:恋爱状况、所在地、家乡、个人简介、学历、职业、身高、体重、个人标签、收入
        CompletableFuture<InfoFiledCacheDTO> infoFuture = CompletableFuture.supplyAsync(() -> accountBaseInfoManager.getInfoByUuid(accountUuid, new String[]{
                UuidInfoField.AFFECTIVESTATUS,
                UuidInfoField.BASEADDR,
                UuidInfoField.HOMETOWN,
                UuidInfoField.PASS_INTRODUCTION_CONTENT,
                UuidInfoField.EDUCATION,
                UuidInfoField.PROFESSION,
                UuidInfoField.HEIGHT,
                UuidInfoField.WEIGHT,
                UuidInfoField.INCOME,
                UuidInfoField.REAL_AVATAR_CERTIFICATION,
                UuidInfoField.PROFILE_VERIFY_STATUS,
                UuidInfoField.AVATAR
        }), threadPoolTaskExecutor).exceptionally(e -> {
            log.warn("计算魅力值基础信息获取异常, uuid:{}", accountUuid, e);
            return null;
        });

        InfoFiledCacheDTO info = infoFuture.join();
        if (StringUtils.isNotBlank(info.getBaseAddr())) {
            LogUtil.info4Dev("所在地+1");
            num.incrementAndGet();
        }
        if (StringUtils.isNotBlank(info.getHometown())) {
            LogUtil.info4Dev("家乡+1");
            num.incrementAndGet();
        }
        if (StringUtils.isNotBlank(info.getIntroduction())) {
            LogUtil.info4Dev("自我介绍+1");
            num.incrementAndGet();
        }
        if (StringUtils.isNotBlank(info.getEducation())) {
            LogUtil.info4Dev("学历+1");
            num.incrementAndGet();
        }
        if (StringUtils.isNotBlank(info.getProfession())) {
            LogUtil.info4Dev("职业+1");
            num.incrementAndGet();
        }
        if (info.getHeight() != 0) {
            LogUtil.info4Dev("身高+1");
            num.incrementAndGet();
        }
        if (info.getWeight() != 0) {
            LogUtil.info4Dev("体重+1");
            num.incrementAndGet();
        }
        if (info.getIncome() != 0) {
            LogUtil.info4Dev("收入+1");
            num.incrementAndGet();
        }
        if (info.getAffectiveStatus() != 0) {
            LogUtil.info4Dev("情感状态+1");
            num.incrementAndGet();
        }
        if (info.realPersonCertificationPass()) {
            LogUtil.info4Dev("真人认证通过+1");
            num.incrementAndGet();
        }
        AccountsPhoto avatar = SpringContextHolder.getBean(AccountsPhotoService.class).getAccountAvatar(accountUuid, null, true);
        if (avatar != null) {
            LogUtil.info4Dev("头像+1");
            num.incrementAndGet();
        }
    }

    /**
     * 系统区间分
     *
     * @return
     */
    private Integer getSystemPoint() {
        Optional<List<Integer>> optional = Optional.ofNullable(AllureConfig.point.getSystemPointRange());
        if (optional.isPresent()) {
            List<Integer> range = optional.get();
            return ThreadLocalRandom.current().nextInt(range.get(0), range.get(1) + 1);
        }

        return 0;
    }

    /**
     * 系统区间比例
     *
     * @return
     */
    private Integer getSystemRatio() {
        Optional<List<Integer>> optional = Optional.ofNullable(AllureConfig.ratio.getSystemRatioRange());
        if (optional.isPresent()) {
            List<Integer> range = optional.get();
            return ThreadLocalRandom.current().nextInt(range.get(0), range.get(1) + 1);
        }

        return 0;
    }

    /**
     * 分值管控
     *
     * @param total
     * @return
     */
    private Integer finalPointHandler(Integer total) {
        Optional<List<Integer>> optional = Optional.ofNullable(AllureConfig.point.getFinalPointRange());
        if (optional.isPresent()) {
            List<Integer> range = optional.get();
            if (total < range.get(0)) {
                return range.get(0);
            }
            if (total > range.get(1)) {
                return range.get(1);
            }
        }

        return total;
    }

    /**
     * 比例管控
     *
     * @param total      总比例分
     * @param finalPoint 总得分
     * @param isIncr     完成项项数
     * @param oldRatio   上次比例分
     * @return
     */
    private Integer finalRatioHandler(Integer total, Integer finalPoint, Boolean isIncr, Integer oldRatio) {
        Optional<List<Integer>> optional = Optional.ofNullable(AllureConfig.ratio.getFinalRatioRange());
        if (!optional.isPresent()) {
            return total;
        }

        List<Integer> range = optional.get();
        Integer min = range.get(0);
        // 总分比最低分值设置低的场景
        if (total < min) {
            // 分值相同，在最低值就随机加个数
            if (Objects.equals(min, finalPoint)) {
                int random = ThreadLocalRandom.current().nextInt(1, 5);
                LogUtil.info4Dev("低于最低值，但分值相同, min:{}, random:{}", min, random);
                int temp = min + random;
                // 合理化校验
                return reasonableCheck(isIncr, temp, oldRatio);
            }
            return min;
        }

        Integer max = range.get(1);
        if (total > max) {
            // 分值相同，在最低值就随机减个数
            if (Objects.equals(max, finalPoint)) {
                int random = ThreadLocalRandom.current().nextInt(1, 5);
                LogUtil.info4Dev("高于最低值，但分值相同, min:{}, random:{}", min, random);
                int temp = max - random;
                // 合理化校验
                return reasonableCheck(isIncr, temp, oldRatio);
            }
            return max;
        }

        if (Objects.equals(total, finalPoint)) {
            int random = ThreadLocalRandom.current().nextInt(1, 5);
            log.debug("分值相同, total:{}, random:{}", total, random);
            int temp = total - random;
            // 合理化校验
            return reasonableCheck(isIncr, temp, oldRatio);
        }

        return reasonableCheck(isIncr, total, oldRatio);
    }

    /**
     * 合理化校验
     *
     * @return
     */
    private int reasonableCheck(Boolean isIncr, Integer temp, Integer oldRatio) {
        // 是否增加
        if (isIncr) {
            LogUtil.info4Dev("完成项增加场景, new:{}, old:{}", temp, oldRatio);
            if (temp >= oldRatio) {
                return temp;
            }
            return oldRatio;
        } else {
            LogUtil.info4Dev("完成项减少场景, new:{}, old:{}", temp, oldRatio);
            if (temp <= oldRatio) {
                return temp;
            }
            return oldRatio;
        }
    }
}
