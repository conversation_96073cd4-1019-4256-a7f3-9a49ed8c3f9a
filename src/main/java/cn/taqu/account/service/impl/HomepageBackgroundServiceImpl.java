package cn.taqu.account.service.impl;

import cn.taqu.account.config.biz.HomepageBackgroundConfig;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.HomepageBackgroundDao;
import cn.taqu.account.model.HomepageBackground;
import cn.taqu.account.service.HomepageBackgroundService;
import cn.taqu.account.vo.resp.HomepageBackgroundResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * 首页背景
 *
 * <AUTHOR>
 * @date 2024/11/26 17:41
 */
@Slf4j
@Service
public class HomepageBackgroundServiceImpl implements HomepageBackgroundService {

    @Resource
    private HomepageBackgroundDao homepageBackgroundDao;
    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;

    @Override
    public List<HomepageBackgroundResp> listBackground(String accountUuid) {
        return HomepageBackgroundConfig.backgroundList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void setBackground(String accountUuid, Integer bgId) {
        boolean match = HomepageBackgroundConfig.backgroundList.stream().anyMatch(item -> Objects.equals(item.getBackgroundId(), bgId));
        if (!match) {
            log.info("设置背景图片，id不存在:{}, uuid:{}", bgId, accountUuid);
            return;
        }

        long milli = Instant.now().toEpochMilli();
        HomepageBackground bg = homepageBackgroundDao.getByUuid(accountUuid);
        if (bg == null) {
            bg = new HomepageBackground();
            bg.setAccountUuid(accountUuid);
            bg.setCreateTime(milli);
        }
        bg.setBackgroundId(bgId);
        bg.setUpdateTime(milli);
        homepageBackgroundDao.merge(bg);

        String key = RedisKeyConstant.ACCOUNT_BACKGROUND.setArg(accountUuid);
        accountBizStringRedisTemplate.opsForValue().set(key, bgId.toString(), 7, TimeUnit.DAYS);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public HomepageBackgroundResp getBackground(String accountUuid) {
        HomepageBackgroundResp resp = new HomepageBackgroundResp();

        String key = RedisKeyConstant.ACCOUNT_BACKGROUND.setArg(accountUuid);
        String val = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(val)) {
            resp.setBackgroundId(Integer.parseInt(val));
            resp.setBackgroundUrl(HomepageBackgroundConfig.backgroundMap.get(resp.getBackgroundId()));
            return resp;
        }

        HomepageBackground bg = homepageBackgroundDao.getByUuid(accountUuid);
        if (bg != null) {
            resp.setBackgroundId(bg.getBackgroundId());
            resp.setBackgroundUrl(HomepageBackgroundConfig.backgroundMap.get(bg.getBackgroundId()));
            // 更新缓存
            accountBizStringRedisTemplate.opsForValue().set(key, bg.getBackgroundId().toString(), 7, TimeUnit.DAYS);
            return resp;
        }

        // 初始化, 随机选一张
        int index = ThreadLocalRandom.current().nextInt(0, HomepageBackgroundConfig.backgroundList.size());
        HomepageBackgroundResp element = HomepageBackgroundConfig.backgroundList.get(index);
        resp.setBackgroundId(element.getBackgroundId());
        resp.setBackgroundUrl(element.getBackgroundUrl());
        setBackground(accountUuid, resp.getBackgroundId());
        return resp;
    }


}
