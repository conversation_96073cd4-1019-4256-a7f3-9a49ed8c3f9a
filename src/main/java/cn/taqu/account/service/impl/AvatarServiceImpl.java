package cn.taqu.account.service.impl;

import cn.taqu.account.constant.AbRuleCode;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.AbTestRespDto;
import cn.taqu.account.dto.AvatarGuideDTO;
import cn.taqu.account.etcd.AvatarGuideConfig;
import cn.taqu.account.service.AvatarGuidePopupService;
import cn.taqu.account.service.AvatarService;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.soa.UserRatingService;
import cn.taqu.account.utils.LogUtil;
import cn.taqu.account.vo.resp.AvatarGuilePopupResp;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 头像业务
 *
 * <AUTHOR>
 * @date 2025/6/23 11:05
 */
@Slf4j
@Service
public class AvatarServiceImpl implements AvatarService {

    @Resource
    private StringRedisTemplate accountBizStringRedisTemplate;

    @Resource
    private AvatarGuidePopupService avatarGuidePopupService;

    @Resource
    private UserRatingService userRatingService;

    @Override
    public AvatarGuilePopupResp guidePopup(String uuid) {
        // 获取头像更换通知
        AvatarGuideDTO guide = getGuide(uuid);
        AvatarGuilePopupResp resp = new AvatarGuilePopupResp();
        if (guide == null) {
            resp.setIsPop(0);
            return resp;
        }

        // AB实验判断
        boolean isExp = isGuideExpGroup(uuid, SoaBaseParams.fromThread().getCloned());
        resp.setIsExp(isExp ? CommConst.YES_1 : CommConst.NO_0);
        if (!isExp) {
            return resp;
        }

        // 是否负一级
        if (userRatingService.isSpecialUser(uuid)) {
            return resp;
        }

        delKey(uuid);

        // 弹窗配置
        setGuideConfig(resp, guide, uuid);

        // 记录弹窗
        avatarGuidePopupService.recordPopup(uuid, guide.getUrl());
        log.info("头像指导弹窗：{},", JsonUtils.objectToString(guide));
        return resp;
    }

    private void delKey(String uuid) {
        String key = RedisKeyConstant.AVATAR_GUIDE_POPUP.setArg(uuid);
        accountBizStringRedisTemplate.delete(key);
    }

    /**
     * 指导配置
     *
     * @param resp
     * @param guide
     * @param uuid 用户UUID
     */
    private void setGuideConfig(AvatarGuilePopupResp resp, AvatarGuideDTO guide, String uuid) {
        AvatarGuideConfig.UI ui = AvatarGuideConfig.CONFIG.getUi().get(guide.getScene());
        resp.setIsExp(1);
        resp.setIsPop(1);
        resp.setType(guide.getScene());
        resp.setAvatar(guide.getUrl());
        resp.setTitleBg(ui.getTitleBg());
        resp.setSubTitle(ui.getSubTitle());
        resp.setTitleDarkBg(ui.getTitleDarkBg());
        resp.setDefaultBeauty(ui.getDefaultBeauty());
    }

    /**
     * 指导弹窗获取
     *
     * @param uuid
     * @return
     */
    private AvatarGuideDTO getGuide(String uuid) {
        String key = RedisKeyConstant.AVATAR_GUIDE_POPUP.setArg(uuid);
        String value = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(uuid)) {
            return null;
        }

        return JsonUtils.stringToObject(value, AvatarGuideDTO.class);
    }

    /**
     * 是否指导弹窗实验组
     *
     * @param accountUuid
     * @param cloned
     * @return
     */
    public boolean isGuideExpGroup(String accountUuid, Integer cloned) {
        AbTestRespDto abTestRespDto = SoaService.getUserExp(accountUuid, AbRuleCode.AvatarGuideTest.KEY, CommConst.APPCODE_TAQU, cloned, "");
        boolean expGroup = AbRuleCode.AvatarGuideTest.isExpGroup(abTestRespDto.getVarias());
        LogUtil.info4Gray("头像实验入组情况:accountUuid{},{}", accountUuid, expGroup);
        return expGroup;
    }
}
