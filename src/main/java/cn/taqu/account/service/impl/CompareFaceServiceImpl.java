package cn.taqu.account.service.impl;

import cn.taqu.account.common.PrometheusMetricsEnum;
import cn.taqu.account.dao.FaceCompareLogDao;
import cn.taqu.account.dao.TransactionWrapper;
import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.dto.CompareUserDTO;
import cn.taqu.account.etcd.CompareFaceConfig;
import cn.taqu.account.model.FaceCompareLog;
import cn.taqu.account.service.AliyunLiveFaceDetectService;
import cn.taqu.account.service.CompareFaceService;
import cn.taqu.account.service.MonitorService;
import cn.taqu.account.utils.DateHelper;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.iai.v20200303.models.CompareFaceRequest;
import com.tencentcloudapi.iai.v20200303.models.CompareFaceResponse;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceSimilarityRequest;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceSimilarityResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Arrays;
import java.util.Objects;

import static cn.taqu.account.service.AliyunLiveFaceDetectService.COMPARE_UNCATCH_ERR_CODE;

/**
 * 人脸比对方法
 *
 * <AUTHOR>
 * @date 2025/7/12 07:46
 */
@Slf4j
@Service
public class CompareFaceServiceImpl implements CompareFaceService {

    @Autowired
    private ThreadPoolTaskExecutor asyncTaskExecutor;

    @Autowired
    private FaceCompareLogDao faceCompareLogDao;

    @Override
    public CompareFaceDto compare(String picOne, String picTwo, String uuid) {
        // 读取系统配置策略
        CompareFaceConfig.CompareMethod compareMethod = CompareFaceConfig.CompareMethod.getBy(CompareFaceConfig.CONFIG.getMethod());

        return compareProcess(picOne, picTwo, uuid, compareMethod);
    }

    private CompareFaceDto compareProcess(String picOne, String picTwo, String uuid, CompareFaceConfig.CompareMethod compareMethod) {
        CompareFaceDto dto = new CompareFaceDto();
        StopWatch started = StopWatch.createStarted();

        try {
            // 自行定义比对策略接口
            switch (compareMethod) {
                case CompareFace:
                    dto = compareFace(picOne, picTwo);
                    break;
                case DetectFaceSimilarity:
                    dto = detectFaceSimilarity(picOne, picTwo);
                    break;
            }

            log.info("腾讯云图片对比认证结果,uuid={},耗时=[{}]ms,图片1={},图片2={},认证结果={}, method={}",
                    uuid, started.getTime(), picOne, picTwo, JSON.toJSON(dto), compareMethod.name());
        } catch (TencentCloudSDKException e) {
            dto.setCode(e.getErrorCode());
            dto.setMsg(e.getMessage());
            dto.setRequestId(e.getRequestId());
            log.warn("腾讯云人脸对比异常,耗时=[{}]ms,图片1={},图片2={}, method={}", started.getTime(), picOne, picTwo, compareMethod.name(), e);
            dto.setScore(0.0f);
            dto.setSimilarity(false);
            if (COMPARE_UNCATCH_ERR_CODE.contains(e.getErrorCode())) {
                MonitorService.incCounterMetrices(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_IGNORE_FAIL_COUNTER);
            }
            MonitorService.incCounterMetrices(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_FAIL_COUNTER);
        } finally {
            started.stop();
            dto.setVerifyPhotoUrl(picOne);
            dto.setBasePhotoUrl(picTwo);
            dto.setCompareMethod(compareMethod);
        }

        return dto;
    }

    @Override
    public Boolean isOpenInterfaceCompare() {
        if (BooleanUtils.isFalse(CompareFaceConfig.CONFIG.getCompareSwitch())) {
            return false;
        }

        Integer today = DateHelper.getDtOfToday();
        return Arrays.stream(CompareFaceConfig.CONFIG.getCompareDate().split(","))
                .filter(StringUtils::isNoneBlank)
                .anyMatch(date -> Objects.equals(Integer.parseInt(date), today));
    }

    @Override
    public void interfaceCompare(CompareFaceDto data, CompareUserDTO user) {
        String distinctRequestId = SoaBaseParams.fromThread().getDistinctRequestId();
        Runnable task = () -> {
            // 当前比对策略
            CompareFaceConfig.CompareMethod method = data.getCompareMethod();
            // 需要比对的策略
            String compareMethod = CompareFaceConfig.CONFIG.getCompareMethod();
            CompareFaceDto compareData = compareProcess(data.getVerifyPhotoUrl(), data.getBasePhotoUrl(), user.getUuid(), CompareFaceConfig.CompareMethod.getBy(compareMethod));

            // 数据落库
            long now = Instant.now().toEpochMilli();
            Integer today = DateHelper.getDtOfToday();
            FaceCompareLog log = new FaceCompareLog();
            log.setAccountUuid(user.getUuid());
            log.setMethod(method.name());
            log.setDt(today);
            log.setCertType(user.getCert());
            log.setVerifyUrl(data.getVerifyPhotoUrl());
            log.setBaseUrl(data.getBasePhotoUrl());
            log.setScore(data.getScore());
            log.setIsSimilar(data.isSimilarity() ? 1 : 0);
            log.setErrorMsg(data.getMsg());
            log.setRequestId(data.getRequestId());
            log.setCompareMethod(compareMethod);
            log.setCompareScore(compareData.getScore());
            log.setCompareIsSimilar(compareData.isSimilarity() ? 1 : 0);
            log.setCompareErrorMsg(compareData.getMsg());
            log.setCompareRequestId(compareData.getRequestId());
            log.setCreateTime(now);
            log.setUpdateTime(now);
            TransactionWrapper.me().wrap(() -> faceCompareLogDao.merge(log));
        };
        asyncTaskExecutor.submitListenable(task).addCallback(success -> {
        }, error -> {
            SoaBaseParams.fromThread().setDistinctRequestId(distinctRequestId);
            log.warn("数据接口比对失败,data={}", JsonUtils.objectToString(data));
        });
    }

    /**
     * 策略1，具体文档地址看枚举 {@link CompareFaceConfig.CompareMethod}
     *
     * @param picOne
     * @param picTwo
     * @return
     */
    public CompareFaceDto compareFace(String picOne, String picTwo) throws TencentCloudSDKException {
        CompareFaceDto dto = new CompareFaceDto();
        // 质量管控数值
        CompareFaceConfig.QualityValid qualityValid = getQualityValid(CompareFaceConfig.CompareMethod.CompareFace);

        CompareFaceRequest req = new CompareFaceRequest();
        req.setUrlA(picOne);
        req.setUrlB(picTwo);
        req.setQualityControl(qualityValid.getQualityControl());
        CompareFaceResponse response = AliyunLiveFaceDetectService.laiClient.CompareFace(req);

        Float similarityScore = response.getScore();
        dto.setSimilarity(similarityScore >= qualityValid.getSimilarityScore());
        dto.setScore(similarityScore);
        dto.setRequestId(response.getRequestId());
        dto.setFaceModelVersion(response.getFaceModelVersion());
        dto.setRequestId(response.getRequestId());
        return dto;
    }

    /**
     * 策略2，具体文档地址看枚举 {@link CompareFaceConfig.CompareMethod}
     *
     * @param picOne
     * @param picTwo
     * @return
     */
    public CompareFaceDto detectFaceSimilarity(String picOne, String picTwo) throws TencentCloudSDKException {
        CompareFaceDto dto = new CompareFaceDto();
        // 质量管控数值
        CompareFaceConfig.QualityValid qualityValid = getQualityValid(CompareFaceConfig.CompareMethod.DetectFaceSimilarity);

        DetectFaceSimilarityRequest req = new DetectFaceSimilarityRequest();
        req.setUrlA(picOne);
        req.setUrlB(picTwo);
        req.setQualityControl(qualityValid.getQualityControl());
        DetectFaceSimilarityResponse response = AliyunLiveFaceDetectService.laiClient.DetectFaceSimilarity(req);

        Float similarityScore = response.getScore();
        dto.setSimilarity(similarityScore >= qualityValid.getSimilarityScore());
        dto.setScore(similarityScore);
        dto.setRequestId(response.getRequestId());
        dto.setFaceModelVersion("");
        dto.setRequestId(response.getRequestId());
        return dto;
    }

    /**
     * 获取质量管控
     */
    private CompareFaceConfig.QualityValid getQualityValid(CompareFaceConfig.CompareMethod compareMethod) {
        return CompareFaceConfig.CONFIG.getQualityValidMap().get(compareMethod.name());
    }
}
