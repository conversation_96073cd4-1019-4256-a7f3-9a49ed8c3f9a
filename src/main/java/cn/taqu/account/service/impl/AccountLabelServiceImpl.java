package cn.taqu.account.service.impl;

import cn.taqu.account.config.biz.FriendshipPreferLabelConfig;
import cn.taqu.account.constant.ActionEventEnum;
import cn.taqu.account.constant.PersonalityLabelEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsLabelDao;
import cn.taqu.account.dao.PersonalityLabelDao;
import cn.taqu.account.event.ActionNoticeReport;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.model.AccountsLabel;
import cn.taqu.account.model.PersonalityLabel;
import cn.taqu.account.service.AccountLabelService;
import cn.taqu.account.service.AccountsLabelService;
import cn.taqu.account.service.PersonalityLabelService;
import cn.taqu.account.vo.resp.FriendshipPreferLabelResp;
import cn.taqu.core.exception.ServiceException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户标签
 *
 * <AUTHOR>
 * @date 2024/10/15 10:51 上午
 */
@Service
public class AccountLabelServiceImpl implements AccountLabelService {

    @Resource
    private StringRedisTemplate accountStringRedisTemplate;
    @Resource
    private PersonalityLabelService personalityLabelService;
    @Resource
    private PersonalityLabelDao personalityLabelDao;
    @Resource
    private AccountBaseInfoManager accountBaseInfoManager;
    @Resource
    private AccountsLabelDao accountsLabelDao;
    @Resource
    private AccountsLabelService accountsLabelService;
    @Resource
    private ActionNoticeReport actionNoticeReport;

    @Override
    public FriendshipPreferLabelResp getFriendshipPreferLabel(String accountUuid) {
        FriendshipPreferLabelResp resp = new FriendshipPreferLabelResp();

        // 所有标签缓存
        List<PersonalityLabel> labels = listLabelByCache(PersonalityLabelEnum.FRIENDSHIP_PREFERENCES.getType());
        Integer sexType = accountBaseInfoManager.getGender(accountUuid);
        List<FriendshipPreferLabelResp.LabelInfo> allLabel = labels.stream().filter(label -> label.match(sexType)).map(label -> {
            FriendshipPreferLabelResp.LabelInfo labelInfo = new FriendshipPreferLabelResp.LabelInfo();
            labelInfo.setId(label.getId());
            labelInfo.setText(label.getContent());
            labelInfo.setSort(label.getSort());
            return labelInfo;
        }).collect(Collectors.toList());

        // 获取用户已设置的交友偏好标签
        List<FriendshipPreferLabelResp.LabelInfo> selectedLabels = querySelectedFriendshipPreferLabel(accountUuid, labels);

        // 获取交友偏好配置
        FriendshipPreferLabelConfig.LabelConfig labelConfig = FriendshipPreferLabelConfig.labelConfig;

        resp.setPreferTabAll(allLabel);
        resp.setSelectedPreferTab(selectedLabels);
        resp.setTitle(labelConfig.getTitle());
        resp.setSubTitle(labelConfig.getSubTitle());
        resp.setLowerLimit(labelConfig.getLowerLimit());
        resp.setUpperLimit(labelConfig.getUpperLimit());
        return resp;
    }

    @Override
    public void saveFriendshipPreferLabel(String accountUuid, String ids) {
        // 简单校验
        validSaveFriendshipPreferLabelParam(ids);

        accountsLabelService.setAccountLabel(accountUuid, PersonalityLabelEnum.FRIENDSHIP_PREFERENCES.getType(), ids);

        // 推到行为埋点里
        actionNoticeReport.report(accountUuid, ActionEventEnum.FRIENDSHIP_LABEL_SETTING);
    }

    @Override
    public List<String> listSetPersonalityLabel(String accountUuid) {
        // 查缓存id
        String redisKey = RedisKeyConstant.ACCOUNT_LABEL.setArg(accountUuid);
        Object label = accountStringRedisTemplate.opsForHash().get(redisKey, "friendship_prefer_label");
        if (label == null) {
            return Collections.emptyList();
        }
        if (label instanceof String) {
            if (StringUtils.isBlank(String.valueOf(label))) {
                return Collections.emptyList();
            }
        }

        // id转具体内容
        List<String> ids = Arrays.stream(String.valueOf(label).split(",")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<PersonalityLabel> labels = personalityLabelService.findListByCache(ids, PersonalityLabelEnum.FRIENDSHIP_PREFERENCES.getType());
        if (CollectionUtils.isEmpty(labels)) {
            return Collections.emptyList();
        }

        return labels.stream().map(PersonalityLabel::getContent).collect(Collectors.toList());
    }

    /**
     * 交友保存交友偏好参数
     *
     * @param ids
     */
    private void validSaveFriendshipPreferLabelParam(String ids) {
        Integer lowerLimit = FriendshipPreferLabelConfig.labelConfig.getLowerLimit();
        Integer upperLimit = FriendshipPreferLabelConfig.labelConfig.getUpperLimit();
        int length = ids.split(",").length;
        if (length < lowerLimit) {
            throw new ServiceException("至少选择4个标签");
        }
        if (length > upperLimit) {
            throw new ServiceException("最多选择15个标签");
        }
    }

    /**
     * 获取用户已选的偏好标签
     *
     * @param accountUuid
     * @param labels
     * @return
     */
    private List<FriendshipPreferLabelResp.LabelInfo> querySelectedFriendshipPreferLabel(String accountUuid, List<PersonalityLabel> labels) {
        AccountsLabel accountsLabel = accountsLabelDao.findByAccountUuid(accountUuid);
        if (accountsLabel == null) {
            return Collections.emptyList();
        }

        String friendshipPrefer = accountsLabel.getFriendship_prefer_label();
        if (StringUtils.isBlank(friendshipPrefer)) {
            return Collections.emptyList();
        }

        List<Long> labelIds = Arrays.stream(friendshipPrefer.split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(labelIds)) {
            return Collections.emptyList();
        }

        Map<Long, PersonalityLabel> collect = labels.stream().collect(Collectors.toMap(PersonalityLabel::getId, Function.identity(), (k1, k2) -> k1));
        return labelIds.stream().map(labelId -> {
            PersonalityLabel label = collect.get(labelId);
            if (label != null) {
                FriendshipPreferLabelResp.LabelInfo resp = new FriendshipPreferLabelResp.LabelInfo();
                resp.setId(labelId);
                resp.setText(label.getContent());
                resp.setSort(label.getSort());
                return resp;
            }
            return null;
        }).limit(100).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取缓存标签
     *
     * @param type
     * @return
     */
    public List<PersonalityLabel> listLabelByCache(Integer type) {
        Set<String> ids = accountStringRedisTemplate.opsForSet().members(RedisKeyConstant.PERSONALITY_LABEL_TYPE_ID.setArg(type));
        if (ids == null || ids.isEmpty()) {
            List<Long> idList = personalityLabelDao.getAllByType(type);
            if (CollectionUtils.isEmpty(idList)) {
                return Collections.emptyList();
            }

            ids = idList.stream().map(String::valueOf).collect(Collectors.toSet());
            accountStringRedisTemplate.opsForSet().add(RedisKeyConstant.PERSONALITY_LABEL_ID_SET.getPattern(), ids.toArray(new String[0]));
        }

        return personalityLabelService.findListByCache(ids, type);
    }

}

