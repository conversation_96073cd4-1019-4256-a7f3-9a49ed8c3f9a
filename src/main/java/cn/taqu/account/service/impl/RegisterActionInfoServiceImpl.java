package cn.taqu.account.service.impl;

import cn.taqu.account.dao.RegisterActionInfoDao;
import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.account.service.RegisterActionInfoService;
import cn.taqu.account.vo.resp.RegisterActionInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/15 17:59
 */
@Slf4j
@Service
public class RegisterActionInfoServiceImpl implements RegisterActionInfoService {

    @Resource
    private RegisterActionInfoDao registerActionInfoDao;

    @Override
    public Map<String, RegisterActionInfoResp> listInfo(List<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            log.info("入参为空");
            return null;
        }

        // 长度管控
        uuids = uuids.subList(0, Math.min(uuids.size(), 200));
        List<RegisterActionInfo> list = registerActionInfoDao.listInfo(uuids);
        Map<String, RegisterActionInfo> infoMap = list.stream().collect(Collectors.toMap(RegisterActionInfo::getAccountUuid, Function.identity(), (k1, k2) -> k1));

        return uuids.stream().map(uuid -> {
            RegisterActionInfo info = infoMap.get(uuid);
            RegisterActionInfoResp resp = new RegisterActionInfoResp();
            resp.setAccountUuid(uuid);
            resp.setPlatformId(Optional.ofNullable(info).map(RegisterActionInfo::getPlatformId).orElse(null));
            resp.setAppVersion(Optional.ofNullable(info).map(RegisterActionInfo::getAppVersion).orElse(null));
            resp.setCreateTime(Optional.ofNullable(info).map(RegisterActionInfo::getCreateTime).orElse(null));
            return resp;
        }).collect(Collectors.toMap(RegisterActionInfoResp::getAccountUuid, Function.identity(), (k1, k2) -> k1));
    }
}
