package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.dao.AccountsDestroyRenewLogDao;
import cn.taqu.account.model.AccountsDestroyRenewLog;
import cn.taqu.core.utils.DateUtil;

/**
 * 用户注销恢复记录表
 * 
 * <AUTHOR>
 * @date 2021/04/13
 */
@Service
public class AccountsDestroyRenewLogService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsDestroyRenewLog> {

    @Autowired
    private AccountsDestroyRenewLogDao accountsDestroyRenewLogDao;

    @Transactional
    public void addLog(String accountUuid, String operateName) {
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        AccountsDestroyRenewLog log = new AccountsDestroyRenewLog();
        log.setAccountUuid(accountUuid);
        log.setCreateTime(currentTimeSeconds);
        log.setUpdateTime(currentTimeSeconds);
        log.setOperateName(operateName);
        accountsDestroyRenewLogDao.merge(log);
    }

}
