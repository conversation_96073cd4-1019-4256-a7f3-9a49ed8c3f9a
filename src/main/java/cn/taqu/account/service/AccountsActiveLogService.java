package cn.taqu.account.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.common.AccountActiveStatus;
import cn.taqu.account.dao.AccountsActiveLogDao;
import cn.taqu.account.model.AccountsActiveLog;
import cn.taqu.core.utils.DateUtil;

/**
 * <AUTHOR>
 * @Classname AccountsActiveLogService
 * @Description 用户活跃记录表
 * @Date 2020/12/14 上午9:41
 */
@Service
public class AccountsActiveLogService {
    /**
     * "用户最近活跃日期"的accounts_active_log表保存个数
     */
    private static final int COUNT_OF_ACCOUNT_ACTIVE_CACHES = AccountActiveStatus.COUNT_OF_ACCOUNT_ACTIVE_CACHES.getValue();

    @Autowired
    private AccountsActiveLogDao accountsActiveLogDao;


    public void insertIgnore(String accountUuid, Long activeTime) {
        accountsActiveLogDao.insertIgnore(
                accountUuid,
                Integer.parseInt(DateUtil.dateToString8(DateUtil.fromSecond(activeTime)))
        );
    }

    public List<Integer> findByUuid(String accountUuid) {
        return accountsActiveLogDao.findByUuid(accountUuid);
    }

    @Transactional
    public void cleanActiveLogByUuid(String uuid) {
        List<Long> Ids = accountsActiveLogDao.findIdByUuid(uuid);
        if (Ids.size() > COUNT_OF_ACCOUNT_ACTIVE_CACHES) {
            List<Long> cleanIds = Ids.subList(COUNT_OF_ACCOUNT_ACTIVE_CACHES, Ids.size());
            accountsActiveLogDao.removeByIds(cleanIds);
        }
    }

    /**
     * 获取某一天的起始id
     * @param activeTime
     * @return
     */
    public Long getAccountsActiveStartIdInDay(Long activeTime){
        return accountsActiveLogDao.getAccountsActiveStartIdInDay(activeTime);
    }

    /**
     *
     * @param activeTime
     * @param startId
     * @param size
     * @return
     */
    public List<AccountsActiveLog> getAccountsActiveList(Long activeTime, Long startId, Integer size){
        return accountsActiveLogDao.getAccountsActive(activeTime, startId, size);
    }
}
