package cn.taqu.account.service;

import java.util.regex.Pattern;

import cn.taqu.core.utils.Md5Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.taqu.account.bo.Attribution;
import cn.taqu.account.common.AccountActionTypeEnum;
import cn.taqu.account.dao.AccountsInfoDao;
import cn.taqu.account.dao.LoginActionInfoDao;
import cn.taqu.account.dao.RegisterActionInfoDao;
import cn.taqu.account.dto.AccountAction;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.Accounts;
import cn.taqu.account.model.AccountsInfo;
import cn.taqu.account.model.LoginActionInfo;
import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.exception.ServiceException;

@Service
public class AccountActionProfileService {

    private static Logger LOGGER = LoggerFactory.getLogger(AccountActionProfileService.class);

    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    private LoginActionInfoDao loginActionInfoDao;
    @Autowired
    private RegisterActionInfoDao registerActionInfoDao;

    public AccountAction getAccountActionByAccountUuid(String accountUuid, AccountActionTypeEnum accountActionType) {
        if (!AccountActionTypeEnum.REGISTER.equals(accountActionType) && !AccountActionTypeEnum.LOGIN.equals(accountActionType)) {
            throw new ServiceException("行为类型只能为登录或注册");
        }

        AccountAction accountAction = new AccountAction();

        Accounts account = accountsManager.getByUuid(accountUuid);
        if (account == null) {
            LOGGER.warn("查询用户为null, accountUuid={}", accountUuid);
            return null;
        }
        AccountsInfo accountInfo = accountsInfoDao.getByAccountUuid(accountUuid);
        if (accountInfo == null) {
            LOGGER.warn("查询用户信息为null, accountUuid={}", accountUuid);
            return null;
        }

        accountAction.setAliTagsStr("");
        accountAction.setActionType(accountActionType);
        accountAction.setAccountUuid(accountUuid);
        accountAction.setAccountName(account.getAccount_name());
        accountAction.setSexType(account.getSex_type());
        // TODO 需要下线
        //accountAction.setPhone(account.getMobile());
        accountAction.setPhoneMD5(Md5Util.encode(account.getMobile()));
        accountAction.setVoiceCertification(accountInfo.getVoice_certification());

        Attribution attribution = parseAttribution(account.getMobile_place());
        accountAction.setPhoneAttributionProvince(attribution.getProvince());
        accountAction.setPhoneAttributionCity(attribution.getCity());

        accountAction.setAliyunFinalDecision(accountInfo.getAliyun_final_decision());

        if (AccountActionTypeEnum.LOGIN.equals(accountActionType)) {
            LoginActionInfo loginActionInfo = loginActionInfoDao.getByUuid(accountUuid);
            if (loginActionInfo == null) {
                LOGGER.warn("查询用户登录信息为null, accountUuid={}", accountUuid);
                return null;
            }
            accountAction.setToken(loginActionInfo.getToken());
            accountAction.setActionIp(loginActionInfo.getActionIp());
            accountAction.setAppcode(loginActionInfo.getAppcode());
            accountAction.setCloned(loginActionInfo.getCloned());
            accountAction.setPlatformId(loginActionInfo.getPlatformId());
            accountAction.setChannel(loginActionInfo.getChannel());
            accountAction.setAppVersion(loginActionInfo.getAppVersion());
            accountAction.setActionMode(loginActionInfo.getActionMode());
            accountAction.setPlatformName(loginActionInfo.getPlatformName());
            accountAction.setAccess(loginActionInfo.getAccess());
            accountAction.setAlias(loginActionInfo.getAlias());
            accountAction.setGender(loginActionInfo.getGender());
            
            if(ToolsService.loginActionInfoSwitchEncryption.isOn(true)) {
                // 开关开启，使用加密字段
                accountAction.setLongitude(EncryptUtil.decrypt(loginActionInfo.getLongitudeCipher()));
                accountAction.setLatitude(EncryptUtil.decrypt(loginActionInfo.getLatitudeCipher()));
            }else {
                accountAction.setLongitude(loginActionInfo.getLongitude());
                accountAction.setLatitude(loginActionInfo.getLatitude());
            }
            
            accountAction.setCity(loginActionInfo.getCity());
        } else {
            RegisterActionInfo registerActionInfo = registerActionInfoDao.getByUuid(accountUuid);
            if (registerActionInfo == null) {
                LOGGER.warn("查询用户注册信息为null, accountUuid={}", accountUuid);
                return null;
            }
            accountAction.setToken(registerActionInfo.getToken());
            accountAction.setActionIp(registerActionInfo.getActionIp());
            accountAction.setAppcode(registerActionInfo.getAppcode());
            accountAction.setCloned(registerActionInfo.getCloned());
            accountAction.setPlatformId(registerActionInfo.getPlatformId());
            accountAction.setChannel(registerActionInfo.getChannel());
            accountAction.setAppVersion(registerActionInfo.getAppVersion());
            accountAction.setActionMode(registerActionInfo.getActionMode());
            accountAction.setPlatformName(registerActionInfo.getPlatformName());
            accountAction.setAccess(registerActionInfo.getAccess());
            accountAction.setAlias(registerActionInfo.getAlias());
            accountAction.setGender(registerActionInfo.getGender());
            
            if(ToolsService.registerActionInfoSwitchEncryption.isOn(true)) {
                // 开关开启，使用加密字段
                accountAction.setLongitude(EncryptUtil.decrypt(registerActionInfo.getLongitudeCipher()));
                accountAction.setLatitude(EncryptUtil.decrypt(registerActionInfo.getLatitudeCipher()));
            }else {
                accountAction.setLongitude(registerActionInfo.getLongitude());
                accountAction.setLatitude(registerActionInfo.getLatitude());
            }
            
            accountAction.setCity(registerActionInfo.getCity());
        }

        return accountAction;
    }

    private Attribution parseAttribution(String attribution) {
        Attribution result = new Attribution();
        if (StringUtils.isBlank(attribution)) {
            return result;
        }
        String[] split = attribution.split(",");
        for (int i = 0; i < split.length; i++) {
            String str = split[i];
            if (0 == i) {
                result.setProvince(str.trim());
            }
            if (1 == i) {
                result.setCity(str.trim());
            }
        }
        return result;
    }

    public AccountAction getAccountInfo(String accountUuid) {
        AccountAction accountAction = new AccountAction();
        Accounts account = accountsManager.getByUuid(accountUuid);
        accountAction.setAccountUuid(accountUuid);
        if(account != null) {
        	accountAction.setAccountName(account.getAccount_name());
        	accountAction.setPhone(account.getMobile());
        	accountAction.setPhoneAttribution(account.getMobile_place());
        }

        AccountsInfo accountInfo = accountsInfoDao.getByAccountUuid(accountUuid);
        if(accountInfo != null) {
        	accountAction.setAliyunFinalDecision(accountInfo.getAliyun_final_decision());
        	accountAction.setVoiceCertification(accountInfo.getVoice_certification());
        }

        return accountAction;
    }

    //系统默认昵称
    private static Pattern systemAccountNameRegx = Pattern.compile("(新用户|用户|违规昵称)[a-zA-Z0-9]{11,16}$|昵称关禁闭中");
    //违规昵称
    private static Pattern violationAccountNameRegx = Pattern.compile("违规昵称[a-zA-Z0-9]{11,16}$|昵称关禁闭中");

    /**
     * 判断是否是系统默认昵称
     *
     * <AUTHOR>
     * @date 2020/08/10 15:09
     * @param accountName
     * @return
     */
    public static boolean isSystemAccountName(String accountName) {
        if(StringUtils.isBlank(accountName)) {
            return false;
        }

        return systemAccountNameRegx.matcher(accountName.trim()).matches();
    }

    /**
     * 判断是否是空昵称or违规昵称
     * @param accountName
     * @return
     */
    public static boolean isViolationAccountName(String accountName) {
        if(StringUtils.isBlank(accountName)) {
            return true;
        }

        return violationAccountNameRegx.matcher(accountName.trim()).matches();
    }

}
