package cn.taqu.account.service;

import cn.taqu.account.common.ActionTypeEnum;
import cn.taqu.account.common.WordTypeEnum;
import cn.taqu.account.dao.RandomGroupDao;
import cn.taqu.account.dao.RandomNicknameDao;
import cn.taqu.account.model.RandomGroup;
import cn.taqu.account.model.RandomNickname;
import cn.taqu.account.search.RandomNicknameSearch;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: zy
 * @Date: 2020/6/23 15:05
 */
@Service
public class RandomNicknameService {
    @Autowired
    private RandomNicknameDao randomNicknameDao;
    @Autowired
    private RandomGroupDao randomGroupDao;

    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;


    public PageData<RandomNickname> findPageList(RandomNicknameSearch search) {
        PageData<RandomNickname> pageData = randomNicknameDao.findPageList(search);
        return pageData;
    }


    @Transactional
    public void batchAdd(String[] randomNickNameArr, Integer sexType, Integer status, Long groupId, String clone) {
        if (StringUtils.isBlank(clone)) {
            throw new ServiceException("clone_is_null", "clone值不能为空");
        }

        String[] cloneArr = clone.split(",");


        List<String> nameList = Arrays.asList(randomNickNameArr);
        RandomGroup randomGroup = randomGroupDao.getById(groupId);
        Integer partsOfSpeech = randomGroup.getPartsOfSpeech();


        for (String cloneValue : cloneArr) {
            List<RandomNickname> randomNicknameList = new ArrayList<>();
            for (String s : randomNickNameArr) {
                RandomNickname randomNickname = new RandomNickname();
                randomNickname.setGroupId(groupId);
                randomNickname.setStatus(status);
                randomNickname.setSexType(sexType);
                randomNickname.setRandomNickname(s);
                randomNickname.setCreateTime(DateUtil.currentTimeSeconds());
                randomNickname.setUpdateTime(DateUtil.currentTimeSeconds());
                randomNickname.setCloned(Long.parseLong(cloneValue));
                randomNicknameList.add(randomNickname);
            }
            randomNicknameDao.merge(randomNicknameList);
            Long cloneLongValue = Long.parseLong(cloneValue);
            if (cloneLongValue.longValue() == 1L) {
                accountsThirdPartyService.updateRandomNickname(nameList, switchType(partsOfSpeech, sexType), ActionTypeEnum.ADD);
            } else if (cloneLongValue.longValue() == 50L) {
                accountsThirdPartyService.updatePeipeiRandomNickname(nameList, switchType(partsOfSpeech, sexType), ActionTypeEnum.ADD);
            }else{
                accountsThirdPartyService.updateRandomNickname(nameList, switchType(partsOfSpeech, sexType), ActionTypeEnum.ADD);
            }
        }

        //更新缓存


    }

    public WordTypeEnum switchType(Integer partsOfSpeech, Integer sexType) {
        if (partsOfSpeech.intValue() == 1 && sexType.intValue() == 1) {
            return WordTypeEnum.MALE_ADJ;
        }
        if (partsOfSpeech.intValue() == 2 && sexType.intValue() == 1) {
            return WordTypeEnum.MALE_NOUN;
        }
        if (partsOfSpeech.intValue() == 1 && sexType.intValue() == 2) {
            return WordTypeEnum.FEMALE_ADJ;
        }
        if (partsOfSpeech.intValue() == 2 && sexType.intValue() == 2) {
            return WordTypeEnum.FEMALE_NOUN;
        }
        throw new ServiceException("parts_of_speech_type_error", "随机昵称词性类型错误");
    }

    @Transactional
    public void deleteById(Long id) {
        RandomNickname randomNickname = randomNicknameDao.findById(id);
        Integer sexType = randomNickname.getSexType();
        RandomGroup randomGroup = randomGroupDao.getById(randomNickname.getGroupId());
        Integer partsOfSpeech = randomGroup.getPartsOfSpeech();
        List<String> nameList = Arrays.asList(randomNickname.getRandomNickname());

        Long cloned = randomNickname.getCloned();
        randomNicknameDao.removeById(id);
        if (cloned.longValue() == 1L) {
            accountsThirdPartyService.updateRandomNickname(nameList, switchType(partsOfSpeech, sexType), ActionTypeEnum.DELETE);
        } else if (cloned.longValue() == 50L) {
            accountsThirdPartyService.updatePeipeiRandomNickname(nameList, switchType(partsOfSpeech, sexType), ActionTypeEnum.DELETE);
        } else {
            accountsThirdPartyService.updateRandomNickname(nameList, switchType(partsOfSpeech, sexType), ActionTypeEnum.DELETE);
        }
    }
}
