package cn.taqu.account.service;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsGrayListDao;
import cn.taqu.account.model.AccountsGrayList;
import cn.taqu.account.utils.AccountUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.PageUtil;

/**
 * 灰名单
 *
 * <AUTHOR>
 * @date 2019/09/12 11:12
 */
@Service
@Transactional
public class AccountsGrayListService {

	private static final Logger LOGGER = LoggerFactory.getLogger(AccountsGrayListService.class);

	@Autowired
	private AccountsInfoService accountsInfoService;
	@Autowired
	private AccountsGrayListDao accountsGrayListDao;
	@Autowired
	private AccountsMemberInfoService accountsMemberInfoService;
	@Autowired
	private RedisLockUtil redisLockUtil;

	/**
	 * 添加用户灰名单
	 *
	 * @param uuidOrToken   用户uuid或token
	 * @param remark 备注(如添加的原因等)
	 */
	public void add(String uuidOrToken, String remark, String operateName) {
		if (StringUtils.isBlank(uuidOrToken)) {
			return;
		}

		String grayListRedisKey = RedisKeyConstant.REDIS_LOCK_KEY.setArg("accountsGrayList", uuidOrToken);
		if (!redisLockUtil.lock(grayListRedisKey, 5000)) {
			LOGGER.warn("重复加入灰名单，uuidOrToken：{}", uuidOrToken);
			return;
		}

		try {
			boolean result;
			//如果是uuid
			if (AccountUtil.checkUuid(uuidOrToken)) {
				result = this.addByUuid(uuidOrToken, remark, operateName);
			} else {
				result = this.addByToken(uuidOrToken, remark, operateName);
			}

			if(!result) {
				throw new ServiceException("data_error", "不是正确的账号uuid或者设备token");
			}
		} finally {
			redisLockUtil.unLock(grayListRedisKey);
		}
	}

	private boolean addByUuid(String accountUuid, String remark, String operateName) {
		List<String> tokenList = accountsMemberInfoService.findTokenByAccountUuid(accountUuid);
		if (tokenList == null || tokenList.size() == 0) {
			return false;
		}

		Long currentTimeSeconds = DateUtil.currentTimeSeconds();

		Map<String, Map<String, Object>> accountInfoMap = accountsInfoService
				.getInfoByUuid(new String[] { accountUuid }, new String[] { "account_name" }, "1", false, false);
		String accountName = MapUtils.getString(accountInfoMap.get(accountUuid), "account_name", "");

		List<AccountsGrayList> addList = Lists.newArrayList();
		for (String token : Sets.newHashSet(tokenList)) {
			AccountsGrayList accountsGrayList = new AccountsGrayList();
			accountsGrayList.setAccountUuid(accountUuid);
			accountsGrayList.setAccountName(accountName);
			accountsGrayList.setToken(token);
			accountsGrayList.setOperateName(operateName);
			accountsGrayList.setRemark(remark);
			accountsGrayList.setCreateTime(currentTimeSeconds);
			accountsGrayList.setUpdateTime(currentTimeSeconds);
			addList.add(accountsGrayList);
		}

		accountsGrayListDao.merge(addList);
		return true;
	}

	private boolean addByToken(String token, String remark, String operateName) {
		Long currentTimeSeconds = DateUtil.currentTimeSeconds();

		List<String> uuidList = accountsMemberInfoService.findAccountUuidByToken(token);
		if (uuidList == null || uuidList.size() == 0) {
			AccountsGrayList accountsGrayList = new AccountsGrayList();
			accountsGrayList.setAccountUuid("");
			accountsGrayList.setAccountName("");
			accountsGrayList.setToken(token);
			accountsGrayList.setOperateName(operateName);
			accountsGrayList.setRemark(remark);
			accountsGrayList.setCreateTime(currentTimeSeconds);
			accountsGrayList.setUpdateTime(currentTimeSeconds);
			accountsGrayListDao.merge(accountsGrayList);
			return true;
		}

		Map<String, Map<String, Object>> accountInfoMap = accountsInfoService
				.getInfoByUuid(uuidList.toArray(new String[uuidList.size()]), new String[] { "account_name" }, "1", false, false);

		List<AccountsGrayList> addList = Lists.newArrayList();
		for (String accountUuid : Sets.newHashSet(uuidList)) {
			AccountsGrayList accountsGrayList = new AccountsGrayList();
			accountsGrayList.setAccountUuid(accountUuid);
			accountsGrayList.setAccountName(MapUtils.getString(accountInfoMap.get(accountUuid), "account_name", ""));
			accountsGrayList.setToken(token);
			accountsGrayList.setOperateName(operateName);
			accountsGrayList.setRemark(remark);
			accountsGrayList.setCreateTime(currentTimeSeconds);
			accountsGrayList.setUpdateTime(currentTimeSeconds);
			addList.add(accountsGrayList);
		}

		accountsGrayListDao.merge(addList);
		return true;
	}

    public void batchAdd(String[] uuidOrTokens, String remark, String operateName) {
    	for (String uuidOrToken : uuidOrTokens) {
    		add(uuidOrToken, remark, operateName);
		}
    }

	/**
	 * 移除用户灰名单
	 *
	 * @param uuidOrToken 用户uuid或设备token
	 */
	public void remove(String uuidOrToken) {
		if(AccountUtil.checkUuid(uuidOrToken)) {
			this.removeByUuid(uuidOrToken);
		} else {
			this.removeByToken(uuidOrToken);
		}
	}

	/**
	 * 根据id移除灰名单用户
	 * @param id
	 */
	public void removeById(Long id) {
		AccountsGrayList accountsGrayList = accountsGrayListDao.get(id);
		if(accountsGrayList == null) {
			throw new ServiceException("graylist_not_exist", "数据不存在，可能已经被删除，请刷新页面后重试");
		}

		/*String accountUuid = accountsGrayList.getAccountUuid();
		if(StringUtils.isNotBlank(accountUuid)) {
			this.removeByUuid(accountUuid);
		}

		String token = accountsGrayList.getToken();
		if(StringUtils.isNotBlank(token)) {
			this.removeByToken(token);
		}*/

		accountsGrayListDao.removeById(id);
	}

	private void removeByUuid(String uuid) {
		List<AccountsGrayList> list = accountsGrayListDao.getByUuid(uuid);
		if (list == null) {
			return;
		}
		// 删除此账号所有关联的token
		List<String> tokenList = list.stream().filter(item -> StringUtils.isNotBlank(item.getToken())).map(item -> item.getToken()).collect(Collectors.toList());
		accountsGrayListDao.deleteInToken(tokenList);
		LOGGER.info("成功删除所有用户{}, 关联tokenList={}", uuid, tokenList);
	}

	private void removeByToken(String token) {
		List<AccountsGrayList> list = accountsGrayListDao.getByToken(token);
		if (list == null) {
			return;
		}
		// 删除此账号所有关联的token
		List<String> uuidList = list.stream().filter(item -> StringUtils.isNotBlank(item.getAccountUuid())).map(item -> item.getAccountUuid()).collect(Collectors.toList());
		accountsGrayListDao.deleteInUuid(uuidList);
		LOGGER.info("成功删除所有设备{}, 关联uuidList={}", token, uuidList);
	}

	/**
	 * 判断用户是否在灰名单中 先判断设备，再判断uuid
	 *
	 * @param token
	 * @param accountUuid
	 * @return
	 */
	public boolean isInGrayList(String token, String accountUuid) {
		boolean flag = false;
		if (StringUtils.isNotBlank(token)) {
			//直接count查询效率高
			//flag = accountsGrayListDao.exists("token", token, Collections.singleton("token"));
			flag = accountsGrayListDao.countByToken(token) > 0;
		}
		if (flag) {
			return flag;
		}
		if (StringUtils.isNotBlank(accountUuid)) {
			//flag = accountsGrayListDao.exists("account_uuid", accountUuid, Collections.singleton("account_uuid"));
			flag = accountsGrayListDao.countByUuid(accountUuid) > 0;
		}
		return flag;
	}

	/**
	 * 用户灰名单查询
	 *
	 * @param params
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	public Page<AccountsGrayList> getAccountsGrayList(Map<String, Object> params, Integer pageNumber,
			Integer pageSize) {
		String accountUuid = MapUtils.getString(params, "account_uuid", null);
		String token = MapUtils.getString(params, "token", null);
		Collection<String> fields = Arrays.asList("id", "account_uuid", "token", "account_name", "remark",
				"operate_name", "create_time","update_time");
		Sql sql = Sql.build(AccountsGrayList.class, fields).eq("account_uuid", accountUuid).eq("token", token);
		sql.orderBy("create_time desc");

		PageData<AccountsGrayList> page = accountsGrayListDao.queryForPage(sql, pageNumber, pageSize);
		return new PageImpl<>(page.getData(), PageUtil.newPageable(pageNumber + 1, pageSize), page.getTotal());
	}

	/**
	 * 异步 校验token是否需要加入
	 * http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
	 *
	 * </br>
	 * 当A账号被添加为恶意用户后，登录他趣时，均记录其设备号，如果设备号是新的，也会被添加为限制设备。
	 *
	 * @param accountUuid
	 * @param token
	 */
	@Async
	public void checkTokenJoinList(String accountUuid, String token) {
		LOGGER.info("判断token是否需要加入灰名单accountUuid={},token={}", accountUuid, token);
		String redisKey = RedisKeyConstant.REDIS_LOCK_KEY.setArg("accountsGrayList", accountUuid + "-" + token);
		// 锁定1分钟
		if (!redisLockUtil.lock(redisKey, 60000)) {
			return;
		}
		try {
			// 判断是否是已受限用户
			if (!isInGrayList("", accountUuid)) {
				return;
			}

			// 判断该对应数据是否已存在数据库
			AccountsGrayList accountsGrayList = accountsGrayListDao.findByAccountUuidAndToken(accountUuid, token);
			if (accountsGrayList != null) {
				return;
			}
			// 判断是否是唯一值
			List<String> tokenList = accountsMemberInfoService.findTokenByAccountUuid(accountUuid);
			// 因为是异步处理，accountUuid与token可能已经关联，简单处理：只要查到只有一条记录，即表示是首次关联用户
			if (tokenList == null || tokenList.size() == 0
					|| (tokenList.size() == 1 && Objects.equals(tokenList.get(0), token))) {

				Long currentTimeSeconds = DateUtil.currentTimeSeconds();

				// 设备加入灰名单
				AccountsGrayList add = new AccountsGrayList();
				add.setAccountUuid(accountUuid);
				add.setAccountName(
						MapUtils.getString(
								accountsInfoService.getInfoByUuid(new String[]{accountUuid},
										new String[]{"account_name"}, "1", false, false).get(accountUuid),
								"account_name", ""));
				add.setToken(token);
				add.setOperateName("自动添加");
				add.setRemark("此设备为灰名单用户新使用设备");
				add.setCreateTime(currentTimeSeconds);
				add.setUpdateTime(currentTimeSeconds);

				accountsGrayListDao.merge(add);
				LOGGER.info("自动添加灰名单accountUuid={},token={}", accountUuid, token);
			}
		} finally {
			redisLockUtil.unLock(redisKey);
		}
	}

	public AccountsGrayList getDetail(String token, String accountUuid) {
		AccountsGrayList accountsGrayList = null;
		if (StringUtils.isNotBlank(token)) {
			accountsGrayList = accountsGrayListDao.getOneByToken(token);
		}
		if (accountsGrayList != null) {
			return accountsGrayList;
		}
		if (StringUtils.isNotBlank(accountUuid)) {
			accountsGrayList = accountsGrayListDao.getOneByUuid(accountUuid);
		}
		return accountsGrayList;
	}

    /**
     * @param uuidArr
     * @return
     */
    public Map<String, Boolean> accountIsInGrayListBatch(String[] uuidArr) {
        Map<String, Boolean> result = Maps.newHashMap();
        for (String accountUuid : uuidArr) {
            if(accountsGrayListDao.countByUuid(accountUuid) > 0) {
                result.put(accountUuid, true);
            }else {
                result.put(accountUuid, false);
            }
        }
        return result;
    }

}
