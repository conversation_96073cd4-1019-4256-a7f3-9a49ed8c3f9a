package cn.taqu.account.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.taqu.account.dao.AccountsGuestDao;
import cn.taqu.account.model.AccountsGuest;
import cn.taqu.account.model.MembersAppInfo;

/**
 * 主要用于画像系统查询用户系统中的数据
 * Created by <PERSON><PERSON><PERSON> on 2017/4/19.
 */
@Service
public class TriggerOperateService {
    @Resource
    private AccountsService accountsService;
    @Resource
    private AccountsInfoService accountsInfoService;
    @Resource
    private AccountsForumProfileService accountsForumProfileService;
    @Resource
    private MembersService membersService;
    @Resource
    private MembersAppInfoService membersAppInfoService;
    @Resource
    private AccountsGuestDao accountsGuestDao;

    public Map<String, Object> getAccountInfoByAccountUuid(String accountUuid) {
        Map<String, Object> result = new HashMap<>();
        if(StringUtils.isBlank(accountUuid)) {
            return result;
        }

        List<String> accountUuids = new ArrayList<>();
        accountUuids.add(accountUuid);
        List<Map<String,Object>> accountFileds = accountsService.getFieldByUuidFromDb(Arrays.asList("account_type", "account_status", "sex_type", "uuid", "create_time", "account_id", "member_id", "last_logintime", "appcode", "cloned", "account_name", "reg_style", "channel", "platform_id"), accountUuids);

        //不是注册账号，查询游客表
        if(accountFileds == null || accountFileds.isEmpty()) {
            AccountsGuest accountsGuest = accountsGuestDao.getByUuid(accountUuid);
            //也不是游客，直接返回空
            if(accountsGuest == null) {
                return result;
            }

            //是游客，返回游客数据
            result.put("account_uuid", accountUuid);
            result.put("account_name", accountsGuest.getAccount_name());
            result.put("account_type", "2");
            result.put("account_status", "1");
            result.put("sex_type", accountsGuest.getSex_type());
            result.put("uuid", accountsGuest.getUuid());
            result.put("register_time", accountsGuest.getCreate_time());
            result.put("account_id", accountsGuest.getAccount_id());
            result.put("member_id", accountsGuest.getMember_id());
            result.put("appcode", accountsGuest.getAppcode());
            return result;
        }

        Map<String,Object> am = accountFileds.get(0);
        result.put("account_uuid", accountUuid);
        result.put("account_type", am.get("account_type"));
        result.put("account_status", am.get("account_status"));
        result.put("sex_type", am.get("sex_type"));
        result.put("uuid", am.get("uuid"));
        result.put("register_time", am.get("create_time"));
        result.put("account_id", am.get("account_id"));
        result.put("member_id", am.get("member_id"));
        result.put("last_logintime", am.get("last_logintime"));
        result.put("appcode", am.get("appcode"));
        result.put("cloned", am.get("cloned"));
        result.put("reg_style", am.get("reg_style"));
        result.put("channel", am.get("channel"));
        result.put("platform_id", am.get("platform_id"));
        result.put("account_name", am.get("account_name"));

        List<Map<String,Object>> accountInfoFileds = accountsInfoService.getFieldByAccountUuidFromDb(Arrays.asList("birth", "sexual", "tqcoin", "age", "baseaddr", "education", "income", "job", "affectivestatus"), accountUuids, true);
        if(accountInfoFileds!=null && !accountInfoFileds.isEmpty()) {
            Map<String,Object> im = accountInfoFileds.get(0);
            result.put("birth", im.get("birth"));
            result.put("sexual", im.get("sexual"));
            result.put("tqcoin", im.get("tqcoin"));
            result.put("age", im.get("age")); // 查数据库这项是有问题的
            result.put("baseaddr", im.get("baseaddr"));
            result.put("affectivestatus", im.get("affectivestatus"));
            result.put("education", im.get("education"));
            result.put("income", im.get("income"));
            result.put("job", im.get("job"));
        }

        List<Map<String,Object>> accountForumFileds = accountsForumProfileService.getFieldByAccountUuidFromDb(Arrays.asList("experience", "account_level", "account_actor"), accountUuids, true);
        if(accountForumFileds!=null && !accountForumFileds.isEmpty()) {
            Map<String,Object> fm = accountForumFileds.get(0);
            result.put("experience", fm.get("experience"));
            result.put("account_level", fm.get("account_level"));
            result.put("account_actor", fm.get("account_actor"));
        }

        return result;
    }

    public Map<String, Object> getMemberInfoByMemberId(Long memberId) {
        Map<String, Object> result = new HashMap<>();

        String token = membersService.getTokenById(memberId, true);
        result.put("token", StringUtils.trimToEmpty(token));

        MembersAppInfo membersAppInfo = membersAppInfoService.findByMemberId(memberId, true);
        if(membersAppInfo != null) {
            result.put("appcode", membersAppInfo.getAppcode()==null?0:membersAppInfo.getAppcode());
            result.put("cloned", membersAppInfo.getCloned()==null?0:membersAppInfo.getCloned());
            result.put("app_version", membersAppInfo.getApp_version()==null?0:membersAppInfo.getApp_version());
            result.put("platform_id", membersAppInfo.getPlatform_id()==null?0:membersAppInfo.getPlatform_id());
            result.put("place", StringUtils.trimToEmpty(membersAppInfo.getPlace()));
            result.put("add_time", membersAppInfo.getCreate_time()==null?0:membersAppInfo.getCreate_time());
            result.put("is_jailbroken", membersAppInfo.getIs_jailbroken()==null?0:membersAppInfo.getIs_jailbroken());
        }
        return result;
    }
}
