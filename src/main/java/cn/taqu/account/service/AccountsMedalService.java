package cn.taqu.account.service;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsMedalDao;
import cn.taqu.account.model.AccountsMedal;
import cn.taqu.account.model.AccountsPrivilege;
import cn.taqu.account.search.AccountsMedalSearch;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.PageUtil;
import cn.taqu.core.utils.StringUtil;

@Service
@Transactional
public class AccountsMedalService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsLevel> {

	@Autowired
	private AccountsPrivilegeService accountsPrivilegeService;
	@Autowired
	private AccountsForumProfileService accountsForumProfileService;
	@Autowired
	private AccountsMedalInfoService accountsMedalInfoService;
	@Autowired
	@Qualifier("medalStringRedisTemplate")
	private StringRedisTemplate medalStringRedisTemplate;
	@Autowired
	private AccountsMedalDao accountsMedalDao;

	/**
	 * 分页查询
	 * @param search
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	public Page<AccountsMedal> pageQuery(AccountsMedalSearch search, Integer pageNumber, Integer pageSize) {
		Collection<String> fields = Arrays.asList("id","medal_name","pic_url","privilege_id","create_time","update_time");
		Sql sql = Sql.build(AccountsMedal.class, fields).eq("status", 1);
		if(search != null && StringUtils.isNotBlank(search.getMedal_name())) {
			sql = sql.eq("medal_name", search.getMedal_name());
		}
		sql = sql.orderBy("id desc");
		PageData<AccountsMedal> page = accountsMedalDao.queryForPage(sql, pageNumber, pageSize);
		return new PageImpl<>(page.getData(), PageUtil.newPageable(pageNumber, pageSize), page.getTotal());
	}

	@Transactional
	public AccountsMedal create(AccountsMedal accountsMedal) {
		this.validateMedal(accountsMedal);
		AccountsPrivilege accountsPrivilege = accountsPrivilegeService.create("");
		accountsMedal.setPrivilege_id(accountsPrivilege.getId());
		accountsMedal.setCreate_time(DateUtil.currentTimeSeconds());
		accountsMedal.setPic_url("");
		accountsMedal = accountsMedalDao.merge(accountsMedal);
		this.updateCache(accountsMedal);
		return accountsMedal;
	}

	public AccountsMedal getByIdFromDb(Long id) {
		return accountsMedalDao.getOneById(id);
	}

	@Transactional
	public AccountsMedal update(Long id, AccountsMedal accountsMedal) {
		this.validateMedal(accountsMedal);
		AccountsMedal dbMedal = this.getByIdFromDb(id);
		if(dbMedal == null) {
			throw new ServiceException("medal_not_exists", "该头衔不存在");
		}
		dbMedal.setUpdate_time(DateUtil.currentTimeSeconds());
		dbMedal.setMedal_name(accountsMedal.getMedal_name());
		accountsMedalDao.merge(dbMedal);
		this.updateCache(dbMedal);
		return dbMedal;
	}

	public void deleteById(Long id) {
		accountsMedalDao.setInvalid(id);
		List<String> accountUuidList = accountsMedalInfoService.findUuidByMedalId(id);
		if(!accountUuidList.isEmpty()) {
			accountsForumProfileService.batchUnbindActor(accountUuidList);
			accountsMedalInfoService.batchUnbindAccountMedal(accountUuidList);
		}
		long privilegeId = MapUtils.getLongValue(this.getInfo(id, "privilege_id"), "privilege_id");
		String key = RedisKeyConstant.ACCOUNT_MEDAL.setArg(id);
		accountsPrivilegeService.deleteCache(privilegeId, "m");
		medalStringRedisTemplate.delete(key);
	}

	private void updateCache(AccountsMedal accountsMedal) {
		if(accountsMedal.getStatus() == null || accountsMedal.getStatus() == 0) {
			return;
		}
		String key = RedisKeyConstant.ACCOUNT_MEDAL.setArg(accountsMedal.getId());
		Map<String, String> hashValues = Maps.newHashMap();
		hashValues.put("id", StringUtil.nullNumberToEmptyString(accountsMedal.getId()));
		hashValues.put("medal_name", StringUtils.trimToEmpty(accountsMedal.getMedal_name()));
		hashValues.put("pic_url", StringUtils.trimToEmpty(accountsMedal.getPic_url()));
		hashValues.put("privilege_id", StringUtil.nullNumberToEmptyString(accountsMedal.getPrivilege_id()));
		medalStringRedisTemplate.opsForHash().putAll(key, hashValues);
	}

	@Transactional
	public Map<String, String> getPrivilege(Long medalId, Long privilegeId, String[] fields) {
		if(privilegeId==null || privilegeId==0) {
			privilegeId = MapUtils.getLong(this.getInfo(medalId, null, "privilege_id"), "privilege_id", 0L);
		}
		return accountsPrivilegeService.getMedalPrivilege(privilegeId, fields);
	}

	@Transactional
	public AccountsPrivilege updatePrivilege(Long medalId, Long privilegeId, Map<String, Object> privilegeConfig) {
		AccountsMedal accountsMedal = this.getByIdFromDb(medalId);
		if(accountsMedal == null) {
			throw new ServiceException(CodeStatus.MEDAL_INFO_NO_FOUND);
		}

		if(privilegeId==null || privilegeId==0) {
			privilegeId = MapUtils.getLong(this.getInfo(medalId, null, "privilege_id"), "privilege_id", 0L);
		}
		AccountsPrivilege accountsPrivilege = accountsPrivilegeService.update(privilegeId, privilegeConfig, "m", true);
		accountsMedal.setPrivilege_id(accountsPrivilege.getId());
		accountsMedalDao.merge(accountsMedal);
		this.updateCache(accountsMedal);
		return accountsPrivilege;
	}

	private void validateMedal(AccountsMedal accountsMedal) {
		if(StringUtils.isBlank(accountsMedal.getMedal_name())) {
			throw new ServiceException("data_error", "头衔名称不能为空");
		}
	}

	/**
	 * 根据medal id获取等级勋章相关配置，可通过optionResult参数来指定需要获取哪个字段的信息，如果没有获取到返回异常
	 * @Title getInfo
	 * @param medalId
	 * @param fields (选填)指定要返回的字段,只能是medal_name、pic_url、privilege_id中的一个或多个
	 * @return 返回获取到的信息
	 * <AUTHOR>
	 * @Date 2015年9月28日 下午7:16:04
	 */
	public Map<String, String> getInfo(Long medalId, String...fields) {
		return this.mGetInfo(Arrays.asList(medalId), fields).get(medalId);
	}

	public Map<Long, Map<String, String>> mGetInfo(List<Long> medalIdList, String...fields) {
		RedisSerializer<String> stringRedisSerializer = medalStringRedisTemplate.getStringSerializer();
		List<Object> valueList = medalStringRedisTemplate.executePipelined((RedisConnection connection) -> {
			medalIdList.forEach(medalId -> {
				connection.hGetAll(stringRedisSerializer.serialize(RedisKeyConstant.ACCOUNT_MEDAL.setArg(medalId)));
			});
			return null;
		}, stringRedisSerializer);

		Map<Long, Map<String, String>> resultMap = new HashMap<>();
		for (int i = 0; i < valueList.size(); i++) {
			Map<Object, Object> redisMedal = (Map<Object, Object>)valueList.get(i);
			Map<String ,String> itemMap = new HashMap<>();
			resultMap.put(medalIdList.get(i), itemMap);
			if(redisMedal==null || redisMedal.isEmpty()) {//缓存没有，直接返回空
				itemMap.put("medal_name", "");
				itemMap.put("pic_url", "");
				itemMap.put("privilege_id", "");
				continue;
			}

			//设置返回数据
			if(fields==null || fields.length==0) {
				for(Map.Entry<Object, Object> entry : redisMedal.entrySet()) {
					itemMap.put(entry.getKey().toString(), entry.getValue().toString());
				}
			} else {
				for(String o : fields) {
					itemMap.put(o, redisMedal.get(o) == null ? "" : redisMedal.get(o).toString().trim());
				}
			}
		}

		return resultMap;
	}
}
