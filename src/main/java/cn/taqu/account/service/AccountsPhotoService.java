package cn.taqu.account.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.*;
import cn.taqu.account.cron.AccountPhotoNumUpdateTask;
import cn.taqu.account.dao.*;
import cn.taqu.account.dto.*;
import cn.taqu.account.etcd.AvatarGuideConfig;
import cn.taqu.account.event.*;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.*;
import cn.taqu.account.thread.*;
import cn.taqu.account.utils.CounterUntil;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static cn.taqu.account.constant.RedisKeyConstant.*;


/**
 * 用户个人相册service
 *
 * <AUTHOR>
 * 2017年4月18日 下午1:39:36
 */
@Service
public class AccountsPhotoService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsPhoto> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsPhotoService.class);

    /**
     * 注册成功后，如果有传头像,去除String头像 中的 “https://avatar01.jiaoliuqu.com/”部分
     */
    public static String regReplaceAvatarPart = CommConst.AVATAR_HOST;
    /**
     * 源站地址
     */
    public static String ORIGN_PHOTO_DOMAIN = CommConst.AVATAR_SRC_HOST;
    /**
     * 源站类型
     */
    public static Integer REPLACE_PHOTO_DOMAIN_TYPE_ORIGN = 1;
    /**
     * 替换图片域名 0-不替换 1-替换成源地址
     */
    public static Integer REPLACE_PHOTO_DOMAIN_CHECK = 1;
    /**
     * 替换图片域名 0-不替换 1-替换成源地址
     */
    public static Integer REPLACE_PHOTO_DOMAIN_COMPARE = 1;

    public static String DEFAULT_AVATAR_BUCKET = CommConst.AVATAR_BUCKET;

    private static Integer UN_FACE_SCORE = -1;

    private static Integer AVATAR_SEQ = 1;

    /**
     * 2022.02.15 新版头像/相册总张数 12张
     */
    public static int AVATAR_PHOTO_LIMIT_20220215 = 13;
    /**
     * 2022.02.15 旧版相册张数 8张
     */
    public static int PHOTO_LIMIT_OLD = 8;
    /**
     * 2022.02.15 新版相册张数 12张
     */
    public static int PHOTO_LIMIT_20220215 = 12;

    @Autowired
    private BuryService buryService;

    @Resource(name = "accountBizStringRedisTemplate")
    private StringRedisTemplate bizRedisTemplate;

    /**
     * 修改头像后的默认状态 5
     * 2020.02.05 临时需求，改为状态6
     */
    private static final int AVATAR_DEFAULT_STATUS = AccountsPhoto.Status.NO_CHECK_ACCOUNT.getValue();
    /**
     * 修改封面后的默认状态 1
     * 2020.02.05 临时需求，改为状态6
     */
    private static final int COVER_DEFAULT_STATUS = AccountsPhoto.Status.NO_CHECK_ACCOUNT.getValue();
    /**
     * 图片未审核状态组
     */
    public static List<Integer> PHOTO_UNCHECK_STATUS_ARR = Lists.newArrayList(
            AccountsPhoto.Status.NO_CHECK.getValue()
            , AccountsPhoto.Status.RE_CHECK.getValue()
            , AccountsPhoto.Status.AUTO_CHECK.getValue()
            , AccountsPhoto.Status.NO_CHECK_ACCOUNT.getValue()
            , AccountsPhoto.Status.REVIEW_90.getValue());
    /**
     * 图片未审核状态组 1、4、5、6、7、8（需要人审状态）
     */
    public static List<Integer> PHOTO_REVIEW_STATUS_ARR = Lists.newArrayList(
            AccountsPhoto.Status.NO_CHECK.getValue()
            , AccountsPhoto.Status.RE_CHECK.getValue()
            , AccountsPhoto.Status.AUTO_CHECK.getValue()
            , AccountsPhoto.Status.NO_CHECK_ACCOUNT.getValue()
            , AccountsPhoto.Status.REVIEW_90.getValue()
        , AccountsPhoto.Status.ONLY_AUTO_CHECK.getValue());
    @Autowired
    private EventTrackReporter eventTrackReporter;

    @Autowired
    private AccountsPhotoDao accountsPhotoDao;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Lazy
    @Autowired
    private AccountsLifeService accountsLifeService;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AvatarHandleService avatarHandleService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private StringRedisTemplate accountBizStringRedisTemplate;
    @Autowired
    private ClickFilterService clickFilterService;
    @Autowired
    private AccountsRealPersonCertificationLogService accountsRealPersonCertificationLogService;
    @Autowired
    private VersionControlService versionControlService;
    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;
    @Autowired
    private ActionNoticeReport actionNoticeReport;
    @Autowired
    private CloneDefaultAvatarService cloneDefaultAvatarService;
    @Autowired
    private UserCertificationLogService userCertificationLogService;

    @Autowired
    private AccountBehaviorReporter behaviorReporter;

    @Autowired
    private ThreadPoolTaskExecutor asyncTaskExecutor;


    /**
     * 域名替换配置
     *
     * @param jsonStr
     */
    public static void setReplacePhotoDomain(String jsonStr) {
        try {
            Map<String, Object> confMap = JsonUtils.stringToObject2(jsonStr, new TypeReference<Map<String, Object>>() {
            });
            ORIGN_PHOTO_DOMAIN = MapUtils.getString(confMap, "orignPhotoDomain", CommConst.AVATAR_SRC_HOST);
            REPLACE_PHOTO_DOMAIN_CHECK = MapUtils.getInteger(confMap, "replacePhotoDomainCheck", 0);
            REPLACE_PHOTO_DOMAIN_COMPARE = MapUtils.getInteger(confMap, "replacePhotoDomainCompare", 0);
            LOGGER.info("获取图片域名配置={}", jsonStr);
        } catch (Exception e) {
            LOGGER.error("获取图片域名配置失败", e);
        }
    }

    /**
     * 修改头像
     *
     * @param accountUuid
     * @param photoUrl
     * @param status      头像抽查审核状态 1-未处理 -1已处罚 2已忽略 3默认头像
     * <AUTHOR>
     * 2017年4月18日 下午2:08:01
     */
    public AccountsPhoto updateAvatar(String accountUuid, String photoUrl, Integer status, Long updateTime) {
        //处理url
        photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
        if (AvatarHandleService.isDefAvatar(photoUrl)) {//如果是默认头像
            status = 3;
        }
        // 2019.7.13 头像status如果是 1未处理，则替换为5机审
        if (status == 1) {
            status = AVATAR_DEFAULT_STATUS;

            // 2024.06.07 文件使用，通知图片系统
            BuryService.pushToPicSysUsedFileBiz(CommConst.APPCODE_TAQU, photoUrl, CommConst.AVATAR_BUCKET, DateUtil.currentTimeSeconds());
        }

        //2017.5.3 防止出现重复请求
        String checkRedisKey = RedisKeyConstant.UPDATE_AVATAR_TIMES.setArg(accountUuid);
        long count = accountStringRedisTemplate.opsForValue().increment(checkRedisKey, 1);
        if (count > 1) {
            AccountsPhoto accountsPhoto = new AccountsPhoto();
            accountsPhoto.setPhoto_url(photoUrl);
            accountsPhoto.setBucket(DEFAULT_AVATAR_BUCKET);
            accountsPhoto.setStatus(status);
            accountsPhoto.setSeq(1);
            accountsPhoto.setVerify_status(0);
            accountsPhoto.setError_msg("");
            accountsPhoto.setLike_num(0L);
            return accountsPhoto;
        }
        accountStringRedisTemplate.expire(checkRedisKey, 1, TimeUnit.SECONDS);

        AccountsPhoto accountsPhoto = null;
        //查询头像是否存在
        List<AccountsPhoto> accountsPhotoList = accountsPhotoDao.findAvatarByUuid(accountUuid);
        if (accountsPhotoList != null && accountsPhotoList.size() > 0) {
            accountsPhoto = accountsPhotoList.get(0);
        }

        if (updateTime == null) {
            updateTime = DateUtil.currentTimeSeconds();
        }

        if (accountsPhoto == null) {
            //新建头像
            accountsPhoto = new AccountsPhoto();
            accountsPhoto.setAccount_uuid(accountUuid);
            accountsPhoto.setSeq(1);
            accountsPhoto.setBucket(DEFAULT_AVATAR_BUCKET);
            accountsPhoto.setVerify_status(0);
            accountsPhoto.setError_msg("");
            accountsPhoto.setLike_num(0L);

        }

        /**
         * 既然更新了头像url，就当新头像来处理(解决未升级到相册版本的用户调用老的修改头像接口时造成的新上传头像跑到之前的时间段去审核)<br/>
         * 如果为老用户的默认香蕉头，此处的updateTime就为accounts表的cs_id或create_time，把create_time更新为此时间不会有什么问题
         */
        if (!Objects.equals(photoUrl, accountsPhoto.getPhoto_url())) {
            accountsPhoto.setCreate_time(updateTime);
            accountsPhoto.setPhoto_url(photoUrl);
            accountsPhoto.setStatus(status);
            accountsPhoto.setVerify_status(0);
            accountsPhoto.setError_msg("");
            accountsPhoto.setLike_num(0L);
        }
        accountsPhoto.setUpdate_time(updateTime);
        // 图片快审 推 异步队列 再去走数美审核
//        setPhotoRiskDescription(accountsPhoto);
        accountsPhoto = _this().merge(accountsPhoto);
//        Accounts accounts = accountsManager.getByUuid(accountUuid);
        // 图片快审 推 异步队列 checkAccountEmptyFacePhoto
        // addToCheckRedisZSet(accounts, accountsPhoto);
        accountsService.checkAccountEmptyFacePhoto(accountUuid);
        //添加到缓存
        Map<String, String> photoMap = new HashMap<String, String>();
        photoMap.put("id", accountsPhoto.getId().toString());
        photoMap.put("photo_url", accountsPhoto.getPhoto_url());
        photoMap.put("status", accountsPhoto.getStatus().toString());
        photoMap.put("seq", accountsPhoto.getSeq().toString());
        photoMap.put("bucket", accountsPhoto.getBucket());
        photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
        photoMap.put("error_msg", accountsPhoto.getError_msg());
        photoMap.put("like_num", accountsPhoto.getLike_num() == null ? "0" : accountsPhoto.getLike_num().toString());
        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO.setArg(accountUuid);
        accountStringRedisTemplate.opsForHash().put(redisKey, "1", JsonUtils.objectToString(photoMap));
        accountStringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);

        if (!Objects.equals(3, status)) {
            // 推到行为埋点里
            actionNoticeReport.report(accountUuid, ActionEventEnum.AVATAR_UPLOAD);
        }

        return accountsPhoto;
    }

    private AccountsPhotoService _this() {
        return SpringContextHolder.getBean(AccountsPhotoService.class);
    }

    /**
     * 根据uuid获取用户相册
     *
     * @param accountUuid 用户uuid
     * @return 用户相册
     * <AUTHOR>
     * 2017年4月18日 下午4:19:33
     */
    public List<Map<String, String>> getAccountsPhoto(String accountUuid, boolean onlyCheck) {
        //返回结果
        List<Map<String, String>> photoMapList = this.getAccountsAllPhoto(accountUuid, onlyCheck);
        //如果没有，从用户头像拿
        if (photoMapList.size() == 0) {
            photoMapList.add(this.getAccountsPhotoByAvatarMap(accountUuid));
        }

        return photoMapList;
    }

    /**
     * 批量获取相册（只返回通过的）
     *
     * @param uuids
     * @return
     */
    public Map<String, List<Map<String, String>>> mGetAccountsPhoto(String[] uuids) {
        boolean[] onlyChecks = new boolean[uuids.length];
        Arrays.fill(onlyChecks, true);
        return this.mGetAccountsPhoto(uuids, onlyChecks);
    }

    /**
     * 批量获取相册
     *
     * @param uuids
     * @param onlyChecks 是否只返回审核通过的
     * @return
     */
    public Map<String, List<Map<String, String>>> mGetAccountsPhoto(String[] uuids, boolean[] onlyChecks) {
        Map<String, List<Map<String, String>>> result = new LinkedHashMap<>();

        //先从缓存获取
        Map<String, List<Map<String, String>>> redisResult = this.mGetAccountsPhotoAllByRedis(uuids, onlyChecks);
        List<String> redisMissList = new ArrayList<>();
        Arrays.stream(uuids).forEach(uuid -> {
            if ((!redisResult.containsKey(uuid)) || (redisResult.containsKey(uuid) && CollectionUtils.isEmpty(redisResult.get(uuid)))) {
                redisMissList.add(uuid);
            }
        });

        //缓存不存在的从数据库获取
        Map<String, List<Map<String, String>>> dbResult = this.mGetAccountsPhotoAllByDB(redisMissList.toArray(new String[redisMissList.size()]), onlyChecks);

        result.putAll(redisResult);
        result.putAll(dbResult);
        //头像为空的处理
        List<String> emptyUuidList = new ArrayList<>();
        Arrays.stream(uuids).forEach(uuid -> {
            List<Map<String, String>> photoList=result.get(uuid);
            if (CollectionUtils.isEmpty(photoList)) {
                emptyUuidList.add(uuid);
            }
            //确保用户上传头像的真实状态能透出
            setAvatarPhotoStatus(photoList);
        });

        if (!emptyUuidList.isEmpty()) {
            Map<String, List<Map<String, String>>> emptyUuidPhotoMap=getAccountsPhotoMap(emptyUuidList);
            Map<String, Map<String, Object>> emptyInfo = accountsInfoService.getInfoByUuid(emptyUuidList.toArray(new String[emptyUuidList.size()]), new String[]{"avatar", "profile_verify_status", "profile_error_msg"}, "1", false, false);
            emptyUuidList.forEach(uuid -> {
                Map<String, String> photoMap = new HashMap<>();
                String photoUrl = MapUtils.getString(emptyInfo.get(uuid), "avatar", AvatarConst.DEFAULT_MALE_AVATAR);
                // 2020.05.29 修复客户端bug
                photoUrl = AccountsInfoService.fixUrl(photoUrl);
                photoMap.put("id", "0");
                photoMap.put("photo_url", photoUrl);
                photoMap.put("status", String.valueOf(AccountsPhoto.Status.DEFAULT_PIC.getValue()));
                photoMap.put("seq", "1");
                photoMap.put("bucket", DEFAULT_AVATAR_BUCKET);
                // TODO 2020.03.04 此处要改，获取头像的活体状态
                photoMap.put("verify_status", MapUtils.getString(emptyInfo.get(uuid), "profile_verify_status", "0"));
                photoMap.put("error_msg", MapUtils.getString(emptyInfo.get(uuid), "profile_error_msg", ""));
                String avatarPhotoStatus = getAvatarPhotoStatus(emptyUuidPhotoMap, uuid);
                photoMap.put("avatar_photo_status", avatarPhotoStatus);
                List<Map<String, String>> photoList = new ArrayList<>(1);
                photoList.add(photoMap);
                result.put(uuid, photoList);
            });
        }

        // 2024.12.13 新增 按我的生活重排序，只查缓存，提高效率
        x:for (Map.Entry<String, List<Map<String, String>>> entry : result.entrySet()) {
            String accountUuid = entry.getKey();
            List<Map<String, String>> photoListOld = entry.getValue();
            List<Integer> accountsLifeSeq = accountsLifeService.getAccountsLifeSeq(accountUuid, false, false);
            // 如果没查到排序数据，说明是默认排序，不重排序
            if(CollectionUtils.isEmpty(accountsLifeSeq)) {
                continue x;
            }
            List<Map<String, String>> photoListNew = Lists.newArrayList();
            List<Integer> seqList = getSeqList(accountsLifeSeq);
            y:for (Integer seq : seqList) {
                for (Map<String, String> photoOld : photoListOld) {
                    int seqOld = MapUtils.getIntValue(photoOld, "seq", 1);
                    if(Objects.equals(seq, seqOld)) {
                        photoListNew.add(photoOld);
                        continue y;
                    }
                }
            }
            result.put(accountUuid, photoListNew);
        }

        logger.debug("mGetAccountsPhoto2 uuids:{}|redisResult:{}",JsonUtils.objectToString(uuids),JsonUtils.objectToString(result));
        return result;
    }

    /**
     * 获取默认排序列表
     * @param list
     * @return
     */
    private static List<Integer> getSeqList(List<Integer> list){
        List<Integer> reList = Lists.newArrayList();
        // 第一条
        reList.add(1);
        // 我的生活排序
        for (Integer seq : list) {
            reList.add(seq + 1);
        }
        for (int i = list.size() + 1; i < AVATAR_PHOTO_LIMIT_20220215; i++) {
            reList.add(i + 1);
        }

        return reList;
    }

    private void setAvatarPhotoStatus(List<Map<String, String>> photoList){
        if(CollectionUtils.isEmpty(photoList)){
            return;
        }
        for(Map<String, String> temp:photoList){
            if("1".equals(temp.get("seq"))){
                temp.put("avatar_photo_status",temp.get("status"));
            }
        }
    }

    private Map<String, List<Map<String, String>>> getAccountsPhotoMap(List<String> uuidList){
        String[] uuids=uuidList.toArray(new String[uuidList.size()]);
        boolean[] onlyChecks = new boolean[uuids.length];
        Arrays.fill(onlyChecks, false);
        Map<String, List<Map<String, String>>> redisResult = this.mGetAccountsPhotoAllByRedis(uuids, onlyChecks);
        List<String> redisMissList = new ArrayList<>();
        Arrays.stream(uuids).forEach(uuid -> {
            if ((!redisResult.containsKey(uuid)) || (redisResult.containsKey(uuid) && CollectionUtils.isEmpty(redisResult.get(uuid)))) {
                redisMissList.add(uuid);
            }
        });
        //缓存不存在的从数据库获取
        Map<String, List<Map<String, String>>> dbResult = this.mGetAccountsPhotoAllByDB(redisMissList.toArray(new String[redisMissList.size()]), onlyChecks);
        Map<String, List<Map<String, String>>> result = new LinkedHashMap<>();
        result.putAll(redisResult);
        result.putAll(dbResult);
        return result;
    }

    private String getAvatarPhotoStatus(Map<String, List<Map<String, String>>> map, String uuid){
        if(CollectionUtil.isEmpty(map)){
            return String.valueOf(AccountsPhoto.Status.DEFAULT_PIC.getValue());
        }
        List<Map<String, String>> photoList=map.get(uuid);
        if(CollectionUtils.isEmpty(photoList)){
            return String.valueOf(AccountsPhoto.Status.DEFAULT_PIC.getValue());
        }
        for(Map<String, String> temp : photoList){
            if("1".equals(temp.get("seq"))){
                return temp.get("status");
            }
        }
        return String.valueOf(AccountsPhoto.Status.DEFAULT_PIC.getValue());
    }

    /**
     * 根据uuid获取用户相册 只返回2-13号图
     *
     * @param accountUuid 用户uuid
     * @param onlyCheck   是否只返回审核的
     * @return 用户封面
     * <AUTHOR>
     * 2017年4月18日 下午4:19:33
     */
    public List<Map<String, String>> getAccountCover(String accountUuid, boolean onlyCheck) {
        List<Map<String, String>> photoMapList = this.getAccountsAllPhoto(accountUuid, onlyCheck);
        //排除第一张头像
        return photoMapList.stream().filter(map -> !map.get("seq").equals("1")).collect(Collectors.toList());
    }

    /**
     * 根据uuid获取用户相册 只返回2-13号图
     *
     * @param accountUuid 用户uuid
     * @param onlyCheck   是否只返回审核的
     * @return 用户封面
     * @param accountUuid
     * @param onlyCheck
     * @return
     */
    public List<AccountsPhotoDto> getAccountCoverList(String accountUuid, boolean onlyCheck) {
        List<Map<String, String>> accountCover = getAccountCover(accountUuid, onlyCheck);
        List<AccountsPhotoDto> list = Lists.newArrayList();
        for (Map<String, String> map : accountCover) {
            AccountsPhotoDto accountsPhotoDto = new AccountsPhotoDto();
            accountsPhotoDto.setBucket(MapUtils.getString(map, "bucket", ""));
            accountsPhotoDto.setErrorMsg(MapUtils.getString(map, "error_msg", ""));
            accountsPhotoDto.setFaceScore(MapUtils.getInteger(map, "face_score"));
            accountsPhotoDto.setId(MapUtils.getLong(map, "id"));
            accountsPhotoDto.setLikeNum(MapUtils.getLong(map, "like_num"));
            accountsPhotoDto.setPhotoUrl(MapUtils.getString(map, "photo_url", ""));
            accountsPhotoDto.setSeq(MapUtils.getInteger(map, "seq"));
            accountsPhotoDto.setStatus(MapUtils.getInteger(map, "status"));
            accountsPhotoDto.setVerifyStatus(MapUtils.getInteger(map, "verify_status"));
            list.add(accountsPhotoDto);
        }

        return list;
    }

    /**
     * 根据uuid数组批量获取用户相册 只返回2-13号图
     *
     * @param uuids
     * @param onlyChecks
     * @return
     */
    public Map<String, List<Map<String, String>>> mgetAccountCover(String[] uuids, boolean[] onlyChecks) {
        Map<String, List<Map<String, String>>> photoMapLists = this.mGetAccountsPhoto(uuids, onlyChecks);
        //排除第一张头像
        photoMapLists.values().forEach(photoMapList -> {
            if (photoMapList.size() > 0) {
                //第一张是头像的情况下才排除
                if (AVATAR_SEQ.equals(MapUtils.getInteger(photoMapList.get(0), "seq"))) {
                    photoMapList.remove(0);
                }
            }
        });
        return photoMapLists;
    }

    /**
     * 获取相册表中（头像+相册）
     * 先取缓存，再取数据库
     *
     * @param accountUuid
     * @param onlyCheck
     * @return
     */
    public List<Map<String, String>> getAccountsAllPhoto(String accountUuid, boolean onlyCheck) {
        //返回结果
        List<Map<String, String>> photoMapList = Lists.newArrayList();
        //从缓存拿数据
        photoMapList.addAll(this.getAccountsPhotoAllByRedis(accountUuid, onlyCheck));
        //从数据库拿
        if (photoMapList.size() == 0) {
            photoMapList.addAll(this.getAccountsPhotoAllByDB(accountUuid, onlyCheck));
        }
        return photoMapList;
    }

    /**
     * 后台设置头像+相册
     *
     * @param accountUuid
     * @param urlArray
     * @return
     */
    @Transactional
    public List<String> setAvatarAndPhotoForBaskstage(String accountUuid, String[] urlArray) {
        AccountsPhoto avatarPhoto = null;

        if (urlArray == null || urlArray.length == 0) {
            throw new ServiceException("photo_empty", "请至少选择一张照片");
        }
        //2017.5.9 防止出现重复请求
        this.chcekResubmit(accountUuid);

        //1.校验数据
        if (urlArray.length > AVATAR_PHOTO_LIMIT_20220215) {
            throw new ServiceException("设置失败，最多只能上传13张照片。");
        }
        //2.校验权限
        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts == null) {
            throw new ServiceException("设置失败，用户不存在，请联系客服。");
        }

        String bucket = DEFAULT_AVATAR_BUCKET;

        //当前时间
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        List<String> urlListNew = Lists.newArrayList(urlArray);    //新的urlList
        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuid(accountUuid);//旧的photo对象
        List<AccountsPhoto> addList = Lists.newArrayList();//添加修改的list
        List<AccountsPhoto> deleteList = Lists.newArrayList();//删除的list
        //修改数据库
        if (photoList != null && photoList.size() > 0) {
            //将原排序设空
            for (AccountsPhoto accountsPhoto : photoList) {
                accountsPhoto.setSeq(0);
            }
            //遍历新url，重新赋值排序
            i:
            for (int i = 0; i < urlListNew.size(); i++) {
                String newUrl = urlListNew.get(i);
                for (AccountsPhoto accountsPhoto : photoList) {
                    String photoUrl = accountsPhoto.getPhoto_url();
                    //如果新url存在为某个旧的里，赋值新的排序
                    if (photoUrl.equals(newUrl) && accountsPhoto.getSeq() == 0) {
                        accountsPhoto.setUpdate_time(currentTimeSeconds);
                        accountsPhoto.setBucket(bucket);
                        accountsPhoto.setSeq(i + 1);
                        //获取头像，后续可以设置缓存使用
                        if (1 == accountsPhoto.getSeq()) {
                            avatarPhoto = accountsPhoto;
                        }
                        continue i;
                    }
                }

                AccountsPhoto accountsPhoto = new AccountsPhoto();
                accountsPhoto.setAccount_uuid(accountUuid);
                accountsPhoto.setPhoto_url(newUrl);
                accountsPhoto.setSeq(i + 1);
                accountsPhoto.setBucket(bucket);
                accountsPhoto.setStatus(COVER_DEFAULT_STATUS);
                accountsPhoto.setCreate_time(currentTimeSeconds);
                accountsPhoto.setUpdate_time(currentTimeSeconds);
                accountsPhoto.setVerify_status(0);
                accountsPhoto.setLike_num(0L);
                // 图片快审 推 异步队列 再去走数美审核
//                setPhotoRiskDescription(accountsPhoto);
                accountsPhoto.setError_msg("");
                //获取头像，后续可以设置缓存使用
                if (1 == accountsPhoto.getSeq()) {
                    avatarPhoto = accountsPhoto;
                }
                addList.add(accountsPhoto);
            }
            //遍历photoList，筛选出添加修改或删除的对象
            for (AccountsPhoto accountsPhoto : photoList) {
                Integer seq = accountsPhoto.getSeq();
                // 图片快审 推 异步队列 再去走数美审核
//                setPhotoRiskDescription(accountsPhoto);
                if (seq != 0) {
                    addList.add(accountsPhoto);
                } else {
                    deleteList.add(accountsPhoto);
                }
            }

        } else {
            //直接添加
            for (int i = 0; i < urlListNew.size(); i++) {
                String url = urlListNew.get(i);

                AccountsPhoto accountsPhoto = new AccountsPhoto();
                accountsPhoto.setAccount_uuid(accountUuid);
                accountsPhoto.setPhoto_url(url);
                accountsPhoto.setSeq(i + 1);
                accountsPhoto.setStatus(1);
                accountsPhoto.setBucket(bucket);
                accountsPhoto.setCreate_time(currentTimeSeconds);
                accountsPhoto.setUpdate_time(currentTimeSeconds);
                accountsPhoto.setVerify_status(0);
                accountsPhoto.setLike_num(0L);
                // 图片快审 推 异步队列 再去走数美审核
//                setPhotoRiskDescription(accountsPhoto);
                accountsPhoto.setError_msg("");
                //获取头像，后续可以设置缓存使用
                if (1 == accountsPhoto.getSeq()) {
                    avatarPhoto = accountsPhoto;
                }
                addList.add(accountsPhoto);
            }
        }

        try {
            if (deleteList.size() != 0) {
                accountsPhotoDao.remove(deleteList);
            }
            addList = accountsPhotoDao.merge(addList);
            accountsPhotoDao.flush();
            // 异步 checkAccountEmptyFacePhoto
//            addToCheckRedisZSet(accounts, addList);
        } catch (ObjectOptimisticLockingFailureException e) {
            logger.warn("设置图片失败，图片可能已被删除，主键插入", e);
            throw new ServiceException("设置图片失败，请刷新后重试。");
        }

        //修改头像
        String avatar = urlArray[0];
        if (regReplaceAvatarPart != null && regReplaceAvatarPart.trim().length() > 0) {
            avatar = regReplaceAvatarPart + StringUtils.removeStart(avatarHandleService.getAvatarByVersion(avatar, "1"), "/");
        }
        accounts.setAvatar(avatar);
        accounts.setCs_id(DateUtil.currentTimeSeconds());
        accounts.setForum_status(1);//头像抽查审核状态 1未处理 -1已处罚 2已忽略
        accounts = accountsManager.merge(accounts);

        this.setAccountsPhotoToRedis(addList, accountUuid);

        // 更新redis中用户信息缓存
        Map<String, String> infoCache = Maps.newHashMap();
        String avatarUri = AvatarHandleService.getAvatarOfSaveRedis(avatar);
        infoCache.put("photo_num", String.valueOf(Math.max(0, addList.size() - 1)));//第一张算入头像中，不计算照片数量
        infoCache.put("avatar", avatarUri);
        infoCache.put("avatar_origin", avatarUri);
        infoCache.put("avatar_status", "1");
        // TODO 2020.03.04 此处需要更新头像缓存 认证状态
        if (null != avatarPhoto && null != avatarPhoto.getVerify_status()) {
            infoCache.put("profile_verify_status", String.valueOf(avatarPhoto.getVerify_status()));
            if (StringUtils.isEmpty(avatarPhoto.getError_msg())) {
                infoCache.put("profile_error_msg", "");
            } else {
                infoCache.put("profile_error_msg", avatarPhoto.getError_msg());
            }
        }

        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);

        List<String> deleteUrlList = Lists.newArrayList();
        if (!deleteList.isEmpty()) {
            for (AccountsPhoto accountsPhoto : deleteList) {
                //还得判断是不是默认头像或违规头像。不能删除了
                Integer status = accountsPhoto.getStatus();
                if (status != 3 && status != -1) {
                    deleteUrlList.add(accountsPhoto.getPhoto_url());
                }
            }
        }

        // 判断用户是否无真人认证图片
        accountsService.checkAccountEmptyFacePhoto(accountUuid);
        SpringContextHolder.getBean(AllureService.class).refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);

        return deleteUrlList;
    }

    /**
     * 设置用户头像
     *
     * @param accountUuid     用户uuid
     * @param url
     * @param bucket
     * @param isBackstage     是否是后台修改
     * @param verifyStatus
     * @param errorMsg
     * @param faceScore
     * @param similarityScore
     * @param isFace          是否活体认证 0-未认证，1-认证成功，2-认证失败
     * @param needDetectFace
     */
    @Transactional
    public void setAccountAvatar(String accountUuid, String url, String bucket, int isBackstage, int verifyStatus, String errorMsg, Integer faceScore, Double similarityScore, Integer isFace, Boolean needDetectFace) {
        accountsInfoService.guestTicketExpire(accountUuid);

        //获取历史头像
        AccountsPhoto accountsPhotoOld = accountsPhotoDao.getAccountAvatar(accountUuid, null, true);

        //2017.5.9 防止出现重复请求
        this.chcekResubmit(accountUuid);

        // 2024.06.07 文件使用，通知图片系统
        BuryService.pushToPicSysUsedFileBiz(CommConst.APPCODE_TAQU, url, CommConst.AVATAR_BUCKET, DateUtil.currentTimeSeconds());

        if (AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(verifyStatus)) {
            errorMsg = CodeStatus.ALIYUN_VERIFY_FALI_MSG.getReasonPhrase();
        }
        AccountsPhoto avatarPhoto = null;
        url = avatarHandleService.getAvatarByVersion(url, "1");
        //2.校验权限
        if (StringUtils.isBlank(url)) {
            throw new ServiceException("avatar_empty", "请选择一张照片");
        }

        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts == null) {
            throw new ServiceException("设置个人相册失败，用户不存在，请联系客服。");
        }

        if (StringUtils.isBlank(bucket)) {
            bucket = DEFAULT_AVATAR_BUCKET;
        }

        if (Objects.isNull(isFace)) {
            isFace = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).detectIsFaceV2(
                    accountUuid,
                    AccountsPhotoService.regReplaceAvatarPart + AvatarHandleService.getAvatarOfSavePhoto(url)
            );
        }

        List<String> deleteUrlList = Lists.newArrayList();
        String photoUrl = "";
        Map<String, String> infoCache = Maps.newHashMap();
        //当前时间
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuid(accountUuid);//旧的photo对象

        //修改数据库
        boolean flagHasAvatar = false;
//        LOGGER.info("设置头像测试日志， url={}, verifyStatus={}", url, verifyStatus);
        //是否有头像
        for (AccountsPhoto accountsPhoto : photoList) {
            if (accountsPhoto.getSeq() == 1) {
                flagHasAvatar = true;
                photoUrl = accountsPhoto.getPhoto_url();
                Integer status = accountsPhoto.getStatus();
//                LOGGER.info("设置头像测试日志， photoUrl={}", photoUrl);
                //头像相同时的处理
                if (Objects.equals(photoUrl, url)) {
                    accountsPhoto.setFace_score(faceScore);
                    accountsPhoto.setVerify_status(verifyStatus);
                    accountsPhoto.setUpdate_time(DateUtil.currentTimeSeconds());
                    accountsPhoto.setLike_num(accountsPhoto.getLike_num() == null ? 0L : accountsPhoto.getLike_num());
                    // 图片快审 推 异步队列 再去走数美审核
//                    setPhotoRiskDescription(accountsPhoto);
                    accountsPhotoDao.merge(accountsPhoto);
                    infoCache.put("pre_profile_verify_status", StringUtil.nullNumberToEmptyString(verifyStatus));
                    infoCache.put("pre_avatar_face_status", StringUtil.nullNumberToEmptyString(isFace));
                    if (StringUtils.isEmpty(accountsPhoto.getError_msg())) {
                        infoCache.put("pre_profile_error_msg", "");
                    } else {
                        infoCache.put("pre_profile_error_msg", accountsPhoto.getError_msg());
                    }
                    accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);
                    this.setAccountsPhotoToRedis(photoList, accountUuid);
                    // 保存到审核zset
//                    this.addToCheckRedisZSet(accounts, accountsPhoto);
//                    LOGGER.info("设置头像测试日志， 设置完头像状态pre_profile_verify_status={}", url, verifyStatus);
                    // 图片快审 推 异步队列 checkAccountEmptyFacePhoto
//                    this.addToCheckRedisZSet(accounts, accountsPhoto);
                    accountsService.checkAccountEmptyFacePhoto(accountUuid);
//                    LOGGER.info("设置头像测试日志， 设置完头像状态pre_profile_verify_status={}", url, verifyStatus);
                    return;
                }

                //还得判断是不是默认头像或违规头像。不能删除了
                if (status != 3 && status != -1) {
                    deleteUrlList.add(photoUrl);
                }
                // 旧向量删除
                ImgVectorRemoveDto vectorRemove = new ImgVectorRemoveDto();
                vectorRemove.setSafeId(RiskSafeIdConstant.SIMILAR_PHOTO_DETECT);
                String bizId = RiskSafeService.genBizId(avatarHandleService.getAvatarByVersion(accountsPhoto.getPhoto_url(), null));
                vectorRemove.setBizId(bizId);
                vectorRemove.setSenderUuid(accountUuid);
                RiskSafeService.imageVectorDelete(vectorRemove);

                //修改值
                accountsPhoto.setPhoto_url(url);
                accountsPhoto.setBucket(bucket);
                accountsPhoto.setCreate_time(currentTimeSeconds);
                accountsPhoto.setUpdate_time(currentTimeSeconds);
                accountsPhoto.setStatus(AVATAR_DEFAULT_STATUS);
                accountsPhoto.setVerify_status(verifyStatus);
                accountsPhoto.setError_msg(errorMsg);
                accountsPhoto.setFace_score(faceScore);
                accountsPhoto.setLike_num(0L);
                // 图片快审 推 异步队列 再去走数美审核
//                setPhotoRiskDescription(accountsPhoto);
                accountsPhotoDao.merge(accountsPhoto);
                // 图片快审 推 异步队列 checkAccountEmptyFacePhoto
//                addToCheckRedisZSet(accounts, accountsPhoto);
                avatarPhoto = accountsPhoto;
                imgVectorAdd(accountsPhoto);
                break;
            }
        }



        if (!flagHasAvatar) { //没有头像，添加
            AccountsPhoto accountsPhoto = new AccountsPhoto();
            accountsPhoto.setAccount_uuid(accountUuid);
            accountsPhoto.setPhoto_url(url);
            accountsPhoto.setSeq(1);
            accountsPhoto.setStatus(AVATAR_DEFAULT_STATUS);
            accountsPhoto.setBucket(bucket);
            accountsPhoto.setCreate_time(currentTimeSeconds);
            accountsPhoto.setUpdate_time(currentTimeSeconds);
            accountsPhoto.setVerify_status(verifyStatus);
            accountsPhoto.setError_msg(errorMsg);
            accountsPhoto.setFace_score(faceScore);
            accountsPhoto.setLike_num(0L);
            // 图片快审 推 异步队列 再去走数美审核
//            setPhotoRiskDescription(accountsPhoto);
            accountsPhoto = accountsPhotoDao.merge(accountsPhoto);
            imgVectorAdd(accountsPhoto);
            // 图片快审 推 异步队列 checkAccountEmptyFacePhoto
//            this.addToCheckRedisZSet(accounts, accountsPhoto);
            avatarPhoto = accountsPhoto;
            photoList.add(accountsPhoto);
        }

        //初始注册头像是否违规，1 违规 0 没违规
        accountsInfoService.setRegisterAvatarStatus(accountUuid, NumberUtils.INTEGER_ZERO);

        //修改头像
        String avatar = url;
        if (regReplaceAvatarPart != null && regReplaceAvatarPart.trim().length() > 0) {
            avatar = regReplaceAvatarPart + StringUtils.removeStart(avatarHandleService.getAvatarByVersion(avatar, "1"), "/");
        }
        accounts.setAvatar(avatar);
        accounts.setCs_id(currentTimeSeconds);
        accounts.setForum_status(1);//头像抽查审核状态 1未处理 -1已处罚 2已忽略
        accounts = accountsManager.merge(accounts);
        this.setAccountsPhotoToRedis(photoList, accountUuid);

        // 保存是否人脸头像到数据库
        if (isFace == 1) {
            AccountsInfo accountsInfo = accountsInfoDao.getByAccountUuid(accountUuid);
            if (accountsInfo != null) {
                accountsInfo.setPerson_face_avatar(1);
                accountsInfo.setUpdate_time(currentTimeSeconds);
                accountsInfoDao.merge(accountsInfo);

                infoCache.put(UserField.Avatar.PERSON_FACE_AVATAR, "1");
            }
        }

        // 更新redis中用户信息缓存
        infoCache.put("photo_num", String.valueOf(Math.max(0, photoList.size() - 1)));//第一张是头像，不能算在照片数量中
        //修改头像先不更新缓存avatar，待审核通过后再去更新缓存
        //infoCache.put("avatar", avatarHandleService.getAvatarOfSaveRedis(avatar));
        infoCache.put("avatar_origin", AvatarHandleService.getAvatarOfSaveRedis(avatar));
        infoCache.put("avatar_status", "0");
        if (null != avatarPhoto && null != avatarPhoto.getVerify_status()) {
            infoCache.put("pre_profile_verify_status", String.valueOf(avatarPhoto.getVerify_status()));
            if (StringUtils.isEmpty(avatarPhoto.getError_msg())) {
                infoCache.put("pre_profile_error_msg", "");
            } else {
                infoCache.put("pre_profile_error_msg", avatarPhoto.getError_msg());
            }
        }
        if (null != similarityScore) {
            SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).singleSave(avatarPhoto, similarityScore);
            if (null != faceScore) {
//                infoCache.put(UuidInfoField.PROFILE_FACE_SCORE, String.valueOf(faceScore));
            }
        }
        infoCache.put("pre_avatar_face_status", StringUtil.nullNumberToEmptyString(isFace));
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);
        // 移除审核队列重新加入
        if (avatarPhoto != null) {
            // 从审核队列中移除
            if (!AvatarHandleService.isDefAvatar(photoUrl)) {
                LOGGER.info("头像数据变更.头像数据从审核队列中移除.头像uuid={}.id={}.url={}", avatarPhoto.getAccount_uuid(), avatarPhoto.getId().toString(), photoUrl);
                SoaService.sendReviewPhotoSkip(null, avatarPhoto.getId(), photoUrl, "");
            } else {
                LOGGER.info("头像数据变更.头像数据从审核队列中移除.头像uuid={}.默认/违规头像不推", avatarPhoto.getAccount_uuid());
            }
        }

        // 判断用户是否无真人认证图片
        accountsService.checkAccountEmptyFacePhoto(accountUuid);

        // 存在历史头像
        if (accountsPhotoOld != null) {
            LOGGER.info("accountUuid={},历史头像存储到redis={}", accountUuid, JSON.toJSONString(accountsPhotoOld));
            accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.ACCOUNT_BEFORE_AVATAR_PHOTO.setArg(accountUuid), JSON.toJSONString(accountsPhotoOld), 1L, TimeUnit.DAYS);
        }

        // 上传头像埋点
        EventTrackingDO tracking = EventTrackingDO.create(accountUuid, AccountEvent.AVATAR_UPLOAD);
        kafkaSinkUtil.push(EventConst.EVENT_TRACKING_TOPIC, tracking);
        behaviorReporter.report(accountUuid, tracking);
        // 推到行为埋点里
        actionNoticeReport.report(accountUuid, ActionEventEnum.AVATAR_UPLOAD);

        //完成真人机审，上报事件到活动平台
        if(isFace == 1 && verifyStatus==1){
            // 2024.05.24 后期可以去掉
            pushMsgToActivityKafka(accountUuid,CommConst.MP_TAQU_EVENT_NOTIFY, CommConst.REAL_PERSON_MACHINE_VERIFY);
            // 2024.05.24 新增推入业务侧
            buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.NEW_REAL_AVATAR_CONFIRMED.getType());
        }
    }

    public void imgVectorAdd(AccountsPhoto photo) {
        CompletableFuture.runAsync(() -> {
            ImgVectorDetectDto dto = new ImgVectorDetectDto();
            String url = avatarHandleService.getAvatarByVersion(photo.getPhoto_url(), null);
            String riskBizId = RiskSafeService.genBizId(url);
            dto.setSafeId(RiskSafeIdConstant.SIMILAR_PHOTO_DETECT);
            dto.setBizId(riskBizId);
            dto.setImageUrl(Collections.singletonList(url));
            dto.setSenderUuid(photo.getAccount_uuid());
            RiskSafeService.imageVectorAdd(dto);
            accountBizStringRedisTemplate.opsForSet().add(USER_PHOTO_DETECT_VERSION_SET, photo.getAccount_uuid());
            // 比对记录
            ImageCompareRecord record = new ImageCompareRecord();
            record.setOriginId(photo.getId());
            record.setBizId(riskBizId);
            Long now = DateUtil.currentTimeSeconds();
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setBizType(ImageCompareRecord.ALBUM);
            record.setConclusion("pass");
            TransactionWrapper.me().wrap(() -> SpringContextHolder.getBean(ImageCompareRecordDao.class).merge(record), TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        }, asyncTaskExecutor).exceptionally(e -> {
            logger.warn("异步风控相册向量上报失败", e);
            return null;
        });
    }

    private ImgVectorDetectRespDto imgVectorDetect(String uuid, String photo) {
        ImgVectorDetectDto dto = new ImgVectorDetectDto();
        String url = avatarHandleService.getAvatarByVersion(photo, null);
        String riskBizId = RiskSafeService.genBizId(url);
        dto.setSafeId(vectorDetectUseNewSafeId(uuid) ? RiskSafeIdConstant.SIMILAR_PHOTO_DETECT : RiskSafeIdConstant.REAL_PERSON_COVER);
        dto.setBizId(riskBizId);
        dto.setImageUrl(Collections.singletonList(url));
        dto.setSenderUuid(uuid);
        ImgVectorDetectRespDto resp = RiskSafeService.imageVectorDetect(dto);
        if (!resp.pass()) {
            logger.warn("相册照片已上传过或相似度过高 {}", photo);
        }
        return resp;
    }

    /**
     * 获取审核类型
     *
     * @param accountsPhoto
     * @return
     */
    public Integer getPhotoCheckType(AccountsPhoto accountsPhoto) {
        Integer seq = accountsPhoto.getSeq();
        Map<String, Object> infoByUuid = accountsInfoService.getInfoByUuid(new String[]{accountsPhoto.getAccount_uuid()}, new String[]{UuidInfoField.MOBILE, "account_name", "profile_verify_status", "face_certification", "real_person_certification"}, "0", false, false).get(accountsPhoto.getAccount_uuid());
        Boolean isRealPersonCertification = infoByUuid.getOrDefault("real_person_certification", "2").equals(String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));

        // 头像
        if (seq == 1) {
            // 已经真人
            if (isRealPersonCertification) {
                // 推给审核平台
                return PhotoOrignCheckEnum.REAL_PERSON_AVATAR.getValue();
            } else {
                // 推给审核平
                return PhotoOrignCheckEnum.NOT_REAL_PERSON_AVATAR.getValue();
            }
        } else {
            // 相册
            // 真人认证
            if (isRealPersonCertification) {
                return PhotoOrignCheckEnum.REAL_PERSON_COVER.getValue();
            } else {
                return null;
            }
        }
    }

    /**
     * 头像复审
     */
    @Transactional
    public void review(String accountUuid) {
        List<Integer> ignoreList = Lists.newArrayList(
                AccountsPhoto.Status.NO_CHECK.getValue(),
                AccountsPhoto.Status.RE_CHECK.getValue(),
                AccountsPhoto.Status.AUTO_CHECK.getValue(),
                AccountsPhoto.Status.NO_CHECK_ACCOUNT.getValue(),
                AccountsPhoto.Status.REVIEW_90.getValue()
        );
        List<AccountsPhoto> accountsPhotoList = accountsPhotoDao.findAvatarByUuid(accountUuid);
        if (CollectionUtils.isEmpty(accountsPhotoList)) {
            LOGGER.info("不操作头像复审，accountUuid: {}", accountUuid);
            return;
        }
        AccountsPhoto accountsPhoto = accountsPhotoList.get(0);
        if (ignoreList.contains(accountsPhoto.getStatus()) || AvatarHandleService.isDefAvatar(AvatarHandleService.getAvatarOfSaveRedis(accountsPhoto.getPhoto_url()))) {
            LOGGER.info("不操作头像复审，accountUuid: {}", accountUuid);
            return;
        }

        // 网安要求长时间未活跃用户需要绑定手机号，出现有些用户不绑定就退出，此时头像已经推送到审核平台
        Object avatarQuality = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "avatar_script_quality");
        if (avatarQuality == null || !avatarQuality.toString().equals("1")) {
            if (!accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_SCRIPT_RUN_AVATAR_SCRIPT_COMMIT.setArg(accountUuid))) {
                LOGGER.info("头像质量重新检测.长时间未活跃复审.uuid={}", accountUuid);
                // 推送到头像审核队列
                BuryService.pushToAvatarScript(accountUuid);
                accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.ACCOUNT_SCRIPT_RUN_AVATAR_SCRIPT_COMMIT.setArg(accountUuid), "1", 5L, TimeUnit.DAYS);
            }
        }

        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        accountsPhoto.setStatus(AccountsPhoto.Status.REVIEW_90.getValue());
        accountsPhoto.setCreate_time(currentTimeSeconds);
        accountsPhoto.setUpdate_time(currentTimeSeconds);
        accountsPhotoDao.merge(accountsPhoto);
        // 图片快审 推 异步队列 checkAccountEmptyFacePhoto
//        addToCheckRedisZSet(accountsManager.getByUuid(accountUuid), accountsPhoto);
        accountsService.checkAccountEmptyFacePhoto(accountUuid);
    }

    @Transactional
    public void updateAccountAvatar(AccountsPhoto accountsAvatar) {
        long currentTimeSeconds = DateUtil.currentTimeSeconds();

        Accounts accounts = accountsManager.getByUuid(accountsAvatar.getAccount_uuid(), false);
        String avatar = accountsAvatar.getPhoto_url();
        if (regReplaceAvatarPart != null && regReplaceAvatarPart.trim().length() > 0) {
            avatar = regReplaceAvatarPart + StringUtils.removeStart(avatarHandleService.getAvatarByVersion(avatar, "1"), "/");
        }
        accounts.setAvatar(avatar);
        accounts.setCs_id(currentTimeSeconds);
        accounts.setForum_status(accountsAvatar.getStatus());
        accountsManager.merge(accounts);

//        Integer isFace = AliyunLiveFaceDetectService.detectIsFaceV2(accountsAvatar.getAccount_uuid(), avatar);
        if (Objects.equals(accountsAvatar.getStatus(), 2) || Objects.equals(accountsAvatar.getStatus(), 3)) {
            this.avatarCheckedUpdateCache(accountsAvatar);
//            updateAvatarFaceStatus(accountsAvatar.getAccount_uuid(), String.valueOf(isFace), String.valueOf(isFace));
        } else {
            this.avatarUncheckedUpdateCache(accountsAvatar);
//            updateAvatarFaceStatus(accountsAvatar.getAccount_uuid(), null, String.valueOf(isFace));
        }
    }

    /**
     * 设置用户相册
     *
     * @param accountUuid
     * @param urlArray
     * @param bucket
     * @param appcode
     * @param isBackstage
     * @param version
     * @param verifyStatusMap
     * @param faceScoreMap
     * @param similarityScoreMap
     * @return
     */
    @Transactional
    public List<String> setAccountCover(String accountUuid, String[] urlArray, String bucket, Integer appcode, int isBackstage, Integer version, Map<String, Integer> verifyStatusMap, Map<String, Integer> faceScoreMap, Map<String, Float> similarityScoreMap, String smid) {
        accountsInfoService.guestTicketExpire(accountUuid);
        Map<String, Object> infoMap = accountsInfoService.singleGetInfo(accountUuid, new String[] { UuidInfoField.REAL_PERSON_CERTIFICATION, UuidInfoField.SEX_TYPE });
        boolean isReal = "1".equals(MapUtils.getString(infoMap, UuidInfoField.REAL_PERSON_CERTIFICATION, "2"));
        boolean isFemale = "2".equals(MapUtils.getString(infoMap, UuidInfoField.SEX_TYPE, "1"));

        if (urlArray == null) {
            urlArray = new String[]{};
        }
        //2017.5.9 防止出现重复请求
        this.chcekResubmit(accountUuid);

        //1.校验数据
        if (urlArray.length > PHOTO_LIMIT_20220215) {
            throw new ServiceException("设置相册失败，最多只能上传12张照片。");
        }

        //2.校验权限
        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts == null) {
            throw new ServiceException("设置相册失败，用户不存在，请联系客服。");
        }

        if (StringUtils.isBlank(bucket)) {
            bucket = DEFAULT_AVATAR_BUCKET;
        }

        //当前时间
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        List<String> urlListNew = Lists.newArrayList(urlArray);    //新的urlList
        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuid(accountUuid);//旧的photo对象
        List<AccountsPhoto> addList = Lists.newArrayList();//添加修改的list
        List<AccountsPhoto> deleteList = Lists.newArrayList();//删除的list
        List<AccountsPhoto> needPushToHigh = Lists.newArrayList();

        //修改数据库
        if (CollectionUtils.isNotEmpty(photoList)) {
            //取出头像
            AccountsPhoto accountAvatar = photoList.remove(0);
            //将原排序设空
            for (AccountsPhoto accountsPhoto : photoList) {
                accountsPhoto.setSeq(0);
            }
            //遍历新url，重新赋值排序
            i:
            for (int i = 0; i < urlListNew.size(); i++) {
                String newUrl = urlListNew.get(i);
                newUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(newUrl);
                for (AccountsPhoto accountsPhoto : photoList) {
                    String photo_url = accountsPhoto.getPhoto_url();
                    //如果新url存在为某个旧的里，赋值新的排序
                    if (photo_url.equals(newUrl) && accountsPhoto.getSeq() == 0) {
                        accountsPhoto.setUpdate_time(currentTimeSeconds);
                        accountsPhoto.setBucket(bucket);
                        accountsPhoto.setSeq(i + 2);

                        //新接口添加人脸对比相似度和颜值分，老照片的值仍需要保存
                        if (null != verifyStatusMap && null != faceScoreMap) {
                            if (AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus().equals(verifyStatusMap.get(photo_url)) && null != accountsPhoto.getVerify_status()) {
                                verifyStatusMap.put(photo_url, accountsPhoto.getVerify_status());
                            }
                            if (UN_FACE_SCORE.equals(faceScoreMap.get(photo_url)) && null != accountsPhoto.getFace_score() && accountsPhoto.getFace_score() > 0) {
                                faceScoreMap.put(photo_url, accountsPhoto.getFace_score());
                            }
                        }

                        continue i;
                    }
                }

                AccountsPhoto accountsPhoto = new AccountsPhoto();
                accountsPhoto.setAccount_uuid(accountUuid);
                accountsPhoto.setPhoto_url(newUrl);
                accountsPhoto.setSeq(i + 2);
                accountsPhoto.setBucket(bucket);
                accountsPhoto.setStatus(COVER_DEFAULT_STATUS);
                accountsPhoto.setCreate_time(currentTimeSeconds);
                accountsPhoto.setUpdate_time(currentTimeSeconds);
                accountsPhoto.setVerify_status(0);
                accountsPhoto.setLike_num(0L);
                accountsPhoto.setError_msg("");
                addList.add(accountsPhoto);
            }
            //遍历photoList，筛选出添加修改或删除的对象
            for (AccountsPhoto accountsPhoto : photoList) {
                Integer seq = accountsPhoto.getSeq();
                if (seq != 0) {
                    addList.add(accountsPhoto);
                } else {
                    deleteList.add(accountsPhoto);
                }
            }
            addList.add(0, accountAvatar);

        } else {
            AccountsPhoto accountAvatar = this.getAccountsPhotoByAvatar(accountUuid);
            //直接添加
            for (int i = 0; i < urlListNew.size(); i++) {
                String url = urlListNew.get(i);

                AccountsPhoto accountsPhoto = new AccountsPhoto();
                accountsPhoto.setAccount_uuid(accountUuid);
                accountsPhoto.setPhoto_url(url);
                accountsPhoto.setSeq(i + 2);
                accountsPhoto.setStatus(1);
                accountsPhoto.setBucket(bucket);
                accountsPhoto.setCreate_time(currentTimeSeconds);
                accountsPhoto.setUpdate_time(currentTimeSeconds);
                accountsPhoto.setVerify_status(0);
                accountsPhoto.setLike_num(0L);
                accountsPhoto.setError_msg("");
                addList.add(accountsPhoto);
            }
            addList.add(0, accountAvatar);
        }

        try {
            if (CollectionUtils.isNotEmpty(deleteList)) {
                accountsPhotoDao.remove(deleteList);
            }

            //设置相册的新接口需要添加人脸图片对比相似度和颜值分
            logger.info("相册比对.verifyStatusMap={},faceScoreMap={},addList={}", verifyStatusMap, faceScoreMap, JSON.toJSONString(addList));
            if (null != verifyStatusMap && null != faceScoreMap) {
                for (AccountsPhoto addPhoto : addList) {

                    // 2024.06.07 文件使用，通知图片系统
                    BuryService.pushToPicSysUsedFileBiz(CommConst.APPCODE_TAQU, addPhoto.getPhoto_url(), CommConst.AVATAR_BUCKET, DateUtil.currentTimeSeconds());

                    if (null != verifyStatusMap.get(addPhoto.getPhoto_url())) {
                        int verifyStatus = verifyStatusMap.get(addPhoto.getPhoto_url());
                        addPhoto.setVerify_status(verifyStatus);
                        if (AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(verifyStatus)) {
                            addPhoto.setError_msg(CodeStatus.ALIYUN_VERIFY_FALI_MSG.getReasonPhrase());
                        }
                    }
                    Integer faceScore = faceScoreMap.get(addPhoto.getPhoto_url());
                    if (null == faceScore || UN_FACE_SCORE.equals(faceScore)) {
                        addPhoto.setFace_score(0);
                    } else {
                        addPhoto.setFace_score(faceScore);
                    }

                    if (isReal) {
                        // 真人认证
                        if (addPhoto.getVerify_status() == 1 && PHOTO_UNCHECK_STATUS_ARR.contains(addPhoto.getStatus())) {
                            addPhoto.setStatus(COVER_DEFAULT_STATUS);
                        } else {
                            if (addPhoto.getSeq() != 1 && PHOTO_UNCHECK_STATUS_ARR.contains(addPhoto.getStatus())) {
                                addPhoto.setStatus(AccountsPhoto.Status.ONLY_AUTO_CHECK.getValue());
                                needPushToHigh.add(addPhoto);
                            }
                        }
                    } else {
                        // 非本人图片机审
                        if (addPhoto.getSeq() != 1 && PHOTO_UNCHECK_STATUS_ARR.contains(addPhoto.getStatus())) {
                            addPhoto.setStatus(AccountsPhoto.Status.ONLY_AUTO_CHECK.getValue());
                            needPushToHigh.add(addPhoto);
                        }
                    }
                }
                //保存人脸图片对比日志
                SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).batchSave(accountUuid, addList, similarityScoreMap);
            }

            addList = accountsPhotoDao.merge(addList);
            // 图片向量比对结果入库
            if (isReal && isFemale) {
                addList.stream().filter(p -> p.getSeq() > 1).forEach(p -> {
                    String key = PHOTO_VECTOR_BIZ_ID_MAPPING.setArg(accountUuid);
                    String val = (String) accountBizStringRedisTemplate.opsForHash().get(key, p.getPhoto_url());
                    if (StringUtils.isNotBlank(val)) {
                        ImageCompareRecord record = JsonUtils.stringToObject(val, ImageCompareRecord.class);
                        record.setOriginId(p.getId());
                        TransactionWrapper.me().wrapQuietly(() -> SpringContextHolder.getBean(ImageCompareRecordDao.class).merge(record));
                        accountBizStringRedisTemplate.opsForHash().delete(key, p.getPhoto_url());
                    }
                });
            }
            accountsPhotoDao.flush();

            // 2024.12.10 设置相册后，清除我的生活数据
            accountsLifeService.resetEmpty(accountUuid);

        } catch (ObjectOptimisticLockingFailureException e) {
            logger.warn("设置图片失败，图片可能已被删除，主键插入", e);
            throw new ServiceException("设置图片失败，请刷新后重试。");
        }

        this.setAccountsPhotoToRedis(addList, accountUuid);

        // 更新redis中用户信息缓存
        Map<String, String> infoCache = Maps.newHashMap();
        infoCache.put("photo_num", String.valueOf(Math.max(0, addList.size() - 1)));//第一张是头像，不能算在照片数量中去
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);

        List<String> deleteUrlList = Lists.newArrayList();
        if (!deleteList.isEmpty()) {
            for (AccountsPhoto accountsPhoto : deleteList) {
                //还得判断是不是默认头像或违规头像。不能删除了
                Integer status = accountsPhoto.getStatus();
                if (status != 3 && status != -1) {
                    deleteUrlList.add(accountsPhoto.getPhoto_url());
                }

                SoaService.sendReviewPhotoSkip(null, accountsPhoto.getId(), accountsPhoto.getPhoto_url(), "");
                LOGGER.info("图片数据变更.相册数据从审核队列中移除.真人相册uuid={}.id={}", accountsPhoto.getAccount_uuid(), accountsPhoto.getId().toString());
                // 风控向量删除
//                String riskBizId = SpringContextHolder.getBean(ImageCompareRecordDao.class).getBizId(accountsPhoto.getId(), ImageCompareRecord.ALBUM);
//                if (StringUtils.isNotBlank(riskBizId)) {
//                    ImgVectorRemoveDto dto = new ImgVectorRemoveDto();
//                    dto.setSenderUuid(accountUuid);
//                    dto.setBizId(riskBizId);
//                    dto.setSafeId(RiskSafeIdConstant.REAL_PERSON_COVER);
//                    RiskSafeService.imageVectorDelete(dto);
//                }
            }
        }
        // 机审状态是8才要推 2024.05.17 不处理
        for (AccountsPhoto item : needPushToHigh) {
            if (Objects.equals(item.getStatus(), AccountsPhoto.Status.ONLY_AUTO_CHECK.getValue())) {

            }
        }
        // 判断用户是否无真人认证图片
        accountsService.checkAccountEmptyFacePhoto(accountUuid, smid);

        // 相册修改事件
        Map<String, Object> map = new HashMap<>();
        map.put("account_uuid", accountUuid);
        map.put("create_time", DateUtil.currentTimeSeconds());
//        kafkaSinkUtil.push(AccountPhotoNumUpdateTask.TOPIC, map);
        SpringContextHolder.getBean(AccountPhotoNumUpdateTask.class).sendAuditEvent(map);
        SpringContextHolder.getBean(AllureService.class).refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);

        return deleteUrlList;
    }

    /**
     * 忽略头像+相册 V5 支撑快审
     *
     * @return
     */
    @Transactional
    public Map<String, List<AccountsPhoto>> photoIgnoreV5(Map<Long, String> photoMap, String operatorToken, Integer checkType) {
        // 1-真人认证头像 2-非真人认证头像 3-真人相册
        if (checkType != 2) {
            // 移动重新加入
            return photoIgnoreV4(photoMap, operatorToken, checkType);
        }

        Set<Long> photoIds = photoMap.keySet();
        // 需要操作的id
        List<Long> updateIds = new ArrayList<>();
        //查询所有要忽略的图片
        List<AccountsPhoto> photoList = accountsPhotoDao.findAccountsPhotoInIds(Lists.newArrayList(photoIds));
        // 存储更新的图片
        Set<String> finishAvatarRealPersonUuid = new HashSet<>();
        Map<String, List<AccountsPhoto>> resultV5 = new HashMap<>();
        for (AccountsPhoto accountsPhoto : photoList) {
            Long id = accountsPhoto.getId();
            // 如果不是待审核状态就跳过
            if (!PHOTO_UNCHECK_STATUS_ARR.contains(accountsPhoto.getStatus())) {
                continue;
            }
            String photoUrl = AvatarHandleService.getAvatarOfSavePhoto(accountsPhoto.getPhoto_url());
            // 图片地址相同才允许审核通过
            if (StringUtils.isBlank(photoMap.get(id)) || Objects.equals(AvatarHandleService.getAvatarOfSavePhoto(photoMap.get(id)), photoUrl)) {
                String faceCertification = accountsInfoService.getFaceCertification(accountsPhoto.getAccount_uuid());
                Boolean isAvatarSameWithFace = accountsInfoService.isAvatarSameWithFace(accountsPhoto.getAccount_uuid());
                LOGGER.info("非真人头像审核.uuid={}.faceCertification={}.isAvatarSameWithFace={}", accountsPhoto.getAccount_uuid(), faceCertification, isAvatarSameWithFace);
                if (CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString().equals(faceCertification) && isAvatarSameWithFace) {
                    // 真人认证
                    finishAvatarRealPersonUuid.add(accountsPhoto.getAccount_uuid());
                    updateIds.add(id);
                    photoMap.remove(id);
                    logger.info("非真人头像审核.uuid=[{}]", accountsPhoto.getAccount_uuid());
                    List<AccountsPhoto> photos = resultV5.get(accountsPhoto.getAccount_uuid());
                    if (CollectionUtils.isEmpty(photos)) {
                        photos = new ArrayList<>();
                    }
                    photos.add(accountsPhoto);
                    resultV5.put(accountsPhoto.getAccount_uuid(), photos);
                }
            }
        }
        Map<String, List<AccountsPhoto>> resultV4 = new HashMap<>();
        if (MapUtils.isNotEmpty(photoMap)) {
            resultV4 = photoIgnoreV4(photoMap, operatorToken, checkType);
        }
        if (CollectionUtils.isEmpty(finishAvatarRealPersonUuid)) {
            return MapUtils.isNotEmpty(resultV4) ? resultV4 : new HashMap<>();
        }

        // 完成的头像
        for (String accountUuid : finishAvatarRealPersonUuid) {
            String infoRedisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
            Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"face_certification", "real_person_certification"}, "1", false, false).get(accountUuid);

            LOGGER.info("判断真人认证uuid={}, infoMap={}", accountUuid, JSON.toJSONString(infoMap));
            if (String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()).equals(MapUtils.getString(infoMap, "face_certification", "0"))
                    && !String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()).equals(MapUtils.getString(infoMap, "real_person_certification", "2"))) {
                // 保存真人认证记录
                AliyunLiveFaceDetect liveFaceDetect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
                String basePhotoUrl = "";
                if (liveFaceDetect != null && StringUtils.isNotBlank(liveFaceDetect.getBase_photo_url())) {
                    basePhotoUrl = liveFaceDetect.getBase_photo_url();
                }
                String remark = TemplateStringConst.getContent(TemplateStringConst.CERT_LOG_REMARK_REAL_PERSON_BIND, accountUuid, DateUtil.dateToString20(new Date()));
                BuryService.pushToCertLog(accountUuid, CommConst.NO_0, CertTypeEnum.REAL_PERSON.getValue(), accountUuid, AccountsCertLog.OperatorTypeEnum.BIND.getValue(), basePhotoUrl, remark);

                // 保存真人认证状态到数据库
                setRealPersonCertificationIntoDb(accountUuid, CommonAuditStatus.AUDIT_SUCCESS.getStatus());

                // 保存真人认证状态到缓存
                Map<String, String> infoCache = new HashMap<>();
                infoCache.put("real_person_certification", "1");
                // TODO: 2023/2/17 real_person_certification store to db
                infoCache.put("profile_verify_status", "1");
                infoCache.put("pre_profile_verify_status", "1");
                // profile_verify_status pre_profile_verify_status
                accountStringRedisTemplate.opsForHash().putAll(infoRedisKey, infoCache);
                setRealPersonCertificationStatusCache(accountUuid, RealPersonCertificationStatusEnum.AUDIT_SUCCESS);

                // 同时删除缓存信息
                clearPrevPassAvatarCache(accountUuid);

                // 新版推送im
                pushRealPersonCertificationSuccessIm(accountUuid);

                // 过审后解除高风险
                BuryService.pushToAccountLivingCert(accountUuid, CertTypeEnum.REAL_PERSON);

                // 相册审核事件上报
                photoAuditEventPushToKafka(accountUuid);

                // 过相册
                SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).processResponseV5(accountUuid);
                // 保存到记录表
                accountsRealPersonCertificationLogService.addLog(accountUuid);
                // 判断用户是否无真人认证图片
                accountsService.checkAccountEmptyFacePhotoBatch(Lists.newArrayList(accountUuid));
            }
        }
        // 移除
        if (CollectionUtils.isNotEmpty(updateIds)) {
            // 释放审核锁 移除队列
            removeCheckZSetAndLock(new ArrayList<>(updateIds), operatorToken, new ArrayList<>(finishAvatarRealPersonUuid), PhotoOrignCheckEnum.NOT_REAL_PERSON_AVATAR.getValue());
        }

        resultV5.putAll(resultV4);
        return MapUtils.isNotEmpty(resultV5) ? resultV5 : new HashMap<>();
    }

    @Transactional
    public void setRealPersonCertificationIntoDb(String accountUuid, Integer status) {
        AccountsInfo accountsInfo = accountsInfoDao.getByAccountUuid(accountUuid);
        accountsInfo.setReal_person_certification(status);
        accountsInfoDao.merge(accountsInfo);
    }

    /**
     * 忽略头像+相册
     *
     * @return
     */
    @Transactional
    public Map<String, List<AccountsPhoto>> photoIgnoreV4(Map<Long, String> photoMap, String operatorToken, Integer orignCheck) {
        Set<Long> photoIds = photoMap.keySet();
        // 需要操作的id
        Set<Long> updatePhotoIds = Sets.newHashSet();
        List<String> updateAccountUuids = new ArrayList<>();
        //查询所有要忽略的图片
        List<AccountsPhoto> photoList = accountsPhotoDao.findAccountsPhotoInIds(Lists.newArrayList(photoIds));
        // 存储更新的图片
        List<AccountsPhoto> updateList = Lists.newArrayList();
        long currentTimeSeconds = DateUtil.currentTimeSeconds();

        Set<String> finishCoverUuid = new HashSet<>();
        // 旧版判断逻辑
        Set<String> finishAvatarRealPersonUuid = new HashSet<>();
        Set<String> finishAvatarUuid = new HashSet<>();
        Set<String> finishUpdateDefaultAvatarUuid = new HashSet<>();

        Long id = null;
        String photoUrl = null;

        for (AccountsPhoto accountsPhoto : photoList) {
            id = accountsPhoto.getId();
            // 如果不是待审核状态就跳过
            if (!PHOTO_UNCHECK_STATUS_ARR.contains(accountsPhoto.getStatus())) {
                continue;
            }
            photoUrl = AvatarHandleService.getAvatarOfSavePhoto(accountsPhoto.getPhoto_url());
            // 图片地址相同才允许审核通过
            if (StringUtils.isBlank(photoMap.get(id)) || Objects.equals(AvatarHandleService.getAvatarOfSavePhoto(photoMap.get(id)), photoUrl)) {
                LOGGER.info("操作员忽略图片v4,图片id=[{}],uuid=[{}],url=[{}]", accountsPhoto.getId(), accountsPhoto.getAccount_uuid(), accountsPhoto.getPhoto_url());
                // 非真人相册，直接状态修改为8 机审
                if (accountsPhoto.getSeq() > 1 && !Objects.equals(accountsPhoto.getVerify_status(), 1)) {
                    accountsPhoto.setStatus(AccountsPhoto.Status.ONLY_AUTO_CHECK.getValue());
                } else {
                    accountsPhoto.setStatus(AccountsPhoto.Status.PASS.getValue());
                }
                // 2021.01.25 为了兼容默认头像被审核情况，判断如果忽略的图片地址是默认头像，则状态设置为3
                if (AvatarHandleService.isDefAvatar(photoUrl)) {
                    accountsPhoto.setStatus(AccountsPhoto.Status.DEFAULT_PIC.getValue());
                }
                accountsPhoto.setUpdate_time(currentTimeSeconds);
                updateAccountUuids.add(accountsPhoto.getAccount_uuid());
                updatePhotoIds.add(id);
                updateList.add(accountsPhoto);
                // 邀请推送 审核真人头像
                if (accountsPhoto.getSeq() != 1) {
                    finishCoverUuid.add(accountsPhoto.getAccount_uuid());
                } else if (accountsPhoto.getSeq() == 1 && accountsPhoto.getVerify_status() != null && accountsPhoto.getVerify_status().equals(1)) {
                    // 头像,且通过真人认证
                    finishAvatarRealPersonUuid.add(accountsPhoto.getAccount_uuid());
                }
                if (accountsPhoto.getSeq() == 1) {
                    finishAvatarUuid.add(accountsPhoto.getAccount_uuid());
                }
            }
        }
        // 此处应该是updateList吧
        // 如果有改到这块，把photoList 改为 updateList 测试
        accountsPhotoDao.merge(photoList);
        accountsPhotoDao.flush();

        Map<String, List<AccountsPhoto>> result = new HashMap<>();
        Set<String> accountUuidSet = Sets.newHashSet();
        try {
            for (AccountsPhoto accountsPhoto : updateList) {
                //设置返回值
                String accountUuid = accountsPhoto.getAccount_uuid();
                List<AccountsPhoto> accountPhotoList = result.computeIfAbsent(accountUuid, k -> new ArrayList<>());
                accountPhotoList.add(accountsPhoto);
                accountUuidSet.add(accountsPhoto.getAccount_uuid());
                // 修改默认头像推送
                Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"avatar"}, "1", false, false).get(accountUuid);
                String avatar = MapUtils.getString(infoMap, "avatar");
                if (accountsPhoto.getSeq() == 1 && AvatarHandleService.isDefAvatar(avatar)) {
                    finishUpdateDefaultAvatarUuid.add(accountUuid);
                    LOGGER.info("用户uuid={}更换了默认头像={}", accountUuid, accountsPhoto.getPhoto_url());
                }

                //修改缓存
                this.updatePhotoCache(accountsPhoto);
            }
        } catch (Exception e) {
            logger.warn("审核图片，批量忽略，修改图片缓存异常.", e);
        }

        // ******** LIJIE 把计算数量移出方法外
//        // 根据uuid查询用户已审核通过的相册数，落地到account_info表中
//        accountsInfoService.updateAccountPhotoNumber(accountUuidSet, "db");

        //图片过审后如果改用户是阿里云人脸活体验证用户，则进行图片自动认证比对
        SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).batchVerifyFacePhoto(updateList);
        // 图片审核后判断是否完成任务
        SpringContextHolder.getBean(AccountsPhotoService.class).checkFinishIncentiveTask(Lists.newArrayList(accountUuidSet));

        // 推队列-匹配高风险人像
        photoList.stream().filter(item -> {
            return item.getStatus().equals(AccountsPhoto.Status.PASS.getValue());
        }).forEach(item -> {

        });

        // 判断用户是否无真人认证图片
        accountsService.checkAccountEmptyFacePhotoBatch(Lists.newArrayList(accountUuidSet));

        // 相册审核事件上报
        accountUuidSet.forEach(this::photoAuditEventPushToKafka);

        // 释放审核锁 移除队列
        if (orignCheck == null) {
            removeCheckZSetAndLock(new ArrayList<>(updatePhotoIds), operatorToken, updateAccountUuids);
        } else {
            removeCheckZSetAndLock(new ArrayList<>(updatePhotoIds), operatorToken, updateAccountUuids, orignCheck);
        }

        String content = orignCheck + "|" + id + "|" + photoUrl;
        String pushReviewMd5 = Md5Util.encryptSHA1(content);
        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO_PUSH_REVIEW.setArg(pushReviewMd5);
        Boolean keyExist = accountStringRedisTemplate.hasKey(redisKey);
        logger.info("content={},pushReviewMd5={},redisKey={},keyExist={}", content, pushReviewMd5, redisKey, keyExist);
        accountStringRedisTemplate.delete(redisKey);

        return result;
    }

    /**
     * 推送真人成功IM
     *
     * @param accountUuid
     */
    private void pushRealPersonCertificationSuccessIm(String accountUuid) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }
        // 推到队列
        try {
            HashMap<String, Object> messageMap = Maps.newHashMap();
            messageMap.put("target_type", "uuid");
            messageMap.put("target", accountUuid);
            messageMap.put("targets", Lists.newArrayList());
            messageMap.put("fields", Lists.newArrayList(""));
            messageMap.put("event", "real_person_certification_success");

            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("go_im_send_com_msg", messageMap, null);
            if (mqResponse.fail()) {
                LOGGER.warn("真人认证状态更新,推送到IM队列失败,用户uuid:{}", accountUuid);
            } else {
                LOGGER.info("真人认证状态更新,推送到IM队列成功,用户uuid:{},data={}", accountUuid, JSON.toJSONString(messageMap));
            }
        } catch (Exception e) {
            LOGGER.error("真人认证状态更新,推送到IM队列失败,用户uuid:{}", accountUuid, e);
        }

    }

    /**
     * 移除
     *
     * @param ids
     */
    @Transactional
    public void removeCheckZSetAndLock(List<Long> ids, String operatorToken, List<String> accountUuids, Integer photoOrignCheck) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        accountsPhotoDao.flush();
        logger.info("审核锁.解锁.operatorToken={}", operatorToken);
    }

    /**
     * 移除
     *
     * @param ids
     */
    @Transactional
    public void removeCheckZSetAndLock(List<Long> ids, String operatorToken, List<String> accountUuids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        accountsPhotoDao.flush();
    }

    /**
     * 判断是否完成任务
     *
     * @param accountUuidList
     * <AUTHOR>
     * @date 2020/03/17 18:04
     */
    public void checkFinishIncentiveTask(List<String> accountUuidList) {
        if (accountUuidList == null || accountUuidList.isEmpty()) {
            return;
        }

        for (String accountUuid : accountUuidList) {
            List<Map<String, String>> photoMapList = this.getAccountsAllPhoto(accountUuid, true);
            if (CollectionUtils.isNotEmpty(photoMapList)) {
                LOGGER.info("完成图片审核,用户uuid={},审核图片数={}", accountUuid, photoMapList.size());
            }

            // 2023.08.11 - S服务任务的需求，把原来4张完成社区任务改成3张完成社区任务
            if (photoMapList != null && photoMapList.size() >= 3) {
                try {
                    buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.PERFECT_PHOTO.getType());
                } catch (Exception e) {
                    LOGGER.error("相册审核完成，通知社区任务完成处理失败。", e);
                }
            }
        }
    }

    /**
     * 推送任务，新需求 上传成功即可发送奖励
     *
     */
    public void checkFinishIncentiveTaskWithoutCheck(String accountUuid) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }

        List<Map<String, String>> photoMapList = this.getAccountsAllPhoto(accountUuid, false);
        if (CollectionUtils.isNotEmpty(photoMapList)) {
            LOGGER.info("完成图片审核,用户uuid={},图片数={}", accountUuid, photoMapList.size());
        }

        // 目前数量大于等于4 即完成任务
        if (photoMapList != null && photoMapList.size() >= 5) {
            try {
                buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.PERFECT_PHOTO.getType());
            } catch (Exception e) {
                LOGGER.error("相册审核完成，通知社区任务完成处理失败。", e);
            }
        }
    }

    @Transactional
    public Map<String, String> updateAccountsPhotoWithPunish(Integer status, Long photoId, String url, String operatorToken) {
        return updateAccountsPhotoWithPunish(status, photoId, url, operatorToken, null, null);
    }

    /**
     * 审核图片 删除图片 重新排序，处罚仅支持单独设置，不支持批量
     * @param status 头像抽查审核状态 1-未处理 -1已处罚 2已忽略 3默认头像，只支持：-1
     * @param photoId
     * @param url
     * @param operatorToken
     * @param checkType 1-真人认证审核、2-非真人认证审核
     * @param senderUuid
     * @return
     */
    @Transactional
    public Map<String, String> updateAccountsPhotoWithPunish(Integer status, Long photoId, String url, String operatorToken, Integer checkType, String senderUuid) {
        String removePhotoUrl = "";
        Map<String, String> returnUrlMap = Maps.newHashMap();
        //校验
        if (status == null || status != AccountsPhoto.Status.ILLEGAL.getValue()) {
            throw new ServiceException("设置照片状态，传入错误状态，status:" + status);
        }
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        //查询所有需要更新的图片
        AccountsPhoto accountsPhoto = accountsPhotoDao.findById(photoId);

        if (photoId!= null) {
            // 2024.06.07 审核拒绝，通知图片系统禁用
            // 2024.12.23 业务也有禁用，可能出现并发问题，延迟3s推。
            BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, url, CommConst.AVATAR_BUCKET, 1, DateUtil.currentTimeSeconds(), 3L);

            //审核不通过
            if (accountsPhoto == null) {
                // photo_no_exists 该图片可能已被删除，刷新后重试。
                removeCheckZSetAndLock(Lists.newArrayList(photoId), operatorToken, new ArrayList<>());
                return returnUrlMap;
            } else if (StringUtils.isNotBlank(url) && !Objects.equals(AvatarHandleService.getAvatarOfSavePhoto(url), AvatarHandleService.getAvatarOfSavePhoto(accountsPhoto.getPhoto_url()))) {
                // 2024/1/20 若历史人审头像被拒，只修改为默认头像，其他状态不改
                if ((Objects.equals(checkType, 1) || Objects.equals(checkType, 2)) && StringUtils.isNotBlank(senderUuid) ) {
                    AccountsPhoto preAccountPhoto = getPreAvatarInfo(senderUuid);
                    if (Objects.nonNull(preAccountPhoto)) {
                        logger.info("[风控安全审核]人审通过头像重新被拒处理,accountUuid:{}", senderUuid);
                        Accounts accounts = accountsManager.getByUuid(senderUuid, false);
                        int gender = Optional.ofNullable(accounts)
                                .map(Accounts::getSex_type)
                                .orElse(0);

                        String defaultPhotoUrl = AvatarHandleService.getDefAvatar(gender);
                        if (accounts != null) {
                            accounts.setAvatar(defaultPhotoUrl);
                            accounts.setCs_id(currentTimeSeconds);
                            accounts.setForum_status(status);//头像抽查审核状态 1未处理 -1已处罚 2已忽略
                            accountsManager.merge(accounts);

                            preAccountPhoto.setPhoto_url(StringUtils.remove(defaultPhotoUrl, AccountsService.regReplaceAvatarPart));
                            accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.ACCOUNT_BEFORE_AVATAR_PHOTO.setArg(senderUuid), JSON.toJSONString(preAccountPhoto), 1L, TimeUnit.DAYS);

                            Map<String, String> hashValues = new HashMap<>();
                            hashValues.put("avatar", preAccountPhoto.getPhoto_url());
                            accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(senderUuid), hashValues);
                        }
                    }
                }

                // photo_url_update 该图片可能已被修改，刷新后重试。
                removeCheckZSetAndLock(Lists.newArrayList(photoId), operatorToken, Arrays.asList(accountsPhoto.getAccount_uuid()));
                return returnUrlMap;
            }

            //单独处罚
            Integer accountsPhotoStatus = accountsPhoto.getStatus();
            if (accountsPhotoStatus != 1 && accountsPhotoStatus != 2 && accountsPhotoStatus != 4 && accountsPhotoStatus != 5 && accountsPhotoStatus != 6 && accountsPhotoStatus != 7 && accountsPhotoStatus != 8) {
                //不等于1-未处理，返回失败 photo_checked 该图片已被处罚，刷新后重试。
                removeCheckZSetAndLock(Lists.newArrayList(photoId), operatorToken, new ArrayList<>());
                return returnUrlMap;
            }

            //还得判断是不是默认头像或违规头像。不能删除了
            if (accountsPhotoStatus != 3 && accountsPhotoStatus != -1) {
                returnUrlMap.put("id", accountsPhoto.getId().toString());
                returnUrlMap.put("photo_url", accountsPhoto.getPhoto_url());
                returnUrlMap.put("status", accountsPhoto.getStatus().toString());
                returnUrlMap.put("seq", accountsPhoto.getSeq().toString());
                returnUrlMap.put("bucket", accountsPhoto.getBucket());
                returnUrlMap.put("account_uuid", accountsPhoto.getAccount_uuid());
                returnUrlMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
                returnUrlMap.put("error_msg", accountsPhoto.getError_msg());
            }

            Integer seq = accountsPhoto.getSeq();
            String accountUuid = accountsPhoto.getAccount_uuid();
            //根据uuid查询所有个人图片
            List<AccountsPhoto> accountsPhotoList = accountsPhotoDao.findByUuid(accountUuid);

            if (CollectionUtils.isNotEmpty(accountsPhotoList)) {
                //如果是第一张图片(即头像)，更换为默认图片
                if (seq == 1) {
                    AccountsPhoto preAccountPhoto = getPreAvatarInfo(accountUuid);
                    boolean usePreAvatar = preAccountPhoto != null && StringUtils.isNotBlank(preAccountPhoto.getPhoto_url()) && preAccountPhoto.getStatus() == AccountsPhoto.Status.PASS.getValue();

                    if (usePreAvatar) { // 回滚到上一张已通过头像
                        accountsPhoto.setPhoto_url(preAccountPhoto.getPhoto_url());
                        accountsPhoto.setBucket(preAccountPhoto.getBucket());
                        accountsPhoto.setSeq(preAccountPhoto.getSeq());
                        accountsPhoto.setStatus(preAccountPhoto.getStatus());
                        accountsPhoto.setVerify_status(preAccountPhoto.getVerify_status());
                        accountsPhoto.setError_msg(preAccountPhoto.getError_msg());
                        accountsPhoto.setFace_score(preAccountPhoto.getFace_score());
                        accountsPhoto.setRisk_description(preAccountPhoto.getRisk_description());
                        accountsPhoto.setRisk_photo_url(preAccountPhoto.getRisk_photo_url());
                        accountsPhoto.setLike_num(preAccountPhoto.getLike_num());

                        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
                        if (Objects.nonNull(accounts)) {
                            accounts.setAvatar(preAccountPhoto.getPhoto_url());
                            accountsManager.merge(accounts);
                        }
                    } else {  // 回滚为默认头像
                        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
                        int gender = Optional.ofNullable(accounts)
                                .map(Accounts::getSex_type)
                                .orElse(0);

                        String defaultPhotoUrl = AvatarHandleService.getDefAvatar(gender);
                        if (accounts != null) {
                            accounts.setAvatar(defaultPhotoUrl);
                            accounts.setCs_id(currentTimeSeconds);
                            accounts.setForum_status(status);//头像抽查审核状态 1未处理 -1已处罚 2已忽略
                            accountsManager.merge(accounts);
                        }

                        if (StringUtils.isBlank(accountsPhoto.getBucket())) {
                            accountsPhoto.setBucket(DEFAULT_AVATAR_BUCKET);
                        }

                        accountsPhoto.setStatus(-1);
                        accountsPhoto.setPhoto_url(StringUtils.remove(defaultPhotoUrl, AccountsService.regReplaceAvatarPart));
                        accountsPhoto.setVerify_status(AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus());
                        accountsPhoto.setError_msg("");
                    }
                    accountsPhoto.setUpdate_time(currentTimeSeconds);
                    accountsPhoto = accountsPhotoDao.merge(accountsPhoto);

                    LOGGER.info("操作员更新图片状态v4,id=[{}],uuid=[{}],url=[{}]", accountsPhoto.getId(), accountsPhoto.getAccount_uuid(), accountsPhoto.getPhoto_url());

                    Map<String, String> photoMap = new HashMap<String, String>();
                    photoMap.put("id", accountsPhoto.getId().toString());
                    photoMap.put("photo_url", accountsPhoto.getPhoto_url());
                    photoMap.put("status", accountsPhoto.getStatus().toString());
                    photoMap.put("seq", accountsPhoto.getSeq().toString());
                    photoMap.put("bucket", accountsPhoto.getBucket());
                    photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
                    photoMap.put("error_msg", accountsPhoto.getError_msg());
                    photoMap.put("like_num", accountsPhoto.getLike_num() == null ? "0" : accountsPhoto.getLike_num().toString());
                    String redisKey = RedisKeyConstant.ACCOUNT_PHOTO.setArg(accountUuid);
                    accountStringRedisTemplate.opsForHash().put(redisKey, accountsPhoto.getSeq().toString(), JsonUtils.objectToString(photoMap));
                    accountStringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);

                    this.avatarCheckedUpdateCache(accountsPhoto);
                    updateAvatarFaceStatus(accountsPhoto.getAccount_uuid(), "0", "0");
                    this.removeCheckZSetAndLock(Collections.singletonList(accountsPhoto.getId()), operatorToken, Collections.singletonList(accountsPhoto.getAccount_uuid()));

                    // 上一张审核通过的照片清除掉（只使用一次）
                    if (usePreAvatar) {
                        clearPrevPassAvatarCache(accountUuid);
                    }

                    // 头像审核失败 真人认证审核状态重置
                    setRealPersonCertificationStatusCache(accountUuid, RealPersonCertificationStatusEnum.NONE_OR_AUDIT_FAIL);

                } else {
                    //用户相册，删除掉审核不通过的那张
                    List<AccountsPhoto> newPhotoList = null;
                    // 移除
                    newPhotoList = accountsPhotoList.stream().filter(photoSel -> !Objects.equals(photoSel.getSeq(), seq)).collect(Collectors.toList());

                    AccountsPhoto checkedAvatar = null;
                    //重新设置相册顺序为1、2、3、4.....，seq为1的自动变成头像
                    int newPhotoSeq = 1;
                    for (AccountsPhoto photo : newPhotoList) {
                        photo.setSeq(newPhotoSeq);
                        if (newPhotoSeq == 1) {
                            checkedAvatar = photo;
                        }
                        newPhotoSeq++;
                    }
                    LOGGER.info("操作员更新图片状态v4,id=[{}],uuid=[{}],url=[{}]", accountsPhoto.getId(), accountsPhoto.getAccount_uuid(), accountsPhoto.getPhoto_url());
                    // 删除的url
                    removePhotoUrl = accountsPhoto.getPhoto_url();
                    // 相册删除
                    accountsPhotoDao.removeById(accountsPhoto.getId());
                    accountsPhotoDao.merge(newPhotoList);

                    // ******** LIJIE 把计算数量移出方法外
//                        Set<String> accountUuidSet = newPhotoList.stream().map(AccountsPhoto::getAccount_uuid).collect(Collectors.toSet());
//                        // 根据uuid查询用户已审核通过的相册数，落地到account_info表中
//                        accountsInfoService.updateAccountPhotoNumber(accountUuidSet, "db");

                    if (checkedAvatar != null) {
                        //更新accounts表与缓存
                        this.updateAccountAvatar(checkedAvatar);
                        //TODO 2021.05.06 修改后，直接取图片的认证情况 需要观察
                        Integer verifyStatus = checkedAvatar.getVerify_status();
                        //更新"头像人像"状态 这个很影响审核处理速度，改成直接取图片值
//                            int isFace = AliyunLiveFaceDetectService.detectIsFaceV2(
//                                    accountUuid,
//                                    regReplaceAvatarPart + AvatarHandleService.getAvatarOfSavePhoto(checkedAvatar.getPhoto_url())
//                            );
                        if (Objects.equals(checkedAvatar.getStatus(), 2) || Objects.equals(checkedAvatar.getStatus(), 3)) {
                            updateAvatarFaceStatus(accountUuid, String.valueOf(verifyStatus), String.valueOf(verifyStatus));
                        } else {
                            updateAvatarFaceStatus(accountUuid, null, String.valueOf(verifyStatus));
                        }
                    }

                    this.setAccountsPhotoToRedis(newPhotoList, accountUuid);
                    this.removeCheckZSetAndLock(Collections.singletonList(accountsPhoto.getId()), operatorToken, Arrays.asList(accountsPhoto.getAccount_uuid()));
                }
            }
            // 判断用户是否无真人认证图片
            accountsService.checkAccountEmptyFacePhoto(accountUuid);

            // 相册审核事件上报
            photoAuditEventPushToKafka(accountUuid);
        }
        if (StringUtils.isNotBlank(removePhotoUrl)) {
            String deletePhotoKey = RedisKeyConstant.ACCOUNT_DELETE_PHOTO_URL_MD5.setArg(Md5Util.encryptSHA1(removePhotoUrl));
            // 删除锁住30分钟 url的md5
            accountStringRedisTemplate.opsForValue().set(deletePhotoKey, "1", 30L, TimeUnit.MINUTES);
            logger.info("删除图片,url=[{}],redisKey=[{}].30分钟内同一个地址不再上传", removePhotoUrl, deletePhotoKey);
        }
        return returnUrlMap;
    }

    private AccountsPhoto getPreAvatarInfo(String uuid) {
        String redisKey = RedisKeyConstant.ACCOUNT_BEFORE_AVATAR_PHOTO.setArg(uuid);
        String photoStr = accountStringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isBlank(photoStr)) {
            return null;
        }
        return JSONObject.parseObject(photoStr, AccountsPhoto.class);
    }

    private void clearPrevPassAvatarCache(String uuid) {
        String redisKey = RedisKeyConstant.ACCOUNT_BEFORE_AVATAR_PHOTO.setArg(uuid);
        accountStringRedisTemplate.delete(redisKey);
    }

    private void photoAuditEventPushToKafka(String uuid) {
        Map<String, Object> map = new HashMap<>();
        map.put("account_uuid", uuid);
        map.put("create_time", System.currentTimeMillis() / 1000);

        kafkaSinkUtil.push("account_photo_audit_event", map);
    }

    @Transactional
    public void updatePhotoCache(AccountsPhoto accountsPhoto) {
        Map<String, String> photoMap = new HashMap<String, String>();
        photoMap.put("id", accountsPhoto.getId().toString());
        photoMap.put("photo_url", accountsPhoto.getPhoto_url());
        photoMap.put("status", accountsPhoto.getStatus().toString());
        photoMap.put("seq", accountsPhoto.getSeq().toString());
        photoMap.put("bucket", accountsPhoto.getBucket());
        photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
        photoMap.put("error_msg", accountsPhoto.getError_msg());
        photoMap.put("like_num", accountsPhoto.getLike_num() == null ? "0" : accountsPhoto.getLike_num().toString());
        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO.setArg(accountsPhoto.getAccount_uuid());
        accountStringRedisTemplate.opsForHash().put(redisKey, accountsPhoto.getSeq().toString(), new Gson().toJson(photoMap));
        accountStringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);

        //如果是忽略操作，并且是头像，更新缓存
        if (Objects.equals(accountsPhoto.getStatus(), 2) && Objects.equals(accountsPhoto.getSeq(), 1)) {
//            this.avatarCheckedUpdateCache(accountsPhoto);
            updateAccountAvatar(accountsPhoto);
            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
                    accountsPhoto.getAccount_uuid(),
                    new String[]{"pre_avatar_face_status"}
            );
            String preAvatarFaceStatus = MapUtils.getString(infoMap, "pre_avatar_face_status", "0");
            updateAvatarFaceStatus(accountsPhoto.getAccount_uuid(), preAvatarFaceStatus, preAvatarFaceStatus);
        }
    }

    private void avatarCheckedUpdateCache(AccountsPhoto accountsPhoto) {
        Map<String, String> hashValues = new HashMap<>();
        hashValues.put("avatar", accountsPhoto.getPhoto_url());
        hashValues.put("avatar_origin", accountsPhoto.getPhoto_url());
        hashValues.put("avatar_status", "1");
        hashValues.put("profile_verify_status", String.valueOf(accountsPhoto.getVerify_status()));
        hashValues.put("pre_profile_verify_status", String.valueOf(accountsPhoto.getVerify_status()));
        hashValues.put("profile_error_msg", accountsPhoto.getError_msg());
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountsPhoto.getAccount_uuid()), hashValues);
    }

    private void avatarUncheckedUpdateCache(AccountsPhoto accountsPhoto) {
        Map<String, String> hashValues = new HashMap<>();
        hashValues.put("avatar_origin", accountsPhoto.getPhoto_url());
        hashValues.put("avatar_status", "0");
        hashValues.put("profile_verify_status", String.valueOf(accountsPhoto.getVerify_status()));
        hashValues.put("profile_error_msg", accountsPhoto.getError_msg());
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountsPhoto.getAccount_uuid()), hashValues);
    }

    public void updateAvatarFaceStatus(String accountUuid, String preAvatarFaceStatus, String avatarFaceStatus) {
        Map<String, String> map = Maps.newHashMap();
        if (Objects.nonNull(preAvatarFaceStatus)) {
            map.put("pre_avatar_face_status", preAvatarFaceStatus);
        }
        if (Objects.nonNull(avatarFaceStatus)) {
            map.put("avatar_face_status", avatarFaceStatus);
        }
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), map);
    }

    private Map<String, String> getAccountsPhotoByAvatarMap(String accountUuid) {
        Map<String, String> photoMap = new HashMap<>();
        AccountsPhoto accountsPhoto = this.getAccountsPhotoByAvatar(accountUuid);

        photoMap.put("id", StringUtil.nullNumberToEmptyString(accountsPhoto.getId()));
        photoMap.put("photo_url", StringUtils.trimToEmpty(accountsPhoto.getPhoto_url()));
        photoMap.put("status", StringUtil.nullNumberToEmptyString(accountsPhoto.getStatus()));
        photoMap.put("seq", StringUtil.nullNumberToEmptyString(accountsPhoto.getSeq()));
        photoMap.put("bucket", StringUtils.trimToEmpty(accountsPhoto.getBucket()));
        photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
        photoMap.put("error_msg", accountsPhoto.getError_msg());
        return photoMap;
    }

    private AccountsPhoto getAccountsPhotoByAvatar(String accountUuid) {
        AccountsPhoto accountsPhoto = new AccountsPhoto();
        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts != null) {
            String avatar = accounts.getAvatar();
            if (StringUtils.isBlank(avatar)) {
                Integer sexType = accounts.getSex_type();
                if (sexType == null) {
                    sexType = 1;
                }
                avatar = AccountsService.getAvatarBySexType(sexType);//根据性别获取 yes

                accounts.setAvatar(avatar);
                accounts.setForum_status(3);
            }
            accountsPhoto = this.updateAvatar(accountUuid, avatar, accounts.getForum_status(), DateUtil.currentTimeSeconds());
        }
        return accountsPhoto;
    }

    /**
     * 数据库查用户图片（头像+相册）
     *
     * @param accountUuid
     * @param onlyCheck   是否只返回审核通过的
     * @return
     */
    private List<Map<String, String>> getAccountsPhotoAllByDB(String accountUuid, boolean onlyCheck) {
        List<Map<String, String>> result = this.mGetAccountsPhotoAllByDB(new String[]{accountUuid}, new boolean[]{onlyCheck}).get(accountUuid);
        return result == null ? Collections.emptyList() : result;
    }

    /**
     * 最原始查库
     *
     * @param accountUuid
     * @return
     */
    public List<AccountsPhoto> getAccountsPhotoByAccountUuidDB(String accountUuid){
        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuid(accountUuid);
        return photoList;
    }

    /**
     * 批量数据库查用户图片（头像+相册）
     *
     * @param uuids      用户uuid
     * @param onlyChecks 是否只返回审核通过的
     * @return
     */
    public Map<String, List<Map<String, String>>> mGetAccountsPhotoAllByDB(String[] uuids, boolean[] onlyChecks) {
        if (uuids.length == 0) {
            return Maps.newHashMap();
        }

        int len = uuids.length;
        Map<String, Boolean> onlyCheckMap = new HashMap<>(len);
        for (int i = 0; i < len; i++) {
            onlyCheckMap.put(uuids[i], onlyChecks[i]);
        }

        Map<String, List<Map<String, String>>> result = Maps.newLinkedHashMapWithExpectedSize(uuids.length);
        Map<String, List<Map<String, String>>> cacheList = Maps.newLinkedHashMapWithExpectedSize(uuids.length);
        CounterUntil.cachePenetrated("photo");
        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuidList(Arrays.asList(uuids));

        Arrays.stream(uuids).forEach(uuid -> {
            result.put(uuid, Lists.newArrayList());
            cacheList.put(uuid, Lists.newArrayList());
        });

        photoList.forEach(accountsPhoto -> {
            //判断如果名称空间不存在，设置为默认值
            if (StringUtils.isBlank(accountsPhoto.getBucket())) {
                accountsPhoto.setBucket(DEFAULT_AVATAR_BUCKET);
            }

            Map<String, String> photoMap = new HashMap<>();
            photoMap.put("id", accountsPhoto.getId().toString());
            // 2020.05.29 修复客户端bug
            photoMap.put("photo_url", AccountsInfoService.fixUrl(accountsPhoto.getPhoto_url()));
            photoMap.put("status", accountsPhoto.getStatus().toString());
            photoMap.put("seq", accountsPhoto.getSeq().toString());
            photoMap.put("bucket", accountsPhoto.getBucket());
            photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
            photoMap.put("error_msg", accountsPhoto.getError_msg());
            photoMap.put("like_num", accountsPhoto.getLike_num() == null ? "0" : accountsPhoto.getLike_num().toString());
            Integer verifyStatus = accountsPhoto.getVerify_status();
            //防止缓存error_msg为空的情况，下发默认的信息
            if (AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(verifyStatus) && StringUtils.isEmpty(accountsPhoto.getError_msg())) {
                photoMap.put("error_msg", CodeStatus.ALIYUN_VERIFY_FALI_MSG.getReasonPhrase());
            }
            boolean onlyCheck = onlyCheckMap.get(accountsPhoto.getAccount_uuid());
            if (!onlyCheck
                    || onlyCheck && (Objects.equals(accountsPhoto.getStatus(), 2) || Objects.equals(accountsPhoto.getStatus(), 3) || Objects.equals(accountsPhoto.getStatus(), 4))) {
                result.get(accountsPhoto.getAccount_uuid()).add(photoMap);
            }
            cacheList.get(accountsPhoto.getAccount_uuid()).add(photoMap);
        });

        // 回写缓存 2020.04.07 可能有问题，需要判断一下，如果只查询已审核通过的，可以不回写缓存，因为不一定是缓存没有值，可能只是缓存没有已审核通过的数据
        RedisSerializer<String> rs = accountStringRedisTemplate.getStringSerializer();
        accountStringRedisTemplate.executePipelined((RedisConnection connection) -> {
            cacheList.forEach((uuid, photoMapList) -> {
                byte[] key = rs.serialize(RedisKeyConstant.ACCOUNT_PHOTO.setArg(uuid));
                HashMap<byte[], byte[]> hashValues = new HashMap<>(AVATAR_PHOTO_LIMIT_20220215);
                x:for (int i = 0; i < AVATAR_PHOTO_LIMIT_20220215; i++) {
                    y:for (Map<String, String> photoMap : photoMapList) {
                        if(Objects.equals(i + 1, MapUtils.getInteger(photoMap, "seq"))) {
                            hashValues.put(rs.serialize(String.valueOf(i + 1)), rs.serialize(JsonUtils.objectToString(photoMap)));
                            continue x;
                        }
                    }
                    hashValues.put(rs.serialize(String.valueOf(i + 1)), rs.serialize(JsonUtils.objectToString(new HashMap<String, String>())));
                }
                connection.hMSet(key, hashValues);
                connection.expire(key, 24 * 60 * 60);
            });
            return null;
        }, rs);

        return result;
    }

    /**
     * @param uuids      用户uuid
     * @param onlyChecks 是否只返回审核通过的
     * @return
     */
    public Map<String, List<Map<String, String>>> mGetAccountsPhotoAllByRedis(String[] uuids, boolean[] onlyChecks) {
        //从缓存中取
        List<byte[]> hashKeys = Lists.newArrayList();
        RedisSerializer<String> rs = accountStringRedisTemplate.getStringSerializer();
        hashKeys.addAll(Lists.newArrayList(rs.serialize("1"), rs.serialize("2"), rs.serialize("3"), rs.serialize("4"),
                rs.serialize("5"), rs.serialize("6"), rs.serialize("7"), rs.serialize("8"), rs.serialize("9"),
                rs.serialize("10"), rs.serialize("11"), rs.serialize("12"), rs.serialize("13")));

        List<Object> valuesList = accountStringRedisTemplate.executePipelined((RedisConnection connection) -> {
            Arrays.stream(uuids).forEach(uuid -> {
                byte[] key = rs.serialize(RedisKeyConstant.ACCOUNT_PHOTO.setArg(uuid));
                connection.hMGet(key, hashKeys.toArray(new byte[hashKeys.size()][]));
            });
            return null;
        }, rs);

        int len = uuids.length;
        Map<String, Boolean> onlyCheckMap = new HashMap<>(len);
        for (int i = 0; i < len; i++) {
            onlyCheckMap.put(uuids[i], onlyChecks[i]);
        }

        Map<String, List<Map<String, String>>> result = Maps.newLinkedHashMapWithExpectedSize(uuids.length);
        for (int i = 0, size = uuids.length; i < size; i++) {
            String uuid = uuids[i];
            List<Object> values = valuesList.isEmpty() ? null : (List<Object>) valuesList.get(i);
            if (values == null || values.isEmpty() || values.contains(null)) {
                continue;
            }

            boolean onlyCheck = onlyCheckMap.get(uuid);
            List<Map<String, String>> photoMapList = Lists.newArrayList();
            for (Object photoObj : values) {
                String photoStr = (String) photoObj;
                if (StringUtils.isBlank(photoStr)) {
                    photoMapList.clear();
                    break;
                }

                Map<String, String> photoMap = new HashMap<>();
                try {
                    photoMap = JsonUtils.stringToObject(photoStr, new TypeReference<Map<String, String>>() {
                    });
                } catch (Exception e) {
                    logger.error("缓存中存在异常结构数据，photoStr：{}", photoStr, e);
                }

                if (!photoMap.isEmpty()) {
                    int status = MapUtils.getIntValue(photoMap, "status", 0);
                    if (StringUtils.isNoneBlank(photoMap.get("id"))
                            && StringUtils.isNoneBlank(photoMap.get("photo_url"))
                            && StringUtils.isNoneBlank(photoMap.get("status"))
                            && StringUtils.isNoneBlank(photoMap.get("seq"))
                            && StringUtils.isNoneBlank(photoMap.get("bucket"))
                            && StringUtils.isNoneBlank(photoMap.get("verify_status"))
                            && StringUtils.isNoneBlank(photoMap.get("like_num"))
                        // && StringUtils.isNoneBlank(photoMap.get("error_msg"))
                    ) {
                        if (!onlyCheck
                                || onlyCheck && (status == 2 || status == 3 || status == 4)) {
                            // 2020.05.29 修复客户端bug
                            photoMap.put("photo_url", AccountsInfoService.fixUrl(photoMap.getOrDefault("photo_url", "")));
                            photoMapList.add(photoMap);
                        }
                    } else {
                        photoMapList.clear();
                        break;
                    }
                }
            }
            result.put(uuid, photoMapList);
        }

        return result;
    }

    /**
     * 从reids取n张图片， 如果包含 null元素，或者其中某张照片格式不对，则返回空
     *
     * @param accountUuid
     * @param onlyCheck   是否只返回审核的
     * @return
     * <AUTHOR>
     * 2017年12月13日 下午5:46:06
     */
    public List<Map<String, String>> getAccountsPhotoAllByRedis(String accountUuid, boolean onlyCheck) {
        List<Map<String, String>> result = this.mGetAccountsPhotoAllByRedis(new String[]{accountUuid}, new boolean[]{onlyCheck}).get(accountUuid);
        return result == null ? Collections.emptyList() : result;
    }

    /**
     * 重复点击处理
     *
     * @param accountUuid
     */
    private void chcekResubmit(String accountUuid) {
        if (!clickFilterService.isValidClick("set_account_photo", accountUuid, 3)) {
            throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
        }
    }

    /**
     * 保存至缓存
     *
     * @param photoList
     * @param accountUuid
     */
    public void setAccountsPhotoToRedis(List<AccountsPhoto> photoList, String accountUuid) {
        //photoList按seq重新排序 不排序，按seq写入
//        Collections.sort(photoList, (o1, o2) -> {
//            Integer seq1 = o1.getSeq();
//            Integer seq2 = o2.getSeq();
//            return seq1.compareTo(seq2);
//        });

        //修改缓存
        HashMap<Object, Object> redisMap = new HashMap<Object, Object>(AVATAR_PHOTO_LIMIT_20220215);
        x:for (int i = 0; i < AVATAR_PHOTO_LIMIT_20220215; i++) {
            y:for (AccountsPhoto accountsPhoto : photoList) {
                if(Objects.equals(i + 1, accountsPhoto.getSeq())) {
                    Map<String, String> photoMap = new HashMap<String, String>();
                    photoMap.put("id", accountsPhoto.getId().toString());
                    photoMap.put("photo_url", accountsPhoto.getPhoto_url());
                    photoMap.put("status", accountsPhoto.getStatus().toString());
                    photoMap.put("seq", accountsPhoto.getSeq().toString());
                    photoMap.put("bucket", accountsPhoto.getBucket());
                    photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
                    photoMap.put("error_msg", accountsPhoto.getError_msg());
                    photoMap.put("face_score", StringUtil.nullNumberToEmptyString(accountsPhoto.getFace_score()));
                    photoMap.put("like_num", StringUtil.nullNumberToEmptyString(accountsPhoto.getLike_num()));
                    redisMap.put(String.valueOf(i + 1), JsonUtils.objectToString(photoMap));
                    continue x;
                }
            }
            redisMap.put(String.valueOf(i + 1), JsonUtils.objectToString(new HashMap<String, String>()));
        }

        LOGGER.info("uuid={},redisMap={}", accountUuid, JsonUtils.objectToString(redisMap));

        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO.setArg(accountUuid);
        accountStringRedisTemplate.opsForHash().putAll(redisKey, redisMap);
        accountStringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
    }

    @Transactional
    public void setPhotoToRecheck(String accountUuid) {
        List<AccountsPhoto> photoList = accountsPhotoDao.findByUuid(accountUuid);
        if (CollectionUtils.isEmpty(photoList)) {
            return;
        }

        long second = DateUtil.currentTimeSeconds();
        for (AccountsPhoto photo : photoList) {
            String url = photo.getPhoto_url();
            url = AvatarHandleService.getAvatarOfSavePhoto(url);
            photo.setPhoto_url(url);
            if (StringUtils.isBlank(url) || "null".equals(url.trim()) || AvatarHandleService.isDefAvatar(url)) {
                photo.setStatus(2);//默认头像设置为已忽略
            } else {
                photo.setStatus(4);//非默认头像设置为待复审，并且要将创建时间修改为当前时间，才会出现在当天的审核列表页中
                photo.setCreate_time(second);
            }
            photo.setUpdate_time(second);
        }

        accountsPhotoDao.merge(photoList);
        // 图片快审 推 异步队列 checkAccountEmptyFacePhoto
//        addToCheckRedisZSet(accountsManager.getByUuid(accountUuid), photoList);
        accountsService.checkAccountEmptyFacePhoto(accountUuid);
    }

    /**
     * 批量删除用户未审核的相册
     *
     * @param accountUuidList
     */
    @Async
    public void mdelAccountNoCheckPhoto(List<String> accountUuidList, String operatorToken) {
        try {
            accountUuidList.forEach(accountUuid -> {
                try {
                    accountsPhotoDao.getNoCheckPhoto(accountUuid).forEach(accountPhoto -> {
                        logger.info("自动封号删除用户相册, uuid={}", accountUuid);
                        me().updateAccountsPhotoWithPunish(AccountsPhoto.Status.ILLEGAL.getValue(), accountPhoto.getId(), accountPhoto.getPhoto_url(), null);
                        SoaService.sendReviewPhotoSkip(null, accountPhoto.getId(), AvatarHandleService.getAvatarOfSavePhoto(accountPhoto.getPhoto_url()), operatorToken);
                    });
                } catch (Exception e) {
                    logger.warn("自动封号删除用户相册失败, uuid:" + accountUuid, e);
                }
            });
        } catch (Exception e) {
            logger.error("自动封号删除用户未审核相册异常: uuidList:" + accountUuidList, e);
        }
    }

    private AccountsPhotoService me() {
        return SpringContextHolder.getBean(this.getClass());
    }

    public List<AccountsPhoto> findByUuid(String accountUuid) {
        return accountsPhotoDao.findByUuid(accountUuid);
    }

    public AccountsPhoto findAvatarByUuid(String accountUuid) {
        List<AccountsPhoto> accountsPhotoList = accountsPhotoDao.findAvatarByUuid(accountUuid);
        if (CollectionUtils.isNotEmpty(accountsPhotoList)) {
            return accountsPhotoList.get(0);
        }
        return null;
    }

    public Long getCountByVerifyStatus(String accountUuid, Integer verifyStatus) {
        return accountsPhotoDao.getCountByVerifyStatus(accountUuid, verifyStatus);
    }

    /**
     * 更新图片真人状态
     *
     * @param id
     * @param verifyStatus
     * @param errorMsg
     * @param photoUrl
     */
    @Transactional
    public void updateVerifyStatusByIdAndPhotoUrl(Long id, Integer verifyStatus, String errorMsg, String photoUrl) {
        accountsPhotoDao.updateVerifyStatusByIdAndPhotoUrl(id, verifyStatus, errorMsg, photoUrl);
    }

    @Transactional
    public void updateVerifyStatusById(Long id, Integer verifyStatus, String errorMsg) {
        accountsPhotoDao.updateVerifyStatusById(id, verifyStatus, errorMsg);
    }

    @Transactional
    public void updateVerifyStatusAndStatusByUuid(String accountUuid, Integer verifyStatus, String errorMsg) {
        accountsPhotoDao.updateVerifyStatusAndStatusByUuid(accountUuid, verifyStatus, errorMsg);
    }

    /**
     * 带数美id的图片检测
     *
     * @param accountUuid
     * @param verifyPhotoUrl
     * @return
     */
    public ShumeiImgCheckResponseDTO shumeiImgFromRiskDetect(String accountUuid, String verifyPhotoUrl, Integer appcode, Integer cloned, String smid, Long photoId, String type) {
        verifyPhotoUrl = ORIGN_PHOTO_DOMAIN + StringUtils.removeStart(avatarHandleService.getAvatarByVersion(verifyPhotoUrl, "1"), "/");

        try {
            ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = RiskSafeService.imgReviewFromRiskDetect(appcode, cloned, accountUuid, type, Arrays.asList(verifyPhotoUrl), smid, photoId, null);
            if (null == shumeiImgCheckResponseDTO || StringUtils.isBlank(shumeiImgCheckResponseDTO.getRiskDescription())) {
                // 2024.12.27 不知道这个日志具体是做什么用的riskDescription 是有可能为空
                logger.warn("数美图片检测风险描述为空, accountUuid={}, verifyPhotoUrl={}, response={}", accountUuid, verifyPhotoUrl, null == shumeiImgCheckResponseDTO ? "null" : JSONObject.toJSONString(shumeiImgCheckResponseDTO));
                if (null == shumeiImgCheckResponseDTO) {
                    shumeiImgCheckResponseDTO = new ShumeiImgCheckResponseDTO();
                }
                shumeiImgCheckResponseDTO.setRiskDescription("");
            }
            return shumeiImgCheckResponseDTO;
        } catch (Exception e) {
            LOGGER.warn("数美图片检测接口,调用失败,用户uuid={},检测图片url={}", accountUuid, verifyPhotoUrl, e);
        }
        return null;
    }


    /**
     * 移除
     *
     * @param redisKey
     * @param jsonStr
     */
    public void removeValueInCheckZSet(String redisKey, String jsonStr) {
        accountStringRedisTemplate.opsForZSet().remove(redisKey, jsonStr);
    }

    /**
     * 获取用户的头像
     *
     * @param accountUuid
     * @param verifyStatus
     * @param ifPassCheck
     * @return
     */
    public AccountsPhoto getAccountAvatar(String accountUuid, Integer verifyStatus, boolean ifPassCheck) {
        return accountsPhotoDao.getAccountAvatar(accountUuid, verifyStatus, ifPassCheck);
    }

    /**
     * 图片比对域名替换
     *
     * @param photoUrl
     * @return
     */
    public static String replacePhotoDomainCompare(String photoUrl) {
        if (StringUtils.isBlank(photoUrl)) {
            return "";
        }
        // 替换源域名
        if (REPLACE_PHOTO_DOMAIN_TYPE_ORIGN.equals(REPLACE_PHOTO_DOMAIN_COMPARE)) {
            photoUrl = photoUrl.replace(regReplaceAvatarPart, ORIGN_PHOTO_DOMAIN);
        }
        return photoUrl;
    }

    /**
     * 获取审核域名
     *
     * @return
     */
    public static String getPhotoCheckDomain() {
        return AccountsPhotoService.REPLACE_PHOTO_DOMAIN_TYPE_ORIGN.equals(AccountsPhotoService.REPLACE_PHOTO_DOMAIN_CHECK) ? AccountsPhotoService.ORIGN_PHOTO_DOMAIN : regReplaceAvatarPart;
    }

    /**
     * 审核域名替换
     *
     * @param photoUrl
     * @return
     */
    public static String replacePhotoDomainCheck(String photoUrl) {
        if (StringUtils.isBlank(photoUrl)) {
            return "";
        }
        // 替换源域名
        if (REPLACE_PHOTO_DOMAIN_TYPE_ORIGN.equals(REPLACE_PHOTO_DOMAIN_CHECK)) {
            photoUrl = photoUrl.replace(regReplaceAvatarPart, ORIGN_PHOTO_DOMAIN);
        }
        return photoUrl;
    }

    /**
     * 设置真人认证审核状态
     *
     * @param accountUuid
     */
    public void setRealPersonCertificationStatusCache(String accountUuid, RealPersonCertificationStatusEnum realPersonCertificationStatusEnum) {
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();
        hashValue.put(UuidInfoField.REAL_PERSON_CERTIFICATION_STATUS, realPersonCertificationStatusEnum.getStatus().toString());
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);
    }

    /**
     * 图片检测
     *
     * @param accountUuid
     * @param photoUrl
     * @param bucket
     * @param scene
     * @return
     */
    public Map<String, String> detectPhoto(String accountUuid, String photoUrl, String bucket, String smid, Integer scene) {
        Map<String, String> result = new HashMap<>();
        Map<String, Object> infoMap = accountsInfoService.singleGetInfo(accountUuid, new String[] { UuidInfoField.REAL_PERSON_CERTIFICATION, UuidInfoField.SEX_TYPE });
        boolean isReal = "1".equals(MapUtils.getString(infoMap, UuidInfoField.REAL_PERSON_CERTIFICATION, "2"));
        boolean isFemale = "2".equals(MapUtils.getString(infoMap, UuidInfoField.SEX_TYPE, "1"));
        // 仅针对女用户
        if (scene == 1 && isReal && isFemale && !AvatarHandleService.isDefAvatar(photoUrl)) {
            ImgVectorDetectRespDto resp = imgVectorDetect(accountUuid, photoUrl);
            if (!resp.pass()) {
                throw new ServiceException("photo_risk_compare_similar", "该照片和头像相似度过高，请更换一张新的");
            }
            String riskBizId = resp.getBizId();
            // 需要redis写入一个值，以便setAccountCover时使用
            String key = PHOTO_VECTOR_BIZ_ID_MAPPING.setArg(accountUuid);
            accountBizStringRedisTemplate.expire(key, 10, TimeUnit.MINUTES);
            Long now = DateUtil.currentTimeSeconds();
            ImageCompareRecord record = new ImageCompareRecord();
            record.setBizId(riskBizId);
            record.setConclusion(resp.getHitTypeCode());
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setBizType(ImageCompareRecord.ALBUM);
            accountBizStringRedisTemplate.opsForHash().put(key, photoUrl, JsonUtils.objectToString(record));
        }

        String url = avatarHandleService.getAvatarByVersion(photoUrl, null);
        url = replacePhotoDomainCompare(url);

        Integer verifyStatus = 0;
        Double similarityScore = 0d;

        CompareFaceDto compareFaceDto = detectPhotoAndGetCompareFaceDto(accountUuid, url, smid);
        if (compareFaceDto != null) {
            similarityScore = Double.parseDouble(compareFaceDto.getScore() + "");
            verifyStatus = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).isVerifySuccess(similarityScore);
        }

        result.put("similarity_score", String.valueOf(similarityScore));
        result.put("verify_status", String.valueOf(verifyStatus));
        return result;
    }

    /**
     * 检测头像
     *
     * @param accountUuid
     * @param avatarUrl
     * @param needDetectFace
     * @return
     */
    public CompareFaceDto detectAvatarAndGetCompareFaceDto(String accountUuid, String avatarUrl, String smid, Boolean needDetectFace, Long photoId) {
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"profile_verify_status", "face_certification"}, "1", false, false).get(accountUuid);
        String auditSuccess = String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        Boolean isFaceDetect = auditSuccess.equals(infoMap.get("profile_verify_status")) || auditSuccess.equals(infoMap.get("face_certification"));

        // 头像违规检测：数美
        // 图片质量检测：腾讯
        // AI图片检测：腾讯图片比对
        ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = null;
        TencentImgQualityDTO tencentImgQualityDTO = null;
        CompareFaceDto compareFaceDto = null;
        Boolean isFaceImg = null;

        Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();

        // 头像
        if (!isFaceDetect) {
            // 查询活体图片
            Future<ShumeiImgCheckResponseDTO> shumeiFuture = ThirdPartFactory.shumeiImgCheckPool.submit(new ShumeImgCheckWithSmidCall(accountUuid, avatarUrl, RiskDetectEnum.IMAGE_AVATAR.name(), smid, appcode, cloned, photoId, SoaBaseParams.fromThread().getDistinctRequestId()));
            Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, avatarUrl));
            Future<Boolean> faceDetectFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgFaceDetectCall(accountUuid, avatarUrl, needDetectFace));
            try {
                shumeiImgCheckResponseDTO = shumeiFuture.get(5000, TimeUnit.MILLISECONDS);
                tencentImgQualityDTO = imgQualityFuture.get(5000, TimeUnit.MILLISECONDS);
                isFaceImg = faceDetectFuture.get(5000, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                LOGGER.warn("线程池处理.InterruptedException", e);
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (ExecutionException e) {
                if (e.getCause() instanceof ServiceException) {
                    throw (ServiceException) e.getCause();
                } else {
                    LOGGER.warn("ExecutionException", e);
                }
            } catch (TimeoutException e) {
                try {
                    shumeiFuture.cancel(true);
                    imgQualityFuture.cancel(true);
                    faceDetectFuture.cancel(true);
                    LOGGER.warn("线程池处理.超时停止");
                } catch (Exception ee) {
                    // 结束子进程
                    LOGGER.warn("线程池处理.超时停止.结束子进程失败", ee);
                }
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (Exception e) {
                logger.warn("图片检测失败", e);
            }
            LOGGER.info("图片违规检测.结果={}", shumeiImgCheckResponseDTO == null ? "" : JSON.toJSONString(shumeiImgCheckResponseDTO));
            LOGGER.info("图片质量检测.结果={}", tencentImgQualityDTO == null ? "" : JSON.toJSONString(tencentImgQualityDTO));
            LOGGER.info("图片人脸检测.结果={}", isFaceImg);
            // 图片质量检测调用失败
            if (tencentImgQualityDTO == null) {
                throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
            }
            if (!tencentImgQualityDTO.isImgQualityPass()) {
                throw new ServiceException(CodeStatus.UPLOAD_AVATAR_FAIL_QUALIITY);
            }
            if (isFaceImg == null || !isFaceImg) {
                throw new ServiceException(CodeStatus.UPDATE_AVATAR_FAIL_DETECT_FACE);
            }
            if (shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), RiskDetectRiskLevelEnum.REJECT.name())) {
                throw new ServiceException(CodeStatus.UPLOAD_AVATAR_FAIL_ILLEGAL);
            }
        } else {
            // 查询活体图片
            AliyunLiveFaceDetect liveFaceDetect = SpringContextHolder.getBean(AliyunLiveFaceDetectDao.class).getInfoByAccountUuid(accountUuid);
            String faceBasePhoto = "";
            if (liveFaceDetect != null) {
                faceBasePhoto = liveFaceDetect.getBase_photo_url();
            } else {
                throw new ServiceException(CodeStatus.UPLOAD_AVATAR_FAIL_LIVE_FACE);
            }

            Future<ShumeiImgCheckResponseDTO> shumeiFuture = ThirdPartFactory.shumeiImgCheckPool.submit(new ShumeImgCheckWithSmidCall(accountUuid, avatarUrl, RiskDetectEnum.IMAGE_AVATAR.name(), smid, appcode, cloned, photoId, SoaBaseParams.fromThread().getDistinctRequestId()));
            Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, avatarUrl));
            Future<CompareFaceDto> imgCompareFuture = ThirdPartFactory.tencentImgComparePool.submit(new TencentImgCompareCall(accountUuid, avatarUrl, faceBasePhoto, RiskCertificationTypeEnum.PHOTO_ALBUM));
            Future<Boolean> faceDetectFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgFaceDetectCall(accountUuid, avatarUrl, needDetectFace));
            try {
                shumeiImgCheckResponseDTO = shumeiFuture.get(5000, TimeUnit.MILLISECONDS);
                tencentImgQualityDTO = imgQualityFuture.get(5000, TimeUnit.MILLISECONDS);
                compareFaceDto = imgCompareFuture.get(5000, TimeUnit.MILLISECONDS);
                isFaceImg = faceDetectFuture.get(5000, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                LOGGER.warn("线程池处理.InterruptedException", e);
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (ExecutionException e) {
                if (e.getCause() instanceof ServiceException) {
                    throw (ServiceException) e.getCause();
                } else {
                    LOGGER.warn("ExecutionException", e);
                }
            } catch (TimeoutException e) {
                try {
                    imgQualityFuture.cancel(true);
                    imgCompareFuture.cancel(true);
                    faceDetectFuture.cancel(true);
                    LOGGER.warn("线程池处理.超时停止");
                } catch (Exception ee) {
                    // 结束子进程
                    LOGGER.warn("线程池处理.超时停止.结束子进程失败", ee);
                }
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (Exception e) {
                logger.warn("setAccountAvatarV3图片检测失败", e);
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            }
            LOGGER.info("图片违规检测.结果={}", shumeiImgCheckResponseDTO == null ? "" : JSON.toJSONString(shumeiImgCheckResponseDTO));
            LOGGER.info("图片质量检测.结果={}", tencentImgQualityDTO == null ? "" : JSON.toJSONString(tencentImgQualityDTO));
            LOGGER.info("图片比对检测.结果={}", compareFaceDto == null ? "" : JSON.toJSONString(compareFaceDto));
            LOGGER.info("图片人脸检测.结果={}", isFaceImg);
            if (tencentImgQualityDTO == null) {
                throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
            }
            if (!tencentImgQualityDTO.isImgQualityPass()) {
                throw new ServiceException(CodeStatus.UPLOAD_AVATAR_FAIL_QUALIITY);
            }
            if (compareFaceDto == null || !compareFaceDto.isRequestSuccess()) {
                throw new ServiceException(CodeStatus.PHOTO_DETECT_COMPARE_BUSY);
            }
            if (isFaceImg == null || !isFaceImg) {
                throw new ServiceException(CodeStatus.UPDATE_AVATAR_FAIL_DETECT_FACE);
            }
            if (!compareFaceDto.isSimilarity()) {
                sendAvatarCompareFailSystemMessage(accountUuid);
                userCertificationLogService.logPhotoAlbumFail(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(avatarUrl), ToolsService.addAccountPrivateUrlPreDomain(faceBasePhoto), compareFaceDto.getScore());
                throw new ServiceException(CodeStatus.UPLOAD_AVATAR_FAIL_INCONSISTENT);
            }
            if (shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), RiskDetectRiskLevelEnum.REJECT.name())) {
                buryService.toBbsAccountAvatarIll(accountUuid);
                throw new ServiceException(CodeStatus.UPLOAD_AVATAR_FAIL_ILLEGAL);
            }

            userCertificationLogService.logPhotoAlbumSuccess(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(avatarUrl), ToolsService.addAccountPrivateUrlPreDomain(faceBasePhoto), compareFaceDto.getScore());
        }
        return compareFaceDto;
    }

    /**
     * 相册图片检测（还是请求j62校验图片违规）
     *
     * @param accountUuid
     * @param photoUrl
     * @return
     */
    public CompareFaceDto detectPhotoAndGetCompareFaceDto(String accountUuid, String photoUrl, String smid) {
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"profile_verify_status", "face_certification"}, "1", false, false).get(accountUuid);
        String auditSuccess = String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        Boolean isRealPersonCertification = auditSuccess.equals(infoMap.get("profile_verify_status"));
        Boolean isFaceDetect = auditSuccess.equals(infoMap.get("face_certification"));
        // 头像违规检测：数美
        // 图片质量检测：腾讯
        // AI图片检测：腾讯图片比对
        ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = null;
        TencentImgQualityDTO tencentImgQualityDTO = null;
        CompareFaceDto compareFaceDto = null;

        Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();

        if (!isFaceDetect) {
            Future<ShumeiImgCheckResponseDTO> shumeiFuture = ThirdPartFactory.shumeiImgCheckPool.submit(new ShumeImgCheckWithSmidCall(accountUuid, photoUrl, RiskDetectEnum.PHOTO.name(), smid, appcode, cloned, null, SoaBaseParams.fromThread().getDistinctRequestId()));
            Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, photoUrl));
            try {
                shumeiImgCheckResponseDTO = shumeiFuture.get(5000, TimeUnit.MILLISECONDS);
                tencentImgQualityDTO = imgQualityFuture.get(5000, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                LOGGER.warn("线程池处理.InterruptedException", e);
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (ExecutionException e) {
                if (e.getCause() instanceof ServiceException) {
                    ServiceException serviceException = (ServiceException) e.getCause();
                    throw serviceException;
                } else {
                    LOGGER.warn("ExecutionException", e);
                }
            } catch (TimeoutException e) {
                try {
                    shumeiFuture.cancel(true);
                    imgQualityFuture.cancel(true);
                    LOGGER.warn("线程池处理.超时停止");
                } catch (Exception ee) {
                    // 结束子进程
                    LOGGER.warn("线程池处理.超时停止.结束子进程失败", ee);
                }
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (Exception e) {
                logger.warn("图片检测失败.uuid={}", accountUuid, e);
            }
            LOGGER.info("图片质量检测.结果={}", tencentImgQualityDTO == null ? "" : JSON.toJSONString(tencentImgQualityDTO));
            LOGGER.info("图片违规检测.结果={}", shumeiImgCheckResponseDTO == null ? "" : JSON.toJSONString(shumeiImgCheckResponseDTO));
            if (tencentImgQualityDTO == null) {
                throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
            }
            if (!tencentImgQualityDTO.isImgQualityPass()) {
                throw new ServiceException(CodeStatus.UPLOAD_COVER_FAIL_QUALIITY);
            }
            if (shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), RiskDetectRiskLevelEnum.REJECT.name())) {
                throw new ServiceException(CodeStatus.UPLOAD_COVER_FAIL_ILLEGAL);
            }
        } else {
            // 查询活体图片
            AliyunLiveFaceDetect liveFaceDetect = SpringContextHolder.getBean(AliyunLiveFaceDetectDao.class).getInfoByAccountUuid(accountUuid);
            String faceBasePhoto = "";
            if (liveFaceDetect != null) {
                faceBasePhoto = liveFaceDetect.getBase_photo_url();
            } else {
                throw new ServiceException(CodeStatus.UPLOAD_COVER_FAIL_LIVE_FACE);
            }

            Future<ShumeiImgCheckResponseDTO> shumeiFuture = ThirdPartFactory.shumeiImgCheckPool.submit(new ShumeImgCheckWithSmidCall(accountUuid, photoUrl, RiskDetectEnum.PHOTO.name(), smid, appcode, cloned, null, SoaBaseParams.fromThread().getDistinctRequestId()));
            Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, photoUrl));
            Future<CompareFaceDto> imgCompareFuture = ThirdPartFactory.tencentImgComparePool.submit(new TencentImgCompareCall(accountUuid, photoUrl, faceBasePhoto, RiskCertificationTypeEnum.PHOTO_ALBUM));
            try {
                shumeiImgCheckResponseDTO = shumeiFuture.get(5000, TimeUnit.MILLISECONDS);
                tencentImgQualityDTO = imgQualityFuture.get(5000, TimeUnit.MILLISECONDS);
                compareFaceDto = imgCompareFuture.get(5000, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                LOGGER.warn("线程池处理.InterruptedException", e);
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (ExecutionException e) {
                if (e.getCause() instanceof ServiceException) {
                    ServiceException serviceException = (ServiceException) e.getCause();
                    throw serviceException;
                } else {
                    LOGGER.warn("ExecutionException", e);
                }
            } catch (TimeoutException e) {
                try {
                    shumeiFuture.cancel(true);
                    imgQualityFuture.cancel(true);
                    imgCompareFuture.cancel(true);
                    LOGGER.warn("线程池处理.超时停止");
                } catch (Exception ee) {
                    // 结束子进程
                    LOGGER.warn("线程池处理.超时停止.结束子进程失败", ee);
                }
                throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
            } catch (Exception e) {
                logger.warn("图片检测失败.uuid={}", accountUuid, e);
            }
            LOGGER.info("图片违规检测.结果={}", shumeiImgCheckResponseDTO == null ? "" : JSON.toJSONString(shumeiImgCheckResponseDTO));
            LOGGER.info("图片质量检测.结果={}", tencentImgQualityDTO == null ? "" : JSON.toJSONString(tencentImgQualityDTO));
            LOGGER.info("图片比对检测.结果={}", compareFaceDto == null ? "" : JSON.toJSONString(compareFaceDto));
            if (tencentImgQualityDTO == null) {
                throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
            }
            if (!tencentImgQualityDTO.isImgQualityPass()) {
                throw new ServiceException(CodeStatus.UPLOAD_COVER_FAIL_QUALIITY);
            }
            if (compareFaceDto == null || !compareFaceDto.isRequestSuccess()) {
                throw new ServiceException(CodeStatus.PHOTO_DETECT_COMPARE_BUSY);
            }
            if (shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), RiskDetectRiskLevelEnum.REJECT.name())) {
                // 真人才推
                if (isRealPersonCertification) {
                    sendCoverIllSystemMessage(accountUuid);
                }
                throw new ServiceException(CodeStatus.UPLOAD_COVER_FAIL_ILLEGAL);
            }

            if (!compareFaceDto.isSimilarity()) {
                userCertificationLogService.logPhotoAlbumFail(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl), ToolsService.addAccountPrivateUrlPreDomain(faceBasePhoto), compareFaceDto.getScore());
            } else {
                userCertificationLogService.logPhotoAlbumSuccess(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl), ToolsService.addAccountPrivateUrlPreDomain(faceBasePhoto), compareFaceDto.getScore());
            }
        }
        return compareFaceDto;
    }

    /**
     * 发送头像违规小秘书
     *
     * @param accountUuid
     */
    private void sendAvatarCompareFailSystemMessage(String accountUuid) {
        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info = "您提交的头像与您本人相差过大，请重新上传高清无遮挡的本人照片" +
                "\n" +
                "\n" +
                "%s";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "重新更换头像");
        contentReplace.put("r", "m=mine&a=info");
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "您提交的头像与您本人相差过大，请重新上传高清无遮挡的本人照片");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);

        MessageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
    }

    /**
     * 发送头像违规小秘书
     *
     * @param accountUuid
     */
    public void sendAvatarIllSystemMessage(String accountUuid) {

        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info = "您的头像不符合头像上传要求已被清除，原因可能有：非本人照片、戴墨镜、遮脸、头像不清晰及内容违规等，请重新上传！" +
                "\n" +
                "\n" +
                "%s";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "重新上传头像");
        contentReplace.put("r", "m=mine&a=info");
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "您的头像不符合头像上传要求已被清除");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);
        MessageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
    }


    /**
     * 发送头像违规小秘书
     *
     * @param accountUuid
     */
    public void sendCoverIllSystemMessage(String accountUuid) {
        String info = "您的相册涉嫌违规己被清除，原因可能有：涉黄低俗、留其他联系方式、政治敏感、照片过于模糊、打广告、未成年、二维码等，请重新上传，若再次违规将有可能被禁言！";

        Map<String, String> content = Maps.newHashMap();
        content.put("content", info);
        content.put("relaction", "");
        content.put("is_local_push", "1");

        MessageService.systemNotice(accountUuid, content, "系统消息", "text", 1);
    }

    /**
     * 上传头像
     */
    public void setAccountAvatarV3(String accountUuid, String url, String bucket, String smid, Boolean needDetectFace, Boolean isBeauty) {
        AccountsPhoto accountsPhotoOld = getOldAccountPhoto(accountUuid, url, bucket);

        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"avatar"}, "1", false, false).get(accountUuid);
        String oldAvatar = infoMap.getOrDefault("avatar", "").toString();
        String avatarUrl = avatarHandleService.getAvatarByVersion(url, null);
        avatarUrl = replacePhotoDomainCompare(avatarUrl);
        CompareFaceDto compareFaceDto;
        compareFaceDto = detectAvatarAndGetCompareFaceDto(accountUuid, avatarUrl, smid, needDetectFace, accountsPhotoOld.getId());
        Integer verifyStatus = 0;
        double similarityScore = 0d;
        if (compareFaceDto != null) {
            similarityScore = Double.parseDouble(compareFaceDto.getScore() + "");
            verifyStatus = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).isVerifySuccess(similarityScore);
        }
        if (verifyStatus == 1) {
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "avatar_same_with_face", "1");
        } else {
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "avatar_same_with_face", "0");
        }
        Integer isFace = needDetectFace ? 1 : null;
        SpringContextHolder.getBean(AccountsPhotoService.class).setAccountAvatar(accountUuid, url, bucket, 0, verifyStatus, "", -1, similarityScore, isFace, needDetectFace);

        // 推送头像奖励
        if (StringUtils.isNotBlank(oldAvatar) && (AvatarHandleService.isDefAvatar(oldAvatar) || cloneDefaultAvatarService.isDefaultAvatar(oldAvatar))) {
            BuryService.pushBbsAvatarTask(accountUuid, ChangeAvatarTypeEnum.DEFAULT_TO_HAVE.getValue());
        } else {
            BuryService.pushBbsAvatarTask(accountUuid, ChangeAvatarTypeEnum.HAVE_TO_HAVE.getValue());
        }

        // 一键美颜之后不需要重复通知
        String key = RedisKeyConstant.AVATAR_HAS_BEAUTY.setArg(accountUuid);
        if (isBeauty) {
            bizRedisTemplate.opsForValue().set(key, "1", AvatarGuideConfig.CONFIG.getBiz().getCircleDay(), TimeUnit.DAYS);
        } else {
            // 有换过其他头像（非美颜）就可以继续弹窗提示
            bizRedisTemplate.delete(key);
        }

    }

    private AccountsPhoto getOldAccountPhoto(String accountUuid, String url, String bucket) {
        AccountsPhoto accountsPhotoOld = accountsPhotoDao.getAccountAvatar(accountUuid, null, false);
        if (Objects.isNull(accountsPhotoOld)) {
            accountsPhotoOld = new AccountsPhoto();
            accountsPhotoOld.setAccount_uuid(accountUuid);
            accountsPhotoOld.setPhoto_url(url);
            accountsPhotoOld.setSeq(1);
            accountsPhotoOld.setStatus(AVATAR_DEFAULT_STATUS);
            accountsPhotoOld.setBucket(bucket);
            accountsPhotoOld.setCreate_time(DateUtil.currentTimeSeconds());
            accountsPhotoOld.setUpdate_time(DateUtil.currentTimeSeconds());
            accountsPhotoOld.setVerify_status(0);
            accountsPhotoOld.setError_msg("");
            accountsPhotoOld.setFace_score(-1);
            accountsPhotoOld.setLike_num(0L);
            final AccountsPhoto photo = accountsPhotoOld;
            accountsPhotoOld = TransactionWrapper.me().wrap(() -> accountsPhotoDao.merge(photo));;
        }
        return accountsPhotoOld;
    }

    /**
     * 移除无效相册
     */
    @Transactional
    public void removeUnClearCover(String accountUuid, List<String> deletePhotoUrls) {
        if (CollectionUtils.isEmpty(deletePhotoUrls)) {
            return;
        }
        List<AccountsPhoto> accountsAllPhoto = accountsPhotoDao.findByUuid(accountUuid);
        accountsAllPhoto.remove(0);
        List<Long> removeIds = new ArrayList<>();
        List<AccountsPhoto> newCoverList = new ArrayList<>();
        for (AccountsPhoto cover : accountsAllPhoto) {
            if (deletePhotoUrls.contains(cover.getPhoto_url())) {
                removeIds.add(cover.getId());
            } else {
                newCoverList.add(cover);
            }
        }
        if (removeIds.size() > 0) {
            accountsPhotoDao.removeByIds(removeIds);

            //重新设置相册顺序为1、2、3、4.....，seq为1的自动变成头像
            int newPhotoSeq = 2;
            for (AccountsPhoto photo : newCoverList) {
                photo.setSeq(newPhotoSeq);
                newPhotoSeq++;
            }
            accountsPhotoDao.merge(newCoverList);
            accountsPhotoDao.flush();
            HashSet<String> uuidSet = new HashSet<>();
            uuidSet.add(accountUuid);
            // 根据uuid查询用户已审核通过的相册数，落地到account_info表中
            accountsInfoService.updateAccountPhotoNumber(uuidSet, "db");

            // 更新redis中用户信息缓存
            Map<String, String> infoCache = Maps.newHashMap();
            infoCache.put("photo_num", String.valueOf(newCoverList.size()));//第一张是头像，不能算在照片数量中去
            accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);
            // 清空缓存
            this.setAccountsPhotoToRedis(accountsPhotoDao.findByUuid(accountUuid), accountUuid);
            accountsService.checkAccountEmptyFacePhoto(accountUuid);
        }
    }

    @Transactional
    public void setAvatarAsDef(String accountUuid, String sexType) {
        String illUrl = AvatarHandleService.getDefAvatar(StringUtils.isBlank(sexType) ? null : Integer.valueOf(sexType));
        AccountsPhoto accountAvatar = accountsPhotoDao.getAccountAvatar(accountUuid, null, false);
        if (accountAvatar != null) {
            accountAvatar.setPhoto_url(illUrl);
            accountAvatar.setStatus(AccountsPhoto.Status.ILLEGAL.getValue());
            accountAvatar.setLike_num(0L);
            accountAvatar.setVerify_status(0);
            accountAvatar.setRisk_photo_url("");
            accountAvatar.setRisk_description("");
            accountAvatar.setCreate_time(DateUtil.currentTimeSeconds());
            accountAvatar.setUpdate_time(DateUtil.currentTimeSeconds());
            accountsPhotoDao.merge(accountAvatar);
            accountsPhotoDao.flush();
        }
        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts != null) {
            accounts.setAvatar(illUrl);
            accounts.setAvatar_time(DateUtil.currentTimeSeconds());
            accounts.setAvatar_status(1);
            accountsManager.merge(accounts);
        }
        Map<String, String> avatarMap = new HashMap<>();
        avatarMap.put("avatar", illUrl);
        avatarMap.put("avatar_status", "1");
        avatarMap.put("avatar_origin", illUrl);
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), avatarMap);
        // 清空缓存
        this.setAccountsPhotoToRedis(accountsPhotoDao.findByUuid(accountAvatar.getAccount_uuid()), accountAvatar.getAccount_uuid());
        logger.info("头像置默认.用户uuid={}", accountUuid);
        accountsService.checkAccountEmptyFacePhoto(accountUuid);
    }

    public Map<String, String> testDetectPhoto(String accountUuid, String avatarUrl) {
        Map<String, String> result = new HashMap<>();
        this.detectAvatarAndGetCompareFaceDto(accountUuid, avatarUrl, "", false, 0L);
        result.put("code", "1");
        return result;
    }

    @Deprecated
    public Map<String, String> isAvatarThumbsUp(String fromAccountUuid, String toAccountUuid) {
        AccountsPhoto accountAvatar = accountsPhotoDao.getAccountAvatar(toAccountUuid, null, false);
        Map<String, String> result = new HashMap<>();
        if (accountAvatar == null) {
            result.put("has_thumbs_up", "1");
            return result;
        }
        result.put("has_thumbs_up", accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_PHOTO_THUMBS_UP.setArg(Md5Util.encryptSHA1(accountAvatar.getId() + accountAvatar.getPhoto_url() + fromAccountUuid))) ? "1" : "0");
        return result;
    }

    public String getAccountUuidByPhotoId(Long photoId) {
        if (photoId == null) {
            return null;
        }
        AccountsPhoto photo = accountsPhotoDao.findById(photoId);
        if (photo == null) {
            return null;
        }
        return photo.getAccount_uuid();
    }

    /**
     * 过滤相册张数 12张 or 8张
     */
    public void filterPhotoNum(Map<String, Object> result) {
        Object imgListObject = MapUtils.getObject(result, "img_list");
        if (imgListObject != null) {
            List<Map<String, String>> list = (List<Map<String, String>>) imgListObject;
            result.put("img_list", filterPhotoNum(list));
        }
    }

    /**
     * 过滤相册张数 12张 or 8张
     */
    public List<Map<String, String>> filterPhotoNum(List<Map<String, String>> list) {
        if (!versionControlService.isNewPhotoNumConfig()) {
            // 旧版，判断张数，只取前8张图
            if (list != null && list.size() > PHOTO_LIMIT_OLD) {
                return Lists.newArrayList(list.subList(0, PHOTO_LIMIT_OLD));
            }
        }
        return list;
    }

    @Async
    public void pushMsgToActivityKafka(String uuid, String topicName, String eventCode) {
        Integer appcode=SoaBaseParams.fromThread().getAppcode();
        Integer cloned=SoaBaseParams.fromThread().getCloned();
        String token=SoaBaseParams.fromThread().getToken();
        Integer platformId=SoaBaseParams.fromThread().getPlatform_id();
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("uuid", uuid);

        Map<String, Object> eventInfo = new HashMap<>();
        eventInfo.put("appcode", appcode);
        eventInfo.put("cloned", cloned);
        eventInfo.put("token", token);
        eventInfo.put("platform_id", platformId);

        Map<String, Object> eventBody = new HashMap<>();
        eventBody.put("tracer_id", SoaBaseParams.fromThread().getDistinctRequestId());
        eventBody.put("distinct_id", SoaBaseParams.fromThread().getDistinctRequestId());
        eventBody.put("event_code", eventCode);
        eventBody.put("event_ts", System.currentTimeMillis());
        eventBody.put("business_type", 99);
        eventBody.put("user_info", userInfo);
        eventBody.put("event_info", eventInfo);
        kafkaSinkUtil.pushToOnlineBusinessKafka(true, topicName, eventBody);
    }

    @Transactional
    public void auditProcess(String senderUuid, String operator, int checkType, RiskSafeHitTypeEnum riskSafeHitTypeEnum, Map<Long, String> photoMap) {
        if (Objects.nonNull(riskSafeHitTypeEnum) && photoMap.size() > 0) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    Map<String, List<AccountsPhoto>> result = photoIgnoreV5(photoMap, operator, checkType);
                    Set<String> uuidSet = new HashSet<>(result.keySet());

                    // 根据uuid查询用户已审核通过的相册数，落地到account_info表中
                    accountsInfoService.updateAccountPhotoNumber(uuidSet, "db");

                    // 相册修改事件
                    for (String uuid : uuidSet) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("account_uuid", uuid);
                        map.put("create_time", DateUtil.currentTimeSeconds());
//                        kafkaSinkUtil.push(AccountPhotoNumUpdateTask.TOPIC, map);
                        SpringContextHolder.getBean(AccountPhotoNumUpdateTask.class).sendAuditEvent(map);
                    }
                    // 吸引力刷新
                    SpringContextHolder.getBean(AllureService.class).refreshAllure(senderUuid);
                    // 上报bbs（生成头像审批通过记录）
                    SoaService.batchIgnoreAccountIllAvatarLog(senderUuid, result, operator);
                    break;
                case BLOCK:
                    Set<Long> photoIdSet = photoMap.keySet();
                    if (photoIdSet.size() != 1) {
                        throw new ServiceException("photo_more", "只能选择一张图片进行处罚。");
                    }
                    Long photoId = Lists.newArrayList(photoIdSet).get(0);
                    String accountUuid = getAccountUuidByPhotoId(photoId);
                    Map<String, String> accountsPhotoMap = Maps.newHashMap();
                    if (StringUtils.isNotBlank(accountUuid)) {
                        String deleteKey = RedisKeyConstant.ACCOUNT_PHOTO_DOING_DELETE_UUID.setArg(accountUuid);
                        Boolean lockSuccess = redisLockUtil.lockLimitTimes(deleteKey, 50, 2000, 60);
                        if (!lockSuccess) {
                            throw new ServiceException(CodeStatus.PHTO_CHECK_BUSY);
                        }
                        AccountsPhoto preAvatar = getPreAvatarInfo(accountUuid);
                        accountsPhotoMap = updateAccountsPhotoWithPunish(AccountsPhoto.Status.ILLEGAL.getValue(), photoId, photoMap.get(photoId), operator, checkType, senderUuid);
                        accountsInfoService.updateAccountPhotoNumber(Collections.singleton(accountUuid), "db");

                        String riskBizId = SpringContextHolder.getBean(ImageCompareRecordDao.class).getBizId(photoId, ImageCompareRecord.ALBUM);
                        if (checkType == 1 && StringUtils.isNotBlank(riskBizId)) {
                            ImgVectorRemoveDto dto = new ImgVectorRemoveDto();
                            dto.setSenderUuid(accountUuid);
                            dto.setBizId(riskBizId);
                            dto.setSafeId(RiskSafeIdConstant.SIMILAR_PHOTO_DETECT);
                            // 向量清理
                            RiskSafeService.imageVectorDelete(dto);
                            if (preAvatar != null && !AvatarHandleService.isDefAvatar(preAvatar.getPhoto_url())) {
                                // 向量回滚
                                imgVectorAdd(preAvatar);
                            }
                        }

                        redisLockUtil.unLock(RedisKeyConstant.ACCOUNT_PHOTO_DOING_DELETE_UUID.setArg(accountUuid));

                        // 相册修改事件
                        Map<String, Object> map = new HashMap<>();
                        map.put("account_uuid", accountUuid);
                        map.put("create_time", DateUtil.currentTimeSeconds());
//                        kafkaSinkUtil.push(AccountPhotoNumUpdateTask.TOPIC, map);
                        SpringContextHolder.getBean(AccountPhotoNumUpdateTask.class).sendAuditEvent(map);
                    }

                    // 删除非空，且不是头像，才清空我的生活
                    if(!accountsPhotoMap.isEmpty() && !Objects.equals(MapUtils.getInteger(accountsPhotoMap, "seq"), 1)) {
                        // 相册删除，同步处理我的生活
                        accountsLifeService.resetEmpty(accountUuid);
                    }
                    // 吸引力刷新
                    SpringContextHolder.getBean(AllureService.class).refreshAllure(senderUuid);
                    // 上报bbs（生成头像审批拒绝记录）
                    SoaService.batchDeleteAccountIllAvatarLog(senderUuid, Lists.newArrayList(accountsPhotoMap), operator);
                    break;
                default:
                    break;
            }
        }
    }

    public List<AccountsPhoto> queryNotAudit(Long expireTime, Long startTime, Integer seq, Integer status, Integer limit) {
        return accountsPhotoDao.queryNotAudit(expireTime, startTime, seq, status, limit);
    }

    /**
     * 同步数据 创建
     *
     * @param accountsLife
     */
    @Transactional
    public void accountsLifeSyncToAccountPhotoSave(AccountsLife accountsLife) {
        logger.info("[AccountsLifeSyncToAccountPhotoSave]同步我的生活数据至相册开始，accountsLife={}", JsonUtils.objectToString(accountsLife));
        String accountUuid = accountsLife.getAccountUuid();
        // 相册排序 = 我的生活排序 + 1
        Integer photoSeq = accountsLife.getSeq() + 1;
        String photoUrlOrigin = accountsLife.getPhotoUrlOrigin();
        Integer status = accountsLife.getStatus();
        Integer verifyStatus = accountsLife.getVerifyStatus();
        AccountsPhoto.Status AccountsPhotoStatus = AccountsPhoto.accountsLifeStatusToAccountsPhotoStatus(AccountsLife.Status.of(status), verifyStatus);

        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        AccountsPhoto accountsPhoto = accountsPhotoDao.findByUuidAndSeq(accountUuid, photoSeq);//旧的photo对象

        if(accountsPhoto == null) {
            accountsPhoto = new AccountsPhoto();
            accountsPhoto.setAccount_uuid(accountUuid);
            accountsPhoto.setCreate_time(currentTimeSeconds);
            accountsPhoto.setSeq(photoSeq);
        }else {
            if(Objects.equals(photoUrlOrigin, accountsPhoto.getPhoto_url()) && Objects.equals(AccountsPhotoStatus.getValue(), accountsPhoto.getStatus())) {
                // 地址和状态没变化，不同步
                logger.info("[AccountsLifeSyncToAccountPhotoSave]相册数据无变化，不同步");
                return;
            }
        }

        accountsPhoto.setPhoto_url(photoUrlOrigin);
        accountsPhoto.setBucket(CommConst.AVATAR_BUCKET);
        accountsPhoto.setStatus(AccountsPhotoStatus.getValue());
        accountsPhoto.setUpdate_time(currentTimeSeconds);
        accountsPhoto.setVerify_status(verifyStatus);
        accountsPhoto.setLike_num(0L);
        accountsPhoto.setError_msg("");

        accountsPhoto = accountsPhotoDao.merge(accountsPhoto);

        // 更新缓存
        updateAccountsPhotoRedisBySeq(accountUuid, photoSeq, accountsPhoto);

        logger.info("[AccountsLifeSyncToAccountPhotoSave]同步我的生活数据至相册成功，accountsLife={}", JsonUtils.objectToString(accountsLife));
    }

    /**
     * 同步数据 删除
     * @param accountUuid
     * @param seq
     * @return 执行的数量 0-未执行，1-执行1条
     */
    @Transactional
    public int accountsLifeSyncToAccountPhotoDel(String accountUuid, Integer seq) {
        logger.info("[AccountsLifeSyncToAccountPhotoDel]同步我的生活数据至相册开始，accountUuid={}，seq={}", accountUuid, seq);
        // 相册排序 = 我的生活排序 + 1
        Integer photoSeq = seq + 1;

        AccountsPhoto accountsPhoto = accountsPhotoDao.findByUuidAndSeq(accountUuid, photoSeq);//旧的photo对象

        if(accountsPhoto == null) {
            logger.info("[AccountsLifeSyncToAccountPhotoDel]相册数据不存在，不删除");
            return 0;
        }else {
            accountsPhotoDao.remove(accountsPhoto);
        }
        // 删除缓存
        updateAccountsPhotoRedisBySeq(accountUuid, photoSeq, null);

        logger.info("[AccountsLifeSyncToAccountPhotoDel]同步我的生活数据至相册成功，accountUuid={}，seq={}", accountUuid, seq);
        return 1;
    }

    private boolean vectorDetectUseNewSafeId(String uuid) {
        String cutoff = accountBizStringRedisTemplate.opsForValue().get(USER_PHOTO_DETECT_CUTOFF);
        if (StringUtils.isNotEmpty(cutoff)) {
            long cf = Long.parseLong(cutoff);
            if (System.currentTimeMillis() >= cf) {
                return true;
            }
        }
        return accountBizStringRedisTemplate.opsForSet().isMember(USER_PHOTO_DETECT_VERSION_SET, uuid);
    }

    /**
     *
     * @param accountUuid
     * @param seq
     * @param accountsPhoto
     */
    public void updateAccountsPhotoRedisBySeq(String accountUuid, Integer seq, AccountsPhoto accountsPhoto) {
        Map<String, String> photoMap = new HashMap<String, String>();
        if(accountsPhoto == null) {
            // 删除

        }else {
            // 修改
            photoMap.put("id", accountsPhoto.getId().toString());
            photoMap.put("photo_url", accountsPhoto.getPhoto_url());
            photoMap.put("status", accountsPhoto.getStatus().toString());
            photoMap.put("seq", accountsPhoto.getSeq().toString());
            photoMap.put("bucket", accountsPhoto.getBucket());
            photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountsPhoto.getVerify_status()));
            photoMap.put("error_msg", accountsPhoto.getError_msg());
            photoMap.put("face_score", StringUtil.nullNumberToEmptyString(accountsPhoto.getFace_score()));
            photoMap.put("like_num", StringUtil.nullNumberToEmptyString(accountsPhoto.getLike_num()));
        }

        LOGGER.info("单张修改相册缓存，uuid={},photoMap={}", accountUuid, JsonUtils.objectToString(photoMap));

        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO.setArg(accountUuid);
        accountStringRedisTemplate.opsForHash().put(redisKey, seq.toString(), JsonUtils.objectToString(photoMap));
        accountStringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
    }

}
