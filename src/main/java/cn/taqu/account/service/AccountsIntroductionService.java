package cn.taqu.account.service;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollectionUtil;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RiskSafeIdConstant;
import cn.taqu.account.constant.RiskCertificationTypeEnum;
import cn.taqu.account.dao.*;
import cn.taqu.account.dto.*;
import cn.taqu.account.model.*;
import cn.taqu.account.thread.*;
import cn.taqu.account.utils.TimeFormatUtil;
import cn.taqu.account.vo.IntroductionSampleVo;
import cn.taqu.core.common.client.HttpClient;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import cn.taqu.core.utils.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.taqu.account.common.UuidInfoField.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-15 11:27
 */
@Service
@Slf4j
@Transactional
public class AccountsIntroductionService {

    @Autowired
    private AccountsIntroductionDao accountsIntroductionDao;
    @Autowired
    private AccountsIntroductionPhotoDao accountsIntroductionPhotoDao;
    @Autowired
    private AccountsIntroductionLogDao accountsIntroductionLogDao;
    @Autowired
    private AccountsIntroductionPhotoLogDao accountsIntroductionPhotoLogDao;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private AccountsInfoService accountsInfoService;

    @Qualifier("accountStringRedisTemplate")
    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private BuryService buryService;

    @Autowired
    private AccountsIntroductionSampleDao sampleDao;

    @Autowired
    private AllureService allureService;

    @Autowired
    private AvatarHandleService avatarHandleService;

    /**
     * 自我介绍每日提交次数上限
     */
    private static Integer INTRODUCTION_SUBMIT_TIMES = 5;
    /**
     * 自我介绍 优秀案例uuid
     */
    private static List<String> INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS = new ArrayList<>();
    private static List<String> INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS = new ArrayList<>();
    private static Integer INTRODUCTION_SUBMIT_TIMEOUT_MS = 8000;
    /**
     * h5地址
     */
    private static String OTHER_INTRODUCTION_URL = "https://web.whtaqu.cn/vueiii/trunk/branch/community/promotion/introduce/index.html";
    private static String OTHER_INTRODUCTION_GRAY_URL = "https://web.whtaqu.cn/vueiii/trunk/branch/community/promotion/introduce/pre/index.html";

    private final Cache<Integer, List<IntroductionSampleVo>> sampleCache = new TimedCache<>(180_000);

    public static void initConf(String jsonStr){
        try{
            JSONObject json = JSON.parseObject(jsonStr);
            INTRODUCTION_SUBMIT_TIMES = MapUtils.getInteger(json, "introductionSubmitTimes", 5);
            JSONArray samplesMaleUuids = json.getJSONArray("introductionGoodSamplesMaleUuids");
            if(CollectionUtils.isNotEmpty(samplesMaleUuids)){
                INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS = samplesMaleUuids.toJavaList(String.class);
            }
            JSONArray samplesFemaleUuids = json.getJSONArray("introductionGoodSamplesFemaleUuids");
            if(CollectionUtils.isNotEmpty(samplesFemaleUuids)){
                INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS = samplesFemaleUuids.toJavaList(String.class);
            }
            OTHER_INTRODUCTION_URL = json.getString("otherIntroductionUrl");
            OTHER_INTRODUCTION_GRAY_URL = json.getString("otherIntroductionGrayUrl");
            INTRODUCTION_SUBMIT_TIMEOUT_MS = MapUtils.getInteger(json, "introductionSubmitTimeoutMs", 8000);
        }catch (Exception e){
            log.error("自我介绍配置错误.jsonStr={}.", jsonStr, e);
        }
    }

    /**
     * 保存自我介绍 超时返回
     * @param accountUuid
     * @param content
     * @param bucket
     * @param introductionPhotoDTOs
     */
    public void setAccountsIntroductionInTime(String accountUuid, String content, String bucket, List<IntroductionPhotoDTO> introductionPhotoDTOs,String smid){
        Future<Boolean> future = ThirdPartFactory.setIntroductionPool.submit(new SetIntroductionCall(accountUuid, content, bucket, introductionPhotoDTOs, smid, SoaBaseParams.fromThread().getDistinctRequestId()));
        Boolean execResult = false;
        String redisKey = RedisKeyConstant.ACCOUNT_SUBMIT_INTRODUCTION_TIMEOUT.setArg(accountUuid);
        if(accountStringRedisTemplate.hasKey(redisKey)){
            throw new ServiceException(CodeStatus.INTRODUCTION_RETURN_TIMEOUT);
        }
        accountStringRedisTemplate.opsForValue().set(redisKey, "1", INTRODUCTION_SUBMIT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        try {
            // 等待计算结果，最长等待timeout 毫秒，timeout毫秒后中止任务
            execResult = future.get(INTRODUCTION_SUBMIT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            // 主线程在等待计算结果时被中断!
        } catch (ExecutionException e) {
            // 主线程等待计算结果，但计算抛出异常!
            if(e.getCause() instanceof ServiceException){
                ServiceException serviceException = (ServiceException) e.getCause();
                throw serviceException;
            }else{
                log.warn("ExecutionException", e);
            }
        } catch (TimeoutException e) {
            accountStringRedisTemplate.delete(redisKey);
//            // 主线程等待计算结果超时，因此中断任务线程！
//            try{
//                future.cancel(true);
//                log.warn("线程池处理.超时停止");
//            }catch (Exception ee){
//                log.warn("线程池处理.超时停止.结束子进程失败", ee);
//            }
        } catch (Exception e){
            log.warn("设置自我介绍失败", e);
            throw e;
        }
        if(!execResult){
            throw new ServiceException(CodeStatus.INTRODUCTION_RETURN_TIMEOUT);
        }
    }

    public void setAccountsIntroductionInTimeV2(String accountUuid, String content, String bucket, List<IntroductionPhotoDTO> introductionPhotoDTOs,String smid){
        Future<Boolean> future = ThirdPartFactory.setIntroductionPool.submit(new SetIntroductionCallV2(accountUuid, content, bucket, introductionPhotoDTOs, smid, SoaBaseParams.fromThread().getDistinctRequestId()));
        Boolean execResult = false;
        String redisKey = RedisKeyConstant.ACCOUNT_SUBMIT_INTRODUCTION_TIMEOUT.setArg(accountUuid);
        if(accountStringRedisTemplate.hasKey(redisKey)){
            throw new ServiceException(CodeStatus.INTRODUCTION_RETURN_TIMEOUT);
        }
        accountStringRedisTemplate.opsForValue().set(redisKey, "1", INTRODUCTION_SUBMIT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        try {
            // 等待计算结果，最长等待timeout 毫秒，timeout毫秒后中止任务
            execResult = future.get(INTRODUCTION_SUBMIT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            // 主线程在等待计算结果时被中断!
        } catch (ExecutionException e) {
            // 主线程等待计算结果，但计算抛出异常!
            if(e.getCause() instanceof ServiceException){
                ServiceException serviceException = (ServiceException) e.getCause();
                throw serviceException;
            }else{
                log.warn("ExecutionException", e);
            }
        } catch (TimeoutException e) {
            accountStringRedisTemplate.delete(redisKey);
        } catch (Exception e){
            log.warn("设置自我介绍失败", e);
            throw e;
        }
        if(!execResult){
            throw new ServiceException(CodeStatus.INTRODUCTION_RETURN_TIMEOUT);
        }
    }

    /**
     * 保存自我介绍
     * @param accountUuid
     * @param content
     * @param bucket
     * @param introductionPhotoDTOs
     */
    public IntroductionDetectDTO detectAccountsIntroduction(String accountUuid, String content, String bucket, List<IntroductionPhotoDTO> introductionPhotoDTOs, String smid) {
        // 判断一下是否在审核中
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"my_introduction_status"}, "1", false, false).get(accountUuid);
        String introductionStatus = MapUtils.getString(infoMap, "my_introduction_status", "-1");
        if(introductionStatus.equals(CommonAuditStatus.AUDITING.getStatus().toString())){
            throw new ServiceException(CodeStatus.INTRODUCTION_STATUS_AUDITING);
        }

        // 检查今日次数
        Boolean submitTimesLimit = isIntroductionSubmitTimesLimit(accountUuid);
        if (submitTimesLimit) {
            throw new ServiceException(CodeStatus.INTRODUCTION_TIMES_LIMIT);
        }
        List<String> photoUrls = new ArrayList<>();
        for (IntroductionPhotoDTO introductionPhotoDTOS: introductionPhotoDTOs) {
            // 客户端可能上传前缀
            if(StringUtils.isNotBlank(introductionPhotoDTOS.getImg_name())){
                introductionPhotoDTOS.setImg_name(AvatarHandleService.getAvatarOfSavePhoto(introductionPhotoDTOS.getImg_name()));
                photoUrls.add(introductionPhotoDTOS.getImg_name());
            }
        }

        // 长度校验
        // 去掉换行符
        if (StringUtils.isBlank(content) || content.replaceAll("\n", "n").length() < 40) {
            throw new ServiceException(CodeStatus.INTRODUCTION_COMMON_ERROR_MSG);
        }
        // emoji校验
        if (StringUtil.hasUcs4(content)) {
            throw new ServiceException(CodeStatus.INTRODUCTION_CONTENT_EMOJI_ERROR_MSG);
        }

        // 图片检测
        if (photoUrls == null || photoUrls.size() == 0) {
            // 如果没有图片 返回提示
            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_EMPTY);
        }

        // 图片检测 （违规 质量 活体 非当前头像）
        AccountsPhoto accountAvatar = accountsPhotoService.getAccountAvatar(accountUuid, null, false);
        if (accountAvatar == null) {
            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_EMPTY);
        }
        String avatarUrl = accountAvatar.getPhoto_url();
        List<AccountsPhotoDetectDTO> photoDetectDTOs = new ArrayList<>();
        List<String> imageUrls = new ArrayList<>();
        for (String photoUrl: photoUrls) {
            AccountsPhotoDetectDTO photoDetectDTO = detectIntroductionPhoto(accountUuid, photoUrl, avatarUrl, smid);
            photoDetectDTOs.add(photoDetectDTO);
            imageUrls.add(photoDetectDTO.getFullPhotoUrl());
        }

        // 创建审核记录
        AccountsIntroductionLog accountsIntroductionLog = new AccountsIntroductionLog();
        accountsIntroductionLog.setAccountUuid(accountUuid).setStatus(CommonAuditStatus.AUDITING.getStatus()).setContent(content).setOperator("")
                .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
        accountsIntroductionLog = accountsIntroductionLogDao.merge(accountsIntroductionLog);

        // 违规内容检测
        ShumeiContentCheckDTO shumeiContentCheckDTO = detectIntroductionContent(accountUuid, content, smid, String.valueOf(accountsIntroductionLog.getId()), imageUrls);
        IntroductionDetectDTO introductionDetectDTO = new IntroductionDetectDTO();
        introductionDetectDTO.setDetectPass(true);
        introductionDetectDTO.setShumeiContentCheckDTO(shumeiContentCheckDTO);
        introductionDetectDTO.setPhotoDetectDTOS(photoDetectDTOs);
        introductionDetectDTO.setIntroductionLogId(accountsIntroductionLog.getId());
        return introductionDetectDTO;
    }

    public IntroductionDetectDTO detectAccountsIntroductionV2(String accountUuid, String content, String bucket, List<IntroductionPhotoDTO> introductionPhotoDTOs, String smid) {
        // 判断一下是否在审核中
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"my_introduction_status"}, "1", false, false).get(accountUuid);
        String introductionStatus = MapUtils.getString(infoMap, "my_introduction_status", "-1");
        if(introductionStatus.equals(CommonAuditStatus.AUDITING.getStatus().toString())){
            throw new ServiceException(CodeStatus.AUDIT_ONGOING);
        }

        // 检查今日次数
        Boolean submitTimesLimit = isIntroductionSubmitTimesLimit(accountUuid);
        if (submitTimesLimit) {
            throw new ServiceException(CodeStatus.INTRODUCTION_TIMES_LIMIT);
        }
        List<String> photoUrls = new ArrayList<>();
        for (IntroductionPhotoDTO introductionPhotoDTOS: introductionPhotoDTOs) {
            // 客户端可能上传前缀
            if(StringUtils.isNotBlank(introductionPhotoDTOS.getImg_name())){
                introductionPhotoDTOS.setImg_name(AvatarHandleService.getAvatarOfSavePhoto(introductionPhotoDTOS.getImg_name()));
                photoUrls.add(introductionPhotoDTOS.getImg_name());
            }
        }

        // 长度校验
        // 去掉换行符
        if (StringUtils.isBlank(content) || content.length() < 40) {
            throw new ServiceException(CodeStatus.INTRODUCTION_COMMON_ERROR_MSG);
        }

        // 图片检测
        if (photoUrls == null || photoUrls.size() == 0) {
            // 如果没有图片 返回提示
            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_EMPTY);
        }

        // 图片检测 （违规 质量 活体 非当前头像）
        AccountsPhoto accountAvatar = accountsPhotoService.getAccountAvatar(accountUuid, null, false);
        if (accountAvatar == null) {
            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_EMPTY);
        }
        String avatarUrl = accountAvatar.getPhoto_url();
        List<AccountsPhotoDetectDTO> photoDetectDTOs = new ArrayList<>();
        List<String> imageUrls = new ArrayList<>();
        for (String photoUrl: photoUrls) {
            AccountsPhotoDetectDTO photoDetectDTO = detectIntroductionPhoto(accountUuid, photoUrl, avatarUrl, smid);
            photoDetectDTOs.add(photoDetectDTO);
            imageUrls.add(photoDetectDTO.getFullPhotoUrl());
        }

        // 创建审核记录
        AccountsIntroductionLog accountsIntroductionLog = new AccountsIntroductionLog();
        accountsIntroductionLog.setAccountUuid(accountUuid).setStatus(CommonAuditStatus.AUDITING.getStatus()).setContent(content).setOperator("")
            .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
        accountsIntroductionLog = accountsIntroductionLogDao.merge(accountsIntroductionLog);

        // 违规内容检测
        ShumeiContentCheckDTO shumeiContentCheckDTO = detectIntroductionContent(accountUuid, content, smid, String.valueOf(accountsIntroductionLog.getId()), imageUrls);
        IntroductionDetectDTO introductionDetectDTO = new IntroductionDetectDTO();
        introductionDetectDTO.setDetectPass(true);
        introductionDetectDTO.setShumeiContentCheckDTO(shumeiContentCheckDTO);
        introductionDetectDTO.setPhotoDetectDTOS(photoDetectDTOs);
        introductionDetectDTO.setIntroductionLogId(accountsIntroductionLog.getId());
        return introductionDetectDTO;
    }

    /**
     * 添加提交次数
     * @param accountUuid
     */
    public void addIntroductionSubmitTime(String accountUuid) {
        String redisKey = RedisKeyConstant.ACCOUNT_SUBMIT_INTRODUCTION_TIMES.setArg(accountUuid);
        Integer times = getIntroductionSubmitTimes(accountUuid);
        times++;
        accountStringRedisTemplate.opsForValue().set(redisKey, times.toString(), TimeFormatUtil.getTodayEndMsTimeStampWithRandom(), TimeUnit.MILLISECONDS);
    }

    /**
     * 获取自我介绍提交次数
     * @param accountUuid
     * @return
     */
    private Integer getIntroductionSubmitTimes(String accountUuid){
        String redisKey = RedisKeyConstant.ACCOUNT_SUBMIT_INTRODUCTION_TIMES.setArg(accountUuid);
        String timesStr = accountStringRedisTemplate.opsForValue().get(redisKey);
        Integer times = 0;
        if(StringUtils.isNotBlank(timesStr) && StringUtils.isNumeric(timesStr)){
            times = Integer.parseInt(timesStr);
        }
        return times;
    }

    /**
     * 是否今日达到次数上限
     * @param accountUuid
     * @return
     */
    private Boolean isIntroductionSubmitTimesLimit(String accountUuid){
        Integer submitTimes = getIntroductionSubmitTimes(accountUuid);
        return submitTimes >= INTRODUCTION_SUBMIT_TIMES;
    }

    /**
     * 检测自我介绍
     * @param accountUuid
     * @param content
     * @param bucket
     * @param introductionPhotoDTOs
     */
    public void saveMyIntroductonAndAddTimes(String accountUuid, String content, String bucket, List<IntroductionPhotoDTO> introductionPhotoDTOs, IntroductionDetectDTO introductionDetectDTO) {
        // 保存 + 缓存
        this.saveMyIntroduction(accountUuid, content, bucket, introductionPhotoDTOs, introductionDetectDTO);
        // 增加次数
        this.addIntroductionSubmitTime(accountUuid);
    }
    /**
     * 检测自我介绍
     * @param accountUuid
     * @param content
     * @param bucket
     * @param introductionPhotoDTOs
     */
    public void saveMyIntroduction(String accountUuid, String content, String bucket, List<IntroductionPhotoDTO> introductionPhotoDTOs, IntroductionDetectDTO introductionDetectDTO) {

        AccountsIntroductionLog accountsIntroductionLog = new AccountsIntroductionLog();
        accountsIntroductionLog.setId(introductionDetectDTO.getIntroductionLogId());
        accountsIntroductionLog.setAccountUuid(accountUuid).setStatus(CommonAuditStatus.AUDITING.getStatus()).setContent(content).setOperator("")
                .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
        accountsIntroductionLog = accountsIntroductionLogDao.merge(accountsIntroductionLog);

        List<AccountsIntroductionPhotoLog> introductionPhotoLogs = new ArrayList<>();
        for (IntroductionPhotoDTO introductionPhotoDTO: introductionPhotoDTOs) {
            Integer width = introductionPhotoDTO.getWidth();
            Integer height = introductionPhotoDTO.getHeight();
            AccountsIntroductionPhotoLog photoLog = new AccountsIntroductionPhotoLog();
            photoLog.setAccountUuid(accountUuid).setBucket(bucket).setIntroductionLogId(accountsIntroductionLog.getId())
                    .setPhotoUrl(introductionPhotoDTO.getImg_name()).setWidth(width == null ? 0 : width).setHeight(height == null ? 0 : height)
                    .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
            introductionPhotoLogs.add(photoLog);

            // 2024.06.07 文件使用，通知图片系统
            BuryService.pushToPicSysUsedFileBiz(CommConst.APPCODE_TAQU, introductionPhotoDTO.getImg_name(), CommConst.AVATAR_BUCKET, DateUtil.currentTimeSeconds());
        }
        accountsIntroductionPhotoLogDao.merge(introductionPhotoLogs);
        // 构造一个map
//        String contentThirdBizCode = "";
//        String contentSuggestion = "";
//        if(introductionDetectDTO != null && introductionDetectDTO.getShumeiContentCheckDTO() != null){
//            String requestId = introductionDetectDTO.getShumeiContentCheckDTO().getRequestId();
//            String suggestion = introductionDetectDTO.getShumeiContentCheckDTO().getSuggestion();
//            contentThirdBizCode = StringUtils.isBlank(requestId) ? "" : requestId;
//            contentSuggestion = StringUtils.isBlank(suggestion) ? "" : suggestion;
//        }

        JSONArray ja = new JSONArray();
        List<AccountsPhotoDetectDTO> photoDetectDTOS = introductionDetectDTO.getPhotoDetectDTOS();
        for (AccountsPhotoDetectDTO photoDetectDTO : photoDetectDTOS) {
            // photoDetectDTO 不为空
            if(Objects.isNull(photoDetectDTO.getCompareFaceDto()) || Objects.isNull(photoDetectDTO.getTencentImgQualityDTO())) {
                throw new ServiceException(CodeStatus.INTRODUCTION_DETECT_FAIL_TIMEOUT);
            }
            JSONObject picObj = new JSONObject();
            picObj.put("photoUrl", AvatarHandleService.getAvatarOfSavePhoto(photoDetectDTO.getPhotoUrl()));
            picObj.put("imgCheckRequestId", Objects.nonNull(photoDetectDTO.getShumeiImgCheckResponseDTO()) ? photoDetectDTO.getShumeiImgCheckResponseDTO().getRequestId() : "");
            picObj.put("imgCheckRiskLevel", Objects.nonNull(photoDetectDTO.getShumeiImgCheckResponseDTO()) ? photoDetectDTO.getShumeiImgCheckResponseDTO().getRiskLevel() : "");
            picObj.put("imgCheckRiskDescription", Objects.nonNull(photoDetectDTO.getShumeiImgCheckResponseDTO()) ? photoDetectDTO.getShumeiImgCheckResponseDTO().getRiskDescription() : "");
            picObj.put("imgQualityRequestId", photoDetectDTO.getTencentImgQualityDTO().getRequestId());
            picObj.put("imgCompareRequestId", photoDetectDTO.getCompareFaceDto().getRequestId());
            picObj.put("imgCompareScore", photoDetectDTO.getCompareFaceDto().getScore());
            ja.add(picObj);
        }

        Map<String, String> infoCache = new HashMap<>();
        infoCache.put("my_introduction_status", "0");
        infoCache.put("my_introduction_content", content);
        List<Map<String, String>> imgs = new ArrayList<>();
        for (AccountsIntroductionPhotoLog introductionPhotoLog : introductionPhotoLogs) {
            Map<String, String> img = new HashMap<>();
            img.put("img_name", introductionPhotoLog.getPhotoUrl());
            img.put("width", introductionPhotoLog.getWidth() + "");
            img.put("height", introductionPhotoLog.getHeight() + "");
            imgs.add(img);
        }
        infoCache.put("my_introduction_imgs", JSON.toJSONString(imgs));
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);
    }

    /**
     * 检测内容
     *
     * @param accountUuid
     * @param content
     */
    private ShumeiContentCheckDTO detectIntroductionContent(String accountUuid, String content, String smid, String bizId, List<String> photoUrls) {
        Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();

        // 文本检测 涉嫌敏感
        ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accountUuid, content, photoUrls, smid, bizId, RiskDetectEnum.TEXT_INTRODUCTION.name(), appcode, cloned);
        log.info("数美审核结果.自我介绍.uuid={}.result={}", accountUuid, contentCheckDTO);

        if (contentCheckDTO == null ||
                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
            throw new ServiceException(CodeStatus.INTRODUCTION_CONTENT_SENSITIVE);
        }
        return contentCheckDTO;
    }

    /**
     * 图片检测
     * @param accountUuid
     * @param photoUrl
     * @param avatarUrl
     */
    public AccountsPhotoDetectDTO detectIntroductionPhoto(String accountUuid, String photoUrl, String avatarUrl, String smid) {
        // 去前缀，拼上源站域名
        photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
        photoUrl = CommConst.AVATAR_SRC_HOST + photoUrl;

        // 去前缀，拼上源站域名
        avatarUrl = AvatarHandleService.getAvatarOfSavePhoto(avatarUrl);
        avatarUrl = CommConst.AVATAR_SRC_HOST + avatarUrl;

        Boolean samePhoto = isSamePhoto(accountUuid, photoUrl, avatarUrl);
        if (samePhoto) {
            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_SAME_WITH_AVATAR);
        }

        Map<String, Object> infoMap = accountsInfoService.singleGetInfo(accountUuid, new String[] { UuidInfoField.REAL_PERSON_CERTIFICATION, UuidInfoField.SEX_TYPE });
        boolean isReal = "1".equals(MapUtils.getString(infoMap, UuidInfoField.REAL_PERSON_CERTIFICATION, "2"));
        boolean isFemale = "2".equals(MapUtils.getString(infoMap, UuidInfoField.SEX_TYPE, "1"));

        // 真人女用户相似度比对
        if (isReal && isFemale) {
            Boolean isSimilar = isSimilarPhoto(accountUuid, photoUrl);
            if (isSimilar) {
                throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_SIMILAR_WITH_AVATAR);
            }
        }

        // 头像违规检测：数美
        // 图片质量检测：腾讯
        // AI图片检测：腾讯图片比对
        ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = null;
        TencentImgQualityDTO tencentImgQualityDTO = null;
        CompareFaceDto compareFaceDto = null;
        // 查询活体图片
        AliyunLiveFaceDetect liveFaceDetect = SpringContextHolder.getBean(AliyunLiveFaceDetectDao.class).getInfoByAccountUuid(accountUuid);
        String faceBasePhoto = "";
        if (liveFaceDetect != null) {
            faceBasePhoto = liveFaceDetect.getBase_photo_url();
        } else {
            throw new ServiceException(CodeStatus.INTRODUCTION_FAIL_LIVE_FACE);
        }

//        Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
//        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
//        Future<ShumeiImgCheckResponseDTO> shumeiFuture = ThirdPartFactory.shumeiImgCheckPool.submit(new ShumeImgCheckWithSmidCall(accountUuid, photoUrl, smid, appcode, cloned));
        Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, photoUrl));
        Future<CompareFaceDto> imgCompareFuture = ThirdPartFactory.tencentImgComparePool.submit(new TencentImgCompareCall(accountUuid, photoUrl, faceBasePhoto, RiskCertificationTypeEnum.UNKNOWN));

        try {
//            shumeiImgCheckResponseDTO = shumeiFuture.get(5000, TimeUnit.MILLISECONDS);
            tencentImgQualityDTO = imgQualityFuture.get(5000, TimeUnit.MILLISECONDS);
            compareFaceDto = imgCompareFuture.get(5000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.warn("线程池处理.InterruptedException", e);
        } catch (ExecutionException e) {
            if(e.getCause() instanceof ServiceException){
                ServiceException serviceException = (ServiceException) e.getCause();
                throw serviceException;
            }else{
                log.warn("ExecutionException", e);
            }
        } catch (TimeoutException e) {
            try{
//                shumeiFuture.cancel(true);
                imgQualityFuture.cancel(true);
                imgCompareFuture.cancel(true);
                log.warn("线程池处理.超时停止");
            }catch (Exception ee){
                // 结束子进程
                log.warn("线程池处理.超时停止.结束子进程失败", ee);
            }
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        }

//        log.info("图片违规检测.结果={}", shumeiImgCheckResponseDTO == null ? "" :JSON.toJSONString(shumeiImgCheckResponseDTO));
        log.info("图片质量检测.结果={}", tencentImgQualityDTO == null ? "" :JSON.toJSONString(tencentImgQualityDTO));
        log.info("图片比对检测.结果={}", compareFaceDto == null ? "" :JSON.toJSONString(compareFaceDto));

//        if(shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), RiskDetectRiskLevelEnum.REJECT.name())){
//            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_ILLEGAL);
//        }

        if (tencentImgQualityDTO == null) {
            throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
        }
        if (!tencentImgQualityDTO.isImgQualityPass()) {
            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_FAIL_QUALIITY);
        }
        if (compareFaceDto == null || !compareFaceDto.isSimilarity()) {
            throw new ServiceException(CodeStatus.INTRODUCTION_PHOTO_FAIL_INCONSISTENT);
        }

        AccountsPhotoDetectDTO photoDetectDTO = new AccountsPhotoDetectDTO();
        photoDetectDTO.setShumeiImgCheckResponseDTO(shumeiImgCheckResponseDTO);
        photoDetectDTO.setTencentImgQualityDTO(tencentImgQualityDTO);
        photoDetectDTO.setCompareFaceDto(compareFaceDto);
        photoDetectDTO.setPhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(photoUrl));
        photoDetectDTO.setFullPhotoUrl(photoUrl);
        return photoDetectDTO;
    }

    /**
     * 同一张图片比对
     * @param accountUuid   accountUuid
     * @param pic1          最好用源站域名
     * @param pic2          最好用源站域名
     * @return
     */
    public static Boolean isSamePhoto(String accountUuid, String pic1, String pic2){
        try {
            HttpClient httpClient = new HttpClient(pic1 + "?qhash/md5", 6 * 1000, 6 * 1000, 6 * 1000);
            String response = httpClient.get("");
            String hash1 = JSON.parseObject(response).getString("hash");

            HttpClient httpClient2 = new HttpClient(pic2 + "?qhash/md5", 6 * 1000, 6 * 1000, 6 * 1000);
            String response2 = httpClient2.get("");
            String hash2 = JSON.parseObject(response2).getString("hash");
            if(StringUtils.isNotBlank(hash1) && hash1.equals(hash2)){
                return true;
            }
        }catch (Exception e){
            log.warn("相同图片比对失败.uuid={}.pic1={}.pic2={}", accountUuid, pic1, pic2, e);
            return false;
        }
        return false;
    }

    public Boolean isSimilarPhoto(String uuid, String url) {
        String riskBizId = RiskSafeService.genBizId(url);
        ImgVectorDetectDto dto = new ImgVectorDetectDto();
        dto.setSafeId(RiskSafeIdConstant.SIMILAR_PHOTO_DETECT);
        dto.setBizId(riskBizId);
        dto.setImageUrl(Collections.singletonList(url));
        dto.setSenderUuid(uuid);
        ImgVectorDetectRespDto resp = RiskSafeService.imageVectorDetect(dto);
        return !resp.pass();
    }

    /**
     * 获取 优质自我介绍案例
     * @param accountUuid
     * @param currentAccountUuid
     * @return
     */
    public Map<String, Object> getIntroductionFromGoodSamples(String accountUuid, String currentAccountUuid) {
        Integer sexType = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);
        String uuidFromGoodSamples = getRandomUuidFromGoodSamples(sexType, accountUuid, currentAccountUuid);
        if(StringUtils.isBlank(uuidFromGoodSamples)){
            throw new ServiceException(CodeStatus.INTRODUCTION_NO_GOOD_SAMPLES_UUID);
        }
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{uuidFromGoodSamples}, new String[]{"account_name", "avatar", "pass_introduction_content", "pass_introduction_imgs"}, "1", false, false).get(uuidFromGoodSamples);
        Map<String, Object> result = new HashMap<>();
        result.put("account_uuid", uuidFromGoodSamples);
        result.put("account_name", MapUtils.getString(infoMap, "account_name", ""));
        String avatar = MapUtils.getString(infoMap, "avatar", AvatarHandleService.getDefAvatar(sexType));
        avatar = ToolsService.addPhotoUrlPreDomain(AvatarHandleService.getAvatarOfSavePhoto(avatar));
        result.put("account_avatar", avatar);
        String passIntroductionContent = MapUtils.getString(infoMap, "pass_introduction_content", "");
        passIntroductionContent = passIntroductionContent.replace("\n", "&n");
        result.put("pass_introduction_content", passIntroductionContent);

        String introductionImgsStr = MapUtils.getString(infoMap, "pass_introduction_imgs", "");
        List<JSONObject> introductionImgs = new ArrayList<>();
        if(StringUtils.isNotBlank(introductionImgsStr)){
            introductionImgs = JSON.parseObject(introductionImgsStr, List.class);
            for (JSONObject img :introductionImgs) {
                img.put("photo_url", ToolsService.addPhotoUrlPreDomain(AvatarHandleService.getAvatarOfSavePhoto(img.getString("img_name"))));
                img.remove("img_name");
            }
        }
        result.put("pass_introduction_imgs", introductionImgs);

        return result;
    }

    public List<IntroductionSampleVo> samplesV2(Integer sexType) {
        return sampleCache.get(sexType, false, () -> {
            List<AccountsIntroductionSample> samples = sampleDao.listBySex(sexType);
            return samples.stream().map(s -> {
                IntroductionSampleVo row = new IntroductionSampleVo();
                row.setSampleId(s.getId());
                row.setPassIntroductionContent(s.getSample());
                row.setAccostCount("999+人");
                if (StringUtils.isNotBlank(s.getImages())) {
                    row.setPassIntroductionImgs(
                        Stream.of(s.getImages().split(","))
                            .map(p -> ImmutableMap.of("photo_url", ToolsService.addPhotoUrlPreDomain(p)))
                            .collect(Collectors.toList())
                    );
                }
                return row;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 获取一下用户uuid
     * @param sexType
     * @param accountUuid
     * @param currentUuid
     * @return
     */
    private String getRandomUuidFromGoodSamples(Integer sexType, String accountUuid, String currentUuid){
        if(INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.size() == 0 || INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.size() == 0){
            return null;
        }
        List<String> exUuids = Arrays.asList(accountUuid, currentUuid);
        // 首次走随机逻辑
        if(StringUtils.isBlank(currentUuid)){
            return getRandomUuidFromGoodSamplesFirst(sexType, exUuids);
        }else{
            if(sexType == 1){
                int index = INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.indexOf(currentUuid);
                if(++index >= INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.size()){
                    index = 0;
                }
                String uuid = INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.get(index);
                // 如果不是自己
                if(!uuid.equals(accountUuid)){
                    return uuid;
                }else{
                    if(++index >= INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.size()){
                        index = 0;
                    }
                    return INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.get(index);
                }
            }else{
                int index = INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.indexOf(currentUuid);
                if(++index >= INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.size()){
                    index = 0;
                }
                String uuid = INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.get(index);
                if(!uuid.equals(accountUuid)){
                    return uuid;
                }else{
                    if(++index >= INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.size()){
                        index = 0;
                    }
                    return INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.get(index);
                }
            }
        }
    }

//    private List<String> sampleUuids(Integer sexType, String accountUuid){
//        if (sexType == 1 && CollectionUtils.isNotEmpty(INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS)) {
//            return INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.stream().filter(s -> !accountUuid.equals(s)).collect(Collectors.toList());
//        } else if (sexType == 2 && CollectionUtils.isNotEmpty(INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS)) {
//            return INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.stream().filter(s -> !accountUuid.equals(s)).collect(Collectors.toList());
//        }
//        return Collections.emptyList();
//    }

    private String getRandomUuidFromGoodSamplesFirst(Integer sexType, List<String> exUuids){
        String goodSampleUuid = "";
        if(sexType == 1){
            if(INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.size() == 0){
                return null;
            }
            Random rand = new Random();
            for (int i = 0; i < exUuids.size() + 1; i++) {
                goodSampleUuid = INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.get(rand.nextInt(INTRODUCTION_GOOD_SAMPLES_MALES_UUIDS.size()));
                if(!exUuids.contains(goodSampleUuid)){
                    return goodSampleUuid;
                }
            }
        }else{
            if(INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.size() == 0){
                return null;
            }
            Random rand = new Random();
            for (int i = 0; i < exUuids.size() + 1; i++) {
                goodSampleUuid = INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.get(rand.nextInt(INTRODUCTION_GOOD_SAMPLES_FEMALES_UUIDS.size()));
                if(!exUuids.contains(goodSampleUuid)){
                    return goodSampleUuid;
                }
            }
        }
        return null;
    }

    /**
     * 审核通过
     * @param introductionLogId
     * @param operatorToken
     */
    public void auditPass(Long introductionLogId, String operatorToken){
        AccountsIntroductionLog accountsIntroductionLog =accountsIntroductionLogDao.getById(introductionLogId);
        // 待审
        if(accountsIntroductionLog != null && CommonAuditStatus.AUDITING.getStatus().equals(accountsIntroductionLog.getStatus())){
            accountsIntroductionLog.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
            accountsIntroductionLog.setOperator(operatorToken);
            accountsIntroductionLog.setAuditTime(DateUtil.currentTimeSeconds());
        }else{
            return;
        }
        log.info("审核自我介绍数据.token={}.id={}", operatorToken, introductionLogId);
        String accountUuid = accountsIntroductionLog.getAccountUuid();
        // 删除当前用户通过的数据
        AccountsIntroduction oldIntro = accountsIntroductionDao.findByUuid(accountUuid);
        if(oldIntro != null){
            accountsIntroductionDao.removeById(oldIntro.getId());
            // 删除照片
            List<AccountsIntroductionPhoto> introPhotos = accountsIntroductionPhotoDao.findByIntroductionId(oldIntro.getId());
            List<Long> introPhotoIds = introPhotos.stream().map(AccountsIntroductionPhoto::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(introPhotoIds)){
                accountsIntroductionPhotoDao.removeByIds(introPhotoIds);
            }
        }
        accountsIntroductionLog = accountsIntroductionLogDao.merge(accountsIntroductionLog);

        // 保存到状态表
        AccountsIntroduction introduction = new AccountsIntroduction();
        introduction.setAccountUuid(accountsIntroductionLog.getAccountUuid())
                .setContent(accountsIntroductionLog.getContent()).setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus())
                .setAuditTime(accountsIntroductionLog.getAuditTime()).setOperator(operatorToken)
                .setCreateTime(accountsIntroductionLog.getCreateTime()).setUpdateTime(accountsIntroductionLog.getUpdateTime());
        introduction = accountsIntroductionDao.merge(introduction);

        // 找出图片
        List<AccountsIntroductionPhotoLog> introPhotoLogs = accountsIntroductionPhotoLogDao.findByIntroductionLogId(introductionLogId);
        List<AccountsIntroductionPhoto> introPhotos = new ArrayList<>();
        for (AccountsIntroductionPhotoLog introPhotoLog: introPhotoLogs){
            Integer width = introPhotoLog.getWidth();
            Integer height = introPhotoLog.getHeight();
            AccountsIntroductionPhoto introPhoto = new AccountsIntroductionPhoto();
            introPhoto.setAccountUuid(introPhotoLog.getAccountUuid()).setIntroductionId(introduction.getId())
                    .setPhotoUrl(introPhotoLog.getPhotoUrl()).setBucket(introPhotoLog.getBucket())
                    .setWidth(width == null ? 0 : width).setHeight(height == null ? 0 : height)
                    .setCreateTime(introPhotoLog.getCreateTime()).setUpdateTime(introPhotoLog.getUpdateTime());
            introPhotos.add(introPhoto);
        }
        accountsIntroductionPhotoDao.merge(introPhotos);

        // 后置处理
        // 更新 缓存
        Map<String, String> infoCache = new HashMap<>();
        infoCache.put(MY_INTRODUCTION_STATUS, introduction.getStatus().toString());
        infoCache.put(PASS_INTRODUCTION_STATUS, introduction.getStatus().toString());
        infoCache.put(PASS_INTRODUCTION_CONTENT, introduction.getContent());
        List<Map<String, String>> imgs = new ArrayList<>();
        for (AccountsIntroductionPhoto introductionPhoto : introPhotos) {
            Map<String, String> img = new HashMap<>();
            img.put("img_name", introductionPhoto.getPhotoUrl());
            img.put("width", introductionPhoto.getWidth() + "");
            img.put("height", introductionPhoto.getHeight() + "");
            imgs.add(img);
        }
        infoCache.put(PASS_INTRODUCTION_IMGS, JSON.toJSONString(imgs));

        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);
        // 推社区
        buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.SELF_INTRODUCTION.getType());
        allureService.refreshAllure(accountUuid);
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.INTRODUCTION, introduction.getContent());
    }

    /**
     * 审核不通过
     * @param introductionLogId
     * @param operatorToken
     */
    public void auditReject(Long introductionLogId, String operatorToken) {
        // 更新记录表
        AccountsIntroductionLog accountsIntroductionLog = accountsIntroductionLogDao.getById(introductionLogId);
        if(accountsIntroductionLog == null){
            log.warn("未查询到数据, id={}", introductionLogId);
            return;
        }
        boolean isAuditingStatus = Objects.equals(accountsIntroductionLog.getStatus(), CommonAuditStatus.AUDITING.getStatus());

        accountsIntroductionLog.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
        accountsIntroductionLog.setOperator(operatorToken);
//        Long auditTimeHis = accountsIntroductionLog.getAuditTime();
        accountsIntroductionLog.setAuditTime(DateUtil.currentTimeSeconds());

        log.info("审核自我介绍数据.token={}.id={}", operatorToken, introductionLogId);
        accountsIntroductionLog = accountsIntroductionLogDao.merge(accountsIntroductionLog);

        String accountUuid = accountsIntroductionLog.getAccountUuid();
        Map<String, String> infoCache = new HashMap<>();

        // 获取用户已通过的自我介绍
        Map<String, Object> infos = accountsInfoService.getUserInfo(accountUuid, MY_INTRODUCTION_CONTENT, PASS_INTRODUCTION_CONTENT, PASS_INTRODUCTION_IMGS);
        String passIntroductionContent = MapUtils.getString(infos, PASS_INTRODUCTION_CONTENT, "");
        String passIntroductionImgStr = MapUtils.getString(infos, PASS_INTRODUCTION_IMGS, "");
        String myIntroductionContent = MapUtils.getString(infos, MY_INTRODUCTION_CONTENT, "");

//        if (StringUtils.isBlank(passIntroductionContent)) {
//            log.info("无已通过自我介绍: {}", accountUuid);
//            infoCache.put(MY_INTRODUCTION_STATUS, CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
//            infoCache.put(PASS_INTRODUCTION_STATUS, CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
//            return;
//        }

        if (Objects.equals(passIntroductionContent, accountsIntroductionLog.getContent())) {
            log.info("当前展示自我介绍质检拒绝: {}", accountUuid);
            infoCache.put(PASS_INTRODUCTION_STATUS, CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
            infoCache.put(PASS_INTRODUCTION_CONTENT, "");// 通过的自我介绍内容
            infoCache.put(PASS_INTRODUCTION_IMGS, "");

            if (Objects.equals(myIntroductionContent, accountsIntroductionLog.getContent())) {
                infoCache.put(MY_INTRODUCTION_CONTENT, "");
                infoCache.put(MY_INTRODUCTION_IMGS, "");
                infoCache.put(MY_INTRODUCTION_STATUS, CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
            }

            // 获取上一次审核通过的自我介绍
//            AccountsIntroductionLog lastLog = accountsIntroductionLogDao.getLastPass(accountUuid, auditTimeHis);
//            infoCache.put(PASS_INTRODUCTION_CONTENT, lastLog.getContent());// 通过的自我介绍内容
//            infoCache.put(MY_INTRODUCTION_CONTENT, lastLog.getContent());// 通过的自我介绍内容
//            List<AccountsIntroductionPhotoLog> lastPhotos = accountsIntroductionPhotoLogDao.findByIntroductionLogId(lastLog.getId());
//            if (CollectionUtils.isNotEmpty(lastPhotos)) {
//                List<Map<String, String>> images = new ArrayList<>();
//                for (AccountsIntroductionPhotoLog photo : lastPhotos) {
//                    Map<String, String> img = new HashMap<>();
//                    img.put("img_name", photo.getPhotoUrl());
//                    img.put("width", photo.getWidth() + "");
//                    img.put("height", photo.getHeight() + "");
//                    images.add(img);
//                }
//                infoCache.put(PASS_INTRODUCTION_IMGS, JSON.toJSONString(images));
//                infoCache.put(MY_INTRODUCTION_IMGS, JSON.toJSONString(images));
//            } else {
//                infoCache.put(PASS_INTRODUCTION_IMGS, "");
//                infoCache.put(MY_INTRODUCTION_IMGS, "");
//            }
        } else if (isAuditingStatus) {
            // 回滚用户已通过的自我介绍
            infoCache.put(MY_INTRODUCTION_STATUS, CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
            infoCache.put(MY_INTRODUCTION_CONTENT, passIntroductionContent);
            infoCache.put(MY_INTRODUCTION_IMGS, passIntroductionImgStr);
            infoCache.put(PASS_INTRODUCTION_STATUS, CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
            infoCache.put(PASS_INTRODUCTION_CONTENT, passIntroductionContent);
            infoCache.put(PASS_INTRODUCTION_IMGS, passIntroductionImgStr);
        }  else {
            log.warn("质检拒绝过去的自我介绍: {}, 不触发小秘书", accountUuid);
            return;
        }
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);
        allureService.refreshAllure(accountUuid);
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.INTRODUCTION, infoCache.get(PASS_INTRODUCTION_CONTENT));

        // 删除照片
        List<AccountsIntroductionPhotoLog> introPhotos = accountsIntroductionPhotoLogDao.findByIntroductionLogId(introductionLogId);
        if (CollectionUtils.isNotEmpty(introPhotos)) {
            List<String> introPhotoUrl = introPhotos.stream().map(AccountsIntroductionPhotoLog::getPhotoUrl).collect(Collectors.toList());
            Collection<String> aboutToDelete = introPhotoUrl;
            // 2024.12.06 拒绝后需要回滚上一次通过的自我介绍，照片删除需要做下比对
            List<Map<String, Object>> passImages = JsonUtils.stringToObject(MapUtils.getString(infoCache, PASS_INTRODUCTION_IMGS, ""), new TypeReference<List<Map<String, Object>>>() {});
            if (CollectionUtils.isNotEmpty(passImages)) {
                List<String> passImageNames = passImages.stream().map(m -> MapUtils.getString(m, "img_name", "")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(passImageNames)) {
                    aboutToDelete = CollectionUtil.subtract(introPhotoUrl, passImageNames);
                }
            }
            // 2024.06.07 审核拒绝，通知图片系统禁用
            for (String url : aboutToDelete) {
                BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, url, CommConst.AVATAR_BUCKET, 1, DateUtil.currentTimeSeconds());
            }
        }

        // 后置处理
        // 发小秘书
//        sendAuditFailSystemNotice(accountUuid);
    }

    /**
     * 重置自我介绍
     * @param accountUuid
     */
    public void resetAccountsIntroduction(String accountUuid) {
        log.info("重置自我介绍. String={}", accountUuid);

        // 删除当前用户通过的数据
        AccountsIntroduction oldIntro = accountsIntroductionDao.findByUuid(accountUuid);
        List<String> introPhotoUrl = Lists.newArrayList();
        if(oldIntro != null){
            accountsIntroductionDao.removeById(oldIntro.getId());
            // 删除照片
            List<AccountsIntroductionPhoto> introPhotos = accountsIntroductionPhotoDao.findByIntroductionId(oldIntro.getId());
            List<Long> introPhotoIds = introPhotos.stream().map(AccountsIntroductionPhoto::getId).collect(Collectors.toList());
            introPhotoUrl = introPhotos.stream().map(AccountsIntroductionPhoto::getPhotoUrl).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(introPhotoIds)){
                accountsIntroductionPhotoDao.removeByIds(introPhotoIds);
            }
        }

        Map<String, String> infoCache = new HashMap<>();

        // 获取用户已通过的自我介绍
        Map<String, Object> infos = accountsInfoService.getUserInfo(accountUuid, "pass_introduction_content");
        String passIntroductionContent = MapUtils.getString(infos, "pass_introduction_content");

        if (StringUtils.isBlank(passIntroductionContent)) {
            infoCache.put("my_introduction_status", CommonAuditStatus.AUDIT_FAIL.getStatus().toString());
            infoCache.put("pass_introduction_status", CommonAuditStatus.AUDIT_FAIL.getStatus().toString());
        } else {
            infoCache.put("my_introduction_status", CommonAuditStatus.AUDIT_FAIL.getStatus().toString());
            infoCache.put("pass_introduction_status", CommonAuditStatus.AUDIT_FAIL.getStatus().toString());
            infoCache.put("pass_introduction_content", "");// 通过的自我介绍内容
            infoCache.put("pass_introduction_imgs", "");// 通过的自我介绍图片
        }

        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoCache);
        log.info("重置自我介绍成功. String={}", accountUuid);

        // 2024.06.07 审核拒绝，通知图片系统禁用
        for (String url : introPhotoUrl) {
            BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, url, CommConst.AVATAR_BUCKET, 1, DateUtil.currentTimeSeconds());
        }

    }

    /**
     * 获取我的自我介绍
     * @param accountUuid
     * @return
     */
    public Map<String, Object> getMyIntroduction(String accountUuid) {
        Map<String, Object> result = new HashMap<>();

        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"my_introduction_status", "my_introduction_content", "my_introduction_imgs"}, "1", false, false).get(accountUuid);
        result.put("pass_introduction_status", MapUtils.getString(infoMap, "my_introduction_status", "-1"));
        result.put("pass_introduction_content", MapUtils.getString(infoMap, "my_introduction_content", ""));

        String myIntroductionImgsStr = MapUtils.getString(infoMap, "my_introduction_imgs", "");
        List<JSONObject> introductionImgs = new ArrayList<>();
        if(StringUtils.isNotBlank(myIntroductionImgsStr)){
            introductionImgs = JSON.parseObject(myIntroductionImgsStr, List.class);
            for (JSONObject img :introductionImgs) {
                // 使用源站域名 + token

                img.put("img_name", getPrivatePic(img.getString("img_name")) );
                String imgWidth = img.getString("width");
                String imgHeight = img.getString("height");
                img.put("width", StringUtils.isNumeric(imgWidth) && !imgWidth.equals("0") ? imgWidth : CommConst.DEFAULT_INTRODUCTION_IMG_WIDTH);
                img.put("height", StringUtils.isNumeric(imgHeight) && !imgHeight.equals("0") ? imgHeight : CommConst.DEFAULT_INTRODUCTION_IMG_HEIGHT);
            }
        }
        result.put("pass_introduction_imgs", introductionImgs);

        return result;
    }

    /**
     * 获取访问私有地址
     *
     * @param url
     * @return
     */
    public static String getPrivatePic(String url) {
       return SoaService.privateDownloadUrl(CommConst.AVATAR_BUCKET_TYPE, CommConst.AVATAR_SRC_HOST + AvatarHandleService.getAvatarOfSavePhoto(url), CommConst.APPCODE_TAQU, 3600L);
    }

    /**
     * 获取自我介绍地址
     * @return
     */
    public Map<String, String> getIntroductionFromGoodSamplesUrl() {
        Map<String, String> result = new HashMap<>();
        String env = RequestParams.getSoa_basic_java().getEnv();
        String introductionUrl = "";
        if(StringUtils.isNotBlank(env) && (env.equals("gray") || env.equals("1"))){
            introductionUrl = "m=web&a=url&hide_nav=0&ul=" + OTHER_INTRODUCTION_GRAY_URL;
        }else{
            introductionUrl = "m=web&a=url&hide_nav=0&ul=" + OTHER_INTRODUCTION_URL;
        }
        result.put("otherIntroductionUrl", introductionUrl);

        return result;
    }

    /**
     * 查询未审核记录（超过1小时未审核）
     *
     * @param expireTime
     * @param status
     * @param limit
     * @return
     */
    public List<AccountsIntroductionLog> queryNotAudit(Long expireTime, Long startTime, Integer status, Integer limit) {
        return accountsIntroductionLogDao.queryNotAudit(expireTime, startTime, status, limit);
    }

    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]自我介绍审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        if (Objects.nonNull(riskSafeHitTypeEnum)) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    auditPass(Long.valueOf(dto.getBizId()), dto.getOperator());
                    break;
                case BLOCK:
                    auditReject(Long.valueOf(dto.getBizId()), dto.getOperator());
                    break;
                default:
                    break;
            }
        }
    }

    public static void adjustIntroduction(Map<String, Object> map) {
        String status = MapUtils.getString(map, PASS_INTRODUCTION_STATUS, "-1");
        String content = MapUtils.getString(map, PASS_INTRODUCTION_CONTENT);
        if (StringUtils.isEmpty(content) && CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString().equals(status)) {
            map.replace(PASS_INTRODUCTION_STATUS, "-1");
        }
    }
}
