package cn.taqu.account.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.taqu.account.common.AliyunTextStatusEnum;
import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.common.RecsysReportServerEnum;
import cn.taqu.account.common.RiskDetectEnum;
import cn.taqu.account.common.RiskSafeHitTypeEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.NickNameRiskVerifyDao;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.dto.ShumeiContentCheckDTO;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.NickNameRiskVerify;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.web.springmvc.JsonResult;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by zhuangyi on 2019/7/23.
 */
@Service
@Slf4j
public class NickNameRiskVerifyService {
    @Autowired
    private NickNameRiskVerifyDao nickNameRiskVerifyDao;
    @Autowired
    private AccountsSetNameLogService accountsSetNameLogService;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private BuryService buryService;
    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private RiskSafeService riskSafeService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    private static List<Integer> IGNORE_LIST = Lists.newArrayList(
        NickNameRiskVerify.Status.AUDITING.getValue(),
        NickNameRiskVerify.Status.REVIEW_90.getValue()
    );

    /**
     * 通过或者拒绝 用户的昵称新增或者修改
     * @param ids
     * @param status
     * @param operator
     * @return
     */
    @Transactional
    public JsonResult passVerify(Long[] ids, Integer status, String operator) {
        return this.passVerify(ids, status, operator, true);
    }

    /**
     * 通过或者拒绝 用户的昵称新增或者修改
     *
     * @param ids    id数组
     * @param status 1通过  2不通过
     * @return
     */
    @Transactional
    public JsonResult passVerify(Long[] ids, Integer status, String operator, Boolean reCheckPass) {
        List<NickNameRiskVerify> nickNameRiskVerifyList = nickNameRiskVerifyDao.findListByIds(ids);
        if (CollectionUtils.isEmpty(nickNameRiskVerifyList)) {
            return JsonResult.success();
        }
        if (status == NickNameRiskVerify.Status.AUDIT_SUCCESS.getValue()) {
            Set<Integer> statusSet = nickNameRiskVerifyList.stream().map(NickNameRiskVerify::getStatus).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(statusSet)){
                throw new ServiceException(CodeStatus.AUDIT_NICKNAME_NOT_EXIST);
            }
            // 是否可以重复审核 不能重复审核就抛异常
            if(!reCheckPass){
                if(CollectionUtils.isNotEmpty(statusSet) && !(statusSet.size() == 1 && statusSet.contains(NickNameRiskVerify.Status.AUDITING.getValue()))){
                    throw new ServiceException(CodeStatus.AUDIT_NICKNAME_NOT_AUDITING);
                }
            }
            //通过的记录进行处理
            nickNameRiskVerifyList.stream().forEach(nickNameRiskVerify -> {
                String accountUuid = nickNameRiskVerify.getAccountUuid();
                String nickname = nickNameRiskVerify.getNewName();
                String accountNameOld = nickNameRiskVerify.getOldName();
                String accountNameNew = nickNameRiskVerify.getNewName();
//                Integer defaultNickName = nickNameRiskVerify.getDefaultNickName();
                if (nickNameRiskVerify.getType().intValue() == 1) {
                    //当为新增的操作时候 直接修改昵称
                    log.info("高风险昵称新增用户审批通过accountuuid:" + accountUuid);
                    accountsManager.setNicknameByUuid(accountNameNew, accountUuid);
                    buryService.updateNicknameIndex(accountUuid, nickname);
                } else if (nickNameRiskVerify.getType().intValue() == 2) {
                    //当为修改的操作时候，需要做一系列操作
                    log.info("高风险昵称修改用户昵称审批通过accountuuid:" + accountUuid);
                    accountsManager.setNicknameByUuid(accountNameNew, accountUuid);
                    accountsService.setNicknameUpdateTime(accountUuid);
                    accountsService.afterSetNicknameByUuid(accountUuid, accountNameOld, accountNameNew, nickNameRiskVerify.getDefaultNickName(), nickNameRiskVerify.getOldDefaultNickName(), nickname);
                }
                Map<String, String> infoHashValue = new HashMap<>();
                infoHashValue.put("account_name_status", "1");//昵称状态 0:未审核 1:已审核
                infoHashValue.put("account_name", accountNameNew);
//                infoHashValue.put(UuidInfoField.ACCOUNT_NAME_DEFAULT, defaultNickName.toString());
                infoHashValue.put("account_name_origin", accountNameNew);
                accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoHashValue);

                // 2020.01.11 通知大数据
                if (AccountActionProfileService.isSystemAccountName(accountNameNew)) {
                    BuryService.pushFilterCommonStatusToRecsys(accountUuid, 1, RecsysReportServerEnum.NICKNAME_UPDATE.value());
                } else {
                    BuryService.pushFilterCommonStatusToRecsys(accountUuid, 0, RecsysReportServerEnum.NICKNAME_UPDATE.value());
                }
            });

            nickNameRiskVerifyDao.passVerify(ids, status, operator);
            return JsonResult.success("操作成功");
        }

        if (status == NickNameRiskVerify.Status.AUDIT_FAIL.getValue()) {
            Set<Integer> statusSet = nickNameRiskVerifyList.stream().map(NickNameRiskVerify::getStatus).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(statusSet)){
                throw new ServiceException(CodeStatus.AUDIT_NICKNAME_NOT_EXIST);
            }
            // 数据包含待审核数据
            if(CollectionUtils.isNotEmpty(statusSet) && statusSet.contains(NickNameRiskVerify.Status.AUDIT_FAIL.getValue())){
                throw new ServiceException(CodeStatus.AUDIT_NICKNAME_ALREADY_AUDIT_FAIL);
            }
            nickNameRiskVerifyDao.passVerify(ids, status, operator);
            nickNameRiskVerifyList.stream().forEach(nickNameRiskVerify -> {
                String name;
                String accountUuid = nickNameRiskVerify.getAccountUuid();
                Integer defaultNickName = CommConst.NO_0;
                Integer oldDefaultNickName = CommConst.NO_0;
                if (AccountActionProfileService.isSystemAccountName(nickNameRiskVerify.getOldName())) {
                    //无最近非违规昵称
                    defaultNickName = CommConst.YES_1;
                    name = accountsThirdPartyService.getRandomNicknameV2(accountUuid);
                }else {
                    //有最近非违规昵称
                    defaultNickName = nickNameRiskVerify.getOldDefaultNickName();
                    name = nickNameRiskVerify.getOldName();
                    oldDefaultNickName = CommConst.NO_0;
                }
                //2020-03-20 拒绝后,数据库也应该一并修改,处理通过后,复审拒绝的用户,数据库由于没更新,后台看到的是更改后的数据,需要一并更新了数据库
                accountsManager.setNicknameByUuid(name, accountUuid);
                accountsSetNameLogService.addLog(accountUuid, nickNameRiskVerify.getNewName(), name, defaultNickName, oldDefaultNickName, 2, operator, 2);
                // 昵称违规
                buryService.toBbsAccountNameViolation(accountUuid);
                buryService.updateNicknameIndex(accountUuid, name);

                Map<String, String> infoHashValue = new HashMap<>();
                infoHashValue.put("account_name_status", "1");//昵称状态 0:未审核 1:已审核
                infoHashValue.put("account_name", name);
//                infoHashValue.put(UuidInfoField.ACCOUNT_NAME_DEFAULT, defaultNickName.toString());
                infoHashValue.put("account_name_origin", name);
                accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoHashValue);
                //去除7天的限制,删除掉redis的key
                accountStringRedisTemplate.delete(RedisKeyConstant.NICKNAME_UPD_TIME.setArg(accountUuid));
                accountsService.setAccountChangeNicknameNum(accountUuid, DateUtil.currentTimeSeconds(), -1);
            });
            return JsonResult.success("操作成功");
        }
        return JsonResult.success("参数异常");
    }

    @Transactional
    public NickNameRiskVerify addVerify(String uuid, String oldName, String newName, Integer defaultNickName, Integer type, Integer status, String operator, String textCheck, Integer appcode, String suggesionCode, String requestId, String responseStr) {
        NickNameRiskVerify oldNickName = nickNameRiskVerifyDao.findByUuid(uuid);
        Integer oldNickNameDefaultNickName = CommConst.NO_0;
        if(oldNickName != null){
            oldNickNameDefaultNickName = oldNickName.getDefaultNickName();
        }
        nickNameRiskVerifyDao.deleteByUuid(uuid);
        NickNameRiskVerify nickNameRiskVerify = new NickNameRiskVerify();
        nickNameRiskVerify.setAccountUuid(uuid);
        nickNameRiskVerify.setOldName(oldName);
        nickNameRiskVerify.setNewName(newName);
        nickNameRiskVerify.setDefaultNickName(defaultNickName == null ? CommConst.NO_0 : defaultNickName);
        nickNameRiskVerify.setOldDefaultNickName(oldNickNameDefaultNickName == null ? CommConst.NO_0 : oldNickNameDefaultNickName);
        nickNameRiskVerify.setUpdateTime(DateUtil.currentTimeSeconds());
        nickNameRiskVerify.setType(type);
        nickNameRiskVerify.setStatus(status);
        nickNameRiskVerify.setOperator(operator);
        nickNameRiskVerify.setSuggestion(textCheck);
        nickNameRiskVerify.setAppcode(appcode);
        nickNameRiskVerify.setSuggestionCode(suggesionCode);
        nickNameRiskVerify.setRequestId(requestId);
        nickNameRiskVerify.setResponseStr(responseStr);
        // 通知审核平台跳过审核
        if (oldNickName != null) {
            riskSafeService.deleteByBizIds(appcode, RiskDetectEnum.TEXT_NICKNAME.name(), operator, Arrays.asList(oldNickName.getId()));
        }
        return nickNameRiskVerifyDao.merge(nickNameRiskVerify);
    }



    /**
     * 昵称复审
     */
    public void review(String accountUuid) {
        NickNameRiskVerify entity = nickNameRiskVerifyDao.findByUuid(accountUuid);
        String suggestion = AliyunTextStatusEnum.BLOCK.name();
        String checkmsg = "";
        Long id = null;
        String requestId = "";
        String responseStr = "";
        if (Objects.isNull(entity)) {
            //该用户不存在审核记录，由历史原因造成，昵称审核是后边才加入的需求
            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
                    accountUuid,
                    new String[]{"account_name", "reg_appcode", "reg_cloned"}
            );
            String nickname = MapUtils.getString(infoMap, "account_name");
            Integer regAppcode = MapUtils.getInteger(infoMap, "reg_appcode", 1);
            Integer regCloned = MapUtils.getInteger(infoMap, "reg_cloned", 1);

            NickNameRiskVerify merge = addVerify(accountUuid, "用户" + accountUuid, nickname, CommConst.NO_0, 1, 3, null, checkmsg, regAppcode, suggestion, requestId, responseStr);
            id = merge.getId();
            try {
                ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accountUuid, nickname, CommConst.OLD_CLIENT_PACKAGE, String.valueOf(merge.getId()), RiskDetectEnum.TEXT_NICKNAME.name(), regAppcode, regCloned);
                suggestion = contentCheckDTO.getSuggestion();
                checkmsg = contentCheckDTO.getRiskLabel1() + contentCheckDTO.getRiskLabel2() + contentCheckDTO.getRiskLabel3() + "";
                requestId = contentCheckDTO.getRequestId();
                responseStr = JSON.toJSONString(contentCheckDTO);
            } catch (Exception e) {
                log.error("调用大保健文本检测失败,用户uuid{}", accountUuid, e);
            }
            merge.setSuggestion(checkmsg);
            merge.setRequestId(requestId);
            merge.setResponseStr(responseStr);
            nickNameRiskVerifyDao.merge(merge);
        } else {
            if (IGNORE_LIST.contains(entity.getStatus())){
                log.info("不操作昵称复审，accountUuid: {}", accountUuid);
                return;
            }
            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(accountUuid, new String[]{"reg_appcode", "reg_cloned"});
            Integer regAppcode = MapUtils.getInteger(infoMap, "reg_appcode", 1);
            Integer regCloned = MapUtils.getInteger(infoMap, "reg_cloned", 1);

            NickNameRiskVerify merge = addVerify(accountUuid, entity.getOldName(), entity.getNewName(), entity.getDefaultNickName(),
                    2, 3, null, checkmsg, entity.getAppcode(), suggestion, requestId, responseStr);
            id = merge.getId();
            try {
                ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accountUuid, entity.getNewName(), CommConst.OLD_CLIENT_PACKAGE, String.valueOf(merge.getId()), RiskDetectEnum.TEXT_NICKNAME.name(), regAppcode, regCloned);
                suggestion = contentCheckDTO.getSuggestion();
                checkmsg = contentCheckDTO.getRiskLabel1() + contentCheckDTO.getRiskLabel2() + contentCheckDTO.getRiskLabel3() + "";
                requestId = contentCheckDTO.getRequestId();
                responseStr = JSON.toJSONString(contentCheckDTO);
            } catch (Exception e) {
                log.error("调用大保健文本检测失败,用户uuid{}", accountUuid, e);
            }
            merge.setSuggestion(checkmsg);
            merge.setRequestId(requestId);
            merge.setResponseStr(responseStr);
            nickNameRiskVerifyDao.merge(merge);
        }

        try {
            if (id == null) {
                log.error("昵称复审失败，id=null");
                return;
            }
            if (Objects.equals(AliyunTextStatusEnum.BLOCK.name(), suggestion)) {
                BuryService.pushNicknameAutoAudit(CommonAuditStatus.AUDIT_FAIL.getStatus(), id, "自动审核不通过");
            } else {
                BuryService.pushNicknameAutoAudit(CommonAuditStatus.AUDIT_SUCCESS.getStatus(), id, "自动审核通过");
            }
        } catch (Exception e) {
           log.error("昵称推入自动审核失败。", e);
        }
    }

    /**
     * 人审回调昵称失败
     *
     * @param accountUuid
     * @param nickname
     */
    public void nicknameCallbackAuditFail(String accountUuid, String nickname) {
        NickNameRiskVerify nickNameRiskVerify = nickNameRiskVerifyDao.findByUuid(accountUuid);
        if(nickNameRiskVerify == null) {
            log.warn("未找用户昵称修改记录，accountUuid={}", accountUuid);
            return;
        }
        Integer status = nickNameRiskVerify.getStatus();
        String newName = nickNameRiskVerify.getNewName();
        // 只有昵称相同、且处于审核通过的昵称，才能人审
        if(Objects.equals(nickname, newName) && Objects.equals(status, NickNameRiskVerify.Status.AUDIT_SUCCESS.getValue())) {
            nickNameRiskVerifyDao.passVerify(nickNameRiskVerify.getId(), NickNameRiskVerify.Status.AUDIT_FAIL.getValue(), "人审不通过");
            String name;
            Integer defaultNickName = CommConst.NO_0;
            if (AccountActionProfileService.isSystemAccountName(nickNameRiskVerify.getOldName())) {
                //无最近非违规昵称
                name = accountsThirdPartyService.getRandomNicknameV2(accountUuid);
                defaultNickName = CommConst.YES_1;
            }else {
                //有最近非违规昵称
                name = nickNameRiskVerify.getOldName();
                defaultNickName = nickNameRiskVerify.getDefaultNickName();
            }
            //2020-03-20 拒绝后,数据库也应该一并修改,处理通过后,复审拒绝的用户,数据库由于没更新,后台看到的是更改后的数据,需要一并更新了数据库
            accountsManager.setNicknameByUuid(name, accountUuid);
            accountsSetNameLogService.addLog(accountUuid, nickNameRiskVerify.getNewName(), name, defaultNickName, CommConst.NO_0, 2, "人审不通过", 2);
            buryService.updateNicknameIndex(accountUuid, name);
            Map<String, String> infoHashValue = new HashMap<>();
            infoHashValue.put("account_name_status", "1");//昵称状态 0:未审核 1:已审核
            infoHashValue.put("account_name", name);
            infoHashValue.put("account_name_origin", name);
            accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoHashValue);
            //去除7天的限制,删除掉redis的key
            accountStringRedisTemplate.delete(RedisKeyConstant.NICKNAME_UPD_TIME.setArg(accountUuid));
            accountsService.setAccountChangeNicknameNum(accountUuid, DateUtil.currentTimeSeconds(), -1);
        }else {
            log.warn("昵称人审不同过，不处理。昵称/状态不符合要求，nickname={}, newName={}, status={}", nickname, newName, status);
        }
    }

    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]昵称审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        if (Objects.nonNull(riskSafeHitTypeEnum)) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    passVerify(new Long[]{Long.valueOf(dto.getBizId())}, NickNameRiskVerify.Status.AUDIT_SUCCESS.getValue(), dto.getOperator(), true);
                    break;
                case BLOCK:
                    passVerify(new Long[]{Long.valueOf(dto.getBizId())}, NickNameRiskVerify.Status.AUDIT_FAIL.getValue(), dto.getOperator(), true);
                    break;
                default:
                    break;
            }
        }
    }
}
