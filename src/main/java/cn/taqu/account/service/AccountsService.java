package cn.taqu.account.service;

import cn.hutool.core.lang.Pair;
import cn.taqu.account.client.mp.MPAccountRequest;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.*;
import cn.taqu.account.constant.h5link.DestroyH5Mapping;
import cn.taqu.account.dao.*;
import cn.taqu.account.dto.InfoFiledCacheDTO;
import cn.taqu.account.dto.RegisterLoginControlIsControlRespDto;
import cn.taqu.account.dto.ShumeiContentCheckDTO;
import cn.taqu.account.etcd.TqUserClient;
import cn.taqu.account.etcd.TqUserGradeClient;
import cn.taqu.account.event.EventId;
import cn.taqu.account.event.EventTrackReporter;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.manager.AccountsDestroyLogManager;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.*;
import cn.taqu.account.service.impl.DestroyServiceImpl;
import cn.taqu.account.soa.AccountFollowSoaService;
import cn.taqu.account.soa.handler.AccountFollowServiceImpl;
import cn.taqu.account.utils.*;
import cn.taqu.account.utils.mq.AntiSpamMqUtil;
import cn.taqu.account.vo.*;
import cn.taqu.account.vo.req.SetDestroyReasonReq;
import cn.taqu.account.vo.resp.DestroyInfoResp;
import cn.taqu.account.vo.resp.DestroyResp;
import cn.taqu.core.common.client.*;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.log.Log;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.*;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taqu.mp.account.client.MPAccountClient;
import com.taqu.mp.account.common.AccountChannelEnum;
import com.taqu.mp.account.constant.CamelCaseBizConstant;
import com.taqu.mp.account.constant.SnakeCaseBizConstant;
import com.taqu.mp.account.dto.AccountLoginDTO;
import com.taqu.mp.account.dto.AccountRegisterDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Transactional
public class AccountsService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.Accounts> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsService.class);
    // 校验昵称正则表达式
//    private static final Pattern NICKNAME_PATTERN = Pattern.compile("^[A-Za-z0-9_\u4e00-\u9fa5]+$");
    private static final Pattern NICKNAME_PATTERN_2 = Pattern.compile("^[A-Za-z0-9_\u3400-\u9fff\ue000-\uf8ff\u20000-\u3fffd]+$");
    // 昵称正则
    private static final Pattern NICKNAME_PURE_WORD_PATTERN = Pattern.compile("^[a-zA-Z]+$");
    //昵称最大长度
    private static final int NICKNAME_MAX_LEN = 16;
    //是否启用昵称登录,true-启用，false禁用
    private static Boolean isLoginByName;
    /**
     * channel最大长度
     */
    public static final Integer CHANNEL_MAX_LEN = 45;

    /**
     * 注销优化支持包
     */
    public static final List<Integer> DESTROY_V3_CLONED = Lists.newArrayList(1, 4, 63);

    //日活跃配置
//    private static ActiveDayConf activeDayConf;

    public static String RISK_APPEAL_LINK;

    public static String RISK_APPEAL_LINK_GRAY;

    @Autowired
    private AccountBaseInfoManager accountBaseInfoManager;
    @Autowired
    private MobilePlaceService mobilePlaceService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;
    @Autowired
    private MembersService membersService;
    @Autowired
    private TicketService ticketService;
    @Autowired
    private MobileService mobileService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private BuryService buryService;
    @Autowired
    @Qualifier("ticketStringRedisTemplate")
    private StringRedisTemplate ticketStringRedisTemplate;
    @Autowired
    @Qualifier("kickStringRedisTemplate")
    private StringRedisTemplate kickStringRedisTemplate;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    private AccountsForumProfileService accountsForumProfileService;
    @Autowired
    private AccountsSetNameLogService accountsSetNameLogService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private AccountsTicketLogService accountsTicketLogService;
    @Autowired
    private AvatarHandleService avatarHandleService;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    private AccountSnListService accountSnListService;
    @Autowired
    private AccountsCertificationService accountsCertificationService;
    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;
    @Autowired
    private AccountsDestroyLogService accountsDestroyLogService;
    @Autowired
    private AccountsAchievementService accountsAchievementService;
    @Autowired
    private AntiSpamMqUtil antiSpamMqUtil;
    @Autowired
    private AccountsCardService accountsCardService;
    @Autowired
    private AntiSpamService antiSpamService;
    @Autowired
    private VerifyRecordService verifyRecordService;
    @Autowired
    private ClickFilterService clickFilterService;
    @Autowired
    private NickNameRiskVerifyService nickNameRiskVerifyService;
    @Autowired
    private NickNameRiskVerifyDao nickNameRiskVerifyDao;
    @Lazy
    @Autowired
    private AccountsGrayListService accountsGrayListService;
    @Autowired
    private ModifyGrowScoreService modifyGrowScoreService;
    @Autowired
    private AccountsDestroyRenewLogService accountsDestroyRenewLogService;
    @Autowired
    private AccountLoginVersionLimitService accountLoginVersionLimitService;
    @Autowired
    private RegisterLimitService registerLimitService;
    @Autowired
    private AccountActionService accountActionService;

    @Autowired
    private AccountsDestroyLogDao accountsDestroyLogDao;
    @Autowired
    private ReRegisterWhitelistDao reRegisterWhitelistDao;
    @Autowired
    private MPAccountClient mpAccountClient;
    @Autowired
    private RiskService riskService;

    @Autowired
    private CityAbTestService cityAbTestService;

    @Autowired
    private AliyunLiveFaceDetectService aliyunLiveFaceDetectService;

    @Autowired
    private AsyncTrackingService asyncTrackingService;

    @Autowired
    private AccountsDestroyLogManager accountsDestroyLogManager;

    @Autowired
    private SoaService soaService;

    @Autowired
    private RealPersonService realPersonService;

    @Value("${default.illegal.avatar}")
    public String DEFAULT_ILLEGAL_AVATAR;
    /**
     * 每天每台设备注册上限数
     */
    @Value("${member.reg.limit.daily}")
    private Integer dailyMemberRegLimit;
    /**
     * 头像默认图片的baseurl
     */
    @Value("${default.avatar.baseurl}")
    public String defaultAvatarBaseurl;
    /**
     * 注册时昵称不能使用的特殊字符
     */
    @Value("${reg.nick.special.char}")
    private String regNickSpecialChar;
    /**
     * 注册成功后，如果有传头像,去除String头像 中的 “https://avatar01.touchcdn.com/”部分
     */
    public static String regReplaceAvatarPart = CommConst.AVATAR_HOST;
    /**
     * 头像默认图片的baseurl 2017.3.9
     */
    public static String baseAvatarUrl = CommConst.AVATAR_HOST + "avatar/";

    @Value("${avatar.url}")
    private String avatarUrl = CommConst.AVATAR_HOST;
    //@Value("${avatar.url}")
    private String imgUrl = "https://forumimg01.jiaoliuqu.com/";

    @Autowired
    private AccountFollowSoaService accountFollowSoaService;
    @Autowired
    private StringRedisTemplate accountBizStringRedisTemplate;
    @Autowired
    private EventTrackReporter eventTrackReporter;

    /**
     * 校验用户登录限制（token）（次数）
     */
    public static int VERIFY_LOGIN_TOKEN_LIMIT_TIMES = 10;
    /**
     * 校验用户登录限制（token）（时间范围 单位：分钟）
     */
    public static int VERIFY_LOGIN_TOKEN_LIMIT_RANGE = 5;
    /**
     * 校验用户登录限制（ip）（次数）
     */
    public static int VERIFY_LOGIN_IP_LIMIT_TIMES = 100;
    /**
     * 校验用户登录限制（ip）（时间范围 单位：分钟）
     */
    public static int VERIFY_LOGIN_IP_LIMIT_RANGE = 5;
    /**
     * 校验用户登录限制（account）（次数）
     */
    public static int VERIFY_LOGIN_ACCOUNT_LIMIT_TIMES = 10;
    /**
     * 校验用户登录限制（account）（时间范围 单位：分钟）
     */
    public static int VERIFY_LOGIN_ACCOUNT_LIMIT_RANGE = 5;

    // 搜索系统账号增量队列名（主）
    public final static String SEARCH_MQ_QUEUE_NAME = "accountDeltaQueue";

    /**
     * 修改昵称次数redis过期 (52个周之前)
     */
    private final static Integer EXPIRED_WEEKS_ACCOUNT_CHANGE_NICKNAME_NUM = 52;

    /**
     * ticket过期时间
     */
    public static Integer TICKET_EXPIRED_DAYS = 60;
    /**
     * 网安要求为活跃天数 绑定手机
     */
    public static Integer WA_BIND_MOBILE_WHERE_NO_ACTIVE_DAYS = 90;

    //在里面的uuid被限制只能登录他趣或者配配其中一种
    private static Map<String, String> uuidLoginLimitMap = Maps.newHashMap();
    @Autowired
    private AccountDestroyReasonDao accountDestroyReasonDao;

    /**
     * 初始化ticket配置
     * @param json
     */
    public static void initAccountTicketConfig(String json){
        try{
            Map<String, String> map = JsonUtils.stringToObject(json, new TypeReference<Map<String, String>>() {
            });
            TICKET_EXPIRED_DAYS = MapUtils.getInteger(map, "ticketExpiredDays", 60);
            WA_BIND_MOBILE_WHERE_NO_ACTIVE_DAYS = MapUtils.getInteger(map, "waBindMobileWhereNoActiveDays", 90);
        }catch (Exception e){
            LOGGER.error("账号ticket配置错误", e);
        }

    }

    public static void initUuidLimitConf(String json) {
        try {
            uuidLoginLimitMap = JsonUtils.stringToObject(json, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            LOGGER.warn("设置用户uuid登录限制相关配置失败,json={}", json, e);
        }
    }

    public static void setNicknameLoginConf(String json) {
        try {
            Map<String, Object> map = JsonUtils.stringToObject(json, new TypeReference<Map<String, Object>>() {
            });
            isLoginByName = MapUtils.getBoolean(map, "on", false);
        } catch (Exception e) {
            LOGGER.warn("设置是否启用昵称登录失败,conf={}", json, e);
        }

    }

    /**
     * 用户退出，根据登录凭证ticket执行退出操作，退出成功返回true；否则返回false
     *
     * @param ticket 登录凭证
     * @return
     * @Title logout
     * <AUTHOR>
     * @Date 2015年9月21日 下午2:25:36
     */
    // TODO 之后要去掉
    @Transactional
    public void logout(String ticket, String token, Integer platformId, Integer appcode, Integer cloned, Boolean isSendIm) {
        logger.info("ticket登出");
        // 根据ticket查询用户信息，通过此来判断用户是否已经登录
        GetByTicketVo vo = accountsInfoService.getByTicket(ticket);
        String accountUuid = vo.getAccount_uuid();
        logoutByAccountUuid(accountUuid, token, platformId, appcode, cloned, isSendIm);
    }

    /**
     * 用户退出，根据登录凭证ticket执行退出操作，退出成功返回true；否则返回false
     *
     * @param
     * @return
     * @Title logout
     * <AUTHOR>
     * @Date 2015年9月21日 下午2:25:36
     */
    @Transactional
    public void logoutByAccountUuid(String accountUuid, String token, Integer platformId, Integer appcode, Integer cloned, Boolean isSendIm) {
        this.logoutCommon(accountUuid, token, platformId, appcode, cloned, LogoutTypeEnum.NORMAL_LOGOUT, Boolean.TRUE);
        if (isSendIm) {
            this.accountLogoutKick(accountUuid, token);
        }
    }

    /**
     * 用户自动退出
     * 脚本操作
     *
     * @param ticket
     */
    @Transactional
    public void autoLogout(String ticket) {
        // 根据ticket查询用户信息，通过此来判断用户是否已经登录
        GetByTicketVo vo = accountsInfoService.getByTicket(ticket);
        String accountUuid = vo.getAccount_uuid();

        // 如果用户实名认证过，就不踢出
        if (accountsInfoService.isZhimaCertification(accountUuid)) {
            LOGGER.info("{}，已实名认证，不踢出", accountUuid);
            return;
        }

        this.logoutCommon(accountUuid, null, null, null, null, LogoutTypeEnum.AUTO_LOGOUT, Boolean.TRUE);
    }

    @Transactional
    public void autoLogoutV2(String accountUuid) {
        // 如果用户实名认证过，就不踢出
        if (accountsInfoService.isZhimaCertification(accountUuid)) {
            LOGGER.info("{}，已实名认证，不踢出", accountUuid);
            return;
        }

        this.logoutCommon(accountUuid, null, null, null, null, LogoutTypeEnum.AUTO_LOGOUT, Boolean.TRUE);
    }

    /**
     * 强制退出
     * 服务端接口
     * @param accountUuid 用户uuid
     */
    @Transactional
    public void forceLogout(String accountUuid) {
        this.logoutCommon(accountUuid, null, null, null, null, LogoutTypeEnum.FORCE_LOGOUT, Boolean.TRUE);
    }

    private void logoutCommon(String accountUuid, String token, Integer platformId, Integer appcode, Integer cloned, LogoutTypeEnum logoutTypeEnum, Boolean isForce) {
        if (!logoutTypeEnum.equals(LogoutTypeEnum.DESTROY) && MPAccountRequest.isRequestMP()) { // 注销时不调用
            SoaBaseParams params = SoaBaseParams.fromThread();
            if (params.getAppcode() == null) {
                params.setAppcode("1");
            }
            if (StringUtils.isBlank(params.getPlatform_name())) {
                params.setPlatform_name("android");
            }
            mpAccountClient.logout(accountUuid, isForce);
        }
        // 用户主动登出
        accountActionService.activeLogout(accountUuid, logoutTypeEnum);

        // 如果token为null，则查找，包括 token，platformId，appcode，cloned
        if(StringUtils.isBlank(token)){
            AccountsMemberInfo accountsMemberInfo = accountsMemberInfoService.getByUuid(accountUuid);
            if (accountsMemberInfo == null) {
                LOGGER.warn("登出账号未查询到用户设备信息，accountUuid={}", accountUuid);
                return;
            }
            token = accountsMemberInfo.getToken();
            platformId = accountsMemberInfo.getPlatformId() == null ? 1 : accountsMemberInfo.getPlatformId();
            appcode = accountsMemberInfo.getAppcode() == null ? 1 : accountsMemberInfo.getAppcode();
            cloned = accountsMemberInfo.getCloned() == null ? 1 : accountsMemberInfo.getCloned();
        }

        //数据画像中心埋点
        buryService.toDataCenter("account", "logout", accountUuid, appcode, null);
        // 用户正常退出 或 注销，才解除推送用户设备关联
        if(Objects.equals(LogoutTypeEnum.NORMAL_LOGOUT, logoutTypeEnum) || Objects.equals(LogoutTypeEnum.DESTROY, logoutTypeEnum)) {
            this.loginoutBury(token, platformId, appcode, accountUuid, "0", cloned);
        }
    }

    /**
     * 用户注销，根据登录凭证ticket执行注销操作
     */
    @Transactional
    public void destroy(String accountUuid, String reason) {
        if (AccountDestroyUtil.hasDestroyFreeze()) {
            logger.info("{}触发注销冷静期", accountUuid);
            destroyByAccountUuidV2(accountUuid, reason, false,false);
        } else {
            logger.info("{}直接注销", accountUuid);
            destroyByAccountUuid(accountUuid, reason, false, false);
        }
    }

    public void relieve(String accountUuid) {
        mpAccountClient.relieve(accountUuid, null, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void destroyV2(String accountUuid, String reason, String mobile, String verify, String deviceToken, String shumeiDeviceId, String continueLogin) {
        Map<String, Object> infoMaps = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{UuidInfoField.IS_BIND_MOBILE, UuidInfoField.MOBILE}, "1", false, false).get(accountUuid);
        if(null == infoMaps){
            throw new ServiceException(CodeStatus.ACCOUNT_INFO_NO_FOUND);
        }
        String isBindMobileStr = StringUtil.nullObjectToEmptyString(infoMaps.get(UuidInfoField.IS_BIND_MOBILE));
        boolean isBindMobile = Objects.equals(CommonEnableStatus.ENABLE.getStatus(), Integer.parseInt(isBindMobileStr));
        // 0.如果ticket 已经绑定手机号，则直接取手机号； 未绑定手机号，则校验一下手机号是否被使用
        if(isBindMobile){
            // 绑定了手机
            // 2025.03.21 历史bug导致j2和j70手机号存在不一致，这里以j70为准
            Map<String, Object> mobileMap = mpAccountClient.getMobileByUuids(Collections.singletonList(accountUuid));
            mobile = StringUtil.nullObjectToEmptyString(mobileMap.get(accountUuid));
            LOGGER.info("账号注销destroyV2,用户已绑定手机,uuid=[{}],reason=[{}],mobile=[{}]", accountUuid, reason, mobile);
        }else{
            LOGGER.warn("账号注销destroyV2,用户未绑定手机,校验手机号uuid=[{}],reason=[{}],mobile=[{}]", accountUuid, reason, mobile);
            // 校验手机号（客户端填写完手机号，发送短信后把手机号删了）
            if (!validateMobilePhone(mobile)) {
                throw new ServiceException(CodeStatus.WRONG_MOBILE_NUMBER_FORMAT);
            }

            // 手输手机号 需要校验一下手机号是否被使用了，0-手机号可用
            if(mobileService.checkMobileStatus(mobile, false) != 0){
                throw new ServiceException(CodeStatus.REGISTER_MOBILE_EXISTS_CHECKED);
            }
        }

        // 1.校验短信
        if(!verifyRecordService.checkVerify(VerifyRecordService.Code.DESTROY_ACCOUNT, mobile, verify)){
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }

        // 2.如果ticket 未绑定手机号 走校绑定逻辑
        if(!isBindMobile){
            mobileService.processBindMobile(accountUuid, null, mobile, deviceToken, shumeiDeviceId, continueLogin, BindMobileTypeEnum.BIND, Boolean.TRUE);
        }

        // 3.走注销逻辑
        if (AccountDestroyUtil.hasDestroyFreeze()) {
            logger.info("{}触发注销冷静期", accountUuid);
            destroyByAccountUuidV2(accountUuid, mobile, reason, false, false); // 这里ticketid先设为空，后续接入账号中台时需修改
        } else {
            destroyByAccountUuid(accountUuid, mobile, reason, false, false); // 这里ticketid先设为空，后续接入账号中台时需修改
        }

    }

    /**
     * @param accountUuid
     * @param verify
     */
    @Transactional(rollbackFor = Exception.class)
    public void destroyV3(String accountUuid, String verify) {
        InfoFiledCacheDTO info = accountBaseInfoManager.getInfoByUuid(accountUuid, new String[]{UuidInfoField.LOGIN_CLONED});
        if (null == info) {
            throw new ServiceException(CodeStatus.ACCOUNT_INFO_NO_FOUND);
        }

        validCloned(info.getLoginCloned());

        // 获取手机号
        String mobile = accountsManager.getMobileByUuid(accountUuid);
        LOGGER.info("账号注销,uuid={}, verify:{}, mobile={}", accountUuid, verify, mobile);
        // 校验短信
        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.DESTROY_ACCOUNT, mobile, verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }
        // 获取注销原因
        String reasonJson = accountBizStringRedisTemplate.opsForValue().get(RedisKeyConstant.DESTROY_REASON.setArg(accountUuid));
        if (StringUtils.isBlank(reasonJson)) {
            logger.warn("注销原因为空:{}", accountUuid);
            throw new ServiceException(CodeStatus.DESTROY_ERROR);
        }

        SetDestroyReasonReq reasonReq = JsonUtils.stringToObject(reasonJson, SetDestroyReasonReq.class);
        String parentContent = DestroyServiceImpl.CONTENT_MAP.get(reasonReq.getParentId());
        AtomicReference<String> reason = new AtomicReference<>("");
        Optional.of(reasonReq).ifPresent(item -> {
            reason.set(item.getContent());
        });

        // 走注销逻辑
        String reportContent;
        if (AccountDestroyUtil.hasDestroyFreeze()) {
            logger.info("{}触发注销冷静期", accountUuid);
            destroyByAccountUuidV2(accountUuid, mobile, parentContent, false, false); // 这里ticketid先设为空，后续接入账号中台时需修改
            reportContent = "成功提交注销申请(进入注销冷静期)";
        } else {
            destroyByAccountUuid(accountUuid, mobile, parentContent, false, false); // 这里ticketid先设为空，后续接入账号中台时需修改
            reportContent = "成功注销";
        }
        // 这里加入日志
        AccountDestroyReason destroyReason = null;
        Long time = DateUtil.currentTimeSeconds();
        destroyReason = new AccountDestroyReason();
        destroyReason.setAccountUuid(accountUuid);
        destroyReason.setReason(reasonReq.getContent());
        destroyReason.setMobileCipher(EncryptUtil.encrypt(mobile));
        destroyReason.setMobileDigest(EncryptUtil.sm3(mobile));
        destroyReason.setParentId(reasonReq.getParentId());
        destroyReason.setChildrenId(Optional.ofNullable(reasonReq.getChildrenId()).orElse(0));
        destroyReason.setReason(parentContent);
        destroyReason.setCreateTime(time);
        destroyReason.setUpdateTime(time);
        accountDestroyReasonDao.merge(destroyReason);
        String token = SoaBaseParams.fromThread().getToken();
        HashMap<String, String> report = Maps.newHashMap();
        report.put("status", reportContent);
        eventTrackReporter.report(token, accountUuid, EventId.DESTROY_MOBILE_TYPE, report);
    }

    /**
     * 校验
     */
    private void validCloned(Integer cloned) {
        if (!DESTROY_V3_CLONED.contains(cloned)) {
            throw new ServiceException(CodeStatus.DESTROY_ERROR);
        }
    }

    /**
     * 摧毁的帐户uuid
     * 用户注销，根据accountUuid执行注销操作
     *
     * @param accountUuid        用户uuid
     * @param reason      原因
     * @param isCheckUuid 检查uuid
     * @param isScript    是脚本
     */
    @Transactional
    public void destroyByAccountUuid(String accountUuid, String reason, boolean isCheckUuid,boolean isScript) {
        Map<String, Object> mobileMap = mpAccountClient.getMobileByUuids(Collections.singletonList(accountUuid));
        String mobile = StringUtil.nullObjectToEmptyString(mobileMap.get(accountUuid));
        destroyByAccountUuid(accountUuid, mobile, reason, isCheckUuid, isScript);
    }

    @Transactional
    public void destroyByAccountUuid(String accountUuid, String mobile, String reason, boolean isCheckUuid,boolean isScript) {
        // 校验用户是否存在
        if (isCheckUuid) {
            Accounts accounts = accountsManager.getByUuid(accountUuid, false);
            if (accounts == null) {
                LOGGER.warn("注销用户不存在，accountUuid={}", accountUuid);
                return;
            }
        }

        Map<String, Object> accountInfo = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[] { UuidInfoField.ACCOUNT_NAME }, "1", false, false).get(accountUuid);

        // 直接注销
        mpAccountClient.deReg(accountUuid, mobile, null, reason, "", false);

        // 更新用户信息及缓存
        String avatar = getAvatarBySexType(accountsManager.getSexTypeByAccountUuid(accountUuid)); // no
        accountsManager.updateDestroyStatus("该用户不存在" + accountUuid, avatar,accountUuid);

        accountsDestroyLogService.addLog(accountUuid, MapUtils.getString(accountInfo, "account_name"), mobile, reason,isScript);

        // bug fixed: remove mobile binding continue login cache
        if (StringUtils.isNotBlank(mobile)) {
            accountStringRedisTemplate.delete(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile));
        }

        //修改手机号验证状态
        accountsInfoDao.update(Sql.build("update accounts_info set is_check_mobile = 0 where account_uuid=?", accountUuid));

        Map<String, String> hashKeys = new HashMap<>();
        hashKeys.put("account_name", "该用户不存在" + accountUuid);
        hashKeys.put("account_name_origin", "该用户不存在" + accountUuid);
        hashKeys.put("account_status", "0");
        hashKeys.put(UuidInfoField.MOBILE, "");
//        hashKeys.put(UuidInfoField.MOBILE_CIPHER, "");
        // 2024.05.27 不记录缓存
//        hashKeys.put(UuidInfoField.IS_BIND_MOBILE, "0");
//        hashKeys.put(UuidInfoField.IS_CHECK_MOBILE, "0");
        hashKeys.put("avatar", avatar);
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), hashKeys);

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        String token = sbp.getToken();
        Integer platformId = sbp.getPlatform_id();
        Integer appcode = sbp.getAppcode();
        Integer cloned = sbp.getCloned();
        this.logoutCommon(accountUuid, token, platformId, appcode, cloned, LogoutTypeEnum.DESTROY, Boolean.TRUE);

        //添加埋点，要处理的东西较多，在埋点中异步处理
        buryService.accountDestroy(accountUuid);

    }

    @Transactional
    public void destroyByAccountUuidV2(String accountUuid, String reason, boolean isCheckUuid,boolean isScript) {
        Map<String, Object> mobileMap = mpAccountClient.getMobileByUuids(Collections.singletonList(accountUuid));
        String mobile = StringUtil.nullObjectToEmptyString(mobileMap.get(accountUuid));
        destroyByAccountUuidV2(accountUuid, mobile, reason, isCheckUuid, isScript);
    }

    @Transactional
    public void destroyByAccountUuidV2(String accountUuid, String mobile, String reason, boolean isCheckUuid,boolean isScript) {
        // 校验用户是否存在
        if (isCheckUuid) {
            Accounts accounts = accountsManager.getByUuid(accountUuid, false);
            if (accounts == null) {
                LOGGER.warn("注销用户不存在，accountUuid={}", accountUuid);
                return;
            }
        }

        // 有14天注销冷静期
        mpAccountClient.deReg(accountUuid, mobile, null, reason, "", true);

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        String token = sbp.getToken();
        Integer platformId = sbp.getPlatform_id();
        Integer appcode = sbp.getAppcode();
        Integer cloned = sbp.getCloned();
        this.logoutCommon(accountUuid, token, platformId, appcode, cloned, LogoutTypeEnum.DESTROY, Boolean.TRUE);
    }

    /**
     * TODO 特别说明：这个需求改动了账号中台SDK的登录方法：com.taqu.mp.account.client.MPAccountClient#login(com.taqu.mp.account.dto.AccountLoginDTO)，PHP迁移时要记得迁移该方法逻辑
     */
    @Transactional
    public void actualDestroyAfterFreeze(String accountUuid) {
        Map<String, Object> accountInfo = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"account_name", UuidInfoField.MOBILE}, "1", false, false).get(accountUuid);

        this.logoutCommon(accountUuid, null, null, null, null, LogoutTypeEnum.DESTROY, Boolean.FALSE);

        // 更新用户信息及缓存
        String avatar = getAvatarBySexType(accountsManager.getSexTypeByAccountUuid(accountUuid)); // no
        accountsManager.updateDestroyStatus("该用户不存在" + accountUuid, avatar,accountUuid);

        String mobile = MapUtils.getString(accountInfo, UuidInfoField.MOBILE);

        accountsDestroyLogService.addLog(accountUuid, MapUtils.getString(accountInfo, "account_name"), mobile, "", false);

        // bug fixed: remove mobile binding continue login cache
        if (StringUtils.isNotBlank(mobile)) {
            accountStringRedisTemplate.delete(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile));
        }

        //修改手机号验证状态
        accountsInfoDao.update(Sql.build("update accounts_info set is_check_mobile = 0 where account_uuid=?", accountUuid));

        Map<String, String> hashKeys = new HashMap<>();
        hashKeys.put("account_name", "该用户不存在" + accountUuid);
        hashKeys.put("account_name_origin", "该用户不存在" + accountUuid);
        hashKeys.put("account_status", "0");
        hashKeys.put(UuidInfoField.MOBILE, "");
//        hashKeys.put(UuidInfoField.MOBILE_CIPHER, "");
        // 2024.05.27 不记录缓存
//        hashKeys.put(UuidInfoField.IS_BIND_MOBILE, "0");
//        hashKeys.put(UuidInfoField.IS_CHECK_MOBILE, "0");
        hashKeys.put("avatar", avatar);
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), hashKeys);

        //添加埋点，要处理的东西较多，在埋点中异步处理
        buryService.accountDestroy(accountUuid);
    }

    /**
     * 用户注销之后的操作
     *
     * @param uuid
     */
    @Transactional
    public void afterDestroy(String uuid) {
        // 解绑业务级实名认证
        accountsCertificationService.cancelCertificationForChat(uuid, true, uuid);
        logger.info("账号{}注销，业务级实名认证信息已解绑", uuid);

        // 解绑实名认证 删除缓存
        accountsCertificationService.cancelCertification(uuid, true, uuid);
        // 2024.02.06 缓存key已经无使用，不需要再设置
//        accountsCertificationService.deleteCertificationCache(uuid);
        logger.info("账号{}注销，实名认证信息已解绑", uuid);
        // 解绑第三方
        accountsThirdPartyService.deBindAll(uuid);
        logger.info("账号{}注销，第三方账号已解绑", uuid);
        // 删除飙车榜排行
//        accountDriverService.removeFromRank(uuid);
        logger.info("账号{}注销，已移出飙车榜", uuid);
        // 注销埋点上报
        HashMap<String, String> report = Maps.newHashMap();
        report.put("status", "成功注销");
        eventTrackReporter.report("", uuid, EventId.DESTROY_MOBILE_TYPE, report);
    }

    /**
     * 根据登录凭证ticket修改用户密码。如果旧密码与新密码相同也当作成功<br/>
     *
     * @param accountUuid  登录凭证
     * @param oldPass 原密码
     * @param newPass 新密码
     * @Title modifyPassword
     * <AUTHOR>
     * @Date 2015年9月21日 下午5:16:45
     */
    @Transactional
    public void modifyPassword(String accountUuid, String oldPass, String newPass) {
        Accounts accounts = accountsManager.getUuidAndPwdByUuid(accountUuid);
        if (accounts == null) {
            throw new ServiceException(CodeStatus.USER_NO_EXISTS);
        }
        if (!oldPass.equals(accounts.getAccount_password())) {
            throw new ServiceException(CodeStatus.OLD_PASS_ERROR);
        }
        if (oldPass.equals(newPass)) {
            throw new ServiceException(CodeStatus.NEW_PWD_NO_CHG);
        }

        if (MPAccountRequest.isRequestMP()) {
            mpAccountClient.modifyPassword(accountUuid, oldPass, newPass);
        }

        accounts.setAccount_password(newPass);
        this.accountsManager.updatePasswordForReset(newPass, accountUuid);
    }

    /**
     * 根据手机短信验证码重置密码
     *
     * @param mobile   手机号
     * @param verify   手机验证码
     * @param password 新密码
     * @Title resetPassword
     * <AUTHOR>
     * @Date 2015年9月23日 下午2:17:42
     */
    @Transactional
    public int resetPassword(String mobile, String verify, String password) {
        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.RESET_PASSWORD, mobile, verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }
        // 通过mobile查询accountUuid，查不到的返回手机号未注册（没有注册也进行重置密码的操作）
        String accountUuid = getUuidByMobile(mobile);

        if (StringUtils.isBlank(accountUuid)) {
            // 未注册的手机号
            return 0;
        }

        if (accountsDestroyLogService.isTimeInValidDestroy(accountsDestroyLogService.getLastValidCreateTime(accountUuid))) {
            throw new ServiceException(CodeStatus.RESET_PWD_MOBILE_DESTROYED);
        }

        if (MPAccountRequest.isRequestMP()) {
            mpAccountClient.resetPassword(mobile, verify, password);
        }

        // 更新新密码到数据库
        accountsManager.updatePasswordForReset(password, accountUuid);
        return 1;
    }

    /**
     * 设置用户密码，只有未设置密码的用户才能进行设置密码，如果密码已设置请使用{@link #modifyPassword}或 {@link #resetPassword}
     *
     * @param accountUuid
     * @param password
     * @return
     * @Title setPassword
     * <AUTHOR>
     * @Date 2015年9月22日 下午5:33:45
     */
    @Transactional
    public void setPassword(String accountUuid, String password) {
//        accountsInfoService.getByTicketIfGuestExpire(ticket);//非注册用户不能操作
        if (MPAccountRequest.isRequestMP()) {
            mpAccountClient.setPassword(accountUuid, password);
        }

        int updateCount = accountsManager.updatePasswordForFirst(password, accountUuid);
        if (updateCount > 0) {
            return;
        }
        throw new ServiceException(CodeStatus.SET_PWD_FAILURE);
    }

    /**
     * 后台根据uuid修改密码，新密码没传时默认为123456
     *
     * @param uuid
     * @param password
     * @return -1 失败， 其他 成功
     */
    @Transactional
    public int modifyPasswordByUuid(String uuid, String password) {
        accountsInfoService.guestTicketExpire(uuid);

        String pwd = encryptPassword(password);
        if (null == pwd) {
            LOGGER.warn("加密密码失败，uuid:{}", uuid);
            return -1;
        }
        if (MPAccountRequest.isRequestMP()) {
            mpAccountClient.changePasswordAdmin(uuid, pwd);
        }

        accountsManager.updatePasswordForReset(pwd, uuid);
        return 0;
    }

    /**
     * 密码加密算法：sha1(md5(trim($password)))
     *
     * @param originPassword 原始密码，如果为空默认为123456
     * @return 加密后的密文字符串
     */
    private String encryptPassword(String originPassword) {
        if (StringUtils.isBlank(originPassword)) {
            originPassword = "123456";
        }
        try {
            return Md5Util.encryptSHA1(Md5Util.encode(originPassword.trim()));
        } catch (Exception e) {
            LOGGER.warn("加密密码失败，原因：{}", e);
            return null;
        }
    }

    /**
     * 将mobile为当前号码的账号的mobile字段更新为空，注意不包含当前账号
     *
     * @param mobile 手机号
     * @param uuid   唯一标识
     * @Title:setMobileNull
     * @author:huangyuehong
     * @Date:2015年9月17日 下午2:01:24
     */
    @Transactional
    public void setMobileNull(String mobile, String uuid) {
        accountsManager.setMobileNullByUuid(mobile, uuid);
    }

    /**
     * 通过账号进行登录,登录成功后返回账号相关的信息
     *
     * @param accountUuid
     * @param version     0或空: 返回全路径; 其他: 返回不带host的uri
     * @return
     * @Title loginById
     * <AUTHOR>
     * @Date 2015年9月29日 下午1:55:02
     */
    @Transactional
    public LoginVo loginById(String newTicket, String accountUuid, int version) {
        if (StringUtils.isBlank(accountUuid)) {
            //数据异常
            LOGGER.error("异常accountUuid:{}", accountUuid);
            throw new ServiceException(CodeStatus.LOGIN_FAILURE);
        }
        // 推广使用账号，不允许登录
        if (StringUtils.equalsAny(accountUuid.trim(), "gc2kqj77774", "xu32l582nrg")) {
            LOGGER.warn("不允许登录的accountUuid:{}", accountUuid);
            throw new ServiceException(CodeStatus.USERNAME_OR_PASSWORD_ERROR);
        }

        // 返回对象
        LoginVo loginVo = new LoginVo();
        // 校验uuid 并返回用户有效
        Accounts accounts = checkAndGetAccount(accountUuid);

        if (accountsDestroyLogService.isAccountHadValidDestroy(accountUuid)) {
            LOGGER.info("用户名或者密码错误场景2");
            throw new ServiceException(CodeStatus.USERNAME_OR_PASSWORD_ERROR);
        }

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        String token = sbp.getToken();
        Long appVersion = sbp.getApp_version();
        Integer platformId = sbp.getPlatform_id();
        String channel = sbp.getChannel();
        Integer appcode = sbp.getAppcode();
        Integer cloned = sbp.getCloned();
        String ip = sbp.getIp();

        HashMap<String,Object> paramsMap =Maps.newHashMap();
        paramsMap.put("accountUuid",accountUuid);
        paramsMap.put("token",token);
        Map<String, Object> blacklistInfo = SoaService.universalBlacklistInfoV3(
            new String[]{BlackListTypeEnum.TOKEN_BLACK_LIST.name(), BlackListTypeEnum.ACCOUNT_BLACK_LIST.name()},paramsMap);

        String defaultCardId = null;
        // 判断用户是否在黑名单
        /*
         * 2024.06.13 以下判断下线
         * 2021.04.26 17-18周社区1组迭代 http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
         * 需求：登录时如果是数美封禁在黑名单内，且未实名，则进行实名认证，认证成功，移除黑名单加入白名单。
         * 备注：需要版本控制
         * 方案：黑名单也登录成功，接口增加参数返回，客户端获取到参数做认证操作。取消则登出
         */
        Map<String, String> blackListInfo = (Map<String, String>) blacklistInfo.get(BlackListTypeEnum.ACCOUNT_BLACK_LIST.toString());
        if (!blackListInfo.isEmpty() && StringUtils.isNotBlank(MapUtils.getString(blackListInfo, "accountUuid"))) {//账号处于黑名单中
            LOGGER.info("登录用户account_uuid:{}处于黑名单列表，登录失败", accountUuid);
            String riskType = MapUtils.getString(blackListInfo, "riskType", null);
            if(defaultCardId == null){
                defaultCardId = getDefaultCardId(accountUuid);
            }
            String content = MapUtils.getString(blacklistInfo, "dialogContent", getInBlacklistContentV2(defaultCardId, riskType));

            // 此处已经抛出异常结束了
            String confirmSchema = MapUtils.getString(blacklistInfo, "relation", getConfirmSchema(defaultCardId, appcode, cloned, 1, token));
            ShowDialogUtil.throwShowDialog(content, "去申诉", confirmSchema, "我知道了", "");
        }

        // 判断用户是否在token黑名单内 2024.06.13
        if(StringUtils.isNotBlank(token) && Boolean.parseBoolean(String.valueOf(blacklistInfo.get(BlackListTypeEnum.TOKEN_BLACK_LIST.toString())))) {
            LOGGER.info("登录token:{}处于黑名单列表，登录失败", token);

            if(defaultCardId == null){
                defaultCardId = getDefaultCardId(accountUuid);
            }
            String content = MapUtils.getString(blacklistInfo, "dialogContent", TOKEN_BLACKLIST_CONTENT);
            String confirmSchema =  MapUtils.getString(blacklistInfo, "relation", getConfirmSchema(defaultCardId, appcode, cloned, 2, token));
            ShowDialogUtil.throwShowDialog(content, "去申诉", confirmSchema, "我知道了", "");
        }

        String oldTicket = accounts.getAccount_ticket();
        loginVo.setOld_ticket(oldTicket);

        // 请求用户中台，不会调用此处
        if (!MPAccountRequest.isRequestMP()) {
            newTicket = ticketService.generateTicket(accounts.getAccount_name());
        }
        accounts.setAccount_ticket(newTicket);
        accounts.setLast_logintime(DateUtil.currentTimeSeconds());
        accounts.setIs_logged_in(1);

        try {
            accountsTicketLogService.addLog(accounts.getUuid(), oldTicket, newTicket, RequestParams.getSoa_basic_java().getToken(), 1, ip, appcode);
        } catch (Exception e) {
            Log.warn("记录用户ticket变化日志出错，account_uuid为：{}，失败原因：{}", accounts.getUuid(), e.getMessage(), e);
        }

        AccountProfileVo profileVo = accountsInfoService.getProfileByAccountUuid(accountUuid);
        Map<String, String> secretConfig = accountsPersonalInfoService.getSecretConfig(accountUuid, null);
        Map<String, String> personalInfo = (Map<String, String>) accountsPersonalInfoService.getPersonalInfo(accountUuid);

        Long memberId = null;
        String oldToken = null;
        boolean isUpdateMemberId = false;
        // 绑定登录设备，不管绑定成功与否，绑定失败时忽略错误，继续往下进行
        try {
            // 根据token查询设备id
            if (token == null || (token = token.trim()).length() == 0) {
                throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
            }

            if (accounts.getMember_id() != null) {
                oldToken = membersService.getTokenById(accounts.getMember_id(), true);
            }

            MembersVo membersVo = membersService.regMembers(token, appVersion, appcode, cloned, platformId, channel);
            memberId = membersVo.getId();
            accounts.setMember_id(memberId);
            //无异常，需要修改redis中用户信息
            isUpdateMemberId = true;
        } catch (Exception e) {
            Log.warn("用户通过ID登录绑定设备失败,用户uuid为:{},失败原因:{}", accountUuid, e.getMessage(), e);
        }
        loginVo.setNickname(accounts.getAccount_name());
        loginVo.setUsername(accounts.getEmail());
        String mobile = accounts.getMobile();
        if (mobile == null || mobile.trim().length() == 0) {
            // 0:手机号为空则未绑定手机
            loginVo.setIs_bind_mobile(0);
        } else {
            loginVo.setIs_bind_mobile(1);
        }

        String waSayToBindMobile = "0";
        try{
            // 1.判断用户是否已经绑定手机
            // 2.是否存在缓存(wa)
            // 3.判断是否90天未登录
            // 4.是的话保存缓存
            if(loginVo.getIs_bind_mobile() == 0){
                String waTaskBindMobileRedisKey = RedisKeyConstant.ACCOUNT_NO_ACTIVE_TASK_WA_BIND_MOBILE.setArg(accountUuid);
                if(accountStringRedisTemplate.hasKey(waTaskBindMobileRedisKey)){
                    waSayToBindMobile = "1";
                }else{
                    // 返回0说明首次进入
                    Long noActiveDays = accountsMemberInfoService.getNoActiveDaysByPrevActiveTime(accountUuid);
                    if(noActiveDays == 0){
                        // 重新去查数据库
                        accountsMemberInfoService.getAccountActiveTimeFromDB(accountUuid, 15);
                        noActiveDays = accountsMemberInfoService.getNoActiveDaysByPrevActiveTime(accountUuid);
                    }
                    LOGGER.info("网安绑定手机.未活跃天数.uuid={} 天数={}", accountUuid, noActiveDays);
                    // 未活跃天数大于等于90天
                    if(WA_BIND_MOBILE_WHERE_NO_ACTIVE_DAYS <= noActiveDays){
                        accountStringRedisTemplate.opsForValue().set(waTaskBindMobileRedisKey, "1", WA_BIND_MOBILE_WHERE_NO_ACTIVE_DAYS, TimeUnit.DAYS);
                        waSayToBindMobile = "1";
                    }
                }
            }
        }catch (Exception e){
            LOGGER.error("网安喊你绑定手机错误", e);
        }
        // 判断用户是否属于风险用户需要完成真人认证
        Pair<Boolean, Integer> riskCert = SoaService.riskNeedLivingCert(accountUuid);
        boolean riskNeedLivingCert = riskCert.getKey(); // 是否需要真人认证
        loginVo.setToRealPersonCertification(riskNeedLivingCert ? 1 : 0);
        loginVo.setToRealPersonCertificationDialog(riskNeedLivingCert ? "检测到您的账号当前存在风险，完成真人认证解除风险" :"");
        loginVo.setRisk_status(riskCert.getValue()); // =2高风险用户，需进去认证页面完成认证校验
        loginVo.setWaSayToBindMobile(waSayToBindMobile);

        loginVo.setIs_check_mobile(profileVo.getIs_check_mobile());
        loginVo.setAccount_level(profileVo.getAccount_level());
        loginVo.setExperience(profileVo.getExperience());
        loginVo.setAccount_actor(profileVo.getAccount_actor());
        loginVo.setMedal_url(profileVo.getMedal_url());
        loginVo.setHonor_name(profileVo.getHonor_name());
        loginVo.setTqcoin(profileVo.getTqcoin());
        loginVo.setTicket_id(newTicket);
        loginVo.setAccount_type(accounts.getAccount_type());
        loginVo.setSex_type(accounts.getSex_type());
        loginVo.setRegister_time(accounts.getCreate_time());
        if (!"1".equals(secretConfig.get(UuidInfoField.AGE_IS_SECRET))) {
            String ageStr = MapUtils.getString(personalInfo, UuidInfoField.AGE);
            if (StringUtils.isNumeric(ageStr)) {
                loginVo.setAge(Integer.valueOf(ageStr));
            }
        }
        loginVo.setAvatar(avatarHandleService.getAvatarByVersion(accounts.getAvatar(), String.valueOf(version)));
        loginVo.setEmail(accounts.getEmail());
        loginVo.setAccount_status(accounts.getAccount_status());
        loginVo.setMobile(mobile);
        loginVo.setUuid(accounts.getUuid());
        loginVo.setDriver_level(profileVo.getDriver_level());
        loginVo.setKilometer(profileVo.getKilometer());

        String aliyunFaceCertification = MapUtils.getString(personalInfo, "face_certification");
        if (StringUtils.isNumeric(aliyunFaceCertification)) {
            loginVo.setAliyunFaceCertification(Integer.valueOf(aliyunFaceCertification));
        }
        String profileVerifyStatus = MapUtils.getString(personalInfo, "pre_profile_verify_status");
        if (StringUtils.isBlank(profileVerifyStatus)) {
            profileVerifyStatus = MapUtils.getString(personalInfo, "profile_verify_status");
        }
        if (StringUtils.isNumeric(profileVerifyStatus)) {
            loginVo.setProfileVerifyStatus(Integer.valueOf(profileVerifyStatus));
        }

        //保存account
        accountsManager.merge(accounts);
        accountsDestroyLogService.invalidByUuid(accountUuid);
        //数据画像中心埋点
        buryService.toDataCenter("account", "login", StringUtils.trimToEmpty(accounts.getUuid()), appcode, null);
        Map<String, String> accountInfoHashValues = new HashMap<>();
        accountInfoHashValues.put("login_appcode", appcode == null ? "1" : appcode.toString());//更新最后登录appcode
        accountInfoHashValues.put("login_cloned", cloned == null ? "1" : cloned.toString());//更新最后登录cloned
        // 2024.05.27 不记录缓存
//        accountInfoHashValues.put(UuidInfoField.IS_CHECK_MOBILE, String.valueOf(loginVo.getIs_check_mobile()));
//        accountInfoHashValues.put(UuidInfoField.IS_BIND_MOBILE, String.valueOf(loginVo.getIs_bind_mobile()));
        if (isUpdateMemberId) {
            accountInfoHashValues.put("member_id", String.valueOf(memberId));//更新用户设备信息
        }
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), accountInfoHashValues);

        accountsMemberInfoService.createOrUpdate(accounts.getUuid(), sbp.getToken(), sbp.getApp_version(), sbp.getAppcode(), sbp.getCloned(), sbp.getChannel(), sbp.getPlatform_id());
        accountsMemberInfoService.updateAppVersionCache(accountUuid, sbp.getApp_version(), sbp.getPlatform_id(), sbp.getChannel());

        if (!Objects.equals(oldToken, token)) {
            this.accountLogoutKick(accountUuid, oldToken);
        }

        // 当A账号被添加为恶意用户后，登录他趣时，均记录其设备号，如果设备号是新的（之前未关联过用户，第一次关联某个用户），也会被添加为限制设备。
        accountsGrayListService.checkTokenJoinList(accountUuid, token);

        // 2021.04.29 // 推入队列半小时后清除Ticket
        if (Objects.equals(loginVo.getToCertification(), CommConst.YES_1)) {
            BuryService.pushAutoLogout(newTicket, token, platformId, appcode, cloned);
        }

        asyncTrackingService.pushUserLoginToActivityKafka(accountUuid,
                SoaBaseParams.fromThread().getAppcode(),
                SoaBaseParams.fromThread().getCloned(),
                SoaBaseParams.fromThread().getToken(),
                SoaBaseParams.fromThread().getPlatform_id());

        return loginVo;
    }

    private CodeStatus getBlackListCodeStatus(){
        Integer cloned=SoaBaseParams.fromThread().getCloned();
        List<String> clonedList=ShanLianEnum.getNvBaoCloneList();
        if(clonedList.contains(String.valueOf(cloned))){
            return CodeStatus.TOKEN_IN_BLACKLIST_V3;
        }
        return CodeStatus.TOKEN_IN_BLACKLIST_V2;
    }

    // 有时效 有原因
    static String BLACKLIST_CONTENT_HOUR_REASON = "您的账号（{0}）由于“{1}”违规，禁用{2}小时，{3}后自动解封。若解封后再次违规将有可能永久封号！";
    // 有时效 无原因
    static String BLACKLIST_CONTENT_HOUR = "您的账号（{0}）因违规禁用{1}小时，{2}后自动解封。若解封后再次违规将有可能永久封号！";
    // 永久 有原因
    static String BLACKLIST_CONTENT_FOREVER_REASON = "您的账号（{0}）由于“{1}”违规已被封禁，暂时无法登录";
    // 永久 无原因
    static String BLACKLIST_CONTENT_FOREVER = "您的账号（{0}）由于违规已被封禁，暂时无法登录";
    // 永久 数美
    static String BLACKLIST_CONTENT_FOREVER_SHUMEI = "您的账号（{0}）存在高风险行为，暂时无法登录";


    // 2024.06.13 新封号文案 https://o15vj1m4ie.feishu.cn/wiki/Ucn5wrf1kiNMqlkKiSzcdhXHnpc
    // 封号 有原因
    static String USER_BLACKLIST_CONTENT_REASON = "您的账号（{0}）由于“{1}”违规已被封禁，无法登录";
    // 封号 无原因
    static String USER_BLACKLIST_CONTENT = "您的账号（{0}）由于违规已被封禁，无法登录";
    // 封设备
    static String TOKEN_BLACKLIST_CONTENT = "该设备过往登录账号存在涉嫌违反平台行为规范，设备已被限制使用";


    /**
     * 黑名单中提示文案
     *
     * @param defaultCardId
     * @param remark
     * @param reason
     * @param unlockTime
     * @param bannedHours
     * @return
     */
    @Deprecated
    private String getInBlacklistContent(String defaultCardId, String remark, String reason, Long unlockTime,
        Long bannedHours) {
//        Map<String, Object> accountInfo = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"default_card_id"}, "1", false, false).get(accountUuid);
//        String defaultCardId = MapUtils.getString(accountInfo, "default_card_id", "");
        Long now = DateUtil.currentTimeSeconds();

        // 默认文案
        String content = null;

        // ******** 文案修改需求文档：https://hb-wiki.yuque.com/staff-er4ql2/atbfuf/vgyawy
        if(unlockTime != null && unlockTime !=-1 && bannedHours != null && bannedHours != -1 && unlockTime > now) {
            // 有时效
            if (StringUtils.isNotBlank(reason) && StringUtils.isNotBlank(remark)) {
                // 有原因
                content = MessageFormat.format(BLACKLIST_CONTENT_HOUR_REASON, defaultCardId, remark, bannedHours, TimeFormatUtil.getCountdownContent(now, unlockTime));
            }else {
                content = MessageFormat.format(BLACKLIST_CONTENT_HOUR, defaultCardId, bannedHours, TimeFormatUtil.getCountdownContent(now, unlockTime));
            }
        }else {
            // 无时效
            if (StringUtils.isNotBlank(reason) && StringUtils.isNotBlank(remark)) {
                // 有原因
                content = MessageFormat.format(BLACKLIST_CONTENT_FOREVER_REASON, defaultCardId, remark);
            }else if(StringUtils.isNotBlank(remark) && remark.contains("数美") && remark.contains("自动封禁")){
                // 数美
                content = MessageFormat.format(BLACKLIST_CONTENT_FOREVER_SHUMEI, defaultCardId);
            } else {
                content = MessageFormat.format(BLACKLIST_CONTENT_FOREVER, defaultCardId);
            }
        }

        return content;
    }

    /**
     * 黑名单中提示文案
     * @param defaultCardId
     * @param riskType
     * @return
     */
    private String getInBlacklistContentV2(String defaultCardId, String riskType) {
        // 默认文案
        String content = null;

        if ( StringUtils.isNotBlank(riskType)) {
            // 有原因
            content = MessageFormat.format(USER_BLACKLIST_CONTENT_REASON, defaultCardId, riskType);
        } else {
            content = MessageFormat.format(USER_BLACKLIST_CONTENT, defaultCardId);
        }

        return content;
    }

    private String getDefaultCardId(String accountUuid){
        Map<String, Object> accountInfo = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"default_card_id"}, "1", false, false).get(accountUuid);
        String defaultCardId = MapUtils.getString(accountInfo, "default_card_id", "");
        return defaultCardId;
    }

    private Accounts checkAndGetAccount(String accountUuid) {
        Accounts accounts = accountsManager.getByUuid(accountUuid);
        if (accounts == null || (accounts.getAccount_status() != null && accounts.getAccount_status() == 0)) {
            LOGGER.warn("登录的用户accountUuid:{}不存在或已注销，登录失败", accountUuid);
            throw new ServiceException(CodeStatus.LOGIN_FAILURE);
        }
        return accounts;
    }


    /**
     * 更新用户一些额外信息未存表（经纬度，最后登录app版本）
     *
     * @param accountUuid
     * @param accountUuid
     * <AUTHOR>
     * @date 2020/03/05 15:44
     */
    public void updateAccountExtraInfo(String accountUuid) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }
        Map<String, String> accountInfoHashValues = new HashMap<>();

        String longitude = RequestParams.getSoa_basic_java().getLongitude();
        String latitude = RequestParams.getSoa_basic_java().getLatitude();
        if (NumberUtils.isCreatable(longitude) && NumberUtils.isCreatable(latitude)) {
            accountInfoHashValues.put("longitude", longitude);
            accountInfoHashValues.put("latitude", latitude);
        }
        Long appVersion = RequestParams.getSoa_basic_java().getApp_version();
        if (appVersion != null) {
            accountInfoHashValues.put("last_app_version", String.valueOf(appVersion));
            accountInfoHashValues.put("last_active_app_version", String.valueOf(appVersion));
        }
        Integer platform_id = RequestParams.getSoa_basic_java().getPlatform_id();
        if (platform_id != null) {
            accountInfoHashValues.put("last_platform_id", String.valueOf(platform_id));
        }

        updateAccountInfosRedis(accountUuid, accountInfoHashValues);
    }

    /**
     * 更新用户坐标
     *
     * @param accountUuid
     * <AUTHOR>
     * @date 2020/03/05 15:44
     */
    public void updateAccountPosition(String accountUuid) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }
        String longitude = RequestParams.getSoa_basic_java().getLongitude();
        String latitude = RequestParams.getSoa_basic_java().getLatitude();
        if (NumberUtils.isCreatable(longitude) && NumberUtils.isCreatable(latitude)) {
            Map<String, String> accountInfoHashValues = new HashMap<>();
            accountInfoHashValues.put("longitude", longitude);
            accountInfoHashValues.put("latitude", latitude);
            accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), accountInfoHashValues);
        }
    }

    public void updateAccountInfosRedis(String accountUuid, Map<String, String> accountInfoHashValues) {
        if (StringUtils.isBlank(accountUuid) || accountInfoHashValues == null || accountInfoHashValues.isEmpty()) {
            return;
        }
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), accountInfoHashValues);
    }


    /**
     * 根据用户名、密码进行登录。用户名可以是账号名称、邮箱号、手机号
     *
     * @param username
     * @param password
     * @param version  0或空: 返回全路径; 其他: 返回不带host的uri
     * @return 登录成功后返回账号信息{@link LoginVo}，否则返回null
     * @Title login
     * <AUTHOR>
     * @Date 2015年9月30日 上午9:27:35
     */
    @Transactional
    public LoginVo login(String username, String password, int version, String smid, String extraJson) {
        String token = SoaBaseParams.fromThread().getToken();

        if (StringUtils.isBlank(username) || StringUtil.hasUcs4(username)) {
            throw new ServiceException("invalid_username", "用户名只能是11位手机号或中英文组成的昵称");
        }
        this.checkLogin(username);

        String accountUuid = null;

//        LOGGER.info("登录开关={}",isLoginByName);
        if (isLoginByName) {
            // 测试环境使用
            Accounts accounts = accountsManager.getUuidAndPwdByName(username);
            if(accounts != null) {
                accountUuid = accounts.getUuid();
            }
        }

        if(StringUtils.isBlank(accountUuid)) {
            if (!validateMobilePhone(username)) {
                throw new ServiceException(CodeStatus.WRONG_MOBILE_NUMBER_FORMAT);
            }
            accountUuid = getUuidByMobile(username);
        }

        if (StringUtils.isBlank(accountUuid)) {
            logger.warn("账号密码尝试登录不存在的账号");
            throw new ServiceException(CodeStatus.USERNAME_OR_PASSWORD_ERROR);
        }

        String mobile = getMobileByUuid(accountUuid);

        antiSpamService.checkPasswordLogin(accountUuid, mobile, token);

//        if (!Objects.equals(password, accounts.getAccount_password())) {
//            //android密码错误上报到反垃圾系统
//            if (Objects.equals(SoaBaseParams.fromThread().getPlatform_id(), 1)) {
//                antiSpamService.loginPwdErrorReport(accountsUuid);
//            }
//            LOGGER.info("用户名或者密码错误场景1");
//            throw new ServiceException(CodeStatus.USERNAME_OR_PASSWORD_ERROR);
//        }

        String ticket = "";
        int freezeFlag = 0;
        if (MPAccountRequest.isRequestMP()) {
            AccountLoginDTO loginDTO;
            Map<String, Object> login;

            // 若开启昵称登录，且输入的是昵称，则走uuid密码登录，反之，输入的是手机号，则还是走phone密码登录
            if (isLoginByName && !validateMobilePhone(username)) {
                loginDTO = AccountLoginDTO.builder()
                        .loginType(AccountChannelEnum.UUID_PASSWORD.value())
                        .uuid(accountUuid)
                        .password(password)
                        .smDeviceId(smid)
                        .extraJson(extraJson)
                        .build();
                login = mpAccountClient.login(loginDTO);
            } else { // 没开启昵称登录，则走phone密码登录
                loginDTO = AccountLoginDTO.builder()
                        .loginType(AccountChannelEnum.PHONE_PASSWORD.value())
                        .userName(username)
                        .password(password)
                        .smDeviceId(smid)
                        .extraJson(extraJson)
                        .build();
                login = mpAccountClient.login(loginDTO);

                accountUuid = MapUtils.getString(login, CamelCaseBizConstant.UUID);
            }
            ticket = MapUtils.getString(login, CamelCaseBizConstant.TICKET);

            if (login.containsKey(CamelCaseBizConstant.FREEZE_FLAG) &&
                    Boolean.parseBoolean(MapUtils.getString(login, CamelCaseBizConstant.FREEZE_FLAG))) {
                freezeFlag = 1;
            }
        }

        LoginVo loginVo = this.loginById(ticket, accountUuid, version);
        loginVo.setFreeze_flag(freezeFlag);
        return loginVo;  // login by password
    }

    public static boolean validateMobilePhone(String number) {
        Pattern pattern = Pattern.compile("^[1]\\d{10}$");
        return pattern.matcher(number).matches();
    }


    public void checkLogin(String username) {
        //2016.10.14 加入设备、ip、账号访问时间次数校验
        //初步设计，在五分钟内 同一个token只能尝试10次   同一个IP只能尝试10次 同一个用户只能尝试10次
        String ip = RequestParams.getSoa_basic_java().getIp();
        String token = RequestParams.getSoa_basic_java().getToken();
        //校验username
        if (StringUtils.isNotBlank(username)) {
            String key = RedisKeyConstant.VERIFY_LOGIN_ACCOUNT.setArg(username);
            this.verifyLoginTimes(key, VERIFY_LOGIN_ACCOUNT_LIMIT_RANGE, VERIFY_LOGIN_ACCOUNT_LIMIT_TIMES, username, "用户限制");
        }
        //校验ip
        if (StringUtils.isNotBlank(ip)) {
            String key = RedisKeyConstant.VERIFY_LOGIN_IP.setArg(ip);
            this.verifyLoginTimes(key, VERIFY_LOGIN_IP_LIMIT_RANGE, VERIFY_LOGIN_IP_LIMIT_TIMES, ip, "ip限制");
        }
        //校验token
        if (StringUtils.isNotBlank(token)) {
            String key = RedisKeyConstant.VERIFY_LOGIN_TOKEN.setArg(token);
            this.verifyLoginTimes(key, VERIFY_LOGIN_TOKEN_LIMIT_RANGE, VERIFY_LOGIN_TOKEN_LIMIT_TIMES, token, "token限制");
        }
    }

    /**
     * 校验登录次数
     *
     * @param key        reids的key
     * @param limitRange 时间范围
     * @param limitTimes 次数
     * @param type       类型字符串
     * <AUTHOR>
     * 2016年11月1日 上午10:14:18
     */
    private void verifyLoginTimes(String key, int limitRange, int limitTimes, String subject, String type) {
        String timesStr = (String) accountStringRedisTemplate.opsForHash().get(key, "times");
        if (timesStr == null) {
            accountStringRedisTemplate.opsForHash().put(key, "times", "1");
            accountStringRedisTemplate.expire(key, limitRange, TimeUnit.MINUTES);
        } else {
            long times = Long.parseLong(timesStr);
            //如果次数等于限制次数
            if (times >= limitTimes) {
                logger.warn("[{}]登录次数已达到{}, {}分钟内允许登录的次数为:{}", subject, type, limitRange, limitTimes);
                throw new ServiceException(CodeStatus.LOGIN_TIMES_OVER_LIMIT);
            } else {
                accountStringRedisTemplate.opsForHash().increment(key, "times", 1L);
            }
            if (Objects.equals(accountStringRedisTemplate.getExpire(key), -1L)) {
                accountStringRedisTemplate.expire(key, limitRange, TimeUnit.MINUTES);
            }
        }
    }

    /**
     * @param mobile
     * @param nickname
     * @param password
     * @param token
     * @param platformId
     * @param appcode
     * @param appVersion
     * @param channe
     * @param sexType
     * @param avatar
     * @param version    0或空: 返回全路径; 其他: 返回不带host的uri
     * @return
     */
    @Transactional
    public RegVo mobileReg(String mobile, String nickname, String password,
                           String token, Integer platformId, Integer appcode,
                           Integer cloned, Long appVersion, String channe,
                           Integer sexType, String avatar, int version, Long birth,
                           Integer defaultNickName, String smid,
                           String extraJson) {
        if (Objects.equals(avatar, "null")) {
            avatar = "";
        }


        // 2020.03.08 注册头像强制上传，
        // 2024.04.18 客户端上传的可能是默认头像，也有值
        if (StringUtils.isBlank(avatar)) {
            throw new ServiceException(CodeStatus.NEED_AVATAR);
        }

        try {
            if (StringUtils.isNotBlank(avatar)) {
                if (!avatar.startsWith("http")) {
                    avatar = getAvatarUrlPrefix() + StringUtils.removeStart(avatar, "/");
                }
            }

            String isVerifyKey = RedisKeyConstant.IS_REG_VERIFY.setArg(mobile);
            String verifyResult = accountStringRedisTemplate.opsForValue().get(isVerifyKey);
            RegStyle regStyle = null;
            //注册方式
            if (StringUtils.isNotBlank(verifyResult)) {
                regStyle = RegStyle.getByName(verifyResult, RegStyle.Mobile);
            }
            //再注册检查
            reregistrationCheck(regStyle, mobile);

            if (regStyle == null) {//注册验证码校验过后在指定时间内还没有完善资料，则要求用户返回重新输入验证码
                throw new ServiceException(CodeStatus.REG_VERIFY_EXPIRE);
            }

            //判断是否有效点击
            if (!clickFilterService.isValidClick("reg", token)) {
                throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
            }

            registerLimit(ActionModeEnum.TAQU.value(), mobile, null);

            antiSpamService.antiFraud("", AccountActionTypeEnum.REGISTER, sexType);

            // 2023.10.20 用户注册渠道，传给用户中台，默认验证码
            int accountChannel = AccountChannelEnum.PHONE_CODE.value();
            if(regStyle == RegStyle.Flash) {
                accountChannel = AccountChannelEnum.FLASH.value();
            }

            AccountRegisterDTO registerDTO = AccountRegisterDTO.builder()
                    .regType(accountChannel)
                    .phone(mobile)
                    .password(password)
                    .smDeviceId(smid)
                    .extraJson(extraJson)
                    .build();

            RegVo regVo = this.reg(mobile, nickname, password, token, platformId, appcode, cloned, appVersion, channe, sexType, avatar, RegStyle.getByName(verifyResult, RegStyle.Mobile), version, birth, defaultNickName, registerDTO, smid, false);
            this.registerBury(appcode, regVo);
            if (regStyle == RegStyle.Flash) {
                regVo.setRegisterLoginTypeEnumType(RegisterLoginTypeEnum.QUICK);
                logger.info("[flashsdk] 闪验手动注册成功");
            } else {
                regVo.setRegisterLoginTypeEnumType(RegisterLoginTypeEnum.VERIFICATION_CODE);
                logger.info("[mobile] 手机验证码注册成功");

                // 将手机验证码注册的手机号、uuid、注册时间、验证码缓存3小时，给风控接码平台校验
                // key = 验证码， value = 手机号|验证码时间|uuid|注册时间
                // @link cn.taqu.account.controller.api.jrpc.MobileController.captchaPlatformVerify
                String mobileRegKey = RedisKeyConstant.MOBILE_REG_VERIFY.setArg(mobile);
                String verifyCodeAndTime = accountStringRedisTemplate.opsForValue().get(mobileRegKey);

                logger.info("mobileRegKey: {}, verifyCodeAndTime: {}", mobileRegKey, verifyCodeAndTime);
                if (StringUtils.isNotBlank(verifyCodeAndTime)) {
                    String verifyCode = verifyCodeAndTime.split("\\|")[0];
                    String time = verifyCodeAndTime.split("\\|")[1];

                    String verifyRegKey = RedisKeyConstant.MOBILE_REG_SUCCESS.setArg(verifyCode);
                    accountStringRedisTemplate.opsForList().rightPush(verifyRegKey, mobile + "|" + time + "|" + regVo.getUuid() + "|" + regVo.getCreate_time());
                    accountStringRedisTemplate.expire(verifyRegKey, 3, TimeUnit.HOURS);
                }

            }
            return regVo;
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            logger.error("用户注册异常", e);
        }
        throw new ServiceException(CodeStatus.REG_FAILURE);
    }

    /**
     * 从闪验获取手机号 v2
     *
     * @param accessToken 运营商token
     * @return
     */
    @Transactional
    public LoginVo quickLoginV2(String accessToken, String smid, String extraJson) {
        AccountLoginDTO loginDTO = AccountLoginDTO.builder()
                .loginType(AccountChannelEnum.FLASH.value())
                .accToken(accessToken)
                .smDeviceId(smid)
                .extraJson(extraJson)
                .build();

        Map<String, Object> login = mpAccountClient.login(loginDTO);

        boolean needRegister = "1".equals(MapUtils.getString(login, SnakeCaseBizConstant.NEED_REGISTER));

        String uuid = "";
        String ticket = "";
        int freezeFlag = 0;
        if (!needRegister) {
            uuid = MapUtils.getString(login, CamelCaseBizConstant.UUID);
            ticket = MapUtils.getString(login, CamelCaseBizConstant.TICKET);
            if (login.containsKey(CamelCaseBizConstant.FREEZE_FLAG) &&
                    Boolean.parseBoolean(MapUtils.getString(login, CamelCaseBizConstant.FREEZE_FLAG))) {
                freezeFlag = 1;
            }
        }
//        String mobile = login.get(CamelCaseBizConstant.PHONE);
        String mobile = MapUtils.getString(login, CamelCaseBizConstant.PHONE);

        if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
            logger.warn("闪验返回手机号:{} 格式错误, accessToken:{}", mobile, accessToken);
            throw new ServiceException(CodeStatus.FLASH_MOBILE_ERROR);
        }
        LoginVo loginVo = this.autoLoginOrReg(uuid, mobile, ticket, RegStyle.Flash, "");
        loginVo.setFreeze_flag(freezeFlag);
        return loginVo;
    }

    /**
     * 通过验证码实现自动注册登录
     *
     * @param mobile     手机号
     * @param verifyCode 验证码
     * @return
     */
    @Transactional
    public LoginVo autoLogin(String mobile, String verifyCode, String smid, String extraJson) {
        AccountLoginDTO loginDTO = AccountLoginDTO.builder()
                .loginType(AccountChannelEnum.PHONE_CODE.value())
                .phone(mobile)
                .verifyCode(Integer.valueOf(verifyCode))
                .smDeviceId(smid)
                .extraJson(extraJson)
                .build();
        Map<String, Object> login = mpAccountClient.login(loginDTO);

        boolean needRegister = "1".equals(login.get(SnakeCaseBizConstant.NEED_REGISTER));
        String uuid = "";
        String ticket = "";
        int freezeFlag = 0;
        if (!needRegister) {
            uuid = MapUtils.getString(login, CamelCaseBizConstant.UUID);
            ticket = MapUtils.getString(login, CamelCaseBizConstant.TICKET);
            if (login.containsKey(CamelCaseBizConstant.FREEZE_FLAG) &&
                    Boolean.parseBoolean(MapUtils.getString(login, CamelCaseBizConstant.FREEZE_FLAG))) {
                freezeFlag = 1;
            }
        }

        LoginVo loginVo = this.autoLoginOrReg(uuid, mobile, ticket, RegStyle.Mobile, verifyCode);
        loginVo.setFreeze_flag(freezeFlag);
        return loginVo;
    }

    /**
     * 登录或注册，已注册过则登录，未注册过则自动注册
     *
     * @param mobile 手机号
     * @return
     */
    private LoginVo autoLoginOrReg(String uuid, String mobile, String ticket, RegStyle regStyle, String verifyCode) {
        if (StringUtils.isNotBlank(uuid)) {
            if (accountsDestroyLogService.isAccountHadValidDestroy(uuid)) {
                throw new ServiceException(CodeStatus.MOBILE_DESTROYED);
            }
            LoginVo loginVo = this.loginById(ticket, uuid, 1); // login by flash and message verify
            if (regStyle == RegStyle.Flash) {
                logger.info("闪验自动登录成功");
            }
            return loginVo;
        }

        // uuid为空，走注册流程
        registerLimit(ActionModeEnum.TAQU.value(), mobile, null);
        if (riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Mobile.getValue(), mobile)) {
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }
        //用户未注册并且在不自动注册的情况下，返回is_bind_mobile=0
        LoginVo loginVo = new LoginVo();
        loginVo.setIs_bind_mobile(0);
        loginVo.setMobile(mobile);
        mobileService.setRegVerify(mobile, regStyle);
        if (regStyle == RegStyle.Flash) {
            logger.info("[flashsdk] 跳转人工注册");
        }
        if (regStyle == RegStyle.Mobile) {
            mobileService.setMobileRegVerify(mobile, verifyCode, DateUtil.currentTimeSeconds());
            logger.info("cache phone: {} register verify code: {}", mobile, verifyCode);
        }
        return loginVo;
    }

    /**
     * 判断游客是否需要绑定手机号
     *
     * @param mobile
     * @return
     */
    public Boolean isNeedVisitorBindMobile(String regType, String mobile) {
        //游客才需要走判断流程,如果未绑定手机号,则进行绑定手机号
        Boolean need = false;
//        if (AccountsThirdParty.VISITOR.equals(regType)) {
//            String uuid = getUuidByMobile(mobile);
//            if (StringUtils.isBlank(uuid)) {
//                need = true;
//            }
//        }
        return need;
    }

    public String getMobileByUuid(String uuid) {
        return accountsManager.getMobileByUuid(uuid);
    }

    public String getUuidByMobile(String mobile) {
        return accountsManager.getUuidByMobile(mobile);
    }

    /**
     * 用户注册
     *
     * @param mobile     用户名
     * @param nickname   昵称
     * @param password   密码
     * @param token      设备token
     * @param platformId 平台码1:android,2:iphone,3:ipad
     * @param appcode    应用的类型码1:性价比;2:蓝色妖姬等
     * @param appVersion 应用版本号
     * @param channel    应用渠道码 例如tongbu,91zhushou
     * @param sexType    性别1:男;2:女;0:未知;默认为0
     * @param avatar     图片(没有则从16张默认图片中取)
     * @param isThird
     * @Title reg
     * <AUTHOR>
     * @Date 2015年9月30日 上午10:39:19
     */
    @Transactional
    public RegVo reg(String mobile, String nickname, String password,
                     String token, Integer platformId, Integer appcode,
                     Integer cloned, Long appVersion, String channel,
                     Integer sexType, String avatar, RegStyle regStyle,
                     int version, Long birth,
                     Integer defaultNickName, AccountRegisterDTO registerDTO,
                     String smid, boolean isThird) {
        logger.debug("[reg] mobile:{}，sexType:{}", mobile, sexType);
        // 注册校验设备黑名单 2021.04.25
        HashMap<String, Object> paramsMap = Maps.newHashMap();
        paramsMap.put("token", token);
        boolean inTokenBlackList = Boolean.parseBoolean(String.valueOf(SoaService.universalBlacklistInfoV3(
            new String[]{BlackListTypeEnum.TOKEN_BLACK_LIST.name()}, paramsMap).get(BlackListTypeEnum.TOKEN_BLACK_LIST.toString())));
        if (StringUtils.isNotBlank(token) && inTokenBlackList) {
            CodeStatus codeStatus=getBlackListCodeStatus();
            throw new ServiceException(codeStatus);
//            throw new ServiceException(CodeStatus.TOKEN_IN_BLACKLIST_V2);
        }

        //非第三方注册，用户名只能为手机号
        if (!isThird && !ValidateUtil.isMobile(mobile)) {
            throw new ServiceException(CodeStatus.USERNAME_INVALIDATE);
        }

        //旧版如果没有传昵称，则设置一个随机昵称
        if (StringUtils.isEmpty(nickname)) {
            nickname = accountsThirdPartyService.getRandomNickname(sexType);
        }

        CodeStatus nicknameCode = this.checkNickname(nickname);
        if (nicknameCode != null) {
            throw new ServiceException(nicknameCode);
        }

        MembersVo membersVo;
        try {
            // 注册设备
            membersVo = membersService.regMembers(token, appVersion, appcode, cloned, platformId, channel);
        } catch (Exception e) {
            throw new ServiceException(CodeStatus.REGISTER_MEMBER_FAILURE);
        }

        // 是否达到注册次数上限
        if (this.dailyMemberRegLimit != null) {
            long regCount = accountsManager.getCountByMemberId(membersVo.getId(), DateUtil.getTodayBeginSecond(), DateUtil.getTodayEndSecond());
            if (regCount >= this.dailyMemberRegLimit) {
                throw new ServiceException(CodeStatus.REG_TOO_MANY_TODAY);
            }
        }

        //登录和注册的版本限制，注册时用户uuid传空
        loginVersionLimit("", platformId, appVersion);

        RegVo regVo = this.regAccounts(mobile, nickname, password, sexType, avatar, regStyle, membersVo, version, birth, defaultNickName, registerDTO, smid);
        BuryService.regBuryToAdmp(regVo.getUuid(), regVo.getSex_type(), regVo.getCreate_time(), regStyle, birth, regVo.getIsSetAvatar());
        modifyGrowScoreService.sendModifyGrowScoreMq(regVo.getUuid(), ModifyGrowScoreEnum.REGISTER, regStyle);
        buryService.toBbsFinishIncentiveTask(regVo.getUuid(), FinishIncentiveTaskEnum.NEWBIE_REGISTER.getType());
        // 风控埋点 注册推送
        buryService.toAntispam(token, System.currentTimeMillis() / 1000, platformId, "register");

        // 城市AB实验入组上报
//        cityAbTestService.regReport(regVo.getUuid());

        // ******** 替换为上报位置信息
        SpringContextHolder.getBean(TqUserClient.class).reportCity(regVo.getUuid(), regVo.getCityId(), sexType);

        // 用户注册标签实时同步  2025.1.2 放到更前面调用
//        SoaService.userLabelSync(regVo.getUuid(), regVo.getSex_type(), regVo.getCreate_time(), regVo.getCityId());

        buryService.pushToAccountRegisterQueue(regVo.getUuid(), regVo.getSex_type(), channel, appVersion, platformId, appcode, cloned);

        return regVo;
    }

    /**
     * 注册成功后添加到埋点
     *
     * @param appcode
     * @param regVo
     */
    public void registerBury(Integer appcode, RegVo regVo) {
        //数据画像中心埋点
        buryService.toDataCenter("account", "reg", regVo.getUuid(), appcode, null);
    }

    private AccountsForumProfile setAccountForumForReg(Accounts accounts) {
        return accountsForumProfileService.addByAccountUuid(accounts.getUuid(), accounts.getAccount_id());
    }

    /**
     * 获取gdk编码字节数，一个汉字算两个字节
     *
     * @param value
     * @return
     */
    private int getGbkLen(String value) {
        byte[] bytes = Encodes.decodeCharset(value, "GBK");
        return bytes.length;
    }

    /**
     * 昵称格式校验
     *
     * @param nickname
     * @return
     */
    private CodeStatus nicknameCheckFormats(String nickname) {
        if (StringUtils.isBlank(nickname)) {
            return CodeStatus.NICKNAME_INVALID;
        }

        // 不能大于最大昵称长度
        if (this.getGbkLen(nickname) > NICKNAME_MAX_LEN) {
            return CodeStatus.NICKNAME_INVALID;
        }

        // 判断是否为正确的昵称(只能由字母、数字、下划线组成,且不能为纯数字)
        if (ValidateUtil.isInteger(nickname) || !NICKNAME_PATTERN_2.matcher(nickname).matches() || NICKNAME_PURE_WORD_PATTERN.matcher(nickname).matches()) {
            return CodeStatus.NICKNAME_INVALID;
        }

        // 不能含有特殊字符
        if (regNickSpecialChar != null && regNickSpecialChar.trim().length() > 0) {
            char[] specialChars = regNickSpecialChar.trim().toCharArray();
            char[] nickChars = nickname.toCharArray();
            for (char spec : specialChars) {
                for (char nick : nickChars) {
                    if (nick == spec) {
                        return CodeStatus.NICKNAME_INVALID;
                    }
                }
            }
        }

        // 不能包含敏感词（20240118接入统一风控安全，此处不需要调用了）
//        if (SoaService.isInSensitiveWords(nickname, "NICKNAME_BLOCK")) {
//            return CodeStatus.NICKNAME_SENSITIVE_WORDS;
//        }

        return null;
    }

    /**
     * 校验昵称格式，校验通过返回null,否则返回{@link CodeStatus}中的具体错误
     * 2020.06.10 昵称可重复
     *
     * @param nickname
     * @return
     * @Title checkNickname
     * <AUTHOR>
     * @Date 2015年10月8日 下午2:39:36
     */
    private CodeStatus checkNickname(String nickname) {
        CodeStatus codeStatus = this.nicknameCheckFormats(nickname);
        if (codeStatus != null) {
            return codeStatus;
        }

        String resultCode = getResultCodeByCheckNickname(nickname);

        if (!Objects.equals(resultCode, "N") && !Objects.equals(resultCode, "C")
                && !Objects.equals(resultCode, "B")) {
            return CodeStatus.ACCOUNT_NAME_EXISTS;
        }

        return null;
    }

    public String getResultCodeByCheckNickname(String nickname) {
        // 如果是默认昵称，都返回没有重复
        if (AccountActionProfileService.isSystemAccountName(nickname)) {
            return "N";
        }

        List<String> list = accountsManager.listUuidByAccountName(nickname);
        // 长度大于 100 只取100
        if (list != null && list.size() > 100) {
            List<String> subList = list.subList(0, 100);
            Map<String, Object> map = checkNicknameDuplication(nickname, subList);
            String resultCode = MapUtils.getString(map, "result_code", "N");
            return resultCode;
        } else {
            Map<String, Object> map = checkNicknameDuplication(nickname, list);
            String resultCode = MapUtils.getString(map, "result_code", "N");
            return resultCode;
        }
    }

    /**
     * http://doc.internal.taqu.cn/pages/viewpage.action?pageId=********
     * <p>
     * 返回 {"result_code":"码","msg":"消息"}
     * result_code:
     * N:没有重复的昵称
     * A:与后台设置的保留昵称重复
     * B:与土豪的昵称重复
     * C:与普通用户的昵称重复
     *
     * @param accountUuid
     * @param nameList
     * @return
     * <AUTHOR>
     * @date 2020/06/12 10:06
     */
    private Map<String, Object> checkNicknameDuplication(String accountUuid, List<String> nameList) {
        Object[] form = {
                accountUuid, nameList
        };
        SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.PRIVILEGE_SYSTEM);
        SoaResponse response = soaClient.call("account", "checkNicknameDuplication", form);
        if (response.fail()) {
            LoggerUtils.soaRequestFail("[privilege]", "account", "checkNicknameDuplication", response.getCode(), response.getMsg());
        }
        Map<String, Object> result = Maps.newHashMap();
        try {
            result = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            logger.error("格式化失败,获取数据格式={}", response.getData(), e);
        }
        return result;
    }

    /**
     * (后台)
     * 校验昵称格式，校验通过返回null,否则返回{@link CodeStatus}中的具体错误
     *
     * @param nickname
     * @return
     * <AUTHOR>
     * 2017年5月9日 上午11:01:10
     */
    private CodeStatus checkNicknameForBackstage(String accountUuid, String nickname) {
        if (StringUtils.isBlank(nickname)) {
            return CodeStatus.NICKNAME_INVALID_BACKSTAGE;
        }

        // 不能大于10个字符(与数据库一致)
        if ((nickname = nickname.trim()).length() > 10) {
            return CodeStatus.NICKNAME_INVALID_BACKSTAGE;
        }

        return null;
    }

    /**
     * 根据性别获取获取预设头像
     *
     * @param gender 性别，1:男; 2:女
     * @return
     * <AUTHOR>
     * 2017年3月9日 下午2:04:01
     */
    public static String getAvatarBySexType(Integer gender) {
        return regReplaceAvatarPart + AvatarHandleService.getDefAvatar(gender);
    }

    /**
     * @Title:setNickname
     * @Description:设置昵称
     * @author:huangyuehong
     * @Date:2016年2月17日 下午5:24:05
     */
    @Transactional
    public SetNicknameResult setNickname(String nickname, String accountUuid, Integer useRenameCard, Integer appcode, String smid) {
        return this.setNicknameByUuid(nickname, accountUuid, useRenameCard, appcode, smid);
    }

    @Transactional
    public SetNicknameResult setNicknameByUuid(String nickname, String accountUuid, Integer useRenameCard, Integer appcode, String smid) {
        if (!accountsInfoService.isAccountMobileChecked(accountUuid)) {
            throw new ServiceException(CodeStatus.SET_NICKNAME_NO_BIND_MOBILE);
        }

        //默认允许修改名称
        int status = 1;
        if (useRenameCard == null || useRenameCard != 1) {    //如果useRenameCard 为null ， 或者 useRenameCard 不等于1 校验
            status = Integer.valueOf(accountsInfoService.getSetNicknamePrivByAccount(accountUuid, appcode).get("nick_name_enable"));
        }
        //没权限修改昵称
        if (status < 1) {
            throw new ServiceException(CodeStatus.CAN_NOT_SET_NICKNAME);
        }

        SetNicknameResult result = setNickname(accountUuid, nickname, appcode, smid);
        this.setNicknameUpdateTime(accountUuid);
        // 改名卡不记录次数
        if (!Objects.equals(1, useRenameCard)) {
            this.setAccountChangeNicknameNum(accountUuid, DateUtil.currentTimeSeconds(), 1);
        }
        return result;
    }

    /**
     * 修改昵称v2
     *
     * @param nickname
     * @param accountUuid
     * @param useRenameCard
     * @param appcode
     * <AUTHOR>
     * @date 2020/02/14 15:48
     */
    @Transactional
    public void setNicknameV2(String nickname, String accountUuid, Integer useRenameCard, Integer appcode, String smid) {
        if (!accountsInfoService.isAccountMobileChecked(accountUuid)) {
            throw new ServiceException(CodeStatus.SET_NICKNAME_NO_BIND_MOBILE);
        }
        try {
            setNickname(accountUuid, nickname, appcode, smid);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            // 改名卡不记录次数
            if (!Objects.equals(1, useRenameCard)) {
                this.setAccountChangeNicknameNum(accountUuid, DateUtil.currentTimeSeconds(), 1);
            }
        }
        this.setNicknameUpdateTime(accountUuid);
    }

    /**
     * 设置昵称次数
     *
     * @param accountUuid
     * @param time        秒时间戳
     * @param offset      变化量 1 or -1
     * <AUTHOR>
     * @date 2020/02/14 16:56
     */
    public void setAccountChangeNicknameNum(String accountUuid, long time, int offset) {
        int num = 0;

        // 记录当天次数
        String todayRedisKey = RedisKeyConstant.ACCOUNT_CHANGE_NICKNAME_TODAY_NUM.setArg(accountUuid);
        String timeStr= accountStringRedisTemplate.opsForValue().get(todayRedisKey);
        if(StringUtils.isNotBlank(timeStr) && StringUtils.isNumeric(timeStr)){
            Integer todayNum = Integer.parseInt(timeStr) + 1;
            accountStringRedisTemplate.opsForValue().set(todayRedisKey, todayNum.toString(), TimeFormatUtil.getTodayEndMsTimeStampWithRandom(), TimeUnit.MILLISECONDS);
        }else{
            accountStringRedisTemplate.opsForValue().set(todayRedisKey, "1", TimeFormatUtil.getTodayEndMsTimeStampWithRandom(), TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 设置昵称
     *
     * @param accountUuid
     * @param nickname
     * @param appcode
     * @return
     * <AUTHOR>
     * @date 2020/06/11 00:16
     */
    private SetNicknameResult setNickname(String accountUuid, String nickname, Integer appcode, String smid) {
        // 校验昵称合法性(nicknameStatus为null表示通过)
        CodeStatus nicknameStatus = checkNickname(nickname);
        if (nicknameStatus != null) {
            throw new ServiceException(nicknameStatus);
        }

        Map<String, Object> accountMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"account_name"}, "1", false, true).get(accountUuid);
        //2017.3.23 记录用户昵称修改记录
        String accountNameNew = nickname;
        String accountNameOld = MapUtils.getString(accountMap, "account_name");
        SetNicknameResult result = new SetNicknameResult();

        // 所有设置昵称行为都要走阿里和敏感词检测
        result.setRiskVerify(false);
        appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
        String checkmsg = "";
        String suggestion = RiskDetectRiskLevelEnum.BLOCK.name();
        String requestId = "";
        String responseStr = "";

        NickNameRiskVerify merge = nickNameRiskVerifyService.addVerify(accountUuid, accountNameOld, accountNameNew, CommConst.NO_0, 2, 0, null, checkmsg, appcode, suggestion, requestId, responseStr);
        Long id = merge.getId();
        try {
            ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accountUuid, nickname, smid, String.valueOf(merge.getId()), RiskDetectEnum.TEXT_NICKNAME.name(), appcode, cloned);
            suggestion = contentCheckDTO.getSuggestion();
            checkmsg = contentCheckDTO.getRiskLabel1() + contentCheckDTO.getRiskLabel2() + contentCheckDTO.getRiskLabel3() + "";
            requestId = contentCheckDTO.getRequestId();
            responseStr = JSON.toJSONString(contentCheckDTO);
            LOGGER.info("数美昵称检测.uuid={}.result={}", accountUuid, responseStr);
        } catch (Exception e) {
            logger.error("调用大保健文本检测失败,用户uuid{}", accountUuid, e);
        }
        merge.setSuggestion(checkmsg);
        merge.setRequestId(requestId);
        merge.setResponseStr(responseStr);
        nickNameRiskVerifyDao.merge(merge);

        // 自动审核不通过
        if (Objects.equals(AliyunTextStatusEnum.BLOCK.name(), suggestion)) {
            throw new ServiceException(CodeStatus.NICKNAME_SENSITIVE_WORDS);
        }

        Map<String, String> infoHashValue = new HashMap<>();
        infoHashValue.put("account_name_status", "0");//昵称状态 0:未审核 1:已审核
        infoHashValue.put("account_name", accountNameOld);//昵称
        infoHashValue.put("account_name_origin", nickname);//昵称
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), infoHashValue);

        if (Objects.equals(RiskDetectRiskLevelEnum.BLOCK.name(), suggestion)) {
            BuryService.pushNicknameAutoAudit(CommonAuditStatus.AUDIT_FAIL.getStatus(), id, "自动审核不通过");
        } else {
            BuryService.pushNicknameAutoAudit(CommonAuditStatus.AUDIT_SUCCESS.getStatus(), id, "自动审核通过");
        }

        return result;
    }

    /**
     * //更新完以后，添加缓存 写入日志 更新索引 推入反垃圾队列 操作
     *
     * @param accountUuid
     * @param accountNameOld
     * @param accountNameNew
     * @param nickname
     */
    public void afterSetNicknameByUuid(String accountUuid, String accountNameOld, String accountNameNew, Integer defaultNickName, Integer oldDefaultNickName,String nickname) {
        //2017.6.27 添加直播im缓存
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "account_name", nickname);
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "nickname", nickname);

        try {
            accountsSetNameLogService.addLog(accountUuid, accountNameOld, accountNameNew, defaultNickName, oldDefaultNickName, 2, accountUuid, 1);
        } catch (Exception e) {
            logger.error("用户修改昵称记录写入数据库错误。accountUuid={}，account_name_old={}，account_name_new={}",
                    accountUuid, accountNameOld, accountNameNew, e);
        }

        try {
            // 更新搜索系统昵称索引
            buryService.updateNicknameIndex(accountUuid, nickname);
        } catch (Exception e) {
            logger.error("用户修改昵称，更新搜索系统昵称索引异常。accountUuid={}，nickname={}", accountUuid, nickname, e);
        }

        // 推入队列通知反垃圾系统
        antiSpamMqUtil.pushToAccountActionMq(accountUuid, "", AccountActionTypeEnum.EDIT_INFO,null);
    }

    public void setNicknameUpdateTime(String accountUuid) {
        accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.NICKNAME_UPD_TIME.setArg(accountUuid), DateUtil.currentTimeSeconds().toString(), 30, TimeUnit.DAYS);
    }

    private void delNicknameUpdateTime(String accountUuid) {
        accountStringRedisTemplate.delete(RedisKeyConstant.NICKNAME_UPD_TIME.setArg(accountUuid));
    }

    /**
     * 设置昵称(后台)
     *
     * @param accountUuid
     * @param nickname
     * @param userId      后台操作用户的id
     * @return
     * <AUTHOR>
     * 2017年5月9日 上午10:52:55
     */
    @Transactional(readOnly = false)
    public SetNicknameResult setNicknameForBackstage(String accountUuid, String nickname, String userId, boolean isViolation) {
        // 先判断是否需要随机昵称
        if (isViolation) {
            nickname = accountsThirdPartyService.getRandomNicknameV2(accountUuid);
        }

        SetNicknameResult result = new SetNicknameResult();
        result.setUuid(accountUuid);
        result.setNickname(nickname);

        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts == null || StringUtils.isBlank(accounts.getUuid())) {
            throw new ServiceException("account_not_exists", "此账号不存在");
        }
        if (!Objects.equals(1, accounts.getAccount_type())) {
            throw new ServiceException("guest_cannot_update", "游客账号不能编辑");
        }

        //如果名称没变，返回成功
        if (StringUtils.equals(nickname, accounts.getAccount_name())) {
            return result;
        }

        // 校验昵称合法性(nicknameStatus为null表示通过)
        CodeStatus nicknameStatus = checkNicknameForBackstage(accountUuid, nickname);
        if (nicknameStatus != null) {
            throw new ServiceException(nicknameStatus);
        }

        //2017.3.23 记录用户昵称修改记录
        String accountNameNew = nickname;
        String accountNameOld = accounts.getAccount_name();

        // 根据account_uuid设置或更新昵称
        accountsManager.setNicknameByUuid(nickname, accountUuid);

        //2017.6.27 添加直播im缓存
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "account_name", nickname);
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "nickname", nickname);
//        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), UuidInfoField.ACCOUNT_NAME_DEFAULT, isViolation ? CommConst.YES_1.toString() : CommConst.NO_0.toString());

        try {
            accountsSetNameLogService.addLog(accountUuid, accountNameOld, accountNameNew, isViolation ? CommConst.YES_1 : CommConst.NO_0, CommConst.NO_0, 2, userId, 2);
        } catch (Exception e) {
            logger.warn("用户修改昵称记录写入数据库错误。account_uuid={}，account_name_old={}，account_name_new={}", accountUuid, accountNameOld, accountNameNew, e);
        }

        try {
            // 更新搜索系统昵称索引
            buryService.updateNicknameIndex(accountUuid, nickname);
        } catch (Exception e) {
            Log.error("更新搜索系统昵称索引，失败原因:{}", e.getMessage(), e);
        }

        // 2020.02.05 如果是系统设置昵称，清除用户昵称更新时间，让用户能马上修改
        if (AccountActionProfileService.isSystemAccountName(nickname)) {
            this.delNicknameUpdateTime(accountUuid);
            // 2020.02.14 昵称修改次数 -1
            this.setAccountChangeNicknameNum(accountUuid, DateUtil.currentTimeSeconds(), -1);
            BuryService.pushFilterCommonStatusToRecsys(accountUuid, 1, RecsysReportServerEnum.NICKNAME_UPDATE.value());
        } else {
            BuryService.pushFilterCommonStatusToRecsys(accountUuid, 0, RecsysReportServerEnum.NICKNAME_UPDATE.value());
        }

        return result;
    }

    @Transactional(readOnly = false)
    public SetNicknameResult setNicknameForPunish(String accountUuid, String nickname, String userId, boolean isViolation) {
        // 先判断是否需要随机昵称
        if (isViolation) {
            nickname = accountsThirdPartyService.getRandomNicknameV2(accountUuid);
        }

        SetNicknameResult result = new SetNicknameResult();
        result.setUuid(accountUuid);
        result.setNickname(nickname);


        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts == null || StringUtils.isBlank(accounts.getUuid())) {
            throw new ServiceException("account_not_exists", "此账号不存在");
        }
        if (!Objects.equals(1, accounts.getAccount_type())) {
            throw new ServiceException("guest_cannot_update", "游客账号不能编辑");
        }

        //如果名称没变，返回成功
        if (StringUtils.equals(nickname, accounts.getAccount_name())) {
            return result;
        }

        // 校验昵称合法性(nicknameStatus为null表示通过)
        // 处罚不需要校验
//        CodeStatus nicknameStatus = checkNicknameForBackstage(accountUuid, nickname);
//        if (nicknameStatus != null) {
//            throw new ServiceException(nicknameStatus);
//        }

        //2017.3.23 记录用户昵称修改记录
        String accountNameNew = nickname;
        String accountNameOld = accounts.getAccount_name();

        // 根据account_uuid设置或更新昵称
        accountsManager.setNicknameByUuid(nickname, accountUuid);

        //2017.6.27 添加直播im缓存
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "account_name", nickname);
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "nickname", nickname);
//        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), UuidInfoField.ACCOUNT_NAME_DEFAULT, isViolation ? CommConst.YES_1.toString() : CommConst.NO_0.toString());

        try {
            accountsSetNameLogService.addLog(accountUuid, accountNameOld, accountNameNew, isViolation ? CommConst.YES_1 : CommConst.NO_0, CommConst.NO_0, 2, userId, 2);
        } catch (Exception e) {
            logger.warn("用户修改昵称记录写入数据库错误。account_uuid={}，account_name_old={}，account_name_new={}", accountUuid, accountNameOld, accountNameNew, e);
        }

        try {
            // 更新搜索系统昵称索引
            buryService.updateNicknameIndex(accountUuid, nickname);
        } catch (Exception e) {
            Log.error("更新搜索系统昵称索引，失败原因:{}", e.getMessage(), e);
        }

        // 2020.02.05 如果是系统设置昵称，清除用户昵称更新时间，让用户能马上修改
        if (AccountActionProfileService.isSystemAccountName(nickname)) {
            this.delNicknameUpdateTime(accountUuid);
            // 2020.02.14 昵称修改次数 -1
            this.setAccountChangeNicknameNum(accountUuid, DateUtil.currentTimeSeconds(), -1);
            BuryService.pushFilterCommonStatusToRecsys(accountUuid, 1, RecsysReportServerEnum.NICKNAME_UPDATE.value());
        } else {
            BuryService.pushFilterCommonStatusToRecsys(accountUuid, 0, RecsysReportServerEnum.NICKNAME_UPDATE.value());
        }

        return result;
    }


    public void updateNicknameIndex(String uuid) {
        Map<String, Object> accountInfo = accountsInfoService.getInfoByUuid(new String[]{uuid}, new String[]{"account_name"}, "1", false, false).get(uuid);
        buryService.updateNicknameIndex(uuid, MapUtils.getString(accountInfo, "account_name"));
    }

    public List<Map<String, Object>> getAccountsInfoByUuidsForAppSearch(List<String> accountUuids) {
        List<Map<String, Object>> resultList = getPersonalInfoPage(accountUuids.toArray(new String[accountUuids.size()]));
        if (resultList != null) {
            for (Map<String, Object> item : resultList) {
                String avatar = MapUtils.getString(item, "avatar");
                if (StringUtils.isNotBlank(avatar)) {
                    item.put("avatar", StringUtils.removeEnd(this.getAvatarUrlPrefix(), "/") + "/" + StringUtils.removeStart(ToolsService.urlToUri(avatar, "1"), "/"));
                }
            }
        }
        return resultList;
    }

    /**
     * 根据accountUuid查询用户相关的信息，仅用在app搜索用户
     * account_uuid
     * avatar
     * nickname
     * gender_type
     * gender_certification
     * account_level
     * identity => []
     */
    private List<Map<String, Object>> getPersonalInfoPage(String... accountUuids) {
        List<Map<String, Object>> returnList = new LinkedList<>();
        Map<String, Map<String, Object>> accountInfoMaps = accountsInfoService.getInfoByUuid(accountUuids, new String[]{"avatar", "account_name", "sex_type",
                UuidInfoField.ZHIMA_CERTIFICATION, UuidInfoField.ACCOUNT_LEVEL, "avatar_dress_id", "baseaddr", UuidInfoField.AGE, UuidInfoField.CONSTELLATION,
                UuidInfoField.AGE_IS_SECRET, "baseaddr_is_secret", UuidInfoField.DRIVER_LEVEL, "achievement", "enable_location"}, "1", false, true);

        // 2020.02.18 查询有趣值
        Map<String, Long> accountGrowScoreMap = SpringContextHolder.getBean(TqUserGradeClient.class).batchGetAccountGrowScore(Lists.newArrayList(accountUuids));

        for (Map.Entry<String, Map<String, Object>> accountInfoEntry : accountInfoMaps.entrySet()) {
            String accountUuid = accountInfoEntry.getKey();
            Map<String, Object> accountInfo = accountInfoEntry.getValue();

            Map<String, Object> returnItem = new HashMap<>();
            returnItem.put("account_uuid", accountUuid);
            returnItem.put("avatar", MapUtils.getString(accountInfo, "avatar", ""));
            returnItem.put("nickname", MapUtils.getString(accountInfo, "account_name", ""));
            returnItem.put("gender_type", MapUtils.getString(accountInfo, "sex_type", ""));
            //性别认证的值，直接用芝麻认证代替
            returnItem.put("gender_certification", MapUtils.getString(accountInfo, UuidInfoField.ZHIMA_CERTIFICATION, ""));
            returnItem.put("zhima_certification", MapUtils.getString(accountInfo, UuidInfoField.ZHIMA_CERTIFICATION, ""));
            returnItem.put("account_level", MapUtils.getString(accountInfo, UuidInfoField.ACCOUNT_LEVEL, ""));
            returnItem.put("avatar_dress_id", MapUtils.getString(accountInfo, "avatar_dress_id", ""));
            returnItem.put("constellation", MapUtils.getString(accountInfo, UuidInfoField.CONSTELLATION, ""));
            returnItem.put("age", Objects.equals("1", MapUtils.getString(accountInfo, UuidInfoField.AGE_IS_SECRET)) ? "" : MapUtils.getString(accountInfo, UuidInfoField.AGE, ""));
            returnItem.put("baseaddr", Objects.equals("1", MapUtils.getString(accountInfo, "baseaddr_is_secret")) ? "" : MapUtils.getString(accountInfo, "baseaddr", ""));
            // 2020.07.14 下线功能
            returnItem.put("identity", Lists.newArrayList());
//            returnItem.put("identity", accountIdentityService.getAccountIdentityListByAccountUuid(accountUuid));
            returnItem.put("driver_level", MapUtils.getString(accountInfo, UuidInfoField.DRIVER_LEVEL, "C"));
            String achievement = StringUtils.defaultIfBlank(MapUtils.getString(accountInfo, "achievement"), "{}");
            returnItem.put("achievement", accountsAchievementService.formatAdorn(JsonUtils.stringToObject(achievement, new TypeReference<Map<String, String>>() {
            })));

            returnItem.put("grow_score", MapUtils.getString(accountGrowScoreMap, accountUuid, ""));
            returnItem.put("enable_location", MapUtils.getString(accountInfo, "enable_location", ""));
            returnList.add(returnItem);
        }

        return returnList;
    }

    private Map<String, Long> getAccountGrowScore(String[] accountUuids) {
        Map<String, Long> result = Maps.newHashMap();
        if (accountUuids == null || accountUuids.length == 0) {
            return result;
        }
        try {
            SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.PRIVILEGE_SYSTEM);
            Object[] data = {accountUuids};
            SoaResponse soaResponse = soaClient.call("GrowInfo", "batchGetAccountGrowScore", data);
            if (soaResponse.fail()) {
                logger.error(LoggerUtils.soaRequestFail("[privilege]", "GrowInfo", "batchGetAccountGrowScore", soaResponse.getCode(), soaResponse.getMsg()));
            }
            String json = soaResponse.getData();
            if (StringUtils.isNotBlank(json) && !Objects.equals(json, "[]")) {
                result = JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<Map<String, Long>>() {
                });
            }
        } catch (Exception e) {
            logger.error("获取用户有趣值异常", e);
        }

        return result;
    }

    /**
     * @return
     * @Title:checkAccount
     * @Description:通过account_uuid验证密码
     * @author:huangyuehong
     * @Date:2016年2月19日 下午5:51:04
     */
    public int checkAccount(String account_uuid, String password) {
//        if (MPAccountRequest.isRequestMP()) {
            mpAccountClient.checkPassword(account_uuid, password);
            return 1;
//        }

        //  以下部分可以去掉，中台校验即可，代码处理要改
//        Accounts accounts = accountsManager.getUuidAndPwdByUuid(account_uuid);
//        if (accounts != null) {
//            String account_password = accounts.getAccount_password();
//            if (StringUtils.isBlank(account_password)) {
//                return -1;
//            } else if (account_password.equals(password)) {
//                return 1;
//            } else {
//                return 0;
//            }
//        }
//        return 0;
    }

    /**
     * @return
     * @Title:checkPassword
     * @Description:通过ticket_id验证密码
     * @author:huangyuehong
     * @Date:2016年2月19日 下午6:00:22
     */
    public int checkPassword(String accountUuid, String password) {
        if (StringUtils.isNotBlank(accountUuid)) {
            return this.checkAccount(accountUuid, password);
        }
        return 0;
    }

    /**
     * 判断输入的密码与当前帐号密码是否一样
     *
     * @param password 输入的密码
     * @param uuid
     * @return
     */
    public boolean checkPasswordByUuid(String password, String uuid) {
//        if (MPAccountRequest.isRequestMP()) {
            mpAccountClient.checkPassword(uuid, password);
            return true;
//        }

        //  以下部分可以去掉，中台校验即可，代码处理要改
//        Accounts accounts = accountsManager.getUuidAndPwdByUuid(uuid);
//        // 当前账号的密码
//        String account_password;
//        if (accounts != null) {
//            account_password = accounts.getAccount_password();
//            if (password.equals(account_password)) {
//                return true;
//            } else {
//                return false;
//            }
//        }
//        return false;
    }

    /**
     * 根据uuid获取account_id
     *
     * @param uuid
     * @return
     * @Title:getAccountIdByUuid
     * @author:huangyuehong
     * @Date:2016年4月27日 下午5:23:31
     */
    public long getAccountIdByUuid(String uuid) {
        Long accountId = accountsManager.getAccountIdByUuid(uuid);
        return accountId == null ? 0 : accountId.longValue();
    }

    public Map<String, String> getNameByUuid(String[] uuids) {
        Map<String, String> returnMap = new HashMap<>();
        // 判断是否需要查询accounts表
        if (uuids.length > 0) {
            List<Object[]> uuidAndAccountNameList = accountsManager.getUuidAndAccountNameInUuids(Arrays.asList(uuids));
            for (Object[] objects : uuidAndAccountNameList) {
                returnMap.put(String.valueOf(objects[0]), String.valueOf(objects[1]));
            }
        }
//        List<String> missUuidList = new ArrayList<>();
//        for (String uuid : uuids) {
//            if (!returnMap.containsKey(uuid)) {
//                missUuidList.add(uuid);
//            }
//        }
//        if (!missUuidList.isEmpty()) {
//            List<Object[]> uuidAndAccountNameList = accountsGuestDao.getUuidAndAccountNameInUuids(Arrays.asList(uuids));
//            for (Object[] objects : uuidAndAccountNameList) {
//                returnMap.put(String.valueOf(objects[0]), String.valueOf(objects[1]));
//            }
//        }
        return returnMap;
    }

    public Map<String, Map<String, String>> getNameAndRegtimeByUuid(String[] uuids) {
        Map<String, Map<String, String>> returnMap = new HashMap<>();
        // 判断是否需要查询accounts表
        if (uuids.length > 0) {
            List<Object[]> uuidAndAccountNameAndRegtimeList = accountsManager.getUuidAndAccountNameAndRegtimeInUuids(Arrays.asList(uuids));
            for (Object[] objects : uuidAndAccountNameAndRegtimeList) {
                Map<String, String> map = new HashMap<>();
                map.put("account_name", objects[1] == null ? "" : String.valueOf(objects[1]));
                map.put("create_time", objects[2] == null ? "0" : String.valueOf(objects[2]));
                returnMap.put(String.valueOf(objects[0]), map);
            }
        }
//        List<String> missUuidList = new ArrayList<>();
//        for (String uuid : uuids) {
//            if (!returnMap.containsKey(uuid)) {
//                missUuidList.add(uuid);
//            }
//        }
//        if (!missUuidList.isEmpty()) {
//            List<Object[]> uuidAndAccountNameAndRegtimeList = accountsGuestDao.getUuidAndAccountNameAndRegtimeInUuids(Arrays.asList(uuids));
//            for (Object[] objects : uuidAndAccountNameAndRegtimeList) {
//                Map<String, String> map = new HashMap<>();
//                map.put("account_name", objects[1] == null ? "" : objects[1].toString());
//                map.put("create_time", objects[2] == null ? "0" : objects[2].toString());
//                returnMap.put(String.valueOf(objects[0]), map);
//            }
//        }
        return returnMap;
    }

    /**
     * 后台添加账号
     *
     * @param accountVo
     * @return
     */
    @Transactional
    public RegVo addAccountForBackstage(AccountVo accountVo, String userId) {
        CodeStatus codeStatus = this.checkNicknameForBackstage(null, accountVo.getNickname());
        if (codeStatus != null) {
            throw new ServiceException(codeStatus);
        }
//        String uuid = uuidService.generateUUID();
//        if (uuid == null) {
//            throw new ServiceException(CodeStatus.GET_UUID_FAILURE);
//        }
        String password = this.encryptPassword(accountVo.getAccount_password());
        if (StringUtils.isBlank(password)) {
            throw new ServiceException(CodeStatus.PASSWORD_ENCRYPTION_ERROR);
        }
        if (accountVo.getAvatar_list() == null || accountVo.getAvatar_list().isEmpty()) {
            throw new ServiceException("avatar_empty", "请至少上传一张头像图片");
        }
        this.validateMobileForBackstage(accountVo.getMobile());
//        this.validateEmailForBackstage(accountVo.getEmail());
        // 注销账号封禁
        if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Mobile.getValue(), accountVo.getMobile())){
            throw new ServiceException(CodeStatus.OPENID_ALREADY_BAN_MOBILE);
        }

        Map<String, Object> userAdmin = mpAccountClient.createUserAdmin(accountVo.getMobile(), password);
        String uuid = MapUtils.getString(userAdmin, CamelCaseBizConstant.UUID);
        String mobile = StringUtils.trimToEmpty(accountVo.getMobile());

        Accounts accounts = new Accounts();
        accounts.setUuid(uuid);
        accounts.setAccount_name(accountVo.getNickname());
        accounts.setMobile(mobile);
        accounts.setMobile_cipher(EncryptUtil.encrypt(mobile));
        accounts.setMobile_digest(EncryptUtil.sm3(mobile));
        accounts.setEmail(StringUtils.EMPTY);
        accounts.setAccount_type(accountVo.getAccount_type() == null ? 1 : accountVo.getAccount_type());
        accounts.setAccount_status(accountVo.getAccount_status() == null ? 1 : accountVo.getAccount_status());
        accounts.setLast_logintime(accountVo.getLast_logintime() == null ? DateUtil.currentTimeSeconds() : accountVo.getLast_logintime());
        accounts.setAccount_password(password);
        accounts.setSex_type(accountVo.getSex_type() == null ? 0 : accountVo.getSex_type());
        accounts.setCreate_time(DateUtil.currentTimeSeconds());
        accounts.setCs_id(accounts.getCreate_time());
        accounts.setReg_style(RegStyle.Admin.name());
        //查询默认头像
        String defaultAvatar = ToolsService.getFirstNoBlankValue(accountVo.getAvatar_list());
        if (StringUtils.isBlank(defaultAvatar)) {
            defaultAvatar = getAvatarBySexType(accountVo.getSex_type()); // no
        }
        accounts.setAvatar(defaultAvatar);
        accounts.setAppcode(SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode());
        accounts.setCloned(SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned());
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(null);
        accountsInfo.setAccount_uuid(accounts.getUuid());
        accountsInfo.setIs_check_mobile(StringUtils.isBlank(mobile) ? 0 : 1);
        accountsInfo.setAffectivestatus(accountVo.getAffectivestatus() == null ? 0 : accountVo.getAffectivestatus());
        accountsInfo.setPersonal_profile(StringUtils.trimToEmpty(accountVo.getPersonal_profile()));
        accountsInfo.setDating_intention(accountVo.getDating_intention() == null ? 0 : accountVo.getDating_intention());
        accountsInfo.setBirth(accountVo.getBirth());
        accountsInfo.setConstellation(accountVo.getBirth() == null ? "" : DateUtil.getConstellationFromSeconds(accountsInfo.getBirth()));
        accountsInfo.setAge(accountVo.getBirth() == null ? 0 : (int) ((DateUtil.currentTimeSeconds() - accountsInfo.getBirth()) / (365 * 24 * 60 * 60)));
        accountsInfo.setSexual(accountVo.getSexual() == null ? 0 : accountVo.getSexual());
        accountsInfo.setBaseaddr(StringUtils.trimToEmpty(accountVo.getBaseaddr()));
        accountsInfo.setHometown(StringUtils.trimToEmpty(accountVo.getHometown()));
        accountsInfo.setSm_type(StringUtils.EMPTY);
        accountsInfo.setCreate_time(DateUtil.currentTimeSeconds());

        accounts = accountsManager.merge(accounts);
        accountsInfo.setAccount_id(accounts.getAccount_id());
        accountsInfo = accountsInfoDao.merge(accountsInfo);
        AccountsForumProfile accountsForumProfile = this.setAccountForumForReg(accounts);
        this.setAccountPhotoForBaskstage(uuid, accountVo.getAvatar_list() == null || accountVo.getAvatar_list().isEmpty() ? Lists.newArrayList(defaultAvatar) : accountVo.getAvatar_list());

        // 更新搜索系统昵称索引
        buryService.updateNicknameIndex(accounts.getUuid(), accounts.getAccount_name());

        RegVo regVo = new RegVo();
        regVo.setUuid(accounts.getUuid());
        regVo.setNickname(accounts.getAccount_name());
        regVo.setMobile(mobile);
        regVo.setIs_check_mobile(accountsInfo.getIs_check_mobile());
        accountsSetNameLogService.addLog(accounts.getUuid(), "", accounts.getAccount_name(), CommConst.NO_0, CommConst.NO_0, 1, userId, 2);
        accountsInfoService.setUuidCache(uuid, mobile, accounts, accountsInfo, accountsForumProfile);

        Integer isFace = aliyunLiveFaceDetectService.detectIsFaceV2(accounts.getUuid(), accounts.getAvatar());
        accountsPhotoService.updateAvatarFaceStatus(accounts.getUuid(), String.valueOf(isFace), null);

//        recommendService.setSexType(uuid, accounts.getSex_type(), null);
//        recommendService.setBaseAddr(uuid, accountsInfo.getBaseaddr_is_secret(), accountsInfo.getBaseaddr(), null);
//        this.setRegDefaultExperience(uuid, 1);
        //initializeAccountDriverScore(uuid);
        this.toMobilePlace(uuid, mobile);

        // 将账户添加到反垃圾系统白名单
        addAccountsToInspectWhiteList(regVo);
        accountsCardService.preGrantNormalCard(uuid);

        return regVo;
    }

    private void addAccountsToInspectWhiteList(RegVo regVo) {
        Object[] form = {
                regVo.getUuid()
        };

        String service = "inspectWhitelist";
        String method = "add";

        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM).call(service, method, form);

        if (soaResponse.fail()) {
            LOGGER.error(LoggerUtils.soaRequestFail("[ssoUrl]", service, method, soaResponse.getCode(), soaResponse.getMsg()));
        }
    }

    /**
     * 参考  addAccountForBackstage
     * @param accountLucVo
     * @return
     */
    public AccountLucVo addAccountForLuc(AccountLucVo accountLucVo) {
        CodeStatus codeStatus = this.checkNicknameForBackstage(null, accountLucVo.getNickname());
        if (codeStatus != null) {
            throw new ServiceException(codeStatus);
        }
        String password = this.encryptPassword(accountLucVo.getAccount_password());
        if (StringUtils.isBlank(password)) {
            throw new ServiceException(CodeStatus.PASSWORD_ENCRYPTION_ERROR);
        }
        if (accountLucVo.getAvatar_list() == null || accountLucVo.getAvatar_list().isEmpty()) {
            throw new ServiceException("avatar_empty", "请至少上传一张头像图片");
        }
        this.validateMobileForBackstage(accountLucVo.getMobile());

        Map<String, Object> userAdmin = mpAccountClient.createUserAdmin(accountLucVo.getMobile(), password);
        String uuid = MapUtils.getString(userAdmin, CamelCaseBizConstant.UUID);
        accountLucVo.setUuid(uuid);
        accountLucVo.setAccount_password(password);

        String mobile = StringUtils.trimToEmpty(accountLucVo.getMobile());

        Accounts accounts = new Accounts();
        accounts.setUuid(uuid);
        accounts.setAccount_name(accountLucVo.getNickname());
        accounts.setMobile(mobile);
        accounts.setMobile_cipher(EncryptUtil.encrypt(mobile));
        accounts.setMobile_digest(EncryptUtil.sm3(mobile));
        accounts.setEmail(StringUtils.EMPTY);
        accounts.setAccount_type(1);
        accounts.setAccount_status(1);
        accounts.setLast_logintime(DateUtil.currentTimeSeconds());
        accounts.setAccount_password(password);
        accounts.setSex_type(accountLucVo.getSex_type() == null ? 1 : accountLucVo.getSex_type());
        accounts.setCreate_time(DateUtil.currentTimeSeconds());
        accounts.setCs_id(accounts.getCreate_time());
        accounts.setReg_style(RegStyle.Admin.name());
        //查询默认头像
        String defaultAvatar = ToolsService.getFirstNoBlankValue(accountLucVo.getAvatar_list());
        if (StringUtils.isBlank(defaultAvatar)) {
            defaultAvatar = getAvatarBySexType(accounts.getSex_type()); // no
        }
        accounts.setAvatar(defaultAvatar);
        accounts.setAppcode(SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode());
        accounts.setCloned(SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned());
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(null);
        accountsInfo.setAccount_uuid(accounts.getUuid());
        accountsInfo.setIs_check_mobile(StringUtils.isBlank(mobile) ? 0 : 1);
        accountsInfo.setAffectivestatus(accountLucVo.getAffectivestatus() == null ? 0 : accountLucVo.getAffectivestatus());
//        accountsInfo.setPersonal_profile("");
//        accountsInfo.setDating_intention(0);
        accountsInfo.setBirth(accountLucVo.getBirth());
        accountsInfo.setConstellation(accountLucVo.getBirth() == null ? "" : DateUtil.getConstellationFromSeconds(accountsInfo.getBirth()));
        accountsInfo.setAge(accountLucVo.getBirth() == null ? 0 : (int) ((DateUtil.currentTimeSeconds() - accountsInfo.getBirth()) / (365 * 24 * 60 * 60)));
        accountsInfo.setSexual(0);
        accountsInfo.setHeight(accountLucVo.getHeight());
        accountsInfo.setWeight(accountLucVo.getWeight());
        accountsInfo.setTrade(accountLucVo.getTrade());
        accountsInfo.setIncome(accountLucVo.getIncome());
        accountsInfo.setProfession(accountLucVo.getProfession());
        accountsInfo.setEducation_level(accountLucVo.getEducation_level());
        accountsInfo.setBaseaddr("");
        accountsInfo.setHometown(StringUtils.trimToEmpty(accountLucVo.getHometown()));
//        accountsInfo.setSm_type("");
        accountsInfo.setCreate_time(DateUtil.currentTimeSeconds());

        accounts = accountsManager.merge(accounts);
        accountsInfo.setAccount_id(accounts.getAccount_id());
        accountsInfo = accountsInfoDao.merge(accountsInfo);
        AccountsForumProfile accountsForumProfile = this.setAccountForumForReg(accounts);
        this.setAccountPhotoForBaskstage(uuid, accountLucVo.getAvatar_list() == null || accountLucVo.getAvatar_list().isEmpty() ? Lists.newArrayList(defaultAvatar) : accountLucVo.getAvatar_list());

        // 更新搜索系统昵称索引
        buryService.updateNicknameIndex(accounts.getUuid(), accounts.getAccount_name());

        RegVo regVo = new RegVo();
        regVo.setUuid(accounts.getUuid());
        regVo.setNickname(accounts.getAccount_name());
        regVo.setMobile(mobile);
        regVo.setIs_check_mobile(accountsInfo.getIs_check_mobile());
        accountsSetNameLogService.addLog(accounts.getUuid(), "", accounts.getAccount_name(), CommConst.NO_0, CommConst.NO_0, 1, "脚本创建", 2);
        accountsInfoService.setUuidCache(uuid, mobile, accounts, accountsInfo, accountsForumProfile);

        Integer isFace = aliyunLiveFaceDetectService.detectIsFaceV2(accounts.getUuid(), accounts.getAvatar());
        accountsPhotoService.updateAvatarFaceStatus(accounts.getUuid(), String.valueOf(isFace), null);

//        this.setRegDefaultExperience(uuid, 1);

        this.toMobilePlace(uuid, mobile);

        // 将账户添加到反垃圾系统白名单
        addAccountsToInspectWhiteList(regVo);
        accountsCardService.preGrantNormalCard(uuid);

        // 设置真人认证
        realPersonService.setUserRealPersonCertification(uuid, defaultAvatar);

        return accountLucVo;
    }

    @Transactional
    public Accounts updateAccountForBackstage(String uuid, AccountVo accountVo, String userId) {
        LOGGER.info("后台修改用户资料开始,用户uuid={},资料数据={},userId={}", uuid, JSON.toJSON(accountVo), userId);
        Accounts accounts = accountsManager.getByUuid(uuid);
        if (accounts == null) {
            throw new ServiceException("account_not_exists", "该账号不存在");
        }
        String oldAccountName = accounts.getAccount_name();
        if (!Objects.equals(1, accounts.getAccount_type())) {
            throw new ServiceException("guest_cannot_update", "游客账号不能编辑");
        }
        //如果昵称有变化
        if (!Objects.equals(accounts.getAccount_name(), accountVo.getNickname())) {
            CodeStatus codeStatus = this.checkNicknameForBackstage(uuid, accountVo.getNickname());
            if (codeStatus != null) {
                throw new ServiceException(codeStatus);
            }
        }
        //如果手机号有变化
        String mobile = accounts.getMobile();
        boolean ifChangePhone = false;
        if (!Objects.equals(mobile, accountVo.getMobile())) {
            ifChangePhone = true;
            this.validateMobileForBackstage(accountVo.getMobile());
            if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Mobile.getValue(), accountVo.getMobile())){
                throw new ServiceException(CodeStatus.OPENID_ALREADY_BAN_MOBILE);
            }
        }
        //如果邮箱有变化
//        if (!Objects.equals(accounts.getEmail(), accountVo.getEmail())) {
//            this.validateEmailForBackstage(accountVo.getEmail());
//        }

        String newMobile = StringUtils.trimToEmpty(accountVo.getMobile());

        if (MPAccountRequest.isRequestMP() && ifChangePhone) {
            logger.info("uuid: {}, mobile: {}", uuid, newMobile);
            mpAccountClient.changePhoneAdmin(uuid, newMobile);
        }

        Map<String, Object> accountUpdate = new HashMap<>();
        accounts.setAccount_name(StringUtils.trimToEmpty(accountVo.getNickname()));
        accountUpdate.put("account_name", StringUtils.trimToEmpty(accountVo.getNickname()));
        accounts.setMobile(newMobile);
        String mobileCipher = EncryptUtil.encrypt(newMobile);
        accounts.setMobile_cipher(mobileCipher);
        accountUpdate.put("mobile", newMobile);
        accountUpdate.put("mobile_cipher", mobileCipher);
        accountUpdate.put("mobile_digest", EncryptUtil.sm3(newMobile));
        String defaultAvatar = ToolsService.getFirstNoBlankValue(accountVo.getAvatar_list());
        if (StringUtils.isNotBlank(defaultAvatar)) {
            accounts.setAvatar(StringUtils.trimToEmpty(defaultAvatar));
            accountUpdate.put("avatar", StringUtils.trimToEmpty(defaultAvatar));
        }
        accounts.setSex_type(accountVo.getSex_type());
        accountUpdate.put("sex_type", accountVo.getSex_type());
//        accounts.setEmail(StringUtils.EMPTY);
//        accountUpdate.put("email", StringUtils.EMPTY);

        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(accounts.getUuid());
        accountsInfo.setIs_check_mobile(StringUtils.isBlank(newMobile) ? 0 : 1);
        accountsInfo.setAffectivestatus(accountVo.getAffectivestatus() == null ? 0 : accountVo.getAffectivestatus());
        accountsInfo.setPersonal_profile(StringUtils.trimToEmpty(accountVo.getPersonal_profile()));
        accountsInfo.setDating_intention(accountVo.getDating_intention() == null ? 0 : accountVo.getDating_intention());
        accountsInfo.setBirth(accountVo.getBirth());
        accountsInfo.setConstellation(accountVo.getBirth() == null ? "" : DateUtil.getConstellationFromSeconds(accountsInfo.getBirth()));
        accountsInfo.setAge(accountVo.getBirth() == null ? 0 : (int) ((DateUtil.currentTimeSeconds() - accountsInfo.getBirth()) / (365 * 24 * 60 * 60)));
        accountsInfo.setSexual(accountVo.getSexual() == null ? 0 : accountVo.getSexual());
        accountsInfo.setBaseaddr(StringUtils.trimToEmpty(accountVo.getBaseaddr()));
        accountsInfo.setHometown(StringUtils.trimToEmpty(accountVo.getHometown()));
        accountsInfo.setSm_type(StringUtils.EMPTY);
        accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());

        accountsManager.updateByUuid(accountUpdate, StringUtils.trimToEmpty(uuid));
        accountsInfoDao.merge(accountsInfo);
        this.setAccountPhotoForBaskstage(uuid, accountVo.getAvatar_list() == null || accountVo.getAvatar_list().isEmpty() ? Lists.newArrayList(defaultAvatar) : accountVo.getAvatar_list());
        if (!Objects.equals(oldAccountName, accountVo.getNickname())) {
            accountsSetNameLogService.addLog(accounts.getUuid(), oldAccountName, accountVo.getNickname(), CommConst.NO_0, CommConst.NO_0, 2, userId, 2);
            // 更新搜索系统昵称索引
            buryService.updateNicknameIndex(accounts.getUuid(), accountVo.getNickname());
        }
        accountsInfoService.setUuidCache(uuid, accountVo.getMobile(), accounts, accountsInfo, null);

        Integer isFace = aliyunLiveFaceDetectService.detectIsFaceV2(
                accounts.getUuid(),
                AccountsPhotoService.regReplaceAvatarPart + StringUtils.removeStart(avatarHandleService.getAvatarByVersion(accounts.getAvatar(), "1"), "/")
        );
        accountsPhotoService.updateAvatarFaceStatus(accounts.getUuid(), String.valueOf(isFace), null);

//        recommendService.setSexType(uuid, accountVo.getSex_type(), oldSexType);
//        recommendService.setBaseAddr(uuid, accountsInfo.getBaseaddr_is_secret(), accountsInfo.getBaseaddr(), oldBaseAddr);
        this.toMobilePlace(uuid, mobile);
        LOGGER.info("后台修改用户资料完成,用户uuid={},资料数据={},userId={}", uuid, JSON.toJSON(accountVo), userId);
        return accounts;
    }

    private void validateMobileForBackstage(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return;
        }

        if (!ValidateUtil.isMobile(mobile)) {
            throw new ServiceException("mobile_invalid", "不是合法的手机号");
        }

        int checkStatus = mobileService.checkMobileStatus(mobile, true);
        if (checkStatus > 0) {
            throw new ServiceException(CodeStatus.MOBILE_EXISTS);
        }
    }

    @Deprecated
    private void validateEmailForBackstage(String email) {
        if (StringUtils.isBlank(email)) {
            return;
        }
        String accountUuid = accountsManager.getUuidByEmail(email);
        if (!StringUtils.isBlank(accountUuid)) {
            throw new ServiceException("email_exists", "邮箱已存在");
        }
    }

    private void setAccountPhotoForBaskstage(String accountUuid, List<String> avatarList) {
        List<String> noBlankAvatarList = new ArrayList<>();
        if (avatarList != null) {
            for (String avatar : avatarList) {
                if (StringUtils.isNotBlank(avatar)) {
                    noBlankAvatarList.add(avatar);
                }
            }
        }
        if (!noBlankAvatarList.isEmpty()) {
            accountsPhotoService.setAvatarAndPhotoForBaskstage(accountUuid, noBlankAvatarList.toArray(new String[noBlankAvatarList.size()]));
        }
    }

    /**
     * 根据accountUuid查询数据库，可以指定查询字段
     * 2023.08.03手机号查中台
     *
     * @param fields
     * @param accountUuids
     * @param master       是否查主库 true:是; false:否;
     * @return
     */
    protected List<Map<String, Object>> getFieldByAccountUuidFromDb(Collection<String> fields, Collection<String> accountUuids, boolean master) {
        Sql sql = Sql.build(Accounts.class, fields).in("uuid", accountUuids);
        if (master) {
            sql = sql.masterDB();
        }
        List<Map<String,Object>> list = accountsManager.queryForList(sql);

        // 2023.08.03 如果包含手机号，则查中台
        if(fields.contains("mobile") && CollectionUtils.isNotEmpty(list)) {
            List<String> uuidList = list.stream().map(entry-> MapUtils.getString(entry, "uuid")).distinct().collect(Collectors.toList());
            Map<String, Object> mobileMap = soaService.getMobileByUuidsMP(uuidList);

            for (Map<String,Object> map : list) {
                String uuid = MapUtils.getString(map, "uuid");
                map.put("mobile", MapUtils.getString(mobileMap, uuid, ""));
            }

        }

        return list;
    }

    /**
     * 根据uuid查询数据库，可以指定查询字段
     * 2023.08.03手机号查中台
     *
     * @param fields
     * @param uuids
     * @return
     */
    protected List<Map<String, Object>> getFieldByUuidFromDb(Collection<String> fields, Collection<String> uuids) {
        return getFieldByAccountUuidFromDb(fields, uuids, true);
    }

    public Map<String, String> webLogin(String mobile, String verify) {
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        logger.info("webLogin mobile: {} cloned:{}", mobile, cloned);

        if (cloned == null) {
            throw new ServiceException(CodeStatus.CLONED_MISS_OR_ERR);
        }

        // 非主包分组不允许从这里登录
        if (!ClonedGroupEnum.MAIN_GROUP.contains(cloned)) {
            throw new ServiceException(CodeStatus.CLONED_MISS_OR_ERR);
        }

        if (!ValidateUtil.isMobile(mobile)) {
            throw new ServiceException(CodeStatus.MOBILE_WRONG);
        }

        checkLogin(mobile);

        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.WEB_LOGIN, mobile, verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }

        Accounts accounts = accountsManager.getUuidAndNameByMobile(mobile);
        if (accounts == null) {
            throw new ServiceException(CodeStatus.MOBILE_NOT_EXISTS);
        }
        // 校验uuid 并返回用户有效
        checkAndGetAccount(accounts.getUuid());

        return webLoginSuccess(accounts.getUuid(), accounts.getAccount_name());
    }

    private Map<String, String> webLoginSuccess(String accountUuid, String accountName) {
        String webTicket = ticketService.generateTicket(accountName);
        String salt = RandomStringUtils.randomAlphanumeric(20);

        String accountKey = Md5Util.encryptSHA1(Md5Util.encode(webTicket + salt));
        AutoRegVo autoRegVo = this.loginByIdAndAccountKey(accountUuid, accountKey);

        accountsInfoService.setByAccountKey(accountKey, autoRegVo.getUuid());

        Map<String, Object> accountInfo = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"default_card_id"}, "1", false, false).get(accountUuid);

        Map<String, String> result = new HashMap<>();
        result.put("account_uuid", autoRegVo.getUuid());
        result.put("account_name", autoRegVo.getAccount_name());
        result.put("avatar", avatarHandleService.getAvatarByVersion(autoRegVo.getAvatar(), "1"));
        result.put("web_ticket", webTicket);
        result.put("salt", salt);
        result.put("default_card_id", MapUtils.getString(accountInfo, "default_card_id", ""));
        return result;
    }

    private AutoRegVo loginByIdAndAccountKey(String uuid, String accountKey) {
        //返回的对象
        AutoRegVo autoRegVo = new AutoRegVo();
        Accounts accounts = accountsManager.getByUuid(uuid, false);
        // 传入的uuid一定会查到用户，所以暂不校验accounts
        String oldAccountKey = accounts.getAccount_key();
        //更新accountKey缓存
        if (oldAccountKey != null) {
            ticketStringRedisTemplate.delete(RedisKeyConstant.ACCOUNT_KEY.setArg(oldAccountKey));
        }
        Map<Object, Object> accountKeyMap = new HashMap<>();
        accountKeyMap.put("uuid", accounts.getUuid() == null ? "" : accounts.getUuid());
        ticketStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_KEY.setArg(accountKey), accountKeyMap);
        //绑定accountKey
        this.bindAccountKey(accountKey, uuid);
        autoRegVo.setAccount_name(accounts.getAccount_name());
        autoRegVo.setAvatar(accounts.getAvatar());
        autoRegVo.setUuid(uuid);
        return autoRegVo;
    }

    private void bindAccountKey(String accountKey, String accountUuid) {
        accountsManager.clearAccountKey(accountKey);
        accountsManager.updateAccountKeyByUuid(accountKey, accountUuid);
    }

    /**
     * 注册账号
     *
     * @param mobile
     * @param nickname
     * @param password
     * @param sexType
     * @param avatar
     * @param membersVo
     * @param version   0或空: 返回全路径; 其他: 返回不带host的uri
     * @return
     */
    private RegVo regAccounts(String mobile, String nickname, String password,
                              Integer sexType, String avatar, RegStyle regStyle,
                              MembersVo membersVo, int version,
                              Long birth, Integer defaultNickName,
                              AccountRegisterDTO registerDTO, String smid) {
        // 手机号校验
        regCheckMobileUsable(mobile);

        if (!Objects.equals(sexType, 1) && !Objects.equals(sexType, 2)) {
            throw new ServiceException(CodeStatus.SEX_TYPE_ERROR);
        }

        boolean isRandomAvatar = false;//是否是随机头像
        if (avatar == null || avatar.trim().length() == 0 || (StringUtils.isNotBlank(avatar) && AvatarHandleService.isDefAvatar(AvatarHandleService.getAvatarOfSavePhoto(avatar)))) {
            isRandomAvatar = true;
            avatar = getAvatarBySexType(sexType);//根据性别获取 no
        }

        // 年龄校验
        if (null != birth && birth != 0L && birth >= -********** && birth < DateUtil.currentTimeSeconds()) {
            if (birth > (DateUtil.currentTimeSeconds() - *********)) {
                throw new ServiceException(CodeStatus.ACCOUNT_BIRTH_ERROR);
            }
        }
        registerDTO.setSexType(sexType);
        // 扩展字段处理
        regExtraJsonHandler(registerDTO);
        logger.debug("请求sdk reg:{}", JsonUtils.objectToString(registerDTO));
        Map<String, Object> reg = mpAccountClient.reg(registerDTO);
        // TODO 2025.1.2 以下注册失败抛异常，会导致j70有数据，j2无数据。理论上需要处理

        String uuid = MapUtils.getString(reg, CamelCaseBizConstant.UUID);
        String ticket = MapUtils.getString(reg, CamelCaseBizConstant.TICKET);

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer appcode = sbp.getAppcode() == null ? 0 : sbp.getAppcode();
        Integer cloned = sbp.getCloned() == null ? 0 : sbp.getCloned();
        String channel = StringUtils.trimToEmpty(sbp.getChannel());
        Integer platformId = sbp.getPlatform_id() == null ? 0 : sbp.getPlatform_id();
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();

        Accounts accounts = new Accounts();
        accounts.setUuid(uuid);
        accounts.setAccount_name(nickname);
        accounts.setAccount_ticket(ticket);
        accounts.setAccount_password(password);
        accounts.setMobile(mobile);
        accounts.setMobile_cipher(EncryptUtil.encrypt(mobile));
        accounts.setMobile_digest(EncryptUtil.sm3(mobile));
        accounts.setAccount_type(1);// 1表示正常注册
        accounts.setMember_id(membersVo == null ? null : membersVo.getId());
        accounts.setSex_type(sexType);
        accounts.setCreate_time(currentTimeSeconds);
        accounts.setAvatar(avatar);
        accounts.setCs_id(currentTimeSeconds);
        accounts.setAppcode(appcode);
        accounts.setCloned(cloned);
        accounts.setChannel(channel);
        accounts.setPlatform_id(platformId);
        accounts.setReg_style(regStyle.name());
        if (isRandomAvatar) {
            accounts.setForum_status(3);
        }
        // 注册直接设为登录状态（0:退出; 1:登录）
        accounts.setIs_logged_in(1);
        accounts.setLast_logintime(currentTimeSeconds);
        accounts.setEmail(StringUtils.EMPTY);
        accounts = accountsManager.merge(accounts);

        // 同步保存用户位置信息 2025.1.2 后面移到这的
        Integer cityId = asyncTrackingService.syncTrackingAccountLocationInfo(uuid);

        // 2025.1.2 用户注册标签实时同步 位置提前
        SoaService.userLabelSync(uuid, sexType, currentTimeSeconds, cityId);

        //2017.3.23 记录用户昵称修改记录
        String accountNameNew = nickname;
        try {
            accountsSetNameLogService.addLog(accounts.getUuid(), "", accountNameNew, defaultNickName, CommConst.NO_0, 1, uuid, 1);
        } catch (Exception e) {
            logger.warn("用户注册昵称记录写入数据库错误。account_id={}，account_uuid={}，account_name_old={}，account_name_new={}",
                    accounts.getAccount_id(), accounts.getUuid(), "", accountNameNew, e);
        }
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(null);
        accountsInfo.setAccount_id(accounts.getAccount_id());
        accountsInfo.setAccount_uuid(accounts.getUuid());
        if (null != birth && birth != 0L && birth >= -********** && birth < DateUtil.currentTimeSeconds()) {
            accountsInfo.setBirth(birth);
            accountsInfo.setConstellation(DateUtil.getConstellationFromSeconds(birth));
            accountsInfo.setAge(ToolsService.calAgeFromBirth(birth));
        }

        accountsInfo.setUpdate_sex_number(0);
        if (mobile == null || mobile.trim().length() == 0) {
            accountsInfo.setIs_check_mobile(0);
        } else {
            accountsInfo.setIs_check_mobile(1);
        }
        accountsInfo.setPersonal_profile_status(CommonAuditStatus.NONE.getStatus());
        accountsInfo.setCreate_time(DateUtil.currentTimeSeconds());

        // 是否可以修改生日
        accountsInfo.setChange_age_status(CommConst.YES_1);

        this.accountsInfoDao.merge(accountsInfo);
        AccountSnList accountSnList = accountSnListService.create(uuid);
        if (accountSnList == null) {
            logger.error("注册失败，创建accountSnList异常");
            throw new ServiceException(CodeStatus.REG_FAIL);
        }
        AccountsForumProfile forumProfile = this.setAccountForumForReg(accounts);
        AccountProfileVo profileVo = accountsInfoService.getProfileByAccountUuid(accounts.getUuid());
        //2017.4.18 添加头像到相册
        accountsPhotoService.updateAvatar(uuid, avatar, accounts.getForum_status(), currentTimeSeconds);
        RegVo regVo = new RegVo();
        String checkmsg = "";
        String suggestion = AliyunTextStatusEnum.BLOCK.name();
        String requestId = "";
        String responseStr = "";

        NickNameRiskVerify merge = nickNameRiskVerifyService.addVerify(accounts.getUuid(), "用户" + accounts.getUuid(), nickname, defaultNickName, 1, 0, null, checkmsg, appcode, suggestion, requestId, responseStr);
        try {
            //第三方注册来源是QQ或微信，昵称一定要过审
            if (Objects.equals(1, defaultNickName) && !regStyle.equals(RegStyle.QQ) && !regStyle.equals(RegStyle.WeChat)) {
                suggestion = AliyunTextStatusEnum.PASS.name();
                requestId = SoaBaseParams.fromThread().getDistinctRequestId();
                responseStr = "";
            } else {
                ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accounts.getUuid(), accounts.getAccount_name(), smid, String.valueOf(merge.getId()), RiskDetectEnum.TEXT_NICKNAME.name(), appcode, cloned);
                suggestion = contentCheckDTO.getSuggestion();
                checkmsg = contentCheckDTO.getReason();
                requestId = contentCheckDTO.getRequestId();
                responseStr = JSON.toJSONString(contentCheckDTO);
            }
        } catch (Exception e) {
            logger.error("调用大保健文本检测失败,用户uuid{}", accounts.getUuid(), e);
        }
        merge.setSuggestion(checkmsg);
        merge.setRequestId(requestId);
        merge.setResponseStr(responseStr);
        nickNameRiskVerifyDao.merge(merge);

        Long id = merge.getId();
        if (Objects.equals(AliyunTextStatusEnum.BLOCK.name(), suggestion)) {
            BuryService.pushNicknameAutoAudit(CommonAuditStatus.AUDIT_FAIL.getStatus(), id, "自动审核不通过");
        } else {
            BuryService.pushNicknameAutoAudit(CommonAuditStatus.AUDIT_SUCCESS.getStatus(), id, "自动审核通过");
        }

        regVo.setNickname(accounts.getAccount_name());
        regVo.setUsername(accounts.getEmail());
        if (mobile == null || mobile.trim().length() == 0) {
            regVo.setIs_bind_mobile(0);
        } else {
            regVo.setIs_bind_mobile(1);
        }
        regVo.setIs_check_mobile(profileVo.getIs_check_mobile());
        regVo.setAccount_level(profileVo.getAccount_level());
        regVo.setExperience(profileVo.getExperience());
        regVo.setAccount_actor(profileVo.getAccount_actor());
        regVo.setMedal_url(profileVo.getMedal_url());
        regVo.setHonor_name(profileVo.getHonor_name());
        regVo.setTqcoin(profileVo.getTqcoin());
        regVo.setAccount_id(accounts.getAccount_id());
        //注册成功后登录会有个ticket,网关判断这个ticket来确认登录状态
        regVo.setTicket_id(accounts.getAccount_ticket());
        regVo.setAccount_type(accounts.getAccount_type());
        regVo.setSex_type(accounts.getSex_type());
        regVo.setAge(accountsInfo.getAge());
        regVo.setAvatar(avatarHandleService.getAvatarByVersion(accounts.getAvatar(), String.valueOf(version)));
        regVo.setEmail(accounts.getEmail());
        regVo.setAccount_status(accounts.getAccount_status());
        regVo.setMobile(mobile);
        regVo.setMember_id(membersVo == null ? null : membersVo.getId());
        regVo.setUuid(accounts.getUuid());
        regVo.setCreate_time(accounts.getCreate_time());
        regVo.setAppcode(appcode);
        regVo.setDriver_level(profileVo.getDriver_level());

        // 更新搜索系统昵称索引
        buryService.updateNicknameIndex(accounts.getUuid(), accounts.getAccount_name());
        accountsInfoService.setUuidCache(uuid, mobile, accounts, accountsInfo, forumProfile);

        Integer isFace = aliyunLiveFaceDetectService.detectIsFaceV2(accounts.getUuid(), accounts.getAvatar());
        accountsPhotoService.updateAvatarFaceStatus(accounts.getUuid(), String.valueOf(isFace), null);

        //注册成功后自动登录，设置最后登录appcode为注册时的appcode
        Map<String, String> infoHashValue = new HashMap<>();
        infoHashValue.put("avatar_status", "0");//头像状态 0:未审核; 1:已审核
        infoHashValue.put("avatar_origin", AvatarHandleService.getAvatarOfSaveRedis(StringUtils.trimToEmpty(accounts.getAvatar())));//头像原图
        infoHashValue.put("avatar", "");
//            infoHashValue.put("is_new_def_avatar", isNewDefAvatar ? "1" : "0"); //是否是新的默认头像
//            infoHashValue.put(UuidInfoField.NEW_REG_202306, newRegister ? "1" : "0"); //是否是新版注册方式 2023.06冲刺迭代

        //设置昵称为
        infoHashValue.put("account_name_status", "0");//昵称状态 0:未审核 1:已审核
        infoHashValue.put("account_name", "用户" + accounts.getUuid());//昵称
//            infoHashValue.put(UuidInfoField.ACCOUNT_NAME_DEFAULT, defaultNickName.toString());
        infoHashValue.put("account_name_origin", nickname);//昵称
        infoHashValue.put(UuidInfoField.BIRTH, StringUtil.nullNumberToEmptyString(accountsInfo.getBirth()));
        // 2024.03.21 不写入缓存
//            infoHashValue.put(UuidInfoField.CONSTELLATION, StringUtils.trimToEmpty(accountsInfo.getConstellation()));
//            infoHashValue.put(UuidInfoField.AGE, StringUtil.nullNumberToEmptyString(accountsInfo.getAge()));

        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), infoHashValue);

        this.toMobilePlace(accounts.getUuid(), mobile);
        accountsCardService.preGrantNormalCard(accounts.getUuid());

        // 同步保存用户位置信息 2025.1.2 往前移
//        Integer cityId = asyncTrackingService.syncTrackingAccountLocationInfo(accounts.getUuid());
        regVo.setCityId(cityId);

        // 是否设置头像
        regVo.setIsSetAvatar(!isRandomAvatar);
        return regVo;
    }

    /**
     * 注册扩展字段处理
     *
     * @param register
     */
    private void regExtraJsonHandler(AccountRegisterDTO register) {
        try {
            ExtraJsonDTO extra;
            if (StringUtils.isBlank(register.getExtraJson())) {
                extra = new ExtraJsonDTO();
            } else {
                extra = JsonUtils.stringToObject(register.getExtraJson(), ExtraJsonDTO.class);
                if (extra == null) {
                    extra = new ExtraJsonDTO();
                }
            }

            extra.setSexType(register.getSexType());
            register.setExtraJson(JsonUtils.objectToString(extra));
        } catch (Exception e) {
            LOGGER.warn("extraJson转化失败, {}", register.getExtraJson(), e);
            // 防止客户端传的扩展字段有问题
            ExtraJsonDTO extra = new ExtraJsonDTO();
            extra.setSexType(register.getSexType());
            register.setExtraJson(JsonUtils.objectToString(extra));

        }
    }

    private void regCheckMobileUsable(String mobile) {
        if (StringUtils.isNotBlank(mobile)) {
            if (!ValidateUtil.isMobile(mobile)) {
                throw new ServiceException(CodeStatus.USERNAME_INVALIDATE);
            }

            int checkStatus = mobileService.checkMobileStatus(mobile, true);
            if (checkStatus == 1) {
                // 手机号是否注册涉及到用户隐私问题，因此此处不直接返回手机号已注册的信息，而是返回注册失败
                throw new ServiceException(CodeStatus.REGISTER_MOBILE_EXISTS);
            }
            if (checkStatus == 2) {
                throw new ServiceException(CodeStatus.MOBILE_DESTROYED);
            }
        }
    }

    public Map<String, Object> handleLoginResult(LoginVo loginVo, int tqcoinNameVersion) {
        Map<String, Object> resultMap = new TreeMap<>();
        resultMap.put("nickname", StringUtil.nullToEmptyWithTrim(loginVo.getNickname()));
        resultMap.put("is_bind_mobile", StringUtil.nullNumberToEmptyString(loginVo.getIs_bind_mobile()));
        resultMap.put("is_check_mobile", StringUtil.nullNumberToEmptyString(loginVo.getIs_check_mobile()));
        resultMap.put(AccountsInfoService.getTqcoinName(tqcoinNameVersion), StringUtil.nullNumberToEmptyString(loginVo.getTqcoin()));
        resultMap.put("account_actor", StringUtil.nullNumberToEmptyString(loginVo.getAccount_actor()));
        resultMap.put("account_level", StringUtil.nullNumberToEmptyString(loginVo.getAccount_level()));
        resultMap.put("medal_name", StringUtil.nullToEmptyWithTrim(loginVo.getHonor_name()));
        resultMap.put("ticket_id", StringUtil.nullToEmptyWithTrim(loginVo.getTicket_id()));
        resultMap.put("account_type", StringUtil.nullNumberToEmptyString(loginVo.getAccount_type()));
        resultMap.put("sex_type", StringUtil.nullNumberToEmptyString(loginVo.getSex_type()));
        resultMap.put("age", StringUtil.nullNumberToEmptyString(loginVo.getAge()));
        resultMap.put("avatar", StringUtil.nullToEmptyWithTrim(loginVo.getAvatar()));
        resultMap.put("account_uuid", StringUtil.nullToEmptyWithTrim(loginVo.getUuid()));
        resultMap.put("email", StringUtil.nullToEmptyWithTrim(loginVo.getEmail()));
        resultMap.put("mobile", StringUtil.mobileSecret(loginVo.getMobile()));
        resultMap.put("driver_level", StringUtils.defaultIfBlank(loginVo.getDriver_level(), "C"));
        resultMap.put("kilometer", StringUtil.nullNumberToEmptyString(loginVo.getKilometer()));
     // 2024.4.18 客户端代码已回滚，可以不下发
        //如果是版主显示版主头衔图片
        if (loginVo.getAccount_actor() != null && loginVo.getAccount_actor() > 0) {
            resultMap.put("account_medal", StringUtil.nullToEmptyWithTrim(loginVo.getMedal_url()));
        }
        // 新增活体认证状态
        resultMap.put("face_certification", StringUtil.nullNumberToEmptyString(loginVo.getAliyunFaceCertification()));
        resultMap.put("profile_verify_status", StringUtil.nullNumberToEmptyString(loginVo.getProfileVerifyStatus()));
        resultMap.put("register_time", StringUtil.nullNumberToEmptyString(loginVo.getRegister_time()));

        // 2021.04.28 新增去认证
        resultMap.put("to_certification", StringUtil.nullNumberToEmptyString(loginVo.getToCertification()));
        resultMap.put("dialog_content", StringUtil.nullToEmptyWithTrim(loginVo.getDialogContent()));

        resultMap.put("wa_say_to_bind_mobile", StringUtil.nullObjectToEmptyString(loginVo.getWaSayToBindMobile()));
        resultMap.put("to_real_person_certification", "0");
        resultMap.put("to_real_person_certification_dialog", "");
        resultMap.put("freeze_flag", loginVo.getFreeze_flag() != null ? loginVo.getFreeze_flag() : 0);
        resultMap.put("risk_status", loginVo.getRisk_status() != null ? loginVo.getRisk_status() : 0);
        // 2022.03.31 新增去真人认证
        if(Objects.equals(SoaBaseParams.fromThread().getAppcode(), CommConst.APPCODE_TAQU)
                && !Objects.equals(SoaBaseParams.fromThread().getCloned(), ClonedEnum.QIALIAO.getCode())){
            resultMap.put("to_real_person_certification", StringUtil.nullObjectToEmptyString(loginVo.getToRealPersonCertification()));
            resultMap.put("to_real_person_certification_dialog", StringUtil.nullObjectToEmptyString(loginVo.getToRealPersonCertificationDialog()));
            if(!VersionSwitchService.SWITCH_20220302.isGeVersion() && Objects.equals(MapUtils.getString(resultMap, "to_real_person_certification", "0"), CommConst.YES_1.toString())){
                throw new ServiceException("提示：请更新至最新版本");
            }
        }
        return resultMap;
    }


    /**
     * 登录、登出埋点
     *
     * @param token
     * @param platformId
     * @param accountUuid
     * @param type        1.登陆，0.退出
     * <AUTHOR>
     * 2017年7月25日 下午2:50:43
     */
    public void loginoutBury(String token, Integer platformId, Integer appcode, String accountUuid, String type, Integer cloned) {
        MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
        Map<String, Object> map = new HashMap<>();
        map.put("appcode", appcode);
        map.put("token", token);
        map.put("platform_id", String.valueOf(platformId));
        map.put("account_uuid", accountUuid);
        map.put("type", type);
        map.put("cloned", cloned == null ? "1" : cloned.toString());
        MqResponse mqResponse = mqClient.push("handel_account_device", map, null);
        if (mqResponse.fail()) {
            logger.warn("登录，加入用户绑定设备处理队列失败:" + mqResponse.getCode() + "，uuid:" + accountUuid);
        } else {
            logger.debug("登录，加入用户绑定设备处理队列成功");
        }
    }

    /**
     * 推入数据至队列：handleAdmpDeviceLogin
     * @param token
     * @param accountUuid
     * @param sexType
     * @param loginTime
     * @param platformId
     * @param appcode
     * @param cloned
     * @param ip
     */
    public void pushToAdmpDeviceLogin(String token, String accountUuid, Integer sexType, Long loginTime, Integer platformId, Integer appcode, Integer cloned, String ip){
        MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
        Map<String, Object> map = new HashMap<>();
        map.put("token", token);
        map.put("account_uuid", accountUuid);
        map.put("gender", sexType == null ? "0" : sexType.toString());
        map.put("login_time", String.valueOf(loginTime));
        map.put("platform_id", String.valueOf(platformId));
        map.put("appcode", String.valueOf(appcode));
        map.put("cloned", cloned == null ? "1" : cloned.toString());
        // ip可以传空
        map.put("ip", StringUtils.isBlank(ip) ? "" : ip);
        MqResponse mqResponse = mqClient.push("handleAdmpDeviceLogin", map, null);
        if (mqResponse.fail()) {
            logger.warn("登录，推送handleAdmpDeviceLogin处理队列失败:[{}], data=[{}]", mqResponse.getCode(), JSON.toJSONString(map));
        } else {
            logger.debug("登录，推送handleAdmpDeviceLogin处理队列成功, data=[{}]", JSON.toJSONString(map));
        }
    }

    /**
     * @param regVo
     * @param appcode
     * @param tqcoinNameVersion 趣币字段名称版本 0:返回趣币字段名称为tqcoin; 其他:返回趣币字段名称为score;
     * @return
     */
    @Transactional
    public Map<String, Object> handleReg(RegVo regVo, Integer appcode, int tqcoinNameVersion) {
//        String accountUuid = regVo.getUuid();

        Map<String, Object> resultMap = new TreeMap<>();
        resultMap.put("nickname", StringUtil.nullToEmptyWithTrim(regVo.getNickname()));
        resultMap.put("is_bind_mobile", StringUtil.nullNumberToEmptyString(regVo.getIs_bind_mobile()));
        resultMap.put("is_check_mobile", StringUtil.nullNumberToEmptyString(regVo.getIs_check_mobile()));
        resultMap.put(AccountsInfoService.getTqcoinName(tqcoinNameVersion), StringUtil.nullNumberToEmptyString(regVo.getTqcoin()));
        resultMap.put("account_actor", StringUtil.nullNumberToEmptyString(regVo.getAccount_actor()));
        resultMap.put("account_level", StringUtil.nullNumberToEmptyString(regVo.getAccount_level()));
        resultMap.put("medal_name", StringUtil.nullToEmptyWithTrim(regVo.getHonor_name()));
        resultMap.put("ticket_id", StringUtil.nullToEmptyWithTrim(regVo.getTicket_id()));
        resultMap.put("account_type", StringUtil.nullNumberToEmptyString(regVo.getAccount_type()));
        resultMap.put("sex_type", StringUtil.nullNumberToEmptyString(regVo.getSex_type()));
        resultMap.put("avatar", StringUtil.nullToEmptyWithTrim(regVo.getAvatar()));
        resultMap.put("account_id", StringUtil.nullToEmptyWithTrim(regVo.getUuid()));
        resultMap.put("account_uuid", StringUtil.nullToEmptyWithTrim(regVo.getUuid()));
        resultMap.put("mobile", StringUtil.mobileSecret(regVo.getMobile()));
        resultMap.put("driver_level", StringUtils.defaultIfBlank(regVo.getDriver_level(), "C"));
        // 2022.06.28 注册下发ab实验列表
        resultMap.put("ab_exp_info", SoaService.userABExpList(regVo.getUuid()));
        resultMap.put("age", StringUtil.nullNumberToEmptyString(regVo.getAge()));

        return resultMap;
    }

    public Map<String, Object> handleThirdReg(RegVo regVo, Integer appcode, int version) {
//        String account_uuid = regVo.getUuid();

        Map<String, Object> resultMap = new TreeMap<>();
        resultMap.put("nickname", regVo.getNickname());
        resultMap.put("is_bind_mobile", String.valueOf(regVo.getIs_bind_mobile()));
        resultMap.put("is_check_mobile", String.valueOf(regVo.getIs_check_mobile()));
        resultMap.put(AccountsInfoService.getTqcoinName(version), String.valueOf(regVo.getTqcoin()));
        resultMap.put("account_actor", String.valueOf(regVo.getAccount_actor()));
        resultMap.put("account_level", String.valueOf(regVo.getAccount_level()));
        resultMap.put("medal_name", regVo.getHonor_name() == null ? "" : regVo.getHonor_name());
        resultMap.put("ticket_id", regVo.getTicket_id());
        resultMap.put("sex_type", String.valueOf(regVo.getSex_type()));
        resultMap.put("avatar", regVo.getAvatar());
        resultMap.put("account_uuid", regVo.getUuid());
        resultMap.put("driver_level", StringUtils.defaultIfBlank(regVo.getDriver_level(), "C"));
        resultMap.put("is_do_bind_mobile", regVo.getIsDoBindMobile().toString());
        // 2022.06.28 注册下发ab实验列表
        resultMap.put("ab_exp_info", SoaService.userABExpList(regVo.getUuid()));
        resultMap.put("age", StringUtil.nullNumberToEmptyString(regVo.getAge()));

        // 2020.03.27 更新用户位置
        updateAccountExtraInfo(regVo.getUuid());
        // 2020.06.04 更新活跃
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        String token = sbp.getToken();
        Integer platformId = sbp.getPlatform_id();
        Integer cloned = sbp.getCloned();

        loginoutBury(token, platformId, appcode, regVo.getUuid(), "1", cloned);
        accountsMemberInfoService.createOrUpdate(regVo.getUuid(), token, sbp.getApp_version(), appcode, cloned, sbp.getChannel(), platformId);
        accountsMemberInfoService.updateAppVersionCache(regVo.getUuid(), sbp.getApp_version(), platformId, sbp.getChannel());

        // 2020.04.06刚注册，头像是不可推状态
        BuryService.pushFilterCommonStatusToRecsys(regVo.getUuid(), 1, RecsysReportServerEnum.REAL_AVATAR_UPDATE.value());
        BuryService.pushFilterCommonStatusToRecsys(regVo.getUuid(), 1, RecsysReportServerEnum.AVATAR_UPDATE.value());

        // 推送注册后置处理
        BuryService.pushAccountPostRegister(regVo.getUuid());

        // 2023.08.16 ab实验入组 https://o15vj1m4ie.feishu.cn/wiki/CEFYwjPeViJoCFkF1Enckgv1nzd
        // 2024.11.29 实验已经固化
//        SoaService.getUserExp(regVo.getUuid(), AbRuleCode.DISTANCE_PRIVACY_SETUP, appcode, cloned, token);
        //2023.12.01 Q4社交资料线 数据需求 https://o15vj1m4ie.feishu.cn/docx/OdrNdtiFSo8dXgxtahLcgdUyngh
        // 2024.11.29 实验已经回滚
//        SoaService.getUserExp(regVo.getUuid(), AbRuleCode.FEMALE_DEMAND_REAL_PERSON, appcode, cloned, token);

        return resultMap;
    }

    public String getAvatarUrlPrefix() {
        return StringUtil.nullToEmptyWithTrim(avatarUrl);
    }

    public String getImgUrlPrefix() {
        return StringUtil.nullToEmptyWithTrim(imgUrl);
    }

    void toMobilePlace(String uuid, String mobile) {
        if (StringUtils.isBlank(uuid) || StringUtils.isBlank(mobile)) {
            return;
        }
        accountStringRedisTemplate.opsForList().rightPush(RedisKeyConstant.MOBILE_PLACE_LIST.getPattern(), uuid + "#" + mobile);
    }

    @Transactional
    public void setMobilePlace(String uuid, String mobile) {
        if (StringUtils.isBlank(uuid) || StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
            return;
        }
        Map<String, String> data = mobilePlaceService.getPlace(mobile);
        String mobilePlace = "";
        if (StringUtils.isNotBlank(data.get("province")) && StringUtils.isNotBlank(data.get("city"))) {
            mobilePlace = StringUtils.join(Arrays.asList(data.get("province"), data.get("city")), ",");
        }
        accountsManager.updateMobilePlace(uuid, mobilePlace);
    }

    /**
     * 踢旧账号逻辑
     *
     * @param uuid
     * @param token
     */
    public void accountLogoutKick(String uuid, String token) {
        if (StringUtils.isBlank(uuid) || StringUtils.isBlank(token)) {
            return;
        }
        try {
            Map<String, String> map = new HashMap<>();
            map.put("account_uuid", uuid);
            map.put("token", token);
            kickStringRedisTemplate.convertAndSend("im_kick_by_other", JsonUtils.objectToString(map));
            logger.info("设备登出(或被踢出)消息发送频道im_kick_by_other成功, uuid={}, token={}", uuid, token);
        } catch (Exception e) {
            logger.error("设备登出(或被踢出)消息发送频道im_kick_by_other失败", e);
        }
    }

    public Map<String, Object> getRegStyleAndMobileByUuid(String uuid) {
        Map<String, Object> result = Maps.newHashMap();
        String mobile = accountsManager.getMobileByUuid(uuid);
        AccountsThirdParty accountsThirdParty = accountsThirdPartyService.findLastestOneByUuid(uuid);
        if (null == accountsThirdParty) {
            return result;
        }
        int isBindMobile = StringUtils.isEmpty(mobile) ? 0 : 1;
        result.put("reg_style", Optional.ofNullable(accountsThirdParty.getType()).orElse(""));
        result.put("is_bind_mobile", isBindMobile);
        return result;
    }

    /**
     * 登录时版本检测，如果登录版本小于注册版本。true-登录版本低于注册版本，进行登录限，false-不进行登录限制
     *
     * @param accountUuid
     * @param appVersion
     * @return
     */
    public void loginVersionLimit(String accountUuid, Integer platformId, Long appVersion) {
        SoaBaseParams sbp = SoaBaseParams.fromThread();

        //uuid登录限制
        if (StringUtils.isNotEmpty(accountUuid) && uuidLoginLimt(accountUuid, sbp.getAppcode(), sbp.getCloned())) {
            throw new ServiceException(CodeStatus.LOGIN_FAILURE.getReasonPhrase());
        }
        Boolean limit = accountLoginVersionLimitService.loginVersionLimit(accountUuid, platformId, appVersion, sbp.getAppcode(), sbp.getCloned(), sbp.getChannel());
        if (limit) {
            throw new ServiceException(CodeStatus.LOGIN_VERSION_LIMIT.getReasonPhrase());
        }
    }

    /**
     * 限制某些用户uuid只能在他趣或者配配其中一种上登录
     *
     * @param accountUuid
     * @return true-限制，false-不限制
     */
    public boolean uuidLoginLimt(String accountUuid, Integer appcode, Integer cloned) {
        boolean result = false;
        if (MapUtils.isEmpty(uuidLoginLimitMap) || null == uuidLoginLimitMap.get(accountUuid)) {
            return result;
        }
        try {
            String value = Joiner.on("_").join(Lists.newArrayList(appcode, cloned));
            if (!value.equals(uuidLoginLimitMap.get(accountUuid))) {
                result = true;
            }
        } catch (Exception e) {
            LOGGER.warn("判断是否进行uuid登录限制失败,用户uuid={}", accountUuid, e);
        }

        return result;
    }

    /**
     * 注册限制，相同QQ、微博、微信号、手机号、设备token号，最多只能注册有限的次数，次数可配置
     *
     * @param registerType 注册类型：QQ,WeiBo,WeChat,Apple,Visitor
     * @param mobile       手机号注册，第三方注册时为空
     * @param openId       第三方注册的唯一凭证，手机号注册时为空
     */
    public void registerLimit(String registerType, String mobile, String openId) {
        Integer cloned = SoaBaseParams.fromThread().getCloned();

        // 注册限制次数，0表示不限制
        Integer registerLimitTime = registerLimitService.getRegisterLimitTime();

        // 不需要限制则直接返回
        if (RegisterLimitService.REGISTER_NOT_LIMIT_TIME.equals(registerLimitTime)) {
            return;
        }
        Integer registerTime = 0;
        //手机号注册
        if (ActionModeEnum.TAQU.value().equals(registerType)) {
            registerTime = accountsDestroyLogManager.countByMobile(cloned, mobile);
        }
        //第三方注册
        else {
            registerTime = accountsThirdPartyService.countByOpenIdAndType(openId, registerType);
        }

        if (registerTime < registerLimitTime) {
            return;
        }

        throw new ServiceException(RegisterLimitDocEnum.getDocByType(registerType));

    }

    /**
     * @param accountUuid
     * @param time
     * @return
     * <AUTHOR>
     * @date 2020/02/14 14:32
     */
    public Map<String, String> getAccountChangeNicknameNum(String accountUuid, Long time) {
        int num = 0;
        String redisKey = RedisKeyConstant.ACCOUNT_CHANGE_NICKNAME_TODAY_NUM.setArg(accountUuid);
        String numStr = accountStringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNumeric(numStr)) {
            num = Integer.parseInt(numStr);
        }

        Map<String, String> result = Maps.newHashMap();
        result.put("account_uuid", accountUuid);
        result.put("num", String.valueOf(num));
        return result;
    }

    /**
     * 获取几个月之前的日期
     * @param date      日期
     * @param amount    倒退月数
     * @return
     */
    public static Date getDateBeforeMonth(Date date, Integer amount){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, amount > 0? -1 * amount: amount);
        return cal.getTime();
    }

    /**
     * 获得周一0点时间 周期：周一0点至周日23点59分59秒
     *
     * @param seconds 秒时间戳
     * @return {@link Long}
     */
    public static Long getWeekZero(long seconds) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(seconds * 1000));
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTimeInMillis() / 1000;
    }

    public Map<String, Map<String, Object>> getLastAppVersionByAccountUuids(String[] uuids) {
        Map<String, Map<String, Object>> infos = accountsInfoService.getInfoByUuid(uuids, new String[]{"last_app_version", "last_platform_id"}, "1", false, false);
        return infos;
    }

    public void checkAccountEmptyFacePhoto(String accountUuid, String smid) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("account_uuid", accountUuid);
        data.put("tracer_id", SoaBaseParams.fromThread().getDistinctRequestId());
        data.put("smid", smid);
        MqResponse response = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_ACCOUNT_AVATAR_PHOTO_UPDATE, data, 5L);
        if (response.fail()) {
            LOGGER.error("用户:{} 用户头像/相册变更数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
            return;
        }
        logger.info("push success checkAccountEmptyFacePhoto:{}", accountUuid);
    }

    /**
     * @param accountUuid
     * <AUTHOR>
     * @date 2020/04/01 21:26
     */
    public void checkAccountEmptyFacePhoto(String accountUuid) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("account_uuid", accountUuid);
        data.put("tracer_id", SoaBaseParams.fromThread().getDistinctRequestId());
        MqResponse response = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_ACCOUNT_AVATAR_PHOTO_UPDATE, data, 5L);
        if (response.fail()) {
            LOGGER.error("用户:{} 用户头像/相册变更数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
            return;
        }
        logger.info("push success checkAccountEmptyFacePhoto:{}", accountUuid);
    }

    /**
     * 判断用户是否活体认证 并且拥有有效图片 (批量)
     * 无则，推送告诉社区
     *
     * @param accountUuids
     * <AUTHOR>
     * @date 2020/04/01 21:26
     */
    public void checkAccountEmptyFacePhotoBatch(List<String> accountUuids) {
        for (String accountUuid : accountUuids) {
            checkAccountEmptyFacePhoto(accountUuid);
        }
    }

    /**
     * 判断用户是否活体认证 并且拥有有效图片
     *
     * @param accountUuid
     * @return null-校验失败；true-满足条件 活体+有效图片；false-不满足条件
     * <AUTHOR>
     * @date 2020/04/01 20:47
     */
    public Boolean isFaceCertificationAndHaveValidPhoto(String accountUuid) {
        Map<String, Map<String, Object>> infos = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"face_certification"}, "1", false, false);
        List<Map<String, String>> photo = accountsPhotoService.getAccountsAllPhoto(accountUuid, false);

        Map<String, Object> info = infos.get(accountUuid);
        if (info != null) {
            String faceCertification = MapUtils.getString(info, "face_certification", "");
            // 活体不为1，则未认证
            if (!Objects.equals(faceCertification, "1")) {
                return false;
            }

            if (photo == null) {
                return false;
            }

            for (Map<String, String> map : photo) {
                String status = map.get("status");
                if (Objects.equals(map.get("verify_status"), "1") && (Objects.equals(status, "2") || Objects.equals(status, "3") || Objects.equals(status, "4"))) {
                    return true;
                }
            }
            return false;
        }
        return null;
    }

    /**
     * 判断用户是否活体认证 并且拥有有效相册头像（审核通过）
     *
     * @param accountUuid
     * @return null-校验失败；true-满足条件 活体+有效相册头像（审核通过）；false-不满足条件
     * <AUTHOR>
     *
     */
    public Boolean isFaceCertificationAndHaveValidPhotoAvatar(String accountUuid) {
        Map<String, Map<String, Object>> infos = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"face_certification"}, "1", false, false);
        List<Map<String, String>> photo = accountsPhotoService.getAccountsAllPhoto(accountUuid, false);

        Map<String, Object> info = infos.get(accountUuid);
        if (info != null) {
            String faceCertification = MapUtils.getString(info, "face_certification", "");
            // 活体不为1，则未认证
            if (!Objects.equals(faceCertification, "1")) {
                return false;
            }

            if (photo == null) {
                return false;
            }

            for (Map<String, String> map : photo) {
                if ((Objects.equals(map.get("seq"), "1")) && Objects.equals(map.get("verify_status"), "1") && (Objects.equals(map.get("status"), "2"))) {
                    return true;
                }
            }
            return false;
        }
        return null;
    }

    /**
     * 是否默认头像
     *
     * @param accountUuid
     * @return
     * <AUTHOR>
     * @date 2020/04/13 10:47
     */
    public Boolean isSystemAvatar(String accountUuid) {
        Map<String, Map<String, Object>> infos = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"avatar"}, "1", false, false);
        Map<String, Object> info = infos.get(accountUuid);
        if (info != null) {
            String avatar = MapUtils.getString(info, "avatar", "");
            if (AccountsPhotoFilterService.isSystemAvatar(avatar)) {
                return true;
            }
            return false;
        }
        return null;
    }

    /**
     * 判断用户是否活体认证 并且拥有有效头像（待审核也算）
     *
     * @param accountUuid
     * @return null-校验失败；1-满足条件 活体+有效图片；0-不满足条件
     * <AUTHOR>
     * @date 2020/04/01 20:47
     */
    public Integer isFaceCertificationAndHaveValidAvatar(String accountUuid) {
        Map<String, Map<String, Object>> infos = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"face_certification", "pre_profile_verify_status", "profile_verify_status"}, "1", false, false);

        Map<String, Object> info = infos.get(accountUuid);
        if (info != null) {
            String faceCertification = MapUtils.getString(info, "face_certification", "");
            //  0:未认证; 1:已认证;2-认证失败 主
            String preProfileVerifyStatus = MapUtils.getString(info, "pre_profile_verify_status", "");
            //  0:未认证; 1:已认证;2-认证失败 客
            String profileVerifyStatus = MapUtils.getString(info, "profile_verify_status", "");
            // 活体不为1，则未认证
            if (!Objects.equals(faceCertification, "1")) {
                return 0;
            }
            if (StringUtils.isBlank(preProfileVerifyStatus)) {
                preProfileVerifyStatus = profileVerifyStatus;
            }
            if (Objects.equals(preProfileVerifyStatus, "1")) {
                return 1;
            }

            return 0;
        }
        return null;
    }

    /**
     * uuid是否存在
     *
     * @param uuid 用户uuid
     * @return true-存在；false-不存在；
     * <AUTHOR>
     * @date 2020/06/08 00:53
     */
    public Boolean isExistAccountUuid(String uuid) {
        Long accountId = accountsManager.getAccountIdByUuid(uuid);
        if (accountId != null) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * mobile是否存在
     *
     * @param mobile 用户手机号
     * @return true-存在；false-不存在；
     * <AUTHOR>
     * @date 2021/02/02 09:59
     */
    public Boolean isExistMobile(String mobile) {
        String accountUuid = accountsManager.getMobileByUuid(mobile);
        if (StringUtils.isBlank(accountUuid)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * mobile是否存在 用户表和注销表
     *
     * @param mobile 用户手机号
     * @return true-存在；false-不存在；
     * <AUTHOR>
     * @date 2021/02/02 09:59
     */
    public Map<String, String> isExistMobileHistory(String mobile) {
        //  2023.08.10 增加cloned
        Integer cloned = SoaBaseParams.fromThread().getCloned();

        Map<String, String> existsMap = new HashMap<>();
        Boolean existMobileByAccounts = this.isExistMobile(mobile);
        if (existMobileByAccounts) {
            existsMap.put("exist_mobile", "1");
            return existsMap;
        }

        Boolean existMobileByDestroyLog = accountsDestroyLogService.isExistMobile(cloned, mobile);
        if (existMobileByDestroyLog) {
            existsMap.put("exist_mobile", "1");
        } else {
            existsMap.put("exist_mobile", "0");
        }
        return existsMap;
    }

    /**
     * 昵称是否存在
     *
     * @param nickName
     * @return
     * <AUTHOR>
     * @date 2020/06/12 11:18
     */
    public Boolean isExistNickName(String nickName) {
        String resultCode = getResultCodeByCheckNickname(nickName);
        Boolean flag = !Objects.equals(resultCode, "N") && !Objects.equals(resultCode, "C")
                && !Objects.equals(resultCode, "B");
        return flag;
    }


    public LoginVo loginByContinueKey(String mobile, String continueKey, int version, String accessToken, String smid, String extraJson) {
        int freezeFlag = 0;
        Map<String, Object> login;
        if (StringUtils.isNotBlank(accessToken)) {
            AccountLoginDTO loginDTO = AccountLoginDTO.builder()
                    .loginType(AccountChannelEnum.FLASH.value())
                    .accToken(accessToken)
                    .smDeviceId(smid)
                    .extraJson(extraJson)
                    .build();

            login = mpAccountClient.login(loginDTO);

            mobile = MapUtils.getString(login, CamelCaseBizConstant.PHONE);
            if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
                logger.warn("闪验绑定手机号返回手机号:{} 格式错误, accessToken:{}", mobile, accessToken);
                throw new ServiceException(CodeStatus.FLASH_MOBILE_ERROR);
            }
        } else {
            AccountLoginDTO loginDTO = AccountLoginDTO.builder()
                    .loginType(AccountChannelEnum.PHONE.value())
                    .phone(mobile)
                    .smDeviceId(smid)
                    .extraJson(extraJson)
                    .build();

            login = mpAccountClient.login(loginDTO);
        }
        String ticket = MapUtils.getString(login, CamelCaseBizConstant.TICKET);
        if (login.containsKey(CamelCaseBizConstant.FREEZE_FLAG) &&
                Boolean.parseBoolean(MapUtils.getString(login, CamelCaseBizConstant.FREEZE_FLAG))) {
            freezeFlag = 1;
        }

        String randomStr = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile));
        if (!continueKey.equals(randomStr)) {
            throw new ServiceException("登录失败,请使用普通登录");
        }
        this.checkLogin(mobile);

        String accountsUuid = accountsManager.getUuidByMobile(mobile);
        if (StringUtils.isBlank(accountsUuid)) {
            logger.warn("随机码尝试登录不存在的账号");
            throw new ServiceException(CodeStatus.USERNAME_OR_PASSWORD_ERROR);
        }

        LoginVo loginVo = this.loginById(ticket, accountsUuid, version);
        loginVo.setFreeze_flag(freezeFlag);
        return loginVo;
    }

    /**
     * 回复注销用户，确保用户真实注销，才能操作成功。</br>
     * 如果查询到是假注销用户，我给提示：该用户为历史假注销用户，请先注销后，再恢复。
     *
     * @param accountUuid 用户uuid
     * @param operateName 操作人
     * @return 返回文字
     */
    @Transactional
    public String destroyRenew(String accountUuid, String operateName) {
        // 返回结果
        String reason = "未知问题，请联系研发人员";

        // 查询用户是否存在
        if (!isExistAccountUuid(accountUuid)) {
            reason = "未找到该用户：" + accountUuid;
            return reason;
        }

        // 查询用户是否符合注销
        int accountStatus = getAccountStatus(accountUuid);
        switch (accountStatus) {
            case -1:
                // 旧版假注销
                reason = "该用户为历史假注销用户，请先注销后，再恢复";
                break;
            case 0:
                // 注销
                reason = "账号已恢复";
                // 恢复账号
                if (MPAccountRequest.isRequestMP()) {
                    mpAccountClient.renewAccount(accountUuid);
                }
                renewAccount(accountUuid);
                // 记录日志
                accountsDestroyRenewLogService.addLog(accountUuid, operateName);
                break;
            case 1:
                // 正常用户
                reason = "该账号未注销，无需恢复";
                break;
            default:
                // 正常不可能进入
                LOGGER.warn("返回的用户状态异常.");
                break;
        }
        return reason;
    }

    /**
     * 获取用户状态
     *
     * @param accountUuid
     * @return 账号状态 0:注销; 1:正常状态; -1:旧版假注销
     */
    private int getAccountStatus(String accountUuid) {
        int status = 1;
        Integer accountStatus = accountsManager.getAccountStatusByUuid(accountUuid);
        if (Objects.equals(accountStatus, 0)) {
            // 注销用户
            status = 0;
        } else {
            // 正常用户 判断是否假注销
            Long lastValidCreateTime = accountsDestroyLogService.getLastValidCreateTime(accountUuid);
            if (lastValidCreateTime != null) {
                // 有有效的注销日志
                status = -1;
            }
        }
        return status;
    }

    // 恢复注销用户
    private void renewAccount(String accountUuid) {
        // 恢复数据库状态
        accountsManager.renewAccount(accountUuid);
        accountsDestroyLogService.invalidByUuid(accountUuid);
        // 恢复缓存状态
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "account_status", "1");

        // 更新昵称索引
        this.updateNicknameIndex(accountUuid);
    }

    /**
     * 再注册检查
     *
     * @param regStyle       注册方式
     * @param mobileOrOpenId 第三方open_id
     */
    public void reregistrationCheck(RegStyle regStyle, String mobileOrOpenId) {
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        AccountsDestroyLog log = null;
        // 闪验 也是手机注册
        if (RegStyle.Mobile.equals(regStyle) || RegStyle.Mobile_Auto.equals(regStyle) || RegStyle.Flash.equals(regStyle) || RegStyle.Flash_Auto.equals(regStyle)) {
            if(ToolsService.reregisterWhitelistSwitchEncryption.isOn(true)) {
                if (reRegisterWhitelistDao.getOneByMobileDigest(EncryptUtil.sm3(mobileOrOpenId)) > 0 ){
                    return;
                }
            }else {
                if (reRegisterWhitelistDao.getOneByMobile(mobileOrOpenId) > 0 ){
                    return;
                }
            }
            log = accountsDestroyLogManager.getByMobile(cloned, mobileOrOpenId);
        } else if (RegStyle.Apple.equals(regStyle)) {
            if (reRegisterWhitelistDao.getOneByOpenid(4, mobileOrOpenId) > 0) {
                return;
            }
            log = accountsDestroyLogDao.getByAppleId(mobileOrOpenId);
        } else if (RegStyle.WeChat.equals(regStyle)) {
            if (reRegisterWhitelistDao.getOneByOpenid(2, mobileOrOpenId) > 0) {
                return;
            }
            log = accountsDestroyLogDao.getByWechatId(mobileOrOpenId);
        } else if (RegStyle.WeiBo.equals(regStyle)) {
            if (reRegisterWhitelistDao.getOneByOpenid(3, mobileOrOpenId) > 0) {
                return;
            }
            log = accountsDestroyLogDao.getByWeiboId(mobileOrOpenId);
        } else if (RegStyle.QQ.equals(regStyle)) {
            if (reRegisterWhitelistDao.getOneByOpenid(1, mobileOrOpenId) > 0) {
                return;
            }
            log = accountsDestroyLogDao.getByQqId(mobileOrOpenId);
        } else if (RegStyle.Visitor.equals(regStyle)) {
            log = accountsDestroyLogDao.getByVisitorId(mobileOrOpenId);
        }

        if (log !=null){
            // 不需要校验的和历史数据不处理
            if (log.getNeedToLimitState() == 0 || log.getCharmRating() == null ){
                    return;
            }
            timeCheck(log.getCreate_time());
        }
    }


    /**
     * 时间检查
     *
     * @param startTime 开始时间
     */
    private void timeCheck(Long startTime) {
        // 是否设备白名单
        if (registerLimitService.isRegWhiteToken()) {
            return;
        }
        // 当天零时
        long endTime = DateUtil.getTodayBeginSecond();
        // 未满限制天数
        long limit =  ReRegisterLimitService.CANCELLATION_LENGTH * 24 * 60 * 60;
        if (ReRegisterLimitService.CANCELLATION_LENGTH == 0){
            return;
        }
        if (endTime - startTime <limit ){
            // 固定的日期
            long timeStamp = startTime + limit;
            throw new ServiceException("账号在短时间内重复注册认证，请在" + getDay(timeStamp) + "后再注册");
        }
    }


    public String getDay(Long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = DateUtil.getDateBegin(new Date(timeStamp * 1000));
        return sdf.format(date);
    }

    /**
     * 获取注销信息
     *
     * @param accountUuid
     * @return
     */
    public DestroyInfoResp getDestroyInfo(String accountUuid) {
        Map<String, Object> accountInfo = mpAccountClient.getAccountInfo(accountUuid);
        String mobile = StringUtil.nullObjectToEmptyString(MapUtils.getString(accountInfo, CamelCaseBizConstant.PHONE));

        DestroyInfoResp resp = new DestroyInfoResp();
        resp.setIsBindMobile(StringUtils.isNotBlank(mobile) ? "1" : "0");
        resp.setMobile(StringUtil.mobileSecret(mobile));
        resp.setDestroyProcessType(getAccountDestroyProcessTypeV2(accountUuid) + "");
        resp.setConfirmTip1(AccountDestroyUtil.getDestroyTip1());
        resp.setConfirmTip2(AccountDestroyUtil.getDestroyTip2());
        // 注销相关tips
        DestroyInfoResp.TipContent tips = new DestroyInfoResp.TipContent();
        resp.setInfo(tips);
        tips.setRelation(DestroyH5Mapping.resolveLink());
        AccountFollowServiceImpl.Response followNum = accountFollowSoaService.getFollowNum(accountUuid);
        Optional.ofNullable(followNum).ifPresent(temp -> {
            tips.setFans(temp.getFansNum());
            tips.setFollow(temp.getNum());
        });
        return resp;
    }

    /**
     * 注销处理类型 0-旧的注销逻辑 1-短信注销逻辑
     * @param accountUuid
     * @return
     */
    private Integer getAccountDestroyProcessTypeV2(String accountUuid) {
        // 充值状态
        Double inpourAmountTotal = 0D;
        // 收益状态 cash
        Double interactCash = 0D;

        Map<String,Object> filterConditions = Maps.newHashMap();
        filterConditions.put("uuid", Lists.newArrayList(accountUuid));

        try{
            String dataStr = SoaService.postDateApiExecuteV2("/touch/history/destroyInfo", filterConditions, null, null);
            List<Map<String,Object>> list = JsonUtils.stringToObject(dataStr, new TypeReference<List<Map<String, Object>>>() {});
            if(list != null) {
                for (Map<String, Object> map : list) {
                    if(Objects.equals(MapUtils.getString(map, "uuid"), accountUuid)){
                        inpourAmountTotal = MapUtils.getDouble(map, "hira", 0D);
                        interactCash = MapUtils.getDouble(map, "hirs", 0D);
                        break;
                    }
                }
            }
        }catch (Exception e){
            throw new ServiceException(CodeStatus.DESTROYED_GET_INFO_ERR);
        }

        // 人群：充值 或者 收益 要求走流程1-短信注销逻辑
        if(interactCash.compareTo(0D) > 0 || inpourAmountTotal.compareTo(0D) > 0){
            return 1;
        }
        // 默认 0-旧的注销逻辑，直接注销
        return 0;
    }

    /**
     * 校验channel长度
     * @param channel
     */
    public static void checkAccountsChannelLen(String channel){
        if(StringUtils.length(channel) > CHANNEL_MAX_LEN){
            // 校验channel长度
            LOGGER.warn("非法参数channel，长度大于{}，channel=[{}]", CHANNEL_MAX_LEN, channel);
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }
    }

    /**
     * 删除网安 要求绑定手机号redis key
     * @param accountUuid
     */
    public void deleteWaBindMobileCache(String accountUuid) {
        if(StringUtils.isBlank(accountUuid)){
            return;
        }
        accountStringRedisTemplate.delete(RedisKeyConstant.ACCOUNT_NO_ACTIVE_TASK_WA_BIND_MOBILE.setArg(accountUuid));
    }

    /**
     * 有返回手机号
     *
     * @param uuid
     * @return
     */
    public Accounts getByAccountUuid(String uuid) {
        return accountsManager.getByUuid(uuid);
    }

    /**
     * 查询认证底图
     * @param accountUuidList
     * @return
     */
    public Map<String, Map<String, String>> listAccountCertInfoForBackstage(List<String> accountUuidList) {
        Map<String, Map<String, String>> result = Maps.newHashMap();
        for (String accountUuid : accountUuidList) {
            if (StringUtils.isBlank(accountUuid)) {
                continue;
            }
            Map<String, String> infoMap = Maps.newHashMap();

            String certificationPhotoUrl = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
            certificationPhotoUrl = StringUtils.defaultString(certificationPhotoUrl, "");
            if(StringUtils.isNotBlank(certificationPhotoUrl)){
                certificationPhotoUrl = ToolsService.getPrivateBasePic(certificationPhotoUrl);
            }
            infoMap.put("certificationPhotoUrl", certificationPhotoUrl);

            String certificationPhotoUrlBiz = accountsCertificationService.getCertificationPhotoUrlBizByUuid(accountUuid);
            certificationPhotoUrlBiz = StringUtils.defaultString(certificationPhotoUrlBiz, "");
            if(StringUtils.isNotBlank(certificationPhotoUrlBiz)){
                certificationPhotoUrlBiz = ToolsService.getPrivateBasePic(certificationPhotoUrlBiz);
            }
            infoMap.put("certificationPhotoUrlBiz", certificationPhotoUrlBiz);

            String basePhotoUrl = aliyunLiveFaceDetectService.getBasePhotoUrlPrivateByAccountUuid(accountUuid);
            infoMap.put("basePhotoUrl", StringUtils.defaultString(basePhotoUrl, ""));

            result.put(accountUuid, infoMap);
        }
        return result;
    }

    /**
     *
     * @param defaultCardId
     * @param appcode
     * @param cloned
     * @param type 1-封号，2-设备
     * @param token
     * @return
     */
    private String getConfirmSchema(String defaultCardId, Integer appcode, Integer cloned, Integer type, String token){
        String url = RISK_APPEAL_LINK;
        if(EnvUtil.isGray()) {
            url = RISK_APPEAL_LINK_GRAY;
        }

        String param="account_id="+defaultCardId+"&appcode="+appcode+"&app_clone="+cloned+"&type="+type+"&token="+token;
        url=url+Encodes.urlEncode(param);
        return url;
    }

    /**
     * 登录注册管控
     *
     * @param type
     * @param model
     */
    public void loginAndRegIsControl(String type, String model) {
        RegisterLoginControlIsControlRespDto respDto = SoaService.registerLoginControlIsControl(type, model);
        if(respDto != null) {
            if(Objects.equals(respDto.getIsControl(), "true")) {
                logger.info("登录注册被管控");
                throw new ServiceException(respDto.getControlContent());
            }
        }
    }

    /**
     * 获取他趣id
     *
     * @param accountUuid
     * @return
     */
    public String getCardId(String accountUuid) {
        Map<String, Map<String, Object>> accountInfoMap = accountsInfoService
                .getInfoByUuid(new String[]{accountUuid}, new String[]{"default_card_id"}, "1", false, false);
        return MapUtils.getString(accountInfoMap.get(accountUuid), "default_card_id", "");
    }


    public List<DestroyResp> listDestroy(List<String> uuidList) {
        // 这里应该直接获取j70的账号状态，但因为历史原因，目前对外接口查询的账号状态还是在j2这边，等后续技术优化会来全局处理。
        List<InfoFiledCacheDTO> statusList = accountBaseInfoManager.listAccountStatus(uuidList);
        return statusList.stream().filter(status -> StatusEnum.AccountStatus.DEREGISTER.match(status.getAccountStatus()))
                .map(item -> {
                    DestroyResp resp = new DestroyResp();
                    resp.setAccountUuid(item.getUuid());
                    resp.setNickname("用户已注销");
                    Integer gender = Optional.ofNullable(item.getGender()).orElse(1);
                    String avatar = AccountsService.getAvatarBySexType(gender);
                    resp.setAvatar(avatar);
                    return resp;
                }).collect(Collectors.toList());
    }

}
