package cn.taqu.account.service;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsForumProfileDao;
import cn.taqu.account.model.AccountsForumProfile;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.Sql;

@Service
@Transactional
public class AccountsForumProfileService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsForumProfile> {

	@Autowired
	private AccountsForumProfileDao accountsForumProfileDao;
	@Autowired
	private AccountsService accountsService;
	@Autowired
	private AccountsLevelService accountsLevelService;
	@Autowired
	@Qualifier("accountStringRedisTemplate")
	private StringRedisTemplate accountStringRedisTemplate;

	public void save(AccountsForumProfile accountsForumProfile) {
		accountsForumProfileDao.merge(accountsForumProfile);
	}

	/**
	 * 
	 * @Title:addByAccount_uuid
	 * @Description:从redis和数据库中都没有获取到经验值时，向数据库插入一条初始化记录
	 * @param accountUuid
	 * @author:huangyuehong
	 * @Date:2015年10月12日 上午9:40:58
	 */
	@Transactional
	public AccountsForumProfile addByAccountUuid(String accountUuid, Long accountId){
		if(accountId == null || accountId <= 0) {
			accountId = accountsService.getAccountIdByUuid(accountUuid);
			if(accountId <= 0) {
				throw new ServiceException(CodeStatus.USER_NO_EXISTS);
			}
		}
		return accountsForumProfileDao.merge(this.createDefault(accountUuid, accountId));
	}

	/**
	 * 根据accountUuid查询数据库，可以指定查询字段
	 * @param fields 返回的查询字段
	 * @param accountUuids 用户uuid集合
	 * @param master 是否查主库 true:是; false:否;
	 * @return
	 */
	protected List<Map<String, Object>> getFieldByAccountUuidFromDb(Collection<String> fields, Collection<String> accountUuids, boolean master) {
		Sql sql = Sql.build(AccountsForumProfile.class, fields).in("account_uuid", accountUuids);
		if(master) {
			sql = sql.masterDB();
		}
		return accountsForumProfileDao.queryForList(sql);
	}

	public AccountsForumProfile findOrCreate(String accountUuid) {
		AccountsForumProfile accountsForumProfile = accountsForumProfileDao.findByAccountUuid(accountUuid);
		if(accountsForumProfile == null) {
			Long accountId = accountsService.getAccountIdByUuid(accountUuid);
			accountsForumProfile = this.createDefault(accountUuid, accountId);
		}
		return accountsForumProfile;
	}

	private AccountsForumProfile createDefault(String uuid, Long accountId) {
		AccountsForumProfile accountsForumProfile = new AccountsForumProfile();
		accountsForumProfile.setAccount_uuid(uuid);
		accountsForumProfile.setAccount_id(accountId);
		accountsForumProfile.setExperience(0L);
		/** 默认新用户等级  0经验，1级 **/
		int levelNumDefault = 1;
		Map<Object, Object> levelInfos = accountsLevelService.getInfoByLevelNum(levelNumDefault, null, 1, "id");
		accountsForumProfile.setAccount_level(Integer.parseInt(levelInfos.get("id").toString()));
		accountsForumProfile.setPeipei_level(1);
		accountsForumProfile.setPeipei_experience(0L);
		accountsForumProfile.setDriver_level("C");
		return accountsForumProfile;
	}

	/**
	 * 批量解绑头衔
	 */
	@Transactional
	public void batchUnbindActor(List<String> accountUuidList) {
		this.accountsForumProfileDao.batchUpdateActor(accountUuidList, 0);
		accountStringRedisTemplate.executePipelined((RedisConnection rc) -> {
			RedisSerializer<String> serializer = accountStringRedisTemplate.getStringSerializer();
			Map<byte[], byte[]> hashValues = new HashMap<>();
			hashValues.put(serializer.serialize("account_actor"), serializer.serialize("0"));
			// 不写缓存 2024.05.17
//			hashValues.put(serializer.serialize(UuidInfoField.DRIVER_LEVEL), serializer.serialize("C"));
			for(String accountUuid : accountUuidList) {
				if(StringUtils.isBlank(accountUuid)) {
					continue;
				}
				rc.hMSet(serializer.serialize(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid.trim())), hashValues);
			}
			return null;
		});
	}

	@Transactional
	public void setAccountActor(String accountUuid, Integer accountActor) {
		this.accountsForumProfileDao.batchUpdateActor(Arrays.asList(accountUuid), accountActor);
	}
}
