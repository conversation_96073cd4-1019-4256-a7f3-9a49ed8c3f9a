package cn.taqu.account.service;

import cn.hutool.core.util.NumberUtil;
import cn.taqu.account.common.UserField;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.model.AccountsPhoto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */

@Slf4j
@Service
public class AccountInformationService {

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private AccountsPhotoService accountsPhotoService;

    public Map<String, Object> getAccountPhotoFaceStatus(String uuid) {
        Map<String, Object> res = new HashMap<>();

        String redisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid);
        List<Object> values = accountStringRedisTemplate.opsForHash().multiGet(redisKey, Arrays.asList(UserField.Avatar.PERSON_FACE_AVATAR, UserField.Def.GENDER));
        Object personFaceStatus = values.get(0);
        Object sexType = values.get(1);

        if (sexType != null && NumberUtil.isNumber(sexType.toString())) {
            res.put(UserField.Def.GENDER, Integer.parseInt(sexType.toString()));
        } else {
            res.put(UserField.Def.GENDER, 1);
        }

        log.info("getAccountPhotoFaceStatus personFaceStatus: {}", personFaceStatus);

        if (personFaceStatus == null || Integer.parseInt(personFaceStatus.toString()) == 0) {
            res.put(UserField.Avatar.PERSON_FACE_AVATAR_CERTIFICATION, -1);
            return res;
        }

        if (Integer.parseInt(personFaceStatus.toString()) == 1) {
            AccountsPhoto accountAvatar = accountsPhotoService.getAccountAvatar(uuid, null, false);
            log.info("getAccountPhotoFaceStatus accountAvatar: {}", accountAvatar);

            if (AccountsPhoto.Status.PASS.getValue() == accountAvatar.getStatus()) {
                res.put(UserField.Avatar.PERSON_FACE_AVATAR_CERTIFICATION, 1);
                return res;
            }

            if (accountAvatar.verifyPhotoCheckingStatus()) {
                res.put(UserField.Avatar.PERSON_FACE_AVATAR_CERTIFICATION, 0);
                return res;
            }
        }

        res.put(UserField.Avatar.PERSON_FACE_AVATAR_CERTIFICATION, -1);
        return res;
    }
}
