package cn.taqu.account.service;

import cn.taqu.account.common.CommonEnableStatus;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class PrivacyService {

    //  'http://taqu-user-privacy-setting.test.hbmonitor.com/v1/privacy/mGetByUsers'
    public static String REQ_URL;

    @Autowired
    private RestTemplate restTemplate;

    public boolean getAccountPrivacyStatus(String uuid) {
        boolean result = true;
        if (StringUtils.isBlank(uuid)) {
            return result;
        }
        Map<String, Object> formMap = Maps.newHashMap();
        formMap.put("users", uuid);
        formMap.put("item", "hide_distance");
        String reqUrl = REQ_URL + "/v1/privacy/mGetByUsers";
        try {
            String body = mGetByUsers(formMap, reqUrl);
            log.info("getAccountPrivacyStatus req: {} result: {}, uuid: {}", REQ_URL, body, JsonUtils.objectToString(formMap));
            Map<String, Object> map = JsonUtils.stringToObject(body, new TypeReference<Map<String, Object>>() {
            });
            Map<String, String> settings = MapUtils.getMap(map, "settings");
            if (MapUtils.isEmpty(settings)) {
                return result;
            }
            Integer uuidStatus = MapUtils.getInteger(settings, uuid);
            if (Objects.equals(CommonEnableStatus.DISABLE.getStatus(), uuidStatus)) {
                result = false;
            }
        } catch (Exception e) {
            log.warn("getAccountPrivacyStatus fail, reqUrl: {}, req: {}", REQ_URL, JsonUtils.objectToString(formMap), e);
        }
        return result;

    }
    private String mGetByUsers(Map<String,Object> formMap,String reqUrl) {
            HttpHeaders headers=buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(formMap, headers);
            String result = restTemplate.postForObject(reqUrl, entity, String.class);
            return result;
    }


    private HttpHeaders buildHeaders() {
        SoaBaseParams soaBasic = SoaBaseParams.fromThread();
        HttpHeaders headers = new HttpHeaders();
        headers.set("timestamp", StringUtil.nullNumberToEmptyString(soaBasic.getTimestamp()));
        headers.set("app_version", StringUtil.nullNumberToEmptyString(soaBasic.getApp_version()));
        headers.set("channel", StringUtil.nullToEmptyWithTrim(soaBasic.getChannel()));
        headers.set("appcode", StringUtil.nullNumberToEmptyString(soaBasic.getAppcode()));
        headers.set("alias", StringUtil.nullToEmptyWithTrim(soaBasic.getAlias()));
        headers.set("gender", StringUtil.nullNumberToEmptyString(soaBasic.getGender()));
        headers.set("token", StringUtil.nullToEmptyWithTrim(soaBasic.getToken()));
        headers.set("access", StringUtil.nullToEmptyWithTrim(soaBasic.getAccess()));
        headers.set("platform_name", StringUtil.nullToEmptyWithTrim(soaBasic.getPlatform_name()));
        headers.set("platform_id", StringUtil.nullNumberToEmptyString(soaBasic.getPlatform_id()));
        headers.set("ip", StringUtil.nullToEmptyWithTrim(soaBasic.getIp()));
        headers.set("cloned", StringUtil.nullNumberToEmptyString(soaBasic.getCloned()));
        headers.set("longitude", StringUtil.nullToEmptyWithTrim(soaBasic.getLongitude()));
        headers.set("latitude", StringUtil.nullToEmptyWithTrim(soaBasic.getLatitude()));
        headers.set("log_origin", StringUtil.nullToEmptyWithTrim(soaBasic.getOrigin()));
        headers.set("offset_time", StringUtil.nullNumberToEmptyString(soaBasic.getNextOffsetTimeMills()));
        headers.set("distinctRequestId", StringUtil.nullToEmptyWithTrim(soaBasic.getDistinctRequestId()));
        headers.set("env", StringUtil.nullToEmptyWithTrim(soaBasic.getEnv()));
        headers.set("origin_system", StringUtil.nullToEmptyWithTrim(soaBasic.getOriginSystem()));
        headers.set("ctime", soaBasic.getCtime());
        headers.set("client_uri", StringUtil.nullToEmptyWithTrim(soaBasic.getClientUri()));
        headers.set("city", StringUtil.nullNumberToEmptyString(soaBasic.getCity()));
        headers.set("city_name", StringUtil.nullToEmptyWithTrim(soaBasic.getCity_name()));
        headers.set("noretry", StringUtil.nullNumberToEmptyString(soaBasic.getNoretry()));
        headers.set("distinctRequestId", SoaBaseParams.fromThread().getDistinctRequestId());
        headers.set("origin_system", SoaBaseParams.fromThread().getOriginSystem());
        headers.set("Accept", "*/*");
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent","Apifox/1.0.0 (https://www.apifox.cn)");
        headers.set("Connection","keep-alive");
        return headers;
    }
}
