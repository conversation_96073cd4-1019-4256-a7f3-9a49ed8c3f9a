package cn.taqu.account.service;


import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.taqu.core.utils.RegExp;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/22.
 */
@Service
public class CommonService {

    /**
     * 过滤特殊字符
     * @param original 原始字符串
     * @return
     */
    public static String filterSpecialCharacter(String original) {
        if(StringUtils.isBlank(original)) {
            return "";
        }
        char[] arr = original.toCharArray();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < arr.length; i++) {
            if(isValidChar(arr[i])) {
                sb.append(arr[i]);
            }
        }
        return sb.toString();
    }

    /**
     * 个人简介不允许特殊字符
     * @param ch
     * @return
     */
    public static boolean isValidChar(char ch) {
        if ((ch >= '0' && ch <= '9') || (ch >= 'A' && ch <= 'Z')|| (ch >= 'a' && ch <= 'z'))
            return true;
        if (isChineseByScript(ch))
            return true;// 简体中文汉字编码
        if (RegExp.contains("\\pP", Character.toString(ch)))
            return true;// 中英文标点符号
        return false;
    }

    //使用UnicodeScript方法判断
    public static boolean isChineseByScript(char c) {
        Character.UnicodeScript sc = Character.UnicodeScript.of(c);
        if (sc == Character.UnicodeScript.HAN) {
            return true;
        }
        return false;
    }
}
