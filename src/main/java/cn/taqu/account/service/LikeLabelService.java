package cn.taqu.account.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.LikeLabelDao;
import cn.taqu.account.model.LikeLabel;
import cn.taqu.account.search.LikeLabelSearch;
import cn.taqu.account.vo.CommonPage;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;

@Service
public class LikeLabelService {
	private static final Logger LOGGER = LoggerFactory.getLogger(LikeLabelService.class);
	@Autowired
	private LikeLabelDao likeLabelDao;
	@Autowired
	private QiniuService qiniuService;
	@Autowired
	@Qualifier("accountStringRedisTemplate")
	private StringRedisTemplate accountStringRedisTemplate;

	public CommonPage<LikeLabel> pageQuery(LikeLabelSearch search, Integer pageNumber, Integer pageSize) {
		Sql sql = Sql.build(LikeLabel.class, LikeLabelDao.FIELDS_ALL);
		if(search != null ) {
			if(StringUtils.isNotBlank(search.getName())) {
				sql = sql.like("name", search.getName());
			}
			if(search.getStatus() != null) {
				sql = sql.eq("status", search.getStatus());
			}
			if(search.getType() != null) {
				sql = sql.eq("type", search.getType());
			}
		}
		PageData<LikeLabel> page = likeLabelDao.queryForPage(sql, pageNumber, pageSize);

		CommonPage<LikeLabel> commonPage = new CommonPage<>();
		commonPage.setNowpage(pageNumber);
		commonPage.setPagesize(pageSize);
		commonPage.setRows(page.getData());
		commonPage.setTotal(page.getTotal().longValue());

		return commonPage;
	}

	@Transactional
	public Long create(LikeLabel likeLabel) {
		if(likeLabel == null) {
			throw new ServiceException("参数异常");
		}
		Integer type = likeLabel.getType();
		String name = likeLabel.getName();
		String picUrl = likeLabel.getPicUrl();
		Integer status = likeLabel.getStatus();

		if (type == null || StringUtils.isBlank(name) || StringUtils.isBlank(picUrl) || status == null ) {
			LOGGER.warn("创建喜欢类型，传入参数不完整。【{}】", JsonUtils.objectToString(likeLabel));
			throw new ServiceException("传入参数不完整");
		}
		if(!LikeLabel.Type.isIn(type)) {
			throw new ServiceException("传入类型异常");
		}
		if(!LikeLabel.Status.isIn(status)) {
			throw new ServiceException("传入状态异常");
		}

        Map<String, Object> props = new HashMap<>();
        props.put("type", type);
        props.put("name", name);

		List<LikeLabel> queryList = likeLabelDao.queryByProps(props, Lists.newArrayList("id"));
		if(queryList != null && !queryList.isEmpty()) {
			throw new ServiceException("分类下已存在该标签");
		}

		likeLabel.setExtra(StringUtils.defaultString(likeLabel.getExtra(), ""));
		likeLabel.setUnqId("");
		Long currentTimeSeconds = DateUtil.currentTimeSeconds();
		likeLabel.setCreateTime(currentTimeSeconds);
		likeLabel.setUpdateTime(currentTimeSeconds);
		likeLabel = likeLabelDao.merge(likeLabel);

		updateCache(likeLabel);
		return likeLabel.getId();
	}

	public LikeLabel getById(Long id) {
		return likeLabelDao.get(id);
	}

	@Transactional
	public void update(LikeLabel likeLabel) {
		if(likeLabel == null || likeLabel.getId() == null) {
			throw new ServiceException("参数异常");
		}
		Long id = likeLabel.getId();
		Integer type = likeLabel.getType();
		String name = likeLabel.getName();
		String picUrl = likeLabel.getPicUrl();
		Integer status = likeLabel.getStatus();

		if (type == null || StringUtils.isBlank(name) || StringUtils.isBlank(picUrl) || status == null ) {
			LOGGER.warn("创建喜欢类型，传入参数不完整。【{}】", JsonUtils.objectToString(likeLabel));
			throw new ServiceException("传入参数不完整");
		}
		if(!LikeLabel.Type.isIn(type)) {
			throw new ServiceException("传入类型异常");
		}
		if(!LikeLabel.Status.isIn(status)) {
			throw new ServiceException("传入状态异常");
		}

		LikeLabel likeLabelSel = getById(id);
		if(likeLabelSel == null) {
			throw new ServiceException("该标签不存在");
		}
        if(!Objects.equals(type, likeLabelSel.getType())) {
        	throw new ServiceException("标签类型不允许修改");
        }

        likeLabelSel.setName(name);
        likeLabelSel.setPicUrl(picUrl);
        likeLabelSel.setStatus(status);
        likeLabelSel.setExtra(StringUtils.defaultString(likeLabel.getExtra(), ""));
        likeLabelSel.setUpdateTime(DateUtil.currentTimeSeconds());

        likeLabelSel = likeLabelDao.merge(likeLabelSel);

        if(Objects.equals(status, LikeLabel.Status.UNUSE.value)) {
            SpringContextHolder.getBean(AccountsLikeLabelService.class).deleteAccountsLikeLabel(id);
        }
        updateCache(likeLabelSel);
	}

	@Transactional
	public void deleteById(Long id) {
		likeLabelDao.removeById(id);
		SpringContextHolder.getBean(AccountsLikeLabelService.class).deleteAccountsLikeLabel(id);
		deleteCache(id);
	}

	private void updateCache(LikeLabel likeLabel) {
		if(likeLabel != null ) {
			if(likeLabel.getExtra() == null) {
				likeLabel.setExtra("");
			}
			if(likeLabel.getUnqId() == null) {
				likeLabel.setUnqId("");
			}
			accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.LIKE_LABEL_DATA.setArg(likeLabel.getId()), JsonUtils.objectToString2(likeLabel), 7, TimeUnit.DAYS);
    	}else {
    		LOGGER.warn("传入的LikeLabel为null");
    	}
    }

	private void deleteCache(Long id) {
		accountStringRedisTemplate.delete(RedisKeyConstant.LIKE_LABEL_DATA.setArg(id));
	}

	public PageData<LikeLabel> listLikeLabelPage(Integer type, Integer page, String keyWord){
		Sql sql = Sql.build(LikeLabel.class, LikeLabelDao.FIELDS_ALL);
		sql.eq("type", type).eq("status", LikeLabel.Status.USE.value).like("name", keyWord);
		PageData<LikeLabel> pageData = likeLabelDao.queryForPage(sql, page, 20);

		return pageData;
	}

	@SuppressWarnings("deprecation")
	public List<LikeLabel> listUseByIds(Integer type, Set<Long> labelIds) {
		// 先从缓存取
		ValueOperations<String, String> opsForValue = accountStringRedisTemplate.opsForValue();

		List<LikeLabel> likeLabelList = Lists.newArrayList();
		// 遍历id 取缓存
		for (Long id : labelIds) {
			LikeLabel likeLabel = null;
			String json = opsForValue.get(RedisKeyConstant.LIKE_LABEL_DATA.setArg(id));
			if(StringUtils.isNotBlank(json) ) {
				try {
					likeLabel = JsonUtils.stringToObject2(json, new TypeReference<LikeLabel>() {});
				} catch (Exception e) {
					LOGGER.warn("从缓存获取LikeLabel异常，json={}",json,e);
				}
				if(likeLabel != null) {
					if (Objects.equals(likeLabel.getStatus(), LikeLabel.Status.USE.value)) {
						likeLabelList.add(likeLabel);
						continue;
					}
				}else {
					// 查数据库写缓存
					likeLabel = likeLabelDao.get(id);
				}
			}else {
				// 查数据库写缓存
				likeLabel = likeLabelDao.get(id);
			}
			// 走到这里说明缓存没值，反写缓存
			if(likeLabel != null) {
				updateCache(likeLabel);
				if (Objects.equals(likeLabel.getStatus(), LikeLabel.Status.USE.value)) {
					likeLabelList.add(likeLabel);
					continue;
				}
			}
		}
		return likeLabelList;
	}

	@Transactional
	public void uploadJson(String json) {
		List<Map<String,String>> list = JsonUtils.stringToObject(json, new TypeReference<List<Map<String, String>>>() {});
		for (Map<String, String> map : list) {
			String name = map.get("name");
			String url = map.get("url");

			LikeLabel likeLabel = new LikeLabel();
			likeLabel.setName(name);
			likeLabel.setPicUrl(url);
			likeLabel.setType(LikeLabel.Type.TV.value);
			likeLabel.setStatus(LikeLabel.Status.USE.value);

			createUplad(likeLabel,"tv");
		}
	}

	@Transactional
	public void uploadJsonStar(String json) {
		List<Map<String,String>> list = JsonUtils.stringToObject(json, new TypeReference<List<Map<String, String>>>() {});
		for (Map<String, String> map : list) {
			String name = map.get("name");
			String url = map.get("url");

			LikeLabel likeLabel = new LikeLabel();
			likeLabel.setName(name);
			likeLabel.setPicUrl(url);
			likeLabel.setType(LikeLabel.Type.STAR.value);
			likeLabel.setStatus(LikeLabel.Status.USE.value);

			createUplad(likeLabel, "star");
		}
	}

	@Transactional
	public void uploadJsonGame(String json) {
		List<Map<String,String>> list = JsonUtils.stringToObject(json, new TypeReference<List<Map<String, String>>>() {});
		for (Map<String, String> map : list) {
			String name = map.get("name");
			String url = map.get("url");

			LikeLabel likeLabel = new LikeLabel();
			likeLabel.setName(name);
			likeLabel.setPicUrl(url);
			likeLabel.setType(LikeLabel.Type.GAME.value);
			likeLabel.setStatus(LikeLabel.Status.USE.value);

			createUplad(likeLabel ,"game");
		}
	}

	public void createUplad(LikeLabel likeLabel, String dir) {
		if(likeLabel == null) {
			return;
		}
		Integer type = likeLabel.getType();
		String name = likeLabel.getName();
		String picUrl = likeLabel.getPicUrl();
		Integer status = likeLabel.getStatus();

		Map<String, Object> props = new HashMap<>();
        props.put("type", likeLabel.getType());
        props.put("name", name);
		List<LikeLabel> queryList = likeLabelDao.queryByProps(props, Lists.newArrayList("id"));
		// 判断已经有了就不添加
		if(queryList != null && !queryList.isEmpty()) {
			return;
		}
		String customImageUrl = getCustomImageUrl(picUrl, dir);
		if(customImageUrl == null) {
			return;
		}
		likeLabel.setPicUrl(customImageUrl);

		if (type == null || StringUtils.isBlank(name) || StringUtils.isBlank(picUrl) || status == null ) {
			LOGGER.warn("创建喜欢类型，传入参数不完整。【{}】", JsonUtils.objectToString(likeLabel));
			return;
		}
		if(!LikeLabel.Type.isIn(type)) {
			return;
		}
		if(!LikeLabel.Status.isIn(status)) {
			return;
		}

		likeLabel.setExtra(StringUtils.defaultString(likeLabel.getExtra(), ""));
		likeLabel.setUnqId("");
		Long currentTimeSeconds = DateUtil.currentTimeSeconds();
		likeLabel.setCreateTime(currentTimeSeconds);
		likeLabel.setUpdateTime(currentTimeSeconds);
		likeLabel = likeLabelDao.merge(likeLabel);

		updateCache(likeLabel);
	}

	private String getCustomImageUrl(String url, String dir) {
        //bucket后面可以修改，现在暂时使用该bucket
        return qiniuService.uploadPic("forumimg01", dir, url);
    }
}
