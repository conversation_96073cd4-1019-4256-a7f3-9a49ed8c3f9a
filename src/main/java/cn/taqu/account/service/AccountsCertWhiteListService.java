package cn.taqu.account.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import cn.taqu.account.dao.AccountsCertWhiteListDao;
import cn.taqu.account.model.AccountsCertWhiteList;
import cn.taqu.core.orm.PageData;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-15 16:09
 */
@Deprecated
@Service
@Slf4j
@Transactional
public class AccountsCertWhiteListService {

    @Autowired
    private AccountsCertWhiteListDao accountsCertWhiteListDao;
    @Autowired
    private AccountsInfoService accountsInfoService;

    /**
     * 获取分页 认证白名单
     * @param pageNum
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param accountUuid
     * @param whiteListFrom
     * @return
     */
    public PageData<AccountsCertWhiteList> pageCertWhiteList(Integer pageNum, Integer pageSize, Long startTime, Long endTime, String accountUuid, Integer whiteListFrom){
        List<AccountsCertWhiteList> list = accountsCertWhiteListDao.list(pageNum, pageSize, startTime, endTime, accountUuid, whiteListFrom);
        List<String> uuids = list.stream().map(AccountsCertWhiteList::getAccountUuid).collect(Collectors.toList());
        Map<String, Map<String, Object>> userInfoMap = accountsInfoService.getInfoByUuid(uuids.toArray(new String[]{}), new String[]{"account_name", "avatar", "sex_type"}, "1", false, false);
        PageData<AccountsCertWhiteList> pageData = new PageData<>();
        for (AccountsCertWhiteList record : list) {
            Map<String, Object> infoMap = userInfoMap.get(record.getAccountUuid());
            record.setAccountName(MapUtils.getString(infoMap, "account_name", ""));
            record.setAvatar(MapUtils.getString(infoMap, "avatar", ""));
            record.setSexType(MapUtils.getInteger(infoMap, "sex_type", 0));
        }
        pageData.setPageSize(pageSize);
        pageData.setPage(pageNum);
        if(CollectionUtils.isEmpty(list)){
            pageData.setTotal(Integer.MAX_VALUE);
        }else{
            pageData.setData(list);
            pageData.setTotal(Integer.MAX_VALUE);
        }
        return pageData;
    }


    /**
     * 批量添加白名单
     * @param datas
     * @param loginname
     * @return
     */
    public Map<String, Object> batchCertWhiteList(List<String> datas, String loginname) {
        Map<String, Object> result = new HashMap<>();
        result.put("errorUuids", new ArrayList<>());
        result.put("successSize", 0);
        result.put("errorSize", 0);
        return result;
    }


}
