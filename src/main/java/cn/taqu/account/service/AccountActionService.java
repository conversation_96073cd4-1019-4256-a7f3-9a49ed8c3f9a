package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.common.LogoutTypeEnum;
import cn.taqu.account.dao.LoginActionInfoDao;
import cn.taqu.account.dao.LogoutActionInfoLogDao;
import cn.taqu.account.dao.RegisterActionInfoDao;
import cn.taqu.account.model.LoginActionInfo;
import cn.taqu.account.model.LogoutActionInfoLog;
import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
public class AccountActionService {
    @Autowired
    private LoginActionInfoDao loginActionInfoDao;
    @Autowired
    private LogoutActionInfoLogDao logoutActionInfoLogDao;
    @Autowired
    private RegisterActionInfoDao registerActionInfoDao;

    @Autowired
    private RedisLockUtil redisLockUtil;

    private String ACCOUNT_ACTION_LOGIN_INFO_LOCK = "accountActionLoginInfoLock:${accountUuid}";

    /**
     * 如果登录行为信息已存在,则更新,否则新增.
     * @param loginActionInfo
     */
    public void merge(LoginActionInfo loginActionInfo) {
        String lockKey = ACCOUNT_ACTION_LOGIN_INFO_LOCK.replace("${accountUuid}", loginActionInfo.getAccountUuid());
        redisLockUtil.executeWithLock(lockKey, () -> {
            Long idByAccountUuid = loginActionInfoDao.getIdByAccountUuid(loginActionInfo.getAccountUuid());
            loginActionInfo.setId(idByAccountUuid);
            return loginActionInfoDao.merge(loginActionInfo);
        });
    }

    /**
     * 主动登出（可以通过SoaBaseParams获取到参数）
     * @param accountUuid
     * @param logoutTypeEnum
     */
    public void activeLogout(String accountUuid, LogoutTypeEnum logoutTypeEnum){
        SoaBaseParams soaBasicJava = RequestParams.getSoa_basic_java();
        LogoutActionInfoLog logoutActionInfoLog = new LogoutActionInfoLog();
        logoutActionInfoLog.setAccountUuid(accountUuid);
        logoutActionInfoLog.setToken(soaBasicJava.getToken());
        logoutActionInfoLog.setActionIp(soaBasicJava.getIp());
        logoutActionInfoLog.setAppcode(soaBasicJava.getAppcode());
        logoutActionInfoLog.setCloned(soaBasicJava.getCloned());
        logoutActionInfoLog.setPlatformId(soaBasicJava.getPlatform_id());
        logoutActionInfoLog.setChannel(soaBasicJava.getChannel());
        logoutActionInfoLog.setAppVersion(soaBasicJava.getApp_version());
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        logoutActionInfoLog.setCreateTime(currentTimeSeconds);
        logoutActionInfoLog.setUpdateTime(currentTimeSeconds);
        logoutActionInfoLog.setLogoutType(logoutTypeEnum.getValue());
        logoutActionInfoLog.setPlatformName(soaBasicJava.getPlatform_name());
        logoutActionInfoLog.setAccess(soaBasicJava.getAccess());
        logoutActionInfoLog.setAlias(soaBasicJava.getAlias());
        logoutActionInfoLog.setGender(soaBasicJava.getGender());
        if(ToolsService.logoutActionInfoLogSwitchWrite.isOn(true)) {
            logoutActionInfoLog.setLongitude(soaBasicJava.getLongitude());
            logoutActionInfoLog.setLatitude(soaBasicJava.getLatitude());
        }else {
            log.info("logout_action_info_log不写加密字段");
            logoutActionInfoLog.setLongitude("");
            logoutActionInfoLog.setLatitude("");
        }
        logoutActionInfoLog.setLongitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLongitude()));
        logoutActionInfoLog.setLatitudeCipher(EncryptUtil.encrypt(soaBasicJava.getLatitude()));
        logoutActionInfoLog.setCity(soaBasicJava.getCity());
        this.merge(logoutActionInfoLog);
    }

    /**
     * 新增登出行为信息
     * @param logoutActionInfoLog
     */
    public void merge(LogoutActionInfoLog logoutActionInfoLog){
        logoutActionInfoLogDao.merge(logoutActionInfoLog);
    }

    /**
     * 新增注册行为信息
     * @param registerActionInfo
     */
    public void merge(RegisterActionInfo registerActionInfo) {
        registerActionInfoDao.merge(registerActionInfo);
    }

}
