package cn.taqu.account.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.sms.BusinessSmsReq;
import cn.taqu.account.dto.sms.TemplateDto;
import cn.taqu.account.dto.sms.TemplateParamDto;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/15.Ø
 */
@Service
public class MessageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageService.class);

//    public static String RECHARGE_TIP = "点击下方链接或关注公众号「taqu668」进行趣豆充值可享受更多折扣 https://j.mp/3ePdAku";

    /**
     * 发送系统通知
     * @param accountUuid 接收通知者的UUID
     * @param content 发送的系统通知消息主体 带图片的传List，不是图片的传非数组
     * @param pushContent 发送的推送内容，如果不为空，还会发起一条推送，推送该内容
     * @param type 发送的通知消息类型，默认为text
     * @param appcode 应用码 1:他趣
     */
    public static void systemNotice(String accountUuid, Object content, Object pushContent, String type, Integer appcode) {
        if(StringUtils.isBlank(accountUuid)) {
            LOGGER.warn("推用系统通知到TQMQ自动停止，因为用户uuid为空");
            return;
        }

        Map<String, Object> message = Maps.newHashMap();
        message.put("to_user_id", accountUuid);
        message.put("content", content);
        message.put("push_content", pushContent);
        message.put("type", StringUtils.isBlank(type) ? "text" : type);
        message.put("appcode", ToolsService.normalizedAppcode(appcode));

       /* MqClient mqClient = new MqClient("http://g2.test.k8s.taqu.cn/v2/Soa/jService");
        MqResponse mqResponse = mqClient.push("system_notice", message, null);*/

        MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_SYSTEM_NOTICE, message, null);
        if(mqResponse.fail()) {
            LOGGER.error("添加系统通知到TQMQ失败, 失败码:{}, 失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
        } else {
            LOGGER.info("发送系统通知成功, {}", JsonUtils.objectToString(message));
        }
    }

    /**
     * 发送钉钉通知
     * @param type
     * @param toUser
     * @param title
     * @param content
     */
    public static void sendDingDingMsgText(String toUser, String title, String content){
        try{
            Map<String, String> msg = Maps.newHashMap();
            msg.put("type", "text");
            msg.put("toUser", toUser);
            msg.put("title", title);
            msg.put("content", content);
            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_ASYNC_DINGTALK_MSG, msg, null);
            if(mqResponse.fail()) {
                LOGGER.error("钉钉工作消息link推入TQMQ队列失败, 失败码:{}, 失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
            } else {
                LOGGER.info("发送系统通知成功, {}", JsonUtils.objectToString(msg));
            }
        }catch (Exception e){
            LOGGER.warn("钉钉工作消息link推入TQMQ队列失败", e);
        }
    }

    /**
     * 发短信
     * @param cloned
     * @param content
     * @param phone
     * @param tagCode
     * @param template
     * @param templateFlag
     */
    public static void processBusinessSms(Integer cloned, String content, String phone, String tagCode, TemplateDto template, Integer templateFlag) {
        Map<String, Object> msgInfo = new HashMap<>();
        msgInfo.put("service", "businessSms");
        msgInfo.put("method", "processBusinessSms");
        msgInfo.put("distinctRequestId", SoaBaseParams.fromThread().getDistinctRequestId());
        
        try{
            BusinessSmsReq businessSmsReq = new BusinessSmsReq();
            businessSmsReq.setAppcode(CommConst.APPCODE_TAQU);
            businessSmsReq.setCloned(cloned);
            businessSmsReq.setContent(content);
            businessSmsReq.setPhone(phone);
            businessSmsReq.setTagCode(tagCode);
            businessSmsReq.setTemplate(template);
            businessSmsReq.setTemplateFlag(templateFlag);
            msgInfo.put("asyncforms", businessSmsReq);
            
            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_MP_SMS_ASYNC_INVOKE_QUEUE, msgInfo, null);
            if(mqResponse.fail()) {
                LOGGER.error("短信推入TQMQ队列失败, 失败码:{}, 失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
            } else {
                LOGGER.info("发送短信成功，json={}", JsonUtils.objectToString(msgInfo));
            }
        }catch (Exception e){
            LOGGER.warn("发送短信失败，json={}", JsonUtils.objectToString(msgInfo), e);
        }
    }
    
    /**
     * 
     * @param cloned
     * @param phone
     * @param tagCode
     * @param templateCode
     * @param templateParamList
     */
    public static void processBusinessSmsUseTemplate(Integer cloned, String phone, String tagCode, String templateCode, List<TemplateParamDto> templateParamList) {
        TemplateDto templateDto = new TemplateDto();
        templateDto.setTemplateCode(templateCode);
        templateDto.setParams(templateParamList);
        processBusinessSms(cloned, null, phone, tagCode, templateDto, CommConst.YES_1);
    }
}
