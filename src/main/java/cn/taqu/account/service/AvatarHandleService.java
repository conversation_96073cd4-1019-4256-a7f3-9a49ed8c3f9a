package cn.taqu.account.service;

import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cn.taqu.account.constant.AvatarConst;
import cn.taqu.account.constant.CommConst;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;

/**
 * 头像处理的帮助类
 * <AUTHOR>
 * 2017年8月2日 上午11:24:41
 */
@Component
public class AvatarHandleService {

	private static final Logger LOGGER = LoggerFactory.getLogger(AvatarHandleService.class);

	@Value("${avatar.url}")
	public String defaultAvatarHost = CommConst.AVATAR_HOST;//https://avatar01.jiaoliuqu.com/

	/**
	 * 路径分隔符  "/"
	 */
	public static final String SPT = "/";

	/**
	 * http://
	 */
	public static final String HTTP = "http://";

	/**
	 * https://
	 */
	public static final String HTTPS = "https://";

	/**
	 * 根据版本获取头像，如果传入为null或""，直接原样返回
	 * @param url
	 * @param version 0或空: 返回全路径; 其他: 返回不带host的uri
	 */
	public String getAvatarByVersion(String url, String version) {
		if(StringUtils.isBlank(url)) {
			return url;
		}

		//判断
		if(StringUtils.isBlank(version) || Objects.equals("0", version)) {
			if(!isURL(url)){
				//获取原路径
				url = defaultAvatarHost + removeSPT(url);
			}

			return url;
		}
		String uri;
		//截取后路径
		try {
			URL _url = new URL(url);
			uri = _url.getFile();
		} catch (Exception e) {
			uri = url;
		}
		return removeSPT(uri);
	}

	/**
	 * 处理两个域名的情况 https://avatar01.jiaoliuqu.com/avatar01.jiaoliuqu.com/11.jpg
	 * @param url
	 * @return
	 */
	public static String removeDoubleDomain(String url){
		if(StringUtils.isBlank(url)) {
			return url;
		}
		url = getAvatarOfSavePhoto(url);
		if(StringUtils.isBlank(url)) {
			return url;
		}
		url = getAvatarOfSavePhoto(url);
		return url;
	}

	/**
	 * 获取存redis的url，无域名，带斜杠
	 * @param url
	 * @return
	 * <AUTHOR>
	 * 2017年8月2日 下午12:06:12
	 */
	public static String getAvatarOfSaveRedis(String url) {
		if(StringUtils.isBlank(url)) {
			return url;
		}

		try {
			URL _url = new URL(url);
			url =  _url.getFile();
		} catch (Exception e) {

		}

		return addSPT(url);
	}
	
	/**
	 * 获取存相册的url，无域名，无斜杠
	 * @param url
	 * @return
	 * <AUTHOR>
	 * 2017年8月2日 下午12:06:12
	 */
	public static String getAvatarOfSavePhoto(String url) {
		if(StringUtils.isBlank(url)) {
			return url;
		}

		try {
			URL _url = new URL(url);
			url =  _url.getFile();
		} catch (Exception e) {
		}

		return removeSPT(url);
	}

	public static String addSPT(String url){
		if(!isURL(url) && !url.startsWith(SPT)){
			url = SPT + url;
		}

		return url;
	}

	public static String removeSPT(String url){
		while (url.startsWith(SPT)) {
			url = url.substring(1);
		}
		return url;
	}

	public static boolean isURL(String url){
		if(url.startsWith(HTTP) || url.startsWith(HTTPS)){
			return true;
		}
		return false;
	}

	/**
	 * 是否默认头像的uri
	 * @param avatarUri
	 * @return
	 */
	private static final Pattern DEFAULT_AVATAR_URI = Pattern.compile("^/*avatar/(default/\\d{1,2}|newavatarmale|illegal_avatar|newavatarfemale|new_avatar_asexual|new_avatar_male_v2|new_avatar_female_v2|36536f87ea91a7d6b1986a335aee463c|528f351eb9e8cd2d9fd23972abeb69bb)\\.(jpg$|png$)");
	public static boolean isDefAvatar(String avatarUri) {
		avatarUri = getAvatarOfSavePhoto(avatarUri);
		return StringUtils.isNotBlank(avatarUri) && DEFAULT_AVATAR_URI.matcher(avatarUri).matches();
	}

	/**
	 * 根据性别获取默认头像
	 * 不需要判断 newRegister202306
	 */
	@Deprecated
	public static String getDefAvatar(boolean newRegister202306, Integer gender) {
		return Objects.equals(gender, 2) ? AvatarConst.getDefaultFemaleAvatar(newRegister202306) :
				(Objects.equals(gender, 1) ? AvatarConst.getDefaultMaleAvatar(newRegister202306) : AvatarConst.DEFAULT_ASEXUAL_AVATAR);
	}
	
	/**
	 * 根据性别获取默认头像
	 */
	public static String getDefAvatar(Integer gender) {
	    return Objects.equals(gender, 2) ? AvatarConst.DEFAULT_FEMALE_AVATAR : AvatarConst.DEFAULT_MALE_AVATAR;
	}

	//删除云空间图片
	public void deletePhotoFile(List<String> urlList, String accountUuid) {
		if(CollectionUtils.isNotEmpty(urlList) && StringUtils.isNotBlank(accountUuid)){
			Map<String, Object> data = new HashMap<>();
			data.put("content_uuid", accountUuid.trim());
			data.put("type", "1");
			data.put("pic_url", urlList);
			MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("remove_picture", data, null);
			if(mqResponse.fail()) {
				LOGGER.error("删除照片失败：{}，urls：{}" + JsonUtils.objectToString(mqResponse),JsonUtils.objectToString(urlList));
			}
		}
	}

	/**
	 * 获取源站图片地址
	 * @param photoUrl
	 * @return
	 */
	public static String getAvatarSrcPhotoUrl(String photoUrl){
		photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
		photoUrl = CommConst.AVATAR_SRC_HOST + photoUrl;
		return photoUrl;
	}
	
	/**
	 * 获取图片地址
	 * @param photoUrl
	 * @return
	 */
	public static String getAvatarPhotoUrl(String photoUrl){
	    photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
	    photoUrl = CommConst.AVATAR_HOST + photoUrl;
	    return photoUrl;
	}
	
	/**
	 * 去掉问号后的参数
	 * 
	 * @param url
	 * @return
	 */
	public static String removeParams(String url) {
	    if(StringUtils.isBlank(url)) {
	        return url;
	    }
	    int indexOf = url.indexOf("?");
	    if(indexOf != -1) {
	        return url.substring(0, indexOf);
	    }
	    
	    return url;
	}
	
}
