package cn.taqu.account.service;

import java.util.List;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import cn.taqu.account.dao.ReRegisterWhitelistDao;
import cn.taqu.account.model.ReRegisterWhitelist;
import cn.taqu.account.search.ReRegisterWhiteListSearch;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.dingding.DingTalkHandler;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 再注册白名单服务
 *
 * <AUTHOR>
 * @date 2021/07/21
 */
@Slf4j
@Service
public class ReRegisterWhiteListService {

    @Autowired
    private ReRegisterWhitelistDao reRegisterWhitelistDao;
    @Autowired
    private DingTalkHandler dingTalkHandler;
    private static  final  String title ="再注册白名单添加提醒";


    @Transactional(rollbackOn = Exception.class)
    public void add(ReRegisterWhitelist reRegisterWhitelist){
        validateParams(reRegisterWhitelist);
        reRegisterWhitelist.setStatus(1);
        reRegisterWhitelist.setCreateTime(DateUtil.currentTimeSeconds());
        reRegisterWhitelist.setUpdateTime(0L);
        
        String mobile = reRegisterWhitelist.getMobile();

        reRegisterWhitelist.setMobileCipher(EncryptUtil.encrypt(mobile));
        reRegisterWhitelist.setMobileDigest(EncryptUtil.sm3(mobile));
        if(!ToolsService.reregisterWhitelistSwitchWrite.isOn(true)) {
            log.info("reregister_whitelist不写加密字段");
            reRegisterWhitelist.setMobile("");
        }
        
        reRegisterWhitelistDao.merge(reRegisterWhitelist);
        StringBuilder msg = new StringBuilder().append(reRegisterWhitelist.getMobile().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2")).append(" 被加入注销后再注册白名单").append("\n\n").append("申请原因: ")
                .append(reRegisterWhitelist.getReason()).append("\n\n").append("渠道：").append(reRegisterWhitelist.getSociety())
                .append("\n\n").append("操作人: ").append(reRegisterWhitelist.getOperateName()).append("\n\n").append("时间: ").append(DateUtil.getCurrentTime());
        dingTalkHandler.pushMarkdownMq("",msg.toString(),ReRegisterLimitService.webhookKey,title,new String[]{});
    }

    /**
     * 校验参数
     *
     * @param reRegisterWhitelist 重新注册白名单
     */
    private void validateParams(ReRegisterWhitelist reRegisterWhitelist) {
        if (StringUtils.isBlank(reRegisterWhitelist.getSociety())){
            throw new ServiceException("公会名不能为空。");
        } else if (reRegisterWhitelist.getRegisterType() == 1 && StringUtils.isBlank(reRegisterWhitelist.getMobile())) {
            throw new ServiceException("手机号码不能为空。");
        } else if (reRegisterWhitelist.getRegisterType() == 2 && StringUtils.isBlank(reRegisterWhitelist.getOpenid())) {
            throw new ServiceException("openid不能为空。");
        } else if (StringUtils.isBlank(reRegisterWhitelist.getReason())) {
            throw new ServiceException("理由不能为空。");
        } else if (StringUtils.isBlank(reRegisterWhitelist.getOperateName())) {
            throw new ServiceException("操作人不能为空。");
        }
    }

    /**
     * 分页查询
     *
     * @param search 搜索
     * @return {@link Page<AccountsDestroyLog>}
     */
    public Page<ReRegisterWhitelist> pageQuery(ReRegisterWhiteListSearch search) {
        Long startTime = search.getStartTime() == null ? DateUtil.getTodayBeginSecond() : search.getStartTime();
        Long endTime =search.getEndTime() == null ? DateUtil.getTodayEndSecond() : search.getEndTime();
        Integer status = search.getStatus() == null ? 0:search.getStatus();
        Integer registerType = search.getRegisterType() == null ? 1:search.getRegisterType();
        Sql sql = Sql.build(ReRegisterWhitelist.class);
        sql = sql.ge("create_time", startTime);
        sql = sql.lt("create_time", endTime);
        sql = sql.eq("status",status);
        sql = sql.eq("register_type", registerType);
        sql = sql.orderBy("id desc");
        PageData<ReRegisterWhitelist> page = reRegisterWhitelistDao.queryForPage(sql, search.getPage(), search.getRows());
        List<ReRegisterWhitelist> data = page.getData();
        if(ToolsService.reregisterWhitelistSwitchEncryption.isOn(true)) {
            for (ReRegisterWhitelist reRegisterWhitelist : data) {
                reRegisterWhitelist.setMobile(EncryptUtil.decrypt(reRegisterWhitelist.getMobileCipher()));
            }
        }
        
        return new PageImpl<>(data, PageUtil.newPageable(search.getPage(), search.getRows()), page.getTotal());
    }


    /**
     * 更新状态
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Transactional(rollbackOn = Exception.class)
    public void updateStatusByTime(long startTime, long endTime) {
        reRegisterWhitelistDao.updateStatusByTime(startTime,endTime);
    }
}
