package cn.taqu.account.service;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.soa.ICityAbTestService;
import cn.taqu.account.utils.LogUtil;
import cn.taqu.account.utils.SoaHeaderBuilder;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Service
public class CityAbTestService implements ICityAbTestService {

    // http://city-ab-test.test.hbmonitor.com
    public static String REQ_URL;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private StringRedisTemplate accountBizStringRedisTemplate;

    /**
     * 缓存获取
     *
     * @param uuid
     * @param expCode
     * @return
     */
    @Override
    public Response getUserExpCache(String uuid, String expCode) {
        String key = RedisKeyConstant.AB_TEST_CITY_CACHE.setArg(uuid, expCode);
        String value = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(value)) {
            return JsonUtils.stringToObject(value, Response.class);
        }

        Response response = getUserExp(uuid, expCode);
        accountBizStringRedisTemplate.opsForValue().set(key, JsonUtils.objectToString(response), 30, TimeUnit.SECONDS);
        return response;
    }

    /**
     * 缓存获取
     *
     * @param uuid
     * @param expCode
     * @return
     */
    @Override
    public Response getTargetExpCache(String uuid, String expCode, String cityId, Integer appcode, Integer cloned, Integer platformId, Long appVersion) {
        String key = RedisKeyConstant.OTHERS_AB_TEST_CITY_CACHE.setArg(uuid, expCode, cityId, appcode, cloned, platformId, appVersion);
        String value = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(value)) {
            return JsonUtils.stringToObject(value, Response.class);
        }

        Response response = getTargetExp(uuid, expCode, cityId, appcode, cloned, platformId, appVersion);
        accountBizStringRedisTemplate.opsForValue().set(key, JsonUtils.objectToString(response), 30, TimeUnit.SECONDS);
        return response;
    }

    /**
     * 获取用户实验
     *
     * @param uuid
     * @param expCode
     * @return
     */
    public Response getUserExp(String uuid, String expCode) {
        Map<String, Object> req = new HashMap<>();
        req.put("uuid", uuid);
        req.put("exp_code", expCode);

        Response response;
        try {
            HttpHeaders headers = SoaHeaderBuilder.buildHeadersFromSoaParams();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(req, headers);
            response = restTemplate.postForObject(REQ_URL + "/v1/cab/getResult", entity, Response.class);
            if (response == null) {
                response = new Response();
                response.setExpCode(expCode);
            }
        } catch (Exception e) {
            log.warn("getCityAbTestUserExp error, uuid:{}, expCode:{},", uuid, expCode, e);
            response = new Response();
            response.setExpCode(expCode);
        }

        return response;
    }

    /**
     * 获取用户实验(主要用于获取他人，非自己)
     *
     * @param uuid
     * @param expCode
     * @return
     */
    public Response getTargetExp(String uuid, String expCode, String cityId, Integer appcode, Integer cloned, Integer platformId, Long appVersion) {
        Map<String, Object> req = new HashMap<>();
        req.put("uuid", uuid);
        req.put("exp_code", expCode);
        req.put("city_id", cityId);
        req.put("appcode", appcode);
        req.put("cloned", cloned);
        req.put("platform_id", platformId);
        req.put("app_version", appVersion);

        Response response;
        try {
            HttpHeaders headers = SoaHeaderBuilder.buildHeadersFromSoaParams();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(req, headers);
            response = restTemplate.postForObject(REQ_URL + "/v1/cab/getResultCli", entity, Response.class);
            if (response == null) {
                response = new Response();
                response.setExpCode(expCode);
            }
        } catch (Exception e) {
            log.warn("getCityAbTestTargetExp error,", JsonUtils.objectToString(req), e);
            response = new Response();
            response.setExpCode(expCode);
        }

        LogUtil.info4Dev("getTargetExp:{}, response:{}", JsonUtils.objectToString(req), JsonUtils.objectToString(response));
        return response;
    }

    public void regReport(String accountUuid) {
        Map<String, Object> req = new HashMap<>();
        req.put("uuid", accountUuid);

        try {
            HttpHeaders headers = SoaHeaderBuilder.buildHeadersFromSoaParams();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(req, headers);
            ResponseEntity<String> result = restTemplate.postForEntity(REQ_URL + "/v1/cab/regReport", entity, String.class);
            log.info("regReport req: {} result: {}, uuid: {}", REQ_URL, result.getBody(), accountUuid);
        } catch (Exception e) {
            log.warn("regReport error, req: {}, uuid: {}", REQ_URL, accountUuid, e);
        }
    }

    /**
     * 城市实验返回值
     */
    @Data
    public static class Response {

        /**
         * 政策实验code
         */
        @JsonProperty(value = "exp_code")
        private String expCode;

        /**
         * 变量；类似msg_spolicy_strategy_experiment_a实验组，msg_spolicy_strategy_control对照组
         */
        @JsonProperty(value = "varias")
        private String varies;
    }

}
