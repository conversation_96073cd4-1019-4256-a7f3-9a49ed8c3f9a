package cn.taqu.account.service;

import cn.taqu.account.vo.resp.FriendshipPreferLabelResp;

import java.util.List;

/**
 * 用户标签
 *
 * <AUTHOR>
 * @date 2024/10/15 10:20 上午
 */
public interface AccountLabelService {

    /**
     * 获取用户交友偏好标签
     *
     * @param accountUuid
     * @return
     */
    FriendshipPreferLabelResp getFriendshipPreferLabel(String accountUuid);

    /**
     * 设置用户交友偏好标签
     *
     * @param accountUuid
     * @param ids
     */
    void saveFriendshipPreferLabel(String accountUuid, String ids);

    /**
     * 获取用户已设置的标签
     *
     * @param accountUuid
     * @return
     */
    List<String> listSetPersonalityLabel(String accountUuid);

}
