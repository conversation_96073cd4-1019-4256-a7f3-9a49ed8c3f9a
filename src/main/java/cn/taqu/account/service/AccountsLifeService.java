package cn.taqu.account.service;

import cn.taqu.account.common.*;
import cn.taqu.account.config.biz.AccountsLifeConfig;
import cn.taqu.account.constant.*;
import cn.taqu.account.dao.*;
import cn.taqu.account.dto.*;
import cn.taqu.account.dto.life.AccountsLifeDto;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.model.*;
import cn.taqu.account.thread.TencentImgCompareCall;
import cn.taqu.account.thread.ThirdPartFactory;
import cn.taqu.account.utils.LogUtil;
import cn.taqu.account.vo.life.AccountsLifeVo;
import cn.taqu.account.vo.life.GetAccountsLifeVo;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static cn.taqu.account.constant.RedisKeyConstant.USER_PHOTO_DETECT_CUTOFF;
import static cn.taqu.account.constant.RedisKeyConstant.USER_PHOTO_DETECT_VERSION_SET;
import static cn.taqu.account.vo.life.AccountsLifeVo.*;

/**
 * 我的生活
 *
 * <AUTHOR>
 * 2024年12月2日下午5:21:02
 */
@Slf4j
@Service
public class AccountsLifeService {

    public static final int LIFE_TASK_COMPLETE_THRESHOLD = 4;

    public static final Set<Integer> REAL_PERSON_SEQ = Sets.newHashSet(1, 2, 3);

    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    private AccountsLifeDao accountsLifeDao;
    @Autowired
    private AccountsLifeLogDao accountsLifeLogDao;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private UserCertificationLogService userCertificationLogService;
    @Autowired
    private BuryService buryService;
    @Lazy
    @Autowired
    private AllureService allureService;

    @Autowired
    @Qualifier("accountBizStringRedisTemplate")
    private StringRedisTemplate accountBizStringRedisTemplate;

    @Autowired
    private ImageCompareRecordDao imageCompareRecordDao;

    private Executor lifeHisRealPersonDetector;

    @PostConstruct
    public void init() {
        lifeHisRealPersonDetector =  new ThreadPoolExecutor(1, 4,
            0L, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(128)
        );
    }


    /**
     * 默认容器排序 1,2,3,4,5
     */
    private static final List<Integer> DEFAULT_CONTAINER_SEQ = Lists.newArrayList(1,2,3,4,5);

    private static AccountsLifeConfig ACCOUNTS_LIFE_CONFIG = new AccountsLifeConfig();

    public static void setConfig(String json) {
        log.info("修改我的生活配置，json={}", json);
        AccountsLifeConfig AccountsLifeConfig = new AccountsLifeConfig();
        if(StringUtils.isNotBlank(json)) {
            AccountsLifeConfig = JsonUtils.stringToObject2(json, new TypeReference<AccountsLifeConfig>() {});
        }
        ACCOUNTS_LIFE_CONFIG = AccountsLifeConfig;
    }

    /**
     *
     * @param accountUuid
     * @param accountsLifeSeq
     */
    @Transactional
    public void setAccountsLifeSeq(String accountUuid, String accountsLifeSeq) {
        // 校验数据结构
        // 数据不规范会抛异常
        try {
            Splitter.on(",").split(accountsLifeSeq).forEach(Integer::valueOf);
        } catch (Exception e) {
            log.warn("设置我的生活排序, 传参异常, accountUuid={}, accountsLifeSeq={}", accountUuid, accountsLifeSeq);
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }

        accountsInfoDao.updateAccountsLifeSeq(accountUuid, accountsLifeSeq);

        // 写缓存
        String redisKey = RedisKeyConstant.ACCOUNTS_LIFE_SEQ.setArg(accountUuid);
        accountBizStringRedisTemplate.opsForValue().set(redisKey, accountsLifeSeq, 10, TimeUnit.DAYS);
    }

    /**
     * 获取我的生活排序
     *
     * @param accountUuid
     * @param isQueryDb 是否查询数据库
     * @param isReturnDefault 是否返回默认排序
     * @return
     */
    public List<Integer> getAccountsLifeSeq(String accountUuid, boolean isQueryDb, boolean isReturnDefault) {
        boolean updateRedis = false;
        List<Integer> seqList = Lists.newArrayList();
        // 查缓存，缓存不存在，查库
        String redisKey = RedisKeyConstant.ACCOUNTS_LIFE_SEQ.setArg(accountUuid);

        String accountsLifeSeq = accountBizStringRedisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isBlank(accountsLifeSeq) && isQueryDb) {
            // 查库
            accountsLifeSeq = accountsInfoDao.getAccountsLifeSeq(accountUuid);
            updateRedis = true;
        }

        if(StringUtils.isBlank(accountsLifeSeq)) {
            if(isReturnDefault) {
                seqList.addAll(DEFAULT_CONTAINER_SEQ);
            }
        }else {
            Splitter.on(",").split(accountsLifeSeq).forEach(entry ->{
                seqList.add(Integer.valueOf(entry));
            });
        }

        // 写缓存
        if(updateRedis && CollectionUtils.isNotEmpty(seqList)) {
            accountsLifeSeq = Joiner.on(",").join(seqList);
            accountBizStringRedisTemplate.opsForValue().set(redisKey, accountsLifeSeq, 30, TimeUnit.DAYS);
        }

        return seqList;
    }

    /**
     * @param accountUuid
     * @return
     */
    public GetAccountsLifeVo getAccountsLifeForClientMyself(String accountUuid) {
        GetAccountsLifeVo getAccountsLifeVo = new GetAccountsLifeVo();
        List<AccountsLifeVo> imgList = getAccountsLifeVo(accountUuid, true, true);
        InfoFiledCacheDTO info = SpringContextHolder.getBean(AccountBaseInfoManager.class).getInfoByUuid(accountUuid, new String[] {
            UuidInfoField.REAL_AVATAR_CERTIFICATION,
            UuidInfoField.PROFILE_VERIFY_STATUS,
            UuidInfoField.AVATAR
        });
        boolean realPersonCertification = info.realPersonCertificationPass();
        // 新状态处理
        imgList.forEach(img -> {
            if (AccountsLifeService.REAL_PERSON_SEQ.contains(img.getSeq())) {
                if (StringUtils.isEmpty(img.getPhotoUrl())) {
                    // 要求真人认证
                    img.setAuthStatus(REAL_PERSON_REQUIRED);
                } else if (AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus().equals(img.getVerifyStatus())) {
                    img.setAuthStatus(TO_BE_COMPARED);
                    if (realPersonCertification) {
                        // 历史照片异步比对 临时返回【未进行真人比对】
                        CompletableFuture.supplyAsync(() -> detectPhoto(accountUuid, img.getPhotoUrl()), lifeHisRealPersonDetector)
                            .thenAccept(status -> {
                                // 如果比对过程很快，有概率能直接给到客户端
                                img.setAuthStatus(AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(status) ? FACE_COMPARE_PASS : FACE_COMPARE_FAIL);
                                AccountsLife accountsLife = accountsLifeDao.getByAccountUuidAndSeq(accountUuid, img.getSeq());
                                if (accountsLife == null) {
                                    log.warn("历史生活照片为空 {} {}", accountUuid, img.getSeq());
                                    return;
                                }
                                accountsLife.setVerifyStatus(status);
                                updateAccountLife(accountsLife);
                            }).exceptionally(e -> {
                                String msg = "历史照片异步比对失败： " + accountUuid;
                                log.warn(msg, e);
                                return null;
                            });
                    }
                } else if (AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(img.getVerifyStatus())) {
                    // 已真人比对 判断风控状态
                    if (AccountsLife.Status.AUDIT_SUCCESS.getValue() == img.getStatus()) {
                        img.setAuthStatus(FACE_COMPARE_PASS);
                    } else if (AccountsLife.Status.AUDITING.getValue() == img.getStatus() || AccountsLife.Status.PHOTO_AUTO_CHECK.getValue() == img.getStatus()) {
                        img.setAuthStatus(FACE_COMPARE_AUDITING);
                    } else {
                        img.setAuthStatus(FACE_COMPARE_FAIL);
                    }
                } else if (AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(img.getVerifyStatus())) {
                    // 真人比对未通过
                    img.setAuthStatus(FACE_COMPARE_FAIL);
                } else {
                    // 审核中可能需要完善下判断
                    img.setAuthStatus(FACE_COMPARE_AUDITING);
                }
            }
        });
        getAccountsLifeVo.setImgList(imgList);
        Integer sexType = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);
        String exampleHtml = MapUtils.getString(ACCOUNTS_LIFE_CONFIG.getExampleHtmlMap(), sexType, AccountsLifeConfig.EXAMPLE_HTML_DEFAULT);
        getAccountsLifeVo.setExampleHtml(exampleHtml);
        Integer status = realPersonCertification ? CommConst.YES_1 : CommConst.NO_0;
        getAccountsLifeVo.setRealPersonCertification(status);
        return getAccountsLifeVo;
    }

    public void updateAccountLife(AccountsLife life) {
        TransactionWrapper.me().wrap(() -> accountsLifeDao.merge(life));
        updateAccountsLifeRedisBySeq(life.getAccountUuid(), life.getSeq(), life);
    }

    public void updateAccountLifeStatus(AccountsLife life) {
        accountsLifeDao.updateStatus(life);
        updateAccountsLifeRedisBySeq(life.getAccountUuid(), life.getSeq(), life);
    }

    /**
     * 获取用户我的生活
     *
     * @param accountUuid
     * @param isOrigin 是否主态
     * @param emptyReturn 图片为空是否返回
     * @return
     */
    public List<AccountsLifeVo> getAccountsLifeVo(String accountUuid, boolean isOrigin, boolean emptyReturn) {
        List<AccountsLife> accountsLifeList = getAccountsLifeOrAccountsPhoto(accountUuid, isOrigin, true);
        return accountsLifeToAccountsLifeVo(accountUuid, accountsLifeList, isOrigin, emptyReturn);
    }

    /**
     * 获取用户我的生活
     *
     * @param accountUuid
     * @param isOrigin 是否主态
     * @param emptyReturn 图片为空是否返回
     * @return
     */
    public List<AccountsLifeVo> accountsLifeToAccountsLifeVo(String accountUuid, List<AccountsLife> accountsLifeList, boolean isOrigin, boolean emptyReturn) {
        List<AccountsLifeVo> voList = Lists.newArrayList();
        List<Integer> seqList = getAccountsLifeSeq(accountUuid, true, true);

        for (Integer containerSeq : seqList) {
            AccountsLifeVo accountsLifeVo = new AccountsLifeVo();
            accountsLifeVo.setUpdateStatus(CommConst.YES_1);
            accountsLifeVo.setContainerText(MapUtils.getString(ACCOUNTS_LIFE_CONFIG.getContainerConfigMap(), containerSeq, ""));
            accountsLifeVo.setSeq(containerSeq);

            for (AccountsLife accountsLife : accountsLifeList) {
                if (Objects.equals(containerSeq, accountsLife.getSeq())) {
                    if (isOrigin) {
                        // 主态
                        String photoUrl = accountsLife.getPhotoUrlOrigin();
                        if(StringUtils.isBlank(photoUrl)) {
                            accountsLifeVo.setPhotoUrl("");
                        } else {
                            accountsLifeVo.setPhotoUrl(AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl));
                        }
                        accountsLifeVo.setWidth(accountsLife.getWidthOrigin());
                        accountsLifeVo.setHeight(accountsLife.getHeightOrigin());
                        accountsLifeVo.setRemark(accountsLife.getRemarkOrigin());
                        accountsLifeVo.setStatus(accountsLife.getStatus());
                        accountsLifeVo.setVerifyStatus(accountsLife.getVerifyStatus());
                    } else {
                        // 客态
                        String photoUrl = accountsLife.getPhotoUrl();
                        if (StringUtils.isBlank(photoUrl)) {
                            accountsLifeVo.setPhotoUrl("");
                        } else {
                            accountsLifeVo.setPhotoUrl(AvatarHandleService.getAvatarPhotoUrl(photoUrl));
                        }
                        accountsLifeVo.setWidth(accountsLife.getWidth());
                        accountsLifeVo.setHeight(accountsLife.getHeight());
                        accountsLifeVo.setRemark(accountsLife.getRemark());
                        accountsLifeVo.setVerifyStatus(accountsLife.getVerifyStatus());
                    }
                    SoaBaseParams baseParams = SoaBaseParams.fromThread();
                    boolean faceCompareFailed = REAL_PERSON_SEQ.contains(accountsLife.getSeq()) && isRealPersonVersion(baseParams.getPlatform_id(), baseParams.getApp_version()) && AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(accountsLife.getVerifyStatus());
                    if (faceCompareFailed) {
                        accountsLifeVo.setUpdateStatus(CommConst.YES_1);
                    } else if (accountsLife.verifyAuditingStatus()) {
                        accountsLifeVo.setUpdateStatus(CommConst.NO_0);
                    }
                    break;
                }
            }

            if (StringUtils.isBlank(accountsLifeVo.getPhotoUrl()) && !emptyReturn) {
                continue;
            }

            voList.add(accountsLifeVo);
        }
        return voList;
    }

    /**
     *  查我的生活，再查相册，不写数据库数据
     *
     * @param accountUuid
     * @param isOrigin
     * @param isUpdateRedis 是否更新缓存
     * @return
     */
    public List<AccountsLife> getAccountsLifeOrAccountsPhoto(String accountUuid, boolean isOrigin, boolean isUpdateRedis){
        List<AccountsLife> list = this.getAccountsLifeByAccountUuid(accountUuid, isUpdateRedis);
        // 如果我的生活为空，则查询相册数据
        if(CollectionUtils.isEmpty(list)) {
            // 是否只返回审核的
            boolean onlyCheck = !isOrigin;

            List<AccountsPhotoDto> accountCover = accountsPhotoService.getAccountCoverList(accountUuid, onlyCheck);
            if(CollectionUtils.isNotEmpty(accountCover)) {
                list = this.accountsPhotoDtoToAccountsLife(accountUuid, accountCover);
            }
        }

        LogUtil.info4Dev("我的生活or相册，accountUuid={}, isOrigin={}, list={}", accountUuid, isOrigin, JsonUtils.objectToString(list));

        //过滤主客态
        List<AccountsLife> reList = Lists.newArrayList();

        if(CollectionUtils.isNotEmpty(list)) {
            if(isOrigin) {
                reList.addAll(list);
            }else {
                for (AccountsLife accountsLife : list) {
                    if(accountsLife.verifyVisitorStatus()) {
                        reList.add(accountsLife);
                    }
                }
            }
        }

        return reList;
    }

    private List<AccountsLife> accountsPhotoDtoToAccountsLife(String accountUuid, List<AccountsPhotoDto> accountCover){
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        List<AccountsLife> reList = Lists.newArrayList();
        for (AccountsPhotoDto accountsPhotoDto : accountCover) {
            Integer seq = accountsPhotoDto.getSeq();
            // 如果排序值是 2~6 则创建我的生活
            if(DEFAULT_CONTAINER_SEQ.contains(seq - 1)) {
                String photoUrl = accountsPhotoDto.getPhotoUrl();
                Integer status = accountsPhotoDto.getStatus();
                Integer verifyStatus = accountsPhotoDto.getVerifyStatus();

                AccountsLife accountsLife = new AccountsLife();
                accountsLife.setAccountUuid(accountUuid);
                accountsLife.setCreateTime(currentTimeSeconds);
                accountsLife.setPhotoUrlOrigin(photoUrl);
                accountsLife.setWidthOrigin(0);
                accountsLife.setHeightOrigin(0);
                accountsLife.setRemarkOrigin("");
                accountsLife.setSeq(seq - 1);
                accountsLife.setUpdateTime(currentTimeSeconds);
                accountsLife.setVerifyStatus(verifyStatus);

                AccountsPhoto.Status accountsPhotoStatus = AccountsPhoto.Status.of(status);
                if(accountsPhotoStatus == null) {
                    log.warn("相册数据异常，id={}", accountsPhotoDto.getId());
                }
                // 状态转换
                AccountsLife.Status accountsLifeStatus = AccountsLife.accountsPhotoStatusToAccountsLifeStatus(accountsPhotoStatus);

                accountsLife.setStatus(accountsLifeStatus.getValue());
                // 客态可见，写入数据
                if(accountsLife.verifyVisitorStatus()) {
                    accountsLife.setRemark("");
                    accountsLife.setWidth(0);
                    accountsLife.setHeight(0);
                    accountsLife.setPhotoUrl(photoUrl);
                }

                reList.add(accountsLife);
            }
        }
        return reList;
    }

    /**
     * 只查 AccountsLife
     * @param accountUuid
     * @param isUpdateRedis 是否更新缓存
     * @return
     */
    public List<AccountsLife> getAccountsLifeByAccountUuid(String accountUuid, boolean isUpdateRedis){
        // 查缓存
        List<AccountsLife> list = mGetAccountsLifeAllByRedis(Lists.newArrayList(accountUuid)).get(accountUuid);
        LogUtil.info4Gray("[getAccountsLifeByAccountUuid]查询缓存，accountUuid={}, list={}", accountUuid, JsonUtils.objectToString(list));
        if(CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        // 查数据库
        list = accountsLifeDao.getByAccountUuid(accountUuid);
        LogUtil.info4Gray("[getAccountsLifeByAccountUuid]查询数据库，accountUuid={}, list={}", accountUuid, JsonUtils.objectToString(list));
        if(CollectionUtils.isNotEmpty(list) && isUpdateRedis) {
            setAccountsLifeToRedis(list, accountUuid);
        }

        return list;
    }

    /**
     * @param uuidList 用户uuid
     * @return
     */
    public Map<String, List<AccountsLife>> mGetAccountsLifeAllByRedis(List<String> uuidList) {
        //从缓存中取
        List<byte[]> hashKeys = Lists.newArrayList();
        RedisSerializer<String> rs = accountBizStringRedisTemplate.getStringSerializer();
        hashKeys.addAll(Lists.newArrayList(rs.serialize("1"), rs.serialize("2"), rs.serialize("3"), rs.serialize("4"),
                rs.serialize("5")));

        List<Object> valuesList = accountBizStringRedisTemplate.executePipelined((RedisConnection connection) -> {
            uuidList.forEach(uuid -> {
                byte[] key = rs.serialize(RedisKeyConstant.ACCOUNTS_LIFE_PIC.setArg(uuid));
                connection.hMGet(key, hashKeys.toArray(new byte[hashKeys.size()][]));
            });
            return null;
        }, rs);

        Map<String, List<AccountsLife>> result = Maps.newLinkedHashMapWithExpectedSize(uuidList.size());
        for (int i = 0, size = uuidList.size(); i < size; i++) {
            String uuid = uuidList.get(i);
            List<Object> values = valuesList.isEmpty() ? null : (List<Object>) valuesList.get(i);
            if (values == null || values.isEmpty() ) {
                continue;
            }

            List<AccountsLife> photoList = Lists.newArrayList();
            for (Object photoObj : values) {
                String photoStr = (String) photoObj;
                if (StringUtils.isBlank(photoStr)) {
                    continue;
                }

                AccountsLife accountsLife = null;
                try {
                    accountsLife = JsonUtils.stringToObject(photoStr, new TypeReference<AccountsLife>() {
                    });
                    // 如果后续增加字段。历史数据字段不全此处要处理
                    photoList.add(accountsLife);
                } catch (Exception e) {
                    log.error("缓存中存在异常结构数据，photoStr：{}", photoStr, e);
                }
            }
            result.put(uuid, photoList);
        }

        return result;
    }

    private List<AccountsLife> accountsPhotoSyncToAccountsLife(String accountUuid){
        List<AccountsLife> reList = Lists.newArrayList();
        List<AccountsPhotoDto> accountCover = accountsPhotoService.getAccountCoverList(accountUuid, false);
        if(CollectionUtils.isNotEmpty(accountCover)) {
            reList = accountsPhotoDtoToAccountsLife(accountUuid, accountCover);
        }

        if(CollectionUtils.isNotEmpty(reList)) {
            accountsLifeDao.merge(reList);
            accountsLifeDao.flush();
        }
        return reList;
    }

    /**
     * @param accountUuid
     * @param bucket
     * @param accountsLifeDto
     * @param smid
     */
    @Transactional
    public void setAccountsLife(String accountUuid, String bucket, AccountsLifeDto accountsLifeDto, String smid) {
        // 先查询用户我的生活，如果没有数据，则先同步数据
        List<AccountsLife> list = accountsLifeDao.getByAccountUuid(accountUuid);
        // 如果我的生活为空，则查询相册数据
        boolean isInitLifeRedis = false;
        if(CollectionUtils.isEmpty(list)) {
            list = accountsPhotoSyncToAccountsLife(accountUuid);
            if(CollectionUtils.isNotEmpty(list)) {
                isInitLifeRedis = true;
            }
        }

        String photoUrl = accountsLifeDto.getPhotoUrl();
        photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
        Integer width = accountsLifeDto.getWidth();
        Integer height = accountsLifeDto.getHeight();
        String remark = accountsLifeDto.getRemark();
        Integer seq = accountsLifeDto.getSeq();
        Integer verifyStatus = AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus();
        String url = CommConst.AVATAR_SRC_HOST + photoUrl;
        String riskBizId = RiskSafeService.genBizId(url);
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        ImageCompareRecord record = null;
        boolean realPersonRequired = REAL_PERSON_SEQ.contains(seq) && isRealPersonVersion(baseParams.getPlatform_id(), baseParams.getApp_version());
        if (realPersonRequired) {
            // 风控相似度比较
            boolean useNewSafeId = vectorDetectUseNewSafeId(accountUuid);
            ImgVectorDetectDto dto = new ImgVectorDetectDto();
            dto.setSafeId(useNewSafeId ? RiskSafeIdConstant.SIMILAR_PHOTO_DETECT : RiskSafeIdConstant.ACCOUNTS_LIFE_REAL_PERSON);
            dto.setBizId(riskBizId);
            dto.setImageUrl(Collections.singletonList(url));
            dto.setSenderUuid(accountUuid);
            ImgVectorDetectRespDto resp = RiskSafeService.imageVectorDetect(dto);

            record = new ImageCompareRecord();
            record.setBizId(riskBizId);
            Long now = DateUtil.currentTimeSeconds();
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setBizType(ImageCompareRecord.LIFE);
            record.setConclusion(resp.getHitTypeCode());

            if (!resp.pass()) {
                throw new ServiceException("photo_risk_compare_similar", useNewSafeId ? "该照片和头像相似度过高，请更换一张新的" : "该照片已上传过或相似度过高，请重新上传");
            }
            verifyStatus = detectPhoto(accountUuid, photoUrl);
        }
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();

        // 查询数据
        AccountsLife accountsLife = accountsLifeDao.getByAccountUuidAndSeq(accountUuid, seq);
        if(accountsLife != null) {
            // 审核中不允许编辑
            if(accountsLife.verifyAuditingStatus()) {
                throw new ServiceException("审核中，请等待");
            }
        }else {
            accountsLife = new AccountsLife();
            accountsLife.setAccountUuid(accountUuid);
            accountsLife.setCreateTime(currentTimeSeconds);
            accountsLife.setSeq(seq);
        }

        // 创建log
        AccountsLifeLog accountsLifeLog = accountsLifeLogDao.addAccountsLifeLog(accountUuid, photoUrl, width, height, remark, seq, verifyStatus);

        // 违规内容检测 同时推入审核 审核不同通过直接抛异常了
        // 前3张真人比对失败 不送审
        if (AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(verifyStatus)) {
            log.info("我的生活真人比对失败，跳过审核{}", accountsLife);
            accountsLifeLog.setStatus(AccountsLife.Status.AUDIT_FAIL.getValue());
            accountsLifeLogDao.merge(accountsLifeLog);
        } else {
            checkRiskAccountsLife(accountUuid, seq, photoUrl, remark, smid, String.valueOf(accountsLifeLog.getId()));
        }

        accountsLife.setHeightOrigin(height);
        accountsLife.setPhotoUrlOrigin(photoUrl);
        accountsLife.setRemarkOrigin(remark);
        accountsLife.setStatus(AccountsLife.Status.AUDITING.getValue());
        accountsLife.setUpdateTime(currentTimeSeconds);
        accountsLife.setVerifyStatus(verifyStatus);
        accountsLife.setWidthOrigin(width);

        accountsLife = accountsLifeDao.merge(accountsLife);

        if (record != null) {
            record.setOriginId(accountsLife.getId());
            imageCompareRecordDao.merge(record);
        }

        // 更新相册
        accountsPhotoService.accountsLifeSyncToAccountPhotoSave(accountsLife);

        if (isInitLifeRedis) {
            // 先初始化全量缓存
            setAccountsLifeToRedis(list, accountUuid);
        }

        // 再修改单条缓存
        updateAccountsLifeRedisBySeq(accountUuid, seq, accountsLife);

        // 文件使用，通知图片系统
        BuryService.pushToPicSysUsedFileBiz(CommConst.APPCODE_TAQU, photoUrl, CommConst.AVATAR_BUCKET, DateUtil.currentTimeSeconds());
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
    }

    /**
     * @param accountUuid
     * @param seq
     */
    @Transactional
    public void delAccountsLife(String accountUuid, Integer seq) {
        AccountsLife accountsLife = accountsLifeDao.getByAccountUuidAndSeq(accountUuid, seq);
        if (accountsLife == null) {
            log.info("未查询到我的生活数据，accountUuid={}, seq={}", accountUuid, seq);
        } else {
            accountsLifeDao.removeById(accountsLife.getId());
            updateAccountsLifeRedisBySeq(accountUuid, seq, null);
        }

        accountsPhotoService.accountsLifeSyncToAccountPhotoDel(accountUuid, seq);

    }

    /**
     * 图片认证结果 暂无使用
     *
     * @param accountUuid
     * @param photoUrl
     * @return
     */
    public Integer detectPhoto(String accountUuid, String photoUrl) {
        CompareFaceDto compareFaceDto = detectPhotoAndGetCompareFaceDto(accountUuid, photoUrl);
        Integer verifyStatus = AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus();
        Double similarityScore;
        if (compareFaceDto != null) {
            similarityScore = Double.parseDouble(compareFaceDto.getScore() + "");
            log.info("我的生活，照片比对相似度 {} {} {}", accountUuid, photoUrl, similarityScore);
            verifyStatus = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class).isVerifySuccess(similarityScore);
        }
        return verifyStatus;
    }

    /**
     * 图片对比  暂无使用
     * @param accountUuid
     * @param photoUrl
     * @return
     */
    public CompareFaceDto detectPhotoAndGetCompareFaceDto(String accountUuid, String photoUrl) {
        // 图片比对
        CompareFaceDto compareFaceDto = null;
        String faceCertification = accountsInfoService.getFaceCertification(accountUuid);
        if(!Objects.equals(faceCertification, CommConst.YES_1.toString())) {
            // 用户未活体
            return compareFaceDto;
        }
        // 查询活体图片
        AliyunLiveFaceDetect liveFaceDetect = SpringContextHolder.getBean(AliyunLiveFaceDetectDao.class).getInfoByAccountUuid(accountUuid);

        String faceBasePhoto = "";
        if (liveFaceDetect != null) {
            faceBasePhoto = liveFaceDetect.getBase_photo_url();
        } else {
            // 用户无活体数据 正常不应该出现
            log.warn("用户已活体认证，但无底图，accountUuid={}", accountUuid);
            return compareFaceDto;
        }

        Future<CompareFaceDto> imgCompareFuture = ThirdPartFactory.tencentImgComparePool.submit(new TencentImgCompareCall(accountUuid, photoUrl, faceBasePhoto, RiskCertificationTypeEnum.ACCOUNT_LIFE));
        try {
            compareFaceDto = imgCompareFuture.get(5000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.warn("线程池处理.InterruptedException", e);
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        } catch (ExecutionException e) {
            if (e.getCause() instanceof ServiceException) {
                ServiceException serviceException = (ServiceException) e.getCause();
                throw serviceException;
            } else {
                log.warn("ExecutionException", e);
            }
        } catch (TimeoutException e) {
            try {
                imgCompareFuture.cancel(true);
                log.warn("线程池处理.超时停止");
            } catch (Exception ee) {
                // 结束子进程
                log.warn("线程池处理.超时停止.结束子进程失败", ee);
            }
            throw new ServiceException(CodeStatus.PHOTO_DETECT_BUSY);
        } catch (Exception e) {
            log.warn("图片检测失败.uuid={}", accountUuid, e);
        }
        log.info("图片比对检测.结果={}", compareFaceDto == null ? "" : JSON.toJSONString(compareFaceDto));
        if (compareFaceDto == null || !compareFaceDto.isRequestSuccess()) {
            throw new ServiceException(CodeStatus.PHOTO_DETECT_COMPARE_BUSY);
        }

        if (!compareFaceDto.isSimilarity()) {
            userCertificationLogService.logAccountLifeFail(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl), ToolsService.addAccountPrivateUrlPreDomain(faceBasePhoto), compareFaceDto.getScore());
        } else {
            userCertificationLogService.logAccountLifeSuccess(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl), ToolsService.addAccountPrivateUrlPreDomain(faceBasePhoto), compareFaceDto.getScore());
        }
        return compareFaceDto;
    }


    /**
     * 风控检测
     *
     * @param accountUuid
     * @param photoUrl
     * @param remark
     * @param smid
     * @param bizId
     * @return
     */
    private ShumeiContentCheckDTO checkRiskAccountsLife(String accountUuid, Integer seq, String photoUrl, String remark, String smid, String bizId) {
        Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? CommConst.APPCODE_TAQU : SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? CommConst.CLONED_TAQU : SoaBaseParams.fromThread().getCloned();

        // 文本检测 涉嫌敏感
        // 判断版本号，确定是否走新的风控场景
        RiskDetectEnum detectEnum = RiskDetectEnum.ACCOUNTS_LIFE;
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        if (AccountsLifeService.REAL_PERSON_SEQ.contains(seq) && isRealPersonVersion(baseParams.getPlatform_id(), baseParams.getApp_version())) {
            detectEnum = RiskDetectEnum.ACCOUNTS_LIFE_REAL_PERSON;
        }
        ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accountUuid, remark, Lists.newArrayList(AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl)), smid, bizId, detectEnum.name(), appcode, cloned);
        log.info("数美审核结果.我的生活.uuid={}.result={}", accountUuid, contentCheckDTO);

        if (contentCheckDTO == null ||
                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
            throw contentCheckDTO != null ?
                new ServiceException(CodeStatus.CONTENT_RISK_FAILED.value(), contentCheckDTO.getToast()) :
                new ServiceException(CodeStatus.CONTENT_RISK_FAILED);
        }
        return contentCheckDTO;
    }

    public static boolean isRealPersonVersion(Integer platformId, Long version) {
        if (PlatformEnum.ANDROID.getValue().equals(platformId)) {
            // 内测会比规划版本号-1
            return version >= 8330;
        } else if (PlatformEnum.IPHONE.getValue().equals(platformId) || PlatformEnum.IPAD.getValue().equals(platformId)){
            return version >= 11280;
        } else {
            return true;
        }
    }

    /**
     * @param dto
     */
    @Transactional
    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]我的生活审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        if (Objects.nonNull(riskSafeHitTypeEnum)) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    auditPass(Long.valueOf(dto.getBizId()), dto.getOperator(), JsonUtils.objectToString(dto));
                    break;
                case BLOCK:
                    auditReject(Long.valueOf(dto.getBizId()), dto.getOperator(), JsonUtils.objectToString(dto));
                    break;
                default:
                    break;
            }
        }
    }


    /**
     * 审核通过
     * @param logId
     * @param operatorToken
     */
    private void auditPass(Long logId, String operatorToken, String riskRespData){
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        AccountsLifeLog accountsLifeLog = accountsLifeLogDao.get(logId);
        // 待审
        if(accountsLifeLog != null && CommonAuditStatus.AUDITING.getStatus().equals(accountsLifeLog.getStatus())){
            accountsLifeLog.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
            accountsLifeLog.setAuditTime(currentTimeSeconds);
            accountsLifeLog.setUpdateTime(currentTimeSeconds);
        }else{
            log.warn("[审核通过]未查询到我的生活日志数据，id={}", logId);
            return;
        }

        log.info("[审核通过]审核我的生活数据.token={}.id={}", operatorToken, logId);
        String accountUuid = accountsLifeLog.getAccountUuid();
        Integer seq = accountsLifeLog.getSeq();
        String photoUrl = accountsLifeLog.getPhotoUrl();
        String remark = accountsLifeLog.getRemark();

        // 同步一下缓存
        getAccountsLifeByAccountUuid(accountUuid, true);

        accountsLifeLog = accountsLifeLogDao.merge(accountsLifeLog);

        // 查询当前数据
        AccountsLife accountsLife = accountsLifeDao.getByAccountUuidAndSeq(accountUuid, seq);
        if(accountsLife == null){
            log.warn("[审核通过]未查询到我的生活数据，accountUuid={}, seq={}", accountUuid, seq);
            return;
        }

        // 主态图片和描述与审核数据一致，才可修改
        if(!Objects.equals(accountsLife.getPhotoUrlOrigin(), photoUrl) || !Objects.equals(accountsLife.getRemarkOrigin(), remark)) {
            log.info("[审核通过]我的生活审核数据与当前数据不一致，不修改当前数据，accountUuid={}, seq={}", accountUuid, seq);
            return;
        }

        // 修改我的生活数据
        accountsLife.setPhotoUrl(accountsLife.getPhotoUrlOrigin());
        accountsLife.setWidth(accountsLife.getWidthOrigin());
        accountsLife.setHeight(accountsLife.getHeightOrigin());
        accountsLife.setRemark(accountsLife.getRemarkOrigin());
        accountsLife.setUpdateTime(currentTimeSeconds);
        accountsLife.setStatus(AccountsLife.Status.AUDIT_SUCCESS.getValue());

        accountsLife = accountsLifeDao.merge(accountsLife);

        // 修改相册数据
        accountsPhotoService.accountsLifeSyncToAccountPhotoSave(accountsLife);
        // 更新缓存
        updateAccountsLifeRedisBySeq(accountUuid, seq, accountsLife);

        // 我的生活图片，3张照算完成任务
        List<AccountsLife> accountsLifeList = getAccountsLifeOrAccountsPhoto(accountUuid, false, true);
        List<AccountsLife> liftList = accountsLifeList;
        // 新版本要求真人照
        ClonedPlanformAppVersionDto versionDto = accountsInfoService.getAccountClonedPlanformAppVersion(accountUuid);
        FinishIncentiveTaskEnum taskEnum = FinishIncentiveTaskEnum.PERFECT_PHOTO;
        if (isRealPersonVersion(versionDto.getPlatformId(), versionDto.getAppVersion()) && CollectionUtils.isNotEmpty(accountsLifeList)) {
            taskEnum = FinishIncentiveTaskEnum.MY_LIFE;
            liftList = accountsLifeList.stream().filter(l -> AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(l.getVerifyStatus())).collect(Collectors.toList());
        }
        log.info("我的生活照片数 {} {}", accountUuid, liftList.size());
        if (CollectionUtils.isNotEmpty(liftList) && liftList.size() == 3) {
            buryService.toBbsFinishIncentiveTask(accountUuid, taskEnum.getType());
        }
        // 4张吸引力刷新
        if (CollectionUtils.isNotEmpty(accountsLifeList) && accountsLifeList.size() >= LIFE_TASK_COMPLETE_THRESHOLD) {
            allureService.refreshAllure(accountUuid);
            LogUtil.info4Dev("我的生活已满足刷新条件:{}, size:{}", accountUuid, accountsLifeList.size());
        }
    }

    /**
     * 审核不通过
     * @param logId
     * @param operatorToken
     */
    private void auditReject(Long logId, String operatorToken, String riskRespData) {
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        AccountsLifeLog accountsLifeLog = accountsLifeLogDao.get(logId);
        // 待审
        if(accountsLifeLog != null &&
            (Objects.equals(CommonAuditStatus.AUDITING.getStatus(), accountsLifeLog.getStatus())
            || Objects.equals(CommonAuditStatus.AUDIT_SUCCESS.getStatus(), accountsLifeLog.getStatus()))){
            accountsLifeLog.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
            accountsLifeLog.setAuditTime(currentTimeSeconds);
            accountsLifeLog.setUpdateTime(currentTimeSeconds);
        }else{
            log.warn("[审核拒绝]未查询到我的生活日志数据，id={}", logId);
            return;
        }

        log.info("[审核拒绝]审核我的生活数据.token={}.id={}", operatorToken, logId);
        String accountUuid = accountsLifeLog.getAccountUuid();
        Integer seq = accountsLifeLog.getSeq();
        String photoUrl = accountsLifeLog.getPhotoUrl();
        String remark = accountsLifeLog.getRemark();

        // 同步一下缓存
        getAccountsLifeByAccountUuid(accountUuid, true);

        accountsLifeLog = accountsLifeLogDao.merge(accountsLifeLog);

        // 查询当前数据
        AccountsLife accountsLife = accountsLifeDao.getByAccountUuidAndSeq(accountUuid, seq);
        if(accountsLife == null){
            log.warn("[审核拒绝]未查询到我的生活数据，accountUuid={}, seq={}", accountUuid, seq);
            return;
        }

        // 主态图片和描述与审核数据一致，才可修改
        if(!Objects.equals(accountsLife.getPhotoUrlOrigin(), photoUrl) || !Objects.equals(accountsLife.getRemarkOrigin(), remark)) {
            log.info("[审核拒绝]我的生活审核数据与当前数据不一致，不修改当前数据，accountUuid={}, seq={}", accountUuid, seq);
            return;
        }

        // 首次审核，无历史数据-拒绝 || （审核通过-再拒绝 && 再拒绝图==记录图）
        if(StringUtils.isBlank(accountsLife.getPhotoUrl()) || (!accountsLife.verifyAuditingStatus() && Objects.equals(accountsLife.getPhotoUrl(), photoUrl)) ) {
            // 无客态数据，删除数据
            accountsLifeDao.removeById(accountsLife.getId());
            // 服务端处理，需要重排序
            updateAccountsLifeSeqForDel(accountUuid, seq);

            accountsPhotoService.accountsLifeSyncToAccountPhotoDel(accountUuid, seq);
            // 更新缓存
            updateAccountsLifeRedisBySeq(accountUuid, seq, null);
            // 向量清理
            if (REAL_PERSON_SEQ.contains(seq)) {
                ImgVectorRemoveDto dto = new ImgVectorRemoveDto();
                dto.setSenderUuid(accountUuid);
                String riskBizId = imageCompareRecordDao.getBizId(accountsLife.getId(), ImageCompareRecord.LIFE);
                dto.setBizId(StringUtils.isNotBlank(riskBizId) ? riskBizId : String.valueOf(accountsLife.getId()));
                dto.setSafeId(RiskSafeIdConstant.ACCOUNTS_LIFE_REAL_PERSON);
                RiskSafeService.imageVectorDelete(dto);
            }
        }else {
            // 有客态数据，恢复客态数据
            accountsLife.setPhotoUrlOrigin(accountsLife.getPhotoUrl());
            accountsLife.setWidthOrigin(accountsLife.getWidth());
            accountsLife.setHeightOrigin(accountsLife.getHeight());
            accountsLife.setRemarkOrigin(accountsLife.getRemark());
            accountsLife.setUpdateTime(currentTimeSeconds);
            accountsLife.setStatus(AccountsLife.Status.AUDIT_SUCCESS.getValue());
            accountsLifeDao.merge(accountsLife);

            accountsPhotoService.accountsLifeSyncToAccountPhotoSave(accountsLife);
            // 更新缓存
            updateAccountsLifeRedisBySeq(accountUuid, seq, accountsLife);
        }

        // 删除照片 通知图片系统
        BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, photoUrl, CommConst.AVATAR_BUCKET, 1, DateUtil.currentTimeSeconds());
    }

    /**
     *
     * 我的生活，如果用户首次传了按3，4，5，1，2 顺序传图。人审核拒绝了5，则重排序 3，4，1，2，5
     *
     * @param accountUuid
     * @param seq 删除的排序
     */
    public void updateAccountsLifeSeqForDel(String accountUuid, Integer delSeq) {
        // 重新
        List<Integer> accountsLifeSeq = getAccountsLifeSeq(accountUuid, true, true);
        // 查询我的生活数据
        List<AccountsLife> accountsLifeList = getAccountsLifeByAccountUuid(accountUuid, false);

        List<Integer> accountsLifeSeqNew = Lists.newArrayList();
        /*
         * 1. 循环当前排序
         * 2.1 若循环中排序和删除排序值一样，则跳过
         * 2.2 若循环中排序值有对应数据，则排序不变
         * 2.3 若循环中排序值无对应数据，则插入删除的排序值，再插入当前排序值
         * 3. 若最终未插入删除的排序值，则插入在最后
         */
        boolean isSetDelSeq = false;
        x:for (Integer seq : accountsLifeSeq) {
            if(Objects.equals(seq, delSeq)) {
                continue x;
            }
            y:for (AccountsLife accountsLife : accountsLifeList) {
                if(Objects.equals(seq, accountsLife.getSeq())) {
                    accountsLifeSeqNew.add(seq);
                    continue x;
                }
            }
            if(!isSetDelSeq) {
                accountsLifeSeqNew.add(delSeq);
                isSetDelSeq = true;
            }
            accountsLifeSeqNew.add(seq);
        }
        // 最后插入
        if(!isSetDelSeq) {
            accountsLifeSeqNew.add(delSeq);
        }

        String accountsLifeSeqNewStr = Joiner.on(",").join(accountsLifeSeqNew);

        setAccountsLifeSeq(accountUuid, accountsLifeSeqNewStr);
    }

    public void setAccountsLifeToRedis(List<AccountsLife> photoList, String accountUuid) {
        //修改缓存
        Map<String, String> redisMap = new HashMap<String, String>(DEFAULT_CONTAINER_SEQ.size());

        x:for (Integer seq : DEFAULT_CONTAINER_SEQ) {
            for (AccountsLife accountsLife : photoList) {
                if(Objects.equals(seq, accountsLife.getSeq())) {
                    redisMap.put(seq.toString(), JsonUtils.objectToString(accountsLife));
                    continue x;
                }
            }
            redisMap.put(seq.toString(), "");
        }

        log.info("uuid={},redisMap={}", accountUuid, JsonUtils.objectToString(redisMap));

        String redisKey = RedisKeyConstant.ACCOUNTS_LIFE_PIC.setArg(accountUuid);
        accountBizStringRedisTemplate.opsForHash().putAll(redisKey, redisMap);
        accountBizStringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
    }

    /**
     * 修改 我的生活缓存
     * @param accountUuid
     * @param seq
     * @param accountsLife
     */
    public void updateAccountsLifeRedisBySeq(String accountUuid, Integer seq, AccountsLife accountsLife) {
        String redisKey = RedisKeyConstant.ACCOUNTS_LIFE_PIC.setArg(accountUuid);
        if(accountsLife == null) {
            // 删除
            Long num = accountBizStringRedisTemplate.opsForHash().delete(redisKey, seq.toString());
            log.info("单张删除我的生活缓存，uuid={}, seq={}, num={}", accountUuid, seq, num);
        }else {
            // 修改
            accountBizStringRedisTemplate.opsForHash().put(redisKey, seq.toString(), JsonUtils.objectToString(accountsLife));
            log.info("单张修改我的生活缓存，uuid={}, seq={}, accountsLife={}", accountUuid, seq, JsonUtils.objectToString(accountsLife));
        }
    }

    /**
     * @param accountUuid
     */
    public void resetEmpty(String accountUuid) {
        List<AccountsLife> list = accountsLifeDao.getByAccountUuid(accountUuid);
        if (CollectionUtils.isNotEmpty(list)) {
            accountsLifeDao.remove(list);
            String redisKey = RedisKeyConstant.ACCOUNTS_LIFE_PIC.setArg(accountUuid);
            accountBizStringRedisTemplate.delete(redisKey);
            log.info("删除我的生活数据，uuid={}", accountUuid);
            // 向量删除
//            for (AccountsLife life : list) {
//                if (REAL_PERSON_SEQ.contains(life.getSeq())) {
//                    ImgVectorRemoveDto dto = new ImgVectorRemoveDto();
//                    dto.setSenderUuid(accountUuid);
//                    String riskBizId = imageCompareRecordDao.getBizId(life.getId(), ImageCompareRecord.LIFE);
//                    dto.setBizId(StringUtils.isNotBlank(riskBizId) ? riskBizId : String.valueOf(life.getId()));
//                    dto.setSafeId(RiskSafeIdConstant.ACCOUNTS_LIFE_REAL_PERSON);
//                    RiskSafeService.imageVectorDelete(dto);
//                }
//            }
        }

        // TOOD 如果回滚，要删除排序，ab推全就无所谓
    }

    /**
     *
     * @param expireTime
     * @param startTime
     * @param limit
     * @return
     */
    public List<AccountsLifeLog> queryNotAudit(Long expireTime, Long startTime, Integer limit) {
        List<AccountsLifeLog> list = accountsLifeLogDao.queryNotAudit(expireTime, startTime, limit);
        return list;
    }

    /**
     * 刷新吸引力
     *
     * @param accountUuid
     */
    @Transactional(rollbackFor = Exception.class)
    public void refreshAllure(String accountUuid) {
        // 判断 当前是否全部删完
        List<AccountsLife> accountsLifeList = getAccountsLifeOrAccountsPhoto(accountUuid, false, false);
        LogUtil.info4Gray("生活删除触发：{}, list:{}", accountUuid, JsonUtils.objectToString(accountsLifeList));
        if (CollectionUtils.isEmpty(accountsLifeList)) {
            LogUtil.info4Dev("我的生活已全删除:{}", accountUuid);
            allureService.refreshAllure(accountUuid);
        }
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
    }

    private boolean vectorDetectUseNewSafeId(String uuid) {
        String cutoff = accountBizStringRedisTemplate.opsForValue().get(USER_PHOTO_DETECT_CUTOFF);
        if (StringUtils.isNotEmpty(cutoff)) {
            long cf = Long.parseLong(cutoff);
            if (System.currentTimeMillis() >= cf) {
                return true;
            }
        }
        return accountBizStringRedisTemplate.opsForSet().isMember(USER_PHOTO_DETECT_VERSION_SET, uuid);
    }
}
