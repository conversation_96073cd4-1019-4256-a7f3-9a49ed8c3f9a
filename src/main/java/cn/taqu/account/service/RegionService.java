package cn.taqu.account.service;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.RegionDao;
import cn.taqu.account.model.Region;
import cn.taqu.account.utils.CounterUntil;
import cn.taqu.core.utils.ValidateUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 地区相关
 * <AUTHOR>
 * 2018年1月3日 下午4:07:32
 */
@Service
public class RegionService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.Region> {

	/**
	 * baseaddr映射
	 */
	public static Map<Long, String> BASEADDR_MAP = new ConcurrentHashMap<>();

	@Autowired
	private AccountsInfoService accountsInfoService;
	@Autowired
	private RegionDao regionDao;
	@Autowired
	private StringRedisTemplate regionStringRedisTemplate;


	private String getBaseaddrByRegionId(Long id) {
	    String baseaddr = null;
	    Region region = regionDao.getById(id);
	    if(region == null) {
	        logger.warn("传入regionId异常，id={}", id);
	        return baseaddr;
	    }

	    Integer level = region.getLevel();
	    if(1 == level) {
	        logger.warn("传入regionId为1级id，不处理，id={}", id);
	        return baseaddr;
	    }else if(2 == level || 3 == level){
	        String regionPath = region.getRegion_path();
	        List<String> list = Splitter.on(",").omitEmptyStrings().splitToList(regionPath);
	        baseaddr = list.get(0) + "," + list.get(1);
	        BASEADDR_MAP.put(id, baseaddr);
	    }else {
	        logger.warn("传入regionId的level异常，不处理，id={}", id);
	        return baseaddr;
        }
	    return baseaddr;
	}

	/**
	 * 纠正baseaddr 目前通过最后一个id r2来纠正数据
	 * @param baseaddr
	 * @return
	 */
	public String correctBaseaddr(String baseaddr) throws ArrayIndexOutOfBoundsException{
		Long r2;
		try {
			r2 = Long.parseLong(baseaddr.split(",")[1]);
		}catch (ArrayIndexOutOfBoundsException e1) {
			throw e1;
		}
		String value = BASEADDR_MAP.get(r2);
		if(StringUtils.isBlank(value)){
		    // 查库
		    value = getBaseaddrByRegionId(r2);
		}
		return StringUtils.isBlank(value)? baseaddr: value;
	}

	public Map<Long, Region> findByIdList(Collection<Long> idColl) {
		List<Long> idList = new ArrayList<>(idColl);

		Map<Long, Region> result = new HashMap<>();
		Set<Long> missIdSet = new HashSet<>();

		RedisSerializer<String> stringRedisSerializer = regionStringRedisTemplate.getStringSerializer();
		List<Object> hashValues = regionStringRedisTemplate.executePipelined((RedisConnection connection) -> {
			idList.forEach(id -> {
				byte[] key = stringRedisSerializer.serialize(RedisKeyConstant.REGION.setArg(id));
				connection.hGetAll(key);
			});
			return null;
		}, stringRedisSerializer);

		for (int i = 0; i < hashValues.size(); i++) {
			Map<Object, Object> hashValue = (Map<Object, Object>)hashValues.get(i);
			Long id = idList.get(i);
			//缓存未命中
			if(hashValue == null || hashValue.isEmpty()) {
				missIdSet.add(id);
				continue;
			}

			//缓存命中
			Region region = new Region();
			region.setSign(MapUtils.getString(hashValue, "sign", ""));
			region.setParent_id(MapUtils.getLong(hashValue, "parent_id"));
			region.setRegion_path(MapUtils.getString(hashValue, "region_path", ""));
			region.setLevel(MapUtils.getInteger(hashValue, "level"));
			region.setRegion_name(MapUtils.getString(hashValue, "region_name", ""));
			region.setIs_unavailable(MapUtils.getInteger(hashValue, "is_unavailable"));
			region.setSort(MapUtils.getInteger(hashValue, "sort"));
			region.setPostcode(MapUtils.getInteger(hashValue, "postcode"));
			region.setLatitude(MapUtils.getString(hashValue, "latitude", ""));
			region.setLongitude(MapUtils.getString(hashValue, "longitude", ""));
			result.put(id, region);
		}

		//缓存未命中的查询数据库
		if(!missIdSet.isEmpty()) {
			List<Region> regionList = regionDao.getByIdList(missIdSet);

			regionStringRedisTemplate.executePipelined((RedisConnection connection) -> {
				for(Region region : regionList) {
					Map<String, String> hashValue = new HashMap<>();
					hashValue.put("sign", StringUtils.trimToEmpty(region.getSign()));
					hashValue.put("parent_id", region.getParent_id() == null ? "0" : region.getParent_id().toString());
					hashValue.put("region_path", StringUtils.trimToEmpty(region.getRegion_path()));
					hashValue.put("level", region.getLevel() == null ? "0" : region.getLevel().toString());
					hashValue.put("region_name", StringUtils.trimToEmpty(region.getRegion_name()));
					hashValue.put("is_unavailable", region.getIs_unavailable() == null ? "0" : region.getIs_unavailable().toString());
					hashValue.put("sort", region.getSort() == null ? "0" : region.getSort().toString());
					hashValue.put("postcode", region.getPostcode() == null ? "" : region.getPostcode().toString());
					hashValue.put("latitude", StringUtils.trimToEmpty(region.getLatitude()));
					hashValue.put("longitude", StringUtils.trimToEmpty(region.getLongitude()));
					result.put(region.getId(), region);

					byte[] byteKey = stringRedisSerializer.serialize(RedisKeyConstant.REGION.setArg(region.getId()));
					Map<byte[], byte[]> byteMap = new HashMap<>(hashValue.size());
					hashValue.forEach((k, v) -> byteMap.put(stringRedisSerializer.serialize(k), stringRedisSerializer.serialize(v)));
					connection.hMSet(byteKey, byteMap);
				}
				return null;
			});
		}

		return result;
	}

	/**
	 * 获取用户地区
	 * @param accountUuid
	 * @return
	 */
	public Map<String, String> getUserPosition(String accountUuid) {
		Map<String, String> accountRegion = this.batchGetUserPosition(new String[]{accountUuid}).get(accountUuid);
		return accountRegion == null ? Maps.newHashMap() : accountRegion;
	}

	public Map<String, Map<String, String>> batchGetUserPosition(String[] accountUuids) {
		Map<String, Map<String, Object>> accountInfos = accountsInfoService.getInfoByUuid(accountUuids, new String[]{"baseaddr"}, "1", false, true);
		return this.mGetBaseaddrName(accountInfos);
	}

	/**
	 * 获取用户所在地城市名称，注意 accountInfos中需要包含baseaddr信息
	 * @param accountInfos
	 * @return key:用户uuid -> 用户所在城信息
	 */
	public Map<String, Map<String, String>> mGetBaseaddrName(Map<String, Map<String, Object>> accountInfos) {
		CounterUntil.cachePenetrated("baseaddr");
		return mGetRegionName(accountInfos, "baseaddr");
	}

	/**
	 * 获取用户所在地城市名称，注意 accountInfos中需要包含baseaddr信息
	 * @param accountInfos
	 * @return key:用户uuid -> 城市信息
	 */
	public Map<String, Map<String, String>> mGetRegionName(Map<String, Map<String, Object>> accountInfos, String field) {
	    Map<String, Map<String, String>> result = new HashMap<>();
	    Set<Long> cityIdSet = new HashSet<>();

	    for(Map.Entry<String, Map<String, Object>> accountInfo : accountInfos.entrySet()) {
	        String accountUuid = StringUtils.trimToEmpty(accountInfo.getKey());
	        if(accountUuid.isEmpty()) {
	            continue;
	        }
	        Map<String, String> accountRegion = new HashMap<>();
	        result.put(accountUuid, accountRegion);

	        String value = MapUtils.getString(accountInfo.getValue(), field, "");
	        String[] valueArray = StringUtils.split(value, ",");
	        String cityId = ArrayUtils.getLength(valueArray) > 1 ? StringUtils.trimToEmpty(valueArray[1]) : "";
	        if(!ValidateUtil.isDecimal(cityId)) {
	            continue;
	        }

	        cityIdSet.add(Long.valueOf(cityId));
	        accountRegion.put("city_id", cityId);
	    }

	    Map<Long, Region> idRegionMap = this.findByIdList(cityIdSet);
	    for(Map.Entry<String, Map<String, String>> accountRegion : result.entrySet()) {
	        Map<String, String> regionInfo = accountRegion.getValue();
	        Long cityId = MapUtils.getLong(regionInfo,"city_id");
	        Region region = idRegionMap.get(cityId);
	        if(region != null) {
	            regionInfo.put("city_name", region.getRegion_name());
	            regionInfo.put("longitude", region.getLongitude());
	            regionInfo.put("latitude", region.getLatitude());
	        }
	        regionInfo.remove("city_id");
	    }
	    return result;
	}

	public Map<Long, Map<String, Object>> mgetInfoByCityId(List<Long> cityIdList) {
		Map<Long, Map<String, Object>> result = new HashMap<>(cityIdList.size());

		//城市数据
		Map<Long, Region> cityRegionMap = this.findByIdList(cityIdList);
		List<Long> provinceIdList = cityRegionMap.values().stream()
				.filter(city -> city.getParent_id() != null && city.getParent_id() > 0)
				.map(Region::getParent_id)
				.distinct()
				.collect(Collectors.toList());

		//省份数据
		Map<Long, Region> provinceRegionMap = this.findByIdList(provinceIdList);

		cityIdList.stream().forEach(cityId -> {
			Map<String, Object> infoMap = new HashMap<>();
			result.put(cityId, infoMap);

			Region city = cityRegionMap.get(cityId);
			if(city == null) {
				return;
			}
			infoMap.put("city", cityId);
			infoMap.put("city_name", city.getRegion_name());
			infoMap.put("longitude", StringUtils.trimToEmpty(city.getLongitude()));
			infoMap.put("latitude", StringUtils.trimToEmpty(city.getLatitude()));

			Region province = provinceRegionMap.get(city.getParent_id());
			if(province == null) {
				return;
			}
			infoMap.put("province", city.getParent_id());
			infoMap.put("province_name", province.getRegion_name());
		});

		return result;
	}

	public Map<String,Object> getInfoByCityName(String cityName){
		Region cityInfo = regionDao.getByRegionName(cityName);
		if(null == cityInfo){
			return Collections.emptyMap();
		}
		//数据库里可能没有存储经纬度信息，故再从缓存里捞取其他信息
		Map<Long, Map<String, Object>> mCityMap = mgetInfoByCityId(Lists.newArrayList(cityInfo.getId()));
		return mCityMap.get(cityInfo.getId());
	}

	public Map<String,Map<String,Object>> mgetProvinceCityInfoByUuids(String[] accountUuids){
		Map<String, Map<String, Object>> accountInfos = accountsInfoService. getInfoByUuid(accountUuids, new String[]{"baseaddr"}, "1", false, true);
		Set<Long> cityIdSet = new HashSet<>();
		Map<String,Long> uuidCityMap = Maps.newHashMap();
		for(Map.Entry<String, Map<String, Object>> accountInfo : accountInfos.entrySet()) {
			String accountUuid = StringUtils.trimToEmpty(accountInfo.getKey());
			if(accountUuid.isEmpty()) {
				continue;
			}
			String baseaddr = MapUtils.getString(accountInfo.getValue(), "baseaddr", "");
			String[] baseaddrArray = StringUtils.split(baseaddr, ",");
			String cityId = ArrayUtils.getLength(baseaddrArray) > 1 ? StringUtils.trimToEmpty(baseaddrArray[1]) : "";
			if(!ValidateUtil.isDecimal(cityId)) {
				continue;
			}
			cityIdSet.add(Long.valueOf(cityId));
			uuidCityMap.put(accountUuid,Long.valueOf(cityId));
		}
		List<Long> cityIdList = cityIdSet.stream().collect(Collectors.toList());
		Map<Long, Map<String, Object>> cityInfoMap = mgetInfoByCityId(cityIdList);
		Map<String,Map<String,Object>> result = Maps.newHashMap();
		uuidCityMap.forEach((uuid,cityId)->{
			if(null != cityInfoMap.get(cityId)){
				result.put(uuid,cityInfoMap.get(cityId));
			}
		});
		return result;
	}
}
