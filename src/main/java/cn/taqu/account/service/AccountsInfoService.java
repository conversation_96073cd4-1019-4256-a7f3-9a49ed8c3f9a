/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.service;

import cn.taqu.account.bo.SoulFitInfo;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.AvatarConst;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.*;
import cn.taqu.account.dto.*;
import cn.taqu.account.etcd.AlEvaluationVersionBarrier;
import cn.taqu.account.etcd.TqUserGradeClient;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.manager.AccountsThirdPartyManager;
import cn.taqu.account.model.*;
import cn.taqu.account.utils.CounterUntil;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.LocationUtils;
import cn.taqu.account.utils.StreamUtil;
import cn.taqu.account.vo.*;
import cn.taqu.account.vo.Initialize.InitializeAccountVo;
import cn.taqu.account.vo.Initialize.ShowDialogVo;
import cn.taqu.account.vo.life.AccountsLifeVo;
import cn.taqu.account.vo.resp.*;
import cn.taqu.core.common.client.*;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.*;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.taqu.mp.account.client.MPAccountClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 账号信息Service
 *
 * @author:laikunzhen
 */
@Service
public class AccountsInfoService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsInfo> {
    @Autowired
    private AccountsPhotoDao accountsPhotoDao;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private AccountsGuestDao accountsGuestDao;
    @Autowired
    private MembersDao membersDao;
    @Autowired
    private AccountsMedalService accountsMedalService;
    @Autowired
    private AccountsLevelService accountsLevelService;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsForumProfileDao accountsForumProfileDao;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private AccountsForumProfileService accountsForumProfileService;
    @Autowired
    private MembersService membersService;
    @Autowired
    private AvatarHandleService avatarHandleService;
    @Autowired
    private AccountsLabelService accountsLabelService;
    @Autowired
    private AccountsEvaluateService accountsEvaluateService;
    @Autowired
    private AccountsAchievementService accountsAchievementService;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    @Qualifier("ticketStringRedisTemplate")
    private StringRedisTemplate ticketStringRedisTemplate;
    @Autowired
    private UuidInfoQueryService uuidInfoQueryService;
    @Autowired
    private VoiceSignInfoService voiceSignInfoService;
    @Autowired
    private AccountsLikeLabelService accountsLikeLabelService;
    @Autowired
    private AccountsCardService accountsCardService;
    @Autowired
    private ModifyGrowScoreService modifyGrowScoreService;
    @Autowired
    private ThirdAppInfoRecordService thirdAppInfoRecordService;
    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;
    @Autowired
    private AccountsRegAttributionService accountsRegAttributionService;
    @Autowired
    private RegionService regionService;
    @Autowired
    private AccountsLocationInfoService accountsLocationInfoService;
    @Autowired
    @Lazy
    private HomepageBackgroundService homepageBackgroundService;
    @Autowired
    @Lazy
    private AllureService allureService;
    @Autowired
    private CounterUntil counterUntil;

    @Autowired
    private MPAccountClient mpAccountClient;

    @Autowired
    private AccountsThirdPartyManager accountsThirdPartyManager;

    @Autowired
    private SoaService soaService;

    @Autowired
    private AccountsCertificationService accountsCertificationService;

    @Autowired
    private IdealTargetService idealTargetService;

    @Autowired
    private SchoolService schoolService;

    @Autowired
    private AccountLabelServiceV2 accountLabelServiceV2;

    @Autowired
    private AccountsLifeService accountsLifeService;

    @Autowired
    private ProfileOptimizationUiService profileOptimizationUiService;

    @Resource(name = "accountBizStringRedisTemplate")
    private StringRedisTemplate bizRedisTemplate;
    @Autowired
    @Lazy
    private AccountBaseInfoManager accountBaseInfoManager;

    @Resource
    private AbTestService abTestService;

    private static Logger LOGGER = LoggerFactory.getLogger(AccountsInfoService.class);

    /**
     * 注册成功后，如果有传头像,去除String头像 中的 “https://avatar01.touchcdn.com/”部分
     */
    public static String regReplaceAvatarPart = "https://avatar01.touchcdn.com/";

    /**
     * 调用搜索接口，根据nickname搜索用户
     */
    private final static String SEARCH_SERVICE = "search";
    private final static String SEARCH_METHOD = "select";

    private final static Integer ANDROID_PLATFORM_ID = 1;
    private final static Integer IPHONE_PLATFORM_ID = 2;
    private final static Integer IPAD_PLATFORM_ID = 3;
    private static Long DATA_ANDROID_VERSION1 = 7200L; // <
    private static Long DATA_IOS_VERSION1 = 10120L; // <
    private static Long DATA_ANDROID_VERSION2 = 7213L; // <
    private static Long DATA_IOS_VERSION2 = 10130L; // <
    private static Long DATA_ANDROID_VERSION3 = 7345L; // <
    private static Long DATA_IOS_VERSION3 = 10166L; // <
    private static Long DATA_ANDROID_VERSION4 = 89999L; // <
    private static Long DATA_IOS_VERSION4 = 10250L; // <
    private static Long DATA_ANDROID_VERSION5 = 79999L; // <
    private static Long DATA_IOS_VERSION5 = 10282L; // <

    /**
     * 灵魂契合度基本分
     */
    private static Map<String, SoulFitInfo> soulFitInfoMap = Maps.newHashMap();
    private static String LIKE_DESC_DOC1 = "你是ta喜欢的类型";
    private static String LIKE_DESC_DOC2 = "ta是你喜欢的类型";
    private static String LIKE_DESC_DOC3 = "你们是对方喜欢的类型";

    // 处理异常图片判断
    private static List<String> PIC_SUFFIX_LIST = Lists.newArrayList("jpg", "JPG", "png", "PNG", "jpeg", "JPEG");
    // 处理异常图片判断
    private static String PIC_SUFFIX_STYLE = "_style";

    /**
     * 交友资料完善引导（引导开关+引导配置）
     */
    private static Map<String, String> friendChecksMap = Maps.newHashMap();
    /**
     * 列表资料卡完善引导（标题、描述、展示位置）
     */
    private static Map<String, Map<String, String>> dataCardMap = Maps.newHashMap();

    // 2024.06.07 历史业务，可以不用判断
//    public static Boolean SWITCH_PROLE_VERIFY_STATUS_TO_REAL_PERSON_CERTIFICATION = false;
    // 2024.06.07 历史业务，可以不用判断
//    public static Integer VERSION_CONTROL_1 = 1;
    // 2024.06.07 历史业务，可以不用判断
//    public static Long ANDROID_VERSION_1 = 7504L;
    // 2024.06.07 历史业务，可以不用判断
//    public static Long IOS_VERSION_1 = 10306L;
    public static Integer SWITCH_RUN_SCRIPT_ACCOUNT_AVATAR = 0;
    public static Integer SWITCH_RUN_SCRIPT_ACCOUNT_COVER = 0;
    public static Long ACCOUNT_AVATAR_START_TIME = 1641916800L;
    public static Long ACCOUNT_COVER_START_TIME = 1641916800L;

    public static final String IDENTITY_NO_18="****************";

    public static final String  IDENTITY_NO_15="*************";

    private static Map<String, Object> getMessageBoxTips(String app) {
        return JSON.parseObject("{\n" +
                "\"realPersonTipsSceneMyInfo\":{\n" +
                "\"title\":\"温馨提示\",\n" +
                "\"info\":\"" + app + "提倡真实交友，自我介绍需要完成真人认证才能填写哦～\",\n" +
                "\"button_1\":\"取消\",\n" +
                "\"button_2\":\"真人认证\",\n" +
                "\"button_1_event\":\"cancel\",\n" +
                "\"button_2_event\":\"m=forum&a=liveAuthenticityV2\"\n" +
                "}\n" +
                "}");
    }

    public static void initMessageBoxTips(String conf){
        try {
        }catch (Exception e){
            LOGGER.error("initMessageBoxTips error,conf={}", conf, e);
        }
    }
    /**
     * 1118需求开关： 头像认证和真人认证映射
     * @param conf
     */
    public static void initSwitchOfAccounts(String conf) {
        try {
            Map<String, Object> map = JsonUtils.stringToObject2(conf, new TypeReference<Map<String, Object>>() {
            });
         // 2024.06.07 历史业务，可以不用判断
//            SWITCH_PROLE_VERIFY_STATUS_TO_REAL_PERSON_CERTIFICATION = MapUtils.getBoolean(map, "switchProfileVerifyStatusToRealPersonCertification", false);
//            ANDROID_VERSION_1 =  MapUtils.getLong(map, "androidVersion_1", 7504L);
//            IOS_VERSION_1 =  MapUtils.getLong(map, "iosVersion_1", 10306L);
            SWITCH_RUN_SCRIPT_ACCOUNT_AVATAR = MapUtils.getInteger(map, "switchRunScriptAccountAvatar", 0);
            SWITCH_RUN_SCRIPT_ACCOUNT_COVER = MapUtils.getInteger(map, "switchRunScriptAccountCover", 0);
            ACCOUNT_AVATAR_START_TIME = MapUtils.getLong(map, "accountAvatarStartTime", 1641916800L);
            ACCOUNT_COVER_START_TIME = MapUtils.getLong(map, "accountCoverStartTime", 1641916800L);
        } catch (Exception e) {
            LOGGER.error("initSwitchOfAccounts error", e);
        }
    }

    public static void setDataVersion(String conf) {
        try {
            Map<String, Object> map = JsonUtils.stringToObject2(conf, new TypeReference<Map<String, Object>>() {
            });
            DATA_ANDROID_VERSION1 = MapUtils.getLong(map, "dataAndroidVersion1", 7200L);
            DATA_IOS_VERSION1 = MapUtils.getLong(map, "dataIosVersion1", 10120L);
            DATA_ANDROID_VERSION2 = MapUtils.getLong(map, "dataAndroidVersion2", 7213L);
            DATA_IOS_VERSION2 = MapUtils.getLong(map, "dataIosVersion2", 10130L);
            DATA_ANDROID_VERSION3 = MapUtils.getLong(map, "dataAndroidVersion3", 7345L);
            DATA_IOS_VERSION3 = MapUtils.getLong(map, "dataIosVersion3", 10166L);
            DATA_ANDROID_VERSION4 = MapUtils.getLong(map, "dataAndroidVersion4", 7999L);
            DATA_IOS_VERSION4 = MapUtils.getLong(map, "dataIosVersion4", 10250L);
            DATA_ANDROID_VERSION5 = MapUtils.getLong(map, "dataAndroidVersion5", 90000L);
            DATA_IOS_VERSION5 = MapUtils.getLong(map, "dataIosVersion5", 10282L);
        } catch (Exception e) {
            LOGGER.error("配置版本控制失败", e);
        }

    }

    public static void setSoulFitInfo(String soulFitInfos) {
        try {
            List<SoulFitInfo> soulFitInfoList = JsonUtils.stringToObject2(soulFitInfos, new TypeReference<List<SoulFitInfo>>() {
            });
            soulFitInfoList.stream().forEach(info -> {
                info.setConstellations(Joiner.on("_").join(info.getConstellation1(), info.getConstellation2()));
                soulFitInfoMap.put(Joiner.on("_").join(info.getConstellation1(), info.getConstellation2()), info);
            });
        } catch (Exception e) {
            LOGGER.warn("设置灵魂契合度失败", e);
        }

    }

    public static void setFriendChecks(String friendChecks) {
        try {
            friendChecksMap = JsonUtils.stringToObject2(friendChecks, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            LOGGER.warn("设置交友资料完善引导（引导开关+引导配置）失败", e);
        }
    }

    public static void setDataCard(String dataCard) {
        try {
            dataCardMap = JsonUtils.stringToObject2(dataCard, new TypeReference<Map<String, Map<String, String>>>() {
            });
        } catch (Exception e) {
            LOGGER.warn("设置列表资料卡完善引导（标题、描述、展示位置）失败", e);
        }
    }

    @Value("${vip.url}")
    private String vip_url;// = "https%3A//h5.test.taqu.cn/html/app/member-center/index.html";
    @Value("${vip.intro.url}")
    private String vip_intro_url;// = "https%3A//h5.test.taqu.cn/html/app/member-center/index.html";

    public static String getTqcoinName(int version) {
        return getTqcoinName(version < 1);
    }

    public static String getTqcoinName(boolean isOld) {
        return isOld ? "tqcoin" : "score";
    }

    public Map<String, String> getByAccountKey(String accountKey) {
        Map<String, String> result = Maps.newHashMap();
        Object[] accountInfos = accountsManager.getByAccountKey(accountKey);
        if (accountInfos != null) {
            result.put("uuid", accountInfos[0] == null ? "" : accountInfos[0].toString());
            result.put("account_uuid", accountInfos[0] == null ? "" : accountInfos[0].toString());
            result.put("account_name", accountInfos[1] == null ? "" : accountInfos[1].toString());
            result.put("account_status", accountInfos[2] == null ? "" : accountInfos[2].toString());
            result.put("account_type", accountInfos[3] == null ? "" : accountInfos[3].toString());
        }
        return result;
    }

    /**
     * 根据用户登录凭证获取用户信息,如果获取到返回vo，vo中包含了uuid,account_status, account_type；如果没有获到取则返回null
     *
     * @param ticket 登录凭证
     * @return 返回获取到的信息
     * @Title getByTicket
     * <AUTHOR>
     * @Date 2015年9月15日 上午10:54:49
     */
    public GetByTicketVo getByTicket(String ticket) {
        if (StringUtils.isBlank(ticket)) {
            logger.warn("getByTicket ticket is null");
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        Map<String, Object> map = mpAccountClient.getUuidByTicket(ticket);
        String uuid = MapUtils.getString(map, "uuid");

        if (StringUtils.isNotBlank(uuid)) {
            GetByTicketVo vo = new GetByTicketVo();
            vo.setUuid(uuid);
            vo.setAccount_uuid(uuid);
            vo.setAccount_status(1);
            vo.setAccount_type(1);
            return vo;
        }

        logger.warn("查询ticket缓存失败，缓存不存在, {}", JSON.toJSONString(map));
        //缓存没找到，返回ticket过期
        throw new ServiceException(CodeStatus.TICKET_EXPIRE);
    }

    void setByAccountKey(String accountKey, String accountUuid) {
        String key = RedisKeyConstant.ACCOUNT_KEY_JAVA.setArg(accountKey);

        Map<String, String> accountInfoMap = new HashMap<>();
        accountInfoMap.put("account_uuid", StringUtils.trimToEmpty(accountUuid));
        ticketStringRedisTemplate.opsForHash().putAll(key, accountInfoMap);
        ticketStringRedisTemplate.expire(key, 365, TimeUnit.DAYS);
    }

    /**
     * 根据ticket获取信息，只有注册用户能正常返回，非注册用户抛出ticket_expire异常
     *
     * @param ticket
     * @return
     */
    public GetByTicketVo getByTicketIfGuestExpire(String ticket) {
        GetByTicketVo vo = this.getByTicket(ticket);
        if (Objects.equals(2, vo.getAccount_type())) {//游客账号返回ticket expire
            throw new ServiceException(CodeStatus.TICKET_EXPIRE_GUEST1);
        }
        return vo;
    }

    /**
     * 判断昵称是否存在，存在返回true；否则返回false
     *
     * @param nickName 昵称
     * @return
     * @Title isExistNickName
     * <AUTHOR>
     * @Date 2015年9月15日 下午2:18:24
     */
    public boolean isExistNickName(String nickName) {
        Boolean flag = accountsService.isExistNickName(nickName);
        return flag;
    }

    /**
     * 设置uuid缓存，初始化基础缓存可能不全了（2023.11.03）
     */
    void setUuidCache(String accountUuid, String mobile, Accounts accounts, AccountsInfo accountsInfo, AccountsForumProfile accountsForumProfile) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Long appVersion = sbp.getApp_version();
        Integer platformId = sbp.getPlatform_id();
        Integer city = sbp.getCity();

        Map<String, String> hashValues = new HashMap<>();
        //accounts信息缓存
        if (accounts != null) {
//            hashValues.put(UuidInfoField.EMAIL, StringUtils.trimToEmpty(accounts.getEmail()));
            hashValues.put(UuidInfoField.MOBILE, StringUtils.trimToEmpty(mobile));
//            hashValues.put(UuidInfoField.MOBILE_CIPHER, StringUtils.trimToEmpty(accounts.getMobile_cipher()));
            // 2024.05.27 不写入 查mobile
//            hashValues.put(UuidInfoField.IS_BIND_MOBILE, StringUtils.isBlank(accounts.getMobile()) ? "0" : "1");
            hashValues.put("account_name", StringUtils.trimToEmpty(accounts.getAccount_name()));
            hashValues.put("account_name_origin", StringUtils.trimToEmpty(accounts.getAccount_name()));
            hashValues.put("account_status", accounts.getAccount_status() == null ? "1" : accounts.getAccount_status().toString());
            hashValues.put("account_type", accounts.getAccount_type() == null ? "1" : accounts.getAccount_type().toString());
            hashValues.put("sex_type", accounts.getSex_type() == null ? "0" : accounts.getSex_type().toString());
            hashValues.put("avatar", AvatarHandleService.getAvatarOfSaveRedis(StringUtils.trimToEmpty(accounts.getAvatar())));
            hashValues.put("uuid", StringUtils.trimToEmpty(accounts.getUuid()));
            hashValues.put("create_time", accounts.getCreate_time().toString());
            hashValues.put("member_id", accounts.getMember_id() == null ? "0" : accounts.getMember_id().toString());
            hashValues.put("reg_appcode", accounts.getAppcode() == null ? "1" : accounts.getAppcode().toString());
            hashValues.put("reg_cloned", accounts.getCloned() == null ? "1" : accounts.getCloned().toString());
            hashValues.put("reg_channel", StringUtils.trimToEmpty(accounts.getChannel()));
            hashValues.put("reg_platform", accounts.getPlatform_id() == null ? "1" : accounts.getPlatform_id().toString());
            hashValues.put("reg_style", StringUtils.trimToEmpty(accounts.getReg_style()));
            hashValues.put("login_appcode", accounts.getAppcode() == null ? "1" : accounts.getAppcode().toString());
            hashValues.put("login_cloned", accounts.getCloned() == null ? "1" : accounts.getCloned().toString());
            hashValues.put("app_version", appVersion == null ? "0" : String.valueOf(appVersion));
            hashValues.put("platform_id", platformId == null ? "0" : String.valueOf(platformId));
            hashValues.put("channel", StringUtils.trimToEmpty(accounts.getChannel()));
            hashValues.put("city_id", city == null ? "" : city.toString());
        }

        //accounts forum信息缓存
        if (accountsForumProfile != null) {
            // 不写缓存，2024.05.17
//            hashValues.put(UuidInfoField.EXPERIENCE, accountsForumProfile.getExperience() == null ? "0" : accountsForumProfile.getExperience().toString());
            hashValues.put("account_actor", accountsForumProfile.getAccount_actor() == null ? "0" : accountsForumProfile.getAccount_actor().toString());
            // 不写缓存，2024.05.16
//            hashValues.put(UuidInfoField.ACCOUNT_LEVEL, accountsForumProfile.getAccount_level() == null || accountsForumProfile.getAccount_level() <= 0 ? String.valueOf(accountsLevelService.findLevelIdByLevelNum(0, 1)) : accountsForumProfile.getAccount_level().toString());//缓存存放的是levelId
            // 不写缓存，2024.05.17
//            hashValues.put(UuidInfoField.DRIVER_LEVEL, StringUtils.trimToEmpty(accountsForumProfile.getDriver_level()));
            // 不写缓存，2024.05.16
//            hashValues.put(UuidInfoField.KILOMETER, accountsForumProfile.getKilometer() == null ? "0" : accountsForumProfile.getKilometer().toString());
        }

        //accounts info信息缓存
        if (accountsInfo != null) {
            // 2024.03.06 不写入
//            hashValues.put(UuidInfoField.UPDATE_SEX_NUMBER, accountsInfo.getUpdate_sex_number() == null ? "0" : accountsInfo.getUpdate_sex_number().toString());
            // 2023.12.05 不写入
//            hashValues.put(UuidInfoField.IS_UPDATE_SEX, "0");
            // 2024.05.27 不写入 查mobile
//            hashValues.put(UuidInfoField.IS_CHECK_MOBILE, accountsInfo.getIs_check_mobile() == null ? "0" : accountsInfo.getIs_check_mobile().toString());
            // 2024.03.21 不写入
//            hashValues.put(UuidInfoField.AGE, accountsInfo.getAge() == null ? "0" : accountsInfo.getAge().toString());
            hashValues.put("baseaddr", StringUtils.trimToEmpty(accountsInfo.getBaseaddr()));
            // 2024.05.16 不写入
//            hashValues.put(UuidInfoField.TQCOIN, accountsInfo.getTqcoin() == null ? "0" : accountsInfo.getTqcoin().toString());
            hashValues.put("sexual", accountsInfo.getSexual() == null ? "0" : accountsInfo.getSexual().toString());
            hashValues.put(UuidInfoField.BIRTH, accountsInfo.getBirth() == null ? "" : accountsInfo.getBirth().toString());
            hashValues.put("personal_profile_be_allow", accountsInfo.getPersonal_profile_be_allow() == null ? "1" : accountsInfo.getPersonal_profile_be_allow().toString());
            // 2024.03.21 不写入
//            hashValues.put(UuidInfoField.CONSTELLATION, StringUtils.trimToEmpty(accountsInfo.getConstellation()));

            // 2024.04.18 不写入
//            hashValues.put(UuidInfoField.CHANGE_AGE_STATUS, accountsInfo.getChange_age_status() == null ? "1" : accountsInfo.getChange_age_status().toString());

            // 部分数据 非必须
            if(accountsInfo.getAffectivestatus() != null) {
                hashValues.put("affectivestatus", accountsInfo.getAffectivestatus().toString());
            }
            if(accountsInfo.getDating_intention() != null) {
                hashValues.put("dating_intention", accountsInfo.getDating_intention().toString());
            }
            if(StringUtils.isNotBlank(accountsInfo.getPersonal_profile())) {
                hashValues.put("personal_profile", StringUtils.trimToEmpty(accountsInfo.getPersonal_profile()));
            }
            if(StringUtils.isNotBlank(accountsInfo.getHometown())) {
                hashValues.put("hometown", StringUtils.trimToEmpty(accountsInfo.getHometown()));
            }
            if(accountsInfo.getHeight() != null) {
                hashValues.put("height", accountsInfo.getHeight().toString());
            }
            if(accountsInfo.getWeight() != null) {
                hashValues.put("weight", accountsInfo.getWeight().toString());
            }
            if(StringUtils.isNotBlank(accountsInfo.getEducation_level())) {
                hashValues.put("education", StringUtils.trimToEmpty(accountsInfo.getEducation_level()));
            }
            if(StringUtils.isNotBlank(accountsInfo.getTrade())) {
                hashValues.put("trade", StringUtils.trimToEmpty(accountsInfo.getTrade()));
            }
            if(StringUtils.isNotBlank(accountsInfo.getProfession())) {
                hashValues.put("profession", StringUtils.trimToEmpty(accountsInfo.getProfession()));
            }
            if(accountsInfo.getIncome() != null) {
                hashValues.put("income", accountsInfo.getIncome().toString());
            }

        }

        if (MapUtils.isEmpty(hashValues)) {
            return;
        }

        //加入到缓存
        String redisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid.trim());
        accountStringRedisTemplate.opsForHash().putAll(redisKey, hashValues);

    }

    public Map<String, Object> getInfoByWebTicket(String webTicket, String salt, String[] fields) {
        if (StringUtils.isBlank(webTicket)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        String accountKey = Md5Util.encryptSHA1(Md5Util.encode(webTicket + salt));
        String key = RedisKeyConstant.ACCOUNT_KEY_JAVA.setArg(accountKey);
        Map<Object, Object> entriesMap = ticketStringRedisTemplate.opsForHash().entries(key);
        String accountUuid = MapUtils.getString(entriesMap, "account_uuid");
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }

        Map<String, Object> result = this.getInfoByUuid(new String[]{accountUuid}, fields, "1", true, true).get(accountUuid);
        if (result == null) {
            result = new HashMap<>();
        }
        return result;
    }

    /**
     * 根据登录凭证ticket获取用户账号信息,查询到返回账号信息的数组，否则返回null
     *
     * @param ticket
     * @return
     * @Title getInfoByTicket
     * <AUTHOR>
     * @Date 2015年9月28日 上午9:03:37
     */
    public Map<String, Object> getInfoByTicket(String ticket, String[] fields, String version) {
        GetByTicketVo vo = this.getByTicket(ticket);
        Map<String, Object> result = this.getInfoByUuid(new String[]{vo.getUuid()}, fields, version, true, true).get(vo.getUuid());
        if (result == null) {
            result = new HashMap<>();
        }
        //偷偷返回account_type，为了社区的事业
        result.put("account_type", vo.getAccount_type() == null ? "0" : vo.getAccount_type().toString());
        return result;
    }

    public Map<String, Object> getInfoByUuidOrNicknameOrCardId(String uuidOrNicknameOrCardId, String[] fields, String version, boolean isSecret) {
        int num = 0;
        Accounts accounts = null;
        List<Accounts> list = accountsManager.listByUuidOrNicknameOrMobile(uuidOrNicknameOrCardId, uuidOrNicknameOrCardId, uuidOrNicknameOrCardId);
        if (CollectionUtils.isNotEmpty(list)) {
            accounts = list.get(0);
            num = list.size();
        }

        if (accounts == null && StringUtils.isNumeric(uuidOrNicknameOrCardId)) {
            String uuid = accountsCardService.getUuidByNormalCard(Long.valueOf(uuidOrNicknameOrCardId));
            if (StringUtils.isNotBlank(uuid)) {
                accounts = accountsManager.getByUuid(uuid, false);
                if (accounts != null) {
                    num = 1;
                }
            }
        }

        if (accounts == null) {
            return null;
        }

        String uuid = accounts.getUuid();
        Map<String, Object> info = this.getInfoByUuid(new String[]{uuid}, fields, version, isSecret, true).get(uuid);
        Map<String, Object> map = Maps.newHashMap();
        map.put("account_info", info);
        map.put("account_num", num);

        return map;
    }

    /**
     * 根据uuid获取用户信息
     *
     * @param accountUuids   用户uuid数组
     * @param fields         要获取的字段数组 具体字段看这里{@link UuidInfoField}
     * @param version        头像版本 0或空: 返回全路径; 其他: 返回不带host的uri
     * @param isSecret       手机号是否需要加密
     * @param returnLevelNum 是否返回账号等级
     * @return
     */
    public Map<String, Map<String, Object>> getInfoByUuid(String[] accountUuids, String[] fields, String version, boolean isSecret, boolean returnLevelNum) {
        if (accountUuids == null || accountUuids.length == 0) {
            return Maps.newLinkedHashMap();
        }
        if(accountUuids.length >= 70) {
            logger.warn("getInfoByUuid请求用户数较大，size={}, fields={}。不影响业务", accountUuids.length, fields);
        }
        if (fields == null || fields.length == 0) {
            logger.warn("[terrible-call]根据uuid获取用户信息，字段fields为空，将获取所有字段。");
            CounterUntil.COUNTER.labels("ALL").inc();
        } else {
            counterUntil.count(accountUuids, fields,
                    SoaBaseParams.fromThread().getOrigin(), SoaBaseParams.fromThread().getOriginSystem(),
                    SoaBaseParams.fromThread().getClientUri(), SoaBaseParams.fromThread().getMethod());
        }

        int partionSize = 100;
        int index = 0;
        List<List<String>> uuidPartitionList = new ArrayList<>();
        //返回结果map
        Map<String, Map<String, Object>> returnMap = Maps.newLinkedHashMap();

        //过滤掉空的uuid，并将uuid分成最多100个一组，按组进行操作，减少redis连接
        for (String uuid : accountUuids) {
            if (StringUtils.isBlank(uuid)) {
                continue;
            }
            returnMap.put(uuid, new HashMap<>());

            List<String> uuidPartition;
            if (index % partionSize == 0) {
                uuidPartition = new ArrayList<>();
                uuidPartitionList.add(uuidPartition);
            } else {
                uuidPartition = uuidPartitionList.get(index / partionSize);
            }

            uuidPartition.add(uuid);
            index++;
        }

        //分批查询
        uuidPartitionList.forEach(uuidPartition -> uuidInfoQueryService.getInfoFromCache(uuidPartition, fields, version, isSecret, returnMap));

        return returnMap;
    }

    public Map<String, Map<String, Object>> listInfoByNormalCard(String[] cardIds) {
        if (cardIds == null || cardIds.length == 0) {
            return Maps.newLinkedHashMap();
        }
//		if(fields == null || fields.length == 0) {
//			logger.warn("[terrible-call]根据cardId获取用户信息，字段fields为空，将获取所有字段。");
//		}

        int partionSize = 100;
        int index = 0;
        List<List<Long>> cardIdPartitionList = new ArrayList<>();
        //返回结果map
        Map<String, Map<String, Object>> returnMap = Maps.newLinkedHashMap();

        //过滤掉空的cardId，并将cardId分成最多100个一组，按组进行操作，减少redis连接
        for (String cardId : cardIds) {
            if (StringUtils.isBlank(cardId)) {
                continue;
            }
            returnMap.put(cardId, new HashMap<>());

            List<Long> cardIdPartition;
            if (index % partionSize == 0) {
                cardIdPartition = new ArrayList<>();
                cardIdPartitionList.add(cardIdPartition);
            } else {
                cardIdPartition = cardIdPartitionList.get(index / partionSize);
            }

            cardIdPartition.add(Long.valueOf(cardId));
            index++;
        }

        cardIdPartitionList.forEach(cardIdPartition -> {
            Map<Long, String> map = accountsCardService.listUuidByCardId(cardIdPartition);
            for (Map.Entry<Long, String> entry : map.entrySet()) {
                Long cardId = entry.getKey();
                String uuid = entry.getValue();
                Map<String, Object> info = MapUtils.getMap(returnMap, cardId.toString(), new HashMap<String, Object>());
                info.put("uuid", uuid);
                returnMap.put(cardId.toString(), info);
            }
        });

        return returnMap;
    }

    /**
     * 根据cardIdh
     * @param cardId
     * @return
     */
    public Map<String, Object> getInfoByNormalCard(Long cardId) {
        String accountUuid = accountsCardService.getUuidByNormalCard(cardId);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.ACCOUNT_INFO_NO_FOUND);
        }

        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts == null || (accounts.getAccount_status() != null && accounts.getAccount_status() == 0)) {
            throw new ServiceException(CodeStatus.ACCOUNT_DESTROY);
        }

        Map<String, Object> map = Maps.newHashMap();

        if (StringUtils.isNotBlank(accountUuid)) {
            map = this.getInfoByUuid(new String[]{accountUuid},
                new String[]{"account_name", "avatar", "photo", "reg_channel",UuidInfoField.ZHIMA_CERTIFICATION,UuidInfoField.IS_CHECK_MOBILE,
                    "voice_certification", "pre_profile_verify_status", "create_time", "active_time", "sex_type", "reg_cloned", "reg_appcode"},
                "1", true, false).get(accountUuid);
        }else {
            map.put("account_name", "");
            map.put("avatar", "");
        }

        map.put("account_uuid", accountUuid);
        map.put("default_card_id", cardId.toString());

        return map;
    }

    /**
     * 根据cardId获取信息
     * @param cardId
     * @return
     */
    public Map<String, String> getInfoByCard(Long cardId){
        if (cardId == null) {
            throw new ServiceException(CodeStatus.SEARCH_CARD_ID_NOT_EXIST);
        }
        String accountUuid = accountsCardService.getUuid(cardId);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.SEARCH_CARD_ID_NOT_EXIST);
        }
        // 封禁
        HashMap<String,Object> paramsMap =Maps.newHashMap();
        paramsMap.put("accountUuid",accountUuid);
        Map<String, Object> blacklistInfo = SoaService.universalBlacklistInfoV3(new String[]{BlackListTypeEnum.ACCOUNT_BLACK_LIST.name()},paramsMap);
        Map accountBlackList = MapUtils.getMap(blacklistInfo, BlackListTypeEnum.ACCOUNT_BLACK_LIST.name(), new HashMap<>());
        if(StringUtils.isNotBlank(MapUtils.getString(accountBlackList, "accountUuid", ""))) {
            throw new ServiceException(CodeStatus.SEARCH_CARD_ID_IN_BLACKLIST);
        }
        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        if (accounts == null || (accounts.getAccount_status() != null && accounts.getAccount_status() == 0)) {
            throw new ServiceException(CodeStatus.SEARCH_CARD_ID_NOT_EXIST);
        }

        Map<String, String> map = Maps.newHashMap();

        String accountName = "";
        String avatar = "";

        if (StringUtils.isNotBlank(accountUuid)) {
            Map<String, Object> info = this.getInfoByUuid(new String[]{accountUuid}, new String[]{"account_name", "avatar"}, "1", true, false).get(accountUuid);
            accountName = MapUtils.getString(info, "account_name", "");
            avatar = MapUtils.getString(info, "avatar", "");
        }
        map.put("account_uuid", accountUuid);
        map.put("account_name", accountName);
        map.put("avatar", avatar);
        map.put("default_card_id", cardId.toString());

        return map;
    }

    public AuthInfoDto getAuthInfo(String accountUuid) {
        Map<String, Map<String, Object>> infos = this.getInfoByUuid(new String[]{accountUuid}, new String[]{"voice_certification", UuidInfoField.ZHIMA_CERTIFICATION, UuidInfoField.MOBILE}, "1", false, false);
        Map<String, Object> map = infos.get(accountUuid);
        AuthInfoDto authInfoDto = new AuthInfoDto();
        authInfoDto.setUuid(accountUuid);
        final String STATUS_AUTHED = "1";

        boolean voiceAuthed = STATUS_AUTHED.equals(MapUtils.getString(map, "voice_certification"));
        authInfoDto.setVoiceAuthed(voiceAuthed);

        boolean zhimaAuthed = STATUS_AUTHED.equals(MapUtils.getString(map, UuidInfoField.ZHIMA_CERTIFICATION));
        authInfoDto.setZhimaAuthed(zhimaAuthed);

        boolean smsAuthed = StringUtils.isNotBlank(MapUtils.getString(map, UuidInfoField.MOBILE));
        authInfoDto.setSmsAuthed(smsAuthed);
        return authInfoDto;
    }

    public List<String> getMobileByUuid(String[] uuids) {
        List<String> mobileList = Lists.newArrayList();
        Map<String, Map<String, Object>> infos = this.getInfoByUuid(uuids, new String[]{UuidInfoField.MOBILE}, "1", false, false);
        infos.values().stream().filter(map -> StringUtils.isNotBlank(MapUtils.getString(map, UuidInfoField.MOBILE))).forEach(map -> mobileList.add(MapUtils.getString(map, UuidInfoField.MOBILE)));
        return mobileList;
    }

    @Transactional
    public AccountProfileVo getProfileByAccountUuid(String accountUuid) {
        //TODO 此处查询设置 is_check_mobile  登录没设置缓存，注册后续会设置缓存 可能有问题
        AccountProfileVo vo = new AccountProfileVo();

        List<AccountsInfo> infoList;
        infoList = accountsInfoDao.query(Sql.build(AccountsInfo.class, Arrays.asList("is_check_mobile", "tqcoin")).eq("account_uuid", accountUuid, false).masterDB());
        AccountsInfo accountsInfo;
        if (infoList != null && infoList.size() > 0) {
            accountsInfo = infoList.get(0);
        } else {
            logger.info("数据库设置is_check_mobile，无设置缓存, uuid={}", accountUuid);
            accountsInfo = this.findOrCreate(null);
            accountsInfo.setAccount_uuid(accountUuid);
            accountsInfo.setAccount_id(accountsService.getAccountIdByUuid(accountUuid));
            String mobile = accountsService.getMobileByUuid(accountUuid);
            accountsInfo.setIs_check_mobile((mobile != null && mobile.trim().length() > 0) ? 1 : 0);
            accountsInfo.setCreate_time(DateUtil.currentTimeSeconds());
            this.accountsInfoDao.merge(accountsInfo);
        }

        vo.setIs_check_mobile(accountsInfo.getIs_check_mobile() == null ? 0 : accountsInfo.getIs_check_mobile());
        vo.setTqcoin(accountsInfo.getTqcoin() == null ? 0 : accountsInfo.getTqcoin());

        Integer accountActor = null;
        Integer accountLevel = 1;
        List<AccountsForumProfile> profileList;
        profileList = accountsForumProfileDao.query(Sql.build(AccountsForumProfile.class, Arrays.asList("account_level", "account_actor", "experience", "driver_level", "kilometer")).eq("account_uuid", accountUuid, false).masterDB());
        if (profileList != null && profileList.size() > 0) {
            AccountsForumProfile profile = profileList.get(0);
            accountLevel = profile.getAccount_level() == null ? 1 : profile.getAccount_level();
            accountActor = profile.getAccount_actor() == null ? 0 : profile.getAccount_actor();
            vo.setExperience(profile.getExperience() == null ? 0 : profile.getExperience());
            vo.setAccount_actor(accountActor);
            vo.setDriver_level(StringUtils.trimToEmpty(profile.getDriver_level()));
            vo.setKilometer(StringUtils.isBlank(vo.getDriver_level()) ? 0L : profile.getKilometer());
        } else {
            vo.setExperience(0L);
            vo.setAccount_actor(0);
            vo.setDriver_level("");
            vo.setKilometer(0L);
        }

        Integer sexType;
        sexType = accountsManager.getSexTypeFromMasterDBByAccountUuid(accountUuid);
        Map<String, String> level;
        level = accountsLevelService.getInfoFromMasterDB(accountLevel.longValue(), sexType, "min_level_score", "max_level_score", "honor_name", "level_num", "desc");

        // 等级勋章相关不读主，后续如果有需要再改
        if (accountActor != null && accountActor > 0) {
            Map<String, String> medal = accountsMedalService.getInfo(accountActor.longValue(), "pic_url");
            vo.setMedal_url(medal.get("pic_url"));
            AccountsMedal accountsMedal = accountsMedalService.getByIdFromDb(accountActor.longValue());
            if (accountsMedal != null) {
                String medalName = accountsMedal.getMedal_name();
                vo.setHonor_name(medalName);
            }
        } else {
            vo.setMin_level_score(Long.parseLong(level.get("min_level_score")));
            vo.setMax_level_score(Long.parseLong(level.get("max_level_score")));
            vo.setHonor_name(level.get("honor_name"));
        }

        vo.setAccount_level(Integer.parseInt(level.get("level_num")));

        return vo;
    }

    public Map<String, Object> getOwnProfileByUuid(String uuid, String version) {
        //1.创建返回结果集
        Map<String, Object> returnMap = new HashMap<>();
        //2.查询用户信息
        Map<String, Object> accountInfo = this.getInfoByUuid(new String[]{uuid}, new String[]{"uuid", "account_actor", "account_status", "account_type", "avatar", UuidInfoField.EXPERIENCE, "account_name", UuidInfoField.EMAIL
                , "create_time", UuidInfoField.VIP_LEVEL, UuidInfoField.VIP_LEVEL_TITLE, UuidInfoField.VIP_POINT, UuidInfoField.VIP_LEVEL_AVATAR, "avatar_dress_id", UuidInfoField.CONSTELLATION,
                UuidInfoField.HANDSOME_STATUS, UuidInfoField.RICH_STATUS, UuidInfoField.ABILITY_STATUS, UuidInfoField.STAR_STATUS, UuidInfoField.HOST_STATUS, UuidInfoField.DOCTOR_STATUS,
                UuidInfoField.ZHIMA_CERTIFICATION, "sex_type", UuidInfoField.ACCOUNT_LEVEL, UuidInfoField.IS_CHECK_MOBILE, UuidInfoField.DRIVER_LEVEL, UuidInfoField.KILOMETER, "achievement", "create_time", "avatar_origin",
                "voice_sign", "voice_sign_audit_time", "voice_sign_url", "voice_sign_duration", "voice_sign_audit_reason", "avatar_status", "account_name_status", "account_name_origin", "face_certification", "personal_profile", "voice_sign_url", "education", "profession", "height", "weight", "profile_verify_status",
                "pre_profile_verify_status", "pass_voice_sign_url", "default_card_id", "reg_style", "income", "real_person_certification", "my_introduction_status", "my_introduction_content", "my_introduction_imgs"}, version, true, true).get(uuid);

        if (accountInfo == null) {
            throw new ServiceException(CodeStatus.PERSON_INFO_NOT_FOUND);
        }

        String accountUuid = MapUtils.getString(accountInfo, "uuid");
        returnMap.put("account_actor", accountInfo.get("account_actor"));
        returnMap.put("account_status", accountInfo.get("account_status"));
        returnMap.put("account_type", accountInfo.get("account_type"));
        returnMap.put("face_certification", accountInfo.get("face_certification"));
        String avatar;
        int avatarStatus = MapUtils.getIntValue(accountInfo, "avatar_status", 0);
        if (avatarStatus == 0) {
            avatar = MapUtils.getString(accountInfo, "avatar_origin", "");
        } else {
            avatar = MapUtils.getString(accountInfo, "avatar", "");
        }
        returnMap.put("avatar", avatar);
        returnMap.put("avatar_status", String.valueOf(avatarStatus));
        returnMap.put("experience", accountInfo.get(UuidInfoField.EXPERIENCE));
        int accountNameStatus = MapUtils.getIntValue(accountInfo, "account_name_status", 0);
        String nickName;
        if (accountNameStatus == 0) {
            nickName = MapUtils.getString(accountInfo, "account_name_origin", "");
        } else {
            nickName = MapUtils.getString(accountInfo, "account_name", "");
        }
        returnMap.put("pass_voice_sign_url", accountInfo.get("pass_voice_sign_url"));
        returnMap.put("profile_verify_status", accountInfo.get("profile_verify_status"));
        returnMap.put("pre_profile_verify_status", accountInfo.get("pre_profile_verify_status"));
        returnMap.put("nickname", nickName);
        // 2024.03.20 没使用不返回
//        returnMap.put("username", accountInfo.get("email"));
        returnMap.put("uuid", accountInfo.get("uuid"));
        returnMap.put("create_time", accountInfo.get("create_time"));
        // 2024.03.06 不查询
//        returnMap.put("is_update_sex", accountInfo.get(UuidInfoField.IS_UPDATE_SEX));
        returnMap.put("vip_level", accountInfo.get(UuidInfoField.VIP_LEVEL));
        returnMap.put("vip_level_title", accountInfo.get(UuidInfoField.VIP_LEVEL_TITLE));
        returnMap.put("vip_point", accountInfo.get(UuidInfoField.VIP_POINT));
        returnMap.put("vip_level_avatar", accountInfo.get(UuidInfoField.VIP_LEVEL_AVATAR));
        returnMap.put("avatar_dress_id", accountInfo.get("avatar_dress_id"));
        returnMap.put("constellation", accountInfo.get(UuidInfoField.CONSTELLATION));
        returnMap.put("handsome_status", MapUtils.getString(accountInfo, UuidInfoField.HANDSOME_STATUS, "0"));
        returnMap.put("rich_status", MapUtils.getString(accountInfo, UuidInfoField.RICH_STATUS, "0"));
        returnMap.put("ability_status", MapUtils.getString(accountInfo, UuidInfoField.ABILITY_STATUS, "0"));
        returnMap.put("star_status", MapUtils.getString(accountInfo, UuidInfoField.STAR_STATUS, "0"));
        returnMap.put("host_status", MapUtils.getString(accountInfo, UuidInfoField.HOST_STATUS, "0"));
        returnMap.put("doctor_status", MapUtils.getString(accountInfo, UuidInfoField.DOCTOR_STATUS, "0"));
        returnMap.put("zhima_certification", MapUtils.getString(accountInfo, UuidInfoField.ZHIMA_CERTIFICATION, "0"));
        returnMap.put("real_person_certification", MapUtils.getString(accountInfo, "real_person_certification", "2"));
        returnMap.put("driver_level", MapUtils.getString(accountInfo, UuidInfoField.DRIVER_LEVEL, ""));
        returnMap.put("kilometer", MapUtils.getString(accountInfo, UuidInfoField.KILOMETER, "0"));
        String achievement = StringUtils.defaultIfBlank(MapUtils.getString(accountInfo, "achievement"), "{}");
        returnMap.put("achievement", accountsAchievementService.formatAdorn(JsonUtils.stringToObject2(achievement, new TypeReference<Map<String, String>>() {
        })));
        returnMap.put("create_time", MapUtils.getString(accountInfo, "create_time", ""));
        returnMap.put("default_card_id", MapUtils.getString(accountInfo, "default_card_id", ""));
        returnMap.put("reg_style", MapUtils.getString(accountInfo, "reg_style", ""));
        // 2021-6-24新增返回语音签名信息
        returnMap.put("voice_sign", MapUtils.getString(accountInfo, "voice_sign", ""));
        returnMap.put("voice_sign_audit_reason", MapUtils.getString(accountInfo, "voice_sign_audit_reason", ""));
        returnMap.put("voice_sign_audit_time", MapUtils.getString(accountInfo, "voice_sign_audit_time", ""));
        returnMap.put("voice_sign_duration", MapUtils.getString(accountInfo, "voice_sign_duration", ""));
        // 不查缓存，给默认值 2024.05.16
//        Long quCoin = accountsTqcoinLogsService.getQuCoin(accountUuid).get(accountUuid);
//        returnMap.put("tqcoin", String.valueOf(quCoin));
        returnMap.put("tqcoin", "0");

        Integer sexType = MapUtils.getIntValue(accountInfo, "sex_type");
        Map<String, String> levelInfo = accountsLevelService.getInfo(MapUtils.getLongValue(accountInfo, "account_level_id"),
                sexType == 0 ? null : sexType, "min_level_score", "max_level_score", "level_num", "honor_name", "privilege_desc");
        int levelNum = Integer.parseInt(levelInfo.get("level_num"));
        returnMap.put("min_level_score", levelInfo.get("min_level_score"));
        returnMap.put("max_level_score", levelInfo.get("max_level_score"));
        returnMap.put("account_level", levelInfo.get("level_num"));
        returnMap.put("honor_name", levelInfo.get("honor_name"));
        returnMap.put("privilege_desc", levelInfo.get("privilege_desc"));

        try {
            Map<Object, Object> nextlevel = accountsLevelService.getInfoByLevelNum(levelNum + 1, null, 1, "level_num", "level_desc");
            returnMap.put("nextlevel", nextlevel);
            returnMap.put("is_top_level", "0");
        } catch (ServiceException e) {
            returnMap.put("is_top_level", "1");
        }

        //获取保密设置
        Map<String, String> secretConfig = accountsPersonalInfoService.getSecretConfig(accountUuid, null);
        returnMap.put("affectivestatus_is_secret", secretConfig.get("affectivestatus_is_secret"));
        returnMap.put("baseaddr_is_secret", secretConfig.get("baseaddr_is_secret"));
        returnMap.put("sexual_is_secret", secretConfig.get("sexual_is_secret"));
        returnMap.put("age_is_secret", secretConfig.get(UuidInfoField.AGE_IS_SECRET));
        returnMap.put("sex_type_is_secret", secretConfig.get("sex_type_is_secret"));
        returnMap.put("hometown_is_secret", secretConfig.get("hometown_is_secret"));

        //用于计算资料完整度
        returnMap.put("personal_profile", accountInfo.get("personal_profile"));
        returnMap.put("voice_sign_url", accountInfo.get("voice_sign_url"));
        returnMap.put("education", accountInfo.get("education"));
        returnMap.put("profession", accountInfo.get("profession"));
        returnMap.put("height", accountInfo.get("height"));
        returnMap.put("weight", accountInfo.get("weight"));
        returnMap.put("income", accountInfo.get("income"));

        //获取个人信息
        Map<String, String> personalInfo = (Map<String, String>) accountsPersonalInfoService.getPersonalInfo(accountUuid);
        returnMap.put("affectivestatus", personalInfo.get("affectivestatus"));

        // 当年龄保密时，age返回0
        if (secretConfig.get(UuidInfoField.AGE_IS_SECRET).equals("1")) {
            returnMap.put("age", "0");
        } else {
            returnMap.put("age", personalInfo.get(UuidInfoField.AGE));
        }

        returnMap.put("baseaddr", personalInfo.get("baseaddr"));
        returnMap.put("sex_type", personalInfo.get("sex_type"));
        returnMap.put("sexual", personalInfo.get("sexual"));
        returnMap.put("birth", personalInfo.get(UuidInfoField.BIRTH));
        returnMap.put("hometown", personalInfo.get("hometown"));
        returnMap.put("dating_intention", personalInfo.get("dating_intention"));
        returnMap.put("personal_profile_be_allow", personalInfo.get("personal_profile_be_allow"));
        returnMap.put("gender_certification", personalInfo.get(UuidInfoField.GENDER_CERTIFICATION));

        int isBindMobile = 0;

        Accounts accounts = accountsManager.getByUuid(accountUuid);
        if (accounts != null) {
            String mobile = accounts.getMobile();
            returnMap.put("email", accounts.getEmail());
            returnMap.put("mobile", mobile);

            if (mobile != null && mobile.trim().length() > 0) {
                // 1:手机号不为空则绑定手机
                isBindMobile = 1;
            }

            // 符合绑定手机，验证手机，密码不为空三个条件，则is_safe为1
            if (isBindMobile == 1 && accounts.getAccount_password() != null && !accounts.getAccount_password().trim().equals("")) {
                returnMap.put("is_safe", "1");
            } else {
                returnMap.put("is_safe", "0");
            }
        }

        returnMap.put("is_check_mobile", String.valueOf(isBindMobile));
        returnMap.put("is_bind_mobile", String.valueOf(isBindMobile));

        //6.获取用户身份信息
        Map<String, String> accountIdentityMap = Maps.newHashMap();
        // 2020.07.14 下线功能
//		Map<String, String> accountIdentityMap = accountIdentityService.getAccountIdentityMapByAccountUuid(accountUuid);
        returnMap.putAll(accountIdentityMap);

        // 这边展示主态
        returnMap.put("pass_introduction_status", MapUtils.getString(accountInfo, "my_introduction_status", "-1"));
        returnMap.put("pass_introduction_content", MapUtils.getString(accountInfo, "my_introduction_content", ""));
        String myIntroductionImgsStr = MapUtils.getString(accountInfo, "my_introduction_imgs", "");
        List<JSONObject> introductionImgs = new ArrayList<>();
        if(StringUtils.isNotBlank(myIntroductionImgsStr)){
            introductionImgs = JSON.parseObject(myIntroductionImgsStr, List.class);
            for (JSONObject img :introductionImgs) {
                img.put("img_name", ToolsService.addPhotoUrlPreDomain(AvatarHandleService.getAvatarOfSavePhoto(img.getString("img_name"))));
            }
        }
        returnMap.put("pass_introduction_imgs", introductionImgs);

        return returnMap;
    }

    /**
     * 根据accountUuid查询数据库，可以指定查询字段
     *
     * @param fields
     * @param accountUuids
     * @param master       是否查主库 true:是; false:否;
     * @return
     */
    protected List<Map<String, Object>> getFieldByAccountUuidFromDb(Collection<String> fields, Collection<String> accountUuids, boolean master) {
        Sql sql = Sql.build(AccountsInfo.class, fields).in("account_uuid", accountUuids);
        if (master) {
            sql = sql.masterDB();
        }
        return accountsInfoDao.queryForList(sql);
    }

    public List<Map<String, Object>> searchForBackstage(String accountName, String accountUuid, String mobile, String accountId, String token, String cardId) {
        if (Objects.equals("0", accountId)) {
            accountId = null;
        }
        List<Map<String, Object>> result = new ArrayList<>();
        Long memberId = null;
        if (StringUtils.isNotBlank(token)) {
            memberId = membersDao.getIdByToken(token);
        }
        // 如果有填写 cardId 优先
        if (StringUtils.isNumeric(cardId)) {
            accountUuid = accountsCardService.getUuidByNormalCard(Long.valueOf(cardId));
        }
        // 2023.08.03 如果有传手机号，则先通过手机号查中台，获取uuid
        List<String> queryUuids = new ArrayList<>();
        if (StringUtils.isNumeric(mobile)) {
            // 非cloned强制
            List<String> uuids = soaService.getUuidByPhoneWithoutClonedRestrict(mobile);
            logger.info("input uuid: {}, mobile uuids:{}", accountUuid, uuids);

            if (CollectionUtils.isNotEmpty(uuids) && StringUtils.isBlank(accountUuid)) {
                queryUuids.addAll(uuids);
            }
        }

        if (StringUtils.isNotBlank(accountUuid)) {
            queryUuids.add(accountUuid);
        }

        //所有参数都为空时返回空
        if (StringUtils.isBlank(accountName) && queryUuids.isEmpty() && StringUtils.isBlank(accountId) && memberId == null) {
            return result;
        }

        //要限制最多的返回数量，避免内存溢出
        //_%特殊转义
        accountName = StringUtils.replaceEach(accountName, new String[]{"_", "%"}, new String[]{"\\_", "\\%"});
        Collection<String> fields = Arrays.asList("account_id", "uuid", "account_ticket", "account_key", "account_name", "account_password",
                "account_type", "sex_type", "member_id", "email", "forum_status", "cs_id", "account_status", "create_time", "last_logintime",
                "appcode", "cloned", "account_coin", "min_sys_mess_id", "max_sys_mess_id", "avatar", "avatar_status", "avatar_time", "is_logged_in",
                "mobile", "mobile_cipher");
        Sql sql = Sql.build(Accounts.class, fields).rLike("account_name", accountName).in("uuid", queryUuids).eq("member_id", memberId).eq("account_id", accountId).eq("account_type", 1);
        List<Accounts> accountsList = this.accountsManager.queryForPage(sql, 1, 50, false);

        // 注销状态、时间获取
        List<String> uuids = accountsList.stream().map(Accounts::getUuid).collect(Collectors.toList());
        Map<String, Object> statusResponse = mpAccountClient.listAccountStatus(JsonUtils.objectToString(uuids));
        List<AccountStatusDTO> statusList = JsonUtils.stringToObject(JsonUtils.objectToString(statusResponse.get("list")), new TypeReference<List<AccountStatusDTO>>() {
        });
        Map<String, AccountStatusDTO> statusMap = statusList.stream().collect(Collectors.toMap(AccountStatusDTO::getUuid, v -> v, (k1, k2) -> k1));

        for (Accounts accounts : accountsList) {
            String uuid = accounts.getUuid();

            // 处理脏数据的情况
            if (StringUtils.isBlank(uuid)) {
                continue;
            }

            Map<String, Object> map = new HashMap<>();
            map.put("account_id", accounts.getAccount_id());
            String avatar = accounts.getAvatar();
            if(StringUtils.isNotBlank(avatar)){
                avatar = avatar.replaceAll(accountsService.getAvatarUrlPrefix(), "");
                avatar = accountsService.getAvatarUrlPrefix() + avatar;
            }
            map.put("avatar", avatar);
            map.put("account_name", accounts.getAccount_name());
            map.put("mobile", StringUtil.mobileSecret(accountsService.getMobileByUuid(uuid)));
            map.put("email", StringUtils.trimToEmpty(accounts.getEmail()));
            map.put("account_status", accounts.getAccount_status());
            map.put("uuid", uuid);
            map.put("sex_type", accounts.getSex_type());
            map.put("create_time", accounts.getCreate_time());
            map.put("last_logintime", accounts.getLast_logintime());
            map.put("member_id", accounts.getMember_id());
            map.put("token", membersDao.getTokenById(accounts.getMember_id(), false));
            Integer accountLevel = accountsForumProfileDao.getAccountLevelByAccountUuid(uuid);
            map.put("account_level", accountsLevelService.findLevelNumByLevelId(accountLevel == null ? 1 : accountLevel));
            AccountsInfo accountsInfo = accountsInfoDao.getByAccountUuid(uuid);
            if (accountsInfo != null) {
                map.put("aliyun_final_decision", accountsInfo.getAliyun_final_decision());
                map.put("voice_certification", accountsInfo.getVoice_certification());
                map.put("is_check_mobile", accountsInfo.getIs_check_mobile());
            } else {
                map.put("aliyun_final_decision", 0);
                map.put("voice_certification", 0);
                map.put("is_check_mobile", 0);
            }
            //2017.5.8 qiuyuhua 临时处理
            map.put("account_level_id", accountLevel == null ? 1 : accountLevel);
            map.put("third_part", accountsThirdPartyManager.getValidTypeByAccountUuid(uuid));
            map.put("reg_appcode", accounts.getAppcode() == null ? "1" : accounts.getAppcode());
            map.put("reg_cloned", accounts.getCloned() == null ? "1" : accounts.getCloned());
            Map<String, Object> accountInfoMap = this.getInfoByUuid(new String[]{uuid}, new String[]{"login_appcode", "login_cloned", "default_card_id"}, "1", false, true).get(uuid);
            map.put("login_appcode", MapUtils.getString(accountInfoMap, "login_appcode"));
            map.put("login_cloned", MapUtils.getString(accountInfoMap, "login_cloned"));
            map.put("default_card_id", MapUtils.getString(accountInfoMap, "default_card_id"));
            // 用户状态
            AccountStatusDTO status = statusMap.getOrDefault(uuid, new AccountStatusDTO());
            map.put("account_status", status.getStatus());
            map.put("destroy_time", status.getDestroyTime());
            result.add(map);
        }

        return result;
    }

    public Map<String, Object> getInfoByUuidForBackstage(String uuid, String version, String cardId) {
        Map<String, Object> result = new HashMap<>();
        // 如果有填写 cardId 优先
        if (StringUtils.isNumeric(cardId)) {
            String accountUuid = accountsCardService.getUuidByNormalCard(Long.valueOf(cardId));
            if (StringUtils.isNotBlank(accountUuid)) {
                uuid = accountUuid;
            }
        }
        if (StringUtils.isBlank(uuid)) {
            return result;
        }

        List<String> fields = Arrays.asList("uuid", "email", "account_name", "account_type", "sex_type", "avatar", "account_status", "member_id",
                "mobile", "create_time", "last_logintime", "max_sys_mess_id", "min_sys_mess_id", "forum_status", "appcode", "cloned");
        List<Map<String, Object>> accountsList = accountsService.getFieldByUuidFromDb(fields, Collections.singletonList(uuid));
        if (accountsList.isEmpty()) {
            return result;
        }
        Integer accountType = MapUtils.getInteger(accountsList.get(0), "account_type", 2);
        result.put("letter_enable", Objects.equals(1, accountType) ? 1 : 0);
        result.putAll(accountsList.get(0));
        result.put("reg_appcode", MapUtils.getIntValue(result, "appcode", 1));
        result.put("reg_cloned", MapUtils.getIntValue(result, "cloned", 1));
        result.remove("appcode");
        result.remove("cloned");

        Map<String, Object> infoMap = singleGetInfo(
                uuid,
                new String[]{UuidInfoField.BIRTH, UuidInfoField.CONSTELLATION, "baseaddr", "sexual", UuidInfoField.TQCOIN, "affectivestatus", UuidInfoField.IS_CHECK_MOBILE, UuidInfoField.UPDATE_SEX_NUMBER,
                        "pass_personal_profile", "hometown", "dating_intention", "sexual", "personal_profile_be_allow", UuidInfoField.GENDER_CERTIFICATION, "voice_certification",
                        UuidInfoField.ZHIMA_CERTIFICATION, UuidInfoField.ALIYUN_FINAL_DECISION, UuidInfoField.SM_TYPE, "chat_real_certification"}
        );
        result.putAll(infoMap);
        result.put("personal_profile", infoMap.get("pass_personal_profile"));
        long birth = MapUtils.getLongValue(result, UuidInfoField.BIRTH);

        //社区后台个人主页只显示审核通过的个人简介
//        Integer personalProfileBeAllow = MapUtils.getInteger(infoMap, "personal_profile_be_allow");
//        if (null != personalProfileBeAllow && 1 != personalProfileBeAllow) {
//            result.put("personal_profile", "");
//        }

        result.put("age", birth == 0 ? "" : ToolsService.calAgeFromBirth(birth));

        List<String> profileFields = Arrays.asList("account_level", "account_actor", "experience", "driver_level", "kilometer");
        List<Map<String, Object>> accountProfiles = this.accountsForumProfileService.getFieldByAccountUuidFromDb(profileFields, Collections.singletonList(uuid), false);
        if (!accountProfiles.isEmpty()) {
            Map<String, Object> accountProfile = accountProfiles.get(0);
            long accountActor = Math.max(MapUtils.getLongValue(accountProfile, "account_actor", 0), 0L);
            String medalName = accountsMedalService.getInfo(accountActor, "medal_name").get("medal_name");
            result.put("medal_name", medalName);
            result.putAll(accountProfile);
        }
        Integer accountLevel = MapUtils.getInteger(result, "account_level", 1);
        result.put("account_level", accountsLevelService.findLevelNumByLevelId(accountLevel));
        //2017.5.8 qiuyuhua 临时处理
        result.put("account_level_id", accountLevel);
        // 2024.07.30 判断下环境，如果是测试环境，就不加密
        if(LocalConfUtil.getLocalEnv().contains("test")) {
            result.put("mobile",  accountsService.getMobileByUuid(uuid));
        }else {
            result.put("mobile",  StringUtil.mobileSecret(accountsService.getMobileByUuid(uuid)));
        }

        String avatar = MapUtils.getString(result, "avatar", null);
        if (avatar != null) {
            avatar = fixUrl(avatar);
            result.put("avatar", avatarHandleService.getAvatarByVersion(avatar, version));
        }

        AccountsMemberInfo accountsMemberInfo = accountsMemberInfoService.getByUuid(uuid);
        if (accountsMemberInfo != null) {
            result.put("app_version", StringUtil.nullNumberToEmptyString(accountsMemberInfo.getAppVersion()));
            result.put("login_appcode", accountsMemberInfo.getAppcode() == null ? 1 : accountsMemberInfo.getAppcode());
            result.put("login_cloned", accountsMemberInfo.getCloned() == null ? 1 : accountsMemberInfo.getCloned());
        }
        Long memberId = MapUtils.getLong(accountsList.get(0), "member_id", 0L);
        if (memberId > 0) {
            String token = membersService.getTokenById(memberId, true);
            result.put("token", StringUtils.trimToEmpty(token));
        }

        //返回相册
        List<Map<String, String>> photoList = accountsPhotoService.getAccountsAllPhoto(uuid, false);
        if (photoList.isEmpty()) {
            Map<String, String> photoMap = new HashMap<>();
            photoMap.put("photo_url", AvatarConst.DEFAULT_MALE_AVATAR);
            photoMap.put("bucket", accountsService.getAvatarUrlPrefix());
            photoList.add(photoMap);
        }
        result.put("photo", photoList);
        //第三方绑定信息
        result.put("third_party", accountsThirdPartyManager.getValidTypeByAccountUuid(uuid));
        result.put("account_id", MapUtils.getString(result, "uuid", "0"));
        result.put("black_status", SoaService.isInAccountsBlackList(MapUtils.getInteger(result, UuidInfoField.REG_APPCODE), uuid) ? "1" : "0");

        Map<String, Object> accountInfoMap = this.getInfoByUuid(new String[]{uuid}, new String[]{"default_card_id"}, "1", false, true).get(uuid);
        result.put("default_card_id", MapUtils.getString(accountInfoMap, "default_card_id"));

        return result;
    }

    /**
     * 根据account_uuid获取AccountsInfo，如果没获取到则创建一个
     *
     * @param account_uuid
     * @return
     * @Title findOrCreate
     * <AUTHOR>
     * @Date 2015年10月16日 下午7:51:52
     */
    public AccountsInfo findOrCreate(String account_uuid) {
        // 传入空uuid直接创建对象
        if(StringUtils.isBlank(account_uuid)){
            AccountsInfo accountsInfo = new AccountsInfo();
            accountsInfo.setCreate_time(DateUtil.currentTimeSeconds());
            accountsInfo.setPersonal_profile_status(CommonAuditStatus.NONE.getStatus());
            accountsInfo.setPhoto_number(0);
            return accountsInfo;
        }
        List<AccountsInfo> accountsInfoList = accountsInfoDao.queryByProp("account_uuid", account_uuid, AccountsInfoDao.FIELDS_ALL);
        if (accountsInfoList != null && accountsInfoList.size() > 0) {
            return accountsInfoList.get(0);
        }
        AccountsInfo accountsInfo = new AccountsInfo();
        accountsInfo.setAccount_id(accountsService.getAccountIdByUuid(account_uuid));
        accountsInfo.setAccount_uuid(account_uuid);
        String mobile = accountsService.getMobileByUuid(account_uuid);
        accountsInfo.setIs_check_mobile(StringUtils.isNotBlank(mobile) && ValidateUtil.isMobile(mobile) ? 1 : 0);//只要有绑定手机号，那么is_check_moile就设置为1
        accountsInfo.setCreate_time(DateUtil.currentTimeSeconds());
        accountsInfo.setPersonal_profile_status(CommonAuditStatus.NONE.getStatus());
        accountsInfo.setPhoto_number(0);
        return accountsInfo;
    }

    @Transactional
    public AccountsInfo save(AccountsInfo accountsInfo) {
        AccountsInfo info = accountsInfoDao.merge(accountsInfo);
        accountsInfoDao.flush();
        return info;
    }

    /**
     * 根据uuid批量查询最后登录设备信息，返回以uuid为key，以设备信息为value的map
     *
     * @param uuids
     * @param returnToken 是否返回设备token, 0:否; 1:是
     * @return
     */
    public Map<String, Map<String, String>> mGetLastLoginDeviceByUuids(String[] uuids, String returnToken) {
        Map<String, Map<String, Object>> accountInfoMap = this.getInfoByUuid(uuids, new String[]{"member_id"}, "1", true, true);
        List<Long> memberIdList = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : accountInfoMap.entrySet()) {
            Long memberId = MapUtils.getLong(entry.getValue(), "member_id", null);
            if (memberId != null) {
                memberIdList.add(memberId);
            }
        }

        Map<String, Map<String, String>> result = new HashMap<>();
        Map<Long, Map<String, String>> memberInfoMap = membersService.mGetInfoByMemberIdList(memberIdList, returnToken);
        for (Map.Entry<String, Map<String, Object>> entry : accountInfoMap.entrySet()) {
            Long memberId = MapUtils.getLong(entry.getValue(), "member_id", null);
            result.put(entry.getKey(), memberInfoMap.get(memberId));
        }

        return result;
    }

    public List<String> listUuidByAccountName(String accountName) {
        List<String> uuidList = this.accountsManager.listUuidByAccountName(accountName);
        if (CollectionUtils.isEmpty(uuidList)) {
            return Lists.newArrayList();
        }
        return uuidList;
    }

    public String getUuidByMobile(String mobile) {
        String uuid = accountsService.getUuidByMobile(mobile);
        if (StringUtils.isBlank(uuid)) {
            throw new ServiceException(CodeStatus.MOBILE_NOT_EXISTS);
        }
        return uuid;
    }

    public Map<String, String> getUuidAndNameByMobile(String mobile) {
        Map<String, String> result = Maps.newHashMap();
        Accounts accounts = accountsManager.getUuidAndNameByMobile(mobile);
        result.put("mobile", mobile);
        String accountUuid = "";
        String accountNmae = "";
        if (accounts != null) {
            accountUuid = StringUtils.defaultString(accounts.getUuid(), "");
            accountNmae = StringUtils.defaultString(accounts.getAccount_name(), "");
        }
        result.put("account_uuid", accountUuid);
        result.put("account_name", accountNmae);

        return result;
    }

    /**
     * 根据手机号批量查询账号uuid
     *
     * @param mobiles
     * @return
     */
    public Map<String, String> batchGetUuidByMobile(String[] mobiles) {
        Map<String, String> result = new LinkedHashMap<>();
        int mobileLen = mobiles.length;
        List<String> mobileList = new ArrayList<>();
        for (int i = 0; i < mobileLen; i++) {
            String mobile = mobiles[i];
            if (StringUtils.isBlank(mobile)) {
                continue;
            }
            mobile = mobile.trim();
            result.put(mobile, "");
            mobileList.add(mobile);
            //每集满50个去查一次数据库
            if (mobileList.size() == 50 || i == mobileLen - 1) {
                Map<String, String> map = accountsManager.batchGetUuidByMobile(mobileList);
                result.putAll(map);
                mobileList = new ArrayList<>();
            }
        }
        return result;
    }

    public Map<String, String> getSetNicknamePriv(String accountUuid, Integer appcode) {
        return this.getSetNicknamePrivByAccount(accountUuid, appcode);
    }

    /**
     * 获取修改昵称的权限，返回示例
     * <pre>
     *        {
     * 		 	"nick_name_enable": 0/1, //是否可以修改昵称 0:否; 1:是
     * 		    "title": "昵称%s分钟内只能修改一次", //nick_name_enable为0时返回
     * 		    "sub_title": "不可修改的情况下返回：'上次修改:%s', date('Y-m-d H:i:s', $nickname_info['time']))" //nick_name_enable为0时返回
     *        }
     * </pre>
     *
     * @param accountUuid
     * @return
     */
    protected Map<String, String> getSetNicknamePrivByAccount(String accountUuid, Integer appcode) {
        Map<String, String> result = Maps.newHashMap();
        result.put("nick_name_enable", "1");

        //查询用户最后一次修改昵称的时间
        String lastSetNicknameTime = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.NICKNAME_UPD_TIME.setArg(accountUuid));
        if (StringUtils.isBlank(lastSetNicknameTime)) {
            return result;
        }

        long intervalSeconds = 7 * 86400L;//社区后台配置功能已下线，不取配置，直接写死7天

        if (intervalSeconds > 0) {
            result.put("title", "昵称" + DateUtil.changeSecondsToOneWords(intervalSeconds) + "内只能修改一次");
        }

        long lastUpdateTime = Long.valueOf(lastSetNicknameTime);
        //上次修改时间加昵称修改频率小于等于当前时间，则可以修改
        if (lastUpdateTime + intervalSeconds <= DateUtil.currentTimeSeconds()) {
            return result;
        }

        result.put("nick_name_enable", "0");
        result.put("sub_title", "上次修改: " + DateUtil.dateToString20(DateUtil.fromSecond(lastUpdateTime)));
        return result;
    }

    /**
     * 配合商城迁移游客数据用 2017-04-25
     *
     * @param token
     * @param appcode
     * @return
     */
    @Deprecated
    public Map<String, String> getInfoByTokenAndAppCode(String token, Integer appcode) {
        Map<String, String> result = new HashMap<>();
        Members members = membersDao.findOneByToken(token);
        if (members == null || members.getId() == null) {
            return result;
        }
        AccountsGuest accountsGuest = accountsGuestDao.getByMemberId(members.getId());
        String accountUuid = accountsGuest == null ? null : accountsGuest.getUuid();
        if (StringUtils.isBlank(accountUuid)) {
            return result;
        }
        result.put("account_uuid", accountUuid.trim());
        return result;
    }

    /**
     * 判断用户的手机号是否已验证
     *
     * @param accountUuid
     * @return
     */
    public boolean isAccountMobileChecked(String accountUuid) {
        // 此处只查询 is_check_mobile
        Map<String, Object> accountInfos = this.getInfoByUuid(new String[]{accountUuid}, new String[]{UuidInfoField.IS_CHECK_MOBILE}, "1", false, false).get(accountUuid);
        String isMobileCheck = MapUtils.getString(accountInfos, UuidInfoField.IS_CHECK_MOBILE);
        return Objects.equals("1", isMobileCheck);
    }

    /**
     * 判断是否为游客accountUuid，是则抛出ticket_expier异常
     *
     * @param accountUuid
     */
    void guestTicketExpire(String accountUuid) {
        Map<String, Object> accountInfos = this.getInfoByUuid(new String[]{accountUuid}, new String[]{"account_type"}, "1", true, true).get(accountUuid);
        String accountType = MapUtils.getString(accountInfos, "account_type", "0");
        if ("2".equals(accountType)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE_GUEST2);
        }
    }

    public Map<String, Object> getAccountInfoByUserV2(String accountUuid) {
        Map<String, Object> result = this.getAccountInfoByUserNew(accountUuid, accountUuid, 2);
        // 2021.11.30 新增
        setPicSrcHost(result);
        // 2022.02.17 版本控制过滤下发相册数
        accountsPhotoService.filterPhotoNum(result);
        // 2024.12.24 自我介绍旧版本兼容
        AccountsIntroductionService.adjustIntroduction(result);
        return result;
    }

    public GetAccountInfoByUserV3Vo getAccountInfoByUserV3(String accountUuid) {
        GetAccountInfoByUserV3Vo vo = new GetAccountInfoByUserV3Vo();
        int version = 2;

        String[] uuids =  new String[]{accountUuid};

        //用户基本信息
        Map<String, Object> userResultMap = this.getInfoByUuid(uuids, new String[]{
                UuidInfoField.ACCOUNT_STATUS, UuidInfoField.ZHIMA_CERTIFICATION, UuidInfoField.FACE_CERTIFICATION, UuidInfoField.PROFILE_VERIFY_STATUS,
                UuidInfoField.PROFILE_ERROR_MSG, UuidInfoField.AVATAR_ORIGIN, UuidInfoField.ACCOUNT_NAME_ORIGIN, UuidInfoField.ACCOUNT_NAME, UuidInfoField.ACCOUNT_NAME_STATUS,
                UuidInfoField.SEX_TYPE, UuidInfoField.PERSONAL_PROFILE, UuidInfoField.MOBILE, UuidInfoField.AFFECTIVESTATUS, UuidInfoField.BASEADDR,
                UuidInfoField.DATING_INTENTION, UuidInfoField.HOMETOWN, UuidInfoField.ACCOUNT_ACTOR, UuidInfoField.BIRTH, UuidInfoField.AVATAR_DRESS_ID,
                UuidInfoField.DEFAULT_CARD_ID, UuidInfoField.AVATAR_STATUS, UuidInfoField.EDUCATION, UuidInfoField.PROFESSION, UuidInfoField.HEIGHT,
                UuidInfoField.WEIGHT, UuidInfoField.TRADE, UuidInfoField.PERSONAL_PROFILE_STATUS, UuidInfoField.VOICE_SIGN, UuidInfoField.VOICE_SIGN_AUDIT_TIME,
                UuidInfoField.VOICE_SIGN_URL, UuidInfoField.VOICE_SIGN_DURATION, UuidInfoField.VOICE_SIGN_AUDIT_REASON, UuidInfoField.PASS_VOICE_SIGN,
                UuidInfoField.PASS_VOICE_SIGN_AUDIT_TIME, UuidInfoField.PASS_VOICE_SIGN_URL, UuidInfoField.PASS_VOICE_SIGN_DURATION, UuidInfoField.PASS_VOICE_SIGN_AUDIT_REASON,
                UuidInfoField.LONGITUDE, UuidInfoField.LATITUDE, UuidInfoField.ENABLE_LOCATION, UuidInfoField.PRE_PROFILE_VERIFY_STATUS, UuidInfoField.PRE_PROFILE_ERROR_MSG,
                UuidInfoField.INCOME, UuidInfoField.MY_INTRODUCTION_STATUS, UuidInfoField.MY_INTRODUCTION_CONTENT, UuidInfoField.MY_INTRODUCTION_IMGS,
                UuidInfoField.REAL_AVATAR_CERTIFICATION, UuidInfoField.REGISTER_AVATAR_STATUS, UuidInfoField.IP_CITY
        }, String.valueOf(version), true, true).get(accountUuid);
        // ip位置
        vo.setC_city(MapUtils.getString(userResultMap, UuidInfoField.IP_CITY, ""));

        // 用户状态
        String accountStatus = MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_STATUS, "");
        vo.setAccount_status(accountStatus);
        // 他趣id
        String defaultCardId = MapUtils.getString(userResultMap, UuidInfoField.DEFAULT_CARD_ID);
        vo.setDefault_card_id(defaultCardId);
        // 头像
        String avatar = MapUtils.getString(userResultMap, UuidInfoField.AVATAR_ORIGIN, "");
        int avatarStatus = MapUtils.getIntValue(userResultMap, UuidInfoField.AVATAR_STATUS);
        vo.setAvatar(avatar);
        vo.setAvatar_status(String.valueOf(avatarStatus));

        String realAvatarCertification = MapUtils.getString(userResultMap, UuidInfoField.REAL_AVATAR_CERTIFICATION, "");
        vo.setReal_avatar_certification(realAvatarCertification);

        // 语音签名
        Map<String, Object> voiceSignMap = this.getVoiceSignInfo(userResultMap, accountUuid, accountUuid);
        vo.setVoice_sign_info(voiceSignMap);

        // 基础资料
        // 和旧版本处理一致，旧数据中存在 account_name_origin 无值或数据不一致的情况。所以只有审核中时才取该值。
        String nickname = MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_NAME, "");
        int nicknameStatus = MapUtils.getIntValue(userResultMap, UuidInfoField.ACCOUNT_NAME_STATUS, 0);
        if(nicknameStatus == 0) {
            nickname = MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_NAME_ORIGIN, "");
        }
        vo.setNickname(nickname);

        int sexType = MapUtils.getIntValue(userResultMap, UuidInfoField.SEX_TYPE, 0);
        vo.setSex_type(String.valueOf(sexType));
        long birth = MapUtils.getLongValue(userResultMap, UuidInfoField.BIRTH, 0L);
        vo.setBirth(String.valueOf(birth));
        vo.setAge(birth == 0 ? "" : String.valueOf(ToolsService.calAgeFromBirth(birth)));
        vo.setConstellation(birth == 0 ? "" : DateUtil.getConstellationFromSeconds(birth));

        String height = MapUtils.getString(userResultMap, UuidInfoField.HEIGHT, "0");
        String weight = MapUtils.getString(userResultMap, UuidInfoField.WEIGHT, "0");
        vo.setHeight(height);
        vo.setWeight(weight);

        String baseaddr = MapUtils.getString(userResultMap, UuidInfoField.BASEADDR, "");
        // 用户如果没有开启定位则不显示用户位置
        Integer enableLocation = MapUtils.getInteger(userResultMap, UuidInfoField.ENABLE_LOCATION);
        if (!Objects.equals(CommonEnableStatus.ENABLE.getStatus(), enableLocation)) {
            baseaddr = "";
        }
        vo.setBaseaddr(baseaddr);
        String hometown = MapUtils.getString(userResultMap, UuidInfoField.HOMETOWN, "");
        vo.setHometown(hometown);

        int affectivestatus = MapUtils.getIntValue(userResultMap, UuidInfoField.AFFECTIVESTATUS, 0);
        String trade = MapUtils.getString(userResultMap, UuidInfoField.TRADE, "");
        String profession = MapUtils.getString(userResultMap, UuidInfoField.PROFESSION, "");
        String education = MapUtils.getString(userResultMap, UuidInfoField.EDUCATION, "");
        Integer income = MapUtils.getInteger(userResultMap, UuidInfoField.INCOME, 0);
        vo.setAffectivestatus(String.valueOf(affectivestatus));
        vo.setTrade(trade);
        vo.setProfession(profession);
        vo.setEducation(education);
        vo.setIncome(String.valueOf(income));

        // 自我介绍
        String passIntroductionStatus = MapUtils.getString(userResultMap, UuidInfoField.MY_INTRODUCTION_STATUS, "-1");
        String passIntroductionContent = MapUtils.getString(userResultMap, UuidInfoField.MY_INTRODUCTION_CONTENT, "");
        String myIntroductionImgsStr = MapUtils.getString(userResultMap, UuidInfoField.MY_INTRODUCTION_IMGS, "");
        List<JSONObject> introductionImgs = new ArrayList<>();
        if(StringUtils.isNotBlank(myIntroductionImgsStr)){
            introductionImgs = JSON.parseObject(myIntroductionImgsStr, List.class);
            for (JSONObject img :introductionImgs) {
                // 使用源站域名 + 私有token
                img.put("img_name", AccountsIntroductionService.getPrivatePic(img.getString("img_name")));
                String imgWidth = img.getString("width");
                String imgHeight = img.getString("height");
                img.put("width", StringUtils.isNumeric(imgWidth) && !imgWidth.equals("0") ? imgWidth : CommConst.DEFAULT_INTRODUCTION_IMG_WIDTH);
                img.put("height", StringUtils.isNumeric(imgHeight) && !imgHeight.equals("0") ? imgHeight : CommConst.DEFAULT_INTRODUCTION_IMG_HEIGHT);
            }
        }

        vo.setPass_introduction_status(String.valueOf(passIntroductionStatus));
        vo.setPass_introduction_content(passIntroductionContent);
        vo.setPass_introduction_imgs(introductionImgs);

        // 认证
        String mobile = MapUtils.getString(userResultMap, UuidInfoField.MOBILE, null);
        Integer bindMobile = MobileService.isBindMobile(mobile);
        vo.setIs_bind_mobile(String.valueOf(bindMobile));
        String faceCertification = MapUtils.getString(userResultMap, UuidInfoField.FACE_CERTIFICATION, "0");
        String zhimaCertification = MapUtils.getString(userResultMap, UuidInfoField.ZHIMA_CERTIFICATION, "0");
        vo.setFace_certification(faceCertification);
        vo.setZhima_certification(zhimaCertification);

        Integer profileVerifyStatus = MapUtils.getInteger(userResultMap, UuidInfoField.PROFILE_VERIFY_STATUS, 0);
        Integer preProfileVerifyStatus = MapUtils.getInteger(userResultMap, UuidInfoField.PRE_PROFILE_VERIFY_STATUS);
        String profileErrorMsg = MapUtils.getString(userResultMap, UuidInfoField.PROFILE_ERROR_MSG, "");
        String preProfileErrorMsg = MapUtils.getString(userResultMap, UuidInfoField.PRE_PROFILE_ERROR_MSG, "");
        if (avatarStatus == 0) {//只有自己可以查看自己上传的未审核的头像
            if (null != preProfileVerifyStatus) {
                profileVerifyStatus = preProfileVerifyStatus;
                profileErrorMsg = preProfileErrorMsg;
            }
        }
        vo.setProfile_verify_status(String.valueOf(profileVerifyStatus));
        vo.setProfile_error_msg(profileErrorMsg);

        //返回认证信息文案
        if (Objects.equals(bindMobile, CommConst.YES_1)) {
            vo.setIs_bind_mobile_desc("已认证用户真实手机");
        } else {
            vo.setIs_bind_mobile_desc("");
        }

        if ("1".equals(faceCertification) && "1".equals(realAvatarCertification)) {
            vo.setFace_certification_desc("本人真实照片，已通过人脸比对");
        } else {
            vo.setFace_certification_desc("");
        }

        if ("1".equals(zhimaCertification)) {
            String identityNo = getIdentityNo(accountUuid);
            String identityNoDesc = "";
            if (!StringUtils.isBlank(identityNo)) {
                identityNoDesc = identityNoDesc + identityNo;
            }
            vo.setIdentity_no_desc(identityNoDesc);
        } else {
            vo.setIdentity_no_desc("");
        }

        // 我的理想型
        AccountIdealTargetVo idealTarget = idealTargetService.info(accountUuid, false);
        vo.setIdeal_target(idealTarget.getIdealTarget());
        vo.setIdeal_target_status(idealTarget.getIdealTargetStatus());

        // 我的学校
        AccountSchoolDto schoolInfo = schoolService.info(accountUuid, false);
        vo.setSchool_name(schoolInfo.getSchoolName());
        vo.setMajor(schoolInfo.getMajor());
        vo.setSchool_id(schoolInfo.getSchoolId());
        vo.setMajor_status(schoolInfo.getStatus());

        // 个性签名（个人简介）
        String personalProfile = "";

        TqUserGradeClient.UserPrivilege privilege = getInterestPrivilege(accountUuid, "profile", sexType);
        Integer isPersonalProfileEnable = privilege.getEnable() ? 1 : 0;
        Integer personalProfileScore = privilege.getReach_score();

        personalProfile = MapUtils.getString(userResultMap, UuidInfoField.PERSONAL_PROFILE, "");
        String personalProfileStatus = MapUtils.getString(userResultMap, UuidInfoField.PERSONAL_PROFILE_STATUS);

        vo.setPersonal_profile(personalProfile);
        vo.setPersonal_profile_status(personalProfileStatus);
        vo.setPersonal_profile_permission(String.valueOf(isPersonalProfileEnable));
        vo.setPersonal_profile_interest_score(String.valueOf(personalProfileScore));

        // 我的生活
        boolean isOrigin = true; // 是否主态
        boolean emptyReturn = false; // 为空是否返回
        List<AccountsLife> accountsLifeList = accountsLifeService.getAccountsLifeOrAccountsPhoto(accountUuid, isOrigin, true);
        List<AccountsLifeVo> accountsLifeVoList = accountsLifeService.accountsLifeToAccountsLifeVo(accountUuid, accountsLifeList, isOrigin, emptyReturn);
        vo.setAccounts_life_list(accountsLifeVoList);

        // 其他
        if(Objects.equals(sexType, 1)){
            //获取用户初始注册头像审核状态
            int registerAvatarStatus = MapUtils.getIntValue(userResultMap, UuidInfoField.REGISTER_AVATAR_STATUS);
            vo.setRegister_avatar_status(String.valueOf(registerAvatarStatus));
        }else {
            vo.setRegister_avatar_status("");
        }
        vo.setDefault_avatar_status(AvatarHandleService.isDefAvatar(avatar) ? "1" : "0");

        List<String> obtainHighAchievementList = accountsAchievementService.getObtainHighAchievementList(accountUuid, version > 1 ? version : 0);
        vo.setObtain_achievement(obtainHighAchievementList);

        String accountActor = MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_ACTOR, "");
        vo.setAccount_actor(accountActor);

        if(StringUtils.isNotBlank(accountActor) && Integer.parseInt(accountActor) > 0 ){
            Map<String, String> medalInfo = accountsMedalService.getInfo(Long.valueOf(accountActor), "medal_name");
            vo.setMedal_name(MapUtils.getString(medalInfo, "medal_name", ""));
        }else{
            vo.setMedal_name("");
        }

        String dressId = MapUtils.getString(userResultMap, UuidInfoField.AVATAR_DRESS_ID, "");
        vo.setAvatar_dress_id(dressId);

     // location
        String location = "";
        LocationInfoVO locationInfoVO = accountsLocationInfoService.getAccountLocationInfo(accountUuid);
//        logger.info("locationMap: {}", JSON.toJSONString(locationMap));
        if (locationInfoVO != null ) {
            try {
                String cityIdStr = locationInfoVO.getCityId();
                if(StringUtils.isNotBlank(cityIdStr)){
                    Long cityId = Long.valueOf(cityIdStr);
                    Map<Long, Map<String, Object>> map = regionService.mgetInfoByCityId(Collections.singletonList(cityId));
                    if (map.containsKey(cityId)) {
                        Map<String, Object> regionMap = map.get(cityId);
//                    logger.info("regionMap: {}", JSON.toJSONString(regionMap));
//                    location = MapUtils.getString(regionMap, "province_name", "").replace("省", "");
                        location = MapUtils.getString(regionMap, "city_name", "").replace("市", "");
                    }
                }
            } catch (Exception e) {
                logger.error("个人主页获取用户所在城市异常", e);
            }
        }
        location = location.replace("市", "").replace("特别行政区", "");
        if (location.contains("海外") || location.contains("其他")) {
            location = "";
        }
        vo.setLocation(location);

        // 背景
        setBackground(accountUuid, vo);
        // 吸引力
        setAllure(accountUuid, vo);
        // 标签列表
        List<AccountLabelCfg> labels = accountLabelServiceV2.listMyLabelInfo(accountUuid);
        vo.setLabels(labels.stream().map(old -> {
            HomepageLabelResp item = new HomepageLabelResp();
            item.setLabel_name(old.getContent());
            return item;
        }).collect(Collectors.toList()));

        // 此处资料完整度用到的需要时客态
        isOrigin = false; // 是否主态
        accountsLifeList = accountsLifeService.getAccountsLifeOrAccountsPhoto(accountUuid, isOrigin, true);

        // 资料完整度
        double infoPercent = this.calInfoPercentV2(accountUuid, avatar, nickname, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile
            , MapUtils.getString(userResultMap, "pass_voice_sign_url"), education, profession, height, weight, null
            , null, null, null, income, schoolInfo, labels, accountsLifeList);

        vo.setInfo_percent(String.valueOf((int)infoPercent ));

        boolean realAge = accountBaseInfoManager.isRealAge(accountUuid);
        vo.setAge_doubt(realAge ? "0" : "1");
        vo.setAge_doubt_msg("1".equals(vo.getAge_doubt()) ? "对方个人信息为用户自己填写，请注意甄别" : StringUtils.EMPTY);
        return vo;
    }

    private void setAllure(String accountUuid, GetAccountInfoByUserV3Vo vo) {
        AllureResp allure = AlEvaluationVersionBarrier.pass() ? allureService.getAllureV2(accountUuid) : allureService.getAllure(accountUuid);
        if (allure != null) {
            AllureHomepageResp allureResp = new AllureHomepageResp();
            allureResp.setPoint(allure.getAllurePoint());
            allureResp.setRatio(allure.getAllureRatio());
            allureResp.setContent(allure.getContent());
            vo.setAllure(allureResp);
        }
    }

    private void setBackground(String accountUuid, GetAccountInfoByUserV3Vo vo) {
        HomepageBackgroundResp background = homepageBackgroundService.getBackground(accountUuid);
        if (background != null) {
            vo.setBackground_id(background.getBackgroundId());
            vo.setBackground_url(background.getBackgroundUrl());
        }
    }

    public JsonResult getFriendCheckInfo(String ticket) {
        String checksSwitch = friendChecksMap.get("checksSwitch");
        // 开关开启
        if ("1".equals(checksSwitch)) {
            Map<String, Object> result = Maps.newHashMap();
            String[] checks = friendChecksMap.get("checks").split(",");
            result = this.getCheckInfo(ticket, checks, "friend");
            if (result != null) {
                result.put("title", friendChecksMap.get("title"));
                result.put("describe", friendChecksMap.get("describe"));
            }
            return JsonResult.success(result);
        } else {
            return JsonResult.success(null);
        }
    }

    /**
     * 根据上传的个人资料信息名，返回不存在的信息名（交友标签不存在则下发6+6）
     *
     * @param ticket ticket
     * @param checks 信息名
     * @param type   根据"friend"区分交友和引导调用
     * @return {@link Map<String, Object>}
     */
    public Map<String, Object> getCheckInfo(String ticket, String[] checks, String type) {
        String accountUuid = this.getByTicket(ticket).getAccount_uuid();
        Map<String, Object> infoMap = this.getAccountInfoByUserV2(accountUuid);
        Set<String> checkSet = new LinkedHashSet<>();
        Map<String, Object> resultMap = new HashMap<>();

        for (String check : checks) {
            // 是否存在，ture存在，false不存在
            boolean flag = false;
            // 语音签名
            if ("voiceSignature".equals(check)) {
                Map<String, Object> voiceSignInfoMap = (Map<String, Object>) infoMap.get("voice_sign_info");
                flag = "0".equals(voiceSignInfoMap.get("voice_sign")) || "1".equals(voiceSignInfoMap.get("voice_sign"));
                // 签名（个人简介）
            } else if ("profile".equals(check)) {
                flag = StringUtils.isNotBlank(infoMap.get("personal_profile").toString());
                // 出生日期
            } else if ("birthday".equals(check)) {
                flag = !"0".equals(infoMap.get(UuidInfoField.BIRTH));
                // 身高
            } else if ("height".equals(check)) {
                flag = !"0".equals(infoMap.get("height"));
                // 体重
            } else if ("weight".equals(check)) {
                flag = !"0".equals(infoMap.get("weight"));
                // 故乡（","当做未填写，兼容测试环境脏数据）
            } else if ("hometown".equals(check)) {
                flag = StringUtils.isNotBlank(infoMap.get("hometown").toString()) && !",".equals(infoMap.get("hometown"));
                // 情感状况
            } else if ("maritalStatus".equals(check)) {
                flag = !"0".equals(infoMap.get("affectivestatus"));
                // 职业
            } else if ("profession".equals(check)) {
                flag = StringUtils.isNotBlank(infoMap.get("profession").toString());
                // 学历
            } else if ("education".equals(check)) {
                flag = StringUtils.isNotBlank(infoMap.get("education").toString());
                // 年收入
            } else if ("income".equals(check)) {
                flag = !"0".equals(infoMap.get("income"));
                // 标签
            } else if ("personalityLabel".equals(check)) {
                Map<String, List<Map<String, String>>> labels = accountsLabelService.getAccountLabelForNew(accountUuid, 2);
                flag = labels.get("account_label").size() > 0;
                // 交友下发6自我描述+6兴趣爱好标签
                if (!flag && "friend".equals(type)) {
                    List<Map<String, String>> descriptionLabelList = labels.get("description_label");
                    List<Map<String, String>> hobbyLabelList = labels.get("hobby_label");
                    resultMap.put("description_label", descriptionLabelList.subList(0, Math.min(descriptionLabelList.size(), 6)));
                    resultMap.put("hobby_label", hobbyLabelList.subList(0, Math.min(hobbyLabelList.size(), 6)));
                }
            }
            if (!flag) {
                // 不存在则加入列表
                checkSet.add(check);
            }
            // 交友birth字段在checkList中必须下发，如果存在则在外面给出具体数值
            if ("friend".equals(type)) {
                checkSet.add("birthday");
                if (!"0".equals(infoMap.get(UuidInfoField.BIRTH))) {
                    resultMap.put("birthday", infoMap.get(UuidInfoField.BIRTH));
                }
            }
        }
        resultMap.put("checkList", checkSet);
        // 交友，字段都填写则下发空
        if ("friend".equals(type) && checkSet.size() == 1 && !"0".equals(infoMap.get(UuidInfoField.BIRTH))) {
            resultMap = null;
        }
        return resultMap;
    }

    /**
     * 获取相册
     *
     * @param accountUuid 操作人
     * @param toAccountUuid 被看人（为空看自己）
     * @return
     */
    public List<Map<String, String>> getAccountCover(String accountUuid, String toAccountUuid) {
//        String accountUuid = this.getByTicket(ticketId).getAccount_uuid();
        List<Map<String, String>> result = null;
        if(StringUtils.isBlank(toAccountUuid)) {
            // 看自己
            result = accountsPhotoService.getAccountCover(accountUuid, false);
        }else {
            // 看别人
            accountUuid = toAccountUuid;
            result = accountsPhotoService.getAccountCover(accountUuid, true);
        }

        for (Map<String, String> item : result) {
            String picUrl = MapUtils.getString(item, "photo_url" , "");
            if(StringUtils.isNotBlank(picUrl)) {
                picUrl = AvatarHandleService.getAvatarOfSavePhoto(picUrl);
                picUrl = CommConst.AVATAR_SRC_HOST + picUrl;
            }
            item.put("pic_url", picUrl);
            String likeNum = item.get("like_num");
            if(StringUtils.isBlank(likeNum)){
                item.put("like_num", "0");
            }
            item.put("is_thumbs_up", accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_PHOTO_THUMBS_UP.setArg(Md5Util.encryptSHA1(item.get("id") + item.get("pic_url") + accountUuid))) ? "1": "0");
            item.remove("photo_url");
        }

        // 2022.02.17 版本控制过滤下发相册数
        result = accountsPhotoService.filterPhotoNum(result);

        return result;
    }

    /**
     * 获取主页信息
     *
     * @param operUuid
     * @param accountUuid
     * @param version     个人简介显示权限判断规则 为1时判断驾照权限，否则判断等级、头衔权限
     * @return
     */
    public Map<String, Object> getHomepageInfo(String operUuid, String accountUuid, Integer version, Integer isReturnAvatar) {

        operUuid = StringUtils.defaultString(operUuid, "");

        //已注销用户提示，个人主页返回不存在
        Map<String, Object> result = this.mgetHomepageInfo(operUuid, new String[]{accountUuid}, version, true, 0).get(accountUuid);
        if (MapUtils.getIntValue(result, "account_status", 1) == 0) {
            if (Objects.equals(isReturnAvatar, 1)) {
                // 新版异常需要返回头像
                Integer gender = MapUtils.getInteger(result, UuidInfoField.SEX_TYPE, 1);
                String avatar = AccountsService.getAvatarBySexType(gender);
                Map<String, String> returnMap = Maps.newHashMap();
                returnMap.put("tips", "该用户已注销，无法查看");
                returnMap.put("avatar", avatar);
                throw new ServiceException("account_destroy", JsonUtils.objectToString(returnMap));
            }
            throw new ServiceException(CodeStatus.ACCOUNT_DESTROY);
        }

        // 2021.11.30 新增
        if(Objects.equals(operUuid, accountUuid)) {
            // 图片源站在这处理
            setPicSrcHost(result);
        }

        Object img_list = result.get("img_list");
        if(img_list != null){
            // 新版需要补一下头像到imgList
            String avatar = MapUtils.getString(result, "avatar", "");
            JSONArray imgList = JSON.parseArray(JSON.toJSONString(img_list));
            AccountsPhoto accountAvatar = accountsPhotoDao.getAccountAvatar(accountUuid, null, false);
            JSONArray newImgList;
            if(accountAvatar != null && accountAvatar.getPhoto_url().equals(avatar)){
                newImgList = new JSONArray();
                JSONObject p = new JSONObject();
                p.put("id", accountAvatar.getId().toString());
                p.put("pic_url", accountAvatar.getPhoto_url());

                p.put("verify_status", "1");
                p.put("like_num", accountAvatar.getLike_num());
                p.put("is_thumbs_up",accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_PHOTO_THUMBS_UP.setArg(Md5Util.encryptSHA1(accountAvatar.getId() + accountAvatar.getPhoto_url() + operUuid))) ? "1": "0");
                newImgList.add(p);
                newImgList.addAll(imgList);
                result.put("img_list", newImgList);
            }else{
                newImgList = new JSONArray();
                JSONObject p = new JSONObject();
                p.put("id", "-1");
                p.put("pic_url", avatar);

                p.put("verify_status", "1");
                p.put("like_num", "1");
                p.put("is_thumbs_up", "0");
                newImgList.add(p);
                newImgList.addAll(imgList);
                result.put("img_list", newImgList);
            }
            JSONArray imgListArr = fillImgListCanThumpsUp(newImgList);
            result.put("img_list", imgListArr);
            result.put("total_like_num", getTotalLike(imgListArr));
        }

        String location = "";
        LocationInfoVO locationInfoVO = accountsLocationInfoService.getAccountLocationInfo(accountUuid);
//        logger.info("locationMap: {}", JSON.toJSONString(locationMap));
        if (locationInfoVO != null ) {
            try {
                String cityIdStr = locationInfoVO.getCityId();
                if(StringUtils.isNotBlank(cityIdStr)){
                    Long cityId = Long.valueOf(cityIdStr);
                    Map<Long, Map<String, Object>> map = regionService.mgetInfoByCityId(Collections.singletonList(cityId));
                    if (map.containsKey(cityId)) {
                        Map<String, Object> regionMap = map.get(cityId);
//                    logger.info("regionMap: {}", JSON.toJSONString(regionMap));
//                    location = MapUtils.getString(regionMap, "province_name", "").replace("省", "");
                        location = MapUtils.getString(regionMap, "city_name", "").replace("市", "");
                    }
                }
            } catch (Exception e) {
                logger.error("个人主页获取用户所在城市异常", e);
            }
        }
        location=location.replace("市", "").replace("特别行政区","");
        if(location.contains("海外") || location.contains("其他")){
            location="";
        }
        result.put("location", location);
        boolean realAge = accountBaseInfoManager.isRealAge(accountUuid);
        result.put("age_doubt", realAge ? "0" : "1");
        result.put("age_doubt_msg", realAge ? StringUtils.EMPTY : "对方个人信息为用户自己填写，请注意甄别");
        // 2022.02.17 版本控制过滤下发相册数
        accountsPhotoService.filterPhotoNum(result);
        // 2024.12.24 自我介绍旧版本兼容
        AccountsIntroductionService.adjustIntroduction(result);
        return result;
    }


    /**
     * 获取主页信息（客态）
     *
     * @param operUuid
     * @param accountUuid
     * @return
     */
    public GetHomepageInfoV3Vo getHomepageInfoV3(String operUuid, String accountUuid, Integer isReturnAvatar) {
        GetHomepageInfoV3Vo vo = new GetHomepageInfoV3Vo();

        Integer version = 2;

        String[] uuids = new String[]{accountUuid};

        //1. 用户基本信息
        Map<String, Object> userResultMap = this.getInfoByUuid(uuids, new String[]{
                UuidInfoField.ACCOUNT_STATUS, UuidInfoField.ZHIMA_CERTIFICATION, UuidInfoField.FACE_CERTIFICATION, UuidInfoField.PROFILE_VERIFY_STATUS,
                UuidInfoField.PROFILE_ERROR_MSG, UuidInfoField.AVATAR, UuidInfoField.ACCOUNT_NAME, UuidInfoField.MOBILE, UuidInfoField.CREATE_TIME,
                UuidInfoField.SEX_TYPE, UuidInfoField.PERSONAL_PROFILE, UuidInfoField.AFFECTIVESTATUS,
                UuidInfoField.BASEADDR, UuidInfoField.HOMETOWN, UuidInfoField.ACCOUNT_ACTOR, UuidInfoField.BIRTH, UuidInfoField.AVATAR_DRESS_ID,
                UuidInfoField.DEFAULT_CARD_ID, UuidInfoField.EDUCATION, UuidInfoField.PROFESSION, UuidInfoField.HEIGHT,
                UuidInfoField.WEIGHT, UuidInfoField.TRADE, UuidInfoField.VOICE_SIGN, UuidInfoField.VOICE_SIGN_AUDIT_TIME,
                UuidInfoField.VOICE_SIGN_URL, UuidInfoField.VOICE_SIGN_DURATION, UuidInfoField.VOICE_SIGN_AUDIT_REASON, UuidInfoField.PASS_VOICE_SIGN,
                UuidInfoField.PASS_VOICE_SIGN_AUDIT_TIME, UuidInfoField.PASS_VOICE_SIGN_URL, UuidInfoField.PASS_VOICE_SIGN_DURATION, UuidInfoField.PASS_VOICE_SIGN_AUDIT_REASON,
                UuidInfoField.PASS_PERSONAL_PROFILE, UuidInfoField.PASS_PERSONAL_PROFILE_STATUS, UuidInfoField.LONGITUDE, UuidInfoField.LATITUDE, UuidInfoField.ENABLE_LOCATION,
                UuidInfoField.PLATFORM_ID, UuidInfoField.APP_VERSION, UuidInfoField.PRE_PROFILE_VERIFY_STATUS, UuidInfoField.PRE_PROFILE_ERROR_MSG, UuidInfoField.INCOME,
                UuidInfoField.VOICE_SIGN, UuidInfoField.MY_INTRODUCTION_STATUS, UuidInfoField.MY_INTRODUCTION_CONTENT, UuidInfoField.MY_INTRODUCTION_IMGS,
                UuidInfoField.PASS_INTRODUCTION_STATUS, UuidInfoField.PASS_INTRODUCTION_CONTENT, UuidInfoField.PASS_INTRODUCTION_IMGS, UuidInfoField.REAL_AVATAR_CERTIFICATION,
                UuidInfoField.IP_CITY
        }, String.valueOf(version), true, true).get(accountUuid);

        // 用户状态
        String accountStatus = MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_STATUS, "");
        if (Objects.equals(accountStatus, CommConst.NO_0.toString())) {
            if (Objects.equals(isReturnAvatar, 1)) {
                // 新版异常需要返回头像
                Integer gender = MapUtils.getInteger(userResultMap, UuidInfoField.SEX_TYPE, 1);
                String avatar = AccountsService.getAvatarBySexType(gender);
                Map<String, String> returnMap = Maps.newHashMap();
                returnMap.put("tips", "该用户已注销，无法查看");
                returnMap.put("avatar", avatar);
                throw new ServiceException("account_destroy", JsonUtils.objectToString(returnMap));
            }
            // 注销用户不让查看资料
            throw new ServiceException(CodeStatus.ACCOUNT_DESTROY);
        }
        vo.setAccount_status(accountStatus);
        // ip定位
        vo.setC_city(MapUtils.getString(userResultMap, UuidInfoField.IP_CITY, ""));

        // 他趣id
        String defaultCardId = MapUtils.getString(userResultMap, UuidInfoField.DEFAULT_CARD_ID);
        vo.setDefault_card_id(defaultCardId);
        // 头像
        String avatar = MapUtils.getString(userResultMap, UuidInfoField.AVATAR, "");
        vo.setAvatar(avatar);

        String realAvatarCertification = MapUtils.getString(userResultMap, UuidInfoField.REAL_AVATAR_CERTIFICATION, "");
        vo.setReal_avatar_certification(realAvatarCertification);

        // 语音签名
        Map<String, Object> voiceSignMap = this.getVoiceSignInfo(userResultMap, operUuid, accountUuid);
        vo.setVoice_sign_info(voiceSignMap);

        // 基础资料
        String nickname = MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_NAME, "");
        vo.setNickname(nickname);
        int sexType = MapUtils.getIntValue(userResultMap, UuidInfoField.SEX_TYPE, 0);
        vo.setSex_type(String.valueOf(sexType));
        long birth = MapUtils.getLongValue(userResultMap, UuidInfoField.BIRTH, 0L);
        vo.setBirth(String.valueOf(birth));
        vo.setAge(birth == 0 ? "" : String.valueOf(ToolsService.calAgeFromBirth(birth)));
        vo.setConstellation(birth == 0 ? "" : DateUtil.getConstellationFromSeconds(birth));

        String height = MapUtils.getString(userResultMap, UuidInfoField.HEIGHT, "0");
        String weight = MapUtils.getString(userResultMap, UuidInfoField.WEIGHT, "0");
        vo.setHeight(height);
        vo.setWeight(weight);

        String baseaddr = MapUtils.getString(userResultMap, UuidInfoField.BASEADDR, "");
        // 用户如果没有开启定位则不显示用户位置
        Integer enableLocation = MapUtils.getInteger(userResultMap, UuidInfoField.ENABLE_LOCATION);
        if (!Objects.equals(CommonEnableStatus.ENABLE.getStatus(), enableLocation)) {
            baseaddr = "";
        }
        vo.setBaseaddr(baseaddr);
        String hometown = MapUtils.getString(userResultMap, UuidInfoField.HOMETOWN, "");
        vo.setHometown(hometown);

        int affectivestatus = MapUtils.getIntValue(userResultMap, UuidInfoField.AFFECTIVESTATUS, 0);
        String trade = MapUtils.getString(userResultMap, UuidInfoField.TRADE, "");
        String profession = MapUtils.getString(userResultMap, UuidInfoField.PROFESSION, "");
        String education = MapUtils.getString(userResultMap, UuidInfoField.EDUCATION, "");
        Integer income = MapUtils.getInteger(userResultMap, UuidInfoField.INCOME, 0);
        vo.setAffectivestatus(String.valueOf(affectivestatus));
        vo.setTrade(trade);
        vo.setProfession(profession);
        vo.setEducation(education);
        vo.setIncome(String.valueOf(income));

        // 自我介绍
        String passIntroductionStatus = MapUtils.getString(userResultMap, UuidInfoField.PASS_INTRODUCTION_STATUS, "-1");
        String passIntroductionContent = MapUtils.getString(userResultMap, UuidInfoField.PASS_INTRODUCTION_CONTENT, "");
        String myIntroductionImgsStr = MapUtils.getString(userResultMap, UuidInfoField.PASS_INTRODUCTION_IMGS, "");
        List<JSONObject> introductionImgs = new ArrayList<>();
        if(StringUtils.isNotBlank(myIntroductionImgsStr)){
            introductionImgs = JSON.parseObject(myIntroductionImgsStr, List.class);
            for (JSONObject img :introductionImgs) {
                img.put("img_name", ToolsService.addPhotoUrlPreDomain(AvatarHandleService.getAvatarOfSavePhoto(img.getString("img_name"))));
                String imgWidth = img.getString("width");
                String imgHeight = img.getString("height");
                img.put("width", StringUtils.isNumeric(imgWidth) && !imgWidth.equals("0") ? imgWidth : CommConst.DEFAULT_INTRODUCTION_IMG_WIDTH);
                img.put("height", StringUtils.isNumeric(imgHeight) && !imgHeight.equals("0") ? imgHeight : CommConst.DEFAULT_INTRODUCTION_IMG_HEIGHT);
            }
        }
        vo.setPass_introduction_status(String.valueOf(passIntroductionStatus));
        vo.setPass_introduction_content(passIntroductionContent);
        vo.setPass_introduction_imgs(introductionImgs);

        // 认证
        String mobile = MapUtils.getString(userResultMap, UuidInfoField.MOBILE, null);
        Integer bindMobile = MobileService.isBindMobile(mobile);
        vo.setIs_bind_mobile(String.valueOf(bindMobile));
        String faceCertification = MapUtils.getString(userResultMap, UuidInfoField.FACE_CERTIFICATION, "0");
        String zhimaCertification = MapUtils.getString(userResultMap, UuidInfoField.ZHIMA_CERTIFICATION, "0");
        vo.setFace_certification(faceCertification);
        vo.setZhima_certification(zhimaCertification);
        Integer profileVerifyStatus = MapUtils.getInteger(userResultMap, UuidInfoField.PROFILE_VERIFY_STATUS, 0);
        vo.setProfile_verify_status(String.valueOf(profileVerifyStatus));

        //返回认证信息文案
        if (Objects.equals(bindMobile, CommConst.YES_1)) {
            vo.setIs_bind_mobile_desc("已认证用户真实手机");
        } else {
            vo.setIs_bind_mobile_desc("");
        }

        if ("1".equals(faceCertification) && "1".equals(realAvatarCertification)) {
            vo.setFace_certification_desc("本人真实照片，已通过人脸比对");
        } else {
            vo.setFace_certification_desc("");
        }

        if ("1".equals(zhimaCertification)) {
            String identityNo = getIdentityNo(accountUuid);
            String identityNoDesc = "";
            if (!StringUtils.isBlank(identityNo)) {
                identityNoDesc = identityNoDesc + identityNo;
            }
            vo.setIdentity_no_desc(identityNoDesc);
        } else {
            vo.setIdentity_no_desc("");
        }

        // 我的理想型
        AccountIdealTargetVo ideal = idealTargetService.info(accountUuid, true);
        vo.setIdeal_target(ideal.getIdealTarget());
        vo.setIdeal_target_status(ideal.getIdealTargetStatus());

        AccountSchoolDto schoolInfo = schoolService.info(accountUuid, true);
        vo.setSchool_name(schoolInfo.getSchoolName());
        vo.setMajor(schoolInfo.getMajor());
        vo.setMajor_status(schoolInfo.getStatus());

        // 个性签名（个人简介）
        String personalProfile = MapUtils.getString(userResultMap, UuidInfoField.PASS_PERSONAL_PROFILE, "");
        vo.setPersonal_profile(personalProfile);

        // 我的生活
        ClonedPlanformAppVersionDto appVersionDto = getAccountClonedPlanformAppVersion(accountUuid);
        List<AccountsLifeVo> accountsLife = accountsLifeService.getAccountsLifeVo(accountUuid, false, false);
        if (AccountsLifeService.isRealPersonVersion(appVersionDto.getPlatformId(), appVersionDto.getAppVersion())) {
            // 客态需要过滤真人比对不通过的前3张照片
            accountsLife.removeIf(v -> AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(v.getVerifyStatus()));
        }
        vo.setAccounts_life_list(accountsLife);

        // 其他
        List<String> obtainHighAchievementList = accountsAchievementService.getObtainHighAchievementList(accountUuid, version > 1 ? version : 0);
        vo.setObtain_achievement(obtainHighAchievementList);

        String accountActor = MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_ACTOR, "");
        vo.setAccount_actor(accountActor);

        if(StringUtils.isNotBlank(accountActor) && Integer.parseInt(accountActor) > 0 ){
            Map<String, String> medalInfo = accountsMedalService.getInfo(Long.valueOf(accountActor), "medal_name");
            vo.setMedal_name(MapUtils.getString(medalInfo, "medal_name", ""));
        }else{
            vo.setMedal_name("");
        }

        String dressId = MapUtils.getString(userResultMap, UuidInfoField.AVATAR_DRESS_ID, "");
        vo.setAvatar_dress_id(dressId);

        // 距离
        if (!Objects.equals(CommonEnableStatus.DISABLE.getStatus(), enableLocation)) {
            Double lon = MapUtils.getDouble(userResultMap, "longitude");
            Double lat = MapUtils.getDouble(userResultMap, "latitude");
            BigDecimal distance = getDistance(operUuid, lon, lat);
            if (null != distance) {
                String disStr = String.valueOf(distance);
                //客户端无法显示0，这边设置为0.01
                if ("0.00".equals(disStr)) {
                    disStr = "0.01";
                }
                vo.setDistance(disStr);
            } else {
                vo.setDistance("");
            }
        } else {
            vo.setDistance("");
        }
        vo.setDistance_unit("km");

        // location
        String location = "";
        LocationInfoVO locationInfoVO = accountsLocationInfoService.getAccountLocationInfo(accountUuid);
//        logger.info("locationMap: {}", JSON.toJSONString(locationMap));
        if (locationInfoVO != null ) {
            try {
                String cityIdStr = locationInfoVO.getCityId();
                if(StringUtils.isNotBlank(cityIdStr)){
                    Long cityId = Long.valueOf(cityIdStr);
                    Map<Long, Map<String, Object>> map = regionService.mgetInfoByCityId(Collections.singletonList(cityId));
                    if (map.containsKey(cityId)) {
                        Map<String, Object> regionMap = map.get(cityId);
//                    logger.info("regionMap: {}", JSON.toJSONString(regionMap));
//                    location = MapUtils.getString(regionMap, "province_name", "").replace("省", "");
                        location = MapUtils.getString(regionMap, "city_name", "").replace("市", "");
                    }
                }
            } catch (Exception e) {
                logger.error("个人主页获取用户所在城市异常", e);
            }
        }
        location = location.replace("市", "").replace("特别行政区", "");
        if (location.contains("海外") || location.contains("其他")) {
            location = "";
        }
        vo.setLocation(location);

        long createTime = MapUtils.getLongValue(userResultMap, UuidInfoField.CREATE_TIME, 0L);
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();

        // 需求，女用户，注册三日内
        if(currentTimeSeconds - createTime < 86400 * 7 && Objects.equals(sexType, 2)) {
            vo.setIs_new_account(CommConst.YES_1);
        }else {
            vo.setIs_new_account(CommConst.NO_0);
        }
        // 背景赋值
        setBackground(accountUuid, vo);
        // 标签数据赋值
        setLabel(operUuid, accountUuid, vo);

        boolean realAge = accountBaseInfoManager.isRealAge(accountUuid);
        vo.setAge_doubt(realAge ? "0" : "1");
        vo.setAge_doubt_msg("1".equals(vo.getAge_doubt()) ? "对方个人信息为用户自己填写，请注意甄别" : StringUtils.EMPTY);
        // 这个放在最后 访问他人主页，记录访问日志
        this.homepageAccessRecord(operUuid, accountUuid, accountStatus);
        return vo;
    }

    private void setLabel(String accountUuid, String targetUuid, GetHomepageInfoV3Vo vo) {
        GetTargetLabelResp targetLabel = accountLabelServiceV2.getTargetLabel(accountUuid, targetUuid);

        vo.setSame_category(targetLabel.getSameCategory());
        if (CollectionUtils.isNotEmpty(targetLabel.getLabels())) {
            vo.setLabels(targetLabel.getLabels().stream().map(old -> {
                HomepageLabelResp item = new HomepageLabelResp();
                item.setLabel_name(old.getLabelName());
                item.setIs_highlight(old.getIsHighlight());
                return item;
            }).collect(Collectors.toList()));
        }
    }

    private void setBackground(String accountUuid, GetHomepageInfoV3Vo vo) {
        HomepageBackgroundResp background = homepageBackgroundService.getBackground(accountUuid);
        if (background != null) {
            vo.setBackground_id(background.getBackgroundId());
            vo.setBackground_url(background.getBackgroundUrl());
        }
    }

    /**
     * 2021.11.30 设置源站域名
     *
     * @param result
     */
    private void setPicSrcHost(Map<String, Object> result) {
        Object imgListObject = MapUtils.getObject(result, "img_list");
        if(imgListObject != null) {
            List<Map<String, String>> list = (List<Map<String, String>>)imgListObject;
            for (Map<String, String> map : list) {
                String picUrl = MapUtils.getString(map, "pic_url" , "");
                if(StringUtils.isNotBlank(picUrl)) {
                    picUrl = AvatarHandleService.getAvatarOfSavePhoto(picUrl);
                    map.put("pic_url", CommConst.AVATAR_SRC_HOST + picUrl);
                }
            }
        }
        String avatar = MapUtils.getString(result, "avatar", "");
        if(StringUtils.isNotBlank(avatar)){
            avatar = AvatarHandleService.getAvatarOfSavePhoto(avatar);
            result.put("avatar", CommConst.AVATAR_SRC_HOST + avatar);
        }
    }

    /**
     * 总点赞数
     * @param imgListArr
     * @return
     */
    private String getTotalLike(JSONArray imgListArr) {
        Integer totalLike = 0;
        for (int i = 0; i < imgListArr.size(); i++) {
            JSONObject img = imgListArr.getJSONObject(i);
            Integer likeNum = img.getInteger("like_num");
            totalLike += likeNum == null ? 0 : likeNum;
        }
        return totalLike.toString();
    }

    /**
     * 填充图片列表是否可点赞
     * @param imgList
     * @return
     */
    private JSONArray fillImgListCanThumpsUp(JSONArray imgList){
        JSONArray ja = new JSONArray();
        for (int i = 0; i < imgList.size(); i++) {
            JSONObject p = imgList.getJSONObject(i);
            String picUrl = p.getString("pic_url");

            p.put("can_thumbs_up",  AvatarHandleService.isDefAvatar(picUrl) ? "0" : "1");
            ja.add(p);
        }
        return ja;
    }

    /**
     * 批量获取主页信息
     *
     * @param operUuid
     * @param accountUuids
     * @param version
     * @return
     */
    public Map<String, Map<String, Object>> mgetHomepageInfo(String operUuid, String[] accountUuids, Integer version, boolean recordAccess, int labelType) {
        Map<String, Map<String, Object>> resultsMap = this.mgetAccountInfoByUserNew(operUuid, accountUuids, version, labelType);
        for (Map.Entry<String, Map<String, Object>> entry : resultsMap.entrySet()) {
            String accountUuid = entry.getKey();
            Map<String, Object> resultMap = entry.getValue();

            // 已注销用户，提前返回
            String accountStatus = MapUtils.getString(resultMap, "account_status", "");
            if (StringUtils.isNotBlank(accountStatus) && "0".equals(accountStatus)) {
                continue;
            }

            //推荐理由 2024.3.14 确认客户端业务已无使用，先直接返回空字符串
//            resultMap.put("recommend_reason", (StringUtils.isNotBlank(operUuid) && !Objects.equals(operUuid, accountUuid)) ? this.findSimilar(operUuid, accountUuid) : "");
            resultMap.put("recommend_reason", "");
            //性别，1:男; 2:女; null:未知
            if("1".equals(resultMap.get("sex_type"))){
                //获取用户初始注册头像审核状态
                int registerAvatarStatus=getRegisterAvatarStatus(accountUuid);
                resultMap.put("register_avatar_status", String.valueOf(registerAvatarStatus));
            }

            //处理保密字段
            if ("1".equals(resultMap.get("sex_type_is_secret"))) {
                resultMap.put("sex_type", "");
            }
            if ("1".equals(resultMap.get(UuidInfoField.AGE_IS_SECRET))) {
                resultMap.put("age", "");
                resultMap.put("birth", "");
                resultMap.put("constellation", "");
            }
            if ("1".equals(resultMap.get("affectivestatus_is_secret"))) {
                resultMap.put("affectivestatus", "");
            }
            if ("1".equals(resultMap.get("sexual_is_secret"))) {
                resultMap.put("sexual", "");
            }
            if ("1".equals(resultMap.get("baseaddr_is_secret"))) {
                resultMap.put("baseaddr", "");
            }
            if ("1".equals(resultMap.get("hometown_is_secret"))) {
                resultMap.put("hometown", "");
            }

            //个人主页不需要下发个人简介权限
            if (null != resultMap.get("personal_profile_permission")) {
                resultMap.put("personal_profile_permission", null);
            }
            if (null != resultMap.get("personal_profile_interest_score")) {
                resultMap.put("personal_profile_interest_score", null);
            }

            resultMap.remove(UuidInfoField.IS_UPDATE_SEX);

            //评价
            String isEvaluate = StringUtils.isBlank(operUuid) ? "0" : accountsEvaluateService.getAccountEvaluate(operUuid, accountUuid).get("is_evaluate");//是否评价过
            Map<String, Object> evaluateLabel = accountsEvaluateService.getAccountEvaluatedCount(accountUuid);
            evaluateLabel.put("is_evaluate", isEvaluate);
            resultMap.put("evaluate_label", evaluateLabel);

            String avatar = (String) resultMap.getOrDefault("avatar", "");
            resultMap.put("default_avatar_status", AvatarHandleService.isDefAvatar(avatar) ? "1" : "0");

            if (recordAccess) {
                this.homepageAccessRecord(operUuid, accountUuid, accountStatus);
            }
        }

        return resultsMap;
    }

    private int getRegisterAvatarStatus(String accountUuid){
        Map<String, Object> accountInfo = getInfoByUuid(new String[] { accountUuid }, new String[] { "register_avatar_status" }, "1", false, false)
                .get(accountUuid);
        return MapUtils.getIntValue(accountInfo, "register_avatar_status");
    }

    /**
     * 个人主页访问记录
     *
     * @param operUuid
     * @param accountUuid
     * @param accountStatus
     */
    private void homepageAccessRecord(String operUuid, String accountUuid, String accountStatus) {
        if (StringUtils.isBlank(operUuid) || Objects.equals(operUuid, accountUuid)) {
            return;
        }

        Map<String, Object> operInfo = this.getInfoByUuid(new String[]{operUuid}, new String[]{"create_time", UuidInfoField.IS_CHECK_MOBILE}, "1", false, false).get(operUuid);
        long regTime = MapUtils.getLongValue(operInfo, "create_time", 0);
        //TODO 此处只查询 is_check_mobile
        int isCheckMobile = MapUtils.getIntValue(operInfo, UuidInfoField.IS_CHECK_MOBILE, 0);

        //未绑定手机号、注册七天内的用户不记录个人主页访问记录
        boolean isNew = DateUtil.currentTimeSeconds() - regTime < 7 * 86400;
        boolean noCheckMobile = isCheckMobile == 0;
        if (isNew || noCheckMobile) {
            return;
        }

        int accessDurationSecond = 10;
        String visitKey = String.format("view:homepage:%s:%s", operUuid, accountStatus);
        Boolean result = accountStringRedisTemplate.opsForValue().setIfAbsent(visitKey, String.valueOf(DateUtil.currentTimeSeconds()));
        //如果值已存在设置过期时间, 10秒内只推一次
        if (Objects.equals(result, false)) {
            Long ttl = accountStringRedisTemplate.getExpire(visitKey);
            if (ttl == -1) {
                accountStringRedisTemplate.expire(visitKey, accessDurationSecond, TimeUnit.SECONDS);
            }
            return;
        }

        accountStringRedisTemplate.expire(visitKey, accessDurationSecond, TimeUnit.SECONDS);

        // 推给业务记录主页访问次数 【谁看过我】这个业务在使用
        Map<String, String> datas = new HashMap<>();
        datas.put("account_uuid", operUuid);
        datas.put("be_account_uuid", accountUuid);
        // 2021.05.12 丁丁说开全量
        datas.put("test_tag", "B");
        MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("record_the_home_access", datas, null);
        if (mqResponse.fail()) {
            logger.error("用户{}, 访问{}的主页，埋点社区失败，失败码:{}，失败原因:{}", operUuid, accountUuid, mqResponse.getCode(), mqResponse.getMsg());
        }
    }

    /**
     * 历史业务 可下线
     *
     * @param uuid1
     * @param uuid2List
     * @return
     */
    @Deprecated
    public Map<String, String> mFindSimilar(String uuid1, List<String> uuid2List) {
        Map<String, String> resultMap = new HashMap<>();
        Set<String> uuidSet = new HashSet<>(uuid2List);
        uuidSet.add(uuid1);

        //用户信息，性别、性取向、所在地
        Map<String, Map<String, Object>> accountInfoMap = this.getInfoByUuid(uuidSet.toArray(new String[]{}), new String[]{"sex_type", "sexual", "baseaddr", "baseaddr_is_secret"}, "1", false, false);
        Map<String, Object> uuid1Info = accountInfoMap.get(uuid1);
        final int sexual1 = MapUtils.getIntValue(uuid1Info, "sexual");
        final int sexType1 = MapUtils.getIntValue(uuid1Info, "sex_type");
        final String baseaddr1 = MapUtils.getString(uuid1Info, "baseaddr", "");
        final Set<String> noSameRegionUuid = new HashSet<>();

        //先判断是否同城
        accountInfoMap.forEach((uuid2, uuid2Info) -> {
            if (Objects.equals(uuid1, uuid2)) {
                return;
            }

            int sexType2 = MapUtils.getIntValue(uuid2Info, "sex_type");
            String baseaddr2 = MapUtils.getString(uuid2Info, "baseaddr", "");
            Integer baseaddrIsSecret2 = MapUtils.getIntValue(uuid2Info, "baseaddr_is_secret");

            //性取向判断，性取向为男或女，则取性取向对应的性别; 否则取异性;
            if ((sexual1 == 1 && sexType2 == 1) || (sexual1 == 2 && sexType2 == 2)
                    || (sexual1 != 1 && sexual1 != 2 && (sexType1 == 1 && sexType2 == 2 || sexType1 == 2 && sexType2 == 1))) {
                //1. 是否同城
                if (baseaddrIsSecret2 != 1 && StringUtils.isNotBlank(baseaddr2)) {
                    if (baseaddr2.equals(baseaddr1)) {
                        resultMap.put(uuid2, "同城");
                        return;
                    }
                }

                noSameRegionUuid.add(uuid2);
                return;
            }

            resultMap.put(uuid2, "");
        });

        //非同城的判断标签匹配情况
        Map<String, Integer> labelSimilarMap = accountsLabelService.mGetLabelMatchValue(uuid1, new ArrayList<>(noSameRegionUuid));
        uuid2List.forEach(uuid2 -> {
            if (!resultMap.containsKey(uuid2)) {
                String similar;
                switch (MapUtils.getIntValue(labelSimilarMap, uuid2)) {
                    case 1:
                        similar = "TA是你喜欢类型";
                        break;
                    case 2:
                        similar = "你是TA喜欢类型";
                        break;
                    case 3:
                        similar = "你和TA拥有共同爱好";
                        break;
                    default:
                        similar = "";
                        break;
                }
                resultMap.put(uuid2, similar);
            }
        });
        return resultMap;
    }

    @Deprecated
    public String findSimilar(String uuid1, String uuid2) {
        return this.mFindSimilar(uuid1, Arrays.asList(uuid2)).get(uuid2);
    }

    public Map<String, Object> getAccountInfoByUserNew(String operUuid, String uuid, int version) {
        return this.mgetAccountInfoByUserNew(operUuid, new String[]{uuid}, version, 0).get(uuid);
    }

    /**
     * @param operUuid     操作人uuid
     * @param uuids        要获取的用户uuid数组
     * @param version
     * @param labelType
     * @return
     */
    // 头像、封面、昵称、性别、个人简介、社区等级、驾龄（单位天）、年龄、星座、婚恋状况、性取向、交友意向、所在地、家乡、特殊头衔、标签。生日、等级勋章、身份标识、是否可以修改性别、是否绑定手机
    //旧版本app，设置个人个人信息也是调用此接口获取数据，应此该接口不能将保密字段设置为空
    public Map<String, Map<String, Object>> mgetAccountInfoByUserNew(String operUuid, String[] uuids, int version, int labelType) {
        Map<String, Map<String, Object>> resultsMap = new LinkedHashMap<>();

        //在头衔的用户uuid，后面要用
        Map<String, Long> accountActorMap = new HashMap<>();

        //1. 用户基本信息
        Map<String, Map<String, Object>> usersResultMap = this.getInfoByUuid(uuids, new String[]{
                "uuid", "account_status", UuidInfoField.ZHIMA_CERTIFICATION, "face_certification", "profile_verify_status", UuidInfoField.IS_CHECK_MOBILE, "profile_error_msg", "avatar", "account_name", "account_name_origin", "account_name_status", "sex_type", "personal_profile",
                UuidInfoField.ACCOUNT_LEVEL, "create_time", UuidInfoField.AGE, UuidInfoField.AGE_IS_SECRET, UuidInfoField.CONSTELLATION, "affectivestatus", "affectivestatus_is_secret", "sexual", "sexual_is_secret", "baseaddr", "baseaddr_is_secret", "dating_intention",
                "hometown", "hometown_is_secret", "account_actor", UuidInfoField.BIRTH, UuidInfoField.HANDSOME_STATUS, UuidInfoField.RICH_STATUS, UuidInfoField.ABILITY_STATUS, UuidInfoField.STAR_STATUS, UuidInfoField.HOST_STATUS, UuidInfoField.DOCTOR_STATUS,
                UuidInfoField.IS_UPDATE_SEX, "avatar_dress_id", UuidInfoField.DRIVER_LEVEL, UuidInfoField.KILOMETER,
                "default_card_id", "avatar_origin", "avatar_status", "education", "profession", "height", "weight", "trade", "personal_profile_status", "voice_sign", "voice_sign_audit_time", "voice_sign_url", "voice_sign_duration", "voice_sign_audit_reason"
                , "pass_voice_sign", "pass_voice_sign_audit_time", "pass_voice_sign_url", "pass_voice_sign_duration", "pass_voice_sign_audit_reason", "pass_personal_profile", "pass_personal_profile_status", "longitude", "latitude", "enable_location", "platform_id", "app_version",
                "pre_profile_verify_status", "pre_profile_error_msg", "income", "my_introduction_status", "my_introduction_content", "my_introduction_imgs", "pass_introduction_status", "pass_introduction_content", "pass_introduction_imgs", "home_cover", "pass_home_cover", "has_set_home_cover",
                "real_avatar_certification", UuidInfoField.IP_CITY
        }, String.valueOf(version), true, true);

        //2. 用户封面
        int uuidLen = uuids.length;
        boolean[] onlyCheckCovers = new boolean[uuidLen];
        for (int i = 0; i < uuidLen; i++) {
            String uuid = uuids[i];
            onlyCheckCovers[i] = !Objects.equals(operUuid, uuid);
        }
        Map<String, List<Map<String, String>>> accountCoversMap = mgetAccountsCover(uuids, onlyCheckCovers);


        //4. 循环构建用户map数据
        for (Map.Entry<String, Map<String, Object>> entry : usersResultMap.entrySet()) {
            String uuid = entry.getKey();
            Map<String, Object> userResultMap = entry.getValue();

            String avatar = MapUtils.getString(userResultMap, "avatar", "");
            Integer profileVerifyStatus = MapUtils.getInteger(userResultMap, "profile_verify_status", 0);
            Integer preProfileVerifyStatus = MapUtils.getInteger(userResultMap, "pre_profile_verify_status");
            String profileErrorMsg = MapUtils.getString(userResultMap, "profile_error_msg", "");
            String preProfileErrorMsg = MapUtils.getString(userResultMap, "pre_profile_error_msg", "");
            int avatarStatus = MapUtils.getIntValue(userResultMap, "avatar_status");
            if (Objects.equals(operUuid, uuid) && avatarStatus == 0) {//只有自己可以查看自己上传的未审核的头像
                avatar = MapUtils.getString(userResultMap, "avatar_origin", "");
                if (null != preProfileVerifyStatus) {
                    profileVerifyStatus = preProfileVerifyStatus;
                    profileErrorMsg = preProfileErrorMsg;
                }
            }

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("avatar", avatar); //头像
            resultMap.put("avatar_status", String.valueOf(avatarStatus));
            resultMap.put("profile_verify_status", profileVerifyStatus);
            resultMap.put("profile_error_msg", profileErrorMsg);
            resultMap.put("real_avatar_certification", MapUtils.getString(userResultMap, "real_avatar_certification", ""));

            // 2020.04.08 增加日志
            List<Map<String, String>> accountsPhotoAllByRedis = accountsPhotoService.getAccountsPhotoAllByRedis(uuid, false);
            if (accountsPhotoAllByRedis != null && accountsPhotoAllByRedis.size() > 0) {
                for (Map<String, String> map : accountsPhotoAllByRedis) {
                    if (Objects.equals(map.get("seq"), "1")) {
                        String status = map.get("status");// 1-未审核

                        // 比较 avatarStatus  头像状态 0:未审核; 1:已审核  不一致 记录
                        if (Objects.equals(status, "1") && Objects.equals(avatarStatus, 1)) {
                            logger.info("头像与相册不相同，accountUuid={},avatar={},photo={}", uuid, avatar, map);
                        }
                        break;
                    }
                }
            }

            // 2021-06-18 新增返回is_bind_mobile字段，java落地以is_check_mobile为准，故在此处转换一下
            Integer isCheckMobile = MapUtils.getInteger(userResultMap, UuidInfoField.IS_CHECK_MOBILE, 0);
            resultMap.put("is_bind_mobile", isCheckMobile);

            String nickname = MapUtils.getString(userResultMap, "account_name", "");
            int nicknameStatus = MapUtils.getIntValue(userResultMap, "account_name_status");
            if (Objects.equals(operUuid, uuid) && nicknameStatus == 0) {//只有自己可以查看自己上传的未审核的昵称
                nickname = MapUtils.getString(userResultMap, "account_name_origin", "");
            }

            String education = MapUtils.getString(userResultMap, "education", "");
            String trade = MapUtils.getString(userResultMap, "trade", "");
            String profession = MapUtils.getString(userResultMap, "profession", "");
            String height = MapUtils.getString(userResultMap, "height", "0");
            String weight = MapUtils.getString(userResultMap, "weight", "0");
            Integer income = MapUtils.getInteger(userResultMap, "income", 0);

            resultMap.put("education", education);
            resultMap.put("trade", trade);
            // 客态看 如果职业是其他，则隐藏
            if (!Objects.equals(operUuid, uuid) && Objects.equals(profession, "其他")){
                resultMap.put("profession", "");
            }else{
                resultMap.put("profession", profession);
            }
            resultMap.put("height", height);
            resultMap.put("weight", weight);
            resultMap.put("income", String.valueOf(income));

            //语音签名相关信息
            int sexType = MapUtils.getIntValue(userResultMap, "sex_type", 0);
            Map<String, Object> voiceSignMap = getVoiceSignInfo(userResultMap, operUuid, uuid);
            resultMap.put("voice_sign_info", voiceSignMap);

            // 个人简介
            String personalProfile = "";
            if (Objects.equals(operUuid, uuid)) {
                TqUserGradeClient.UserPrivilege privilege = getInterestPrivilege(uuid, "profile", sexType);
                Integer isPersonalProfileEnable = privilege.getEnable() ? 1 : 0;
                Integer personalProfileScore = privilege.getReach_score();
                personalProfile = MapUtils.getString(userResultMap, "personal_profile", "");
                resultMap.put("personal_profile", personalProfile); //个人简介
                resultMap.put("personal_profile_status", MapUtils.getString(userResultMap, "personal_profile_status"));
                resultMap.put("personal_profile_permission", String.valueOf(isPersonalProfileEnable)); //个人简介有趣值权限
                resultMap.put("personal_profile_interest_score", String.valueOf(personalProfileScore)); //个人简介有趣值
            } else {
                personalProfile = MapUtils.getString(userResultMap, "pass_personal_profile", "");
                resultMap.put("personal_profile", personalProfile); //个人简介
                resultMap.put("personal_profile_status", MapUtils.getString(userResultMap, "pass_personal_profile_status"));
                resultMap.put("personal_profile_permission", null); //个人简介有趣值权限
                resultMap.put("personal_profile_interest_score", null); //个人简介有趣值
            }

            resultMap.put("nickname", nickname); //昵称
//            int sexType = MapUtils.getIntValue(userResultMap, "sex_type", 0);
            resultMap.put("sex_type", String.valueOf(sexType)); //性别

            resultMap.put("age", MapUtils.getString(userResultMap, UuidInfoField.AGE, "")); //年龄
            resultMap.put("account_status", MapUtils.getString(userResultMap, "account_status", ""));

            resultMap.put("constellation", MapUtils.getString(userResultMap, UuidInfoField.CONSTELLATION, "")); //星座
            int affectivestatus = MapUtils.getIntValue(userResultMap, "affectivestatus", 0);
            resultMap.put("affectivestatus", String.valueOf(affectivestatus)); //婚恋情况
            resultMap.put("affectivestatus_is_secret", MapUtils.getString(userResultMap, "affectivestatus_is_secret", "0"));
            int sexual = MapUtils.getIntValue(userResultMap, "sexual", 0);
            resultMap.put("sexual", String.valueOf(sexual)); //性取向
            resultMap.put("sexual_is_secret", MapUtils.getString(userResultMap, "sexual_is_secret", "0"));
            int datingIntention = MapUtils.getIntValue(userResultMap, "dating_intention", 0);
            resultMap.put("dating_intention", String.valueOf(datingIntention)); //交友意向
            String baseaddr = MapUtils.getString(userResultMap, "baseaddr", "");
            resultMap.put("baseaddr", baseaddr); //所在地

//            Integer appcode = RequestParams.getSoa_basic_java().getAppcode();
//            Integer cloned = RequestParams.getSoa_basic_java().getCloned();
//            String token = RequestParams.getSoa_basic_java().getToken();
            Long appVersion = RequestParams.getSoa_basic_java().getApp_version();
            Integer platfromId = RequestParams.getSoa_basic_java().getPlatform_id();
            // 用户如果没有开启定位则不显示用户位置
            Long seenAppVersion = MapUtils.getLong(userResultMap, "app_version");
            Integer seenPlatfromId = MapUtils.getInteger(userResultMap, "platform_id");
            Integer enableLocation = MapUtils.getInteger(userResultMap, "enable_location");
            if (!Objects.equals(CommonEnableStatus.ENABLE.getStatus(), enableLocation)) {
                resultMap.put("baseaddr", "");
            }
            resultMap.put("baseaddr_is_secret", MapUtils.getString(userResultMap, "baseaddr_is_secret", "0"));
            resultMap.put("hometown_is_secret", MapUtils.getString(userResultMap, "hometown_is_secret", "0"));
            resultMap.put("age_is_secret", MapUtils.getString(userResultMap, UuidInfoField.AGE_IS_SECRET, "0"));

            //新版本用户看旧版本用户时，不显示所在地
            if (greaterAppVersion(platfromId, appVersion, 7209L, 10129L, 10129L) && lessAppVersion(seenPlatfromId, seenAppVersion, 7209L, 10129L, 10129L)) {
                resultMap.put("baseaddr", "");
            }

            String hometown = MapUtils.getString(userResultMap, "hometown", "");
            resultMap.put("hometown", hometown); //家乡
            // ip定位城市
            resultMap.put("c_city", MapUtils.getString(userResultMap, UuidInfoField.IP_CITY, ""));

            String accountActor = MapUtils.getString(userResultMap, "account_actor", "");
            resultMap.put("account_actor", accountActor); //特殊头衔
            long birth = MapUtils.getLongValue(userResultMap, UuidInfoField.BIRTH, 0L);
            resultMap.put("birth", String.valueOf(birth)); //生日
            resultMap.put("is_update_sex", MapUtils.getString(userResultMap, UuidInfoField.IS_UPDATE_SEX, "0"));

            List<Map<String, String>> accountsPhoto = accountCoversMap.get(uuid);//getAccountsCover(uuid, !Objects.equals(operUuid, uuid));
            // 是否已点赞
            for (Map<String, String> p: accountsPhoto) {
                p.put("is_thumbs_up", accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_PHOTO_THUMBS_UP.setArg(Md5Util.encryptSHA1(p.get("id") + p.get("pic_url") + operUuid))) ? "1": "0");
            }
            resultMap.put("img_list", accountsPhoto); //封面（头像数组）
            resultMap.put("driver_level", MapUtils.getString(userResultMap, UuidInfoField.DRIVER_LEVEL));
            resultMap.put("kilometer", MapUtils.getString(userResultMap, UuidInfoField.KILOMETER));
            //该字段渐渐废弃，不建议使用
            resultMap.put("account_card_id", MapUtils.getString(userResultMap, "default_card_id"));
            resultMap.put("default_card_id", MapUtils.getString(userResultMap, "default_card_id"));
            //他趣应用
            if (Objects.equals("1", MapUtils.getString(userResultMap, UuidInfoField.ZHIMA_CERTIFICATION, ""))) {
                userResultMap.put("gender_certification", "1");
            }

            resultMap.put("gender_certification", MapUtils.getString(userResultMap, UuidInfoField.GENDER_CERTIFICATION, "0"));
            // 2020.03.04 活体认证
            resultMap.put("face_certification", MapUtils.getString(userResultMap, "face_certification", "0"));

            resultMap.put("account_level", MapUtils.getString(userResultMap, UuidInfoField.ACCOUNT_LEVEL, "0")); //社区等级
            resultMap.put("zhima_certification", MapUtils.getString(userResultMap, UuidInfoField.ZHIMA_CERTIFICATION, "0"));

            // 2023.08.16 ab实验入组 https://o15vj1m4ie.feishu.cn/wiki/CEFYwjPeViJoCFkF1Enckgv1nzd
//            Map<String, Object> userExp = SoaService.getUserExp(operUuid, AbRuleCode.DISTANCE_PRIVACY_SETUP, appcode, cloned, token);
//            String varias = MapUtils.getString(userExp, "varias", "");

            // 2023.08.09 个人主页改地理位置显示逻辑 https://o15vj1m4ie.feishu.cn/docx/Th1LduGcUo6LEpxBgKEcpiYan7f
            //LOGGER.info("开始根据用户定位信息计算用户距离,被查看者用户uuid={},用户定位信息={}",uuid,enableLocation);
            // 2023.08.16 实验组用户 或者对方未隐藏，能看
//            boolean privacyStatus=privacyService.getAccountPrivacyStatus(uuid);
            //!SoaService.getAccountPrivacyStatus(uuid)) 调用新隐私服务
//            if (!Objects.equals(CommonEnableStatus.DISABLE.getStatus(), enableLocation) && (Objects.equals(varias, "new") || !privacyStatus)) {
            // 2024.11.28 实验已经固化
            if (!Objects.equals(CommonEnableStatus.DISABLE.getStatus(), enableLocation) ) {
                Double lon = MapUtils.getDouble(userResultMap, "longitude");
                Double lat = MapUtils.getDouble(userResultMap, "latitude");
                BigDecimal distance = getDistance(operUuid, lon, lat);
                if (null != distance) {
                    String disStr = String.valueOf(distance);
                    //客户端无法显示0，这边设置为0.01
                    if ("0.00".equals(disStr)) {
                        disStr = "0.01";
                    }
                    resultMap.put("distance", disStr);
                } else {
                    resultMap.put("distance", "");
                }
            } else {
                resultMap.put("distance", "");
            }
            resultMap.put("distance_unit", "km");

            //用户身份标识
            Map<String, Object> accountIdentityStatusMap = new HashMap<>();
            accountIdentityStatusMap.put("handsome_status", MapUtils.getString(userResultMap, UuidInfoField.HANDSOME_STATUS));
            accountIdentityStatusMap.put("rich_status", MapUtils.getString(userResultMap, UuidInfoField.RICH_STATUS));
            accountIdentityStatusMap.put("ability_status", MapUtils.getString(userResultMap, UuidInfoField.ABILITY_STATUS));
            accountIdentityStatusMap.put("star_status", MapUtils.getString(userResultMap, UuidInfoField.STAR_STATUS));
            accountIdentityStatusMap.put("host_status", MapUtils.getString(userResultMap, UuidInfoField.HOST_STATUS));
            accountIdentityStatusMap.put("doctor_status", MapUtils.getString(userResultMap, UuidInfoField.DOCTOR_STATUS));
            Map<String, List<String>> identityMap = getAccountIdentity(accountIdentityStatusMap);

            long createTime = MapUtils.getLongValue(userResultMap, "create_time", 0L);
            int driverYears = (int) ((DateUtil.currentTimeSeconds() - createTime) / (3600 * 24));
            resultMap.put("driver_years", driverYears); // 驾龄（天），当前日期-注册日期
            resultMap.put("identity", identityMap.get("have")); //身份标识
            resultMap.put("can_identity", identityMap.get("notHave")); //可获得的身份标识
            resultMap.put("obtain_achievement", accountsAchievementService.getObtainHighAchievementList(uuid, version > 1 ? version : 0));
            resultMap.put("medal_name", "");
            if (!Objects.equals("", accountActor) && Integer.parseInt(accountActor) > 0) {//有头衔，获取头衔
                accountActorMap.put(uuid, Long.valueOf(accountActor));
            }

            //3. 用户标签
            //Map<String, List<PersonalityLabel>> likeTypeLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(uuids), 1);
            Map<String, List<PersonalityLabel>> personalityLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(uuids), 2);
            List<PersonalityLabel> likeTypeLabel = Lists.newArrayList();
            List<PersonalityLabel> personalityLabel = personalityLabels.get(uuid);
            if (labelType == 1) {
                resultMap.put("like_type_label", likeTypeLabel); //喜欢类型标签
                resultMap.put("personality_label", labelNameArray(personalityLabel)); //个性标签
            } else {
                resultMap.put("like_type_label", likeTypeLabel); //喜欢类型标签
                resultMap.put("personality_label", simplifyLabel(personalityLabel)); //个性标签
            }

            //2018.1.9  判断版本下发不同信息 平台编码 1为android 2为ios 3为ipad
            String dressId = MapUtils.getString(userResultMap, "avatar_dress_id", "");
            resultMap.put("avatar_dress_id", dressId);

            // 2020.05.14 喜欢的类型
            Map<String, List<Map<String, String>>> accountsLikeLabelMap = accountsLikeLabelService.listAccountsLikeLabelAll(uuid);
            resultMap.put("like_list", accountsLikeLabelMap);

            AccountSchoolDto schoolInfo = schoolService.info(uuid, false);
            List<AccountLabelCfg> labels = accountLabelServiceV2.listMyLabelInfo(uuid);
            boolean isOrigin = false; // 是否主态
            List<AccountsLife> accountsLife = accountsLifeService.getAccountsLifeOrAccountsPhoto(uuid, isOrigin, false);

//            resultMap.put("info_percent", String.valueOf((int) this.calInfoPercent(avatar, nickname, sexType, birth, datingIntention, sexual, affectivestatus, baseaddr, hometown, personalProfile
//                    , MapUtils.getString(userResultMap, "pass_voice_sign_url"), education, profession, height, weight, likeTypeLabel, personalityLabel, accountsPhoto
//                    , accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.MUSIC.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey), income)));
            resultMap.put("info_percent", String.valueOf((int) this.calInfoPercentV2(uuid, avatar, nickname, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile
                , MapUtils.getString(userResultMap, "pass_voice_sign_url"), education, profession, height, weight, personalityLabel
                , accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey)
                , income, schoolInfo, labels, accountsLife)));

            // 自我介绍 封面 区分主客态
            if(StringUtils.isNotBlank(operUuid) && operUuid.equals(uuid)){
                resultMap.put("pass_introduction_status", MapUtils.getString(userResultMap, "my_introduction_status", "-1"));
                resultMap.put("pass_introduction_content", MapUtils.getString(userResultMap, "my_introduction_content", ""));
                String myIntroductionImgsStr = MapUtils.getString(userResultMap, "my_introduction_imgs", "");
                List<JSONObject> introductionImgs = new ArrayList<>();
                if(StringUtils.isNotBlank(myIntroductionImgsStr)){
                    introductionImgs = JSON.parseObject(myIntroductionImgsStr, List.class);
                    for (JSONObject img :introductionImgs) {
                        // 使用源站域名 + 私有token
                        img.put("img_name", AccountsIntroductionService.getPrivatePic(img.getString("img_name")));
                        String imgWidth = img.getString("width");
                        String imgHeight = img.getString("height");
                        img.put("width", StringUtils.isNumeric(imgWidth) && !imgWidth.equals("0") ? imgWidth : CommConst.DEFAULT_INTRODUCTION_IMG_WIDTH);
                        img.put("height", StringUtils.isNumeric(imgHeight) && !imgHeight.equals("0") ? imgHeight : CommConst.DEFAULT_INTRODUCTION_IMG_HEIGHT);
                    }
                }
                resultMap.put("pass_introduction_imgs", introductionImgs);
                String homeCover = MapUtils.getString(userResultMap, "home_cover", "");
                resultMap.put("home_cover", StringUtils.isBlank(homeCover) ? CommConst.DEFAULT_HOME_COVER : ToolsService.addPhotoUrlPreDomain(AvatarHandleService.getAvatarOfSavePhoto(homeCover)));
                resultMap.put("has_home_cover", StringUtils.isBlank(homeCover) ? "0" : "1");
                String hasSetHomeCover = MapUtils.getString(userResultMap, "has_set_home_cover");
                resultMap.put("has_set_home_cover", StringUtils.isBlank(hasSetHomeCover) ? "0" : hasSetHomeCover);
            }else{
                resultMap.put("pass_introduction_status", MapUtils.getString(userResultMap, "pass_introduction_status", "-1"));
                resultMap.put("pass_introduction_content", MapUtils.getString(userResultMap, "pass_introduction_content", ""));
                String myIntroductionImgsStr = MapUtils.getString(userResultMap, "pass_introduction_imgs", "");
                List<JSONObject> introductionImgs = new ArrayList<>();
                if(StringUtils.isNotBlank(myIntroductionImgsStr)){
                    introductionImgs = JSON.parseObject(myIntroductionImgsStr, List.class);
                    for (JSONObject img :introductionImgs) {
                        img.put("img_name", ToolsService.addPhotoUrlPreDomain(AvatarHandleService.getAvatarOfSavePhoto(img.getString("img_name"))));
                        String imgWidth = img.getString("width");
                        String imgHeight = img.getString("height");
                        img.put("width", StringUtils.isNumeric(imgWidth) && !imgWidth.equals("0") ? imgWidth : CommConst.DEFAULT_INTRODUCTION_IMG_WIDTH);
                        img.put("height", StringUtils.isNumeric(imgHeight) && !imgHeight.equals("0") ? imgHeight : CommConst.DEFAULT_INTRODUCTION_IMG_HEIGHT);
                    }
                }
                resultMap.put("pass_introduction_imgs", introductionImgs);
                String homeCover = MapUtils.getString(userResultMap, "pass_home_cover", "");
                resultMap.put("home_cover", StringUtils.isBlank(homeCover) ? CommConst.DEFAULT_HOME_COVER : ToolsService.addPhotoUrlPreDomain(AvatarHandleService.getAvatarOfSavePhoto(homeCover)));
                resultMap.put("has_home_cover", StringUtils.isBlank(homeCover) ? "0" : "1");
                String hasSetHomeCover = MapUtils.getString(userResultMap, "has_set_home_cover");
                resultMap.put("has_set_home_cover", StringUtils.isBlank(hasSetHomeCover) ? "0" : hasSetHomeCover);
            }

            // 2024.4.18 客户端代码已回滚，可以不下发
//            resultMap.put("change_age_status", MapUtils.getString(userResultMap, UuidInfoField.CHANGE_AGE_STATUS, "1"));

            //返回认证信息文案
            setIsBindMobileDesc(resultMap);
            setFaceCertificationDesc(resultMap);
            setIdentityNo(resultMap, uuid);

            resultsMap.put(uuid, resultMap);
        }

        //处理用户头衔
        if (!accountActorMap.isEmpty()) {
            List<Long> medalIdList = accountActorMap.values().stream().distinct().collect(Collectors.toList());
            Map<Long, Map<String, String>> medalInfos = accountsMedalService.mGetInfo(medalIdList, "medal_name");
            accountActorMap.forEach((uuid, medalId) -> {
                Map<String, String> medalInfo = medalInfos.get(medalId);
                Map<String, Object> resultMap = resultsMap.get(uuid);
                resultMap.put("medal_name", medalInfo.get("medal_name"));
            });
        }

        return resultsMap;
    }

    private void setIdentityNo(Map<String, Object> resultMap, String uuid){
        //芝麻认证[0:未认证,1:认证]
        String zhimaCertification = MapUtils.getString(resultMap, UuidInfoField.ZHIMA_CERTIFICATION, "0");
        if("1".equals(zhimaCertification)){
            String identityNo=getIdentityNo(uuid);
            String identityNoDesc="已认证居民身份证件";
            if(!StringUtils.isBlank(identityNo)){
                identityNoDesc=identityNoDesc+":"+identityNo;
            }
            resultMap.put("identity_no_desc",identityNoDesc);
        }
    }

    private String getIdentityNo(String uuid){
        AccountsCertification accountsCertification=null;
        try{
            accountsCertification=accountsCertificationService.getAccountsCertification(uuid);
        }catch (Exception e){
            logger.warn("getAccountsCertification fail uuid:{}",uuid,e);
        }
        String identityNoNew="";
        if(accountsCertification==null){
            return identityNoNew;
        }
        String identityNo="";
        if(ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
            identityNo=EncryptUtil.decrypt(accountsCertification.getIdentityNoCipher());
        }else {
            identityNo=accountsCertification.getIdentityNo();
        }

        if(StringUtils.isBlank(identityNo)){
           return identityNoNew;
        }
        identityNoNew=getIdentityNoStr(identityNo);
        return identityNoNew;
    }

    private String getIdentityNoStr(String identityNo){
        String start=identityNo.substring(0,1);
        int length=identityNo.length();
        String end=identityNo.substring(length-1,length);
        StringBuffer sb=new StringBuffer(start);
        if (identityNo.length() == 15){
            sb.append(IDENTITY_NO_15);
        }else if (identityNo.length() == 18){
            sb.append(IDENTITY_NO_18);
        }else {
            return "";
        }
        sb.append(end);
        String identityNoStr=sb.toString();
        if(identityNoStr.length()==15 || identityNoStr.length()==18){
            return identityNoStr;
        }
        return "";
    }

    private void setIsBindMobileDesc(Map<String, Object> resultMap){
        //是否绑定手机[1:是，0:否]
        String isCheckMobile = MapUtils.getString(resultMap, UuidInfoField.IS_BIND_MOBILE, "0");
        if("1".equals(isCheckMobile)){
            resultMap.put("is_bind_mobile_desc", "已认证用户真实手机");
        }
    }

    private void setFaceCertificationDesc(Map<String, Object> resultMap){
        String faceCertification = MapUtils.getString(resultMap, "face_certification", "0");
        String realAvatarCertification = MapUtils.getString(resultMap, "real_avatar_certification", "0");
        if("1".equals(faceCertification) && "1".equals(realAvatarCertification)){
            resultMap.put("face_certification_desc", "头像为本人真实照片，已通过人脸对比");
        }
    }

    private Boolean greaterAppVersion(Integer platfromId, Long appVersion, Long greaterAndroidVersion, Long greaterIphoneVersion, Long greaterIpadVersion) {
        if ((Objects.equals(1, platfromId) && appVersion.longValue() >= greaterAndroidVersion) || (appVersion.longValue() >= greaterIphoneVersion && Objects.equals(2, platfromId))
                || (appVersion.longValue() >= greaterIpadVersion && Objects.equals(3, platfromId))) {
            return true;
        }
        return false;
    }

    private Boolean lessAppVersion(Integer platfromId, Long appVersion, Long lessAndroidVersion, Long lessIphoneVersion, Long lessIpadVersion) {
        if ((Objects.equals(1, platfromId) && appVersion.longValue() < lessAndroidVersion) || (appVersion.longValue() < lessIphoneVersion && Objects.equals(2, platfromId))
                || (appVersion.longValue() < lessIpadVersion && Objects.equals(3, platfromId))) {
            return true;
        }
        return false;
    }

    public Map<String, Object> getVoiceSignInfo(Map<String, Object> userResultMap, String operUuid, String uuid) {
        Map<String, Object> voiceSignMap = Maps.newHashMap();
        if (Objects.equals(operUuid, uuid)) {
            String voiceSign = MapUtils.getString(userResultMap, "voice_sign");
            int sexType = MapUtils.getIntValue(userResultMap, "sex_type", 0);
            if (StringUtils.isNotBlank(voiceSign)) {
                voiceSignMap.put("voice_sign", voiceSign);
            } else {
                voiceSignMap.put("voice_sign", null);
            }

            if (StringUtils.isNotBlank(MapUtils.getString(userResultMap, "voice_sign_audit_time"))) {
                voiceSignMap.put("voice_sign_audit_time", MapUtils.getString(userResultMap, "voice_sign_audit_time"));
            } else {
                voiceSignMap.put("voice_sign_audit_time", null);
            }

            String voiceSignUrl = MapUtils.getString(userResultMap, "voice_sign_url");
            if (StringUtils.isNotBlank(voiceSignUrl)) {
                // 2024.06.07 拼接源站域名 + 私有token
                voiceSignUrl = getVoiceSignPrivateUrl(voiceSignUrl);
                voiceSignMap.put("voice_sign_url", voiceSignUrl);

            } else {
                voiceSignMap.put("voice_sign_url", null);
            }

            if (StringUtils.isNotBlank(MapUtils.getString(userResultMap, "voice_sign_duration"))) {
                voiceSignMap.put("voice_sign_duration", MapUtils.getString(userResultMap, "voice_sign_duration"));
            } else {
                voiceSignMap.put("voice_sign_duration", null);
            }

            if (StringUtils.isNotBlank(MapUtils.getString(userResultMap, "voice_sign_audit_reason"))) {
                voiceSignMap.put("voice_sign_audit_reason", MapUtils.getString(userResultMap, "voice_sign_audit_reason"));
            } else {
                voiceSignMap.put("voice_sign_audit_reason", null);
            }

            TqUserGradeClient.UserPrivilege privilege = getInterestPrivilege(operUuid, "voice_sign", sexType);
            Integer isVoiceSignEnable = privilege.getEnable() ? 1 : 0;
            Integer voiceSignScore = privilege.getReach_score();
            voiceSignMap.put("voice_sign_permission", String.valueOf(isVoiceSignEnable));
            voiceSignMap.put("voice_sign_interest_score", String.valueOf(voiceSignScore));
            Integer isCanReRecord = voiceSignInfoService.isPermit(operUuid) ? 1 : 0;
            voiceSignMap.put("is_can_re_record", String.valueOf(isCanReRecord));
            if (0 == isCanReRecord) {
                voiceSignMap.put("non_re_record_reason", VoiceSignInfoService.NON_REC_RECORD_REASON);
            } else {
                voiceSignMap.put("non_re_record_reason", null);
            }

        } else {
            String passVoiceSign = MapUtils.getString(userResultMap, "pass_voice_sign");

            if (StringUtils.isNotBlank(passVoiceSign)) {
                voiceSignMap.put("voice_sign", passVoiceSign);
            } else {
                voiceSignMap.put("voice_sign", null);
            }

            if (StringUtils.isNotBlank(MapUtils.getString(userResultMap, "pass_voice_sign_audit_time"))) {
                voiceSignMap.put("voice_sign_audit_time", MapUtils.getString(userResultMap, "pass_voice_sign_audit_time"));
            } else {
                voiceSignMap.put("voice_sign_audit_time", null);
            }

            String voiceSignUrl = MapUtils.getString(userResultMap, "pass_voice_sign_url");
            if (StringUtils.isNotBlank(voiceSignUrl)) {
                voiceSignMap.put("voice_sign_url", voiceSignUrl);

            } else {
                voiceSignMap.put("voice_sign_url", null);
            }

            if (StringUtils.isNotBlank(MapUtils.getString(userResultMap, "pass_voice_sign_duration"))) {
                voiceSignMap.put("voice_sign_duration", MapUtils.getString(userResultMap, "pass_voice_sign_duration"));
            } else {
                voiceSignMap.put("voice_sign_duration", null);
            }

            if (StringUtils.isNotBlank(MapUtils.getString(userResultMap, "pass_voice_sign_audit_reason"))) {
                voiceSignMap.put("voice_sign_audit_reason", MapUtils.getString(userResultMap, "pass_voice_sign_audit_reason"));
            } else {
                voiceSignMap.put("voice_sign_audit_reason", null);
            }
            voiceSignMap.put("voice_sign_permission", null);
            voiceSignMap.put("voice_sign_interest_score", null);
            voiceSignMap.put("is_can_re_record", null);
            voiceSignMap.put("non_re_record_reason", null);
        }

        voiceSignMap.put("voice_sign_limit_time", VoiceSignInfoService.VOICE_SIGN_LIMIT_TIME);
        return voiceSignMap;
    }

    /**
     * 获取访问私有地址
     *
     * @param url
     * @return
     */
    public static String getVoiceSignPrivateUrl(String url) {
        return SoaService.privateDownloadUrl(CommConst.AUDIO_BUCKET_TYPE, CommConst.AUDIO_SRC_HOST + AvatarHandleService.getAvatarOfSavePhoto(url), CommConst.APPCODE_TAQU, 3600L);
    }

    /**
     * 经纬度计算距离
     *
     * @param operUuid 用户自己
     * @param lon1 他人
     * @param lat1 他人
     * @return
     */
    private BigDecimal getDistance(String operUuid, Double lon1, Double lat1) {
        if (null == operUuid) {
            return null;
        }
        Map<String, Map<String, Object>> accountInfoMap = getInfoByUuid(new String[]{operUuid}, new String[]{"longitude", "latitude", "enable_location"}, "1", false, false);
        Map<String, Object> uuid1Info = accountInfoMap.get(operUuid);
        Integer enableLocation = MapUtils.getInteger(uuid1Info, "enable_location");
        Double lon2 = MapUtils.getDouble(uuid1Info, "longitude");
        Double lat2 = MapUtils.getDouble(uuid1Info, "latitude");
        if (Objects.equals(CommonEnableStatus.DISABLE.getStatus(), enableLocation)) {
            return null;
        }
        if (null == lon1 || null == lat1 || null == lon2 || null == lat2) {
            return null;
        }
        Double d = LocationUtils.getDistance(lat1, lon1, lat2, lon2);
        BigDecimal b = BigDecimal.valueOf(d).setScale(2, BigDecimal.ROUND_HALF_UP);
        return b;
    }


    /**
     * 获取用户某个行为的有趣值权限
     * 接口文档地址：https://api.admin.internal.taqu.cn/docs/api/api-1bv60pkabt1dd
     *
     * @param accountUuid
     * @param actionName
     * @return
     */
    public TqUserGradeClient.UserPrivilege getInterestPrivilege(String accountUuid, String actionName,int sexType) {
        return SpringContextHolder.getBean(TqUserGradeClient.class).privilege(accountUuid, actionName, sexType);
    }

    private List<Map<String, Object>> simplifyLabel(List<PersonalityLabel> labels) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (labels != null && !labels.isEmpty()) {
            for (PersonalityLabel personalityLabel : labels) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", personalityLabel.getId());
                map.put("name", personalityLabel.getContent());
                map.put("type", personalityLabel.getType()); // 标签类型 1-喜欢类型，2-自我描述，3-兴趣爱好，4-好友评价
                result.add(map);
            }
        }
        return result;
    }

    private List<String> labelNameArray(List<PersonalityLabel> labels) {
        List<String> result = new ArrayList<>();
        if (labels != null && !labels.isEmpty()) {
            for (PersonalityLabel personalityLabel : labels) {
                result.add(personalityLabel.getContent());
            }
        }
        return result;
    }

    /**
     * 获取资料完成度
     *
     * @param accountUuid
     * @return
     */
    public Map<String, Object> getInfoPercent(String accountUuid) {
        Map<String, Object> result = new HashMap<>();
        result.put("info_percent", 0D);

        if (StringUtils.isBlank(accountUuid)) {
            return result;
        }

        Map<String, Object> accountInfoMap = this.getInfoByUuid(new String[]{accountUuid}, new String[]{"sex_type", "active_time", "baseaddr", "avatar", "account_name", UuidInfoField.BIRTH, "dating_intention", "sexual", "affectivestatus", "hometown"
                , "personal_profile", "baseaddr_is_secret", "pass_voice_sign_url", "education", "profession", "height", "weight", "income"}, "1", false, true).get(accountUuid);
        if (accountInfoMap == null) {
            return result;
        }

        String avatar = MapUtils.getString(accountInfoMap, "avatar");
        String accountName = MapUtils.getString(accountInfoMap, "account_name");
        int sexType = MapUtils.getIntValue(accountInfoMap, "sex_type", 0);
        Long birth = MapUtils.getLong(accountInfoMap, UuidInfoField.BIRTH);
        Integer datingIntention = MapUtils.getInteger(accountInfoMap, "dating_intention");
        Integer sexual = MapUtils.getInteger(accountInfoMap, "sexual");
        Integer affectivestatus = MapUtils.getInteger(accountInfoMap, "affectivestatus");
        String baseaddr = MapUtils.getString(accountInfoMap, "baseaddr", "");
        String hometown = MapUtils.getString(accountInfoMap, "hometown");
        String personalProfile = MapUtils.getString(accountInfoMap, "personal_profile");
        String voiceSignUrl = MapUtils.getString(accountInfoMap, "pass_voice_sign_url");
        String education = MapUtils.getString(accountInfoMap, "education");
        String profession = MapUtils.getString(accountInfoMap, "profession");
        String height = MapUtils.getString(accountInfoMap, "height");
        String weight = MapUtils.getString(accountInfoMap, "weight");
        Integer income = MapUtils.getInteger(accountInfoMap, "income");

        Map<String, List<PersonalityLabel>> likeTypeLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(accountUuid), 1);
        Map<String, List<PersonalityLabel>> personalityLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(accountUuid), 2);
        List<PersonalityLabel> likeTypeLabel = likeTypeLabels.get(accountUuid);
        List<PersonalityLabel> personalityLabel = personalityLabels.get(accountUuid);
        Map<String, List<Map<String, String>>> accountCoversMap = mgetAccountsCover(new String[]{accountUuid}, new boolean[]{false});
        List<Map<String, String>> accountsPhoto = accountCoversMap.get(accountUuid);

        // 2020.05.14 喜欢的类型
        Map<String, List<Map<String, String>>> accountsLikeLabelMap = accountsLikeLabelService.listAccountsLikeLabelAll(accountUuid);

        AccountSchoolDto schoolInfo = schoolService.info(accountUuid, false);
        List<AccountLabelCfg> labels = accountLabelServiceV2.listMyLabelInfo(accountUuid);
        boolean isOrigin = false; // 是否主态
        List<AccountsLife> accountsLife = accountsLifeService.getAccountsLifeOrAccountsPhoto(accountUuid, isOrigin, false);

//        double infoPercent = this.calInfoPercent(avatar, accountName, sexType, birth, datingIntention, sexual, affectivestatus, baseAddr, hometown, personalProfile
//                , voiceSignUrl, education, profession, height, weight, likeTypeLabel, personalityLabel, accountsPhoto
//                , accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.MUSIC.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey), income);
//        result.put("info_percent", infoPercent);
        double infoPercent = this.calInfoPercentV2(accountUuid, avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile
            , voiceSignUrl, education, profession, height, weight, personalityLabel
            , accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey)
            , income, schoolInfo, labels, accountsLife);
        result.put("info_percent", infoPercent);

//        LOGGER.info("保存完个人资料,用户uuid={},资料完善度={}", accountUuid, infoPercent);
        if (infoPercent == 100d) {
            // 2020.06.24 资料100% 推这个
            accountsPersonalInfoService.accountTaskFinished(accountUuid, "1");
            modifyGrowScoreService.sendModifyGrowScoreMq(accountUuid, ModifyGrowScoreEnum.INFO_HUNDRED_PERCENT);
        }
        return result;
    }

    /**
     * 卡尔信息百分比
     * 计算资料完整度 根据版本控制
     * <p>
     * 1版 12项 安卓 <7200 ios<10120 头像，相册，昵称，性别，年龄，婚恋额、性取向，交友意向，位置，家乡，个性标签，喜欢类型
     * 2版 18项 安卓 >=7200 <7213 ios >=10120 <10130 新增 语音签名，个人简介，身高，体重，职业，学历
     * 3版 18项 2020.05.15 安卓 >=7213 ios >=10130 暂定  去除 “性取向”“交友意向”“喜欢的类型”  新增 喜欢的内容-电影电视剧、喜欢的内容-明星、喜欢的内容-游戏
     * 4版 19项 2020.11.23 安卓 >=7345 ios >=10166  暂定  新增收入
     * 5版
     * 6版
     * 7版 12项 2024.12.05 大改版，有ab实验 婚恋状况、所在地、家乡、个人简介、学历、职业、身高、体重、个人标签（变更统计来源）、 收入、学校、我的生活
     *
     *
     * @param avatar           头像
     * @param accountName      用户名
     * @param sexType          性别
     * @param birth            生日
     * @param datingIntention  交友意向
     * @param sexual           性取向
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param personalProfile  个人简介
     * @param voiceSignUrl     语音签名
     * @param education        学历
     * @param profession       职业
     * @param height           身高
     * @param weight           体重
     * @param likeTypeLabel    喜欢的类型
     * @param personalityLabel 个人标签
     * @param accountsPhoto    相册图片
     * @param tvList           喜欢的内容 - 电影电视剧
     * @param musicList        喜欢的内容 - 音乐 暂无此项
     * @param starList         喜欢的内容 - 明星
     * @param gameList         喜欢的内容 - 游戏
     * @param income           收入
     * @return
     */
//    double calInfoPercent(String avatar, String accountName, Integer sexType, Long birth, Integer datingIntention, Integer sexual, Integer affectivestatus,
//                          String baseaddr, String hometown, String personalProfile, String voiceSignUrl, String education, String profession, String height,
//                          String weight, List<PersonalityLabel> likeTypeLabel, List<PersonalityLabel> personalityLabel, List<Map<String, String>> accountsPhoto,
//                          List<Map<String, String>> tvList, List<Map<String, String>> musicList, List<Map<String, String>> starList, List<Map<String, String>> gameList, Integer income) {
//        Long appVersion = RequestParams.getSoa_basic_java().getApp_version();
//        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
//
//        if (isOldVersion1(appVersion, platformId)) {
//            //判断 1版
//            return calInfoPercent1(avatar, accountName, sexType, birth, datingIntention, sexual, affectivestatus, baseaddr, hometown, likeTypeLabel, personalityLabel, accountsPhoto);
//        } else if (isOldVersion2(appVersion, platformId)) {
//            //判断 2版
//            return calInfoPercent2(avatar, accountName, sexType, birth, datingIntention, sexual, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl, education, profession, height, weight, likeTypeLabel, personalityLabel, accountsPhoto);
//        } else if (isOldVersion3(appVersion, platformId)) {
//            //判断 3版
//            return calInfoPercent3(avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl, education, profession, height, weight, personalityLabel, accountsPhoto, tvList, starList, gameList);
//        } else if (isOldVersion4(appVersion, platformId)) {
//            //判断 4版
//            return calInfoPercent4(avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl, education, profession, height, weight, personalityLabel, accountsPhoto, tvList, starList, gameList, income);
//        } else if (isOldVersion5(appVersion, platformId)){
//            //判断 5版
//            return calInfoPercent5(avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl, education, profession, height, weight, personalityLabel, tvList, starList, gameList, income);
//        }else {
//            //判断 6版
//            return calInfoPercent6(accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, education, profession, height, weight, personalityLabel, tvList, starList, gameList, income);
//        }
//
//    }

    /**
     * 卡尔信息百分比 重写方法，去掉早期传参字段，保留第五版开始数据
     * 计算资料完整度 根据版本控制
     * <p>
     * 1版 12项 安卓 <7200 ios<10120 头像，相册，昵称，性别，年龄，婚恋额、性取向，交友意向，位置，家乡，个性标签，喜欢类型
     * 2版 18项 安卓 >=7200 <7213 ios >=10120 <10130 新增 语音签名，个人简介，身高，体重，职业，学历
     * 3版 18项 2020.05.15 安卓 >=7213 ios >=10130 暂定  去除 “性取向”“交友意向”“喜欢的类型”  新增 喜欢的内容-电影电视剧、喜欢的内容-明星、喜欢的内容-游戏
     * 4版 19项 2020.11.23 安卓 >=7345 ios >=10166  暂定  新增收入
     * 5版
     * 6版
     * 7版 12项 2024.12.05 大改版，有ab实验 婚恋状况、所在地、家乡、个人简介、学历、职业、身高、体重、个人标签（变更统计来源）、 收入、学校、我的生活
     *
     * @param accountUuid
     * @param avatar
     * @param accountName
     * @param sexType
     * @param birth
     * @param affectivestatus
     * @param baseaddr
     * @param hometown
     * @param personalProfile
     * @param voiceSignUrl
     * @param education
     * @param profession
     * @param height
     * @param weight
     * @param personalityLabel
     * @param tvList
     * @param starList
     * @param gameList
     * @param income
     * @param accountsLife
     * @param labels
     * @param schoolInfo
     * @return
     */
    double calInfoPercentV2(String accountUuid, String avatar, String accountName, Integer sexType, Long birth,  Integer affectivestatus,
        String baseaddr, String hometown, String personalProfile, String voiceSignUrl, String education, String profession, String height,
        String weight, List<PersonalityLabel> personalityLabel, List<Map<String, String>> tvList, List<Map<String, String>> starList,
        List<Map<String, String>> gameList, Integer income, AccountSchoolDto schoolInfo, List<AccountLabelCfg> labels, List<AccountsLife> accountsLife) {
        Long appVersion = RequestParams.getSoa_basic_java().getApp_version();
        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();

        if (isOldVersion5(appVersion, platformId)){
            //判断 5版
            return calInfoPercent5(avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl, education, profession, height, weight, personalityLabel, tvList, starList, gameList, income);
        }else if(isOldVersion6(accountUuid)){
            //判断 6版
            return calInfoPercent6(accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, education, profession, height, weight, personalityLabel, tvList, starList, gameList, income, schoolInfo);
        }else {
            double percent;
            if (AlEvaluationVersionBarrier.pass()) {
                percent = SpringContextHolder.getBean(AccountsPersonalInfoService.class).accountCardProgress(accountUuid).multiply(BigDecimal.valueOf(100)).doubleValue();
            } else {
                percent = defaultCalInfoPercent(affectivestatus, baseaddr, hometown, personalProfile, education, profession, height, weight, income, schoolInfo, labels, accountsLife);
            }

            // 资料完成度和社区任务解耦 好难。。。
            boolean profileTaskCompleted = isProfileTaskCompleted(accountUuid, avatar, accountName, sexType, birth, affectivestatus,
                baseaddr, hometown, personalProfile, voiceSignUrl, education, profession, height,
                weight, personalityLabel, tvList, starList,
                gameList, income, schoolInfo, labels
            );
            String key = "account:profile:task:status:" + accountUuid;
            bizRedisTemplate.opsForValue().set(key, profileTaskCompleted ? "1" : "0", 2, TimeUnit.SECONDS);
            logger.info("新版profile用户{}资料卡完成情况 {} {}", accountUuid, percent, profileTaskCompleted);
            if (percent < 100d && profileTaskCompleted) {
                // 有可能资料完成度没有100%，但是社区任务进度100，需要做一下推送兜底
                accountsPersonalInfoService.accountTaskFinished(accountUuid, "1");
            }
            return percent;

        }
    }

    boolean isProfileTaskCompleted(String accountUuid, String avatar, String accountName, Integer sexType, Long birth,  Integer affectivestatus,
                            String baseaddr, String hometown, String personalProfile, String voiceSignUrl, String education, String profession, String height,
                            String weight, List<PersonalityLabel> personalityLabel, List<Map<String, String>> tvList, List<Map<String, String>> starList,
                            List<Map<String, String>> gameList, Integer income, AccountSchoolDto schoolDto, List<AccountLabelCfg> labels) {
        int finishedCount = 0;
        if (StringUtils.isNotBlank(accountName)) { //昵称
            finishedCount++;
        }
        if (sexType != null && (sexType == 1 || sexType == 2)) { //性别
            finishedCount++;
        }
        if (birth != null && birth != 0) { //出生日期
            finishedCount++;
        }
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (StringUtils.isNotBlank(education)) { //学历
            finishedCount++;
        }
        if (null != schoolDto && schoolDto.getSchoolId() != null) {    //学校
            finishedCount++;
        }
        if (StringUtils.isNotBlank(profession)) { //职业
            finishedCount++;
        }
        if (StringUtils.isNotBlank(height) && !"0".equals(height)) { //身高
            finishedCount++;
        }
        if (StringUtils.isNotBlank(weight) && !"0".equals(weight)) { //体重
            finishedCount++;
        }
        if (null != income && income > 0) {    //收入
            finishedCount++;
        }
        return finishedCount == 12;
    }


    //第1版 统计资料完整度的数量为12项
    private Boolean isOldVersion1(Long appVersion, Integer platformId) {
        boolean result = false;
        if (ANDROID_PLATFORM_ID.equals(platformId) && appVersion < DATA_ANDROID_VERSION1) {
            result = true;
        } else if ((IPHONE_PLATFORM_ID.equals(platformId) || IPAD_PLATFORM_ID.equals(platformId)) && appVersion < DATA_IOS_VERSION1) {
            result = true;
        }
        return result;
    }

    //第2版 统计资料完整度的数量为18项
    private Boolean isOldVersion2(Long appVersion, Integer platformId) {
        boolean result = false;
        if (ANDROID_PLATFORM_ID.equals(platformId) && appVersion < DATA_ANDROID_VERSION2) {
            result = true;
        } else if ((IPHONE_PLATFORM_ID.equals(platformId) || IPAD_PLATFORM_ID.equals(platformId)) && appVersion < DATA_IOS_VERSION2) {
            result = true;
        }
        return result;
    }

    //第3版 统计资料完整度的数量为19项
    private Boolean isOldVersion3(Long appVersion, Integer platformId) {
        boolean result = false;
        if (ANDROID_PLATFORM_ID.equals(platformId) && appVersion < DATA_ANDROID_VERSION3) {
            result = true;
        } else if ((IPHONE_PLATFORM_ID.equals(platformId) || IPAD_PLATFORM_ID.equals(platformId)) && appVersion < DATA_IOS_VERSION3) {
            result = true;
        }
        return result;
    }

    //第4版 统计资料完整度的数量为18项
    private Boolean isOldVersion4(Long appVersion, Integer platformId) {
        boolean result = false;
        if (ANDROID_PLATFORM_ID.equals(platformId) && appVersion < DATA_ANDROID_VERSION4) {
            result = true;
        } else if ((IPHONE_PLATFORM_ID.equals(platformId) || IPAD_PLATFORM_ID.equals(platformId)) && appVersion < DATA_IOS_VERSION4) {
            result = true;
        }
        return result;
    }

    //第5版 统计资料完整度的数量为18项
    private Boolean isOldVersion5(Long appVersion, Integer platformId){
        boolean result = false;
        if (ANDROID_PLATFORM_ID.equals(platformId) && appVersion < DATA_ANDROID_VERSION5) {
            result = true;
        } else if ((IPHONE_PLATFORM_ID.equals(platformId) || IPAD_PLATFORM_ID.equals(platformId)) && appVersion < DATA_IOS_VERSION5) {
            result = true;
        }
        return result;
    }

    //第6版 统计资料完整度的数量为16项
    private Boolean isOldVersion6(String accountUuid){
        return !profileOptimizationUiService.isProfileExp(accountUuid);
    }

    /**
     * 第1版 12项
     *
     * @param avatar           头像
     * @param accountName      用户名
     * @param sexType          性别
     * @param birth            生日
     * @param datingIntention  交友意向
     * @param sexual           性取向
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param likeTypeLabel    喜欢的类型
     * @param personalityLabel 个人标签
     * @param accountsPhoto    相册图片
     */
    private double calInfoPercent1(String avatar, String accountName, Integer sexType, Long birth, Integer datingIntention, Integer sexual, Integer affectivestatus,
                                   String baseaddr, String hometown, List<PersonalityLabel> likeTypeLabel, List<PersonalityLabel> personalityLabel, List<Map<String, String>> accountsPhoto) {
        int finishedCount = 0;
        if (StringUtils.isNotBlank(avatar)) { //头像
            finishedCount++;
        }
        if (StringUtils.isNotBlank(accountName)) { //昵称
            finishedCount++;
        }
        if (sexType != null && (sexType == 1 || sexType == 2)) { //性别
            finishedCount++;
        }
        if (birth != null && birth != 0) { //出生日期
            finishedCount++;
        }
        if (datingIntention != null && datingIntention != 0) { //交友意向
            finishedCount++;
        }
        if (sexual != null && sexual != 0) { //性取向
            finishedCount++;
        }
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(likeTypeLabel)) {  //喜欢类型
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(personalityLabel)) {  //个性标签
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(accountsPhoto)) {   //相册图片
            finishedCount++;
        }

        return NumberUtil.divDouble(finishedCount, 0.12, 0);
    }

    /**
     * 第2版 18项
     *
     * @param avatar           头像
     * @param accountName      用户名
     * @param sexType          性别
     * @param birth            生日
     * @param datingIntention  交友意向
     * @param sexual           性取向
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param personalProfile  个人简介
     * @param voiceSignUrl     语音签名
     * @param education        学历
     * @param profession       职业
     * @param height           身高
     * @param weight           体重
     * @param likeTypeLabel    喜欢的类型
     * @param personalityLabel 个人标签
     * @param accountsPhoto    相册图片
     * @return
     */
    private double calInfoPercent2(String avatar, String accountName, Integer sexType, Long birth, Integer datingIntention, Integer sexual, Integer affectivestatus,
                                   String baseaddr, String hometown, String personalProfile, String voiceSignUrl, String education, String profession, String height,
                                   String weight, List<PersonalityLabel> likeTypeLabel, List<PersonalityLabel> personalityLabel, List<Map<String, String>> accountsPhoto) {
        int finishedCount = 0;
        if (StringUtils.isNotBlank(avatar)) { //头像
            finishedCount++;
        }
        if (StringUtils.isNotBlank(accountName)) { //昵称
            finishedCount++;
        }
        if (sexType != null && (sexType == 1 || sexType == 2)) { //性别
            finishedCount++;
        }
        if (birth != null && birth != 0) { //出生日期
            finishedCount++;
        }
        if (datingIntention != null && datingIntention != 0) { //交友意向
            finishedCount++;
        }
        if (sexual != null && sexual != 0) { //性取向
            finishedCount++;
        }
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(likeTypeLabel)) {  //喜欢类型
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(personalityLabel)) {  //个性标签
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(accountsPhoto)) {   //相册图片
            finishedCount++;
        }
        if (StringUtils.isNotBlank(personalProfile)) { //个人简介
            finishedCount++;
        }
        if (StringUtils.isNotBlank(voiceSignUrl)) { //语音签名
            finishedCount++;
        }
        if (StringUtils.isNotBlank(education)) { //学历
            finishedCount++;
        }
        if (StringUtils.isNotBlank(profession)) { //职业
            finishedCount++;
        }
        if (StringUtils.isNotBlank(height) && !"0".equals(height)) { //身高
            finishedCount++;
        }
        if (StringUtils.isNotBlank(weight) && !"0".equals(weight)) { //体重
            finishedCount++;
        }
        return NumberUtil.divDouble(finishedCount, 0.18, 0);
    }

    /**
     * 第3版 18项
     *
     * @param avatar           头像
     * @param accountName      用户名
     * @param sexType          性别
     * @param birth            生日
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param personalProfile  个人简介
     * @param voiceSignUrl     语音签名
     * @param education        学历
     * @param profession       职业
     * @param height           身高
     * @param weight           体重
     * @param personalityLabel 个人标签
     * @param accountsPhoto    相册图片
     * @param tvList           喜欢的内容 - 电影电视剧
     * @param starList         喜欢的内容 - 明星
     * @param gameList         喜欢的内容 - 游戏
     * @return
     */
    private double calInfoPercent3(String avatar, String accountName, Integer sexType, Long birth, Integer affectivestatus,
                                   String baseaddr, String hometown, String personalProfile, String voiceSignUrl, String education, String profession, String height,
                                   String weight, List<PersonalityLabel> personalityLabel, List<Map<String, String>> accountsPhoto,
                                   List<Map<String, String>> tvList, List<Map<String, String>> starList, List<Map<String, String>> gameList) {
        int finishedCount = 0;
//		if(Objects.equals(RequestParams.getSoa_basic_java().getToken(), "5221e072010e0a5a2c1c672d57bea5e6")) {
//			LOGGER.info("个人资料计算={}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}",avatar,accountName,sexType,birth,affectivestatus,baseaddr,hometown,personalProfile,voiceSignUrl
//					,education,profession,height,weight,JSON.toJSON(personalityLabel),JSON.toJSON(accountsPhoto),JSON.toJSON(tvList),JSON.toJSON(starList),JSON.toJSON(gameList));
//		}

        if (StringUtils.isNotBlank(avatar)) { //头像
            finishedCount++;
        }
        if (StringUtils.isNotBlank(accountName)) { //昵称
            finishedCount++;
        }
        if (sexType != null && (sexType == 1 || sexType == 2)) { //性别
            finishedCount++;
        }
        if (birth != null && birth != 0) { //出生日期
            finishedCount++;
        }
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(personalityLabel)) {  //个性标签
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(accountsPhoto)) {   //相册图片
            finishedCount++;
        }
        if (StringUtils.isNotBlank(personalProfile)) { //个人简介
            finishedCount++;
        }
        if (StringUtils.isNotBlank(voiceSignUrl)) { //语音签名
            finishedCount++;
        }
        if (StringUtils.isNotBlank(education)) { //学历
            finishedCount++;
        }
        if (StringUtils.isNotBlank(profession)) { //职业
            finishedCount++;
        }
        if (StringUtils.isNotBlank(height) && !"0".equals(height)) { //身高
            finishedCount++;
        }
        if (StringUtils.isNotBlank(weight) && !"0".equals(weight)) { //体重
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(tvList)) {  //喜欢内容 - 电影电视剧
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(starList)) {  //喜欢内容 - 明星
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(gameList)) {  //喜欢内容 - 游戏
            finishedCount++;
        }

        return NumberUtil.divDouble(finishedCount, 0.18, 0);
    }

    /**
     * 第4版 19项
     *
     * @param avatar           头像
     * @param accountName      用户名
     * @param sexType          性别
     * @param birth            生日
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param personalProfile  个人简介
     * @param voiceSignUrl     语音签名
     * @param education        学历
     * @param profession       职业
     * @param height           身高
     * @param weight           体重
     * @param personalityLabel 个人标签
     * @param accountsPhoto    相册图片
     * @param tvList           喜欢的内容 - 电影电视剧
     * @param starList         喜欢的内容 - 明星
     * @param gameList         喜欢的内容 - 游戏
     * @return
     */
    private double calInfoPercent4(String avatar, String accountName, Integer sexType, Long birth, Integer affectivestatus,
                                   String baseaddr, String hometown, String personalProfile, String voiceSignUrl, String education, String profession, String height,
                                   String weight, List<PersonalityLabel> personalityLabel, List<Map<String, String>> accountsPhoto,
                                   List<Map<String, String>> tvList, List<Map<String, String>> starList, List<Map<String, String>> gameList, Integer income) {
        int finishedCount = 0;
//		if(Objects.equals(RequestParams.getSoa_basic_java().getToken(), "5221e072010e0a5a2c1c672d57bea5e6")) {
//			LOGGER.info("个人资料计算={}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}",avatar,accountName,sexType,birth,affectivestatus,baseaddr,hometown,personalProfile,voiceSignUrl
//					,education,profession,height,weight,JSON.toJSON(personalityLabel),JSON.toJSON(accountsPhoto),JSON.toJSON(tvList),JSON.toJSON(starList),JSON.toJSON(gameList));
//		}

        if (StringUtils.isNotBlank(avatar)) { //头像
            finishedCount++;
        }
        if (StringUtils.isNotBlank(accountName)) { //昵称
            finishedCount++;
        }
        if (sexType != null && (sexType == 1 || sexType == 2)) { //性别
            finishedCount++;
        }
        if (birth != null && birth != 0) { //出生日期
            finishedCount++;
        }
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(personalityLabel)) {  //个性标签
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(accountsPhoto)) {   //相册图片
            finishedCount++;
        }
        if (StringUtils.isNotBlank(personalProfile)) { //个人简介
            finishedCount++;
        }
        if (StringUtils.isNotBlank(voiceSignUrl)) { //语音签名
            finishedCount++;
        }
        if (StringUtils.isNotBlank(education)) { //学历
            finishedCount++;
        }
        if (StringUtils.isNotBlank(profession)) { //职业
            finishedCount++;
        }
        if (StringUtils.isNotBlank(height) && !"0".equals(height)) { //身高
            finishedCount++;
        }
        if (StringUtils.isNotBlank(weight) && !"0".equals(weight)) { //体重
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(tvList)) {  //喜欢内容 - 电影电视剧
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(starList)) {  //喜欢内容 - 明星
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(gameList)) {  //喜欢内容 - 游戏
            finishedCount++;
        }

        if (null != income && income > 0) {    //收入
            finishedCount++;
        }

        return NumberUtil.divDouble(finishedCount, 0.19, 0);
    }

    /**
     * 资料完整度11-18需求 对比 版本5 去掉了 头像和语音签名
     * 第11-18版 16项
     *
     * @param accountName      用户名
     * @param sexType          性别
     * @param birth            生日
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param personalProfile  个人简介
     * @param education        学历
     * @param profession       职业
     * @param height           身高
     * @param weight           体重
     * @param personalityLabel 个人标签
     * @param tvList           喜欢的内容 - 电影电视剧
     * @param starList         喜欢的内容 - 明星
     * @param gameList         喜欢的内容 - 游戏
     * @param income           收入
     * @return double
     */
    private double calInfoPercent6(String accountName, Integer sexType, Long birth, Integer affectivestatus,
                                   String baseaddr, String hometown, String personalProfile, String education, String profession, String height,
                                   String weight, List<PersonalityLabel> personalityLabel,
                                   List<Map<String, String>> tvList, List<Map<String, String>> starList, List<Map<String, String>> gameList, Integer income, AccountSchoolDto schoolDto) {
        int finishedCount = 0;
//        LOGGER.info("个人资料计算={}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}", avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl
//                , education, profession, height, weight, JSON.toJSON(personalityLabel), JSON.toJSON(tvList), JSON.toJSON(starList), JSON.toJSON(gameList));
        if (StringUtils.isNotBlank(accountName)) { //昵称
            finishedCount++;
        }
        if (sexType != null && (sexType == 1 || sexType == 2)) { //性别
            finishedCount++;
        }
        if (birth != null && birth != 0) { //出生日期
            finishedCount++;
        }
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(personalityLabel)) {  //个性标签
            finishedCount++;
        }
        if (StringUtils.isNotBlank(personalProfile)) { //个人简介
            finishedCount++;
        }
        if (StringUtils.isNotBlank(education)) { //学历
            finishedCount++;
        }
        if (StringUtils.isNotBlank(profession)) { //职业
            finishedCount++;
        }
        if (StringUtils.isNotBlank(height) && !"0".equals(height)) { //身高
            finishedCount++;
        }
        if (StringUtils.isNotBlank(weight) && !"0".equals(weight)) { //体重
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(tvList)) {  //喜欢内容 - 电影电视剧
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(starList)) {  //喜欢内容 - 明星
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(gameList)) {  //喜欢内容 - 游戏
            finishedCount++;
        }

        if (null != income && income > 0) {    //收入
            finishedCount++;
        }

        return NumberUtil.divDouble(finishedCount, 0.16, 0);
    }

    /**
     * https://o15vj1m4ie.feishu.cn/wiki/VFsXwMYgeixaV6kAShPcrNTXnkh
     *
     * 婚恋状况、所在地、家乡、个人简介、学历、职业、身高、体重、个人标签（变更统计来源）、 收入、学校、我的生活
     *
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param personalProfile  个人简介
     * @param education        学历
     * @param profession       职业
     * @param height           身高
     * @param weight           体重
     * @param income           收入
     * @param accountsLife     我的生活
     * @return double
     */
    private double defaultCalInfoPercent(Integer affectivestatus,
        String baseaddr, String hometown, String personalProfile, String education, String profession, String height,
        String weight,  Integer income, AccountSchoolDto schoolDto, List<AccountLabelCfg> labels, List<AccountsLife> accountsLife) {
        int finishedCount = 0;
//        LOGGER.info("个人资料计算={}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}", avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl
//                , education, profession, height, weight, JSON.toJSON(personalityLabel), JSON.toJSON(tvList), JSON.toJSON(starList), JSON.toJSON(gameList));
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (StringUtils.isNotBlank(personalProfile)) { //个人简介
            finishedCount++;
        }
        if (StringUtils.isNotBlank(education)) { //学历
            finishedCount++;
        }
        if (StringUtils.isNotBlank(profession)) { //职业
            finishedCount++;
        }
        if (StringUtils.isNotBlank(height) && !"0".equals(height)) { //身高
            finishedCount++;
        }
        if (StringUtils.isNotBlank(weight) && !"0".equals(weight)) { //体重
            finishedCount++;
        }
        if (null != income && income > 0) {    //收入
            finishedCount++;
        }
        if (null != schoolDto && schoolDto.getSchoolId() != null) {    //学校
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(labels)) {    //标签
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(accountsLife)){    //我的生活
            finishedCount++;
        }

        return NumberUtil.divDouble(finishedCount, 0.12, 0);
    }

    /**
     * 资料完整度5
     * 第5版 18项
     *
     * @param avatar           头像
     * @param accountName      用户名
     * @param sexType          性别
     * @param birth            生日
     * @param affectivestatus  婚恋状况
     * @param baseaddr         所在地
     * @param hometown         家乡
     * @param personalProfile  个人简介
     * @param voiceSignUrl     语音签名
     * @param education        学历
     * @param profession       职业
     * @param height           身高
     * @param weight           体重
     * @param personalityLabel 个人标签
     * @param tvList           喜欢的内容 - 电影电视剧
     * @param starList         喜欢的内容 - 明星
     * @param gameList         喜欢的内容 - 游戏
     * @param income           收入
     * @return double
     */
    private double calInfoPercent5(String avatar, String accountName, Integer sexType, Long birth, Integer affectivestatus,
                                   String baseaddr, String hometown, String personalProfile, String voiceSignUrl, String education, String profession, String height,
                                   String weight, List<PersonalityLabel> personalityLabel,
                                   List<Map<String, String>> tvList, List<Map<String, String>> starList, List<Map<String, String>> gameList, Integer income) {
        int finishedCount = 0;
//        LOGGER.info("个人资料计算={}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}-{}", avatar, accountName, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile, voiceSignUrl
//                , education, profession, height, weight, JSON.toJSON(personalityLabel), JSON.toJSON(tvList), JSON.toJSON(starList), JSON.toJSON(gameList));

        if (StringUtils.isNotBlank(avatar)) { //头像
            finishedCount++;
        }
        if (StringUtils.isNotBlank(accountName)) { //昵称
            finishedCount++;
        }
        if (sexType != null && (sexType == 1 || sexType == 2)) { //性别
            finishedCount++;
        }
        if (birth != null && birth != 0) { //出生日期
            finishedCount++;
        }
        if (affectivestatus != null && affectivestatus != 0) { //婚恋状况
            finishedCount++;
        }
        if (StringUtils.isNotBlank(baseaddr)) { //所在地
            finishedCount++;
        }
        if (StringUtils.isNotBlank(hometown)) { //家乡
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(personalityLabel)) {  //个性标签
            finishedCount++;
        }
        if (StringUtils.isNotBlank(personalProfile)) { //个人简介
            finishedCount++;
        }
        if (StringUtils.isNotBlank(voiceSignUrl)) { //语音签名
            finishedCount++;
        }
        if (StringUtils.isNotBlank(education)) { //学历
            finishedCount++;
        }
        if (StringUtils.isNotBlank(profession)) { //职业
            finishedCount++;
        }
        if (StringUtils.isNotBlank(height) && !"0".equals(height)) { //身高
            finishedCount++;
        }
        if (StringUtils.isNotBlank(weight) && !"0".equals(weight)) { //体重
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(tvList)) {  //喜欢内容 - 电影电视剧
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(starList)) {  //喜欢内容 - 明星
            finishedCount++;
        }
        if (CollectionUtils.isNotEmpty(gameList)) {  //喜欢内容 - 游戏
            finishedCount++;
        }

        if (null != income && income > 0) {    //收入
            finishedCount++;
        }

        return NumberUtil.divDouble(finishedCount, 0.18, 0);
    }

    public Map<String, List<Map<String, String>>> mgetAccountsCover(String[] uuids, boolean[] onlyChecks) {
        Map<String, List<Map<String, String>>> results = new HashMap<>();
        accountsPhotoService.mgetAccountCover(uuids, onlyChecks).forEach((uuid, accountsPhoto) -> {
            results.put(uuid, convertPicKey(accountsPhoto));
        });
        return results;
    }

    private List<Map<String, String>> convertPicKey(List<Map<String, String>> accountsPhoto) {
        List<Map<String, String>> resultList = new ArrayList<>();
        if (accountsPhoto != null && accountsPhoto.size() > 0) {
            for (int i = 0; i < accountsPhoto.size(); i++) {
                Map<String, String> photoMap = accountsPhoto.get(i);
                Map<String, String> map = new HashMap<String, String>();
                map.put("id", photoMap.get("id"));
                map.put("pic_url", photoMap.get("photo_url"));
                map.put("verify_status", photoMap.get("verify_status"));
                map.put("error_msg", photoMap.get("error_msg"));
                map.put("status", photoMap.get("status"));
                map.put("like_num", MapUtils.getString(photoMap, "like_num", "0"));
                resultList.add(map);
            }
        }
        return resultList;
    }


    private Map<String, List<String>> getAccountIdentity(Map<String, Object> userResultMap) {
        List<String> haveList = new ArrayList<>();
        List<String> notHaveList = new ArrayList<>();
        Object handsomeStatus = userResultMap.get(UuidInfoField.HANDSOME_STATUS);
        Object richStatus = userResultMap.get(UuidInfoField.RICH_STATUS);
        Object abilityStatus = userResultMap.get(UuidInfoField.ABILITY_STATUS);
        Object starStatus = userResultMap.get(UuidInfoField.STAR_STATUS);
        Object hostStatus = userResultMap.get(UuidInfoField.HOST_STATUS);
//		Object whip_status = userResultMap.get("whip_status");
        Object doctorStatus = userResultMap.get(UuidInfoField.DOCTOR_STATUS);

        if (handsomeStatus != null && Integer.parseInt(String.valueOf(handsomeStatus)) > 0) {
            haveList.add("1");
        } else {
            notHaveList.add("1");
        }
        if (richStatus != null && Integer.parseInt(String.valueOf(richStatus)) > 0) {
            haveList.add("2");
        } else {
            notHaveList.add("2");
        }
        if (abilityStatus != null && Integer.parseInt(String.valueOf(abilityStatus)) > 0) {
            haveList.add("3");
        } else {
            notHaveList.add("3");
        }
        if (starStatus != null && Integer.parseInt(String.valueOf(starStatus)) > 0) {
            haveList.add("4");
        } else {
            notHaveList.add("4");
        }
        if (hostStatus != null && Integer.parseInt(String.valueOf(hostStatus)) > 0) {
            haveList.add("5");
        } else {
            notHaveList.add("5");
        }
//		if(whip_status != null && Integer.parseInt(String.valueOf(whip_status)) > 0){
//			list.add("6");
//		} else {
//			notHaveList.add("6");
//		}
        if (doctorStatus != null && Integer.parseInt(String.valueOf(doctorStatus)) > 0) {
            haveList.add("7");
        } else {
            notHaveList.add("7");
        }

        Map<String, List<String>> map = Maps.newHashMap();
        map.put("have", haveList);
        map.put("notHave", notHaveList);

        return map;
    }

    /**
     * 根据昵称查询用户信息
     *
     * @param accountName
     * @return
     */
    public List<Map<String, Object>> searchByAccountName(String accountName) {

        Object[] args = new Object[]{"account", "card_id_or_nickname", accountName, "0", "200"};//返回200条数据
        try {
            SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.SEARCH);
            SoaResponse soaResponse = soaClient.call(SEARCH_SERVICE, SEARCH_METHOD, args);
            if (soaResponse.fail()) {
                logger.error("调用搜索soa根据昵称查询用户接口失败，失败原因{}", soaResponse.getMsg());
                throw new ServiceException(CodeStatus.CALL_SOA_SEARCH_RESPONSE_FAIL.value());
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult)) {
                logger.warn("调用搜索soa根据昵称查询用户接口获取到的数据为空，搜索关键字为：{}", accountName);
                return new LinkedList<>();
            }
            Map<String, Object> soaResultMap = new Gson().fromJson(soaResponse.getData(), new TypeReference<Map<String, Object>>() {
            }.getType());
            List<Map<String, String>> ls = (List<Map<String, String>>) soaResultMap.get("docs");
            List<String> accountUuids = new ArrayList<String>();
            if (ls != null && !ls.isEmpty()) {
                for (Map<String, String> uuidMap : ls) {
                    String uuid = uuidMap.get("uuid");
                    if (uuid != null && uuid.length() > 0) {
                        accountUuids.add(uuid);
                    }
                }
                return getAccountsInfoByUuidsForSearch(accountUuids);
            }
        } catch (Exception e) {
            logger.error("用户搜索，调用搜索系统根据昵称搜索用户失败", e);
        }
        return new LinkedList<>();
    }

    public List<Map<String, Object>> getAccountsInfoByUuidsForSearch(List<String> accountUuids) {
        List<String> fields = Arrays.asList("account_id", "avatar", "account_name", "mobile", "email", "account_status", "uuid", "sex_type",
                "account_type", "create_time", "last_logintime", "member_id");
        List<Map<String, Object>> accountsList = accountsService.getFieldByUuidFromDb(fields, accountUuids);
        for (Map<String, Object> map : accountsList) {
            String uuid = MapUtils.getString(map, "uuid");
            map.put("token", membersDao.getTokenById(MapUtils.getLong(map, "member_id"), false));
            map.put("invaild_time", "0");
            Integer accountLevel = accountsForumProfileDao.getAccountLevelByAccountUuid(uuid);
            map.put("account_level", accountsLevelService.findLevelNumByLevelId(accountLevel == null ? 1 : accountLevel));
            map.put("account_level_id", accountLevel == null ? 1 : accountLevel);
            map.put("third_part", accountsThirdPartyManager.getValidTypeByAccountUuid(uuid));
        }
        return accountsList;
    }

    /**
     * 个人主页信息
     * 新增版本控制 2  2021.05.24 新版本不再请求业务端获取数据
     *
     * @param accountUuid  accountUuid
     * @param version 版本控制 0, 1，2 ...
     * @return
     */
    public Map<String, Object> getAccountInfoOfMyMenu(String accountUuid, int version) {
        Map<String, Object> resultMap = new HashMap<>();

        Map<String, Object> userResultMap = getOwnProfileByUuid(accountUuid, "1");
        //TODO 用户缓存中 uuid可能为空，网关接口已校验uuid，增加日志观察
        if(StringUtils.isBlank(String.valueOf(userResultMap.get("uuid")))) {
            LOGGER.warn("用户缓存数据异常uuid为空，uuid={}", accountUuid);
        }
//        accountUuid = String.valueOf(userResultMap.get("uuid"));
        Integer avatarStatus = MapUtils.getInteger(userResultMap, "avatar_status");
        resultMap.put("avatar_status", avatarStatus);
        resultMap.put("account_actor", userResultMap.get("account_actor"));
        resultMap.put("account_uuid", accountUuid);
        resultMap.put("account_level", userResultMap.get(UuidInfoField.ACCOUNT_LEVEL));
        resultMap.put("driver_level", userResultMap.get(UuidInfoField.DRIVER_LEVEL));
        resultMap.put("kilometer", userResultMap.get(UuidInfoField.KILOMETER));
        String avatar = ToolsService.urlToUri(MapUtils.getString(userResultMap, "avatar", ""), "1");
        resultMap.put("avatar", avatar);
        // 2020.03.23 新增是否默认头像
        boolean isDefaultAvatar = AvatarHandleService.isDefAvatar(avatar);
        resultMap.put("is_default_avatar", isDefaultAvatar ? "1" : "0");
        resultMap.put("is_bind_mobile", userResultMap.get(UuidInfoField.IS_BIND_MOBILE));
        resultMap.put("is_check_mobile", userResultMap.get(UuidInfoField.IS_CHECK_MOBILE));
        String nickname = MapUtils.getString(userResultMap, "nickname");
        resultMap.put("nickname", nickname);
        resultMap.put(AccountsInfoService.getTqcoinName(version), userResultMap.get(UuidInfoField.TQCOIN));
        resultMap.put("vip_level", StringUtil.nullObjectToEmptyString(userResultMap.get(UuidInfoField.VIP_LEVEL)));
        resultMap.put("age_is_secret", MapUtils.getString(userResultMap, UuidInfoField.AGE_IS_SECRET, "0"));
        resultMap.put("age", MapUtils.getString(userResultMap, UuidInfoField.AGE, "0"));
        int datingIntention = MapUtils.getIntValue(userResultMap, "dating_intention", 0);
        resultMap.put("dating_intention", String.valueOf(datingIntention == 0 ? 1 : datingIntention));
        int sexual = MapUtils.getIntValue(userResultMap, "sexual", 0);
        resultMap.put("sexual", String.valueOf(sexual));
        int affectivestatus = MapUtils.getIntValue(userResultMap, "affectivestatus", 0);
        resultMap.put("affectivestatus", String.valueOf(affectivestatus));
        String baseaddr = MapUtils.getString(userResultMap, "baseaddr", "");
        resultMap.put("baseaddr", baseaddr);
        String hometown = MapUtils.getString(userResultMap, "hometown", "");
        resultMap.put("hometown", hometown);
        String personalProfile = MapUtils.getString(userResultMap, "personal_profile", "");
        resultMap.put("personal_profile", personalProfile);
        int sexType = MapUtils.getIntValue(userResultMap, "sex_type", 0);
        resultMap.put("sex_type", String.valueOf(sexType));
        resultMap.put("email", MapUtils.getString(userResultMap, UuidInfoField.EMAIL, ""));
        resultMap.put("mobile", StringUtil.mobileSecret(MapUtils.getString(userResultMap, UuidInfoField.MOBILE, "")));
        long birth = MapUtils.getLongValue(userResultMap, UuidInfoField.BIRTH, 0L);
        resultMap.put("birth", String.valueOf(birth));
        resultMap.put("gender_certification", MapUtils.getString(userResultMap, UuidInfoField.GENDER_CERTIFICATION, "0"));
        resultMap.put("avatar_dress_id", MapUtils.getString(userResultMap, "avatar_dress_id", ""));
        resultMap.put("identity", getAccountIdentity(userResultMap).get("have"));
        resultMap.put("is_account_label", accountsLabelService.mGetAccountLabelByType(Arrays.asList(accountUuid), 0).get(accountUuid).isEmpty() ? "0" : "1");//是否填写过标签 0:否; 1:是;
        int voiceSignCert = StringUtils.isBlank(MapUtils.getString(userResultMap, "pass_voice_sign_url")) ? 0 : 1;   //是否有通过的语音认证，0-没有，1-有
        resultMap.put("voice_sign_cert", String.valueOf(voiceSignCert));
        // 获取用户ID
        resultMap.put("default_card_id", MapUtils.getString(userResultMap, "default_card_id", ""));
        // 注册方式
        resultMap.put("reg_style", MapUtils.getString(userResultMap, "reg_style", ""));

        Integer profileVerifyStatus = MapUtils.getInteger(userResultMap, "profile_verify_status", 0);
        Integer preProfileVerifyStatus = MapUtils.getInteger(userResultMap, "pre_profile_verify_status");

        if (null != preProfileVerifyStatus && null != avatar && avatarStatus == 0) {
            profileVerifyStatus = preProfileVerifyStatus;
        }

        resultMap.put("profile_verify_status", profileVerifyStatus);

        // 21-0616 新增语音签名相关信息
        Map<String, Object> voiceSignMap = getVoiceSignInfo(userResultMap, accountUuid, accountUuid);
        resultMap.put("voice_sign_info", voiceSignMap);

        String voiceSignUrl = MapUtils.getString(userResultMap, "voice_sign_url");
        String education = MapUtils.getString(userResultMap, "education");
        String profession = MapUtils.getString(userResultMap, "profession");
        String height = MapUtils.getString(userResultMap, "height");
        String weight = MapUtils.getString(userResultMap, "weight");
        Integer income = MapUtils.getInteger(userResultMap, "income");
        Map<String, List<PersonalityLabel>> likeTypeLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(accountUuid), 1);
        Map<String, List<PersonalityLabel>> personalityLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(accountUuid), 2);
        List<PersonalityLabel> likeTypeLabel = likeTypeLabels.get(accountUuid);
        List<PersonalityLabel> personalityLabel = personalityLabels.get(accountUuid);
        Map<String, List<Map<String, String>>> accountCoversMap = mgetAccountsCover(new String[]{accountUuid}, new boolean[]{false});
        List<Map<String, String>> accountsPhoto = accountCoversMap.get(accountUuid);

        // 21-0616 新增返回身高、职业
        resultMap.put("profession", profession);
        resultMap.put("height", height);

        // 2020.05.14 喜欢的类型
        Map<String, List<Map<String, String>>> accountsLikeLabelMap = accountsLikeLabelService.listAccountsLikeLabelAll(accountUuid);

        AccountSchoolDto schoolInfo = schoolService.info(accountUuid, false);
        List<AccountLabelCfg> labels = accountLabelServiceV2.listMyLabelInfo(accountUuid);
        boolean isOrigin = false; // 是否主态
        List<AccountsLife> accountsLife = accountsLifeService.getAccountsLifeOrAccountsPhoto(accountUuid, isOrigin, false);

//        double calInfoPercent = this.calInfoPercent(avatar, nickname, sexType, birth, datingIntention, sexual, affectivestatus, baseaddr, hometown, personalProfile
//                , voiceSignUrl, education, profession, height, weight, likeTypeLabel, personalityLabel, accountsPhoto
//                , accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.MUSIC.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey), income);
        double calInfoPercent = this.calInfoPercentV2(accountUuid, avatar, nickname, sexType, birth, affectivestatus, baseaddr, hometown, personalProfile
            , voiceSignUrl, education, profession, height, weight, personalityLabel
            , accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey)
            , income, schoolInfo, labels, accountsLife);
//        LOGGER.info("保存完个人资料,用户uuid={},资料完善度={}", accountUuid, calInfoPercent);
        if (calInfoPercent == 100d) {
            // 2020.06.24 资料100% 推这个
            accountsPersonalInfoService.accountTaskFinished(accountUuid, "1");
            modifyGrowScoreService.sendModifyGrowScoreMq(accountUuid, ModifyGrowScoreEnum.INFO_HUNDRED_PERCENT);
        }

        resultMap.put("info_percent", String.valueOf((int) calInfoPercent));
        resultMap.put("achievement", userResultMap.get("achievement"));
        resultMap.put("create_time", userResultMap.get("create_time"));
        resultMap.put("face_certification", userResultMap.get("face_certification"));
        resultMap.put("real_person_certification", userResultMap.get("real_person_certification"));

        //增加用户第三方绑定信息
        resultMap.putAll(accountsThirdPartyService.getAccountsThirdPartyBindStatus(accountUuid));

        // 旧版本才用过用户系统查业务数据 2021.05.24
        if (version < 2) {
            LOGGER.info("getAccountInfoOfMyMenu 低版本");
            Map<String, Object> liveAccountInfo = SoaService.liveGetInfo(accountUuid);
            // 是否充值过 1-是 0-否 默认值0
            resultMap.put("is_recharge", MapUtils.getString(liveAccountInfo, "is_recharge", "0"));
            // 财富等级 默认值0
            resultMap.put("wealth_level", MapUtils.getString(liveAccountInfo, "level", "0"));

            Map<String, Object> accountNoble = SoaService.getAccountNoble(accountUuid);
            resultMap.put("noble_code", MapUtils.getString(accountNoble, "noble_code", ""));
            resultMap.put("gold_name", MapUtils.getString(accountNoble, "gold_name", "0"));
        }
        resultMap.put("messageBox",
                ShanLianEnum.isShanLianApp() ?
                        getMessageBoxTips(ShanLianEnum.getInstance().getAppName()) :
                        getMessageBoxTips("他趣"));
        Map<String,String> birthMap=accountsCertificationService.getAppIdentityNoBirthByUuids(Lists.newArrayList(accountUuid));
        resultMap.put("identity_no_birth",birthMap.get(accountUuid));
        return resultMap;
    }

    public Map<String, Object> getNearbyHomePageInfo(String accountUuid) {
        String[] fields = new String[]{"account_name", "height", UuidInfoField.AGE, UuidInfoField.AGE_IS_SECRET, "sex_type", "personal_profile", "personal_profile_status", "face_certification", "profile_verify_status"};
        Map<String, Map<String, Object>> map = getInfoByUuid(new String[]{accountUuid}, fields, "1", true, false);
        Map<String, Object> owmMap = getOwnProfileByUuid(accountUuid, "1");
        Map<String, Object> infoMap = map.get(accountUuid);

        Map<String, Object> resultMap = Maps.newHashMap();
        if (owmMap != null) {
            String avatar = ToolsService.urlToUri(MapUtils.getString(owmMap, "avatar", ""), "1");
            Integer avatarStatus = MapUtils.getInteger(owmMap, "avatar_status");
            Integer profileVerifyStatus = MapUtils.getInteger(owmMap, "profile_verify_status", 0);
            Integer preProfileVerifyStatus = MapUtils.getInteger(owmMap, "pre_profile_verify_status");
            if (null != preProfileVerifyStatus && null != avatar && avatarStatus == 0) {
                profileVerifyStatus = preProfileVerifyStatus;
            }
            resultMap.put("avatar", avatar);
            resultMap.put("profile_verify_status", profileVerifyStatus);
        }
        if (infoMap != null) {
            resultMap.put("nickname", MapUtils.getString(infoMap, "account_name"));
            resultMap.put("height", MapUtils.getString(infoMap, "height"));
            resultMap.put("age", MapUtils.getString(infoMap, UuidInfoField.AGE));
            resultMap.put("sex_type", MapUtils.getString(infoMap, "sex_type"));
            resultMap.put("personal_profile", MapUtils.getString(infoMap, "personal_profile"));
            resultMap.put("face_certification", MapUtils.getString(infoMap, "face_certification"));

            Map<String, List<Map<String, String>>> accountCoversMap = mgetAccountsCover(new String[]{accountUuid}, new boolean[]{false});
            List<Map<String, String>> accountsPhoto = accountCoversMap.get(accountUuid);//getAccountsCover(uuid, !Objects.equals(operUuid, uuid));
            resultMap.put("img_list", accountsPhoto); //封面（头像数组）
        }
        String sexType = "2".equals(resultMap.get("sex_type")) ? "female" : "male";
        Map<String, String> dataCard = dataCardMap.get(sexType);
        if (dataCard != null) {
            resultMap.put("title", MapUtils.getString(dataCard, "title"));
            resultMap.put("subtitle", MapUtils.getString(dataCard, "subtitle"));
            resultMap.put("relation", MapUtils.getString(dataCard, "relation"));
        }
        return resultMap;
    }

    /**
     * 特殊处理客户端上报的错误图片地址
     * https://avatar01.jiaoliuqu.com/taqu_android_post_01584415982934.jpg_style.1024  taqu_android_post_01583567863200.jpg_style.480
     *
     * @param profileUrl
     * @return
     * <AUTHOR>
     * @date 2020/03/17 11:55
     */
    public static String fixUrl(String profileUrl) {
        if (StringUtils.isBlank(profileUrl)) {
            return profileUrl;
        }
        profileUrl = AvatarHandleService.removeDoubleDomain(profileUrl);
        for (String picSuffix : PIC_SUFFIX_LIST) {
            if (profileUrl.endsWith(picSuffix)) {
                return profileUrl;
            }
        }
        // 处理路径
        if (-1 == profileUrl.lastIndexOf(".")) {
            return profileUrl;
        }
        String fixUrl = profileUrl.substring(0, profileUrl.lastIndexOf("."));
        if (fixUrl.endsWith(PIC_SUFFIX_STYLE)) {
            fixUrl = fixUrl.substring(0, fixUrl.lastIndexOf("_"));
            return fixUrl;
        } else {
            return profileUrl;
        }
    }

    /**
     * 获取灵魂契合度信息
     *
     * @param ownUuid 自己的uuid
     * @param uuid    对方的uuid
     * @return
     */
    public Map<String, Object> getSoulFitInfo(String ownUuid, String uuid) {
        Map<String, Object> result = Maps.newHashMap();
        String[] fields = new String[]{"city", UuidInfoField.CONSTELLATION, UuidInfoField.BIRTH};
        Map<String, Map<String, Object>> map = getInfoByUuid(new String[]{ownUuid, uuid}, fields, "1", true, false);
        Map<String, Object> ownInfoMap = map.get(ownUuid);
        Map<String, Object> infoMap = map.get(uuid);

        Map<String, List<PersonalityLabel>> likeTypeLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(ownUuid, uuid), 1);
        Map<String, List<PersonalityLabel>> personalityLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(ownUuid, uuid), 2);
        Map<String, Map<String, List<Map<String, String>>>> userHobbyLabels = accountsLabelService.mGetTypeLabelByUuid(Arrays.asList(ownUuid, uuid), 3);

        // 2020.06.01 喜欢的内容类型
        Map<String, Map<String, List<Map<String, String>>>> accountsLikeLabelAll = accountsLikeLabelService.listAccountsLikeLabelAllBatch(new String[]{ownUuid, uuid});

        //自己的基本信息
        String ownCity = MapUtils.getString(ownInfoMap, "city");
        String ownConstellation = MapUtils.getString(ownInfoMap, UuidInfoField.CONSTELLATION);
        String ownBirth = MapUtils.getString(ownInfoMap, UuidInfoField.BIRTH);
        List<PersonalityLabel> ownlikeTypeLabel = likeTypeLabels.get(ownUuid);
        List<PersonalityLabel> ownpersonalityLabel = personalityLabels.get(ownUuid);
        List<Map<String, String>> ownHobbyList = userHobbyLabels.get(ownUuid).get("hobby_label");
        Map<String, List<Map<String, String>>> ownlikeLabel = accountsLikeLabelAll.get(ownUuid);

        //对方的基本信息
        String city = MapUtils.getString(infoMap, "city");
        String constellation = MapUtils.getString(infoMap, UuidInfoField.CONSTELLATION);
        String birth = MapUtils.getString(infoMap, UuidInfoField.BIRTH);
        List<PersonalityLabel> likeTypeLabel = likeTypeLabels.get(uuid);
        List<PersonalityLabel> personalityLabel = personalityLabels.get(uuid);
        List<Map<String, String>> hobbyMapList = userHobbyLabels.get(uuid).get("hobby_label");
        Map<String, List<Map<String, String>>> likeLabel = accountsLikeLabelAll.get(uuid);

        SoulFitInfo soulFitInfo = null;
        if (StringUtils.isNotEmpty(ownConstellation) && StringUtils.isNotEmpty(constellation)) {
            soulFitInfo = getSoulFitInfos(ownConstellation, constellation);
        }
        Integer soulFitType = getSoulFitType(soulFitInfo, ownlikeTypeLabel, ownpersonalityLabel, ownlikeLabel);
        Integer hasBirthday = getHasBirthday(soulFitInfo, ownBirth, birth);
        String likeDesc = getLikeDescribe(ownlikeTypeLabel, ownpersonalityLabel, likeTypeLabel, personalityLabel);
        String cityDoc = getCityDoc(ownCity, city);
        List<String> sameInterests = getInterestDoc(ownHobbyList, hobbyMapList);
        String interestDesc = "";
        if (CollectionUtils.isNotEmpty(sameInterests)) {
            interestDesc = Joiner.on("、").join(sameInterests);
        }

        // 计算喜欢内容相同项数量
        int sameLikeLabel = getSameLikeLabelList(ownlikeLabel, likeLabel);

//		LOGGER.info("获取用户星座数据,{}-{}-{}",ownConstellation,constellation, JSON.toJSON(soulFitInfo));
        //返回数据
        result.put("soul_fit_type", String.valueOf(soulFitType));  //1-有契合度且资料都填写了,2-有契合度数据，“个性标签”、“喜欢类型”至少一个未填写，底部显示完善资料引导,0-无契合度
        result.put("has_birthday", String.valueOf(hasBirthday));   //1-无契合度数据，自己的出生日期未填写  2-无契合度数据，自己的出生日期已填写，对方未填写
        if (null != soulFitInfo) {
            Integer lastIntegrateGree = getLastIntegraeDegree(soulFitInfo.getIntegrateGree(), likeDesc, ownCity.equals(city), sameInterests.size(), sameLikeLabel);
            result.put("integrating_degree", String.valueOf(lastIntegrateGree));  //契合度
            result.put("tacit_understanding", String.valueOf(soulFitInfo.getTacitIntegrate()));  //默契
            result.put("intimacy", String.valueOf(soulFitInfo.getIntimacy()));  //亲密
            result.put("passion", String.valueOf(soulFitInfo.getPassion()));  //激情
            result.put("matching_describe", soulFitInfo.getDocument());  //匹配文案
        } else {
            result.put("integrating_degree", "");  //契合度
            result.put("tacit_understanding", "");  //默契
            result.put("intimacy", "");  //亲密
            result.put("passion", "");  //激情
            result.put("matching_describe", "");  //匹配文案
        }
        result.put("like_describe", likeDesc);  //喜欢的类型描述：你是TA喜欢的类型
        result.put("city_describe", cityDoc);  //同城文案描述：你们来自同个城市：厦门
        result.put("interest_describe", interestDesc);  //共同爱好

        return result;
    }

    private int getSameLikeLabelList(Map<String, List<Map<String, String>>> ownlikeLabel,
                                     Map<String, List<Map<String, String>>> likeLabel) {
        int same = 0;
        same += getSameLikeLabel(ownlikeLabel.get(LikeLabel.Type.TV.mapKey), likeLabel.get(LikeLabel.Type.TV.mapKey));
        same += getSameLikeLabel(ownlikeLabel.get(LikeLabel.Type.MUSIC.mapKey), likeLabel.get(LikeLabel.Type.MUSIC.mapKey));
        same += getSameLikeLabel(ownlikeLabel.get(LikeLabel.Type.STAR.mapKey), likeLabel.get(LikeLabel.Type.STAR.mapKey));
        same += getSameLikeLabel(ownlikeLabel.get(LikeLabel.Type.GAME.mapKey), likeLabel.get(LikeLabel.Type.GAME.mapKey));

        return same;
    }

    private int getSameLikeLabel(List<Map<String, String>> ownlikeLabel,
                                 List<Map<String, String>> likeLabel) {
        int same = 0;
        if (CollectionUtils.isEmpty(ownlikeLabel) || CollectionUtils.isEmpty(likeLabel)) {
            return same;
        }
        for (Map<String, String> ownMap : ownlikeLabel) {
            for (Map<String, String> map : likeLabel) {
                String ownValue = MapUtils.getString(ownMap, "id", "");
                String value = MapUtils.getString(map, "id", "");
                if (StringUtils.isNotBlank(ownValue) && StringUtils.isNotBlank(value) && Objects.equals(ownValue, value)) {
                    same++;
                }
            }
        }
        return same;
    }

    /**
     * 0-无契合度,1-有契合度且资料都填写了,2-有契合度数据，“个性标签”、“喜欢类型”至少一个未填写，底部显示完善资料引导
     *
     * @param soulFitInfo
     * @param ownlikeTypeLabel
     * @param ownpersonalityLabel
     * @param ownlikeLabel
     * @return
     */
    private Integer getSoulFitType(SoulFitInfo soulFitInfo, List<PersonalityLabel> ownlikeTypeLabel, List<PersonalityLabel> ownpersonalityLabel, Map<String, List<Map<String, String>>> ownlikeLabel) {
        Long appVersion = RequestParams.getSoa_basic_java().getApp_version();
        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
        // 2020.06.01 加上版本控制
        if (isOldVersion2(appVersion, platformId)) {
            if (null == soulFitInfo) {
                return 0;
            }
            if (CollectionUtils.isEmpty(ownlikeTypeLabel) || CollectionUtils.isEmpty(ownpersonalityLabel)) {
                return 2;
            }
            return 1;

        } else {
            if (null == soulFitInfo) {
                return 0;
            }

            boolean ownlikeLabelFlag = true;
            if (!CollectionUtils.isEmpty(ownlikeLabel.get(LikeLabel.Type.TV.mapKey)) || !CollectionUtils.isEmpty(ownlikeLabel.get(LikeLabel.Type.MUSIC.mapKey))
                    || !CollectionUtils.isEmpty(ownlikeLabel.get(LikeLabel.Type.STAR.mapKey)) || !CollectionUtils.isEmpty(ownlikeLabel.get(LikeLabel.Type.GAME.mapKey))) {
                ownlikeLabelFlag = false;
            }

            if (ownlikeLabelFlag) {
                return 2;
            }
            return 1;
        }

    }


    /**
     * 1-无契合度数据，自己的出生日期未填写  2-无契合度数据，自己的出生日期已填写，对方未填写
     *
     * @return
     */
    private Integer getHasBirthday(SoulFitInfo soulFitInfo, String ownBirthday, String birthday) {
        if (null == soulFitInfo && StringUtils.isEmpty(ownBirthday)) {
            return 1;
        }
        if (null == soulFitInfo && StringUtils.isNotEmpty(ownBirthday) && StringUtils.isEmpty(birthday)) {
            return 2;
        }
        return null;
    }

    /**
     * 根据星座获取两个的灵魂契合度信息
     *
     * @param ownConstellation
     * @param constellation
     * @return
     */
    private SoulFitInfo getSoulFitInfos(String ownConstellation, String constellation) {
        String constellations1 = Joiner.on("_").join(ownConstellation, constellation);
        if (null != soulFitInfoMap.get(constellations1)) {
            return soulFitInfoMap.get(constellations1);
        }

        String constellations2 = Joiner.on("_").join(constellation, ownConstellation);
        if (null != soulFitInfoMap.get(constellations2)) {
            return soulFitInfoMap.get(constellations2);
        }
//		LOGGER.info("星座匹配,{}-{},星座map={}",constellations1,constellations2,JSON.toJSON(soulFitInfoMap));

        return null;
    }

    /**
     * 根据喜欢的类型和标签信息返回对应的文案
     *
     * @param ownlikeTypeLabel
     * @param ownpersonalityLabel
     * @param likeTypeLabel
     * @param personalityLabel
     * @return
     */
    private String getLikeDescribe(List<PersonalityLabel> ownlikeTypeLabel, List<PersonalityLabel> ownpersonalityLabel, List<PersonalityLabel> likeTypeLabel, List<PersonalityLabel> personalityLabel) {
        Map<String, String> ownlikeLabelMap = ownlikeTypeLabel.stream().filter(StreamUtil.distinctByKey(s -> s.getContent())).collect(Collectors.toMap(PersonalityLabel::getContent, PersonalityLabel::getContent));
        //Map<String,String> ownpersonalityLabelMap = ownpersonalityLabel.stream().collect(Collectors.toMap(PersonalityLabel::getContent,PersonalityLabel::getContent));
        Map<String, String> likeLabelMap = likeTypeLabel.stream().filter(StreamUtil.distinctByKey(s -> s.getContent())).collect(Collectors.toMap(PersonalityLabel::getContent, PersonalityLabel::getContent));
        //Map<String,String> personalityLabelMap = personalityLabel.stream().collect(Collectors.toMap(PersonalityLabel::getContent,PersonalityLabel::getContent));

        String result = "";

        for (PersonalityLabel pl1 : ownpersonalityLabel) {
            if (null != likeLabelMap.get(pl1.getContent())) {
                result = LIKE_DESC_DOC1;
                break;
            }
        }

        for (PersonalityLabel pl1 : personalityLabel) {
            if (null != ownlikeLabelMap.get(pl1.getContent())) {
                if (StringUtils.isNotEmpty(result)) {
                    result = LIKE_DESC_DOC3;
                } else {
                    result = LIKE_DESC_DOC2;
                }
                break;
            }
        }
        return result;
    }

    private String getCityDoc(String ownCity, String city) {
        if (ownCity.equals(city)) {
            return ownCity;
        }
        return "";
    }

    /**
     * 获取共同的爱好
     *
     * @param ownHobbyList
     * @param hobbyMapList
     * @return
     */
    private List<String> getInterestDoc(List<Map<String, String>> ownHobbyList, List<Map<String, String>> hobbyMapList) {
        List<String> sameInterests = Lists.newArrayList();
        int sameInterestsCount = 0;
        if (CollectionUtils.isNotEmpty(ownHobbyList) && CollectionUtils.isNotEmpty(hobbyMapList)) {
            Map<String, String> hobbyMap = Maps.newHashMap();
            for (Map<String, String> map : hobbyMapList) {
                hobbyMap.put(map.get("name"), map.get("name"));
            }
            for (Map<String, String> ownHobbyMap : ownHobbyList) {
                if (null != hobbyMap.get(ownHobbyMap.get("name"))) {
                    if (sameInterestsCount >= 10) {
                        break;
                    }
                    sameInterests.add(ownHobbyMap.get("name"));
                }
            }
        }
        return sameInterests;
    }

    /**
     * 获取最终的契合度
     *
     * @param likeDoc
     * @param sameCityFlag
     * @param sameInterestsCount
     * @param sameLikeLabel
     * @return
     */
    private Integer getLastIntegraeDegree(Integer currentScore, String likeDoc, Boolean sameCityFlag, Integer sameInterestsCount, int sameLikeLabel) {
        Long appVersion = RequestParams.getSoa_basic_java().getApp_version();
        Integer platformId = RequestParams.getSoa_basic_java().getPlatform_id();
        // 2020.06.01 加上版本控制
        if (isOldVersion2(appVersion, platformId)) {
            if (LIKE_DESC_DOC1.equals(likeDoc) || LIKE_DESC_DOC2.equals(likeDoc)) {
                currentScore++;
            } else if (LIKE_DESC_DOC3.equals(likeDoc)) {
                currentScore = currentScore + 2;
            }

            if (sameCityFlag) {
                currentScore = currentScore + 2;
            }

            if (sameInterestsCount >= 5) {
                currentScore = currentScore + 5;
            } else {
                currentScore = currentScore + sameInterestsCount;
            }

            if (currentScore >= 99) {
                return 99;
            }
            return currentScore;
        } else {
            currentScore += sameLikeLabel;

            if (sameCityFlag) {
                currentScore = currentScore + 2;
            }

            if (sameInterestsCount >= 5) {
                currentScore = currentScore + 5;
            } else {
                currentScore = currentScore + sameInterestsCount;
            }

            if (currentScore >= 99) {
                return 99;
            }
            return currentScore;
        }
    }

    public void reportLocationInfo(String accountUuid, Integer enableLocation, String baseaddr) {
        AccountsInfo accountsInfo = accountsInfoDao.getByAccountUuid(accountUuid);
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();
        if (null != accountsInfo && StringUtils.isNotEmpty(baseaddr) && null != enableLocation && 1 == enableLocation) {
//            LOGGER.info("测试日志 - uuid={}, baseaddr={}.", accountUuid, baseaddr);
            if (baseaddr.indexOf("null") != -1) {
                LOGGER.warn("客户端传入baseaddr字段数据错误，accountUuid={}，baseaddr={}", accountUuid, baseaddr);
            } else {
                // baseaddr客户端传入参数可能有误，需要进行处理
                baseaddr = regionService.correctBaseaddr(baseaddr);
                accountsInfo.setBaseaddr(baseaddr);
                _this().merge(accountsInfo);
                hashValue.put("baseaddr", baseaddr);
            }
        }
        String longitude = RequestParams.getSoa_basic_java().getLongitude();
        String latitude = RequestParams.getSoa_basic_java().getLatitude();
        String ip = RequestParams.getSoa_basic_java().getIp();
        if (NumberUtils.isCreatable(longitude) && NumberUtils.isCreatable(latitude)) {
            hashValue.put("longitude", longitude);
            hashValue.put("latitude", latitude);
        }
        hashValue.put("enable_location", String.valueOf(enableLocation));
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

        try {
            Long cityId = null;
            // 保存用户位置信息
            if(StringUtils.isNotBlank(baseaddr)){
                String[] baseaddrSplit = baseaddr.split(",");
                try{
                    cityId = Long.parseLong(baseaddrSplit[baseaddrSplit.length - 1]);
                }catch (Exception e){
                    logger.warn("reportLocationInfo 参数 baseaddr错误, baseaddr=[{}]", baseaddr);
                }
            }
            accountsLocationInfoService.saveAccountLocationInfo(accountUuid, ip, longitude, latitude, cityId);
        }catch (Exception e){
            LOGGER.warn("保存账号位置信息失败", e);
        }
    }

    /**
     * 下发上报给gio的数据
     *
     * @param accountUuid
     * @return
     */
    public Map<String, Object> reportAccountInfo(String accountUuid, int versionNumber) {
        Map<String, Object> result = Maps.newHashMap();
        // 活跃用户模糊图片数据处理
        Map<String, Object> myInfoMap = this.getInfoByUuid(new String[]{accountUuid}, new String[]{"create_time"}, "1", false, false).get(accountUuid);
        Long createTime = MapUtils.getLong(myInfoMap, "create_time", 0L);

        if(SWITCH_RUN_SCRIPT_ACCOUNT_AVATAR == 1 && createTime < ACCOUNT_AVATAR_START_TIME){
            Object avatarQuality = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "avatar_script_quality");
            if(avatarQuality == null || !avatarQuality.toString().equals("1")){
                if(!accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_SCRIPT_RUN_AVATAR_SCRIPT_COMMIT.setArg(accountUuid))){
                    LOGGER.info("头像质量重新检测.uuid={},createTime={}.startTime={}", accountUuid, createTime, ACCOUNT_AVATAR_START_TIME);
                    // 推送到头像审核队列
                    BuryService.pushToAvatarScript(accountUuid);
                    accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.ACCOUNT_SCRIPT_RUN_AVATAR_SCRIPT_COMMIT.setArg(accountUuid), "1", 5L, TimeUnit.DAYS);
                }
            }
        }
        if(SWITCH_RUN_SCRIPT_ACCOUNT_COVER == 1 && createTime < ACCOUNT_COVER_START_TIME){
            Object coverQuality = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_SCRIPT_RUN.setArg(accountUuid), "cover_script_quality");
            if(coverQuality == null || !coverQuality.toString().equals("1")){
                if(!accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_SCRIPT_RUN_COVER_SCRIPT_COMMIT.setArg(accountUuid))){
                    LOGGER.info("相册质量重新检测.uuid={},createTime={}.startTime={}", accountUuid, createTime, ACCOUNT_COVER_START_TIME);
                    // 推送到头像相册队列
                    BuryService.pushToCoverScript(accountUuid);
                    accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.ACCOUNT_SCRIPT_RUN_COVER_SCRIPT_COMMIT.setArg(accountUuid), "1", 5L, TimeUnit.DAYS);
                }
            }
        }

        if (versionNumber < 1) {
            // 从live_api获取数据
            Map<String, Object> accountInfoLive = SoaService.getInfoForJava(accountUuid);
            // 福布斯等级
            result.put("forbes_code", MapUtils.getString(accountInfoLive, "forbes_code", ""));
            //财富等级
            result.put("wealth_grade", MapUtils.getString(accountInfoLive, "wealth_level", "0"));
            // 是否红娘
            result.put("hongniang_if", MapUtils.getString(accountInfoLive, "is_matchmaker", "0"));

            //家族uuid
            String accountFamily = SoaService.getAccountFamily(accountUuid);
            result.put("family_id", accountFamily);
            String isInFamily = StringUtils.isBlank(accountFamily) ? "0" : "1";
            //该用户是否有家族，0-没有家族，1-有家族
            result.put("family_if", isInFamily);
            //上报用户是否是主播、房主、聊主等角色
            Integer typeOfHost = UserRoleService.getTypeOfHost(accountUuid);
            Integer typeOfChat = UserRoleService.getTypeOfChat(accountUuid);
//        Integer typeOfCaller = UserRoleService.getTypeOfCaller(accountUuid);
            // 从bbs获取数据
            Map<String, Object> accountConfig = SoaService.getAccountConfig(accountUuid);
            result.put("invite_if", MapUtils.getString(accountConfig, "invite_if", ""));
            result.put("invite_level", MapUtils.getString(accountConfig, "invite_level", ""));
            result.put("charm", MapUtils.getString(accountConfig, "charm_level", ""));
            result.put("withdrawal_if", MapUtils.getString(accountConfig, "withdrawal_if", ""));
            Integer typeOfCaller = MapUtils.getInteger(accountConfig, "is_caller", 0);
            result.put("anchor_if", typeOfHost);
            result.put("chathost_if", StringUtil.nullNumberToEmptyString(typeOfCaller));
            result.put("househost_if", typeOfChat);

            // 返回用户角色名称，旧版本上报使用
//            result.put("tel_open_if", SoaService.isOpenCallTel(accountUuid) ? "是" : "否");
            result.put("tel_open_if", "否");

            // 获取聊主公会信息
            Map<String, Object> callerConsortia = SoaService.getCallerConsortia(accountUuid);
            result.put("consortia_title", MapUtils.getString(callerConsortia, "consortia_title", ""));
        }

        //上报用户是否开启免打扰
//        Map<String, Integer> pushConfigMap = SoaService.getUserPushConfig(Lists.newArrayList(accountUuid));
//        result.put("push_if", pushConfigMap.get(accountUuid));

        //上报用户收入，职业，是否填写身高，是否填写体重，年龄段，学历，是否填写内心独白
        Map<String, Object> infoMap = singleGetInfo(
                accountUuid,
                new String[]{"income", "profession", "height", "weight", UuidInfoField.AGE, "education", "pass_personal_profile", "hometown"}
        );
        result.put("occupation", MapUtils.getString(infoMap, "profession", ""));
        Integer income = MapUtils.getInteger(infoMap, "income", 0);
        result.put("income", IncomeEnum.getNameByCode(income));
        Integer height = MapUtils.getInteger(infoMap, "height", 0);
        result.put("height_if", Objects.equals(0, height) ? 0 : 1);
        Integer weight = MapUtils.getInteger(infoMap, "weight", 0);
        result.put("weight_if", Objects.equals(0, weight) ? 0 : 1);
        Integer age = MapUtils.getInteger(infoMap, UuidInfoField.AGE);
        Optional.ofNullable(getAgeGroup(age))
                .ifPresent(ageGroup -> result.put("age_group", ageGroup));
        String education = MapUtils.getString(infoMap, "education", "");
        result.put("user_education", education);
        String passPersonalProfile = MapUtils.getString(infoMap, "pass_personal_profile");
        result.put("signature_if", StringUtils.isBlank(passPersonalProfile) ? 0 : 1);

        // 是否推广量    判断活跃用户是来自推广买量或是自然量，若是推广买量则 extension_if=1，否则extension_if=0
        result.put("extension_if", accountsRegAttributionService.existsByAccountUuid(accountUuid) ? 1 : 0);

        // 审核通过的相册照片数
        int photoPassNum = 0;
        List<Map<String, String>> list = accountsPhotoService.mGetAccountsPhoto(StringUtils.split(accountUuid)).get(accountUuid);
        photoPassNum += list.stream().filter(map -> !"1".equals(map.get("seq"))).count();
        result.put("picture_if", photoPassNum);

        // 是否有审核通过 的标签（自我描述+兴趣爱好）
        int labelNum = accountsLabelService.mGetAccountLabelByUuid(Collections.singletonList(accountUuid), 2).get(accountUuid).get("account_label").size();
        result.put("label_if", Objects.equals(0, labelNum) ? 0 : 1);

        //是否有喜欢的内容，包含影视作品、游戏、明星（音乐暂无）
        Map<String, List<Map<String, String>>> likeLabel = accountsLikeLabelService.listAccountsLikeLabelAll(accountUuid);
        int likeLabelIf = 0;
        for (List<Map<String, String>> value : likeLabel.values()) {
            if (value.size() > 0) {
                likeLabelIf = 1;
                break;
            }
        }
        result.put("like_label_if", likeLabelIf);

        int hometownIf = StringUtils.isNotBlank(infoMap.get("hometown").toString()) && !",".equals(infoMap.get("hometown")) ? 1 : 0;
        result.put("hometown_if", hometownIf);

        // 根据uuid查询用户已审核通过的相册数，落地到account_info表中
        _this().updateAccountPhotoNumber(Collections.singleton(accountUuid), "redis");

        return result;
    }

    /**
     * 根据"年龄"，获取"年龄段"
     *
     * @param age
     * @return
     */
    public String getAgeGroup(Integer age) {
        if (Objects.isNull(age)) {
            return null;
        }
        if (age > 60) {
            return ">60";
        } else if (age >= 51) {
            return "51-60";
        } else if (age >= 41) {
            return "41-50";
        } else if (age >= 36) {
            return "36-40";
        } else if (age >= 31) {
            return "31-35";
        } else if (age >= 25) {
            return "25-30";
        } else if (age >= 18) {
            return "18-24";
        }
        return null;
    }

    /**
     * getInfoByUuid 查单个用户
     *
     * @param accountUuid
     * @param fields
     * @return
     */
    public Map<String, Object> singleGetInfo(String accountUuid, String[] fields) {
        Map<String, Map<String, Object>> map = getInfoByUuid(new String[]{accountUuid}, fields, "1", true, false);
        return map.get(accountUuid);
    }

    public Map<String, Object> getUserInfo(String accountUuid, String... fields) {
        return singleGetInfo(accountUuid, fields);
    }

    @Transactional
    public void updateRealPhotoCertByUuid(String accountUuid, Integer status) {
        accountsInfoDao.updateRealPhotoCertByUuid(accountUuid, status);
    }

    public void reportUserCommonInfo(String accountUuid, String appInfos) {
        thirdAppInfoRecordService.addOrUpdate(SoaBaseParams.fromThread().getToken(), appInfos);
    }


    /**
     * 更新account_info表的账户照片数量
     *
     * @param accountUuidSet 账户组uuid
     * @param type           类型（gio上报走缓存，其它走库）
     */
    @Transactional
    public void updateAccountPhotoNumber(Set<String> accountUuidSet, String type) {
        for (String uuid : accountUuidSet) {
            int photoNumber = 0;
            if ("db".equals(type)) {
                photoNumber = accountsPhotoDao.getCountByStatus(uuid, 2, false);
            } else {
                List<Map<String, String>> list = accountsPhotoService.mGetAccountsPhoto(StringUtils.split(uuid), new boolean[]{true}).get(uuid);
                if (list != null) {
                    photoNumber += list.stream().filter(map -> map != null && !"1".equals(map.get("seq"))).count();
                }
            }
//            accountsInfoDao.updateAccountPhotoNumber(uuid, photoNumber);
            // 根据id更新
            AccountsInfo accountsInfo = accountsInfoDao.getByAccountUuid(uuid);
            if(accountsInfo != null){
                accountsInfoDao.updateAccountPhotoNumberById(accountsInfo.getId(), photoNumber);
            }
        }
    }

    /**
     * 从缓存中获取性别，用的比较多
     *
     * @param accountUuid
     * @return
     */
    public Integer getSexTypeByAccountUuidRedis(String accountUuid){
        Map<String, Map<String, Object>> usersResultMap = getInfoByUuid(new String[]{accountUuid}, new String[]{"sex_type"}, "1", true,true);
        return MapUtils.getIntValue(usersResultMap.get(accountUuid),"sex_type", 0);
    }

    /**
     * 从缓存中获取昵称
     *
     * @param accountUuid
     * @return
     */
    public String getAccountNameByAccountUuidRedis(String accountUuid){
        Map<String, Map<String, Object>> usersResultMap = getInfoByUuid(new String[]{accountUuid}, new String[]{"account_name"}, "1", true,true);
        return MapUtils.getString(usersResultMap.get(accountUuid), "account_name", "");
    }

    /**
     * 批量获取用户性别
     * @param accountUuids
     * @return
     */
    public Map<String, String> getSexTypesByAccountUuidRedis(List<String> accountUuids) {
        Map<String, String> sexTypes = new HashMap<>();
        for (String accountUuid : accountUuids) {
            sexTypes.put(accountUuid, getSexTypeByAccountUuidRedis(accountUuid) + "");
        }
        return sexTypes;
    }

    /**
     * 获取用户活体状态
     * @param accountUuid
     * @return
     */
    public String getFaceCertification(String accountUuid){
        Map<String, Object> infoMap = getInfoByUuid(new String[]{accountUuid}, new String[]{UuidInfoField.FACE_CERTIFICATION}, "1", false, false).get(accountUuid);
        return MapUtils.getString(infoMap, UuidInfoField.FACE_CERTIFICATION, "");
    }

    /**
     * 获取用户真人状态
     * @param accountUuid
     * @return
     */
    public String getRealPersonCertification(String accountUuid){
        Map<String, Object> infoMap = getInfoByUuid(new String[]{accountUuid}, new String[]{"real_person_certification"}, "1", false, false).get(accountUuid);
        return MapUtils.getString(infoMap, "real_person_certification", "2");
    }

    /**
     * 是否真人认证（只要活体通过，且完成一次人审流程，与头像当前审核状态无关）
     * @param accountUuid
     * @return
     */
    public Boolean isRealPersonCertification(String accountUuid){
        return Objects.equals("1", getRealPersonCertification(accountUuid));
    }


    /**
     * 是否活体
     * @param accountUuid
     * @return
     */
    public Boolean isFaceCertification(String accountUuid){
        Map<String, Object> infoMap = getInfoByUuid(new String[]{accountUuid}, new String[]{"face_certification"}, "1", false, false).get(accountUuid);
        return MapUtils.getString(infoMap, "face_certification", "").equals(CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
    }

    /**
     * 是否实名认证
     */
    public Boolean isZhimaCertification(String accountUuid) {
        Map<String, Object> infoMap = getInfoByUuid(new String[]{accountUuid}, new String[]{UuidInfoField.ZHIMA_CERTIFICATION}, "1", false, false).get(accountUuid);
        return MapUtils.getString(infoMap, UuidInfoField.ZHIMA_CERTIFICATION, "").equals(CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());
    }
    /**
     * 获取头像是否与活体一致
     * @param accountUuid
     * @return
     */
    public Boolean isAvatarSameWithFace(String accountUuid) {
        Map<String, Object> infoMap = getInfoByUuid(new String[]{accountUuid}, new String[]{"avatar_same_with_face"}, "1", false, false).get(accountUuid);
        String avatarSameWithFace = MapUtils.getString(infoMap, "avatar_same_with_face", "0");
        LOGGER.info("获取头像是否与活体一致.uuid={}.result={}", accountUuid, avatarSameWithFace);
        return avatarSameWithFace.equals("1");
    }

    /**
     * 获取个人简介
     * @param accountUuid
     * @return
     */
    public Map<String, String> getPersonalProfile(String accountUuid) {
        Map<String, String> result = new HashMap<>();
        Map<String, Object> infoMap = this.getInfoByUuid(new String[]{accountUuid}, new String[]{"personal_profile", "personal_profile_status"}, "1", false, false).get(accountUuid);
        result.put("personal_profile", MapUtils.getString(infoMap, "personal_profile", ""));
        result.put("personal_profile_status", MapUtils.getString(infoMap, "personal_profile_status", ""));
        return result;
    }

    private AccountsInfoService _this() {
        return SpringContextHolder.getBean(AccountsInfoService.class);
    }

    /**
     * 初始注册头像是否违规，1 违规 0 没违规
     * @param accountUuid
     * @param status
     */
    @Transactional
    public AccountsInfo setRegisterAvatarStatus(String accountUuid, Integer status){
        AccountsInfo accountsInfo = accountsInfoDao.getByAccountUuid(accountUuid);
        if (accountsInfo == null) {
            throw new ServiceException(CodeStatus.USER_NO_EXISTS);
        }
        accountsInfo.setRegister_avatar_status(status);
        accountsInfoDao.merge(accountsInfo);
        logger.info("setRegisterAvatarStatus accountUuid:{}|status:{}", accountUuid, status);
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "register_avatar_status", String.valueOf(status));
        return accountsInfo;
    }

    /**
     * 后台查询用户状态，提供给后台使用(APP端禁止调用该接口)
     *
     * @param uuid
     * @return
     */
    public UserStatusResp getUserStatusForBackstage(String uuid) {
        List<String> uuids = Lists.newArrayList(uuid);
        Map<String, Object> statusResponse = mpAccountClient.listAccountStatus(JsonUtils.objectToString(uuids));
        List<AccountStatusDTO> statusList = JsonUtils.stringToObject(JsonUtils.objectToString(statusResponse.get("list")), new TypeReference<List<AccountStatusDTO>>() {
        });
        if (CollectionUtils.isEmpty(statusList)) {
            return null;
        }

        AccountStatusDTO account = statusList.get(0);
        UserStatusResp resp = new UserStatusResp();
        resp.setUuid(account.getUuid());
        resp.setAccountStatus(account.getStatus());
        resp.setDestroyTime(account.getDestroyTime());
        return resp;
    }

    /**
     * 后台批量查询用户状态，提供给风控后台(APP端禁止调用该接口)
     *
     * @param uuids
     * @return
     */
    public Map<String, UserStatusResp> listUserStatusForBackstage(String[] uuids) {
        Map<String, Object> statusResponse = mpAccountClient.listAccountStatus(JsonUtils.objectToString(uuids));
        List<AccountStatusDTO> statusList = JsonUtils.stringToObject(JsonUtils.objectToString(statusResponse.get("list")), new TypeReference<List<AccountStatusDTO>>() {
        });

        if (CollectionUtils.isEmpty(statusList)) {
            return null;
        }

        Map<String, AccountStatusDTO> map = statusList.stream().collect(Collectors.toMap(AccountStatusDTO::getUuid, Function.identity(), (k1, k2) -> k1));
        return Arrays.stream(uuids).collect(Collectors.toMap(uuid -> uuid, uuid -> {
            AccountStatusDTO status = map.get(uuid);
            UserStatusResp resp = new UserStatusResp();
            if (status != null) {
                resp.setUuid(status.getUuid());
                resp.setAccountStatus(status.getStatus());
                resp.setDestroyTime(status.getDestroyTime());
            }
            return resp;
        }));
    }

    /**
     * @param accountUuid
     * @param source
     * @return
     */
    public InitializeAccountVo initializeAccount(String accountUuid, Integer source) {
        LOGGER.info("initializeAccount accountUuid={}, source={}", accountUuid, source);

        // 目前要求传uuid，若未传，则返回默认值
        if (StringUtils.isBlank(accountUuid)) {
            return InitializeAccountVo.buildDefaultVo();
        }

        // 根据用户uuid，查询是否弹出
        if (accountsCertificationService.isRewardAccountInvalidNotice(accountUuid)) {
            ShowDialogVo showDialogVo = new ShowDialogVo("提示", "您的支付宝账号绑定已过期，请重新绑定", "前往绑定", AccountsCertificationService.getRewardAccountUpdateH5(), "取消", "");
            return InitializeAccountVo.builAlipayAccountDisabledVo(showDialogVo);
        }

        return InitializeAccountVo.buildDefaultVo();
    }

    /**
     * 获取用户登录cloned，未获取到，则使用
     *
     * @param accountUuid
     * @return
     */
    public Integer getAccountCloned(String accountUuid){
        Map<String, Object> accountInfoMap = this.getInfoByUuid(new String[]{accountUuid}, new String[]{ UuidInfoField.LOGIN_CLONED, UuidInfoField.REG_CLONED}, "1", false, true).get(accountUuid);
        Integer cloned = MapUtils.getInteger(accountInfoMap, UuidInfoField.LOGIN_CLONED, null);
        if(cloned == null) {
            logger.warn("用户无最后登录cloned，accountUuid={}", accountUuid);
            cloned = MapUtils.getInteger(accountInfoMap, UuidInfoField.REG_CLONED, null);
        }
        if(cloned == null) {
            logger.warn("用户无注册cloned，accountUuid={}", accountUuid);
            cloned = CommConst.CLONED_TAQU;
        }

        return cloned;
    }

    /**
     *
     *
     * @param accountUuid
     * @return
     */
    public ClonedPlanformAppVersionDto getAccountClonedPlanformAppVersion(String accountUuid){
        ClonedPlanformAppVersionDto dto = new ClonedPlanformAppVersionDto();
        Map<String, Object> accountInfoMap = this.getInfoByUuid(
            new String[]{accountUuid},
            new String[]{ UuidInfoField.LOGIN_CLONED, UuidInfoField.REG_CLONED, UuidInfoField.PLATFORM_ID, UuidInfoField.APP_VERSION},
            "1",
            false,
            true).get(accountUuid);

        Integer cloned = MapUtils.getInteger(accountInfoMap, UuidInfoField.LOGIN_CLONED, null);
        if(cloned == null) {
            logger.warn("用户无最后登录cloned，accountUuid={}", accountUuid);
            cloned = MapUtils.getInteger(accountInfoMap, UuidInfoField.REG_CLONED, null);
        }
        if(cloned == null) {
            logger.warn("用户无注册cloned，accountUuid={}", accountUuid);
            cloned = CommConst.CLONED_TAQU;
        }
        Integer platformId = MapUtils.getInteger(accountInfoMap, UuidInfoField.PLATFORM_ID, null);
        Long appVersion = MapUtils.getLong(accountInfoMap, UuidInfoField.APP_VERSION, null);

        dto.setCloned(cloned);
        dto.setPlatformId(platformId);
        dto.setAppVersion(appVersion);
        return dto;
    }

    /**
     * 获取uuid与相册
     *
     * @param uuids
     * @return
     */
    public Map<String, LabelAndPhotoResp> listLabelAndPhoto(ArrayList<String> uuids) {
        HashMap<String, LabelAndPhotoResp> map = Maps.newHashMap();
        List<InfoFiledCacheDTO> infoList = accountBaseInfoManager.listInfoByUuid(uuids, new String[]{UuidInfoField.UUID, UuidInfoField.PHOTO});
        Map<String, InfoFiledCacheDTO> infoMap = infoList.stream().collect(Collectors.toMap(InfoFiledCacheDTO::getUuid, Function.identity()));
        for (String uuid : uuids) {
            LabelAndPhotoResp resp = new LabelAndPhotoResp();
            map.put(uuid, resp);

            boolean profileExp = profileOptimizationUiService.isProfileExp(uuid);
            // 新版本
            if (profileExp) {
                List<AccountLabelCfg> labels = accountLabelServiceV2.listMyLabelInfo(uuid);
                resp.setLabel(labels.stream().map(AccountLabelCfg::getContent).collect(Collectors.toList()));
            } else {
                // 旧版
                List<String> labels = accountsLabelService.getLabel(uuid);
                resp.setLabel(labels);
            }

            InfoFiledCacheDTO infoFiled = infoMap.get(uuid);
            List<String> photoUrl = infoFiled.getPhoto().stream().filter(item -> Objects.equals(item.getVerifyStatus(), 1))
                    .map(InfoFiledCacheDTO.Photo::getPhotoUrl)
                    .collect(Collectors.toList());
            resp.setPhoto(photoUrl);
        }

        return map;
    }

}
