package cn.taqu.account.service;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.special.SpecialAccountHandler;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.StringUtil;
import cn.taqu.core.utils.ValidateUtil;
import com.taqu.mp.account.client.MPAccountClient;
import com.taqu.mp.account.constant.CamelCaseBizConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 自动生成Service类
 */
@Slf4j
@Service
public class VerifyRecordService {

    @Autowired
    private MPAccountClient mpAccountClient;

    @Autowired
    @Lazy
    private MobileService mobileService;

    public enum SmsType {
        SINGLE_VCODE, //单条文字验证码
        VOICE_VCODE, //语音验证码
    }

    public enum Code {
        REG_LOGIN, //注册登录
        BIND_MOBILE, //绑定手机号
        RESET_PASSWORD, //重置密码
        MODIFY_MOBILE, //修改手机号
        VOICE_VERIFY, //语音认证码
        WEB_LOGIN, //web登录
        MODIFY_REWARD_ACCOUNT, //修改支付宝账号
        DESTROY_ACCOUNT // 账号注销
    }

    /**
     * 发送短信
     * @param code
     * @param smsType
     * @param mobile
     * @return
     */
    public boolean sendVerify(Code code, SmsType smsType, String mobile) {
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.PUSH).call("verificationCode", "requestVerificationCode", code.name().toLowerCase(), smsType.name(), mobile);
        if(soaResponse.fail()) {
            log.warn("请求推送系统获取验证码失败, {}-{}", soaResponse.getCode(), soaResponse.getMsg());
            throw new ServiceException(soaResponse.getCode(), soaResponse.getMsg());
        }
        return true;
    }

    /**
     * 校验是否是合法的验证码，6位数字
     */
    private static final Pattern VERIFY_REGX = Pattern.compile("\\d{6}|\\d{4}");
    public static boolean isValidVerify(String verify) {
        if(StringUtils.isBlank(verify)) {
            return false;
        }
        return VERIFY_REGX.matcher(verify).matches();
    }

    /**
     * 校验验证码 同时支持校验语音验证码与短信验证码
     * @param code 验证码类型(注册验证码，修改密码验证码之类)
     * @param mobile 手机号
     * @param verify 验证码，可以是短信验证码，也可以是语音验证码
     * @return
     */
    public boolean checkVerify(Code code, String mobile, String verify) {
        if (!ValidateUtil.isMobile(mobile)) {
            throw new ServiceException(CodeStatus.MOBILE_INVALID);
        }
        if (!isValidVerify(verify)) {
            throw new ServiceException(CodeStatus.UNVAILD_VERIFY_CODE);
        }
        // 2021.11.05 配配审核使用，如果为审核手机号校验验证码，直接返回成功
        if(SpecialAccountHandler.isAuditMobile(mobile, verify)){
            return true;
        }

        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.PUSH).call("verificationCode", "verify", code.name().toLowerCase(), mobile, verify);
        if (soaResponse.fail()) {
            log.warn("请求推送系统校验短信验证码失败, {}-{}", soaResponse.getCode(), soaResponse.getMsg());
            //throw new ServiceException(CodeStatus.VERIFY_VCODE_BUSY);
            throw new ServiceException(soaResponse.getCode(), soaResponse.getMsg());
        }
        return "true".equals(soaResponse.getData());
    }

    public void sendVerifyCodeForRetrieve(String uuid, String mobile) {
        if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
            throw new ServiceException(CodeStatus.MOBILE_INVALID);
        }

        Map<String, Object> accountInfo = mpAccountClient.getAccountInfo(uuid);
        String oldMobile = StringUtil.nullObjectToEmptyString(MapUtils.getString(accountInfo, CamelCaseBizConstant.PHONE));

        if (Objects.equals(mobile, oldMobile)) {
            throw new ServiceException("新手机号不能与旧手机号一致");
        }

        // 校验新手机号是否已绑定账号状态，不等于已注销，就是已绑定或其他情况...
        int checkStatus = mobileService.checkMobileStatus(mobile, false);
        if (checkStatus == 1) {
            throw new ServiceException(CodeStatus.MOBILE_EXISTS);
        }

        this.sendVerify(VerifyRecordService.Code.MODIFY_MOBILE, VerifyRecordService.SmsType.SINGLE_VCODE, mobile);
    }
}
