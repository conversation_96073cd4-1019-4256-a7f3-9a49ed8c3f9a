package cn.taqu.account.service;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsCardDao;
import cn.taqu.account.model.AccountsCard;
import cn.taqu.account.model.AccountsCardLog;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户靓号service
 */
@Service
public class AccountsCardService {
    private Logger logger = LoggerFactory.getLogger(AccountsCardService.class);
    public static final Integer EXPIRE_TIME = 7;

    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsCardLogService accountsCardLogService;
    @Autowired
    private AccountsCardExpireService accountsCardExpireService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private AccountsCardDao accountsCardDao;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    @Qualifier("accountBizStringRedisTemplate")
    private StringRedisTemplate accountBizStringRedisTemplate;
    @Autowired
    private RedisLockUtil redisLockUtil;

    public List<Long> getUsedAndNormalCardByUuid(String accountUuid) {
        return accountsCardDao.getUsedAndNormalCardByUuid(accountUuid);
    }

    /**
     * 根据uuid获取数据
     * @param accountUuid 用户uuid
     * @param page 页数 从1开始
     * @param limit 每次取多少条
     * @return
     */
    public List<Map<String, String>> getByUuid(String accountUuid, int page, int limit) {
        if(StringUtils.isBlank(accountUuid)) {
            return Collections.EMPTY_LIST;
        }

        List<AccountsCard> accountsCardList = accountsCardDao.getByUuid(accountUuid, page, limit);
        int size = accountsCardList == null ? 0 : accountsCardList.size();
        List<Map<String, String>> result = new ArrayList<>(size);
        for(int i=0; i<size; i++) {
            AccountsCard accountsCard = accountsCardList.get(i);
            Map<String, String> map = new HashMap<>();
            map.put("card_id", String.valueOf(accountsCard.getCardId()));
            map.put("level", String.valueOf(accountsCard.getLevel()));
            map.put("status", String.valueOf(accountsCard.getStatus()));
            map.put("is_used", Objects.equals(accountsCard.getSource(), 2) ? "1" : String.valueOf(accountsCard.getIsUsed()));
            if(accountsCard.getEndTime() != null && accountsCard.getEndTime()>0) {
                map.put("valid_day", String.valueOf(DateUtil.chaDay(new Date(), DateUtil.fromSecond(accountsCard.getEndTime())) + 1) + "天");
            } else {
                map.put("valid_day", "永久");
            }
            result.add(map);
        }
        return result;
    }

    /**
     * 预发放普通号码，先将用户uuid缓存起来，异步获取普通号码
     */
    public void preGrantNormalCard(String accountUuid) {
        accountStringRedisTemplate.opsForList().rightPush(RedisKeyConstant.PRE_GRANT_CARD.getPattern(), accountUuid);
    }

    /**
     * 给用户发放普通号码
     * @param accountUuid 用户uuid
     * @param cardId 号码id
     */
    @Transactional
    public void grantNormalCard(String accountUuid, Long cardId) {
        String lockKey = "grant:normal:card" + cardId;

        try {
            if(!redisLockUtil.lock(lockKey, 10)) {
                throw new ServiceException("card_granting", "您操作得太频繁了，请10秒后再试");
            }

            Long existsCardId = accountsCardDao.getCardByUuid(accountUuid, AccountsCard.Level.N);
            if(existsCardId != null) {
                throw new ServiceException("uuid_had_card", "用户" + accountUuid + "已经拥有了普通号码，不能重复发放");
            }
            String existsUuid = accountsCardDao.getUuidByCardId(cardId);
            if(StringUtils.isNotBlank(existsUuid)) {
                throw new ServiceException("card_id_exists", "号码" + cardId + "已经发放给其他用户了");
            }

            AccountsCard accountsCard = new AccountsCard();
            accountsCard.setAccountUuid(accountUuid);
            accountsCard.setCardId(cardId);
            accountsCard.setEndTime(0L);
            accountsCard.setLevel(AccountsCard.Level.N);
            accountsCard.setStatus(0);
            accountsCard.setIsUsed(0);
            accountsCard.setSource(0);
            accountsCard.setCreateTime(DateUtil.currentTimeSeconds());
            accountsCard = this.accountsCardDao.merge(accountsCard);

            this.internalUseCard(accountsCard, "system", "普通号码发放后自动使用");
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "default_card_id", String.valueOf(cardId));
        } finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 给用户发放靓号
     * @param accountUuid
     * @param cardId
     * @param cardLevel
     * @param endTime
     * @param operateName
     */
    @Transactional
    public void grantCard(String accountUuid, Long cardId, String cardLevel, Long endTime, String operateName) {
        String lockKey = "grant:nick:card" + cardId;

        try {
            if(!redisLockUtil.lock(lockKey, 10)) {
                throw new ServiceException("card_granting", "您操作得太频繁了，请10秒后再试");
            }

            if (endTime < DateUtil.currentTimeSeconds()) {
                throw new ServiceException("end_time_error", "靓号到期时间不能早于当前时间");
            }
            Date endDate = DateUtil.getDateEnd(DateUtil.fromSecond(endTime));

            String existsUuid = accountsCardDao.getUuidByCardId(cardId);
            if (StringUtils.isNotBlank(existsUuid) && !StringUtils.equals(accountUuid, existsUuid)) {
                throw new ServiceException("card_id_exists", "靓号" + cardId + "已经发放给其他用户了");
            }
            if (StringUtils.equals(accountUuid, existsUuid)) {
                throw new ServiceException("uuid_card_exists", "用户" + accountUuid + "已经拥有了该靓号");
            }
            if (AccountsCard.Level.isNormal(cardLevel)) {
                throw new ServiceException("is_normal_card", cardId + "为普通号码，不能进行手动发放");
            }

            AccountsCard accountsCard = new AccountsCard();
            accountsCard.setAccountUuid(accountUuid);
            accountsCard.setCardId(cardId);
            accountsCard.setEndTime(DateUtil.toSecond(endDate));
            accountsCard.setLevel(cardLevel);
            accountsCard.setStatus(0);
            accountsCard.setIsUsed(0);
            accountsCard.setSource(1);
            accountsCard.setCreateTime(DateUtil.currentTimeSeconds());
            this.accountsCardDao.merge(accountsCard);

            accountsCardLogService.addLog(accountUuid, cardId, cardLevel, accountsCard.getCreateTime(), accountsCard.getEndTime(), AccountsCardLog.Type.GRANT, operateName, "手动发放靓号;到期时间:" + DateUtil.dateToString20(DateUtil.fromSecond(accountsCard.getEndTime())));

            //加入过期列表
            accountStringRedisTemplate.opsForSet().add(RedisKeyConstant.CARD_TTL_SET.setArg(DateUtil.dateToString8(endDate)), cardId.toString());
        } finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 靓号过期回收
     * @param cardId
     */
    @Transactional
    public void revokeExpireCard(Long cardId) {
        AccountsCard accountsCard = accountsCardDao.getByCardId(cardId);
        if (accountsCard == null) {
            return;
        }

        if (AccountsCard.Level.isNormal(accountsCard.getLevel())) {
            logger.error("回收过期靓号错误，靓号:{} 为普通号码，不能回收", cardId);
            return;
        }

        //加入过期表
        String accountUuid = accountsCard.getAccountUuid();
        accountsCardExpireService.create(accountUuid, cardId, accountsCard.getLevel(), accountsCard.getEndTime());
        accountsCardLogService.addLog(accountUuid, cardId, accountsCard.getLevel(), accountsCard.getCreateTime(), accountsCard.getEndTime(), AccountsCardLog.Type.REVOKE, "system", "靓号过期自动回收");
        accountsCardDao.removeById(accountsCard.getId());

        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.PHP.LIVE_V1).call("AccountCard", "updateCardStatus", cardId, 0);
        if (soaResponse.fail()) {
            throw new ServiceException("live_revoke_error", String.format("回收靓号通知直播失败,%s-%s", soaResponse.getCode(), soaResponse.getMsg()));
        }

        //如果过期的是使用中的靓号，回收后自动使用普通靓号
        if (accountsCard.getStatus() == 1) {
            this.useNormalCard(accountUuid, "使用中的靓号:" + cardId + "已过期, 自动使用普通号码");
        }

        Map<String, String> content = Maps.newHashMap();
        String tips = String.format("您的 %d靓号 于%s 过期，请前往直播间—play—靓号功能中佩戴靓号ID", cardId, DateUtil.dateToString("yyyy年MM月dd日 HH:mm", DateUtil.fromSecond(accountsCard.getEndTime())));
        content.put("content", tips);
        content.put("relaction", "");
        content.put("is_local_push", "1");
        messageService.systemNotice(accountUuid, content, "", "text", 1);
    }

    /**
     * 手动收回靓号
     * @param accountUuid
     * @param cardId
     * @param operateName
     */
    @Transactional
    public void revokeCard(String accountUuid, Long cardId, String operateName) {
        AccountsCard accountsCard = accountsCardDao.getByUuidAndCardId(accountUuid, cardId);
        if(accountsCard == null) {
            throw new ServiceException("card_no_exists", "用户:" + accountUuid + "还未拥有靓号:" + cardId + ";无法回收");
        }
        if(AccountsCard.Level.isNormal(accountsCard.getLevel())) {
            throw new ServiceException("revoke_normal_error", "普通号码无法回收");
        }

        //加入过期表，过期时间设置为今天
        accountsCardExpireService.create(accountUuid, cardId, accountsCard.getLevel(), DateUtil.getTodayEndSecond());
        accountsCardLogService.addLog(accountUuid, cardId, accountsCard.getLevel(), accountsCard.getCreateTime(), accountsCard.getEndTime(), AccountsCardLog.Type.REVOKE, operateName, "后台回收靓号");
        //删除数据
        accountsCardDao.removeById(accountsCard.getId());
        //如果回收的是使用中的靓号，回收后自动使用普通靓号
        if(accountsCard.getStatus() == 1) {
            this.useNormalCard(accountUuid, "使用中的靓号:" +cardId+ "被回收, 自动使用普通号码");
        }

        //从过期列表中移除
        accountStringRedisTemplate.opsForSet().remove(RedisKeyConstant.CARD_TTL_SET.setArg(DateUtil.dateToString8(DateUtil.fromSecond(accountsCard.getEndTime()))), cardId.toString());
    }

    /**
     * 转赠靓号
     * @param fromUuid
     * @param toUuid
     * @param cardId
     */
    @Transactional
    public void giveCard(String fromUuid, String toUuid, Long cardId) {
        if(StringUtils.isBlank(fromUuid)) {
            return;
        }

        String lockKey = "give:card:" + fromUuid;
        try {
            if(!redisLockUtil.lock(lockKey, 10)) {
                throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
            }

            AccountsCard accountsCard = accountsCardDao.getByUuidAndCardId(fromUuid, cardId);
            if (accountsCard == null || !Objects.equals(fromUuid, accountsCard.getAccountUuid())) {
                throw new ServiceException("no_card", "您还未拥有靓号:" + cardId + "，不能转赠！");
            }
            if (AccountsCard.Level.isNormal(accountsCard.getLevel())) {
                throw new ServiceException("normal_card", "非靓号不能转赠哦！");
            }
            if (accountsCard.getIsUsed() == 1) {
                throw new ServiceException("card_used", "您已使用过该靓号，不能再转赠他人了哦！");
            }
            if (Objects.equals(accountsCard.getSource(), 2)) {
                throw new ServiceException("given_card", "别人赠送的靓号不能再转赠他人了！");
            }
            if (accountsCard.getEndTime() <= DateUtil.currentTimeSeconds()) {
                throw new ServiceException("card_expire", "靓号已过期，不能转赠");
            }

            accountsCard.setIsUsed(0);
            accountsCard.setAccountUuid(toUuid);
            accountsCard.setSource(2);
            accountsCardDao.merge(accountsCard);

            Long curTimesecond = DateUtil.currentTimeSeconds();
            accountsCardLogService.addLog(fromUuid, cardId, accountsCard.getLevel(), curTimesecond, accountsCard.getEndTime(), AccountsCardLog.Type.GIVEN, toUuid, "转赠靓号给:" + toUuid);
            AccountsCardLog accountsCardLog = accountsCardLogService.addLog(toUuid, cardId, accountsCard.getLevel(), curTimesecond, accountsCard.getEndTime(), AccountsCardLog.Type.GET, fromUuid, "通过用户:" + fromUuid + "转赠获得");

            //发送系统通知
            Map<String, Object> accountInfo = accountsInfoService.getInfoByUuid(new String[]{fromUuid}, new String[]{"account_name"}, "1", false, false).get(fromUuid);
            String accountName = MapUtils.getString(accountInfo, "account_name", "");
            String tips = String.format("收到 %s 赠送的%s级靓号 %d (%d天)", accountName, accountsCard.getLevel(), accountsCard.getCardId(), accountsCardLog.getValidDay());
            Map<String, String> content = Maps.newHashMap();
            content.put("content", tips);
            content.put("relaction", "");
            content.put("is_local_push", "1");
            messageService.systemNotice(toUuid, content, "", "text", 1);
        } finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 批量获取号码信息，返回以靓号为key，以靓号信息为value的键值对，其中账号信息包含的字段如下：<br/>
     * <ol>
     *     <li>status: Integer 0:未使用; 1:使用中</li>
     *     <li>end_time: Long 到期时间，普通号码时，返回0表示无限期</li>
     *     <li>account_uuid: 用户uuid</li>
     *     <li>account_name: 用户昵称</li>
     * </ol>
     * @param cardIds
     * @return
     */
    public Map<Long, Map<String, Object>> mGetAccountCardInfo(Long[] cardIds) {
        List<AccountsCard> cardList = accountsCardDao.getByCardIdList(Arrays.asList(cardIds), Arrays.asList("account_uuid", "card_id", "status", "end_time"));
        int size = cardList == null ? 0 : cardList.size();
        String[] uuids = new String[size];
        for(int i=0; i<size; i++) {
            uuids[i] = cardList.get(i).getAccountUuid();
        }

        Map<String, Map<String, Object>> accountInfoMap = accountsInfoService.getInfoByUuid(uuids, new String[]{"account_name"}, "1", false, false);
        Map<Long, Map<String, Object>> resultMap = new LinkedHashMap<>(size);
        for(int i=0; i<size; i++) {
            AccountsCard card = cardList.get(i);
            String uuid = card.getAccountUuid();
            Long cardId = card.getCardId();

            Map<String, Object> dataMap = new HashMap<>(4);
            dataMap.put("status", card.getStatus());
            dataMap.put("end_time", card.getEndTime());
            dataMap.put("account_uuid", uuid);
            dataMap.put("account_name", MapUtils.getString(accountInfoMap.get(uuid), "account_name", ""));
            resultMap.put(cardId, dataMap);
        }

        return resultMap;
    }

    /**
     * 使用靓号
     * @param accountUuid
     * @param cardId
     */
    @Transactional
    public void useCard(String accountUuid, Long cardId) {
        if(StringUtils.isBlank(accountUuid)) {
            return;
        }

        String lockKey = "use:card:" + accountUuid;
        try {
            if (!redisLockUtil.lock(lockKey, 10)) {
                throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
            }

            AccountsCard accountsCard = accountsCardDao.getByUuidAndCardId(accountUuid, cardId);
            if(accountsCard == null) {
                throw new ServiceException("no_card", "您还未拥有这个靓号哦！");
            }
            if(accountsCard.getStatus() == 1) {
                throw new ServiceException("use_already", "您已经在使用了这个靓号了!");
            }

            //统一更新其他的靓号为未使用
            accountsCardDao.updateOtherCardStatusByUuid(accountUuid, cardId, 0);
            this.internalUseCard(accountsCard, accountUuid, "用户选择使用号码");
        } finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 使用普通号码
     * @param accountUuid
     */
    private void useNormalCard(String accountUuid, String remark) {
        AccountsCard accountsCard = accountsCardDao.getByUuid(accountUuid, AccountsCard.Level.N);
        if(accountsCard == null) {
            logger.warn("使用默认号码过程中发现，用户uuid:{}没有普通号码, 重新获取一个, {}", accountUuid, remark);
            List<Long> cardList = this.mGetNormalCard(9, 1);
            if(cardList == null) {
                throw new ServiceException("use_normal_fail", "无法操作，该用户还没有默认号码");
            }

            accountsCard = new AccountsCard();
            accountsCard.setAccountUuid(accountUuid);
            accountsCard.setCardId(cardList.get(0));
            accountsCard.setLevel(AccountsCard.Level.N);
            accountsCard.setIsUsed(0);
            accountsCard.setStatus(0);
            accountsCard.setEndTime(0L);
            accountsCard.setSource(0);
            accountsCard.setCreateTime(DateUtil.currentTimeSeconds());
            accountsCard = accountsCardDao.merge(accountsCard);
        }

        this.internalUseCard(accountsCard, accountUuid, remark);
    }

    /**
     * 使用号码
     * @param accountsCard
     */
    private void internalUseCard(AccountsCard accountsCard, String operatorName, String remark) {
        String accountUuid = accountsCard.getAccountUuid();
        Long cardId = accountsCard.getCardId();
        String cardLevel = accountsCard.getLevel();

        accountsCardDao.useCardId(accountUuid, cardId);
        accountsCardLogService.addLog(accountUuid, cardId, accountsCard.getLevel(), accountsCard.getCreateTime(), accountsCard.getEndTime(), AccountsCardLog.Type.USE, operatorName, remark);

        Map<String, String> hashValues = new HashMap<>();
        hashValues.put("account_card_id", cardId.toString());
        hashValues.put("account_card_level", cardLevel);

        //更新缓存
        accountBizStringRedisTemplate.opsForValue().set(RedisKeyConstant.ACCOUNT_CARD_UUID.setArg(cardId), accountUuid, EXPIRE_TIME, TimeUnit.DAYS);
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), hashValues);

        //更新账号昵称、靓号索引
        accountsService.updateNicknameIndex(accountUuid);
    }

    public List<Long> checkCardGrant(List<Long> cardIdList) {
        return accountsCardDao.getByCardIdList(cardIdList, Arrays.asList("card_id"))
                .stream()
                .map(AccountsCard::getCardId)
                .collect(Collectors.toList());
    }

    public List<Long> mGetNormalCard(int cardSize, int count) {
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.PHP.LIVE_V1).call("AccountCard", "getNotUseNormalCard", cardSize, count);
        if(soaResponse.fail()) {
            logger.error("异步批量获取靓号(普通号)失败，失败码:{}，失败原因:{}", soaResponse.getCode(), soaResponse.getMsg());
            return null;
        }

        List<Long> cardIdList = JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<List<Long>>() {});
        if(cardIdList == null || cardIdList.isEmpty()) {
            logger.error("异步批量获取靓号(普通号)失败，未获取到可用的普通号:{}", soaResponse.getData());
            return null;
        }

        return cardIdList;
    }

    /**
     * 注：该临时接口，预热主播与聊天室雇主普通靓号用<br/>
     * 批量发放普通靓号，参数为用户uuid与靓号号码的键值对, key:uuid; value:靓号号码</br>
     * 如果用户已经拥有了普通靓号，则使用新的靓号替代原来的靓号
     * @param uuidNormalCard
     */
    @Transactional
    public void mGrantNormalCard(Map<String, Long> uuidNormalCard) {
        logger.info("【废弃接口】:{}, {}", "AccountsCardService", "mGrantNormalCard");
        if(uuidNormalCard == null || uuidNormalCard.isEmpty()) {
            return;
        }

        List<String> uuidList = new ArrayList<>(uuidNormalCard.size());
        uuidNormalCard.forEach((uuid, cardId) -> {
            if(StringUtils.isBlank(uuid)) {
                return;
            }
            if(cardId == null || cardId<=0) {
                throw new ServiceException("card_id_empty", "uuid:"+uuid+"对应的号码:"+cardId+"不是正确的号码，无法处理");
            }
            uuidList.add(uuid);
        });
        List<AccountsCard> cardList = accountsCardDao.getAllByUuidList(uuidList);
        cardList.forEach(card -> {
            if(AccountsCard.Level.isNormal(card.getLevel())) {
                String uuid = card.getAccountUuid();
                Long normalCard = uuidNormalCard.get(uuid);
                card.setCardId(normalCard);
                card.setLevel(AccountsCard.Level.N);
                uuidNormalCard.remove(uuid);
            } else {
                card.setStatus(0);
            }
        });

        uuidNormalCard.forEach((uuid, cardId) -> {
            AccountsCard card = new AccountsCard();
            card.setAccountUuid(uuid);
            card.setCardId(cardId);
            card.setLevel(AccountsCard.Level.N);
            card.setEndTime(0L);
            card.setStatus(1);
            card.setIsUsed(1);
            cardList.add(card);
        });

        accountsCardDao.merge(cardList);

        accountStringRedisTemplate.executePipelined((RedisConnection conn) -> {
            StringRedisConnection stringConn = (StringRedisConnection)conn;

            for (AccountsCard accountsCard : cardList) {
                String uuid = accountsCard.getAccountUuid();
                Long cardId = accountsCard.getCardId();
                if(StringUtils.isBlank(uuid) || cardId == null) {
                    logger.warn("uuid:{}或cardId:{}为空", uuid, cardId);
                    continue;
                }
                String cardLevel = accountsCard.getLevel();
                //如果是靓号
                if(!AccountsCard.Level.isNormal(cardLevel)) {
                    stringConn.del(RedisKeyConstant.ACCOUNT_CARD_UUID.setArg(cardId));
                    continue;
                }
                Map<String, String> hashValues = new HashMap<>();
                hashValues.put("account_card_id", cardId.toString());
                hashValues.put("account_card_level", cardLevel);
                stringConn.hMSet(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), hashValues);
                stringConn.set(RedisKeyConstant.ACCOUNT_CARD_UUID.setArg(cardId), uuid);
            }

            return null;
        });
    }

    /**
     * 获取普通号对应uuid
     *
     * <AUTHOR>
     * @date 2020/06/09 17:13
     * @param cardId
     * @return
     */
	public String getUuidByNormalCard(Long cardId) {
		String uuid = accountsCardDao.getUuidByCardId(cardId, AccountsCard.Level.N);
		return StringUtils.defaultString(uuid, "");
	}

    /**
     * 获取cardid对应uuid
     * @param cardId
     * @return
     */
    public String getCardUuidInUseStatusByDB(Long cardId) {
        String uuid = accountsCardDao.getCardUuidInUseStatusByDB(cardId);
        return StringUtils.defaultString(uuid, "");
    }

	/**
	 * 获取普通号对应uuid(批量)
	 *
	 * <AUTHOR>
	 * @date 2020/06/09 17:13
	 * @param cardIds
	 * @return
	 */
	public Map<Long, String> listUuidByCardId(List<Long> cardIds) {
		Map<Long,String> map = Maps.newHashMap();
		List<AccountsCard> list = accountsCardDao.listUuidByCardId(cardIds, AccountsCard.Level.N);
		if(CollectionUtils.isEmpty(list)) {
			return map;
		}
		list.stream().forEach(entry -> {map.put(entry.getCardId(), entry.getAccountUuid());});
		return map;
	}

    /**
     * 获取uuid
     *
     * @param cardId
     * @return
     */
    public String getUuid(Long cardId) {
        String key = RedisKeyConstant.ACCOUNT_CARD_UUID.setArg(cardId);
        String uuid = accountBizStringRedisTemplate.opsForValue().get(key);
        if (uuid != null) {
            return StringUtils.trimToEmpty(uuid);
        }

        uuid = accountsCardDao.getUuidByCardId(cardId);
        if (StringUtils.isBlank(uuid)) {
            uuid = "";
        }

        accountBizStringRedisTemplate.opsForValue().set(key, uuid, EXPIRE_TIME, TimeUnit.DAYS);
        return uuid;
    }

}
