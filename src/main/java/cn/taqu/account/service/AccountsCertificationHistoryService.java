package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.dao.AccountsCertificationHistoryDao;
import cn.taqu.account.model.AccountsCertificationHistory;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-29 17:46
 */
@Service
@Slf4j
@Transactional
public class AccountsCertificationHistoryService {

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsCertificationHistoryDao accountsCertificationHistoryDao;

    /**
     * 保存日志
     * @param accountUuid
     * @param identityType
     * @param rewardACcountCheck
     * @param operator
     * @param basePhotoUrl
     */
    public void saveOrUpdate(String accountUuid, Integer identityType, Integer rewardACcountCheck, String operator, String basePhotoUrl){
        Long now = DateUtil.currentTimeSeconds();
        AccountsCertificationHistory history = new AccountsCertificationHistory();
        history.setAccountUuid(accountUuid).setIdentityType(identityType).setRewardAccountChecked(rewardACcountCheck)
                .setOperator(operator).setOperatorTime(now).setBasePhotoUrl(basePhotoUrl)
                .setCreateTime(now).setUpdateTime(now);
        AccountsCertificationHistory oldHistory = accountsCertificationHistoryDao.getByUuid(history.getAccountUuid());
        if(oldHistory != null){
            history.setId(oldHistory.getId());
            history.setCreateTime(now).setUpdateTime(now);
        }
        accountsCertificationHistoryDao.merge(history);
    }
}
