package cn.taqu.account.service;

import cn.taqu.account.common.CertTypeEnum;
import cn.taqu.account.dto.AccountCertDataDTO;
import cn.taqu.account.model.AccountsCertLog;
import cn.taqu.account.model.AccountsCertificationChangeLog;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 认证记录的通用处理
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 16:01
 */
@Service
@Slf4j
public class AccountCertLogService {

    @Autowired
    private AccountsCertificationChangeLogService accountsCertificationChangeLogService;
    @Autowired
    private AccountsCertLogService accountsCertLogService;

    public void handleLog(AccountCertDataDTO accountCertData){
        Integer certType = accountCertData.getCertType();
        Map<String, Object> data = accountCertData.getData();
        if(Objects.equals(certType, CertTypeEnum.REAL_NAME.getValue())){
            AccountsCertificationChangeLog certificationChangeLog = JSON.parseObject(JSON.toJSONString(data), AccountsCertificationChangeLog.class);
            accountsCertificationChangeLogService.save(certificationChangeLog);

            AccountsCertLog certLog = new AccountsCertLog();
            certLog.setAccountUuid(certificationChangeLog.getAccountUuid()).setCertWhiteList(MapUtils.getInteger(data, "certWhiteList", 0))
                    .setCertType(CertTypeEnum.REAL_NAME.getValue())
                    .setOperator(certificationChangeLog.getOperator()).setOperatorTime(certificationChangeLog.getOperatorTime())
                    .setOperatorType(certificationChangeLog.getLogType()).setBasePhotoUrl(certificationChangeLog.getBasePhotoUrl())
                    .setRemark(MapUtils.getString(data, "remark", ""));
            accountsCertLogService.save(certLog);
        } else if(Objects.equals(certType, CertTypeEnum.REAL_PERSON.getValue())){
            AccountsCertLog certLog = JSON.parseObject(JSON.toJSONString(accountCertData.getData()), AccountsCertLog.class);
            accountsCertLogService.save(certLog);
        } else if(Objects.equals(certType, CertTypeEnum.REWARD_ACCOUNT.getValue())){
            AccountsCertLog certLog = JSON.parseObject(JSON.toJSONString(accountCertData.getData()), AccountsCertLog.class);
            accountsCertLogService.save(certLog);
        } else{
            log.warn("日志类型错误.data={}", JsonUtils.objectToString2(accountCertData));
        }
    }

}
