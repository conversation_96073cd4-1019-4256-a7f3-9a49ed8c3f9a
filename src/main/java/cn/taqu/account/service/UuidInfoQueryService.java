package cn.taqu.account.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;

import cn.taqu.account.common.AliyunLiveFaceDetectStatus;
import cn.taqu.account.common.GenderEnum;
import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.constant.AvatarConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.vo.LocationInfoVO;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.StringUtil;

/**
 * 根据uuid查询用户信息service, 查询用户信息的的逻辑比较复杂，独立出来，便于维护
 *
 * <AUTHOR>  2019/3/7 5:21 PM
 */
@Service
public class UuidInfoQueryService {
    private static Logger LOGGER = LoggerFactory.getLogger(UuidInfoQueryService.class);
    @Autowired
    private AvatarHandleService avatarHandleService;
    @Autowired
    private AccountsMedalService accountsMedalService;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private RegionService regionService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private AccountsLocationInfoService accountsLocationInfoService;

    @PostConstruct
    private void init() {
        this.initDefaultValue();
    }

    /**
     * 直接从用户信息缓存不存在时的默认值
     */
    private Map<String, String> accountInfoDefaultValue = new HashMap<>();
    /**
     * 用户保密设置
     */
    private Map<String, String> secretDefaultValue = new HashMap<>();
    /**
     * 用户活跃
     */
    private Map<String, String> activeDefaultValue = new HashMap<>();
    /**
     * 用户所在地城市
     */
    private Map<String, String> addrDefaultValue = new HashMap<>();
    /**
     * 用户家乡城市
     */
    private Map<String, String> hometownDefaultValue = new HashMap<>();
    /**
     * 用户相册
     */
    private Map<String, String> accountPhotoValue = new HashMap<>();

    private void initDefaultValue() {
        /** 用户基本信息 **/
        /* **************来自于accounts表的数据***************/
        accountInfoDefaultValue.put(UuidInfoField.EMAIL, "");
        accountInfoDefaultValue.put(UuidInfoField.MOBILE, "");
        accountInfoDefaultValue.put(UuidInfoField.MOBILE_CIPHER, "");
        accountInfoDefaultValue.put(UuidInfoField.IS_BIND_MOBILE, "0");
        accountInfoDefaultValue.put("account_name", "游客");//缓存不存在的默认显示用户名为游客
        accountInfoDefaultValue.put("account_name_origin", "");
        accountInfoDefaultValue.put("account_name_status", "1");
        accountInfoDefaultValue.put(UuidInfoField.ACCOUNT_NAME_DEFAULT, "0");// 默认昵称
        accountInfoDefaultValue.put("account_status", "");
        accountInfoDefaultValue.put("account_type", "2");//缓存不存在的默认当游客处理
        accountInfoDefaultValue.put("sex_type", "0");
        accountInfoDefaultValue.put("avatar", AvatarConst.DEFAULT_MALE_AVATAR);
        accountInfoDefaultValue.put("avatar_origin", "");//原始头像，头像还未审核时，Avatar为空，这些用户查看自己的信息时，返回该字段
        accountInfoDefaultValue.put(UuidInfoField.NEW_REG_202306, "0");// 是否新版默认头像  注册页改造需求使用，默认头像有变化，违规回滚也要回滚正确 https://o15vj1m4ie.feishu.cn/wiki/SOS7whojBigXEQkg7x3cI8TIn6v
        accountInfoDefaultValue.put("uuid", "");
        accountInfoDefaultValue.put("create_time", "0");
        accountInfoDefaultValue.put("member_id", "0");
        accountInfoDefaultValue.put("avatar_status", "1");//头像状态 0:未审核; 1:已审核
        accountInfoDefaultValue.put("city", "");//最后活跃时所在城市名称
        accountInfoDefaultValue.put("reg_appcode", "1");//注册时的appcode
        accountInfoDefaultValue.put("reg_cloned", "1");//注册时的分身版
        accountInfoDefaultValue.put("reg_platform", "0");//注册时的平台
        accountInfoDefaultValue.put("reg_channel", "");//注册时的渠道
        accountInfoDefaultValue.put("reg_style", "");//注册方式

        /** 用户头像是否进行阿里云人脸对比认证 **/
        accountInfoDefaultValue.put("pre_profile_verify_status", ""); //审核通过前的头像认证状态，0-未认证，1-认证成功，2-认证失败
        accountInfoDefaultValue.put("pre_profile_error_msg", "");  //审核通过前的头像认证失败的原因
        accountInfoDefaultValue.put("profile_verify_status", "0"); //头像认证状态，0-未认证，1-认证成功，2-认证失败
        accountInfoDefaultValue.put("profile_error_msg", "");  //头像认证失败的原因

        /** 真人认证状态 **/
        accountInfoDefaultValue.put(UuidInfoField.REAL_PERSON_CERTIFICATION_STATUS, "-1"); // 真人认证 -1-未审核/审核失败 0审核中 1-审核成功
        accountInfoDefaultValue.put("real_person_certification", "-1"); // -1-未认证，1-认证成功，2-认证失败
        accountInfoDefaultValue.put("avatar_same_with_face", "0"); // 头像是否与活体一致 0-未认证，1-认证成功

        /** 用户头像是否进行阿里云人像认证 **/
        accountInfoDefaultValue.put("pre_avatar_face_status", "0"); //主态，审核通过前的头像人像检验状态，0-未认证，1-认证成功，2-认证失败
        accountInfoDefaultValue.put("avatar_face_status", "0"); //客态，头像人像检验状态，0-未认证，1-认证成功，2-认证失败

        /* **************来自于accounts_forum_profile表的数据***************/
        accountInfoDefaultValue.put(UuidInfoField.EXPERIENCE, "0");
        accountInfoDefaultValue.put("account_actor", "0");
        accountInfoDefaultValue.put(UuidInfoField.ACCOUNT_LEVEL, "1");//缓存中存放的是等级id
        accountInfoDefaultValue.put(UuidInfoField.DRIVER_LEVEL, "C");//驾照等级，默认为C
        accountInfoDefaultValue.put(UuidInfoField.KILOMETER, "0");//驾照行驶公里数，默认为0

        /* **************来自于accounts_info表的数据***************/
        accountInfoDefaultValue.put(UuidInfoField.UPDATE_SEX_NUMBER, "0");
        //根据update_sex_number自动计算
        accountInfoDefaultValue.put(UuidInfoField.IS_UPDATE_SEX, "0");
        accountInfoDefaultValue.put(UuidInfoField.IS_CHECK_MOBILE, "0");
        accountInfoDefaultValue.put("affectivestatus", "0");
        accountInfoDefaultValue.put(UuidInfoField.AGE, "0");
        accountInfoDefaultValue.put("baseaddr", "");
        accountInfoDefaultValue.put("hometown", "");
        accountInfoDefaultValue.put(UuidInfoField.TQCOIN, "0");
        accountInfoDefaultValue.put("sexual", "0");
        accountInfoDefaultValue.put(UuidInfoField.BIRTH, "");
        accountInfoDefaultValue.put("personal_profile_be_allow", "1");
        accountInfoDefaultValue.put(UuidInfoField.CONSTELLATION, "");
        accountInfoDefaultValue.put("dating_intention", "");
        accountInfoDefaultValue.put("personal_profile", "");
        accountInfoDefaultValue.put("personal_profile_status", "");
        accountInfoDefaultValue.put(UuidInfoField.GENDER_CERTIFICATION, "0");//性别认证
        accountInfoDefaultValue.put(UuidInfoField.ZHIMA_CERTIFICATION, "0");//芝麻认证
        accountInfoDefaultValue.put("zhima_certification_history", "0");//历史芝麻认证
        accountInfoDefaultValue.put("voice_certification", "0");//语音认证
        accountInfoDefaultValue.put(UuidInfoField.ALIYUN_FINAL_DECISION, "");//阿里风控等级
        accountInfoDefaultValue.put("face_certification", "0");//阿里云人脸活体认证
        accountInfoDefaultValue.put(UuidInfoField.SM_TYPE, "");

        accountInfoDefaultValue.put("chat_real_certification", "0"); // 业务级实名认证
        accountInfoDefaultValue.put("register_avatar_status", "0"); //注册初始头像是否违规，默认为0；0：不违规，1：违规'


        //个人简介审核通过后存储到这里
        accountInfoDefaultValue.put("pass_personal_profile", "");
        accountInfoDefaultValue.put("pass_personal_profile_status", "");
        //********新增字段
        accountInfoDefaultValue.put("education", ""); //学历  对应数据库 education_level
        accountInfoDefaultValue.put("trade", ""); //行业
        accountInfoDefaultValue.put("profession", ""); //职业
        accountInfoDefaultValue.put("height", "0"); //身高，用户未填写时，默认为0
        accountInfoDefaultValue.put("weight", "0"); //体重，用户未填写时，默认为0
        accountInfoDefaultValue.put("income", "0"); //收入，0：未填写，1：5万以下，2：5～10万，3：10～20万，4：20～30万， 5：30～50万，6：50～100万，7：100万以上

        // 主态封面
        accountInfoDefaultValue.put("home_cover", "");
        // 客态封面
        accountInfoDefaultValue.put("pass_home_cover", "");
        accountInfoDefaultValue.put("has_set_home_cover", "0");

        /* *************商城vip数据，来自于商城vip会员系统***************/
        accountInfoDefaultValue.put(UuidInfoField.VIP_LEVEL, ""); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.VIP_STATUS, "0"); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.VIP_LEVEL_TITLE, ""); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.VIP_POINT, ""); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.VIP_LEVEL_AVATAR, ""); // 下线 2023.11.09

        /* **************来自于account_identity表的数据***************/
        accountInfoDefaultValue.put(UuidInfoField.HANDSOME_STATUS, "0"); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.RICH_STATUS, "0"); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.ABILITY_STATUS, "0"); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.STAR_STATUS, "0"); // 下线 2023.11.09
        accountInfoDefaultValue.put(UuidInfoField.HOST_STATUS, "0"); // 下线 2023.11.09
//		hashKeysDefaultValue.put("whip_status", "0"); 鞭标识已下线
        accountInfoDefaultValue.put(UuidInfoField.DOCTOR_STATUS, "0"); // 下线 2023.11.09

        /* **************来自于accounts_dress表的数据***************/
        accountInfoDefaultValue.put("avatar_dress_id", "");//头像挂件 value存储dressId
        accountInfoDefaultValue.put("post_dress_id", "");//帖子背景 value存储dressId
        accountInfoDefaultValue.put("chat_dress_id", "");//聊天气泡 value存储dressId

        /* **************来自于accounts_member_info表的数据***************/
        accountInfoDefaultValue.put("login_appcode", "1");//最后登录的appcode，即当前appcode
        accountInfoDefaultValue.put("login_cloned", "1");//最后登录的cloned，即当前cloned
        accountInfoDefaultValue.put("app_version", "0");//最后使用的app版本(和设备相关)
        accountInfoDefaultValue.put("platform_id", "0");//最后使用的平台
        accountInfoDefaultValue.put("channel", "");//最后使用的平台

        /* **************由accounts_photo表的计算得出***************/
        accountInfoDefaultValue.put("photo_num", "0");//相册里的照片数量(默认为0)

        /* **************来自于accounts_achievement表的数据***************/
        accountInfoDefaultValue.put("achievement", "");//用户成就勋章

        /* **************来自于accounts_card表的数据***************/
        accountInfoDefaultValue.put("account_card_id", "");//用户佩戴的靓号id
        accountInfoDefaultValue.put("account_card_level", "");//用户佩戴的靓号等级
        accountInfoDefaultValue.put("default_card_id", "");//用户默认靓号

        /* ************** 未存表 ***************/
        accountInfoDefaultValue.put("longitude", "");// 经度 消费队列同步来
        accountInfoDefaultValue.put("latitude", "");// 纬度 消费队列同步来
        accountInfoDefaultValue.put("city_id", "");// 城市id 消费队列同步来
        accountInfoDefaultValue.put("last_app_version", "");//最后登录的app版本（和用户相关）（如果app包升级，用户未重新登录，数据不会变化）
        accountInfoDefaultValue.put("last_active_app_version", "");//最后活跃使用的平台（和用户相关） - 订阅网关心跳
        accountInfoDefaultValue.put("last_platform_id", "");//最后使用的平台（和用户相关）
        accountInfoDefaultValue.put("enable_location", ""); //用户手机是否启用定位服务，0-未启用，1-启用

        /* ************** 来自于voice_sign_info表 ***************/

        // 主态
        accountInfoDefaultValue.put("voice_sign", "");// 语音签名状态，0-审核中，1-审核通过，2-审核拒绝
        accountInfoDefaultValue.put("voice_sign_url", "");// 语音签名url
        accountInfoDefaultValue.put("voice_sign_duration", "");//语音签名时长
        accountInfoDefaultValue.put("voice_sign_audit_time", "");//语音签名审核时间
        accountInfoDefaultValue.put("voice_sign_audit_reason", "");//语音签名审核原因

        // 客态
        accountInfoDefaultValue.put("pass_voice_sign", "");// 语音签名状态，0-审核中，1-审核通过，2-审核拒绝
        accountInfoDefaultValue.put("pass_voice_sign_url", "");// 审核通过的语音签名url
        accountInfoDefaultValue.put("pass_voice_sign_duration", "");//审核通过的语音签名时长
        accountInfoDefaultValue.put("pass_voice_sign_audit_time", "");//审核通过的语音签名审核时间
        accountInfoDefaultValue.put("pass_voice_sign_audit_reason", "");//审核通过的语音签名审核原因
        accountInfoDefaultValue.put("voice_sign_pre_url", "https://vox01.jiaoliuqu.com/");  //语音签名前缀

        accountInfoDefaultValue.put("ip_pro", "");  // ip解析的省份
        accountInfoDefaultValue.put("ip_city", ""); // ip解析的城市

        accountInfoDefaultValue.put(UuidInfoField.CHANGE_AGE_STATUS, "1"); // 是否允许修改年龄 1-允许 2-不允许
        accountInfoDefaultValue.put(UuidInfoField.MAKE_FRIEND_OBJECTIVE_ID, ""); // 交友目的id
        accountInfoDefaultValue.put(UuidInfoField.MAKE_FRIEND_OBJECTIVE, ""); // 交友目的

        /* ************** 来自于accounts_introduction 表 ***************/
        // 主态
        accountInfoDefaultValue.put("my_introduction_status", "-1");// 自我介绍状态，0-审核中，1-审核通过，2-审核拒绝
        accountInfoDefaultValue.put("my_introduction_content", "");// 自我介绍内容
        accountInfoDefaultValue.put("my_introduction_imgs", "");// 自我介绍图片

        // 客态
        accountInfoDefaultValue.put("pass_introduction_status", "-1");// 通过的自我介绍状态，0-审核中，1-审核通过，2-审核拒绝
        accountInfoDefaultValue.put("pass_introduction_content", "");// 通过的自我介绍内容
        accountInfoDefaultValue.put("pass_introduction_imgs", "");// 通过的自我介绍图片

        accountInfoDefaultValue.put(UuidInfoField.PROFILE_FACE_SCORE, "");  // 颜值分
        /** 身份证生日 **/
        accountInfoDefaultValue.put(UuidInfoField.IDENTITY_NO_BIRTH, "");
        /** 用户颜值 -2认为无状态 **/
        accountInfoDefaultValue.put(UuidInfoField.FACE_LEVEL, "-2");

        /** 保密设置  
         * 以下5项缓存已删除，代码返回默认值 2023.11.03
         * **/
        secretDefaultValue.put(UuidInfoField.AGE_IS_SECRET, "0");
        secretDefaultValue.put("affectivestatus_is_secret", "0");
        secretDefaultValue.put("baseaddr_is_secret", "0");
        secretDefaultValue.put("sexual_is_secret", "0");
        secretDefaultValue.put("hometown_is_secret", "0");


        /** 用户活跃 **/
        activeDefaultValue.put("active_time", "");
        activeDefaultValue.put("pre_active_time", "");

        /** 所在地城市 **/
        addrDefaultValue.put("baseaddr_city", "");

        /** 所在地城市 **/
        hometownDefaultValue.put("hometown_city", "");


        /** 用户相册 **/
        accountPhotoValue.put("photo", "[]");
        // 真人相册
        accountPhotoValue.put("real_photo_certification", "0");  //是否包含真人认证照片，0-不包含，1-包含
        // 真人头像
        accountPhotoValue.put("real_avatar_certification", "0");  //是否真人头像(真人认证通过且头像为有效照片)，0-不是，1-是
    }

    /**
     * 真正查询数据
     *
     * @param version 0或空: 返回全路径; 其他: 返回不带host的uri
     */
    protected void getInfoFromCache(List<String> uuidList, String[] fields, String version, boolean isSecret, Map<String, Map<String, Object>> resultMap) {
        Set<String> infoFieldList = new LinkedHashSet<>();
        Set<String> secretFieldList = new LinkedHashSet<>();
        Set<String> activeFieldList = new LinkedHashSet<>();
        Set<String> addrFieldList = new LinkedHashSet<>();
        Set<String> hometownFieldList = new LinkedHashSet<>();
        Set<String> photoFieldList = new LinkedHashSet<>();
        //不存在的字段集合
        Set<String> terribleFieldList = new LinkedHashSet<>();

        if (fields == null || fields.length == 0) {
            accountInfoDefaultValue.forEach((k, v) -> infoFieldList.add(k));
            secretDefaultValue.forEach((k, v) -> secretFieldList.add(k));
            activeDefaultValue.forEach((k, v) -> activeFieldList.add(k));
            addrDefaultValue.forEach((k, v) -> addrFieldList.add(k));
            hometownDefaultValue.forEach((k, v) -> hometownFieldList.add(k));
            accountPhotoValue.forEach((k, v) -> photoFieldList.add(k));
        } else {
            for (String field : fields) {
                if (accountInfoDefaultValue.containsKey(field)) {
                    infoFieldList.add(field);
                    //如果查询vip等级，则还需要查询vip状态
                    if (UuidInfoField.VIP_LEVEL.equals(field)) {
                        infoFieldList.add(UuidInfoField.VIP_STATUS);
                    }

                    if ("pre_profile_verify_status".equals(field)) {
                        infoFieldList.add("profile_verify_status");
                        infoFieldList.add("avatar_status");
                    }

                    // 查出 is_bind_mobile 来替换 is_check_mobile, 即把 is_check_mobile 废弃
                    if (UuidInfoField.IS_CHECK_MOBILE.equals(field)) {
                        infoFieldList.add(UuidInfoField.IS_BIND_MOBILE);
                    } else if (UuidInfoField.AGE.equals(field) || UuidInfoField.CONSTELLATION.equals(field) ) {
                        //年龄需要根据birth来计算，所以查询年龄时，需要把birth也查出来
                        infoFieldList.add(UuidInfoField.BIRTH);
                    } else if ("avatar".equals(field)) {
                        infoFieldList.add("sex_type");
//                        infoFieldList.add(UuidInfoField.NEW_REG_202306);
                    }
                    
                    if (UuidInfoField.IS_CHECK_MOBILE.equals(field) || UuidInfoField.IS_BIND_MOBILE.equals(field) ) {
                        infoFieldList.add(UuidInfoField.MOBILE);
                    }
                } else if (secretDefaultValue.containsKey(field)) {
                    secretFieldList.add(field);
                } else if (activeDefaultValue.containsKey(field)) {
                    activeFieldList.add(field);
                } else if (addrDefaultValue.containsKey(field)) {
                    addrFieldList.add(field);
                    //如果是否所在地城市名称，则需要把所在地也查出来
                    if ("baseaddr_city".equals(field)) {
                        infoFieldList.add("baseaddr");
                    }
                } else if (hometownDefaultValue.containsKey(field)) {
                    hometownFieldList.add(field);
                    //如果是否家乡城市名称，则需要把家乡也查出来
                    if ("hometown_city".equals(field)) {
                        infoFieldList.add("hometown");
                    }
                } else if (accountPhotoValue.containsKey(field)) {
                    photoFieldList.add(field);
                    // 真人头像需查询真人认证状态
                    if ("real_avatar_certification".equals(field)) {
                        infoFieldList.add("real_person_certification");
                    }
                } else {
                    //等级id通过等级转换而来，所以查询等级id的时候，需要把等级查出来
                    if ("account_level_id".equals(field)) {
                        infoFieldList.add(UuidInfoField.ACCOUNT_LEVEL);
                    } else if ("medal_name".equals(field)) {
                        infoFieldList.add("account_actor");
                    } else {
                        terribleFieldList.add(field);
                    }
                }
            }
        }

        Map<String, Map<String, Object>> accountInfoMap = null;
        if (!infoFieldList.isEmpty()) {
            if ((infoFieldList.contains("profile_verify_status") || infoFieldList.contains("pre_profile_verify_status"))) {
                infoFieldList.add("real_person_certification");
            }

            if (infoFieldList.contains(UuidInfoField.GENDER_CERTIFICATION)) {
                infoFieldList.add(UuidInfoField.ZHIMA_CERTIFICATION);
            }
            accountInfoMap = this.getAccountInfo(uuidList, infoFieldList.toArray(new String[0]), version, isSecret);
        }

        Map<String, Map<String, String>> secertConfigMap = null;
        if (!secretFieldList.isEmpty()) {
            secertConfigMap = accountsPersonalInfoService.mGetSecretConfigV2(uuidList.toArray(new String[0]), secretFieldList.toArray(new String[0]));
        }

        Map<String, String> activeConfigMap = null;
        if (activeFieldList.contains("active_time")) {
            activeConfigMap = accountsMemberInfoService.findActiveTime(uuidList.toArray(new String[0]));
        }

        Map<String, List<Long>> activeTimeMap = null;
        if (activeFieldList.contains("pre_active_time")) {
            activeTimeMap = accountsMemberInfoService.mGetAccountActiveTime(uuidList, 2);
        }

        Map<String, Map<String, String>> addressMap = null;
        Map<String, Map<String, String>> hometownMap = null;
        if (!addrFieldList.isEmpty()) {
            addressMap = regionService.mGetBaseaddrName(accountInfoMap);
        }
        if (!hometownFieldList.isEmpty()) {
            hometownMap = regionService.mGetRegionName(accountInfoMap, "hometown");
        }

        Map<String, List<Map<String, String>>> photoMap = null;
        //是否包含real_photo_certification字段
        boolean containRealPhotoCertification = false;
        if (!photoFieldList.isEmpty()) {
            photoMap = accountsPhotoService.mGetAccountsPhoto(uuidList.toArray(new String[0]));
            if (photoFieldList.contains("real_photo_certification")) {
                containRealPhotoCertification = true;
            }
        }

        for (String uuid : uuidList) {
            Map<String, Object> uuidInfoMap = resultMap.get(uuid);
            if (accountInfoMap != null) {
                uuidInfoMap.putAll(accountInfoMap.get(uuid));
            }

            if (secertConfigMap != null) {
                uuidInfoMap.putAll(secertConfigMap.get(uuid));
            }

            if (activeConfigMap != null) {
                uuidInfoMap.put("active_time", MapUtils.getString(activeConfigMap, uuid, ""));
            }

            if (activeTimeMap != null) {
                Long preActiveTime = activeTimeMap.get(uuid).get(1);
                uuidInfoMap.put("pre_active_time", Objects.equals(preActiveTime, 0L) ? "" : String.valueOf(preActiveTime));
            }

            if (addressMap != null) {
                uuidInfoMap.put("baseaddr_city", MapUtils.getString(addressMap.get(uuid), "city_name", ""));
            }

            if (hometownMap != null) {
                uuidInfoMap.put("hometown_city", MapUtils.getString(hometownMap.get(uuid), "city_name", ""));
            }

            if (photoMap != null) {
                List<Map<String, String>> photoMapList = photoMap.getOrDefault(uuid, Collections.emptyList());
                for (Map<String, String> onePhoto : photoMapList) {
                    String photoUrl = AvatarHandleService.getAvatarOfSavePhoto(onePhoto.get("photo_url"));
                    onePhoto.put("photo_url", photoUrl);
                }
//                LOGGER.info("photo uuid:{}|photoMapList:{}", uuid, JsonUtils.objectToString(photoMapList));
                uuidInfoMap.put("photo", photoMapList);
                if (containRealPhotoCertification && CollectionUtils.isNotEmpty(photoMapList)) {
                    int hasRealPhotoCert = photoMapList.stream().anyMatch(phMap -> AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(MapUtils.getInteger(phMap, "verify_status"))
                            && AccountsPhoto.Status.PASS.getValue() == MapUtils.getInteger(phMap, "status", 1)) ? 1 : 0;
                    uuidInfoMap.put("real_photo_certification", String.valueOf(hasRealPhotoCert));
                }
            }

            // 真人头像处理
            if (photoFieldList.contains("real_avatar_certification")) {
                Integer realPersonCertification = MapUtils.getInteger(uuidInfoMap, "real_person_certification");
                if (Objects.equals(realPersonCertification, 1) && photoMap != null) {
                    List<Map<String, String>> photoMapList = photoMap.getOrDefault(uuid, Collections.emptyList());
                    int hasRealAvatarCert = photoMapList.stream().anyMatch(
                            phMap -> Objects.equals(MapUtils.getInteger(phMap, "seq"), 1)
                                    && AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(MapUtils.getInteger(phMap, "verify_status"))
                                    && AccountsPhoto.Status.PASS.getValue() == MapUtils.getInteger(phMap, "status", 1)) ? 1 : 0;
                    uuidInfoMap.put("real_avatar_certification", String.valueOf(hasRealAvatarCert));
                } else {
                    uuidInfoMap.put("real_avatar_certification", "0");
                }
            }

            //如果是获取接口不支持的字段，直接将字段值设置为空
            terribleFieldList.forEach(field -> uuidInfoMap.put(field, ""));
        }

    }

    /**
     * 从用户信息缓存{@link RedisKeyConstant#ACCOUNT_INFOS_UUID}获取数据
     *
     * @param version 0或空: 返回全路径; 其他: 返回不带host的uri
     */
    private Map<String, Map<String, Object>> getAccountInfo(List<String> uuidList, String[] fields, String version, boolean isSecret) {
        RedisSerializer<String> redisSerializer = accountStringRedisTemplate.getStringSerializer();
        byte[][] fieldByte = Arrays.stream(fields).map(v -> redisSerializer.serialize(v)).collect(Collectors.toList()).toArray(new byte[fields.length][]);
        List<Object> valuesList = accountStringRedisTemplate.executePipelined((RedisConnection connection) -> {
            uuidList.forEach(uuid -> {
                byte[] keyByte = redisSerializer.serialize(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid));
                connection.hMGet(keyByte, fieldByte);
            });
            return null;
        }, redisSerializer);

        Map<String, Long> uuid2LocationMap = new HashMap<>();
        Map<Long, Map<String, Object>> locationMap = new HashMap<>();
        if (fields.length > 0 && (Arrays.asList(fields).contains("ip_pro") || Arrays.asList(fields).contains("ip_city"))) {
            Map<String, LocationInfoVO> ipLocationMap = accountsLocationInfoService.mGetAccountLocationInfoInCache(uuidList);
            uuidList.forEach(uuid -> {
                LocationInfoVO locationInfoVO = ipLocationMap.get(uuid);
                if (locationInfoVO != null && StringUtils.isNotBlank(locationInfoVO.getCityId())) {
                    uuid2LocationMap.put(uuid, Long.parseLong(locationInfoVO.getCityId()));
                }
            });
            if (!uuid2LocationMap.isEmpty()) {
                locationMap = regionService.mgetInfoByCityId(new ArrayList<>(uuid2LocationMap.values()));
            }
        }

        Map<String, Map<String, Object>> accountInfoMap = new HashMap<>();
        for (int i = 0; i < uuidList.size(); i++) {
            String uuid = uuidList.get(i);
            List<Object> values = valuesList.isEmpty() ? Collections.EMPTY_LIST : (List<Object>) valuesList.get(i);

            Map<String, Object> infoMap = new HashMap<>();
            for (int j = 0; j < values.size(); j++) {
                String field = fields[j];
                if ("uuid".equals(field)) {
                    infoMap.put("uuid", uuid);
                    continue;
                }

                Object value = values.get(j);
                String stringValue = value == null ? "" : value.toString();

                if (StringUtils.isBlank(stringValue)) {
                    stringValue = MapUtils.getString(accountInfoDefaultValue, field, "");
                }
                infoMap.put(field, this.infoValuePostProcess(field, stringValue, version, isSecret));
            }

            // map转换
            if (fields.length > 0 && (Arrays.asList(fields).contains("profile_verify_status") || Arrays.asList(fields).contains("pre_profile_verify_status"))) {
                if (Arrays.asList(fields).contains("profile_verify_status")) {

                    infoMap.put("profile_verify_status", MapUtils.getString(infoMap, "real_person_certification", "2"));
                }
                if (Arrays.asList(fields).contains("pre_profile_verify_status")) {
                    infoMap.put("pre_profile_verify_status", MapUtils.getString(infoMap, "real_person_certification", "2"));
                }
            }
            if (fields.length > 0 && (Arrays.asList(fields).contains(UuidInfoField.GENDER_CERTIFICATION))) {
                infoMap.put("gender_certification", MapUtils.getString(infoMap, UuidInfoField.ZHIMA_CERTIFICATION, "0"));
            }

            // 2024.05.27 修改判断，改为通过mobile判断
            if (fields.length > 0 && (Arrays.asList(fields).contains(UuidInfoField.IS_CHECK_MOBILE))) {
                infoMap.put("is_check_mobile", String.valueOf(MobileService.isBindMobile(MapUtils.getString(infoMap, UuidInfoField.MOBILE, ""))));
            }
            // 2024.05.27 修改判断，改为通过mobile判断
            if (fields.length > 0 && (Arrays.asList(fields).contains(UuidInfoField.IS_BIND_MOBILE))) {
                infoMap.put("is_bind_mobile", String.valueOf(MobileService.isBindMobile(MapUtils.getString(infoMap, UuidInfoField.MOBILE, ""))));
            }

            //修复女用户下发默认男头像问题
            if (fields.length > 0 && Arrays.asList(fields).contains("avatar")) {
                String avatar = MapUtils.getString(infoMap, "avatar", "");
                Integer sexType = MapUtils.getInteger(infoMap, "sex_type");
                if (StringUtils.isBlank(avatar)) {
                    // 为空 应该不会
                    infoMap.put("avatar", AvatarHandleService.getAvatarOfSavePhoto(AccountsService.getAvatarBySexType(sexType))); // no
                } else {
                    // 不为空
                    if (AvatarConst.DEFAULT_MALE_AVATAR.equals(AvatarHandleService.getAvatarOfSavePhoto(avatar))) {
                        if (GenderEnum.FEMALE.getValue().equals(sexType)) {
                            if (AvatarHandleService.isURL(avatar)) {
                                infoMap.put("avatar", AccountsService.getAvatarBySexType(sexType)); // no
                            } else {
                                infoMap.put("avatar", AvatarConst.DEFAULT_FEMALE_AVATAR);
                            }
                        }
                    }
                }
            }

            if (fields.length > 0 && (Arrays.asList(fields).contains("ip_pro") || Arrays.asList(fields).contains("ip_city"))) {
                String cityName = "";
                String provinceName = "";

                Long ipCity = uuid2LocationMap.get(uuid);
                if (ipCity != null) {
                    Map<String, Object> map = locationMap.get(ipCity);
                    cityName = MapUtils.getString(map, "city_name", "").replace("市", "");
                    provinceName = MapUtils.getString(map, "province_name", "").replace("省", "");
                }

                infoMap.put("ip_pro", provinceName);
                infoMap.put("ip_city", cityName);
            }

            // 特殊处理马甲包变更显示默认头像的需求，应对马甲包审核
            // 废弃功能 67已下线
            if (CloneDefaultAvatarService.needReplaceDefaultAvatar()) {
                if (fields.length > 0 && Arrays.asList(fields).contains("avatar")) {
                    String avatar = MapUtils.getString(infoMap, "avatar");

                    if (avatar.equals(AvatarConst.DEFAULT_MALE_AVATAR)) {
                        String defaultAvatar = CloneDefaultAvatarService.getDefaultAvatar(1);
                        if (StringUtils.isNotBlank(defaultAvatar)) {
                            infoMap.put("avatar", defaultAvatar);
                        }
                    }

                    if (avatar.equals(AvatarConst.DEFAULT_FEMALE_AVATAR)) {
                        String defaultAvatar = CloneDefaultAvatarService.getDefaultAvatar(2);
                        if (StringUtils.isNotBlank(defaultAvatar)) {
                            infoMap.put("avatar", defaultAvatar);
                        }
                    }
                }

                if (fields.length > 0 && Arrays.asList(fields).contains("avatar_origin")) {
                    String avatar = MapUtils.getString(infoMap, "avatar_origin");

                    if (avatar.equals(AvatarConst.DEFAULT_MALE_AVATAR)) {
                        String defaultAvatar = CloneDefaultAvatarService.getDefaultAvatar(1);
                        if (StringUtils.isNotBlank(defaultAvatar)) {
                            infoMap.put("avatar_origin", defaultAvatar);
                        }
                    }

                    if (avatar.equals(AvatarConst.DEFAULT_FEMALE_AVATAR)) {
                        String defaultAvatar = CloneDefaultAvatarService.getDefaultAvatar(2);
                        if (StringUtils.isNotBlank(defaultAvatar)) {
                            infoMap.put("avatar_origin", defaultAvatar);
                        }
                    }
                }
            }

            // 下发前处理null值
            emptyValueProcess(infoMap);

            accountInfoMap.put(uuid, infoMap);
        }

        this.infoMapPostProcess(accountInfoMap);
        return accountInfoMap;
    }

    private void emptyValueProcess(Map<String, Object> infoMap) {
        for (Map.Entry<String, Object> entry : infoMap.entrySet()) {
            if (entry.getValue().equals("null") ||
                    entry.getValue().equals("null:null") ||
                    entry.getValue().equals("null,null") ||
                    entry.getValue().equals("null;null")) {
                entry.setValue("");
            }
        }
    }

    /**
     * @param key
     * @param value
     * @param version  0或空: 返回全路径; 其他: 返回不带host的uri
     * @param isSecret
     * @return
     */
    private String infoValuePostProcess(String key, String value, String version, boolean isSecret) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        if (UuidInfoField.ACCOUNT_LEVEL.equals(key)) {
            //老用户缓存中account_level可能存的是0，因此这里需要做特殊处理
            Integer levelId = Math.max(1, StringUtil.toInteger(value, 1));
            value = String.valueOf(levelId);
        } else if ("avatar".equals(key) || "avatar_origin".equals(key)) {//图片需要特殊处理
            // 2020.05.29 修复客户端bug
            value = AccountsInfoService.fixUrl(value);
            value = avatarHandleService.getAvatarByVersion(value, version);
        } else if (UuidInfoField.IS_UPDATE_SEX.equals(key)) {//注册成功后不允许修改性别
            value = "0";
        } else if (UuidInfoField.MOBILE.equals(key) && isSecret) {//手机是否需要加密
            value = StringUtil.mobileSecret(value);
        } else if (UuidInfoField.BIRTH.equals(key) && "0".equals(value)) {
            return "";
        }
        return value;
    }

    private void infoMapPostProcess(Map<String, Map<String, Object>> accountInfoMap) {
        Set<Long> medalIdList = new HashSet<>();

        accountInfoMap.forEach((uuid, infoMap) -> {
            //根据生日计算年龄
            if (infoMap.containsKey(UuidInfoField.AGE)) {
                long birth = MapUtils.getLongValue(infoMap, UuidInfoField.BIRTH);
                infoMap.put("age", birth == 0 ? "" : String.valueOf(ToolsService.calAgeFromBirth(birth)));
            }
            //根据生日计算星座
            if (infoMap.containsKey(UuidInfoField.CONSTELLATION)) {
                long birth = MapUtils.getLongValue(infoMap, UuidInfoField.BIRTH);
                infoMap.put("constellation", birth == 0 ? "" : DateUtil.getConstellationFromSeconds(birth));
            }

            if (infoMap.containsKey("account_actor")) {
                Long accountMedal = MapUtils.getLong(infoMap, "account_actor", 0L);
                infoMap.put("medal_name", "");
                if (accountMedal > 0) {
                    medalIdList.add(accountMedal);
                }
            }

            //等级已下线，是否可以删除？
            if (infoMap.containsKey(UuidInfoField.ACCOUNT_LEVEL)) {
                infoMap.put("account_level_id", infoMap.get(UuidInfoField.ACCOUNT_LEVEL));
                infoMap.put("account_level", String.valueOf(Math.max((MapUtils.getIntValue(infoMap, UuidInfoField.ACCOUNT_LEVEL, 1) - 1), 0)));
                infoMap.put("honor_name", "");
            }
        });

        if (!medalIdList.isEmpty()) {
            //如果有返回头衔，则一起返回头衔名称，为了社区的事业
            Map<Long, Map<String, String>> accountMedalMap = accountsMedalService.mGetInfo(new ArrayList<>(medalIdList), "medal_name");
            accountInfoMap.forEach((uuid, infoMap) -> {
                Long accountMedal = MapUtils.getLong(infoMap, "account_actor", 0L);
                infoMap.put("medal_name", MapUtils.getString(accountMedalMap.get(accountMedal), "medal_name", ""));
            });
        }
    }

}
