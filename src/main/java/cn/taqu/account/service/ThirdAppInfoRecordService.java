package cn.taqu.account.service;

import cn.taqu.account.dao.ThirdAppInfoRecordDao;
import cn.taqu.account.model.ThirdAppInfoRecord;
import cn.taqu.core.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
public class ThirdAppInfoRecordService {

    @Autowired
    private ThirdAppInfoRecordDao thirdAppInfoRecordDao;

    @Transactional
    public void addOrUpdate(String toekn,String thirdAppInfo){
        if(null == toekn || null == thirdAppInfo){
            return;
        }

        ThirdAppInfoRecord record = thirdAppInfoRecordDao.findOneByToken(toekn);
        if(null == record){
            record = new ThirdAppInfoRecord();
            record.setToken(toekn);
            record.setThird_app_info(thirdAppInfo);
            record.setCreate_time(DateUtil.currentTimeSeconds());
            record.setUpdate_time(DateUtil.currentTimeSeconds());
            thirdAppInfoRecordDao.merge(record);
        }else {
            //第三方应用信息不一样时才进行保存
            if(!thirdAppInfo.equals(record.getThird_app_info())){
                record.setThird_app_info(thirdAppInfo);
                record.setUpdate_time(DateUtil.currentTimeSeconds());
                thirdAppInfoRecordDao.merge(record);
            }
        }
    }
}
