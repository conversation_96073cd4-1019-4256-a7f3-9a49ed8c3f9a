package cn.taqu.account.service;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.core.utils.JsonUtils;

/**
 * 用户黑名单相关Service
 *
 * <AUTHOR>
 * 2017年8月8日 上午10:08:31
 */
@Service
@Transactional
public class AccountsBlackListService{
	private static Logger logger = LoggerFactory.getLogger(AccountsBlackListService.class);

    @Autowired
    private AccountsPhotoService accountsPhotoService;

    public void afterBatUuids(List<String> accountUuidList,String operator) {
        if (CollectionUtils.isEmpty(accountUuidList)) {
            return;
        }
        logger.info("黑名单后续处理：list={}", JsonUtils.objectToString(accountUuidList));
        // 2020.04.08 小君说，加入黑名单后，不修改昵称审核状态
//        nickNameRiskVerifyService.updateStatusByUuid(accountUuidList, 2);
        accountsPhotoService.mdelAccountNoCheckPhoto(accountUuidList, operator);

//        accountUuidList.stream().forEach(uuid -> accountsService.setNicknameForBackstage(uuid, "昵称关禁闭中", operator, true));
    }


}
