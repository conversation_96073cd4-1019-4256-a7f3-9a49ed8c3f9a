package cn.taqu.account.service;

import cn.taqu.account.dao.AccountsCertificationLogDao;
import cn.taqu.account.model.AccountsCertificationLog;
import cn.taqu.core.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 实名认证记录表
 * 
 * <AUTHOR>
 * @date 2020/12/01
 */
@Service
public class AccountsCertificationLogService {

    @Autowired
    private AccountsCertificationLogDao accountsCertificationLogDao;

    @Transactional
    public void create(String accountUuid, String oldAccountReward, String newAccountReward, String operatorName) {
        AccountsCertificationLog accountsCertificationLog = new AccountsCertificationLog();
        accountsCertificationLog.setAccount_uuid(accountUuid);
        accountsCertificationLog.setContent("支付宝账号: " + oldAccountReward + " -> " + newAccountReward);
        accountsCertificationLog.setCreate_time(DateUtil.currentTimeSeconds());
        accountsCertificationLog.setOperator_name(StringUtils.trimToEmpty(operatorName));
        this.accountsCertificationLogDao.merge(accountsCertificationLog);
    }
}
