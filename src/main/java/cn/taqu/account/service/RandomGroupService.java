package cn.taqu.account.service;

import cn.taqu.account.common.ActionTypeEnum;
import cn.taqu.account.common.ClonedEnum;
import cn.taqu.account.common.RandomGroupStatusTypeEnum;
import cn.taqu.account.dao.RandomGroupDao;
import cn.taqu.account.dao.RandomNicknameDao;
import cn.taqu.account.model.RandomGroup;
import cn.taqu.account.model.RandomNickname;
import cn.taqu.account.search.RandomGroupSearch;
import cn.taqu.core.log.Log;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.utils.DateUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zy
 * @Date: 2020/6/24 10:19
 */
@Service
public class RandomGroupService {

    @Autowired
    private RandomGroupDao randomGroupDao;
    @Autowired
    private RandomNicknameDao randomNicknameDao;

    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;
    @Autowired
    private RandomNicknameService randomNicknameService;

    public PageData<RandomGroup> findPageList(RandomGroupSearch search) {
        PageData<RandomGroup> pageData = randomGroupDao.findPageList(search);
        List<RandomGroup> randomGroupList = pageData.getData();
        List<RandomNickname> countList = randomNicknameDao.countNum();
        Map<Long, Integer> map = countList.stream().collect(Collectors.toMap(RandomNickname::getGroupId, RandomNickname::getCountNum));
        randomGroupList.stream().forEach(randomGroup -> {
            Integer countNum = MapUtils.getInteger(map, randomGroup.getId(), 0);
            randomGroup.setCountNum(countNum);
        });
        return new PageData<>(pageData.getTotal(), randomGroupList, pageData.getPage(), search.getRows());
    }

    public List<RandomGroup> findOpenStatusList() {
        return randomGroupDao.findListByStatus(RandomGroupStatusTypeEnum.OPEN.getCode());
    }

    @Transactional
    public Long add(String groupName, String partsOfSpeech, String status) {
        RandomGroup randomGroup = new RandomGroup();
        randomGroup.setRandomGroupName(groupName);
        randomGroup.setPartsOfSpeech(Integer.parseInt(partsOfSpeech));
        randomGroup.setStatus(Integer.parseInt(status));
        randomGroup.setCreateTime(DateUtil.currentTimeSeconds());
        randomGroup.setUpdateTime(DateUtil.currentTimeSeconds());
        randomGroup = randomGroupDao.merge(randomGroup);
        return randomGroup.getId();
    }

    @Transactional
    public void edit(Long id, String groupName, String status, String partsOfSpeech) {
        RandomGroup randomGroup = randomGroupDao.getById(id);
        randomGroup.setRandomGroupName(groupName);
        randomGroup.setStatus(Integer.parseInt(status));
        randomGroup.setPartsOfSpeech(Integer.parseInt(partsOfSpeech));
        randomGroup.setUpdateTime(DateUtil.currentTimeSeconds());
        randomGroupDao.merge(randomGroup);
        //启用的时候，重新加入缓存
        if (Integer.parseInt(status) == RandomGroupStatusTypeEnum.OPEN.getCode().intValue()) {
            List<RandomNickname> randomNicknameList = randomNicknameDao.findByGroupId(randomGroup.getId());
            Log.info("q");
            randomNicknameList.stream().forEach(randomNickname -> {
                if (ClonedEnum.TAQU.getCode().longValue() == randomNickname.getCloned().longValue()) {
                    accountsThirdPartyService.updateRandomNickname
                            (Arrays.asList(randomNickname.getRandomNickname()),
                                    randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                    ActionTypeEnum.ADD);
                } else if (ClonedEnum.PEIPEI.getCode().longValue() == randomNickname.getCloned().longValue()) {
                    accountsThirdPartyService.updatePeipeiRandomNickname
                            (Arrays.asList(randomNickname.getRandomNickname()),
                                    randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                    ActionTypeEnum.ADD);
                }else{
                    accountsThirdPartyService.updateRandomNickname
                            (Arrays.asList(randomNickname.getRandomNickname()),
                                    randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                    ActionTypeEnum.ADD);
                }

            });
        }
        //关闭的时候，删除缓存
        if (Integer.parseInt(status) == RandomGroupStatusTypeEnum.CLOSED.getCode().intValue()) {
            List<RandomNickname> randomNicknameList = randomNicknameDao.findByGroupId(randomGroup.getId());
            randomNicknameList.stream().forEach(randomNickname -> {

                if (ClonedEnum.TAQU.getCode().longValue() == randomNickname.getCloned().longValue()) {
                    accountsThirdPartyService.updateRandomNickname
                            (Arrays.asList(randomNickname.getRandomNickname()),
                                    randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                    ActionTypeEnum.DELETE);

                } else if (ClonedEnum.PEIPEI.getCode().longValue() == randomNickname.getCloned().longValue()) {
                    accountsThirdPartyService.updatePeipeiRandomNickname
                            (Arrays.asList(randomNickname.getRandomNickname()),
                                    randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                    ActionTypeEnum.DELETE);
                }else{
                    accountsThirdPartyService.updateRandomNickname
                            (Arrays.asList(randomNickname.getRandomNickname()),
                                    randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                    ActionTypeEnum.DELETE);
                }
            });
        }
    }

    @Transactional
    public void deleteGroupById(Long id) {
        RandomGroup randomGroup = randomGroupDao.getById(id);
        List<RandomNickname> randomNicknameList = randomNicknameDao.findByGroupId(id);

        randomGroupDao.remove(randomGroup);
        randomNicknameDao.remove(randomNicknameList);


        randomNicknameList.stream().forEach(randomNickname -> {
            if (ClonedEnum.TAQU.getCode().longValue() == randomNickname.getCloned().longValue()) {
                accountsThirdPartyService.updateRandomNickname
                        (Arrays.asList(randomNickname.getRandomNickname()),
                                randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                ActionTypeEnum.DELETE);

            } else if (ClonedEnum.PEIPEI.getCode().longValue() == randomNickname.getCloned().longValue()) {
                accountsThirdPartyService.updatePeipeiRandomNickname
                        (Arrays.asList(randomNickname.getRandomNickname()),
                                randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                ActionTypeEnum.DELETE);
            }else {
                accountsThirdPartyService.updateRandomNickname
                        (Arrays.asList(randomNickname.getRandomNickname()),
                                randomNicknameService.switchType(randomGroup.getPartsOfSpeech(), randomNickname.getSexType()),
                                ActionTypeEnum.DELETE);
            }

        });


    }
}
