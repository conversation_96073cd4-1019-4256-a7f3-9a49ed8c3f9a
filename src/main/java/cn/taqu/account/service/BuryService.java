package cn.taqu.account.service;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.account.event.AccountBehaviorReporter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClientException;
import org.apache.pulsar.client.api.Schema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import cn.taqu.account.common.BindMobileTypeEnum;
import cn.taqu.account.common.CertTypeEnum;
import cn.taqu.account.common.FinishIncentiveTaskEnum;
import cn.taqu.account.common.PhotoOrignCheckEnum;
import cn.taqu.account.common.RecsysReportServerEnum;
import cn.taqu.account.common.RiskDetectEnum;
import cn.taqu.account.constant.AbRuleCode;
import cn.taqu.account.constant.AccountBizDataDtoType;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RegStyle;
import cn.taqu.account.constant.TemplateStringConst;
import cn.taqu.account.cron.TaskHelper;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dao.AccountsPhotoImgCompareLogDao;
import cn.taqu.account.dao.AccountsPhotoImgQualityLogDao;
import cn.taqu.account.event.AccountEvent;
import cn.taqu.account.event.EventConst;
import cn.taqu.account.event.EventTrackingDO;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.Accounts;
import cn.taqu.account.model.AccountsCertificationChangeLog;
import cn.taqu.account.model.AccountsMemberInfo;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.AccountsPhotoImgCompareLog;
import cn.taqu.account.model.AccountsPhotoImgQualityLog;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.MultiMqClient;
import cn.taqu.core.common.client.MultiMqClientFactory;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.SpringContextHolder;
import cn.taqu.core.utils.StringUtil;

import static cn.taqu.account.event.EventConst.*;

/**
 * 埋点service
 */
@Service
public class BuryService implements PulsarFactory.ConfigListener {
	private static final Logger LOGGER = LoggerFactory.getLogger(BuryService.class);

	@Autowired
	@Qualifier("accountStringRedisTemplate")
	private StringRedisTemplate accountStringRedisTemplate;

	@Autowired
	private AccountsInfoService accountsInfoService;
	@Autowired
	private AccountsService accountsService;
	@Autowired
	private AccountsCardService accountsCardService;
	@Autowired
	private AccountsManager accountsManager;
	@Autowired
	private AccountsPhotoDao accountsPhotoDao;
	@Autowired
	private AccountsPhotoService accountsPhotoService;
	@Autowired
	private AccountsMemberInfoService accountsMemberInfoService;
	@Autowired
	private AccountsPhotoImgQualityLogDao accountsPhotoImgQualityLogDao;
	@Autowired
	private AccountsPhotoImgCompareLogDao accountsPhotoImgCompareLogDao;
    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;
	@Autowired
	private AccountBehaviorReporter behaviorReporter;

	private static volatile Producer<String> userRegProducer;

	private static volatile Producer<String> userAvatarUpdateProducer;

	public BuryService(PulsarFactory pulsarFactory) throws PulsarClientException {
		userRegProducer = pulsarFactory.producer(EventConst.resolveTopic(ADMP_USER_REG_PULSAR_TOPIC));
		userAvatarUpdateProducer  = pulsarFactory.producer(EventConst.resolveTopic(ACCOUNT_USER_REAL_AVATAR_UPDATE));
		pulsarFactory.addListener(this);
	}

	@Override
	public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
		Producer<String> origin = userRegProducer;
		userRegProducer = factory.producer(EventConst.resolveTopic(ADMP_USER_REG_PULSAR_TOPIC));
		origin.closeAsync();

		origin = userAvatarUpdateProducer;
		userAvatarUpdateProducer  = factory.producer(EventConst.resolveTopic(ACCOUNT_USER_REAL_AVATAR_UPDATE));
		origin.closeAsync();
	}

	private static MqClient getMqClient() {
		return MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
	}

	private static MultiMqClient getMultiMqClient() {
		return MultiMqClientFactory.createTqMq(SoaServer.GO.MULTI_TQMQ);
	}

	/**
	 * 30分钟
	 */
	private static long DELAY_TIME_30_MINUTE = 60 * 30;

	/**
	 * 数据中心埋点
	 * @param type 数据类型
	 * @param oper 操作类型
	 * @param id id	//改传uuid
	 * @param appcode 应用码 1:他趣
	 * @param extra 额外数据
	 */
	public boolean toDataCenter(String type, String oper, String id, Integer appcode, Map<String,Object> extra) {
		Map<String, Object> data = new HashMap<>();
		data.put("type", type);
		data.put("oper", oper);
		data.put("id", id);
		data.put("create_time", DateUtil.currentTimeSeconds());
		data.put("appcode", appcode == null ? 1 : appcode);
		if(extra == null) {
			extra = new HashMap<>();
		}
		extra.put("otracer_id", SoaBaseParams.fromThread().getDistinctRequestId());
		data.put("extra", extra);

		String redisKey = RedisKeyConstant.DATA_CENTER_BURY.getPattern();
		String msg = JsonUtils.objectToString(data);
		accountStringRedisTemplate.opsForList().rightPush(redisKey, msg);
		LOGGER.debug("添加画像中心埋点数据到TQMQ, {}", msg);
		return true;
	}

	public void sendBuryDataToDataCenter() {
		String redisKey = RedisKeyConstant.DATA_CENTER_BURY.getPattern();
		while(true) {
			if(TaskHelper.podStopping()){
				return;
			}
			String dataString = accountStringRedisTemplate.opsForList().leftPop(redisKey);
			if(StringUtils.isBlank(dataString)) {
				return;
			}

			Map<String, Object> data = JsonUtils.stringToObject(dataString, new TypeReference<Map<String, Object>>(){});
			MqResponse response = getMqClient().push("dataCenter", data, null);
			if(response.fail()) {
				LOGGER.error("画像系统埋点数据[{}]添加失败,添加的队列:dataCenter, 失败原因:{}, 失败码:{}", dataString, response.getMsg(), response.getCode());
			} else {
				LOGGER.debug("画像系统埋点数据[{}]添加成功,添加的队列:dataCenter", data);
			}
		}
	}


    public boolean toAntispam(String token, Long createTime, Integer platformId, String type) {
		/**
		 * TODO：命中时，Redis+MQ推送 改为 直接MQ推送
		 */
        Map<String, Object> data = new HashMap<>();
        data.put("token", token);
        data.put("postTime",StringUtil.nullNumberToEmptyString(createTime));
        data.put("platformId", StringUtil.nullNumberToEmptyString(platformId));
        data.put("type", type);

        String redisKey = RedisKeyConstant.ANTISPAM_BURY.getPattern();
        String msg = JsonUtils.objectToString(data);
        accountStringRedisTemplate.opsForList().rightPush(redisKey, msg);
        LOGGER.debug("添加风控系统埋点数据到TQMQ, {}", msg);
        return true;
    }

	public void sendBuryDataToAntispam() {
	    String redisKey = RedisKeyConstant.ANTISPAM_BURY.getPattern();
	    while(true) {
	        if(TaskHelper.podStopping()){
	            return;
	        }
	        String dataString = accountStringRedisTemplate.opsForList().leftPop(redisKey);
	        if(StringUtils.isBlank(dataString)) {
	            return;
	        }

	        Map<String, Object> data = JsonUtils.stringToObject(dataString, new TypeReference<Map<String, Object>>(){});
	        MqResponse response = getMqClient().push(CommConst.MQ_EQUIPMENT_ACTIVATION, data, null);
	        if(response.fail()) {
	            LOGGER.error("风控系统埋点数据[{}]添加失败, 失败原因:{}, 失败码:{}", dataString, response.getMsg(), response.getCode());
	        } else {
//	            LOGGER.info("风控系统埋点数据[{}]添加成功", data);
	        }
	    }
	}

//	@Deprecated
//	public void sendNicknameToContentFilter(String uuid, String nickname, Integer appcode) {
//		Map<String, String> data = new HashMap<>();
//		data.put("account_uuid", uuid);
//		data.put("account_name", nickname);
//		data.put("type", "user");
//		data.put("appcode", appcode == null ? "1" : appcode.toString());
//		this.sendToContentFilterQueue(data);
//	}

//	@Deprecated
//	public void sendToContentFilterQueue() {
//		String redisKey = RedisKeyConstant.CONTENT_FILTER_QUEUE_NICKNAME.getPattern();
//		while(true) {
//			if(TaskHelper.podStopping()){
//				return;
//			}
//			String dataString = accountStringRedisTemplate.opsForList().leftPop(redisKey);
//			if(StringUtils.isBlank(dataString)) {
//				return;
//			}
//
//			Map<String, String> data = JsonUtils.stringToObject(dataString, new TypeReference<Map<String, String>>(){});
//			this.sendToContentFilterQueue(data);
//		}
//	}

//	//昵称达观检测 2024.01.11 可以不推了
//	@Deprecated
//	public void sendToContentFilterQueue(Map<String, String> data) {
//		MqResponse response = getMqClient().push("content_filter_queue", data, null);
//		LOGGER.info("推送个人简介信息到社区后台,内容={}", JSON.toJSON(data));
//		if(response.fail()) {
//			LOGGER.error("发送昵称筛选[{}]到MQ失败,添加的队列:content_filter_queue, 失败原因:{}, 失败码:{}", data, response.getMsg(), response.getCode());
//		}
//	}

	public void pushForumDoPublish(Map<String, ? extends  Object> data) {
		MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("preheat_find_friend", data, null);
		if (mqResponse.fail()) {
			LOGGER.error("修改用户信息推送至tqmq失败，用户uuid:{}, 失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
		}
	}

	public static void pushDataToHandleZhimaAuthDealQueue(String accountUuid) {
	    Map<String, String> data = Maps.newHashMap();
        data.put("account_uuid", accountUuid);
		MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("handleZhimaAuthDeal", data, null);
		if (mqResponse.fail()) {
			LOGGER.error("确认用户认证信息，认证成功后推送数据至tqmq失败，数据:{}， 失败码:{}，失败原因:{}",JsonUtils.objectToString(data), mqResponse.getCode(), mqResponse.getMsg());
		}
	}

	@Deprecated
	public void pushDataToLiveStatisticQueue(String type, String oper, String id, Map<String,Object> extra) {
		Map<String, Object> data = Maps.newHashMap();
		data.put("type", type);
		data.put("oper", oper);
		data.put("id", id);
		data.put("create_time", DateUtil.currentTimeSeconds());
		if(extra == null) {
			extra = new HashMap<>();
		}
		data.put("extra", extra);
		MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("live_statistic", data, null);
		if (mqResponse.fail()) {
			LOGGER.error("确认用户认证信息，推送认证结果至tqmq失败，数据:{}， 失败码:{}，失败原因:{}",JsonUtils.objectToString(data), mqResponse.getCode(), mqResponse.getMsg());
		}
	}

	public void accountDestroy(String accountUuid) {
		//通知搜索账号已注销
		MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
		Map<String, Object> map1 = new HashMap<>();
		map1.put("uuid", accountUuid);
		map1.put("flag", "delete");
		MqResponse response = mqClient.push(AccountsService.SEARCH_MQ_QUEUE_NAME, map1, null);
		if (response.fail()) {
			LOGGER.error("uuid为{}注销账号通知搜索推入TQMQ失败，队返回:{}-{}", accountUuid, response.getCode(), response.getMsg());
		} else {
			LOGGER.info("uuid为{}注销账号通知搜索推入TQMQ成功", accountUuid);
		}

		//通知社区账号已注销
		Map<String, Object> map = new HashMap<>();
		map.put("account_uuid", accountUuid);
		MqResponse mqResponse = mqClient.push("account_cancel", map, null);
		if(mqResponse.fail()) {
			LOGGER.error("注销通知社区推入队列失败:{}-{}，uuid:{}", mqResponse.getCode(), mqResponse.getMsg(), accountUuid);
		} else {
			LOGGER.info("注销通知社区推入队列成功, data:{}", JsonUtils.objectToString(map));
		}

		//通知直播账号已注销
		MqResponse res = getMultiMqClient().push("multi_account_destroy", map, null);
		if(res.fail()) {
			LOGGER.error("注销通知推入多消费队列失败:{}-{}，uuid:{}", res.getCode(), res.getMsg(), accountUuid);
		} else {
			LOGGER.info("注销通知推入多消费队列成功, data:{}", JsonUtils.objectToString(map));
		}

//		accountStringRedisTemplate.opsForList().leftPush(RedisKeyConstant.ACCOUNT_DESTORY.getPattern(), accountUuid);
		toAccountHandlerAfterDestroy(accountUuid);

		// 通知推荐系统
		pushFilterCommonStatusToRecsys(accountUuid, 1, RecsysReportServerEnum.ACCOUNT_DESTROY.value());
	}

	/**
	 *
	 * <AUTHOR>
	 * @date 2020/04/13 10:32
	 * @param accountUuid
	 * @param status 状态：1-过滤 0-不过滤
	 * @param type
	 */
	public static void pushFilterCommonStatusToRecsys(String accountUuid, Integer status, String type) {
		// {“time”:”秒时间戳”,account_uuid “:”用户uuid”,”status”:”状态：1-过滤 0-不过滤”}
		MqClient client = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);

		Map<String, Object> data = new HashMap<>();
		data.put("type", type);

		Map<String, Object> param = new HashMap<>();
		param.put("account_uuid", accountUuid);
		param.put("time", DateUtil.currentTimeSeconds());
		param.put("status", status);
		data.put("data", param);

		MqResponse mqResponse = client.push("recsys_report_server", data, null);
		if(mqResponse.fail()) {
			LOGGER.warn("push recsys_report_server fail. code={}, msg={}",mqResponse.getCode(),mqResponse.getMsg());
		}
	}

	public String getDestroyUuid() {
		String accountUuid = "";
//		return accountStringRedisTemplate.opsForList().rightPop(RedisKeyConstant.ACCOUNT_DESTORY.getPattern());
		MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
		MqResponse mqResponse = mqClient.pop(CommConst.MQ_ACCOUNT_HANDLER_AFTER_DESTROY);

		if (StringUtils.isBlank(mqResponse.getMsg_body())) {
			return accountUuid;
		}

		if (mqResponse.fail()) {
			LOGGER.error("从TQMQ获取注销行为数据失败，失败码:{}，失败原因:{}", mqResponse.getCode(), mqResponse.getMsg());
		} else {
			Map<Object, Object> map = JsonUtils.stringToObject2(mqResponse.getMsg_body(), new TypeReference<Map<Object, Object>>() {
			});
			accountUuid = MapUtils.getString(map, "account_uuid");
		}

		if (StringUtils.isNotBlank(mqResponse.getMsg_id())) {
			mqResponse = mqClient.remove(CommConst.MQ_ACCOUNT_HANDLER_AFTER_DESTROY, mqResponse.getMsg_id());
			if (mqResponse.fail()) {
				LOGGER.error("从TQMQ删除注销行为{}失败, msgId:{}", accountUuid, mqResponse.getMsg_id());
			}
		}

		return accountUuid;
	}

	/**
	 * 推送至php Admp
	 *
	 * @param accountUuid
	 * @param gender
	 * @param regTime
	 * @param regStyle
	 * @param birth
	 * @param isSetAvatar
	 */
	public static void regBuryToAdmp(String accountUuid, Integer gender, Long regTime, RegStyle regStyle, Long birth, Boolean isSetAvatar) {
	    SoaBaseParams sbp = SoaBaseParams.fromThread();
	    Map<String, Object> data = new HashMap<>();
	    data.put("account_uuid", accountUuid);
	    data.put("token", sbp.getToken());
	    data.put("ua", "");
	    data.put("ip", sbp.getIp());
	    data.put("appcode", sbp.getAppcode());
	    data.put("cloned", sbp.getCloned());
	    data.put("platform_id", sbp.getPlatform_id());
	    data.put("platform_name", sbp.getPlatform_name());
	    data.put("channel", sbp.getChannel());
	    data.put("app_version", sbp.getApp_version());
	    data.put("gender", gender);
	    data.put("city", sbp.getCity());
	    data.put("access", sbp.getAccess());
	    data.put("reg_time", regTime);
	    data.put("reg_type", regStyle.name());
	    data.put("birth", birth);
	    data.put("is_set_avatar", isSetAvatar ? 1 : 0);
		try {
			userRegProducer.newMessage()
				.key(accountUuid)
				.eventTime(sbp.getTimestamp())
				.value(JsonUtils.objectToString(data))
				.sendAsync().whenComplete((id, e) -> {
					if (e != null) {
						LOGGER.warn("Pulsar >>>> 用户account_uuid:{}, 用户注册通知admp失败：{}", accountUuid, e.getMessage());
					} else {
						LOGGER.info("Pulsar >>>> 用户account_uuid:{}, 用户注册通知admp成功", accountUuid);
					}
				});
		} catch (Exception e) {
			LOGGER.warn("Pulsar推送失败", e);
		}
	    LOGGER.debug("用户account_uuid:{}, 用户注册通知admp成功", accountUuid);
	}

	/**
	 * 推送至php Admp
	 * @param accountUuid
	 * @param sexType
	 * @param bingTime
	 * @param bindMobileTypeEnum
	 */
	public void bindMobileBuryToAdmp(String accountUuid, Integer sexType, Long bingTime, BindMobileTypeEnum bindMobileTypeEnum) {
	    SoaBaseParams sbp = SoaBaseParams.fromThread();
	    String sexTypeStr = "";
	    if(sexType == null) {
	        sexTypeStr = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid).toString();
	    }else {
	        sexTypeStr = sexType.toString();
	    }
	    Map<String, Object> data = new HashMap<>();
	    data.put("account_uuid", accountUuid);
	    data.put("gender", sexTypeStr);
	    data.put("token", sbp.getToken());
	    data.put("ua", "");
	    data.put("ip", sbp.getIp());
	    data.put("appcode", sbp.getAppcode());
	    data.put("cloned", sbp.getCloned());
	    data.put("platform_id", sbp.getPlatform_id());
	    data.put("platform_name", sbp.getPlatform_name());
	    data.put("channel", sbp.getChannel());
	    data.put("app_version", sbp.getApp_version());
	    data.put("city", sbp.getCity());
	    data.put("access", sbp.getAccess());
	    data.put("bing_time", bingTime);
	    data.put("bind_type", bindMobileTypeEnum.type);
	    MqResponse response = getMqClient().push("handleAdmpBindPhone", data, null);
	    if(response.fail()) {
	        LOGGER.error("用户account_uuid:{}, 用户绑定手机号通知admp失败, {}-{}", accountUuid, response.getCode(), response.getMsg());
	        return;
	    }
	    LOGGER.debug("用户account_uuid:{}, 用户绑定手机号通知admp成功", accountUuid);
	}

	/**
	 * 完成社区任务
	 * @param accountUuid
	 * @param type
	 * @param extraData
	 */
	public void toBbsFinishIncentiveTask(String accountUuid, String type, Map<String, String> extraData){
		Integer sexType = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);

		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		data.put("type", type);
		//社区需要添加
		if (null != sexType) {
			data.put("sex_type", sexType);
		}
		if (FinishIncentiveTaskEnum.NEWBIE_REGISTER.getType().equals(type)) {
			SoaBaseParams soaBasicJava = RequestParams.getSoa_basic_java();
			data.put("cloned", soaBasicJava.getCloned());
			data.put("appcode", soaBasicJava.getAppcode());
			data.put("platform_id", soaBasicJava.getPlatform_id());
			data.put("channel", soaBasicJava.getChannel());
			data.put("app_version", soaBasicJava.getApp_version());
			data.put("token", soaBasicJava.getToken());
			data.put("city", soaBasicJava.getCity());
			data.put("gender", soaBasicJava.getGender());

			Map<String, String> abRule = Maps.newHashMap();
			// 2021.06.18 李阳说可以去掉了AB了
//			abRule.put(AbRuleCode.NEWUSER_GIFT, AbTestService.getAbTest(AbRuleCode.NEWUSER_GIFT));
			abRule.put(AbRuleCode.NEWUSER_GIFT, "");
			data.put("ab_rule", abRule);
		}else if(FinishIncentiveTaskEnum.COMPLETE_MATERIAL_FOR_INVITE_TASK.getType().equals(type)){
			data.putAll(extraData);
		}else if(FinishIncentiveTaskEnum.HOME_COVER.getType().equals(type)){
			SoaBaseParams soaBasicJava = RequestParams.getSoa_basic_java();
			data.put("cloned", soaBasicJava.getCloned());
		}
		MqResponse response = getMqClient().push(CommConst.MQ_COMPLETE_FOURM_TASK, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 完成社区任务:{}, 数据推入失败, code:{}, msg:{}", accountUuid, type, response.getCode(), response.getMsg());
			return;
		}
//		LOGGER.info("用户:{}, 完成社区任务:{}", accountUuid, type);

		if(FinishIncentiveTaskEnum.NEWBIE_REGISTER.getType().equals(type)) {
			LOGGER.info("用户:{}, 完成社区任务:{}, 推入队列数据：{}", accountUuid, type , JsonUtils.objectToString(data));
		}else if(FinishIncentiveTaskEnum.COMPLETE_MATERIAL_FOR_INVITE_TASK.getType().equals(type)){
			LOGGER.info("用户:{}, 完成社区邀请任务:{}, 推入队列数据：{}", accountUuid, type, JsonUtils.objectToString(data));
		}else if(FinishIncentiveTaskEnum.COMPLETED_AVATAR.getType().equals(type)){
			LOGGER.info("用户:{}, 完成头像, 推入队列数据：{}", accountUuid, type, JsonUtils.objectToString(data));
		}else if(FinishIncentiveTaskEnum.SELF_INTRODUCTION.getType().equals(type)){
			LOGGER.info("用户:{}, 完成自我介绍任务, 推入队列数据：{}", accountUuid, type, JsonUtils.objectToString(data));
		}else if(FinishIncentiveTaskEnum.HOME_COVER.getType().equals(type)){
			LOGGER.info("用户:{}, 完成主页封面任务, 推入队列数据：{}", accountUuid, type, JsonUtils.objectToString(data));
		}
	}


	/**
	 * 完成社区任务
	 *
	 * @param accountUuid
	 * @param type
	 * <AUTHOR>
	 * @date 2020/03/17 15:35
	 */
	public void toBbsFinishIncentiveTask(String accountUuid, String type) {
		toBbsFinishIncentiveTask(accountUuid, type, null);
	}

	/**
	 * 注册推入多消费队列
	 *
	 * <AUTHOR>
	 * @date 2020/07/01 09:24
	 * @param accountUuid 用户uuid
	 * @param sexType 性别
	 */
	public void pushToAccountRegisterQueue(String accountUuid, Integer sexType, String channel, Long appVersion, Integer platformId, Integer appcode, Integer cloned) {
		Map<String, Object> data = new HashMap<>();
		SoaBaseParams sbp = SoaBaseParams.fromThread();
		String ip = sbp.getIp();
		Integer cityCode = sbp.getCity();
		data.put("account_uuid", accountUuid);
		data.put("sex_type", String.valueOf(sexType));
		data.put("ip",ip);
		data.put("city_code",cityCode);
		data.put("channel",channel);
		data.put("app_version",appVersion);
		data.put("platform_id",platformId);
		data.put("appcode",appcode);
		data.put("cloned",cloned);
		MqResponse response = getMultiMqClient().push("multi_account_register", data, null);
		if(response.fail()) {
			LOGGER.error("注册推入队列失败,data:{}, code:{}, msg:{}", JsonUtils.objectToString(data), response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("注册推入队列成功, uuid: {}", accountUuid);
	}

	/**
	 * 推多消费队列
	 *
	 * <AUTHOR>
	 * @date 2020/04/10 16:59
	 * @param accountUuid
	 * @param oper 1有真人认证照片 2清空真人照片
	 */
	public static void toBbsFacePhotoUpdate(String accountUuid, String oper) {
		Map<String, String> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		data.put("oper", oper);
		MqResponse response = getMultiMqClient().push("multi_face_photo", data, null);
		if(response.fail()) {
			LOGGER.error("用户:{},oper={}, 用户头像/相册变更数据推入失败, code:{}, msg:{}", accountUuid, oper, response.getCode(), response.getMsg());
			return;
		}
	}

	/**
	 * 推多消费队列
	 *
	 * <AUTHOR>
	 * @date 2020/04/10 16:59
	 * @param accountUuid
	 * @param oper 1头像有真人认证照片 2头像无真人照片 （待审核也算有）
	 */
	public void toBbsAvatarPhotoUpdate(String accountUuid, Integer oper) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		data.put("oper", oper);

		userAvatarUpdateProducer.newMessage(Schema.STRING)
			.key(accountUuid)
			.value(JsonUtils.objectToString(data))
			.sendAsync()
			.thenAccept(m -> LOGGER.info("用户: {}, oper={}, 用户头像变更数据推入成功", accountUuid, oper))
			.exceptionally(e -> {
				LOGGER.warn("用户头像变更推送失败", e);
				return null;
			});
	}

	/**
	 * 实名认证成功推入队列
	 *
	 * @param accountUuid
	 * @param identityNoMd5
	 * @param realNameMd5
	 */
	public static void toAntispamTagRealName(String accountUuid, String identityNoMd5, String realNameMd5) {
	    try {
	        Map<String, Object> data = new HashMap<>();
	        data.put("account_uuid", accountUuid);
	        data.put("identity_no_md5", identityNoMd5);
	        data.put("real_name_md5", realNameMd5);

	        MqResponse response = getMqClient().push(CommConst.MQ_ANTISPAM_TAG_REAL_NAME, data, 10L);
	        if(response.fail()) {
	            LOGGER.error("用户实名认证推入队列异常:{}, 失败原因:{}, 失败码:{}", JsonUtils.objectToString(data), response.getMsg(), response.getCode());
	        }
	    } catch (Exception e) {
	        LOGGER.warn("数据推入队列异常。", e);
	    }
	}

    /**
     * TODO 2024.3.11 该方法可能有问题，直接return可能导致后续推队列异常。 不好评估修改可能带来的影响，暂不修改。
     *
     * @param accountUuid
     */
    public void doAccountAvatarPhotoUpdate(String accountUuid) {
        LOGGER.info("doAccountAvatarPhotoUpdate:{}",accountUuid);
        if(StringUtils.isBlank(accountUuid)) {
            return;
        }
        try {
            Boolean flag = accountsService.isFaceCertificationAndHaveValidPhoto(accountUuid);
            if(flag == null) {
                LOGGER.warn("校验用户是否活体并有有效照片失败，accountUuid={}",accountUuid);
                return;
            }
            if(flag) {
                BuryService.toBbsFacePhotoUpdate(accountUuid, "1");
                BuryService.pushFilterCommonStatusToRecsys(accountUuid, 0, RecsysReportServerEnum.REAL_AVATAR_UPDATE.value());
            }else {
                BuryService.toBbsFacePhotoUpdate(accountUuid, "2");
                BuryService.pushFilterCommonStatusToRecsys(accountUuid, 1, RecsysReportServerEnum.REAL_AVATAR_UPDATE.value());
            }
            //更新是否有真人照片字段
            int status = flag ? 1 : 0;
            accountsInfoService.updateRealPhotoCertByUuid(accountUuid,status);
        } catch (Exception e) {
            LOGGER.warn("校验用户是否活体并有有效照片处理失败，accountUuid={}",accountUuid,e);
            return;
        }

        try {
            Boolean flag = accountsService.isSystemAvatar(accountUuid);
            if(flag == null) {
                LOGGER.warn("校验用户头像是否有效照片失败，accountUuid={}",accountUuid);
                return;
            }
            if(flag) {
                BuryService.pushFilterCommonStatusToRecsys(accountUuid, 1, RecsysReportServerEnum.AVATAR_UPDATE.value());
            }else {
                BuryService.pushFilterCommonStatusToRecsys(accountUuid, 0, RecsysReportServerEnum.AVATAR_UPDATE.value());
            }
        } catch (Exception e) {
            LOGGER.warn("校验用户头像是否有效照片处理失败，accountUuid={}",accountUuid,e);
            return;
        }

        try {
            Integer flag = accountsService.isFaceCertificationAndHaveValidAvatar(accountUuid);
            if(flag == null) {
                LOGGER.warn("校验用户头像是否真人照片失败，accountUuid={}",accountUuid);
                return;
            }

            // 该业务只有状态变化时才推 即 有->无，无->有 推；有->有，无->无 不推； 上一次的状态，缓存一段时间
            String redisKey = RedisKeyConstant.ACCOUNT_HAS_VALID_AVATAR.setArg(accountUuid);
            String oldFlag = accountStringRedisTemplate.opsForValue().getAndSet(redisKey, flag.toString());
            // 设置过期
            accountStringRedisTemplate.expire(redisKey, 90, TimeUnit.DAYS);
            // 无变化 不推队列
            if(StringUtils.isNotBlank(oldFlag) && Objects.equals(flag.toString(), oldFlag)) {
				LOGGER.info("用户头像更新无变化，不推队列");
            }else {
                if(flag == 1) {
                    toBbsAvatarPhotoUpdate(accountUuid,  1);
                }else {
                    toBbsAvatarPhotoUpdate(accountUuid,  2);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("校验用户头像是否有效照片处理失败，accountUuid={}",accountUuid,e);
            return;
        }

        try {
            Boolean flag = accountsService.isFaceCertificationAndHaveValidPhotoAvatar(accountUuid);
            if(flag == null) {
                LOGGER.warn("校验用户是否活体并有有效相册头像失败，accountUuid={}",accountUuid);
                return;
            }
            if(flag) {
				EventTrackingDO tracking = EventTrackingDO.createWithQuery(accountUuid, AccountEvent.REAL_PERSON_CERT_COMPLETE);
                kafkaSinkUtil.push(EventConst.EVENT_TRACKING_TOPIC, tracking);
				behaviorReporter.report(accountUuid, tracking);
            }
        } catch (Exception e) {
            LOGGER.warn("校验用户是否活体并有有效相册头像失败，accountUuid={}",accountUuid,e);
        }
    }

	/**
	 * 超过90天未活跃，推多消费队列
	 */
	public static void pushToAccountActiveTime(String accountUuid, Integer prevActiveTime) {
		Map<String, Object> data = ImmutableMap.of(
				"account_uuid", accountUuid,
				"prev_active_time", prevActiveTime
		);
		MqResponse response = getMultiMqClient().push(CommConst.NOT_ACTIVE_FOR_90_DAYS, data, null);
		if(response.fail()) {
			LOGGER.error("超过90天未活跃，推入队列失败，data:{}, code:{}, msg:{}", JsonUtils.objectToString(data), response.getCode(), response.getMsg());
			return;
		}else {
			LOGGER.info("超过90天未活跃，推入队列，data:{}", JsonUtils.objectToString(data));
		}
	}

	/**
	 * 更新昵称索引
	 */
	public void updateNicknameIndex(String uuid, String nickname) {
		// 丢入mq更新昵称索引
		Map<String, Object> map = new HashMap<>();
		map.put("uuid", uuid);
		map.put("flag", "update");
		map.put("nickname", nickname);
		map.put("cardId", accountsCardService.getUsedAndNormalCardByUuid(uuid));
		MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
		MqResponse response = mqClient.push(AccountsService.SEARCH_MQ_QUEUE_NAME, map, null);
		if (response.fail()) {
			LOGGER.error("uuid为{}修改昵称为{}推入TQMQ失败，列队返回:{}-{}", uuid, nickname, response.getCode(), response.getMsg());
			// 非关键，不throw, 更新索引不能导致整个流程失败
		} else {
//            logger.info("用户昵称、靓号索引推入搜索队列成功: {}", map);
		}
	}

	/**
	 *
	 * 延迟推入 个人简介
	 *
	 * @param auditType CommonAuditStatus 必填
	 * @param id 操作的审核记录
	 * @param operator 操作人
	 * @param reason 原因 不通过填写，可选
	 */
	public static void pushPersonalProfileAutoAudit(Integer auditType, Long id, String operator, String reason) {
	    Map<String, Object> map = new HashMap<>();
	    map.put("type", "personal_profile_auto_audit");
	    map.put("time", DateUtil.currentTimeSeconds());

        Map<String,Object> data = Maps.newHashMap();
        data.put("auditType", auditType);
        data.put("id", id);
        data.put("operator", operator);
        data.put("reason", reason);
        map.put("data", data);

	    MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
	    MqResponse response = mqClient.push(CommConst.MQ_ACCOUNT_BIZ_DATA, map, 10L);
	    if (response.fail()) {
	        LOGGER.error("个人简介推入自动审核失败，id={}, code={}, msg={}", id, response.getCode(), response.getMsg());
	    }
	}

	/**
	 *
	 * 延迟推入 昵称
	 *
	 * @param auditType CommonAuditStatus 必填
	 * @param id 操作的审核记录
	 * @param operator 操作人
	 */
	public static void pushNicknameAutoAudit(Integer auditType, Long id, String operator) {
	    Map<String, Object> map = new HashMap<>();
	    map.put("type", AccountBizDataDtoType.NICKNAME_AUTO_AUDIT);
	    map.put("time", DateUtil.currentTimeSeconds());

	    Map<String,Object> data = Maps.newHashMap();
	    data.put("auditType", auditType);
	    data.put("id", id);
	    data.put("operator", operator);
	    map.put("data", data);

	    MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
	    MqResponse response = mqClient.push(CommConst.MQ_ACCOUNT_BIZ_DATA, map, 10L);
	    if (response.fail()) {
	        LOGGER.error("个人简介推入自动审核失败，id={}, code={}, msg={}", id, response.getCode(), response.getMsg());
	    }
	}

	/**
	 * 延迟推入 自动退出
	 * @param ticket
	 * @param token
	 * @param platformId
	 * @param appcode
	 * @param cloned
	 */
	public static void pushAutoLogout(String ticket, String token, Integer platformId, Integer appcode, Integer cloned) {
	    Long time = DateUtil.currentTimeSeconds();
	    Map<String, Object> map = new HashMap<>();
	    map.put("type", AccountBizDataDtoType.AUTO_LOGOUT);
	    map.put("time", time);

	    Map<String,Object> data = Maps.newHashMap();
	    data.put("ticket", ticket);
	    data.put("token", token);
	    data.put("platformId", platformId);
	    data.put("appcode", appcode);
	    data.put("cloned", cloned);
	    map.put("data", data);

	    MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
	    MqResponse response = mqClient.push(CommConst.MQ_ACCOUNT_BIZ_DATA, map, time + DELAY_TIME_30_MINUTE);
	    if (response.fail()) {
	        LOGGER.error("自动登出推入失败，ticket={}, code={}, msg={}", ticket, response.getCode(), response.getMsg());
	    }
	}

	/**
	 * 添加到图片审核队列
	 * @param accountUuid
	 */
	public void doAccountPhotoCheck(String accountUuid, Integer orginCheck) {
		LOGGER.info("doAccountPhotoCheck:{}, orignCheck={}", accountUuid, orginCheck);
		// 查找用户 create_time
		Object createTimeObj = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "create_time");
		Long createTime = null;
		if (null != createTimeObj) {
			try {
				createTime = Long.parseLong(createTimeObj + "");
			}catch (Exception e){
				LOGGER.warn("uuid={} info cache create_time save err. {}", accountUuid, createTimeObj);
			}
		}
		// 缓存查不到 取一次数据库
		if (createTimeObj == null) {
			Accounts accounts = accountsManager.getByUuid(accountUuid, false);
			if (accounts != null) {
				createTime = accounts.getCreate_time();
			} else {
				LOGGER.warn("doAccountPhotoCheck 用户不存在 uuid={}", accountUuid);
				return;
			}
		}
		// 获取应用信息
		Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
				accountUuid,
				new String[]{"reg_appcode", "reg_cloned"}
		);
		Integer regAppcode = MapUtils.getInteger(infoMap, "reg_appcode", 1);
		Integer regCloned = MapUtils.getInteger(infoMap, "reg_cloned", 1);
		// 查找图片
		List<AccountsPhoto> photos = accountsPhotoDao.getPhotoListInStatus(accountUuid, AccountsPhotoService.PHOTO_REVIEW_STATUS_ARR);
		LOGGER.info("doAccountPhotoCheck.accountUuid={}.orginCheck={},photoInArr={}", accountUuid, orginCheck, JSON.toJSONString(photos));
		for (AccountsPhoto photo: photos) {
			// ACCOUNT_PHOTO_PUSH_REVIEW
			Integer checkType = accountsPhotoService.getPhotoCheckType(photo);
            // type+id+url
            String content = checkType + "|" + photo.getId() + "|" + AvatarHandleService.getAvatarOfSavePhoto(photo.getPhoto_url());
            String pushReviewMd5 = Md5Util.encryptSHA1(content);
            Boolean hasPush = accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_PHOTO_PUSH_REVIEW.setArg(pushReviewMd5));
            if (!hasPush) {
                this.pushToJ61MqPhotoCheck(regAppcode, regCloned, photo, checkType, createTime, RedisKeyConstant.ACCOUNT_PHOTO_PUSH_REVIEW.setArg(pushReviewMd5));
            } else {
                LOGGER.info("图片不再重复推送review.uuid={}.id={}.url={}, key={}", photo.getAccount_uuid(), photo.getId(), photo.getPhoto_url(), pushReviewMd5);
            }
		}
//		for (AccountsPhoto photo: photos) {
//			// 查看是否存在审核队列中，如果存在则跳过
//			if(accountsPhotoService.existInCheckRedis(photo.getId(), orginCheck)){
//				LOGGER.info("数据已经在审核队列,跳过.uuid={},photoId={},orginCheck={}", photo.getAccount_uuid(), photo.getId(), orginCheck);
//				continue;
//			}
//			// 是否存在风控标识
//			boolean hasRisk = Objects.equals(photo.getPhoto_url(), photo.getRisk_photo_url());
//			if(!hasRisk || StringUtils.isBlank(photo.getRisk_description())){
//				accountsPhotoService.setPhotoRiskDescription(photo);
//				updateRiskUrlAndRiskDesc(photo.getId(), photo.getPhoto_url(), photo.getRisk_description(), 3);
//			}
//			accountsPhotoService.addToCheckRedisZSet(createTime, photo);
//		}
	}

	/**
	 * 昵称违规推送
	 * @param accountUuid
	 */
	public void toBbsAccountNameViolation(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		MqResponse response = getMqClient().push(CommConst.MQ_ACCOUNT_NAME_VIOLATION, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 昵称违规推送, 数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("用户:{}, 昵称违规推送", accountUuid);
	}

	/**
	 * 头像违规推送
	 * @param accountUuid
	 */
	public void toBbsAccountAvatarIll(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		Integer sexType = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);
		data.put("account_uuid", accountUuid);
		data.put("sex_type", sexType);
		MqResponse response = getMqClient().push(CommConst.MQ_ILLEGAL_AVATAR, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 头像违规推送, 数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("用户:{}, 头像违规推送", accountUuid);
	}

	public void pushRealPersonCertification(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		MqResponse response = getMqClient().push(CommConst.MQ_ACCOUNT_REAL_PERSON_CERTIFICATION, data, 10L);
		if(response.fail()) {
			LOGGER.error("用户account_uuid:{}, 用户真人认证推送失败, {}-{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("用户account_uuid:{}, 用户真人认证推送成功", accountUuid);
	}


	/**
	 * 推送头像任务
	 * @param accountUuid
	 * @param type
	 */
	public static void pushBbsAvatarTask(String accountUuid, int type) {
		Map<String, Object> data = new HashMap<>();
		AccountsInfoService accInfoService = SpringContextHolder.getBean(AccountsInfoService.class);
		Integer sexType = accInfoService.getSexTypeByAccountUuidRedis(accountUuid);
		String realPersonCertification = accInfoService.getRealPersonCertification(accountUuid);
		data.put("account_uuid", accountUuid);
		data.put("type", type);
		data.put("sex_type", sexType);
		data.put("profile_verify_status", realPersonCertification);
		MqResponse response = getMqClient().push(CommConst.MQ_CHANGE_AVATAR, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 头像变更推送, 数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("用户:{}, 头像变更推送,data={}", accountUuid, JSON.toJSONString(data));
	}

	/**
	 * 推送头像任务
	 * @param accountUuid
	 */
	public static void pushAccountPostRegister(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		data.put("tracer_id", SoaBaseParams.fromThread().getDistinctRequestId());
		MqResponse response = getMqClient().push(CommConst.MQ_POST_REGISTER, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 注册后置推送, 数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("用户:{}, 注册后置推送", accountUuid);
	}

	/**
	 * 活跃用户头像质量检测
	 * @param accountUuid
	 */
	public static void pushToAvatarScript(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		MqResponse response = getMqClient().push(CommConst.MQ_SRCIPT_AVATAR, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 活跃用户头像质量检测, 数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
	}

	/**
	 * 活跃用户相册质量检测
	 * @param accountUuid
	 */
	public static void pushToCoverScript(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		MqResponse response = getMqClient().push(CommConst.MQ_SRCIPT_COVER, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 活跃用户相册质量检测, 数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
	}


	/**
	 * 预注册推送 推送至Admp
	 */
	public static void preRegBuryToAdmp(String type) {
		SoaBaseParams sbp = SoaBaseParams.fromThread();
		Map<String, Object> data = new HashMap<>();
		data.put("token", sbp.getToken());
		data.put("appcode", sbp.getAppcode());
		data.put("cloned", sbp.getCloned());
		data.put("type", type);
		data.put("platform_id", sbp.getPlatform_id());
		MqResponse response = getMqClient().push(CommConst.MQ_HANDLE_ADMP_READY_REG, data, 40L);
		if(response.fail()) {
			LOGGER.error("预注册推送admp失败, {}-{}", response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("预注册推送admp成功.{}", JSON.toJSONString(data));
	}

	public void pushToJ61MqPhotoCheck(Integer appcode, Integer cloned, AccountsPhoto photo, Integer checkType, Long createTime, String redisKey) {
		try {
			RiskDetectEnum riskDetectEnum = PhotoOrignCheckEnum.getByValue(checkType);
			if (Objects.isNull(riskDetectEnum)) {
				LOGGER.warn("头像相册推送审核平台失败.uuid={}.id={}.url={}.checkType={}", photo.getAccount_uuid(), photo.getId(), photo.getPhoto_url(), checkType);
				return;
			}
            if (RiskDetectEnum.REAL_PERSON_COVER.equals(riskDetectEnum) && Objects.equals(8, photo.getStatus())) {
                LOGGER.info("推送审核平台过滤真人认证相册中非真人图片.uuid={}.id={}.url={}.checkType={}", photo.getAccount_uuid(), photo.getId(), photo.getPhoto_url(), checkType);
                return;
            }
            AccountsPhotoImgCompareLog imgCompareLog = accountsPhotoImgCompareLogDao.getLastByUuid(photo.getAccount_uuid(), photo.getPhoto_url());
			AccountsPhotoImgQualityLog qualityLog = accountsPhotoImgQualityLogDao.getLastByUuid(photo.getAccount_uuid(), photo.getPhoto_url());

			// 风控安全审核
            Map<String, Object> extraMap = new HashMap<>();
			extraMap.put("qualityRequestId", qualityLog == null || StringUtils.isBlank(qualityLog.getThirdOrderNo()) ? "" : qualityLog.getThirdOrderNo());
			extraMap.put("faceCertification", photo.getVerify_status());
			extraMap.put("faceRequestId", imgCompareLog == null || StringUtils.isBlank(imgCompareLog.getThirdOrderNo()) ? "" : imgCompareLog.getThirdOrderNo());
			extraMap.put("qualityLevel",  qualityLog == null || StringUtils.isBlank(qualityLog.getClarityScore().toString()) ? "" : qualityLog.getClarityScore().toString());
			RiskSafeService.imgReviewFromRiskDetect(appcode, cloned, photo.getAccount_uuid(), riskDetectEnum.name(), Arrays.asList(AvatarHandleService.getAvatarSrcPhotoUrl(photo.getPhoto_url())), CommConst.OLD_CLIENT_PACKAGE, photo.getId(), extraMap);
			accountStringRedisTemplate.opsForValue().set(redisKey, "1", 1, TimeUnit.DAYS);
		} catch (Exception e) {
			LOGGER.error("头像相册推送审核平台失败.uuid={}.id={}.url={}", photo.getAccount_uuid(), photo.getId(), photo.getPhoto_url(), e);
		}
	}

	/**
	 * 推送图片机审结果记录
	 * @param type
	 * @param accountUuid
	 * @param data
	 */
	public static void pushToPhotoDetectRecord(String type, String accountUuid, Object data){
		JSONObject json = new JSONObject();
		json.put("type", type);
		json.put("account_uuid", accountUuid);
		json.put("data", data);

		MqResponse response = getMqClient().push(CommConst.MQ_ACCOUNT_PHOTO_DETECT_RECORD, json, null);
		if(response.fail()) {
			LOGGER.error("推送图片检测记录失败, {}-{}", response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("推送图片检测记录成功.{}", json.toJSONString());
	}

	/**
	 * 推到认证日志记录(真人 或 支付宝账号)
	 * @param accountUuid
	 * @param certWhiteList
	 * @param certType
	 * @param operator
	 * @param operatorType
	 * @param basePhotoUrl
	 * @param remark
	 */
	public static void pushToCertLog(String accountUuid, Integer certWhiteList, Integer certType, String operator, Integer operatorType, String basePhotoUrl, String remark){
		Map<String, Object> certLog = new HashMap<>();
		certLog.put("certType", certType);

		Map<String, Object> data = new HashMap<>();
		data.put("accountUuid", accountUuid);
		data.put("certWhiteList", certWhiteList);
		data.put("certType", certType);
		data.put("operator", StringUtil.nullObjectToEmptyString(operator));
		data.put("operatorTime", DateUtil.currentTimeSeconds());
		data.put("operatorType", operatorType);
		data.put("basePhotoUrl", AvatarHandleService.getAvatarOfSavePhoto(basePhotoUrl));
		data.put("remark", StringUtil.nullObjectToEmptyString(remark));
		certLog.put("data", data);

		MqResponse response = getMqClient().push(CommConst.MQ_CERT_LOG, certLog, null);
		if(response.fail()) {
			LOGGER.error("推送认证日志记录失败,uuid={}.data={}.{}-{}", accountUuid, JSON.toJSONString(certLog), response.getCode(), response.getMsg());
			return;
		}
	}

	/**
	 * 推到实名认证日志记录
	 * @param accountUuid
	 * @param realName
	 * @param identityNo
	 * @param logType
	 * @param operatorType
	 * @param operator
	 * @param certWhiteList
	 */
	public static void pushToCertificationLog(String accountUuid, String realName, String identityNo, Integer logType, Integer operatorType, String operator, Integer certWhiteList, String basePhotoUrl){
		if(StringUtils.isBlank(operator)){
			// 脚本变更
			operator = "script";
			operatorType = AccountsCertificationChangeLog.OperatorTypeEnum.SCRIPT.getValue();
		}
		Map<String, Object> certLog = new HashMap<>();
		certLog.put("certType", CertTypeEnum.REAL_NAME.getValue());

		Map<String, Object> data = new HashMap<>();
		data.put("accountUuid", accountUuid);
		data.put("realName", realName);
		data.put("identityNo", identityNo);
		data.put("logType", logType);
		data.put("operator", operator);
		data.put("operatorTime", DateUtil.currentTimeSeconds());
		data.put("operatorType", operatorType);
		data.put("certWhiteList", certWhiteList);
		data.put("basePhotoUrl", AvatarHandleService.getAvatarOfSavePhoto(basePhotoUrl));
		String remark = TemplateStringConst.getContent(TemplateStringConst.CERT_LOG_REMARK_REAL_NAME, accountUuid, DateUtil.dateToString20(new Date()), AccountsCertificationChangeLog.LogTypeEnum.getNameByValue(logType), operator);
		data.put("remark", remark);
		certLog.put("data", data);

		MqResponse response = getMqClient().push(CommConst.MQ_CERT_LOG, certLog, null);
		if(response.fail()) {
			LOGGER.error("推送认证日志记录失败,uuid={}.data={}.{}-{}", accountUuid, JSON.toJSONString(certLog), response.getCode(), response.getMsg());
			return;
		}
	}

	/**
	 * 活体成功推送
	 * @param accountUuid
	 */
	public static void pushToAccountLivingCert(String accountUuid, CertTypeEnum certType){
		Map<String, Object> data = new HashMap<>();
		data.put("app_code", SoaBaseParams.fromThread().getAppcode());
		data.put("cloned", SoaBaseParams.fromThread().getCloned());
		data.put("account_uuid", accountUuid);
		data.put("cert_type", certType.name());

		MqResponse response = getMqClient().push(CommConst.MQ_ACCOUNT_LIVING_CERT_RESULT, data, null);
		if(response.fail()) {
			LOGGER.warn("推送真人活体成功到风控失败, {}-{}", response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("推送真人活体成功到风控成功.uuid={}.data={}", accountUuid, JsonUtils.objectToString(data));
	}

	/**
	 * 取消实名认证 或 账号注销 时推送
	 *
	 * @param accountUuid 用户uuid
	 */
	public void toHandleUnbindBankCard(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		MqResponse response = getMqClient().push(CommConst.MQ_HANDLE_UNBIND_BANK_CARD, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 解绑银行卡数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("用户:{}, 解绑银行卡: {}", accountUuid, JSON.toJSONString(data));
	}

	/**
	 * 用户注销 时推荐
	 */
	public static void toAccountHandlerAfterDestroy(String accountUuid) {
		Map<String, Object> data = new HashMap<>();
		data.put("account_uuid", accountUuid);
		MqResponse response = getMqClient().push(CommConst.MQ_ACCOUNT_HANDLER_AFTER_DESTROY, data, null);
		if (response.fail()) {
			LOGGER.error("用户:{}, 注销行为数据推入失败, code:{}, msg:{}", accountUuid, response.getCode(), response.getMsg());
			return;
		}
		LOGGER.info("用户:{}, 注销行为: {}", accountUuid, JSON.toJSONString(data));
	}


	/**
	 * 异步修改文件状态
	 *
	 * @param appcode
	 * @param url
	 * @param bucket
	 * @param status
	 * @param operTime
	 */
    public static void pushToPicSysUpdFileStatus(Integer appcode, String url, String bucket,
        int status, Long operTime) {
        url = AvatarHandleService.getAvatarOfSavePhoto(AvatarHandleService.removeParams(url));
        Map<String, Object> data = new HashMap<>();
        data.put("appcode", appcode);
        data.put("url", url);
        data.put("bucket", bucket);
        data.put("status", status);
        data.put("operTime", operTime);
        MqResponse response = getMqClient().push(CommConst.MQ_PIC_SYS_UPD_FILE_STATUS, data, null);
        if (response.fail()) {
            LOGGER.error("异步修改文件状态推入队列失败, code:{}, msg:{}", response.getCode(), response.getMsg());
            return;
        }
    }
    
    /**
     * 异步修改文件状态
     *
     * @param appcode
     * @param url
     * @param bucket
     * @param status
     * @param operTime
     * @param seconds
     */
    public static void pushToPicSysUpdFileStatus(Integer appcode, String url, String bucket,
        int status, Long operTime, Long seconds) {
        url = AvatarHandleService.getAvatarOfSavePhoto(AvatarHandleService.removeParams(url));
        Map<String, Object> data = new HashMap<>();
        data.put("appcode", appcode);
        data.put("url", url);
        data.put("bucket", bucket);
        data.put("status", status);
        data.put("operTime", operTime);
        MqResponse response = getMqClient().push(CommConst.MQ_PIC_SYS_UPD_FILE_STATUS, data, seconds);
        if (response.fail()) {
            LOGGER.error("异步修改文件状态推入队列失败, code:{}, msg:{}", response.getCode(), response.getMsg());
            return;
        }
    }

    /**
     * 成功使用文件
     *
     * @param appcode
     * @param url
     * @param bucket
     * @param operTime
     */
    public static void pushToPicSysUsedFileBiz(Integer appcode, String url, String bucket, Long operTime) {
        url = AvatarHandleService.getAvatarOfSavePhoto(AvatarHandleService.removeParams(url));
        Map<String, Object> data = new HashMap<>();
        data.put("appcode", appcode);
        data.put("url", url);
        data.put("bucket", bucket);
        data.put("operTime", operTime);
        MqResponse response = getMqClient().push(CommConst.MQ_PIC_SYS_USED_FILE_BIZ, data, null);
        if (response.fail()) {
            LOGGER.error("成功入库文件推入队列失败, code:{}, msg:{}", response.getCode(), response.getMsg());
            return;
        }
    }

}
