package cn.taqu.account.service;

import cn.taqu.account.common.*;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsInfoDao;
import cn.taqu.account.dto.AccountSchoolDto;
import cn.taqu.account.dto.ShumeiContentCheckDTO;
import cn.taqu.account.event.AccountBehaviorReporter;
import cn.taqu.account.event.AccountEvent;
import cn.taqu.account.event.EventConst;
import cn.taqu.account.event.EventTrackingDO;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.*;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.account.utils.LogUtil;
import cn.taqu.account.utils.mq.AntiSpamMqUtil;
import cn.taqu.account.valid.AccountInfoValid;
import cn.taqu.account.vo.AccountIdealTargetVo;
import cn.taqu.account.vo.GetByTicketVo;
import cn.taqu.account.vo.life.AccountsLifeVo;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.SpringContextHolder;
import cn.taqu.core.utils.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class AccountsPersonalInfoService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsInfo> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsPersonalInfoService.class);

    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsForumProfileService accountsForumProfileService;
    @Autowired
    private BuryService buryService;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AntiSpamMqUtil antiSpamMqUtil;
    @Autowired
    private VerifyRecordService verifyRecordService;
    @Autowired
    private ModifyGrowScoreService modifyGrowScoreService;
    @Autowired
    private PersonalProfileInfoService personalProfileInfoService;
    @Autowired
    private AccountsLabelService accountsLabelService;
    @Autowired
    private AccountsLikeLabelService accountsLikeLabelService;
    @Autowired
    private RegionService regionService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private AccountsLifeService accountsLifeService;
    @Autowired
    private AccountLabelServiceV2 accountLabelServiceV2;
    @Autowired
    private SchoolService schoolService;

    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;
    @Autowired
    @Lazy
    private AllureService allureService;

    @Autowired
    private AccountBehaviorReporter behaviorReporter;

    @Resource(name = "accountBizStringRedisTemplate")
    private StringRedisTemplate bizRedisTemplate;

    /**
     * 获取账号保密设置
     *
     * @param accountUuid 用户账号uuid
     * @return
     * @Title getSecretConfig
     * <AUTHOR>
     * @Date 2015年10月8日 上午10:57:25
     */
    public Map<String, String> getSecretConfig(String accountUuid, String[] fields) {
        accountsInfoService.guestTicketExpire(accountUuid);
        return this.mGetSecretConfigV2(new String[]{accountUuid}, fields).get(accountUuid);
    }

    public Map<String, Map<String, String>> mGetSecretConfigV2(String[] accountUuids, String[] fields) {
        Map<String, Map<String, String>> result = Maps.newHashMap();
        for (String uuid : accountUuids) {
            uuid = StringUtils.trim(uuid);
            if (StringUtils.isBlank(uuid)) {
                continue;
            }

            Map<String, String> secretConfig = Maps.newHashMap();

            if (fields != null) {
                for (String field : fields) {
                    if (StringUtils.isNotBlank(field)) {
                        secretConfig.put(field, "0");
                    }
                }
            } else {
                secretConfig.put("sex_type_is_secret", "0");
                secretConfig.put("age_is_secret", "0");
                secretConfig.put("affectivestatus_is_secret", "0");
                secretConfig.put("baseaddr_is_secret", "0");
                secretConfig.put("sexual_is_secret", "0");
                secretConfig.put("hometown_is_secret", "0");
            }


            result.put(uuid, secretConfig);
        }

        return result;
    }

    /**
     * 根据accountUuid查询用户相关的信息，支持批量查询，如果没有查询到返回"用户信息未找到，错误码（a1102）"错误，否则返回单个{@code Map
     * <String,Object>}(单个查询)或二维{@code Map<Long,Map<String,Object>}(批量查询)
     *
     * @param accountUuids
     * @return
     * @Title getPersonalInfo
     * <AUTHOR>
     * @Date 2015年10月9日 下午5:56:15
     */
    public Object getPersonalInfo(String... accountUuids) {
        String[] fields = {"account_name", "affectivestatus", UuidInfoField.AGE, "baseaddr", "sex_type", "sexual", UuidInfoField.BIRTH,
                "hometown", "dating_intention", UuidInfoField.GENDER_CERTIFICATION, UuidInfoField.ZHIMA_CERTIFICATION,
                "face_certification", "pre_profile_verify_status", "profile_verify_status"};
        Map<String, Map<String, Object>> accountInfoMap = accountsInfoService.getInfoByUuid(accountUuids, fields, "1", false, false);

        Map<String, Map<String, String>> returnMap = new LinkedHashMap<>();
        for (Map.Entry<String, Map<String, Object>> accountInfo : accountInfoMap.entrySet()) {
            String accountUuid = accountInfo.getKey();
            Map<String, Object> infoMap = accountInfo.getValue();
            String accountName = MapUtils.getString(infoMap, "account_name", "");
            if (StringUtils.isBlank(accountName)) {
                continue;
            }
            Map<String, String> returnItem = new HashMap<>();
            for (int i = 1; i < fields.length; i++) {
                String field = fields[i];
                returnItem.put(field, MapUtils.getString(infoMap, field));
            }
            returnMap.put(accountUuid, returnItem);
        }

        // 如果没找到，抛出异常
        if (returnMap.isEmpty()) {
            throw new ServiceException(CodeStatus.PERSON_INFO_NOT_FOUND);
        }

        // 如果是查询单个直接返回单项数据
        if (accountUuids.length == 1) {
            return returnMap.get(accountUuids[0]);
        }
        // 如果是多个返回二维map
        return returnMap;
    }

    private void accountUpdBury(String accountUuid, Integer appcode) {
        if (StringUtils.isBlank(accountUuid)) {
            return;
        }
        buryService.toDataCenter("account", "upd", accountUuid, appcode, null);
    }

    /**
     * @param accountUuid
     * @return
     * <AUTHOR>
     * 2017年1月3日 上午11:44:01
     */
    public Map<String, String> getPersonalProfilePriv(String accountUuid, Integer appcode) {
        accountsInfoService.guestTicketExpire(accountUuid);
        long intervalTime = 604800L;//设置一个默认间隔时长为 1 周（单位：秒）
        //返回结果
        Map<String, String> returnMap = new HashMap<String, String>();    // status：1-可修改 0-不可修改  ；time：上次修改的秒时间戳

        //ZSetOperations<String, String> opsForZSet = this.accountStringRedisTemplate.opsForZSet();
        //Double timestampD = opsForZSet.score(RedisKeyConstant.ACCOUNT_PERSONALINFO_PRIV_UUID.getPattern(), accountUuid);
        String timestameStr = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.PROFILE_UPD_TIME.setArg(accountUuid));
        long timestampL = 0L;
        if (StringUtils.isBlank(timestameStr)) {
            //如果timestameStr不存在，说明是第一次修改。那就直接放行。
            returnMap.put("status", "1");
        } else {
            //如果timestampD存在，处理处理
            timestampL = Long.valueOf(timestameStr);

            //获取个人简介修改时长不再调用soa服务，直接系统内获取
            //社区后台配置功能已下线，不取配置，直接写死7天
            /*String profileRate = accountsExperOptionsService.mGetOptions(new String[]{"profile_rate"}, appcode).get("profile_rate");
            if (StringUtils.isNotBlank(profileRate) && ValidateUtil.isDecimal(profileRate)) {
                intervalTime = StringUtil.toLong(profileRate, intervalTime);
            }*/

            //获取当前秒时间戳
            Long currentTimeSeconds = DateUtil.currentTimeSeconds();

            //可以修改：上次修改时间 + 时间间隔 <= 当前时间
            if (timestampL + intervalTime <= currentTimeSeconds) {
                returnMap.put("status", "1");//可修改
                //ps. 此处可再添加一个东西，如果可修改，可以删除redis中对应数据，减少redis数据量
            } else {
                returnMap.put("status", "0");//不可修改
            }
        }
        returnMap.put("time", String.valueOf(timestampL));
        return returnMap;
    }

    /**
     * 设置芝麻认证
     *
     * @param accountUuid
     * @param zhimaCertification
     */
    @Transactional
    public void setZhimaCertification(String accountUuid, Integer zhimaCertification) {
        accountsInfoService.guestTicketExpire(accountUuid);
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(accountUuid);
        accountsInfo.setZhima_certification(zhimaCertification);
        accountsInfo.setGender_certification(zhimaCertification);
        accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
        this.accountsInfoDao.merge(accountsInfo);
        Map<String, String> hashValues = new HashMap<>();
        hashValues.put(UuidInfoField.ZHIMA_CERTIFICATION, zhimaCertification.toString());
        // 2024.03.22 不处理，后续直接删缓存
//        hashValues.put(UuidInfoField.GENDER_CERTIFICATION, zhimaCertification.toString());
        if(Objects.equals(zhimaCertification, CommConst.YES_1)){
            hashValues.put("zhima_certification_history", zhimaCertification.toString());
        }
        this.accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), hashValues);

        Map<String, String> map = new HashMap<>();
        map.put("zhima_certification", zhimaCertification.toString());
        map.put("account_uuid", accountUuid);

        final Integer SUCCESS = 1;
        if (SUCCESS.equals(zhimaCertification)) {
            SoaBaseParams soaBaseParams = RequestParams.getSoa_basic_java();
            antiSpamMqUtil.pushToUuidAFRefreshMq(accountUuid, soaBaseParams.getAppcode(), soaBaseParams.getCloned(), soaBaseParams.getApp_version(), soaBaseParams.getIp());
        }

        buryService.pushForumDoPublish(map);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public Map<String, Object> setAccountInfoByUuid(String uuid, Map<String, Object> accountInfo, Integer appcode, String smid) {
        Map<String, Object> result = new HashMap<>();

        Integer sex = MapUtils.getInteger(accountInfo, "sex");
        Integer age = MapUtils.getInteger(accountInfo, "age");
        Long birth = MapUtils.getLong(accountInfo, "birth");
        Integer sexual = MapUtils.getInteger(accountInfo, "sexual");
        Integer affectiveStatus = MapUtils.getInteger(accountInfo, "affectivestatus");
        String personalProfile = MapUtils.getString(accountInfo, "personal_profile");
        Integer datingIntention = MapUtils.getInteger(accountInfo, "dating_intention");
        String baseaddr = MapUtils.getString(accountInfo, "baseaddr");
        String hometown = MapUtils.getString(accountInfo, "hometown");
        int isSecret = MapUtils.getIntValue(accountInfo, "is_secret", 0);
        String educationLevel = MapUtils.getString(accountInfo, "education");
        String trade = MapUtils.getString(accountInfo, "trade");
        String profession = MapUtils.getString(accountInfo, "profession");
        Integer height = MapUtils.getInteger(accountInfo, "height");
        Integer weight = MapUtils.getInteger(accountInfo, "weight");
        Integer voiceSign = MapUtils.getInteger(accountInfo, "voice_sign");
        Integer income = MapUtils.getInteger(accountInfo, "income");


        if (sex == null && age == null && birth == null && sexual == null && affectiveStatus == null
                && StringUtils.isBlank(personalProfile) && datingIntention == null && StringUtils.isBlank(baseaddr) && StringUtils.isBlank(hometown)
                && StringUtils.isBlank(educationLevel) && StringUtils.isBlank(profession) && null == height && null == weight && null == voiceSign && null == income) {
            return result;
        }

        // 职业校验
        AccountInfoValid.validProfession(trade, profession);
        // 学历校验
        AccountInfoValid.validEducation(educationLevel);

        Map<String, Object> accountsMap = accountsInfoService.getInfoByUuid(new String[]{uuid}, new String[]{"avatar", "account_name", "sex_type", "personal_profile", "pass_voice_sign_url", "education", "profession", "height", "weight", "login_appcode"}, "1", false, false).get(uuid);
        Accounts accounts = new Accounts();
        accounts.setAvatar(MapUtils.getString(accountsMap, "avatar"));
        accounts.setAccount_name(MapUtils.getString(accountsMap, "account_name"));
        accounts.setSex_type(MapUtils.getInteger(accountsMap, "sex_type"));
        Integer loginappCode = MapUtils.getInteger(accountsMap, "login_appcode");
        if (null == appcode) {
            appcode = loginappCode;
            if (null == appcode) {
                appcode = 1;
            }
        }
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(uuid);
        Map<String, String> infoCache = Maps.newHashMap();
//        Map<String, String> secretCache = Maps.newHashMap();

        if (sex != null) {
            //性别已经设置的不允许修改
            Integer sexType = accountsManager.getSexTypeByAccountUuid(uuid);
            if (!Objects.equals(1, sexType) && !Objects.equals(2, sexType) && !Objects.equals(sexType, sex)) {
                Integer updateSexNumber = accountsInfo.getUpdate_sex_number();
                accountsInfo.setUpdate_sex_number(updateSexNumber == null ? 1 : (updateSexNumber + 1));
                accountsInfo.setSex_type_is_secret(isSecret);
                accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
                accountsManager.updateSexType(sex, uuid);
                infoCache.put("sex_type", String.valueOf(sex));
//                secretCache.put("sex_type", String.valueOf(sex));
            }
        }

        if (birth != null) {
            Integer changeAgeStatus = accountsInfo.getChange_age_status();
            if (changeAgeStatus == 2) {
                throw new ServiceException("data_error", "生日不允许修改哦~");
            }

            if (birth >= -********** && birth < DateUtil.currentTimeSeconds()) {
                accountsInfo.setBirth(birth);
                accountsInfo.setConstellation(DateUtil.getConstellationFromSeconds(birth));
                accountsInfo.setAge(ToolsService.calAgeFromBirth(birth));
                accountsInfo.setAge_is_secret(isSecret);
                accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
                infoCache.put(UuidInfoField.BIRTH, StringUtil.nullNumberToEmptyString(accountsInfo.getBirth()));
                // 2024.03.21 不写入缓存
//                infoCache.put(UuidInfoField.CONSTELLATION, StringUtils.trimToEmpty(accountsInfo.getConstellation()));
//                infoCache.put(UuidInfoField.AGE, StringUtil.nullNumberToEmptyString(accountsInfo.getAge()));
            }
        }

        if (birth == null && age != null && age > 0 && age < 255) {
            accountsInfo.setAge(age);
            accountsInfo.setAge_is_secret(isSecret);
            accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
            // 2024.03.21 不写入缓存
//            infoCache.put(UuidInfoField.AGE, StringUtil.nullNumberToEmptyString(accountsInfo.getAge()));
        }

        if (sexual != null) {
            accountsInfo.setSexual(sexual);
            accountsInfo.setSexual_is_secret(isSecret);
            accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
            infoCache.put("sexual", StringUtil.nullNumberToEmptyString(accountsInfo.getSexual()));
        }

        if (affectiveStatus != null) {
            accountsInfo.setAffectivestatus(affectiveStatus);
            accountsInfo.setAffectivestatus_is_secret(isSecret);
            accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
            infoCache.put("affectivestatus", StringUtil.nullNumberToEmptyString(accountsInfo.getAffectivestatus()));
        }

        if (StringUtils.isNotBlank(personalProfile)) {
            personalProfileInfoService.isPermit(uuid);

            personalProfile = personalProfile.trim();
            if (personalProfile.length() > 100) {
                throw new ServiceException("param_not_true", "个性签名不能超过100个字");
            }

            if (StringUtils.isNotBlank(personalProfile) && StringUtil.hasUcs4(personalProfile)) {
                throw new ServiceException("data_error", "个性签名暂不支持表情，请删掉后重新提交");
            }

            personalProfileInfoService.checkContent(personalProfile);
            accountsInfo.setPersonal_profile(personalProfile);
            //0表示审核中
            accountsInfo.setPersonal_profile_status(CommonAuditStatus.AUDITING.getStatus());
            //趣聊大厅展示个人简介判断字段，0表示不展示
            accountsInfo.setPersonal_profile_be_allow(0);
            String suggestion = "";
            String requestId = "";
            String responseStr = "";

            Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
            String type = RiskDetectEnum.TEXT_PERSONAL_PROFILE.name();

            // 生成审核记录
            PersonalProfileInfo personalProfileInfo = new PersonalProfileInfo();
            personalProfileInfo.setPersonal_profile(personalProfile);
            personalProfileInfo.setAccount_uuid(uuid);
            personalProfileInfo.setCreate_time(DateUtil.currentTimeSeconds());
            personalProfileInfo.setOperator("");
            personalProfileInfo.setRisk_level(AliyunTextStatusEnum.REVIEW.getStatus());
            personalProfileInfo.setStatus(PersonalProfileInfo.status.AUDITING.getValue());
            personalProfileInfo.setSuggestion(suggestion);
            personalProfileInfo.setRequest_id(requestId);
            personalProfileInfo.setResponse_str(responseStr);
            PersonalProfileInfo merge = personalProfileInfoService.merge(personalProfileInfo);

            ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(uuid, personalProfile, smid, String.valueOf(merge.getId()), type, appcode, cloned);
            suggestion = contentCheckDTO.getSuggestion();
            requestId= contentCheckDTO.getRequestId();
            responseStr = JSONObject.toJSONString(contentCheckDTO);
            LOGGER.info("数美内容审核.个性签名.审核结果.uuid={}.result={}", uuid, responseStr);
            if (!(RiskDetectRiskLevelEnum.PASS.name().equals(suggestion) || RiskDetectRiskLevelEnum.WAIT.name().equals(suggestion))) {
                // 判断敏感词被拒 还是整句
//                    String filteredText = MapUtils.getString(checkResult, "filteredText");
                String filteredText = contentCheckDTO.getBody();
                // 全星号 = 整句被拒绝
                if(StringUtils.isBlank(filteredText) || StringUtils.isBlank(filteredText.replaceAll("\\*", ""))){
                    // 发小秘书 sendAvatarCompareFailSystemMessage
//                    personalProfileInfoService.pushAuditFailSystemMsgV2(uuid);
                    throw new ServiceException(CodeStatus.PROFILE_ALL_SENSITIVE_WORDS);
                }else{
                    // 部分敏感词汇
                    suggestion = AliyunTextStatusEnum.PASS.name();
                    if(!personalProfile.contains("*")){
                        personalProfile = filteredText.replaceAll("\\*", "");
                    }else{
                        try{
                            StringBuilder sb = new StringBuilder();
                            for (int i = 0; i < personalProfile.length(); i++) {
                                String pWord = personalProfile.substring(i, i + 1);
                                String fWord = filteredText.substring(i, i + 1);
                                if(!fWord.equals("*") || pWord.equals("*")){
                                    sb.append(pWord);
                                }
                            }
                            personalProfile = sb.toString();
                            logger.info("个性签名敏感词替换成功,uuid={},personalProfile={},filteredText={}", uuid, personalProfile, filteredText);
                        }catch (Exception e){
                            logger.warn("个性签名敏感词替换失败,uuid={},personalProfile={},filteredText={}", uuid, personalProfile, filteredText, e);
                            personalProfile = filteredText.replaceAll("\\*", "");
                        }
                    }
                }
            }

            infoCache.put("personal_profile", personalProfile);
            infoCache.put("personal_profile_status", String.valueOf(CommonAuditStatus.AUDITING.getStatus()));
            //趣聊大厅展示个人简介判断字段，0表示不展示
            infoCache.put("personal_profile_be_allow","0");
            // 更新个人简介审核记录
            updatePersonalProfile(merge, suggestion, PersonalProfileInfo.status.AUDITING, requestId, responseStr);

        }

        Map<String, Object> datingIntentionBuryMap = null;
        if (datingIntention != null) {
            if (!Objects.equals(datingIntention, accountsInfo.getDating_intention())) {
                datingIntentionBuryMap = new HashMap<>();
                datingIntentionBuryMap.put("account_uuid", uuid);
                datingIntentionBuryMap.put("dating_intention", datingIntention);
                datingIntentionBuryMap.put("last_activity_time", accountsInfo.getUpdate_time());
                datingIntentionBuryMap.put("update_type", "dating_intention");
            }

            accountsInfo.setDating_intention(datingIntention);
            accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
            infoCache.put("dating_intention", String.valueOf(datingIntention));
        }

        if (StringUtils.isNotBlank(baseaddr)) {
//            LOGGER.info("测试日志 - uuid={}, baseaddr={}.", uuid, baseaddr);
            // 校验传参
            // baseaddr客户端传入参数可能有误，需要进行处理
            if(baseaddr.indexOf("null") != -1) {
                LOGGER.warn("客户端传入baseaddr字段数据错误，accountUuid={}，baseaddr={}", uuid, baseaddr);
            }else {
                baseaddr = regionService.correctBaseaddr(baseaddr);
                accountsInfo.setBaseaddr(baseaddr);
                accountsInfo.setBaseaddr_is_secret(isSecret);
                accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
                infoCache.put("baseaddr", baseaddr.trim());
//                secretCache.put("baseaddr", String.valueOf(isSecret));
            }
        }

        if (StringUtils.isNotBlank(hometown)) {
            accountsInfo.setHometown(hometown);
            accountsInfo.setHometown_is_secret(isSecret);
            accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
            infoCache.put("hometown", hometown.trim());
//            secretCache.put("hometown", String.valueOf(isSecret));
        }

//        LOGGER.info("设置个人资料,用户uuid={},education={},profession={},height={},weight={}", uuid, educationLevel, profession, height, weight);
        if (StringUtils.isNotBlank(educationLevel)) {
            accountsInfo.setEducation_level(educationLevel);
            infoCache.put("education", educationLevel.trim());
        }

        if (StringUtils.isNotBlank(trade) && StringUtils.isNotBlank(profession)) {
            accountsInfo.setProfession(profession);
            accountsInfo.setTrade(trade);
            infoCache.put("profession", profession.trim());
            infoCache.put("trade", trade);
        }

        if (null != height && height != 0) {
            if (height < 140 || height > 250) {
                throw new ServiceException(CodeStatus.INVALID_HEIGHT_DATA);
            }
            accountsInfo.setHeight(height);
            infoCache.put("height", String.valueOf(height));
        }

        if (null != weight && weight != 0) {
            if (weight < 35 || weight > 120) {
                throw new ServiceException(CodeStatus.INVALID_WEIGHT_DATA);
            }
            accountsInfo.setWeight(weight);
            infoCache.put("weight", String.valueOf(weight));
        }

        //年收入，income为年收入的id，目前的取值范围是1-7
        if(null != income && income >= 0 && income < 8){
            accountsInfo.setIncome(income);
            infoCache.put("income",String.valueOf(income));
        }


        String voiceSignUrl = MapUtils.getString(accountsMap, "pass_voice_sign_url");
        Integer heightValue = accountsInfo.getHeight() == null ? 0 : accountsInfo.getHeight();
        Integer weightValue = accountsInfo.getWeight() == null ? 0 : accountsInfo.getWeight();

//        Map<String, List<PersonalityLabel>> likeTypeLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(uuid), 1);
        Map<String, List<PersonalityLabel>> personalityLabels = accountsLabelService.mGetAccountLabelByType(Arrays.asList(uuid), 2);
//        List<PersonalityLabel> likeTypeLabel = likeTypeLabels.get(uuid);
        List<PersonalityLabel> personalityLabel = personalityLabels.get(uuid);
//        Map<String, List<Map<String, String>>> accountCoversMap = accountsInfoService.mgetAccountsCover(new String[]{uuid}, new boolean[]{false});
//        List<Map<String, String>> accountsPhoto = accountCoversMap.get(uuid);

        // 2020.05.14 喜欢的类型
		Map<String, List<Map<String, String>>> accountsLikeLabelMap = accountsLikeLabelService.listAccountsLikeLabelAll(uuid);

		AccountSchoolDto schoolInfo = schoolService.info(uuid, false);
		List<AccountLabelCfg> labels = accountLabelServiceV2.listMyLabelInfo(uuid);
		boolean isOrigin = false; // 是否主态
		List<AccountsLife> accountsLife = accountsLifeService.getAccountsLifeOrAccountsPhoto(uuid, isOrigin, false);

//        double newInfoPercent = accountsInfoService.calInfoPercent(accounts.getAvatar(), accounts.getAccount_name(), accounts.getSex_type(), accountsInfo.getBirth(), accountsInfo.getDating_intention(), accountsInfo.getSexual(), accountsInfo.getAffectivestatus(), accountsInfo.getBaseaddr(), accountsInfo.getHometown(), accountsInfo.getPersonal_profile()
//                , voiceSignUrl, accountsInfo.getEducation_level(), accountsInfo.getProfession(), String.valueOf(heightValue), String.valueOf(weightValue)
//                , likeTypeLabel, personalityLabel, accountsPhoto
//                ,accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey),accountsLikeLabelMap.get(LikeLabel.Type.MUSIC.mapKey),accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey),accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey), accountsInfo.getIncome());
		 double newInfoPercent = accountsInfoService.calInfoPercentV2(uuid, accounts.getAvatar(), accounts.getAccount_name(), accounts.getSex_type(), accountsInfo.getBirth(), accountsInfo.getAffectivestatus(), accountsInfo.getBaseaddr(), accountsInfo.getHometown(), accountsInfo.getPersonal_profile()
	            , voiceSignUrl, accountsInfo.getEducation_level(), accountsInfo.getProfession(), String.valueOf(heightValue), String.valueOf(weightValue), personalityLabel
	            , accountsLikeLabelMap.get(LikeLabel.Type.TV.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.STAR.mapKey), accountsLikeLabelMap.get(LikeLabel.Type.GAME.mapKey), income, schoolInfo, labels, accountsLife);

//		if (infoPercent >= 80) {
//            // 不增加 2024.05.16
////            accountDriverService.addKilometer(uuid, 30, "完善个人资料", "", null);
//        }
        if (!infoCache.isEmpty()) {
            this.accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), infoCache);
        }
//        if (!secretCache.isEmpty()) {
//            this.accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_SECRET_UUID.setArg(uuid), secretCache);
//        }
        if (accountsInfoService.merge(accountsInfo) != null) {
            this.accountUpdBury(uuid, appcode);
        }

        if (datingIntentionBuryMap != null) {
            buryService.pushForumDoPublish(datingIntentionBuryMap);
        }
//        LOGGER.info("保存完个人资料,用户uuid={},资料完善度={},是否大于80={}", uuid, newInfoPercent, newInfoPercent >= 80d);
        //根据newInfoPercent来推送给社区
        if (newInfoPercent == 100d) {
        	// 2020.06.24 资料100% 推这个
            this.accountTaskFinished(uuid, "1");
            modifyGrowScoreService.sendModifyGrowScoreMq(uuid, ModifyGrowScoreEnum.INFO_HUNDRED_PERCENT);
        } else if (newInfoPercent >= 80d) {
            modifyGrowScoreService.sendModifyGrowScoreMq(uuid, ModifyGrowScoreEnum.INFO_EIGHTY_PERCENT);
        }

        EventTrackingDO tracking = EventTrackingDO.create(uuid, AccountEvent.BASE_INFO_UPDATE);
        kafkaSinkUtil.push(EventConst.EVENT_TRACKING_TOPIC, tracking);
        behaviorReporter.report(uuid, tracking);
        // 吸引力刷新
        allureService.refreshAllureValidVersion(uuid);
        // 吸引力刷新 v2
        allureService.refreshAllureV2(uuid, AllureSceneEnum.CARD, null);

        return result;
    }

    /**
     * 用户资料卡进度
     * @param uuid
     * @return
     */
    public BigDecimal accountCardProgress(String uuid) {
        String[] fields = {
            UuidInfoField.AVATAR, UuidInfoField.REAL_AVATAR_CERTIFICATION, UuidInfoField.PROFILE_VERIFY_STATUS, UuidInfoField.ZHIMA_CERTIFICATION, UuidInfoField.PASS_VOICE_SIGN,
            UuidInfoField.HEIGHT, UuidInfoField.WEIGHT, UuidInfoField.ENABLE_LOCATION, UuidInfoField.HOMETOWN,
            UuidInfoField.AFFECTIVESTATUS, UuidInfoField.PROFESSION, UuidInfoField.EDUCATION,
            UuidInfoField.INCOME, UuidInfoField.PASS_INTRODUCTION_CONTENT, UuidInfoField.PASS_PERSONAL_PROFILE
        };
        Map<String, Object> accMap = accountsInfoService.getUserInfo(uuid, fields);
        BigDecimal result = BigDecimal.ZERO;
        String avatar = MapUtils.getString(accMap, UuidInfoField.AVATAR, "");
        if (StringUtils.isNotBlank(avatar) && !AvatarHandleService.isDefAvatar(avatar)) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}头像+1",uuid);
        }

        if (valEquals(UuidInfoField.REAL_AVATAR_CERTIFICATION, accMap, "1") && valEquals(UuidInfoField.PROFILE_VERIFY_STATUS, accMap, "1")) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}真人+1", uuid);
        }
        if (valEquals(UuidInfoField.ZHIMA_CERTIFICATION, accMap, "1")) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}实名+1", uuid);
        }
        if (valPresent(UuidInfoField.PASS_VOICE_SIGN, accMap)) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}语音+1", uuid);
        }

        if (!MapUtils.getString(accMap, UuidInfoField.HEIGHT, "0").equals("0")) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}身高+1", uuid);
        }
        if (!MapUtils.getString(accMap, UuidInfoField.WEIGHT, "0").equals("0")) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}体重+1", uuid);
        }
        if (!MapUtils.getString(accMap, UuidInfoField.INCOME, "0").equals("0")) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}收入+1", uuid);
        }
        if (!MapUtils.getString(accMap, UuidInfoField.AFFECTIVESTATUS, "0").equals("0")) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}情感状况+1", uuid);
        }
        if (valEquals(UuidInfoField.ENABLE_LOCATION, accMap, "1")) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}所在地+1", uuid);
        }
        if (valPresent(UuidInfoField.HOMETOWN, accMap)) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}家乡+1", uuid);
        }
        if (valPresent(UuidInfoField.PROFESSION, accMap)) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}职业+1", uuid);
        }
        if (valPresent(UuidInfoField.EDUCATION, accMap)) {
            result = result.add(BigDecimal.valueOf(0.025));
            LogUtil.info4Gray("完成度v2，{}学历+1", uuid);
        }
        if (valPresent(UuidInfoField.PASS_INTRODUCTION_CONTENT, accMap)) {
            result = result.add(BigDecimal.valueOf(0.1));
            LogUtil.info4Gray("完成度v2，{}自我介绍+1", uuid);
        }
        if (valPresent(UuidInfoField.PASS_PERSONAL_PROFILE, accMap)) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}个人签名+1", uuid);
        }
        // 生活
        List<AccountsLifeVo> lifeList = SpringContextHolder.getBean(AccountsLifeService.class).getAccountsLifeVo(uuid, false, false);
        // 没生活 取相册2～6
        int max;
        if (CollectionUtils.isNotEmpty(lifeList)) {
            max = Math.min(3, lifeList.size());
        } else {
            Set<String> visibleSeq = Sets.newHashSet("2", "3", "4", "5", "6");
            val photoMap = SpringContextHolder.getBean(AccountsPhotoService.class).mGetAccountsPhoto(new String[] {uuid}, new boolean[] {true});
            val photos = photoMap.get(uuid);
            max = CollectionUtils.isEmpty(photos) ? 0 : Math.min(3, (int) photos.stream().filter(p -> visibleSeq.contains(p.get("seq"))).count());
        }
        if (max > 0) {
            LogUtil.info4Gray("完成度v2，{}生活or相册+{}", uuid, max);
        }
        result = result.add(BigDecimal.valueOf(max).multiply(BigDecimal.valueOf(0.1)));

        // 标签
        List<Long> labels = SpringContextHolder.getBean(AccountLabelServiceV2.class).listLabelId(uuid);
        if (CollectionUtils.isNotEmpty(labels) && labels.size() >= 3) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}标签+1", uuid);
        }
        // 理想型
        AccountIdealTargetVo idealTarget = SpringContextHolder.getBean(IdealTargetService.class).info(uuid, true);
        if (idealTarget != null && StringUtils.isNotEmpty(idealTarget.getIdealTarget())) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}理想型+1", uuid);
        }
        // 动态
        Integer postCount = SoaService.getPostCount(uuid);
        if (postCount >= 1) {
            result = result.add(BigDecimal.valueOf(0.05));
            LogUtil.info4Gray("完成度v2，{}动态+1", uuid);
        }
        LogUtil.info4Gray("资料卡完成进度，uuid = {}, result = {}", uuid, result);
        return result;
    }

    private boolean valPresent(String field, Map<String, Object> map) {
        Object val = map.get(field);
        if (val == null) {
            return false;
        }
        if (val instanceof String) {
            String str = (String) val;
            return StringUtils.isNotEmpty(str);
        }
        return true;
    }

    private boolean valEquals(String field, Map<String, Object> map, Object expected) {
        Object val = map.get(field);
        if (val == null) {
            return false;
        }
        return expected.equals(val);
    }

    /**
     * @param uuid
     * @param personalProfile
     * @param oldClientPackage
     * @return
     */
    public void resetAccountsPersonalProfileByUuid(String uuid, String oldClientPackage) {
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(uuid);
        Map<String, String> infoCache = Maps.newHashMap();

        String personalProfile = "";

        accountsInfo.setPersonal_profile(personalProfile);
        //0表示审核中
        accountsInfo.setPersonal_profile_status(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        //趣聊大厅展示个人简介判断字段，0表示不展示
        accountsInfo.setPersonal_profile_be_allow(1);

        infoCache.put("personal_profile", personalProfile);
        infoCache.put("personal_profile_status", String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));
        infoCache.put("pass_personal_profile", personalProfile);
        infoCache.put("pass_personal_profile_status", String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));

        //趣聊大厅展示个人简介判断字段，0表示不展示
        infoCache.put("personal_profile_be_allow","1");

        this.accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), infoCache);

        if (accountsInfoService.merge(accountsInfo) != null) {
            this.accountUpdBury(uuid, CommConst.APPCODE_TAQU);
        }

        EventTrackingDO tracking = EventTrackingDO.create(uuid, AccountEvent.BASE_INFO_UPDATE);
        kafkaSinkUtil.push(EventConst.EVENT_TRACKING_TOPIC, tracking);
        behaviorReporter.report(uuid, tracking);
        allureService.refreshAllureV2(uuid, AllureSceneEnum.CARD, null);
    }

    /**
     * 个人简介复审
     */
    @Transactional
    public void review(String accountUuid) {
        List<Integer> ignoreList = Lists.newArrayList(
                PersonalProfileInfo.status.AUDITING.getValue(),
                PersonalProfileInfo.status.REVIEW_90.getValue()
        );

        PersonalProfileInfo lastEntity = personalProfileInfoService.getLastCreateByUuid(accountUuid);
        if (Objects.isNull(lastEntity)) {
            //该用户不存在审核记录，由历史原因造成，个人简介审核是后边才加入的需求
            Map<String, Object> infoMap = accountsInfoService.singleGetInfo(
                    accountUuid,
                    new String[]{"pass_personal_profile"}
            );
            String personalProfile = MapUtils.getString(infoMap, "pass_personal_profile");
            if (StringUtils.isNotBlank(personalProfile)) {
//                JSONObject checkResult = SoaService.aliyunTextCheckV2(accountUuid, personalProfile);
//                String suggestion = MapUtils.getString(checkResult, "suggestion", AliyunTextStatusEnum.BLOCK.name());
//                String requestId= MapUtils.getString(checkResult, "requestId", "");
//                String responseStr = checkResult.toJSONString();
                Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
                Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
                String suggestion = "";
                String requestId = "";
                String responseStr = "";

                // 生成审核记录
                PersonalProfileInfo personalProfileInfo = new PersonalProfileInfo();
                personalProfileInfo.setPersonal_profile(personalProfile);
                personalProfileInfo.setAccount_uuid(accountUuid);
                personalProfileInfo.setCreate_time(DateUtil.currentTimeSeconds());
                personalProfileInfo.setOperator("");
                personalProfileInfo.setRisk_level(AliyunTextStatusEnum.REVIEW.getStatus());
                personalProfileInfo.setStatus(PersonalProfileInfo.status.AUDITING.getValue());
                personalProfileInfo.setSuggestion(suggestion);
                personalProfileInfo.setRequest_id(requestId);
                personalProfileInfo.setResponse_str(responseStr);
                PersonalProfileInfo merge = personalProfileInfoService.merge(personalProfileInfo);

                ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accountUuid, personalProfile, CommConst.OLD_CLIENT_PACKAGE, String.valueOf(merge.getId()), RiskDetectEnum.TEXT_PERSONAL_PROFILE.name(), appcode, cloned);
                suggestion = contentCheckDTO.getSuggestion();
                requestId= contentCheckDTO.getRequestId();
                responseStr = JSONObject.toJSONString(contentCheckDTO);
                updatePersonalProfile(merge, suggestion, PersonalProfileInfo.status.REVIEW_90, requestId, responseStr);
                return;
            }
        } else {
            if (!ignoreList.contains(lastEntity.getStatus())) {
//                JSONObject checkResult = SoaService.aliyunTextCheckV2(accountUuid, lastEntity.getPersonal_profile());
//                String suggestion = MapUtils.getString(checkResult, "suggestion", AliyunTextStatusEnum.BLOCK.name());
//                String requestId= MapUtils.getString(checkResult, "requestId", "");
//                String responseStr = checkResult.toJSONString();

                Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
                Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
                String suggestion = "";
                String requestId = "";
                String responseStr = "";

                // 生成审核记录
                PersonalProfileInfo personalProfileInfo = new PersonalProfileInfo();
                personalProfileInfo.setPersonal_profile(lastEntity.getPersonal_profile());
                personalProfileInfo.setAccount_uuid(accountUuid);
                personalProfileInfo.setCreate_time(DateUtil.currentTimeSeconds());
                personalProfileInfo.setOperator("");
                personalProfileInfo.setRisk_level(AliyunTextStatusEnum.REVIEW.getStatus());
                personalProfileInfo.setStatus(PersonalProfileInfo.status.AUDITING.getValue());
                personalProfileInfo.setSuggestion(suggestion);
                personalProfileInfo.setRequest_id(requestId);
                personalProfileInfo.setResponse_str(responseStr);
                PersonalProfileInfo merge = personalProfileInfoService.merge(personalProfileInfo);

                ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(accountUuid, lastEntity.getPersonal_profile(), CommConst.OLD_CLIENT_PACKAGE, String.valueOf(merge.getId()), RiskDetectEnum.TEXT_PERSONAL_PROFILE.name(), appcode, cloned);
                suggestion = contentCheckDTO.getSuggestion();
                requestId= contentCheckDTO.getRequestId();
                responseStr = JSONObject.toJSONString(contentCheckDTO);
                updatePersonalProfile(merge, suggestion, PersonalProfileInfo.status.REVIEW_90,  requestId, responseStr);
                return;
            }
        }

        logger.info("不操作个人简介复审，accountUuid: {}", accountUuid);
    }

    @Transactional
    public void updatePersonalProfile(PersonalProfileInfo personalProfileInfo, String suggestion, PersonalProfileInfo.status status, String requestId, String responseStr) {
        Integer textRiskLevel = AliyunTextStatusEnum.getStatusByName(suggestion);
        personalProfileInfo.setRisk_level(textRiskLevel);
        personalProfileInfo.setStatus(status.getValue());
        personalProfileInfo.setSuggestion(suggestion);
        personalProfileInfo.setRequest_id(requestId);
        personalProfileInfo.setResponse_str(responseStr);
        PersonalProfileInfo merge = personalProfileInfoService.merge(personalProfileInfo);
        try {
            if (Objects.equals(AliyunTextStatusEnum.BLOCK.getStatus(), textRiskLevel)) {
                BuryService.pushPersonalProfileAutoAudit(CommonAuditStatus.AUDIT_FAIL.getStatus(), merge.getId(), "数美自动审核", "数美自动封禁");
            } else {
                BuryService.pushPersonalProfileAutoAudit(CommonAuditStatus.AUDIT_SUCCESS.getStatus(), merge.getId(), "数美自动审核", "");
            }
        } catch (Exception e) {
           logger.error("个人简介推入自动审核失败。", e);
        }
    }

    /**
     * 语音认证(新接口)
     *
     * @param ticketId
     * @param verify
     */
    @Transactional
    public void voiceVerify(String ticketId, String verify) {
        if (StringUtils.isBlank(ticketId)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        GetByTicketVo ticketVo = accountsInfoService.getByTicketIfGuestExpire(ticketId);

        String mobile = accountsManager.getMobileByUuid(ticketVo.getUuid());
        if (StringUtils.isBlank(mobile)) {
            throw new ServiceException(CodeStatus.NEED_CHECK_MOBILE);
        }

        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.VOICE_VERIFY, mobile, verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }

        this.setVoiceCerify(ticketVo.getAccount_uuid());
    }

    /**
     * 设置语音认证
     *
     * @param uuid
     */
    @Transactional
    public void setVoiceCerify(String uuid) {
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(uuid);
        accountsInfo.setVoice_certification(1);

        //修改风险等级
        accountsInfo.setVoice_certification(1);
        accountsInfoDao.merge(accountsInfo);

        // 推入队列通知反垃圾系统
        antiSpamMqUtil.pushToAccountActionMq(accountsInfo.getAccount_uuid(), "", AccountActionTypeEnum.EDIT_INFO,null);
        SoaBaseParams soaBaseParams = RequestParams.getSoa_basic_java();
        antiSpamMqUtil.pushToUuidAFRefreshMq(uuid, soaBaseParams.getAppcode(), soaBaseParams.getCloned(), soaBaseParams.getApp_version(), soaBaseParams.getIp());
        //修改redis
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), "voice_certification", "1");
    }

    /**
     * 设置驾照级别
     *
     * @param accountUuid
     * @param driverLevel
     * @param forceSet    是否强制更新 true:是; false:否;
     */
    @Transactional
    public void setDriverLevel(String accountUuid, String driverLevel, boolean forceSet) {
        if (!StringUtils.equalsAnyIgnoreCase(driverLevel, "C", "V")) {
            throw new ServiceException("level_error", "只能设置为C照或V照");
        }
        Map<String, Object> accountInfoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"account_type", UuidInfoField.DRIVER_LEVEL}, "1", true, true).get(accountUuid);
        if (!Objects.equals(MapUtils.getString(accountInfoMap, "account_type"), "1")) {
            throw new ServiceException("no_reg", "非注册用户不能设置驾照级别");
        }
        if (!forceSet && Objects.equals(MapUtils.getString(accountInfoMap, UuidInfoField.DRIVER_LEVEL), "V")) {//已经是V照，不能再设置为其他
            return;
        }
        AccountsForumProfile profile = accountsForumProfileService.findOrCreate(accountUuid);
        driverLevel = driverLevel.trim().toUpperCase();
        profile.setDriver_level(driverLevel);
        accountsForumProfileService.save(profile);
        // 不写缓存 2024.05.17
//        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), UuidInfoField.DRIVER_LEVEL, driverLevel);
    }

    /**
     * 根据uuid获取保密设置，如果存在游客uuid，直接返回ticket_expire
     *
     * @param uuids
     * @param fields
     * @return
     */
    public Map<String, Map<String, String>> getSecretConfigByUuids(String[] uuids, String[] fields) {
        Map<String, Map<String, String>> result = new HashMap<>();

        for (int i = 0; i < uuids.length; i++) {
            String uuid = uuids[i];
            Map<String, String> secretConfig = this.getSecretConfig(uuid, fields);
            result.put(uuid, secretConfig);
        }
        return result;
    }

    /**
     * 完成社区任务
     *
     * <AUTHOR>
     * @date 2020/06/24 14:59
     * @param accountUuid
     * @param taskId
     */
    public void accountTaskFinished(String accountUuid, String taskId) {
        String profileTaskStatusKey = "account:profile:task:status:" + accountUuid;
        if (bizRedisTemplate.hasKey(profileTaskStatusKey)) {
            String profileTaskStatus = bizRedisTemplate.opsForValue().get(profileTaskStatusKey);
            if ("0".equals(profileTaskStatus)) {
                logger.info("新版profile用户未完成资料，退出{}", accountUuid);
                return;
            }
        }
        Map<String, String> data = new HashMap<>();
        data.put("account_uuid", accountUuid);
        data.put("task_id", taskId);
        MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("handleAccountTask", data, null);
        if (mqResponse.fail()) {
            logger.error("用户修改资料任务推tqmq失败，用户uuid:{}, 失败码:{}，失败原因:{}", accountUuid, mqResponse.getCode(), mqResponse.getMsg());
        }
    }

    /**
     * 获取邀请信息判断
     * @param accountUuid
     * @return
     */
    public Map<String, Object> getAccountInviteInfosByUuid(String accountUuid) {
        List<Map<String, String>> accountsAllPhotos = accountsPhotoService.getAccountsAllPhoto(accountUuid, false);
        String accountCoverCheck = "0";
        for (Map<String, String> photoMap: accountsAllPhotos) {
            String seq = StringUtil.nullObjectToEmptyString(photoMap.get("seq"));
            String statusStr = StringUtil.nullObjectToEmptyString(photoMap.get("status"));
            Integer photoStatus = -2;
            if(StringUtils.isNotBlank(statusStr)){
                try{
                    photoStatus = Integer.parseInt(statusStr);
                }catch (Exception e){
                    logger.warn("判断邀请状态失败，", e);
                }
            }
            if(!seq.equals("1")){
                // 审核通过
                if(photoStatus.equals(AccountsPhoto.Status.PASS.getValue())){
                    accountCoverCheck = "2";
                    break;
                }else if(AccountsPhotoService.PHOTO_UNCHECK_STATUS_ARR.contains(photoStatus)) {
                    accountCoverCheck = "1";
                }
            }
        }

        // 判断是否有审核中的签名
        String personProfileInfoCheck = "0";
        PersonalProfileInfo personalProfileInfo = personalProfileInfoService.getLastCreateByUuid(accountUuid);
        if(personalProfileInfo != null){
            // 审核通过
            if(CommonAuditStatus.AUDIT_SUCCESS.getStatus().equals(personalProfileInfo.getStatus())){
                personProfileInfoCheck = "2";
            }else if(CommonAuditStatus.AUDITING.getStatus().equals(personalProfileInfo.getStatus())){
                personProfileInfoCheck = "1";
            }
        }
        Map<String, Object> inviteInfos = new HashMap<>();
        inviteInfos.put("accountCoverCheck", accountCoverCheck);
        inviteInfos.put("personProfileInfoCheck", personProfileInfoCheck);
        return inviteInfos;
    }

    /**
     * 设置业务级实名认证
     */
    @Transactional
    public void setChatRealCertification(String accountUuid, Integer chatRealCertification) {
        // store into db
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(accountUuid);
        accountsInfo.setChat_real_certification(chatRealCertification);
        accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
        this.accountsInfoDao.merge(accountsInfo);

        Map<String, String> hashValues = new HashMap<>();
        hashValues.put("chat_real_certification", chatRealCertification.toString());
        this.accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), hashValues);

//        Map<String, String> map = new HashMap<>();
//        map.put("chat_real_certification", chatRealCertification.toString());
//        map.put("account_uuid", accountUuid);

        final Integer SUCCESS = 1;
        if (SUCCESS.equals(chatRealCertification)) {
//            accountDriverService.addKilometer(accountUuid, 29, "实名认证通过", "", null);

            SoaBaseParams soaBaseParams = RequestParams.getSoa_basic_java();
            antiSpamMqUtil.pushToUuidAFRefreshMq(accountUuid, soaBaseParams.getAppcode(), soaBaseParams.getCloned(), soaBaseParams.getApp_version(), soaBaseParams.getIp());
        }

//        buryService.pushForumDoPublish(map);
    }

}
