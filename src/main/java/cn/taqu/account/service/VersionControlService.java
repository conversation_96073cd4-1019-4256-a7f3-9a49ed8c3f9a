package cn.taqu.account.service;

import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Objects;
import com.google.common.collect.Maps;

import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;

/**
 * 
 * 版本控制助手
 * 
 * <AUTHOR>
 * @date 2021/04/02
 */
@Service
public class VersionControlService {

    private static final Logger LOGGER = LoggerFactory.getLogger(VersionControlService.class);

    @Autowired
    private AccountsInfoService accountsInfoService;
    
    /**
     * 给个非常大的版本号
     */
    private static final Long MAX_VERSION = 999999999L;
    
    /**
     * key:平台，value：版本 
     * 
     * key：1 安卓； key：2 ios；
     */
    private static Map<Integer, Long> BLACK_LIST_CERTIFICATION_MAP = Maps.newHashMap();

    
    public static void setBlackListCertificationConfig(String json) {
        if(StringUtils.isBlank(json)) {
            LOGGER.error("blackListCertificationConfig版本控制配置错误，未配置");
            return;
        }
        try {
            BLACK_LIST_CERTIFICATION_MAP = JsonUtils.stringToObject2(json, new TypeReference<Map<Integer, Long>>(){});
        } catch (Exception e) {
            LOGGER.error("blackListCertificationConfig版本控制配置错误，json={}", json, e);
        }
    }

    
    /**
     * 相册张数
     * 
     * key:平台，value：版本 
     * 
     * key：1 安卓； key：2 ios；
     */
    private static Map<Integer, Long> PHOTO_NUM_MAP = Maps.newHashMap();
    
    
    public static void setPhotoNumConfig(String json) {
        if(StringUtils.isBlank(json)) {
            LOGGER.error("photoNumConfig版本控制配置错误，未配置");
            return;
        }
        try {
            PHOTO_NUM_MAP = JsonUtils.stringToObject2(json, new TypeReference<Map<Integer, Long>>(){});
        } catch (Exception e) {
            LOGGER.error("photoNumConfig版本控制配置错误，json={}", json, e);
        }
    }
    
    /**
     * 用户版本是否是新版 大于等于
     * 
     * @param accountUuid
     * @return 默认 false ；true-大于等于，false-小于
     */
    public boolean isNewBlackListCertificationConfig(String accountUuid) {
        boolean isNew = false;
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        Integer platformId = soaBaseParams.getPlatform_id();
        Long appVersion = soaBaseParams.getApp_version();
        // 查用户系统
        if(platformId == null || appVersion == null) {
            if(StringUtils.isBlank(accountUuid)) {
                LOGGER.warn("blackListCertificationConfig版本控制异常，用户uuid为空");
                return isNew;
            }
            // soa获取
            Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[] { accountUuid }, new String[] { "last_platform_id","last_app_version" }, "1", false, false).get(accountUuid);
            platformId = MapUtils.getInteger(infoMap, "last_platform_id", null);
            appVersion = MapUtils.getLong(infoMap, "last_app_version", null);
        }
        
        // 还为null 直接返回
        if(platformId == null || appVersion == null) {
            LOGGER.warn("blackListCertificationConfig版本控制异常，未查询到登录信息，uuid={}", accountUuid);
            return isNew;
        }
        // 判断是否 3 ipad
        if (Objects.equal(platformId, 3)) {
            platformId = 2;
        }
        
        Long versionControl = MapUtils.getLong(BLACK_LIST_CERTIFICATION_MAP, platformId, null);
        if (versionControl == null) {
            LOGGER.warn("blackListCertificationConfig版本控制异常，使用最大版本，platformId={}", platformId);
            versionControl = MAX_VERSION;
        }
        if(appVersion.longValue() >= versionControl.longValue()) {
            return true;
        }
        
        return isNew;
    }
    
    /**
     * 用户版本是否是新版 大于等于
     * 
     * @param accountUuid
     * @return 默认 false ；true-大于等于，false-小于
     */
    public boolean isNewPhotoNumConfig() {
        boolean isNew = false;
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        Integer platformId = soaBaseParams.getPlatform_id();
        Long appVersion = soaBaseParams.getApp_version();
        if(platformId == null || appVersion == null) {
            LOGGER.warn("photoNumConfig版本控制未取到头参数");
            return isNew;
        }
        
        // 判断是否 3 ipad
        if (Objects.equal(platformId, 3)) {
            platformId = 2;
        }
        
        Long versionControl = MapUtils.getLong(PHOTO_NUM_MAP, platformId, null);
        if (versionControl == null) {
            LOGGER.warn("photoNumConfig版本控制异常，使用最大版本，platformId={}", platformId);
            versionControl = MAX_VERSION;
        }
        if(appVersion.longValue() >= versionControl.longValue()) {
            return true;
        }
        
        return isNew;
    }
    
    
    
}
