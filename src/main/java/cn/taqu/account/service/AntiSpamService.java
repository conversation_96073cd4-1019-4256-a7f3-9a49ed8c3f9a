package cn.taqu.account.service;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;

import cn.taqu.account.common.AccountActionTypeEnum;
import cn.taqu.account.common.AntiFraudResultEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.special.SpecialAccountHandler;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;

/**
 * <AUTHOR>  2019/3/11 1:56 PM
 */
@Service
public class AntiSpamService {
    private Logger logger = LoggerFactory.getLogger(AntiSpamService.class);

    /**
     * 账号密码登录新设备风控，默认开启 etcd配置
     */
    public static int SWITCH_PWD_LOGIN_DEVICE_RISK = 1;
    
    /**
     * 账号密码登录风控校验
     * 
     * @param accounts
     * @param token
     */
    public void checkPasswordLogin(String accountUuid, String mobile, String token) {
        
        if(Objects.equals(SWITCH_PWD_LOGIN_DEVICE_RISK, CommConst.YES_1)) {
            // 2023.10.11 需求文档：https://o15vj1m4ie.feishu.cn/wiki/wikcnF843w96bR1IynIcoXxPfmb
            // 判断是否特殊账号
            if (StringUtils.isNotBlank(mobile) && !SpecialAccountHandler.isAuditAccount(accountUuid)) {
                boolean isVerify = isVerify(accountUuid, token);
                
                if (isVerify) {
                    throw new ServiceException(CodeStatus.NEED_LOGIN_BY_VERIFICATION_CODE);
                }
            }
        }

        this.antiFraud("", AccountActionTypeEnum.LOGIN, null);

    }

    /**
     * 反垃圾系统行为风险认证
     *
     * @param uuid
     * @param actionTypeEnum
     */
    public void antiFraud(String uuid, AccountActionTypeEnum actionTypeEnum, Integer sexType) {
//        SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM).call("antiFraud", "antiFraud", uuid, actionTypeEnum.name(), sexType);
//        if (response.fail()) {
//            logger.error("[anti-fraud]调用反垃圾系统验证{}行为过程出错，错误码:{}，错误描述:{}", actionTypeEnum, response.getCode(), response.getMsg());
//            //调用失败，记录一条日志就好
//            return;
//        }
//
//        this.returnAntiFraudResult(response.getData());
    }

    private void returnAntiFraudResult(String responseData) {
        Map<String, String> map = JsonUtils.stringToObject2(responseData, new TypeReference<Map<String, String>>() {
        });
        String type = map.get("type");
        AntiFraudResultEnum resultEnum = AntiFraudResultEnum.getByName(type);

        if (resultEnum == AntiFraudResultEnum.NONE) {
            return;
        }

        if (resultEnum == null) {
            throw new ServiceException("anti_fraud_" + type.toLowerCase(), "需要" + type + "认证");
        }

        throw new ServiceException(resultEnum.getCode(), resultEnum.getMsg());
    }

    /**
     * 人机验证成功后清除判定结果、保存有效期等后置操作
     *
     * @param accountUuid
     */
    public void verifyValidHandle(String accountUuid) {
//        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM).call("verify", "verifyValidHandle", accountUuid);
//        if (soaResponse.fail()) {
//            //只记录错误日志，不用抛出异常
//            logger.error("调用反垃圾清除人机判定结果失败，错误码{},错误信息{}", soaResponse.getCode(), soaResponse.getMsg());
//        }
    }

    /**
     * 判断是否需要短信校验
     *
     * @param accountUuid 账户uuid
     * @param token
     */
    public boolean isVerify(String accountUuid, String token) {
        Map<String,String> map = Maps.newHashMap();
        map.put("accountUuid", accountUuid);
        map.put("token", token);
        
        Object[] arr = {
            map
        };
        
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.RISK_DISCERN).call("accountTokenManage", "isVerify", arr);
        if (soaResponse.fail()) {
            logger.error("判断是否需要短信校验失败，错误码{},错误信息{}", soaResponse.getCode(), soaResponse.getMsg());
            return false;
        }
        
        String data = soaResponse.getData();
        return "true".equals(data);
    }

}
