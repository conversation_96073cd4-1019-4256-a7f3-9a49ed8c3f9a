package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;

import cn.taqu.account.dao.ChatRealCertificationLogDao;
import cn.taqu.account.dao.PhotoCompareLogDao;
import cn.taqu.account.model.ChatRealCertificationLog;
import cn.taqu.account.model.PhotoCompareLog;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片对比日志 服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 15:30
 */
@Service
@Slf4j
public class PhotoCompareLogService {

    @Autowired
    private PhotoCompareLogDao photoCompareLogDao;

    @Autowired
    private ChatRealCertificationLogDao chatRealCertificationLogDao;

    /**
     * 保存图片比对日志
     * @param accountUuid
     * @param basePhotoUrl
     * @param verifyPhotoUrl
     * @param similarityStatus
     * @param similarityScore
     * @param errorMsg
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void addLog(String accountUuid, String basePhotoUrl, String verifyPhotoUrl, Integer similarityStatus, Float similarityScore,
                       String errorMsg, Integer compareType){
        PhotoCompareLog photoCompareLog = new PhotoCompareLog();
        Long currTime = DateUtil.currentTimeSeconds();
        // 去掉前缀
        basePhotoUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(basePhotoUrl);
        verifyPhotoUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(verifyPhotoUrl);

        photoCompareLog.setAccountUuid(accountUuid)
            .setBasePhotoUrl(basePhotoUrl).setVerifyPhotoUrl(verifyPhotoUrl)
            .setSimilarityStatus(similarityStatus).setSimilarityScore(similarityScore).setErrorMsg(errorMsg)
            .setCreateTime(currTime).setUpdateTime(currTime)
            .setCompareType(compareType)
        ;
        log.info("添加图片对比日志=[{}]", JSON.toJSONString(photoCompareLog));
        photoCompareLogDao.merge(photoCompareLog);
    }

    /**
     * 保存图片比对日志
     * @param accountUuid
     * @param basePhotoUrl
     * @param verifyPhotoUrl
     * @param similarityStatus
     * @param similarityScore
     * @param errorMsg
     * @param compareType
     * @param source
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void addLogForChat(String accountUuid, String basePhotoUrl, String verifyPhotoUrl, Integer similarityStatus, Float similarityScore,
                       String errorMsg, Integer compareType, Integer source){
        ChatRealCertificationLog photoCompareLog = new ChatRealCertificationLog();
        Long currTime = DateUtil.currentTimeSeconds();
        // 去掉前缀
        basePhotoUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(basePhotoUrl);
        verifyPhotoUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(verifyPhotoUrl);

        photoCompareLog.setAccount_uuid(accountUuid);
        photoCompareLog.setCertification_photo_url(verifyPhotoUrl);
        photoCompareLog.setFace_photo_url(basePhotoUrl);
        photoCompareLog.setCreate_time(currTime);
        photoCompareLog.setStatus(similarityStatus);
        photoCompareLog.setSimilarity_score(similarityScore);
        photoCompareLog.setError_msg(errorMsg);
        photoCompareLog.setSource(source);
        log.info("添加图片对比日志=[{}]", JSON.toJSONString(photoCompareLog));
        chatRealCertificationLogDao.merge(photoCompareLog);
    }

}
