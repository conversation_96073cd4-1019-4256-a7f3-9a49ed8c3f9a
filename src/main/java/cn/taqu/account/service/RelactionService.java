package cn.taqu.account.service;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class RelactionService {
    private static final Map<String, String> RELACTION_MAP = new HashMap<>();

    public static void parseRelactionJson(String json) {
        if(StringUtils.isBlank(json)) {
            throw new ServiceException("json_empty", "公用跳转链接json不能为空");
        }
        Map<String, String> relactionMap = JsonUtils.stringToObject(json, new TypeReference<Map<String, String>>() {});
        RELACTION_MAP.putAll(relactionMap);
    }

    public static String getRelaction(String type, String defaultRelaction) {
        return MapUtils.getString(RELACTION_MAP, type, defaultRelaction);
    }
}
