package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.dao.AccountsPhotoImgQualityLogDao;
import cn.taqu.account.model.AccountsPhotoImgQualityLog;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-14 11:34
 */
@Service
public class AccountsPhotoImgQualityLogService {

    @Autowired
    private AccountsPhotoImgQualityLogDao accountsPhotoImgQualityLogDao;

    @Transactional
    public void merge(AccountsPhotoImgQualityLog accountsPhotoImgQualityLog){
        accountsPhotoImgQualityLogDao.merge(accountsPhotoImgQualityLog);
    }
}
