package cn.taqu.account.service;

import cn.taqu.account.common.AllureSceneEnum;
import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.common.FinishIncentiveTaskEnum;
import cn.taqu.account.common.RiskSafeHitTypeEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.cron.MobilePlaceTask;
import cn.taqu.account.dao.PersonalProfileInfoDao;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.model.AccountsInfo;
import cn.taqu.account.model.PersonalProfileInfo;
import cn.taqu.account.utils.TimeFormatUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 个人简介信息
 */
@Slf4j
@Service
@Transactional
public class PersonalProfileInfoService {

    @Autowired
    private PersonalProfileInfoDao personalProfileInfoDao;
    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private BuryService buryService;
    @Autowired
    private AllureService allureService;

    // 签名正则
    private static final Pattern PERSONAL_PROFILE_PURE_WORD_PATTERN = Pattern.compile("^[a-zA-Z]+$");

    private Logger LOGGER = LoggerFactory.getLogger(MobilePlaceTask.class);

//    private static String PERSONAL_PROFILE_AUDIT_SUCCESS_MSG;

//    private static String PERSONAL_PROFILE_AUDIT_FAIL_MSG;

//    private static String PERSONAL_PROFILE_EDIT_LIMIT_MSG;

//    private static Integer PERSONAL_PROFILE_PASS_LIMIT_TIME = 1;
//    private static Integer PERSONAL_PROFILE_REJECT_LIMIT_TIME = 2;

//    private static String PERSONAL_PROFILE_AUDIT_SUCCESS_MSG_V2;
//    private static String PERSONAL_PROFILE_AUDIT_FAIL_MSG_V2;
    private static String PERSONAL_PROFILE_EDIT_LIMIT_MSG_V2;
    private static Integer PERSONAL_PROFILE_SUBMIT_LIMIT_TIME_V2 = 5;


    /**
     * rediskey hash字段-通过次数
     */
    private static String PASS_KEY = "personalProfileAuditPassTimes";
    /**
     * rediskey hash字段-拒绝次数
     */
    private static String REJECT_KEY = "personalProfileAuditRejectTimes";

    /**
     * rediskey hash字段-提交次数
     */
    private static String SUBMIT_KEY = "personalProfileSubmitTimes";

    public static void initConf(String conf){
        Map<String,Object> map = JsonUtils.stringToObject2(conf, new TypeReference<Map<String, Object>>() {});
//        PERSONAL_PROFILE_PASS_LIMIT_TIME = MapUtils.getInteger(map,"personalProfilePassLimitTime",1);
//        PERSONAL_PROFILE_REJECT_LIMIT_TIME = MapUtils.getInteger(map,"personalProfileRejectLimitTime",2);
//        PERSONAL_PROFILE_AUDIT_SUCCESS_MSG = MapUtils.getString(map,"personalProfileAuditSuccessMsg","恭喜你，你的个人简介“${personalProfile}”审核通过拉，快去看看～");
//        PERSONAL_PROFILE_AUDIT_FAIL_MSG = MapUtils.getString(map,"personalProfileAuditFailMsg","很抱歉的通知您，您修改的简介因包含违规内容已被系统删除，请重新填写后提交");
//        PERSONAL_PROFILE_EDIT_LIMIT_MSG = MapUtils.getString(map,"personalProfileEditLimitMsg","今日修改次数已达上限，请明天再提交");

        PERSONAL_PROFILE_SUBMIT_LIMIT_TIME_V2 = MapUtils.getInteger(map,"personalProfileSubmitLimitTimeV2",5);
        PERSONAL_PROFILE_EDIT_LIMIT_MSG_V2 = MapUtils.getString(map,"personalProfileEditLimitMsgV2","今日修改次数已达上限，请明天再提交");
//        PERSONAL_PROFILE_AUDIT_SUCCESS_MSG_V2 = MapUtils.getString(map,"personalProfileAuditSuccessMsgV2","恭喜你，你的个人简介“${personalProfile}”审核通过拉，快去看看～");

    }

    /**
     * 插入操作
     * @param personalProfileInfo
     */
    public PersonalProfileInfo merge(PersonalProfileInfo personalProfileInfo){
        return personalProfileInfoDao.merge(personalProfileInfo);
    }

    /**
     * 审核，默认可以复审
     * @param id
     * @param operator
     */
    public void auditPass(Long id,String operator){
        PersonalProfileInfo personalProfileInfo = personalProfileInfoDao.get(id);
        if (Objects.isNull(personalProfileInfo)) {
            log.warn("审核失败，id为" + id + "的个人简介不存在");
            return;
        }

        personalProfileInfo.setAudit_time(DateUtil.currentTimeSeconds());
        personalProfileInfo.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        personalProfileInfo.setOperator(operator);
        merge(personalProfileInfo);

        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(personalProfileInfo.getAccount_uuid());
        Map<String, String> hashValue = new HashMap<>();
        hashValue.put("personal_profile_status",String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));
        hashValue.put("pass_personal_profile",personalProfileInfo.getPersonal_profile());
        hashValue.put("pass_personal_profile_status",String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));
        //personal_profile_be_allow为1表示审核通过，之前的逻辑在使用
        hashValue.put("personal_profile_be_allow","1");
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(personalProfileInfo.getAccount_uuid());
        accountsInfo.setPersonal_profile_be_allow(1);
        accountsInfo.setPersonal_profile_status(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
        accountsInfoService.merge(accountsInfo);

        //审核通过后每周限制录制一次
        String auditKey = RedisKeyConstant.ACCOUNT_AUDIT_INFO.setArg(personalProfileInfo.getAccount_uuid());
        Object  personalProfileAuditPassTimesStr = accountStringRedisTemplate.opsForHash().get(auditKey, PASS_KEY);
        Integer personalProfileAuditPassTimes = null == personalProfileAuditPassTimesStr ? 0 : Integer.parseInt(personalProfileAuditPassTimesStr.toString());
        accountStringRedisTemplate.opsForHash().put(auditKey,PASS_KEY,String.valueOf(personalProfileAuditPassTimes + 1));
        accountStringRedisTemplate.expire(auditKey, TimeFormatUtil.getRemainSecThisWeek(), TimeUnit.SECONDS);
        buryService.toBbsFinishIncentiveTask(personalProfileInfo.getAccount_uuid(), FinishIncentiveTaskEnum.STYLE_SIGN.getType());
    }

    /**
     * 删除已经通过后的个人简介
     * @param accountUuid
     * @param personalProfileStr
     */
    public boolean deletePass(String accountUuid, String personalProfileStr){
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();

        String[] fields = new String[] {"personal_profile","personal_profile_status","personal_profile_be_allow","pass_personal_profile","pass_personal_profile_status"};
        Map<String, Map<String, Object>> map = accountsInfoService.getInfoByUuid(new String[] {accountUuid}, fields, "1", true, false);
        Map<String, Object> userMap = map.get(accountUuid);
        Integer personalProfileStatus = MapUtils.getInteger(userMap,"personal_profile_status");
        String passPersonalProfile = MapUtils.getString(userMap,"pass_personal_profile","");
        if (!passPersonalProfile.equals(personalProfileStr)) {
            log.warn("删除的个人简介和审核通过的个人简介不一致，无法删除");
            return false;
        }

        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(accountUuid);
        accountsInfo.setPersonal_profile("");
        accountsInfo.setPersonal_profile_be_allow(0);
        accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
        accountsInfo.setPersonal_profile_status(CommonAuditStatus.NONE.getStatus());
        accountsInfoService.merge(accountsInfo);

        hashValue.put("pass_personal_profile","");
        hashValue.put("pass_personal_profile_status","");
        hashValue.put("personal_profile_be_allow","0");
        if(PersonalProfileInfo.status.AUDIT_SUCCESS.getValue().equals(personalProfileStatus)){
            hashValue.put("personal_profile","");
            hashValue.put("personal_profile_status","");
        }
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);
        allureService.refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
        return true;
    }

    /**
     * 判断该用户是否有通过的个人简介，true-有，false-没有
     * @param accountUuid
     * @return
     */
    private String getPassPersonalProfile(String accountUuid){
        Map<String, Map<String, Object>> usersResultMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"pass_personal_profile"}, "1", true,true);
        Map<String,Object> map = usersResultMap.get(accountUuid);
        return MapUtils.getString(map,"pass_personal_profile");
    }

    /**
     * 本周是否还可以进行个人简介
     * @param accountUuid
     * @return
     */
    public void isPermit(String accountUuid){
        // 新逻辑判断总次数
        String auditTimeKey = RedisKeyConstant.ACCOUNT_AUDIT_PERSONALPROFILE_TIMES_INFO.setArg(accountUuid);
        String value = accountStringRedisTemplate.opsForValue().get(auditTimeKey);
        Integer times = StringUtils.isBlank(value) ? 0 : Integer.parseInt(value);
        LOGGER.info("个人签名修改.uuid={}.当前次数={}.上限次数={}", accountUuid, times, PERSONAL_PROFILE_SUBMIT_LIMIT_TIME_V2);
        if(times >= PERSONAL_PROFILE_SUBMIT_LIMIT_TIME_V2){
            throw new ServiceException(PERSONAL_PROFILE_EDIT_LIMIT_MSG_V2);
        }else{
            times++;
            accountStringRedisTemplate.opsForValue().set(auditTimeKey, times.toString(), TimeFormatUtil.getTodayEndMsTimeStampWithRandom(), TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 个人简介内容验证
     * @param personalProfile
     */
    public void checkContent(String personalProfile){
        // 签名不能包含数字或字母
        if(PERSONAL_PROFILE_PURE_WORD_PATTERN.matcher(personalProfile).matches()){
            throw new ServiceException(CodeStatus.PROFILE_PURE_NUM_OR_WORD);
        }
    }

    public void batchAuditReject(List<Long> ids,String operator,String reason){
        if(CollectionUtils.isEmpty(ids)){
            return;
        }

        for(Long id: ids){
            PersonalProfileInfo personalProfileInfo = personalProfileInfoDao.get(id);
            if(null == personalProfileInfo){
                throw new ServiceException("personal_profile_info_not_exist", "审核失败，记录id为"+id+"的个人简介不存在");
            }

            //审核失败无需重新审核失败
            if(PersonalProfileInfo.status.AUDIT_FAIL.getValue().equals(personalProfileInfo.getStatus())){
                continue;
            }
            Integer currentStatus = personalProfileInfo.getStatus();
            personalProfileInfo.setAudit_time(DateUtil.currentTimeSeconds());
            personalProfileInfo.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
            personalProfileInfo.setOperator(operator);
            merge(personalProfileInfo);
            if(PersonalProfileInfo.status.AUDIT_SUCCESS.getValue().equals(currentStatus)){
                //审核通过后的拒绝
                LOGGER.info("通过的个人简介被拒绝,用户uuid={},个人简介={}",personalProfileInfo.getAccount_uuid(),personalProfileInfo.getPersonal_profile());
                boolean success = deletePass(personalProfileInfo.getAccount_uuid(),personalProfileInfo.getPersonal_profile());
                if (!success) {
                    return;
                }
            }else if(PersonalProfileInfo.status.AUDITING.getValue().equals(currentStatus)
            || PersonalProfileInfo.status.REVIEW_90.getValue().equals(currentStatus)){
                //审核中的拒绝
                AccountsInfo accountsInfo = accountsInfoService.findOrCreate(personalProfileInfo.getAccount_uuid());
                String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(personalProfileInfo.getAccount_uuid());
                Map<String, String> hashValue = new HashMap<>();

                String passPersonalProfile = getPassPersonalProfile(personalProfileInfo.getAccount_uuid());
                if(StringUtils.isNotEmpty(passPersonalProfile)){
                    hashValue.put("personal_profile",passPersonalProfile);
                    hashValue.put("personal_profile_status",String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));
                    accountsInfo.setPersonal_profile(passPersonalProfile);
                    accountsInfo.setPersonal_profile_status(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
                }else {
                    hashValue.put("personal_profile","");
                    hashValue.put("personal_profile_status","");
                    hashValue.put("personal_profile_be_allow","0");
                    accountsInfo.setPersonal_profile("");
                    accountsInfo.setPersonal_profile_status(CommonAuditStatus.NONE.getStatus());
                    accountsInfo.setPersonal_profile_be_allow(0);
                }

                accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

                accountsInfo.setUpdate_time(DateUtil.currentTimeSeconds());
                accountsInfoService.merge(accountsInfo);
            }
//            pushAuditFailSystemMsgV2(personalProfileInfo.getAccount_uuid());

            //审核不通过后每周限制录制两次
            String auditKey = RedisKeyConstant.ACCOUNT_AUDIT_INFO.setArg(personalProfileInfo.getAccount_uuid());

            // 拒绝后 拒绝次数+1，通过次数-1
            List<Object> list = accountStringRedisTemplate.opsForHash().multiGet(auditKey, Lists.newArrayList(PASS_KEY,REJECT_KEY));
            Integer personalProfileAuditPassTimes = null == list.get(0) ? 0 : Integer.parseInt(list.get(0).toString());
            Integer personalProfileAuditRejectTimes = null == list.get(1) ? 0 : Integer.parseInt(list.get(1).toString());
            Map<String, String> map = Maps.newHashMap();
            map.put(PASS_KEY, String.valueOf(personalProfileAuditPassTimes - 1));
            map.put(REJECT_KEY, String.valueOf(personalProfileAuditRejectTimes + 1));
            accountStringRedisTemplate.opsForHash().putAll(auditKey, map);

            //本周日晚上12点后可重新录制
            accountStringRedisTemplate.expire(auditKey, TimeFormatUtil.getRemainSecThisWeek(), TimeUnit.SECONDS);
        }
    }

    public PersonalProfileInfo getLastCreateByUuid(String accountUuid){
        List<PersonalProfileInfo> list = personalProfileInfoDao.getByUuid(accountUuid);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list.sort(Comparator.comparing(PersonalProfileInfo::getCreate_time).reversed());
        return list.get(0);
    }

    /**
     * 风控安全审核回调
     *
     * @param dto
     */
    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]个性签名审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        if (Objects.nonNull(riskSafeHitTypeEnum)) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    auditPass(Long.valueOf(dto.getBizId()), dto.getOperator());
                    break;
                case BLOCK:
                    batchAuditReject(Arrays.asList(Long.valueOf(dto.getBizId())), dto.getOperator(), dto.getBlockReason());
                    break;
                default:
                    break;
            }
        }
    }
}
