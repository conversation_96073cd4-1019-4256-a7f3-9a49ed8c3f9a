package cn.taqu.account.service;

import cn.taqu.account.constant.PersonalityLabelEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.PersonalityLabelDao;
import cn.taqu.account.model.PersonalityLabel;
import cn.taqu.account.search.LabelSearch;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.PageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动生成Service类
 */
@Service
public class PersonalityLabelService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.PersonalityLabel> {

    @Resource
    private PersonalityLabelDao personalityLabelDao;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    @Transactional(rollbackFor = Exception.class)
    public Long create(PersonalityLabel personalityLabel) {
        this.verifyData(personalityLabel);

        // 校验权重唯一性
        if (PersonalityLabelEnum.FRIENDSHIP_PREFERENCES.match(personalityLabel.getType())) {
            Long id = personalityLabelDao.sortIsExist(personalityLabel.getType(), personalityLabel.getSort());
            if (id != null) {
                throw new ServiceException("权重已存在, 请更改权重值");
            }
        }

        if (StringUtils.isBlank(personalityLabel.getDescription())) {
            personalityLabel.setDescription("");
        }
        if (personalityLabel.getSort() == null) {
            personalityLabel.setSort(0);
        }
        personalityLabel.setCreate_time(DateUtil.currentTimeSeconds());
        personalityLabel.setUpdate_time(DateUtil.currentTimeSeconds());
        personalityLabel = this.merge(personalityLabel);
        updateCache(personalityLabel);
        return personalityLabel.getId();
    }

    private void verifyData(PersonalityLabel personalityLabel) {
        if (StringUtils.isBlank(personalityLabel.getContent()) || personalityLabel.getType() == null
                || personalityLabel.getSex() == null || personalityLabel.getStatus() == null) {
            throw new ServiceException("data_error", "数据不完整");
        }
    }

    public PersonalityLabel getByIdFromDb(Long id) {
        return personalityLabelDao.getByIdFromDb(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(PersonalityLabel personalityLabel) {
        if (personalityLabel.getId() == null) {
            throw new ServiceException("id_empty", "id不能为空");
        }
        PersonalityLabel dbPersonalityLabel = personalityLabelDao.getByIdFromDb(personalityLabel.getId());
        if(dbPersonalityLabel == null) {
            throw new ServiceException("label_no_exists", "id为"+personalityLabel.getId()+"的标签信息不存在");
        }
        // 校验权重唯一性
        if (PersonalityLabelEnum.FRIENDSHIP_PREFERENCES.match(personalityLabel.getType())) {
            Long id = personalityLabelDao.sortIsExist(personalityLabel.getType(), personalityLabel.getSort());
            if (id != null && !Objects.equals(id, dbPersonalityLabel.getId())) {
                throw new ServiceException("权重已存在, 请更改权重值");
            }
        }

        dbPersonalityLabel.setContent(personalityLabel.getContent());
        dbPersonalityLabel.setDescription(personalityLabel.getDescription() == null ? "" : personalityLabel.getDescription());
        dbPersonalityLabel.setSecond_type(personalityLabel.getSecond_type());
        dbPersonalityLabel.setSex(personalityLabel.getSex());
        dbPersonalityLabel.setStatus(personalityLabel.getStatus());
        dbPersonalityLabel.setType(personalityLabel.getType());
        dbPersonalityLabel.setUpdate_time(DateUtil.currentTimeSeconds());
        dbPersonalityLabel.setSort(personalityLabel.getSort() == null ? 0 : personalityLabel.getSort());
        dbPersonalityLabel = this.merge(dbPersonalityLabel);
        updateCache(dbPersonalityLabel);
    }

    private void updateCache(PersonalityLabel personalityLabel) {
    	if(personalityLabel != null && personalityLabel.getDescription() == null) {
    		personalityLabel.setDescription("");
    	}
        accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.PERSONALITY_LABEL_DATA.setArg(personalityLabel.getId()), JsonUtils.objectToString(personalityLabel));
        accountStringRedisTemplate.opsForSet().add(RedisKeyConstant.PERSONALITY_LABEL_ID_SET.getPattern(), personalityLabel.getId().toString());
        accountStringRedisTemplate.opsForSet().add(RedisKeyConstant.PERSONALITY_LABEL_TYPE_ID.setArg(personalityLabel.getType()), personalityLabel.getId().toString());
    }

    public Page<PersonalityLabel> pageQuery(LabelSearch search, Integer pageNumber, Integer pageSize) {
        Collection<String> fields = Arrays.asList("id", "content","description", "type", "sex", "status", "second_type", "sort");
        Sql sql = Sql.build(PersonalityLabel.class, fields).eq("content", search.getContent()).eq("type", search.getType()).eq("status", search.getStatus()).orderBy("sort desc, id desc");
        PageData<PersonalityLabel> page = personalityLabelDao.queryForPage(sql, pageNumber, pageSize).convert(record -> {
            String count = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.LABEL_USE_COUNT.setArg(record.getId()));
            record.setUse_count(StringUtils.isBlank(count) ? 0 : Long.valueOf(count));
            return record;
        });
        return new PageImpl<>(page.getData(), PageUtil.newPageable(pageNumber, pageSize), page.getTotal());
    }

    public void updateStatusById(Integer status, Long id) {
        personalityLabelDao.updateStatusById(status, id);
    }

    public List<PersonalityLabel> findListByCache(Integer type) {
        Set<String> ids = accountStringRedisTemplate.opsForSet().members(RedisKeyConstant.PERSONALITY_LABEL_ID_SET.getPattern());
        if(ids == null || ids.isEmpty()) {
            List<Long> idList = personalityLabelDao.getAllId();
            ids = new HashSet<>();
            for(Long id : idList) {
                accountStringRedisTemplate.opsForSet().add(RedisKeyConstant.PERSONALITY_LABEL_ID_SET.getPattern(), id.toString());
                ids.add(id.toString());
            }
        }
        return this.findListByCache(ids, type);
    }

    public List<PersonalityLabel> findListByCache(Collection<String> idSet, Integer type) {
        // 过滤无效的id
        idSet = idSet.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        List<PersonalityLabel> labels = new ArrayList<>();
        Set<Long> needQuerySet = new HashSet<>();
        if (idSet != null && !idSet.isEmpty()) {
            for (String id : idSet) {
                String plstr = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.PERSONALITY_LABEL_DATA.setArg(id));
                if (StringUtils.isBlank(plstr)) {
                    needQuerySet.add(Long.valueOf(id));
                    continue;
                }

                PersonalityLabel pl = JsonUtils.stringToObject(plstr, PersonalityLabel.class);
                if(pl.getDescription() == null) {
                	needQuerySet.add(Long.valueOf(id));
                    continue;
                }
                if (pl == null || (pl.getStatus() !=  null && pl.getStatus() == 1)) continue;
                String useCountStr = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.LABEL_USE_COUNT.setArg(id));
                Long useCount = StringUtils.isBlank(useCountStr) ? 0L : Long.valueOf(useCountStr);
                pl.setUse_count(useCount);
                labels.add(pl);
            }
        }

        if (!needQuerySet.isEmpty()) {
            List<PersonalityLabel> personalityLabelList = this.personalityLabelDao.findByIds(needQuerySet);
            for (PersonalityLabel personalityLabel : personalityLabelList) {
                this.updateCache(personalityLabel);
                labels.add(personalityLabel);
            }
        }

        // 只有交友偏好需要排序
        if (PersonalityLabelEnum.FRIENDSHIP_PREFERENCES.match(type) && CollectionUtils.isNotEmpty(labels)) {
            return labels.stream().peek(label -> {
                if (label.getSort() == null) {
                    label.setSort(0);
                }
            }).sorted(Comparator.comparing(PersonalityLabel::getSort).reversed())
                    .collect(Collectors.toList());
        }

        return labels;
    }

    public List<PersonalityLabel> findList() {
    	return personalityLabelDao.queryAll(PersonalityLabelDao.FIELDS_ALL);
    }

    /**
     * 根据类型和性别获取标签，只返回标签名称和标签id，
     * @param type
     * @param sex
     * @return
     */
    public List<Map<String, String>> findByTypeAndSex(Integer type, Integer sex) {
        List<PersonalityLabel> personalityLabelList = personalityLabelDao.getSimpleByTypeAndSex(type, sex);
        List<Map<String, String>> result = new ArrayList<>(personalityLabelList.size());
        for(PersonalityLabel personalityLabel : personalityLabelList) {
            Map<String, String> data = new HashMap<>();
            data.put("id", personalityLabel.getId().toString());
            data.put("name", personalityLabel.getContent());
            result.add(data);
        }
        return result;
    }
}
