package cn.taqu.account.service;

import cn.taqu.account.dto.RiskAccountStatusCheckReqDTO;
import cn.taqu.account.dto.RiskAccountStatusCheckRespDTO;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 风控安全审核（https://o15vj1m4ie.feishu.cn/wiki/Mfigwy61YiMl0VkmsXYctDpcnQG）
 *
 * <AUTHOR>
 * @date 2023/12/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskSafeStatusService {

    private static final SoaServer RISK_SAFE_STATUS = new SoaServer("/soa/java/risk-safe-status");

    /**
     * 风控安全审核 @link https://o15vj1m4ie.feishu.cn/wiki/BwJ3w2oXwiBgH3kExogcfM4dnhg
     *
     * @param request
     * @return resp： true 通过，false不通过
     */
    public Boolean checkAccountStatus(RiskAccountStatusCheckReqDTO request) {
        try {
            Map<String, Object> data = Maps.newHashMap();
            data.put("appCode", request.getAppCode());
            data.put("accountUuid", request.getAccountUuid());
            data.put("safeId", request.getSafeId());
            data.put("token", request.getToken());
            Object[] form = {data};

            SoaResponse response = SoaClientFactory.create(RISK_SAFE_STATUS).call("statusUnified", "accountRiskStatus", form);
            log.info("【找回账号，用户状态风控请求】req: {}, resp:{}", JsonUtils.objectToString(request), response.getData());
            if (response.fail() || StringUtils.isBlank(response.getData())) {
                // TODO 临时处理
                return false;
            }

            RiskAccountStatusCheckRespDTO resp = JSON.parseObject(response.getData(), RiskAccountStatusCheckRespDTO.class);
            return Objects.equals(resp.getIsHit(), 1);
        } catch (Exception e) {
            log.error("风控异常", e);
        }

        return false;
    }

}
