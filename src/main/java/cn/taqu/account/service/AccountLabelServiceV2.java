package cn.taqu.account.service;

import cn.taqu.account.dto.PageParam;
import cn.taqu.account.model.AccountLabelCfg;
import cn.taqu.account.vo.req.LabelAdminReq;
import cn.taqu.account.vo.req.LabelCategoryAdminReq;
import cn.taqu.account.vo.resp.*;
import cn.taqu.core.orm.PageData;

import java.util.List;

/**
 * 用户标签V2
 *
 * <AUTHOR>
 * @date 2024/11/25 10:20 上午
 */
public interface AccountLabelServiceV2 {

    // ***************************** 后台相关service *****************************
    /**
     * 新增分类
     *
     * @param req
     */
    void addCategory(LabelCategoryAdminReq req);

    /**
     * 更新分类
     *
     * @param req
     */
    void updateCategory(LabelCategoryAdminReq req);

    /**
     * 分类详情
     *
     * @param id
     * @return
     */
    LabelCategoryResp getCategoryInfo(Long id);

    /**
     * 新增标签
     *
     * @param label
     */
    void add(AccountLabelCfg label);

    /**
     * 更新标签
     *
     * @param label
     */
    void update(AccountLabelCfg label);

    /**
     * 更新标签状态
     *
     * @param id
     * @param status
     */
    void updateStatus(Long id, Integer status);

    /**
     * 更新分类状态
     *
     * @param categoryId
     * @param categoryStatus
     */
    void updateCategoryStatus(Long categoryId, Integer categoryStatus);

    /**
     * 获取标签详情
     *
     * @param id
     * @return
     */
    LabelResp getInfo(Long id);

    /**
     * 分页数据
     *
     * @param req
     * @return
     */
    PageData<LabelPageResp> listPage(LabelAdminReq req);

    /**
     * 获取分类列表
     *
     * @return
     */
    List<LabelCategoryResp> listCategory(LabelCategoryAdminReq req);


    // ***************************** API, SOA 相关service *****************************

    /**
     * 获取我的分类标签
     *
     * @return
     */
    MyCategoryLabelResp getCategoryLabel(String accountUuid);

    /**
     * 分页标签
     *
     * @return
     */
    List<PageLabelResp> pageLabel(String accountUuid, Long categoryId, PageParam pageParam);

    /**
     * 设置标签
     *
     * @param accountUuid
     * @param labelIds
     */
    void setLabel(String accountUuid, List<Long> labelIds);

    /**
     * 获取我的标签id列表
     *
     * @return
     */
    List<Long> listLabelId(String accountUuid);

    /**
     * 获取用户分类
     *
     * @param accountUuid
     */
    List<AccountLabelCfg> listMyLabelInfo(String accountUuid);

    /**
     * 获取他人标签
     *
     * @param accountUuid
     * @param targetUuid
     * @return
     */
    GetTargetLabelResp getTargetLabel(String accountUuid, String targetUuid);

    /**
     * 获取他人标签【供给soa使用】需要管控实验
     *
     * @param accountUuid
     * @param targetUuid
     * @return
     */
    GetTargetLabelResp getTargetLabelForSoa(String accountUuid, String targetUuid);

}
