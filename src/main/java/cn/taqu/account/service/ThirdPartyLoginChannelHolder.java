package cn.taqu.account.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static cn.taqu.account.constant.RedisKeyConstant.ACCOUNT_THIRD_LOGIN_CHANNEL;

/**
 * <AUTHOR>
 * @date 2024/11/28 下午6:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdPartyLoginChannelHolder {

    private final StringRedisTemplate lockStringRedisTemplate;

    public void via(String openId, String channel) {
        log.info("third login openId = {}, channel = {}", openId, channel);
        String key = ACCOUNT_THIRD_LOGIN_CHANNEL.setArg(openId);
        lockStringRedisTemplate.opsForValue().set(key, channel, 30, TimeUnit.MINUTES);
    }

    public boolean validate(String openId, String channel) {
        String key = ACCOUNT_THIRD_LOGIN_CHANNEL.setArg(openId);
        String expected = lockStringRedisTemplate.opsForValue().get(key);
//        if (expected == null) {
//            return true;
//        }
        return channel.equals(expected);
    }

}
