package cn.taqu.account.service;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsAchievementDao;
import cn.taqu.account.model.AccountsAchievement;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 用户成就service
 */
@Service
public class AccountsAchievementService {
    private Logger logger = LoggerFactory.getLogger(AccountsAchievementService.class);
    private Comparator<String> achievementComparator = Comparator.comparingInt(o -> {
        int value = StringUtils.isBlank(o) ? 0 : Integer.valueOf(o);
        //等级成就勋章要排在最前面
        if(value == AccountsAchievement.EXPER_ACHIEVEMENT_ID) {
            value = -100;
        }
        return value;
    });
    @Autowired
    private AccountsAchievementDao accountsAchievementDao;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    /**
     * 添加成就勋章
     * @param accountUuid
     * @param achievementId
     * @param level
     */
    @Transactional
    public void addAcheivement(String accountUuid, Long achievementId, Integer level) {
        //已存在的情况
        if(accountsAchievementDao.achievementExists(accountUuid, achievementId, level)) {
            return;
        }

        AccountsAchievement accountsAchievement = new AccountsAchievement();
        accountsAchievement.setAccount_uuid(accountUuid);
        accountsAchievement.setAchievement_id(achievementId);
        accountsAchievement.setAchievement_level(level);
        accountsAchievement.setCreate_time(DateUtil.currentTimeSeconds());
        accountsAchievement.setIs_adorn(0);
        accountsAchievement = accountsAchievementDao.merge(accountsAchievement);

        Map<String, Object> accountsAchievementMap = this.getAllAchievement(accountUuid);
        Map<String, String> adornMap = (Map<String, String>)accountsAchievementMap.get("adorn");
        //已佩戴的该成就的勋章等级
        Integer adornedAchievementLevel = MapUtils.getInteger(adornMap, achievementId.toString());
        Integer toAdornAchievementLevel = adornedAchievementLevel;
        //当前是否有佩戴相同成就的不同等级的勋章，如果有的话，自动佩戴上该成就的最高等级勋章
        if(adornedAchievementLevel != null) {
            Map<String, TreeSet<String>> obtainMap = (Map<String, TreeSet<String>>)accountsAchievementMap.get("obtain");
            Integer maxLevel = this.getMaxLevelFromObtain(achievementId, obtainMap);
            if (maxLevel == null) {
                return;
            }
            Integer maxObtainAchievementLevel = Math.max(maxLevel, level);
            toAdornAchievementLevel = Math.max(maxObtainAchievementLevel == null ? 0 : maxObtainAchievementLevel, toAdornAchievementLevel);
        }
        //如果用户还没有佩戴该勋章，并且当前的勋章是等级成就勋章，直接佩戴
        else if(Objects.equals(achievementId, AccountsAchievement.EXPER_ACHIEVEMENT_ID)) {
            toAdornAchievementLevel = level;
        }
        //如果用户还没有佩戴该勋章，判断用户已佩戴勋章是否小于3个(不包括经验成就勋章)，是的话，自动佩戴上该勋章
        else {
            adornMap.remove(String.valueOf(AccountsAchievement.EXPER_ACHIEVEMENT_ID));//排除掉经验成就勋章
            int adornSize = adornMap == null ? 0 : adornMap.size();
            if(adornSize < 3) {
                toAdornAchievementLevel = level;
            }
        }

        if(Objects.equals(toAdornAchievementLevel, adornedAchievementLevel)) {
            this.saveToCache(accountsAchievement);
            return;
        }

        accountsAchievementDao.setAdorn(1, accountUuid, achievementId, toAdornAchievementLevel);
        if(adornedAchievementLevel != null) {
            accountsAchievementDao.setAdorn(0, accountUuid, achievementId, adornedAchievementLevel);
        }
        this.refreshCache(accountUuid);
    }

    /**
     * 回收成就勋章，如果回收的勋章已佩戴，则自动佩戴同成就的下一级勋章，如果没有下一级则直接摘掉，不佩戴新的
     * @param accountUuid
     * @param achievementId
     * @param level
     */
    public void cancelAchievement(String accountUuid, Long achievementId, Integer level) {
        //获取同成就下的所有等级勋章
        List<AccountsAchievement> accountsAchievementList = accountsAchievementDao.getAccountByAchievementId(accountUuid, achievementId);
        boolean isAdorn = false;//要回收的成就勋章是否已佩戴
        Long cancelId = null;//要撤销的成就勋章id
        Long autoAdornId = null;//要自动佩戴的成就勋章id
        //按等级从大到小排序
        Collections.sort(accountsAchievementList, (o1, o2) -> o2.getAchievement_level().compareTo(o1.getAchievement_level()) );
        for(AccountsAchievement accountsAchievement : accountsAchievementList) {
            if(Objects.equals(level, accountsAchievement.getAchievement_level())) {
                cancelId = accountsAchievement.getId();
                isAdorn = Objects.equals(1, accountsAchievement.getIs_adorn());
                continue;
            }

            if(isAdorn) {
                autoAdornId = accountsAchievement.getId();
                break;
            }
        }

        //如果要回收的成就勋章id为null
        if(cancelId == null) {
            return;
        }

        accountsAchievementDao.delById(cancelId);
        if(autoAdornId != null) {
            accountsAchievementDao.setAdornByIdList(1, Arrays.asList(autoAdornId));
        }
        this.refreshCache(accountUuid);
    }

    /**
     * 佩戴勋章，最多只能佩戴三个
     * @param accountUuid 用户uuid
     * @param achievementList 要佩戴的勋章数组，数组元素格式为"成就id_成就勋章等级"
     */
    @Transactional
    public void adornAchievement(String accountUuid, List<String> achievementList) {
        int len = achievementList == null ? 0 : achievementList.size();
        List<Object> toAdornList = new ArrayList<>();
        for(int i=0; i<len; i++) {
            String achievement = StringUtils.trimToEmpty(achievementList.get(i));
            if(achievement.isEmpty()) {
                continue;
            }
            if(!this.isAchievement(achievement)) {
                logger.warn("成就勋章数据格式错误，无法佩戴，{}", achievementList);
                throw new ServiceException(CodeStatus.ADORN_ACHIEVE_ERROR);
            }

            Long achievementId = Long.valueOf(achievement.split("_")[0]);
            //经验成就勋章不允许手动佩戴
            if(Objects.equals(AccountsAchievement.EXPER_ACHIEVEMENT_ID, achievementId)) {
                throw new ServiceException(CodeStatus.ADORN_EXPER_ACHEIVE);
            }

            toAdornList.add(achievement);
        }

        //佩戴数量不能超过3个
        if(toAdornList.size() > 3) {
            throw new ServiceException(CodeStatus.ADORN_EXCEED);
        }

        List<Long> adornIdList = new ArrayList<>();
        if(!toAdornList.isEmpty()) {
            String redisKey = RedisKeyConstant.ACCOUNT_ACHIEVEMENT.setArg(accountUuid);
            List<Object> values = accountStringRedisTemplate.opsForHash().multiGet(redisKey, toAdornList);
            //如果从缓存中获取到的数据包含null值，说明还有未获取的勋章，不让佩戴
            if(values.contains(null)) {
                throw new ServiceException(CodeStatus.ADORN_NO_EXISTS);
            }

            for(Object value : values) {
                Map<String, String> achievementValue = JsonUtils.stringToObject(value.toString(), new TypeReference<Map<String, String>>() {});
                adornIdList.add(MapUtils.getLongValue(achievementValue, "id"));
            }
        }

        accountsAchievementDao.setAdornByUuid(0, accountUuid, Arrays.asList(AccountsAchievement.EXPER_ACHIEVEMENT_ID));
        if(adornIdList.size() > 0) {
            accountsAchievementDao.setAdornByIdList(1, adornIdList);
        }
        this.refreshCache(accountUuid);
    }

    /**
     * 卸下成就勋章
     * @param accountUuid
     * @param achievementId
     * @param level
     */
    @Transactional
    public void unadornAchievement(String accountUuid, Long achievementId, Integer level) {
        //经验成就勋章不允许手动卸下
        if(Objects.equals(AccountsAchievement.EXPER_ACHIEVEMENT_ID, achievementId)) {
            throw new ServiceException(CodeStatus.UNADORN_EXPER_ACHEIVE);
        }

        int count;
        if(level == 0) {
            count = accountsAchievementDao.setAdorn(0, accountUuid, achievementId);
        } else {
            count = accountsAchievementDao.setAdorn(0, accountUuid, achievementId, level);
        }
        if(count == 0) {
            return;
        }
        this.refreshCache(accountUuid);
    }

    /**
     * 获取用户已获取的成就勋章数组(只返回最高级)，数组元素格式为"成就id_成就勋章等级"
     * @param accountUuid
     * @param version 0:不返回经验成就勋章; 否则返回经验成就勋章
     * @return
     */
    public List<String> getObtainHighAchievementList(String accountUuid, int version) {
        List<String> result = new ArrayList<>();
        TreeMap<String, TreeSet<String>> obtainMap = (TreeMap<String, TreeSet<String>>)this.getAllAchievement(accountUuid).get("obtain");
        obtainMap.forEach((achievementId, levelSet) -> {
            if(levelSet!=null && !levelSet.isEmpty()) {
                if(version == 0 && Objects.equals(achievementId, String.valueOf(AccountsAchievement.EXPER_ACHIEVEMENT_ID))) {
                    return;
                }
                result.add(achievementId + "_" + levelSet.last());
            }
        });
        return result;
    }

    /**
     * 获取指定成就的已获取的最大等级，如果没有获得过相应的勋章，返回null
     * @param achievementId 成就id
     * @param obtainMap 已获取的成就勋章数据，格式必须为 key:成就id -> value:成就对应的等级集合
     * @return
     */
    private Integer getMaxLevelFromObtain(Long achievementId, Map<String, TreeSet<String>> obtainMap) {
        TreeSet<String> achievementLevelSet = obtainMap.get(String.valueOf(achievementId));
        if(achievementLevelSet == null) {
            return null;
        }


        Integer maxLevel = null;
        for(String achievementLevel : achievementLevelSet) {
            if(StringUtils.isNotBlank(achievementLevel)) {
                Integer level = Integer.valueOf(achievementLevel);
                maxLevel = maxLevel == null || maxLevel < level ? level : maxLevel;
            }
        }
        return maxLevel;
    }

    /**
     * 获取用户得到的全部成就勋章，返回值:
     * <code>
     *     {
     *          "obtain": {
     *              "1": [1, 2, 3],
     *              "2": [1, 2],
     *              "3": [1],
     *              "4": [1, 2]
     *          },
     *          "adorn": {
     *              "1": 3,
     *              "2": 3,
     *              "3": 1
     *          }
     *     }
     * </code>
     * obtain为已获取的成就勋章, 成就id -> 成就勋章等级数组
     * adorn为佩戴的成就勋章, 成就id -> 成就勋章等级
     * 返回的数据，已经是按成就id，成就勋章等级排好顺序的
     * @param accountUuid
     * @return
     */
    public Map<String, Object> getAllAchievement(String accountUuid) {
        Map<String, Object> result = new HashMap<>();
        //已获取的成就勋章
        TreeMap<String, TreeSet<String>> obtainMap = new TreeMap<>(achievementComparator);
        result.put("obtain", obtainMap);

        String redisKey = RedisKeyConstant.ACCOUNT_ACHIEVEMENT.setArg(accountUuid);
        Map<Object, Object> hashValues = this.accountStringRedisTemplate.opsForHash().entries(redisKey);
        if(hashValues == null) {
            return result;
        }

        for(Map.Entry<Object, Object> entry : hashValues.entrySet()) {
            String key = entry.getKey().toString();
            if("adorn".equals(key)) {
                continue;
            }
            String[] achievement = key.split("_");
            String achievementId = achievement[0];
            String level = achievement[1];
            TreeSet<String> obtainItemSet = obtainMap.get(achievementId);
            if(obtainItemSet == null) {
                obtainItemSet = new TreeSet<>(achievementComparator);
                obtainMap.put(achievementId, obtainItemSet);
            }
            obtainItemSet.add(level);
        }

        //已佩戴的成就勋章
        TreeMap<String, String> adornMap = new TreeMap<>(achievementComparator);
        result.put("adorn", adornMap);
        Object achievement = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "achievement");
        if(achievement != null && !achievement.toString().trim().isEmpty()) {
            Map<String, String> _adornMap = JsonUtils.stringToObject(achievement.toString().trim(), new TypeReference<Map<String, String>>() {});
            adornMap.putAll(_adornMap);
        }

        return result;
    }

    public List<String> formatAdorn(Map<String, String> adornMap) {
        TreeMap<String, String> treeMap = new TreeMap<>(achievementComparator);
        treeMap.putAll(adornMap);

        List<String> result = new ArrayList<>(treeMap.size());
        treeMap.forEach((k, v) -> result.add(k + "_" + v));
        return result;
    }

    /**
     * 保存用户成就勋章到缓存
     * @param accountsAchievement
     */
    private void saveToCache(AccountsAchievement accountsAchievement) {
        String redisKey = RedisKeyConstant.ACCOUNT_ACHIEVEMENT.setArg(accountsAchievement.getAccount_uuid());
        accountStringRedisTemplate.opsForHash().putAll(redisKey, this.getHashValues(Arrays.asList(accountsAchievement)));
        accountStringRedisTemplate.expire(redisKey, 7, TimeUnit.DAYS);
    }

    /**
     * 刷新成就勋章缓存(hash结构)
     * hashKey: "成就id_成就勋章"; hashValue: {"is_adorn":"0:未佩戴;1:已佩戴", "id":"数据库表id"}
     * @param accountUuid
     */
    private void refreshCache(String accountUuid) {
        String redisKey = RedisKeyConstant.ACCOUNT_ACHIEVEMENT.setArg(accountUuid);
        List<AccountsAchievement> accountsAchievementList = accountsAchievementDao.queryByProp("account_uuid", accountUuid, Arrays.asList("id", "achievement_id", "achievement_level", "is_adorn"));
        Map<String, String> hashValues = this.getHashValues(accountsAchievementList);

        //已佩戴的成就勋章
        TreeMap<String, String> adornMap = new TreeMap<>(achievementComparator);
        for (AccountsAchievement accountsAchievement : accountsAchievementList) {
            if (accountsAchievement.getIs_adorn() == 1) {
                adornMap.put(accountsAchievement.getAchievement_id().toString(), accountsAchievement.getAchievement_level().toString());
            }
        }
        accountStringRedisTemplate.delete(redisKey);
        accountStringRedisTemplate.opsForHash().putAll(redisKey, hashValues);
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "achievement", JsonUtils.objectToString(adornMap));
        accountStringRedisTemplate.expire(redisKey, 7, TimeUnit.DAYS);
    }

    /**
     * 将用户成就勋章列表转换成为redis hash值
     * @param achievementList
     * @return
     */
    private Map<String, String> getHashValues(Collection<AccountsAchievement> achievementList) {
        Map<String, String> hashValues = new HashMap<>();
        for(AccountsAchievement accountsAchievement : achievementList) {
            String hashKey = accountsAchievement.getAchievement_id() + "_" + accountsAchievement.getAchievement_level();
            Map<String, String> hashValueMap = new HashMap<>();
            hashValueMap.put("is_adorn", accountsAchievement.getIs_adorn().toString());
            hashValueMap.put("id", accountsAchievement.getId().toString());
            String hashValue = JsonUtils.objectToString(hashValueMap);
            hashValues.put(hashKey, hashValue);
        }
        return hashValues;
    }

    private Pattern achievementPattern = Pattern.compile("^\\d+_\\d+$");
    private boolean isAchievement(String achievement) {
        return achievementPattern.matcher(achievement).matches();
    }
}
