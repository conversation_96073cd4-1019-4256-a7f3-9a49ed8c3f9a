package cn.taqu.account.service;

import cn.taqu.account.common.PlatformEnum;
import cn.taqu.account.dao.RegisterActionInfoDao;
import cn.taqu.account.model.RegisterActionInfo;
import cn.taqu.account.utils.LogUtil;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 账户登录版本限制服务
 *
 * <AUTHOR>
 * @date 2021/06/25
 */
@Slf4j
@Service
public class AccountLoginVersionLimitService {

    /**
     * 是否启用登录时的版本检测
     */
    private static Boolean on = true;
    /**
     * 安卓限制登录的版本
     */
    private static Integer androidLimitVersion = 7133;
    /**
     * ios限制登录或注册的版本
     */
    private static Long iosLimitVersion = 10112L;
    private static Integer limitAppcode;
    private static Integer limitCloned;

    /**
     * 安卓限制的注册登录范围
     */
    private static Integer androidLimitRegLoginRange;

    /**
     * ios限制的注册登录范围
     */
    private static Integer iosLimitRegLoginRange;


    /**
     * 开启登录版本限制
     */
    private static final  String ON = "on";
    /**
     * 安卓限制版本
     */
    private static final  String ANDROIDLIMITVERSION = "androidLimitVersion";
    private static final  String IOSLIMITVERSION = "iosLimitVersion";


    private static Logger LOGGER = LoggerFactory.getLogger(AccountLoginVersionLimitService.class);

    @Autowired
    private RegisterActionInfoDao registerActionInfoDao;

    public static void setOn(String limitConf){
        Map<String, Object> confMap = JsonUtils.stringToObject2(limitConf, new TypeReference<Map<String, Object>>() {});
        try {
            if(null != confMap.get(ON)){
                on = Boolean.parseBoolean(String.valueOf(confMap.get("on")));
            }
            if(null != confMap.get(ANDROIDLIMITVERSION)){
                androidLimitVersion = Integer.parseInt(String.valueOf(confMap.get("androidLimitVersion")));
            }
            iosLimitVersion = MapUtils.getLong(confMap,IOSLIMITVERSION,10112L);
            limitAppcode = MapUtils.getInteger(confMap,"limitAppcode");
            limitCloned = MapUtils.getInteger(confMap,"limitCloned");
            androidLimitRegLoginRange = MapUtils.getInteger(confMap,"androidLimitRegLoginRange", 0);
            iosLimitRegLoginRange = MapUtils.getInteger(confMap,"iosLimitRegLoginRange", 0);
        }catch (Exception e){
            LOGGER.warn("版本登录限制设置失败：{}，错误：{}",limitConf,e);
        }

    }


    /**
     * 同一个平台的情况下，登录的版本低于注册的版本时，返回true，高于或者等于则返回false
     *
     * @param accountUuid
     * @param platformId
     * @param appVersion
     * @return
     */
    public Boolean loginVersionLimit(String accountUuid, Integer platformId, Long appVersion, Integer appCode, Integer cloned, String channel) {
        //accountUuid为空，表示注册行为。非空表示登录行为
        if (on && StringUtils.isEmpty(accountUuid) && null != platformId && null != appVersion) {
            //只对主版本进行限制
            if (null != limitAppcode && limitAppcode.equals(appCode) && null != limitCloned && limitCloned.equals(cloned)) {
                if (PlatformEnum.ANDROID.getValue().equals(platformId) && appVersion < androidLimitVersion) {
                    return true;
                }
                return (PlatformEnum.IPHONE.getValue().equals(platformId) || PlatformEnum.IPAD.getValue().equals(platformId)) && appVersion < iosLimitVersion;
            }

        }else if(on && StringUtils.isNotEmpty(accountUuid) && null != platformId && null != appVersion){
            if(null != limitAppcode && limitAppcode.equals(appCode) && null != limitCloned && limitCloned.equals(cloned)){
                //安卓版本低于7133版本的限制登录
                if (PlatformEnum.ANDROID.getValue().equals(platformId) && appVersion < androidLimitVersion) {
                    log.info("android版本太限制登录:{}", accountUuid);
                    return true;
                }

                // 用户限制，如果注册在更高版本的app，就不允许登录低版本app
                RegisterActionInfo registerActionInfo = registerActionInfoDao.getRegisterPidVersionByUuid(accountUuid);
                if (null == registerActionInfo) {
                    return false;
                }

                Integer regLoginRange = 0;
                if (PlatformEnum.ANDROID.getValue().equals(platformId)) {
                    if (androidLimitRegLoginRange > 0) {
                        regLoginRange = androidLimitRegLoginRange;
                    }
                } else {
                    if (iosLimitRegLoginRange > 0) {
                        regLoginRange = iosLimitRegLoginRange;
                    }
                }
                // 鸿蒙不需要这些限制高版本登录低版本
                if (channel != null) {
                    LogUtil.info4Gray("登录日志渠道跟踪:{},{}", accountUuid, channel);
                    if (Objects.equals(channel, "harmony")) {
                        return false;
                    }
                }

                boolean limit = platformId.equals(registerActionInfo.getPlatformId()) && (appVersion + regLoginRange < registerActionInfo.getAppVersion());
                if (limit) {
                    LOGGER.info("login version limit - platformId:{}, accountUuid:{}, loginAppVersion:{}, regAppVersion:{}", platformId, accountUuid, appVersion, registerActionInfo.getAppVersion());
                }
                return limit;
            }
        }
        return false;
    }

}
