package cn.taqu.account.service;

import cn.taqu.account.dao.AccountsCertLogDao;
import cn.taqu.account.model.AccountsCertLog;
import cn.taqu.core.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 认证记录
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-29 15:50
 */
@Service
@Transactional
public class AccountsCertLogService {

    @Autowired
    private AccountsCertLogDao accountsCertLogDao;

    public void save(AccountsCertLog accountsCertLog){
        Long now = DateUtil.currentTimeSeconds();
        accountsCertLog.setCreateTime(now).setUpdateTime(now);
        accountsCertLogDao.merge(accountsCertLog);
    }
}
