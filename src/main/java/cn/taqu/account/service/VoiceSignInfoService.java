package cn.taqu.account.service;


import cn.taqu.account.common.*;
import cn.taqu.account.constant.ActionEventEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.VoiceSignInfoDao;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.dto.ShumeiContentCheckDTO;
import cn.taqu.account.event.ActionNoticeReport;
import cn.taqu.account.model.VoiceSignInfo;
import cn.taqu.account.utils.TimeFormatUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 语音签名服务类
 */
@Slf4j
@Service
@Transactional
public class VoiceSignInfoService {

    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private VoiceSignInfoDao voiceSignInfoDao;
    //    @Autowired
//    private MessageService messageService;
    @Autowired
    private BuryService buryService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;
    @Autowired
    private ProfileOptimizationUiService profileOptimizationUiService;
    @Resource
    private ActionNoticeReport actionNoticeReport;

    private static final String AUDITING_REASON = ""; //语音签名审核中的原因
    public static Integer VOICE_SIGN_LIMIT_TIME = 15; //语音签名录制限制时长
    public static String VOICE_SIGN_PRE_DOMAIN;  //语音签名url访问前缀
    private static String VOICE_SIGN_AUDIT_PASS_MSG;
    //    private static String VOICE_SIGN_AUDIT_REJECT_MSG;
//    private static String VOICE_SIGN_AUDIT_REJECT_SHOW_MSG;
    private static Integer VOICE_SIGN_PASS_LIMIT_TIME = 1;
    private static Integer VOICE_SIGN_REJECT_LIMIT_TIME = 2;
    public static String NON_REC_RECORD_REASON;
    private static List<String> copywritingList;
    private static Map<Integer, List<String>> copywritingMap;
    private static final String DEFAULT_COPYWRITING = "别人都能和声音好听的小哥哥/小姐姐交朋友，我也想";


    public static void initConf(String conf){
        Map<String,Object> map = JsonUtils.stringToObject2(conf, new TypeReference<Map<String, Object>>() {});
        VOICE_SIGN_LIMIT_TIME = MapUtils.getInteger(map,"voiceSignLimitTime",15);
        VOICE_SIGN_PASS_LIMIT_TIME = MapUtils.getInteger(map,"voiceSignPassLimitTime",1);
        VOICE_SIGN_REJECT_LIMIT_TIME = MapUtils.getInteger(map,"voiceSignRejectLimitTime",2);
        VOICE_SIGN_PRE_DOMAIN = MapUtils.getString(map,"voiceSignPreDomain","https://vox01.jiaoliuqu.com/");
        VOICE_SIGN_AUDIT_PASS_MSG = MapUtils.getString(map,"voiceSignAuditPassMsg","恭喜你，你的语音签名审核通过了，快去看看～");
//        VOICE_SIGN_AUDIT_REJECT_MSG = MapUtils.getString(map,"voiceSignAuditRejectMsg","你的语音签名由于“${reason}”审核不通过，请重新录制，点击前往录制~");
//        VOICE_SIGN_AUDIT_REJECT_SHOW_MSG = MapUtils.getString(map,"voiceSignAuditRejectShowMsg","由于“${reason}”审核不通过，请重新录制");
        NON_REC_RECORD_REASON = MapUtils.getString(map,"nonRecRecordReason","每周只能修改1次语音签名，本周已修改过啦");
    }

    public static void initCopyWritingList(String copyWritingStr){
        copywritingList = JsonUtils.stringToObject2(copyWritingStr, new TypeReference<List<String>>() {});
    }

    /**
     * 根据性别区分文案
     * @param copyWritingStr
     */
    public static void initCopyWritingMap(String copyWritingStr){
        copywritingMap = JsonUtils.stringToObject2(copyWritingStr, new TypeReference<Map<Integer,List<String>>>() {});
    }

    /**
     * 个人签名语音录制接口
     * @param accountUuid
     * @param voiceSignStr
     * @return
     */
    @Transactional
    public Boolean recordVoiceSign(String accountUuid,String voiceSignStr, String source,String smid){
        boolean result = true;
        Map<String , Object> map = JsonUtils.stringToObject2(voiceSignStr, new TypeReference<Map<String, Object>>() {});
        String voiceSignUrl = MapUtils.getString(map,"voice_sign_url");
        Integer voiceSignDuration = MapUtils.getInteger(map,"voice_sign_duration");
        String bucket = MapUtils.getString(map,"bucket");
        if(StringUtils.isEmpty(voiceSignUrl) || null == voiceSignDuration){
            throw new ServiceException("语音录制失败，请重新录制");
        }
        VoiceSignInfo voiceSignInfo = new VoiceSignInfo();
        voiceSignInfo.setVoice_sign_url(voiceSignUrl);
        voiceSignInfo.setVoice_sign_duration(voiceSignDuration);
        voiceSignInfo.setStatus(CommonAuditStatus.AUDITING.getStatus());
        voiceSignInfo.setAccount_uuid(accountUuid);
        voiceSignInfo.setBucket(bucket);
        voiceSignInfo.setCreate_time(DateUtil.currentTimeSeconds());
        voiceSignInfo.setAudit_reason(AUDITING_REASON);
        voiceSignInfo.setSource(source);
        voiceSignInfo = voiceSignInfoDao.merge(voiceSignInfo);

        // 2024.06.07 文件使用，通知图片系统
        BuryService.pushToPicSysUsedFileBiz(CommConst.APPCODE_TAQU, voiceSignUrl, CommConst.AUDIO_BUCKET, DateUtil.currentTimeSeconds());

        // 推送语音签名到审核平台
        Integer appcode = SoaBaseParams.fromThread().getAppcode() == null ? 1 : SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned() == null ? 1 : SoaBaseParams.fromThread().getCloned();
        ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.audioReviewFromRiskDetect(accountUuid, voiceSignInfo.getId(), VOICE_SIGN_PRE_DOMAIN + voiceSignUrl, voiceSignDuration, RiskDetectEnum.AUDIO_SIGN.name(), smid, appcode, cloned);
        if (contentCheckDTO == null ||
                StringUtils.isBlank(contentCheckDTO.getSuggestion()) ||
                !(contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.PASS.name()) || contentCheckDTO.getSuggestion().equals(RiskDetectRiskLevelEnum.WAIT.name()))) {
            throw new ServiceException(CodeStatus.VOICE_SIGN_ILLEGAL);
        }
        // 语音信息设置到缓存
        setVoiceinfoTotCache(voiceSignInfo);
        return result;
    }

    /**
     * 个人签名语音录制接口（不审核）
     * @param accountUuid
     * @param voiceSignStr
     * @return
     */
    public void setVoiceSignInfoNoAudit(String accountUuid,String voiceSignStr){
    	// 直接数据库添加数据
    	Map<String , Object> map = JsonUtils.stringToObject2(voiceSignStr, new TypeReference<Map<String, Object>>() {});
    	String voiceSignUrl = MapUtils.getString(map,"voice_sign_url");
    	Integer voiceSignDuration = MapUtils.getInteger(map,"voice_sign_duration");
    	String bucket = MapUtils.getString(map,"bucket");
    	if(StringUtils.isEmpty(voiceSignUrl) || null == voiceSignDuration){
    		//文案1
    		throw new ServiceException("语音录制失败，请重新录制");
    	}
    	Long currentTimeSeconds = DateUtil.currentTimeSeconds();
    	VoiceSignInfo voiceSignInfo = new VoiceSignInfo();
    	voiceSignInfo.setVoice_sign_url(voiceSignUrl);
    	voiceSignInfo.setVoice_sign_duration(voiceSignDuration);
    	voiceSignInfo.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
    	voiceSignInfo.setAccount_uuid(accountUuid);
    	voiceSignInfo.setBucket(bucket);
    	voiceSignInfo.setCreate_time(currentTimeSeconds);
    	voiceSignInfo.setAudit_reason(AUDITING_REASON);
    	voiceSignInfo.setOperator("系统设置");
    	voiceSignInfo.setAudit_time(currentTimeSeconds);
    	voiceSignInfoDao.merge(voiceSignInfo);

    	//语音信息设置到缓存
    	String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();
		hashValue.put("voice_sign", String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));// 语音签名状态，0-审核中，1-审核通过，2-审核拒绝
		hashValue.put("voice_sign_url", voiceSignUrl);// 语音签名url
		hashValue.put("voice_sign_duration", String.valueOf(voiceSignDuration));//语音签名时长
		hashValue.put("voice_sign_audit_time", String.valueOf(currentTimeSeconds));//语音签名审核时间
		hashValue.put("voice_sign_audit_reason", "");//语音签名审核原因

		hashValue.put("pass_voice_sign", String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()));// 语音签名状态，0-审核中，1-审核通过，2-审核拒绝
		hashValue.put("pass_voice_sign_url", voiceSignUrl);// 审核通过的语音签名url
		hashValue.put("pass_voice_sign_duration", String.valueOf(voiceSignDuration));//审核通过的语音签名时长
		hashValue.put("pass_voice_sign_audit_time", String.valueOf(currentTimeSeconds));//审核通过的语音签名审核时间
		hashValue.put("pass_voice_sign_audit_reason", "");//审核通过的语音签名审核原因
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

        // 通知社区完成任务
    	try {
        	buryService.toBbsFinishIncentiveTask(voiceSignInfo.getAccount_uuid(), FinishIncentiveTaskEnum.VOICE_SIGN.getType());
        }catch (Exception e) {
        	log.error("语音签名审核通过，通知社区任务完成处理失败。", e);
		}

    }

    private void setVoiceinfoTotCache(VoiceSignInfo voiceSign) {
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(voiceSign.getAccount_uuid());
        Map<String, String> hashValue = new HashMap<>();
        hashValue.put("voice_sign",String.valueOf(voiceSign.getStatus()));
        hashValue.put("voice_sign_url",voiceSign.getVoice_sign_url());
        hashValue.put("voice_sign_duration",String.valueOf(voiceSign.getVoice_sign_duration()));
        hashValue.put("voice_sign_audit_reason",voiceSign.getAudit_reason());
        hashValue.put("voice_sign_audit_time","");
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);
    }


    public void auditPass(Long id,String operator){
        VoiceSignInfo voiceSignInfo = voiceSignInfoDao.get(id);
        if(null == voiceSignInfo){
            throw new ServiceException(CodeStatus.VOICE_SIGN_INFO_NOT_EXIST);
        }
        voiceSignInfo.setAudit_time(DateUtil.currentTimeSeconds());
        voiceSignInfo.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        voiceSignInfo.setOperator(operator);
        voiceSignInfoDao.merge(voiceSignInfo);

        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(voiceSignInfo.getAccount_uuid());
        Map<String, String> hashValue = new HashMap<>();
        hashValue.put("voice_sign",String.valueOf(voiceSignInfo.getStatus()));
        hashValue.put("voice_sign_audit_reason","每周只能修改一次语音签名");
        hashValue.put("voice_sign_audit_time",String.valueOf(voiceSignInfo.getAudit_time()));

        //审核通过后voice_sign里的数据和pass_voice_sign同步，审核中时不同步
        hashValue.put("pass_voice_sign",String.valueOf(voiceSignInfo.getStatus()));
        hashValue.put("pass_voice_sign_url",voiceSignInfo.getVoice_sign_url());
        hashValue.put("pass_voice_sign_duration",String.valueOf(voiceSignInfo.getVoice_sign_duration()));
        hashValue.put("pass_voice_sign_audit_reason","每周只能修改一次语音签名");
        hashValue.put("pass_voice_sign_audit_time",String.valueOf(voiceSignInfo.getAudit_time()));
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

        //审核通过后每周限制录制一次
        String auditKey = RedisKeyConstant.ACCOUNT_AUDIT_INFO.setArg(voiceSignInfo.getAccount_uuid());
        Object voiceSignAuditPassTimesStr = accountStringRedisTemplate.opsForHash().get(auditKey, "voiceSignAuditPassTimes");
        Integer voiceSignAuditPassTimes = null == voiceSignAuditPassTimesStr ? 0 : Integer.parseInt(voiceSignAuditPassTimesStr.toString());
        accountStringRedisTemplate.opsForHash().put(auditKey, "voiceSignAuditPassTimes", String.valueOf(voiceSignAuditPassTimes + 1));
        accountStringRedisTemplate.expire(auditKey, TimeFormatUtil.getRemainSecThisWeek(), TimeUnit.SECONDS);
        Map<String, Object> setMap = Maps.newHashMap();
        setMap.put("voice_sign", 1);
        accountsPersonalInfoService.setAccountInfoByUuid(voiceSignInfo.getAccount_uuid(), setMap, null, CommConst.OLD_CLIENT_PACKAGE);

        buryService.toBbsFinishIncentiveTask(voiceSignInfo.getAccount_uuid(), FinishIncentiveTaskEnum.VOICE_SIGN.getType());
        pushSystemMsg(voiceSignInfo.getAccount_uuid(), VOICE_SIGN_AUDIT_PASS_MSG, "", "");

        actionNoticeReport.report(voiceSignInfo.getAccount_uuid(), ActionEventEnum.VOICE_PASS);
    }

    /**
     * 判断是否有旧数据，有旧数据使用旧的。版本控制，ab实验
     *
     * @param id
     * @param operator
     * @param reason
     * @param isRiskSafe
     */
    public void auditReject(Long id,String operator,String reason,boolean isRiskSafe) {
        VoiceSignInfo voiceSignInfo = voiceSignInfoDao.get(id);
        if(null == voiceSignInfo){
            throw new ServiceException("check_voice_sign_info_not_exist", "拒绝失败，该记录不存在");
        }
        //审核通过后再拒绝的操作
        if(CommonAuditStatus.AUDIT_SUCCESS.getStatus().equals(voiceSignInfo.getStatus())){
            voiceSignInfo.setOperator(operator);
            voiceSignInfo.setAudit_reason(reason);
            rejectPassVoiceSign(voiceSignInfo);
            return;
        }
        String accountUuid = voiceSignInfo.getAccount_uuid();

        // 先拒绝当前数据，再查看是否插入新通过的数据
        voiceSignInfo.setAudit_time(DateUtil.currentTimeSeconds());
        voiceSignInfo.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
        voiceSignInfo.setOperator(operator);
        voiceSignInfo.setAudit_reason(reason);
        voiceSignInfoDao.merge(voiceSignInfo);

        // 查询是否有旧数据
        Map<String, Object> passVoiceSignInfo = this.getPassVoiceSign(accountUuid);
        String passVoiceSignUrl = MapUtils.getString(passVoiceSignInfo, "pass_voice_sign_url", "");
        Integer passVoiceSignDuration = MapUtils.getInteger(passVoiceSignInfo, "pass_voice_sign_duration", null);
        // 实验组 判断用户是否有历史，有旧数据，就使用旧数据
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();

        if(StringUtils.isNotBlank(passVoiceSignUrl) && profileOptimizationUiService.isProfileOptimizationUiTargetAccountByServer(accountUuid)) {
            //新增一个审核通过的数据
            VoiceSignInfo voiceSignInfoNew = new VoiceSignInfo();
            voiceSignInfoNew.setVoice_sign_url(passVoiceSignUrl);
            voiceSignInfoNew.setVoice_sign_duration(passVoiceSignDuration);
            voiceSignInfoNew.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
            voiceSignInfoNew.setAccount_uuid(accountUuid);
            voiceSignInfoNew.setBucket(CommConst.AUDIO_BUCKET);
            voiceSignInfoNew.setCreate_time(DateUtil.currentTimeSeconds());
            voiceSignInfoNew.setAudit_reason(AUDITING_REASON);
            voiceSignInfoNew.setSource("");
            voiceSignInfoNew = voiceSignInfoDao.merge(voiceSignInfo);
            log.info("语音签名回滚至上一条通过数据，accountUuid={}, voiceSignUrl={}", accountUuid, passVoiceSignUrl);

            hashValue.put("voice_sign", MapUtils.getString(passVoiceSignInfo, "pass_voice_sign", ""));
            hashValue.put("voice_sign_url", MapUtils.getString(passVoiceSignInfo, "pass_voice_sign_url", ""));
            hashValue.put("voice_sign_duration", MapUtils.getString(passVoiceSignInfo, "pass_voice_sign_duration", ""));
            hashValue.put("voice_sign_audit_reason", MapUtils.getString(passVoiceSignInfo, "pass_voice_sign_audit_reason", ""));
            hashValue.put("voice_sign_audit_time", MapUtils.getString(passVoiceSignInfo, "pass_voice_sign_audit_time", ""));
        }else {
            // 清空数据
            hashValue.put("voice_sign","");
            hashValue.put("voice_sign_url","");
            hashValue.put("voice_sign_duration","");
            hashValue.put("voice_sign_audit_reason","");
            hashValue.put("voice_sign_audit_time","");

            //审核通过后voice_sign里的数据和pass_voice_sign同步，审核中时不同步
            hashValue.put("pass_voice_sign","");
            hashValue.put("pass_voice_sign_url","");
            hashValue.put("pass_voice_sign_duration","");
            hashValue.put("pass_voice_sign_audit_reason","");
            hashValue.put("pass_voice_sign_audit_time","");
        }

        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

        //审核不通过后每周限制录制两次
        String auditKey = RedisKeyConstant.ACCOUNT_AUDIT_INFO.setArg(accountUuid);
        Object  voiceSignAuditPassTimesStr = accountStringRedisTemplate.opsForHash().get(auditKey, "voiceSignAuditFailTimes");
        Integer voiceSignAuditFailTimes = null == voiceSignAuditPassTimesStr ? 0 : Integer.parseInt(voiceSignAuditPassTimesStr.toString());
        accountStringRedisTemplate.opsForHash().put(auditKey,"voiceSignAuditFailTimes",String.valueOf(voiceSignAuditFailTimes + 1));
        //本周日晚上12点后可重新录制
        accountStringRedisTemplate.expire(auditKey, TimeFormatUtil.getRemainSecThisWeek(), TimeUnit.SECONDS);

        // 2024.06.07 审核拒绝，通知图片系统禁用
        BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, voiceSignInfo.getVoice_sign_url(), CommConst.AUDIO_BUCKET, 1, DateUtil.currentTimeSeconds());
    }

    /**
     * 直接清空数据
     *
     * @param voiceSignInfo
     */
    private void rejectPassVoiceSign(VoiceSignInfo voiceSignInfo){
        VoiceSignInfo info = voiceSignInfoDao.findLastestOneByUuidAndStatus(voiceSignInfo.getAccount_uuid(),CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        if(!voiceSignInfo.getId().equals(info.getId())){
            throw new ServiceException("拒绝失败，拒绝通过后的语音签名时需保证该语音签名是最新的一条~");
        }

        voiceSignInfo.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
        voiceSignInfo.setAudit_time(DateUtil.currentTimeSeconds());
        voiceSignInfoDao.merge(voiceSignInfo);

        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(voiceSignInfo.getAccount_uuid());
        Map<String, String> hashValue = new HashMap<>();

        String[] fields = new String[] {"voice_sign"};
        Map<String, Map<String, Object>> map = accountsInfoService.getInfoByUuid(new String[] {voiceSignInfo.getAccount_uuid()}, fields, "1", true, false);
        Map<String, Object> userMap = map.get(voiceSignInfo.getAccount_uuid());
        Integer voiceSignStatus = MapUtils.getInteger(userMap,"voice_sign");

        if(CommonAuditStatus.AUDIT_SUCCESS.getStatus().equals(voiceSignStatus)){
            hashValue.put("voice_sign","");
            hashValue.put("voice_sign_url","");
            hashValue.put("voice_sign_duration","");
            hashValue.put("voice_sign_audit_reason","");
            hashValue.put("voice_sign_audit_time","");
        }

        //审核通过后voice_sign里的数据和pass_voice_sign同步，审核中时不同步
        hashValue.put("pass_voice_sign","");
        hashValue.put("pass_voice_sign_url","");
        hashValue.put("pass_voice_sign_duration","");
        hashValue.put("pass_voice_sign_audit_reason","");
        hashValue.put("pass_voice_sign_audit_time","");
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

        // 2024.06.07 审核拒绝，通知图片系统禁用
        BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, voiceSignInfo.getVoice_sign_url(), CommConst.AUDIO_BUCKET, 1, DateUtil.currentTimeSeconds());
    }

    /**
     * 本周是否还可以进行语音签名,true-还可以录音，false-不能录音
     * @param accountUuid
     * @return
     */
    public Boolean isPermit(String accountUuid){
        if(StringUtils.isEmpty(accountUuid)){
            return false;
        }
        Boolean result = true;
        String auditKey = RedisKeyConstant.ACCOUNT_AUDIT_INFO.setArg(accountUuid);
        List<Object>   list  = accountStringRedisTemplate.opsForHash().multiGet(auditKey, Lists.newArrayList("voiceSignAuditPassTimes","voiceSignAuditFailTimes"));
        Integer voiceSignAuditPassTimes = null == list.get(0) ? 0 : Integer.parseInt(list.get(0).toString());
        Integer voiceSignAuditFailTimes = null == list.get(1) ? 0 : Integer.parseInt(list.get(1).toString());
        if(voiceSignAuditPassTimes >= VOICE_SIGN_PASS_LIMIT_TIME || voiceSignAuditFailTimes >= VOICE_SIGN_REJECT_LIMIT_TIME){
            result = false;
        }
        return result;

    }

    private void pushSystemMsg(String accountUuid,String msg,String reason,String relaction){
        String tips = msg;
        if(StringUtils.isNotBlank(reason)){
            tips = msg.replace("${reason}",reason);
        }
        Map<String, Object> content = Maps.newHashMap();
        content.put("content", tips);
        content.put("relaction", relaction);
        content.put("is_local_push", "1");
        MessageService.systemNotice(accountUuid, content, "", "text", 1);
    }

    /**
     * 语音拒绝推送
     * @param accountUuid
     */
//    private void pushFailVoiceAuditSystemMsg(String accountUuid){
//        JSONArray contentJa = new JSONArray();
//        JSONObject contentMap = new JSONObject();
//        String info="您的语音签名不通过，原因可能有：" +
//                "\n" +
//                "1.不是本人的声音； " +
//                "\n" +
//                "2.声音模糊无法听清；" +
//                "\n" +
//                "3.说话过于简单；" +
//                "\n" +
//                "建议录制：自己唱的一首歌/一段内心独白/交友宣言。" +
//                "\n" +
//                "\n" +
//                "%s";
//        JSONArray contentReplaces = new JSONArray();
//        JSONObject contentReplace = new JSONObject();
//        contentReplace.put("w", "重新录制");
//        contentReplace.put("r", "m=mine&a=infoVoice");
//        contentReplace.put("c", "#0000FF");
//        contentReplaces.add(contentReplace);
//
//        JSONObject contentLevel2 = new JSONObject();
//        contentLevel2.put("content_replace", contentReplaces);
//        contentLevel2.put("content", info);
//        contentLevel2.put("describe", "您的语音签名不通过");
//
//        contentMap.put("content", contentLevel2);
//        contentMap.put("relaction", "");
//        contentMap.put("is_local_push", "1");
//
//        contentJa.add(contentMap);
//
//        messageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
//    }

    public String getCopywriting(){
        String copywriting = DEFAULT_COPYWRITING;
        try {
            Random random = new Random();
            int n = random.nextInt(copywritingList.size());
            copywriting = copywritingList.get(n);
        }catch (Exception e){
            log.warn("语音签名随机文案获取失败", e);
        }

        return copywriting;
    }

    /**
     * 根据用户下发文案（主要性别）
     * @param accountUuid
     * @return
     */
    public String getCopywriting(String accountUuid){
        // uuid为空，走不区分性别文案
        if(StringUtils.isBlank(accountUuid)) {
            log.warn("获取语音签名文案，accountUuid为空");
            return getCopywriting();
        }
        Integer sexType = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);
        if (sexType == null || copywritingMap.get(sexType) == null) {
            log.warn("获取语音签名文案，性别异常，accountUuid={}", accountUuid);
            return getCopywriting();
        }

        String copywriting = DEFAULT_COPYWRITING;
        try {
            Random random = new Random();
            List<String> list = copywritingMap.get(sexType);
            int n = random.nextInt(list.size());
            copywriting = list.get(n);
        }catch (Exception e){
            log.warn("语音签名随机文案获取失败", e);
        }

        return copywriting;
    }

	public Map<String,Object> getVoiceSignInfoByAccountUuid(String accountUuid) {
		//1. 用户基本信息
		Map<String, Map<String, Object>> usersResultMap = accountsInfoService.getInfoByUuid(new String[] {accountUuid}, new String[]{
				"voice_sign","voice_sign_audit_time","voice_sign_url","voice_sign_duration","voice_sign_audit_reason"
				,"pass_voice_sign","pass_voice_sign_audit_time","pass_voice_sign_url","pass_voice_sign_duration","pass_voice_sign_audit_reason",
				"pass_personal_profile","pass_personal_profile_status","sex_type"
		}, "1", true,true);
		Map<String,Object> voiceSignMap = accountsInfoService.getVoiceSignInfo(usersResultMap.get(accountUuid), accountUuid, accountUuid);
		return voiceSignMap;
	}

    public Map<String,Object> getPassVoiceSign(String accountUuid) {
	    Map<String, Object> usersResultMap = accountsInfoService.getInfoByUuid(new String[] {accountUuid}, new String[]{
	        "pass_voice_sign","pass_voice_sign_audit_time","pass_voice_sign_url","pass_voice_sign_duration","pass_voice_sign_audit_reason"
	    }, "1", true,true).get(accountUuid);
	    return usersResultMap;
	}

    /**
     * 查询未审核记录（超过1小时未审核）
     *
     * @param expireTime
     * @return
     */
    public List<VoiceSignInfo> queryNotAudit(Long expireTime, Long startTime, Integer status, Integer limit) {
        return voiceSignInfoDao.queryNotAudit(expireTime, startTime, status, limit);
    }

    /**
     * 风控安全审核回调
     *
     * @param dto
     */
    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]语音审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        if (StringUtils.isNotBlank(dto.getHitTypeCode())) {
            RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
            String operator = dto.getOperator();
            String blockReason = dto.getBlockReason();
            if (Objects.nonNull(riskSafeHitTypeEnum)) {
                switch (riskSafeHitTypeEnum) {
                    case PASS:
                        auditPass(Long.parseLong(dto.getBizId()), operator);
                        break;
                    case BLOCK:
                        auditReject(Long.parseLong(dto.getBizId()), operator, blockReason, true);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    public String getVoiceSignPreDomain() {
        return VOICE_SIGN_PRE_DOMAIN;
    }

    /**
     * @param accountUuid
     * @param auditReason
     * @param operator
     */
    public void resetVoiceSignInfo(String accountUuid, String operator, String auditReason) {
        VoiceSignInfo voiceSignInfo = voiceSignInfoDao.getLastOneByUuid(accountUuid);

        if(null == voiceSignInfo){
            throw new ServiceException("check_voice_sign_info_not_exist", "语音签名重置失败，用户无数据");
        }

        voiceSignInfo.setAudit_time(DateUtil.currentTimeSeconds());
        voiceSignInfo.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
        voiceSignInfo.setOperator(operator);
        voiceSignInfo.setAudit_reason(auditReason);
        voiceSignInfoDao.merge(voiceSignInfo);

        //重置后每周限制录制两次
        String auditKey = RedisKeyConstant.ACCOUNT_AUDIT_INFO.setArg(accountUuid);
        Object  voiceSignAuditPassTimesStr = accountStringRedisTemplate.opsForHash().get(auditKey, "voiceSignAuditFailTimes");
        Integer voiceSignAuditFailTimes = null == voiceSignAuditPassTimesStr ? 0 : Integer.parseInt(voiceSignAuditPassTimesStr.toString());
        accountStringRedisTemplate.opsForHash().put(auditKey,"voiceSignAuditFailTimes",String.valueOf(voiceSignAuditFailTimes + 1));
        //本周日晚上12点后可重新录制
        accountStringRedisTemplate.expire(auditKey, TimeFormatUtil.getRemainSecThisWeek(), TimeUnit.SECONDS);

        // 重置缓存数据
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();

        hashValue.put("voice_sign","");
        hashValue.put("voice_sign_url","");
        hashValue.put("voice_sign_duration","");
        hashValue.put("voice_sign_audit_reason","");
        hashValue.put("voice_sign_audit_time","");

        //审核通过后voice_sign里的数据和pass_voice_sign同步，审核中时不同步
        hashValue.put("pass_voice_sign","");
        hashValue.put("pass_voice_sign_url","");
        hashValue.put("pass_voice_sign_duration","");
        hashValue.put("pass_voice_sign_audit_reason","");
        hashValue.put("pass_voice_sign_audit_time","");
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);

        // 2024.06.07 审核拒绝，通知图片系统禁用
        BuryService.pushToPicSysUpdFileStatus(CommConst.APPCODE_TAQU, voiceSignInfo.getVoice_sign_url(), CommConst.AUDIO_BUCKET, 1, DateUtil.currentTimeSeconds());
    }
}
