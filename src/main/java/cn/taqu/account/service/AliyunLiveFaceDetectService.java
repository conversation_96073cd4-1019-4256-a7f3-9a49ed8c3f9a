package cn.taqu.account.service;

import cn.taqu.account.client.TengxunyunApiClient;
import cn.taqu.account.client.api.tengxunyun.TengxunyunApi;
import cn.taqu.account.client.api.tengxunyun.TengxunyunVoucher;
import cn.taqu.account.client.api.tengxunyun.TengxunyunWBappid;
import cn.taqu.account.client.api.tengxunyun.TxGetLiveResultResponse;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.*;
import cn.taqu.account.cron.AliyunFacePhotoStoreTask;
import cn.taqu.account.dao.AccountsChatCertificationDao;
import cn.taqu.account.dao.AliyunLiveFaceDetectDao;
import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.dto.CompareUserDTO;
import cn.taqu.account.dto.KafkaManufacturerCallDto;
import cn.taqu.account.dto.TencentImgQualityDTO;
import cn.taqu.account.event.*;
import cn.taqu.account.model.*;
import cn.taqu.account.mq.AccountManufacturerCallProducer;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.account.utils.ShowDialogUtil;
import cn.taqu.account.utils.TimeFormatUtil;
import cn.taqu.account.vo.PictureInfoVo;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.cloudauth.model.v20190307.DescribeVerifyResultRequest;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.ProtocolType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceRequest;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceResponse;
import com.tencentcloudapi.tiia.v20190529.TiiaClient;
import com.tencentcloudapi.tiia.v20190529.models.AssessQualityRequest;
import com.tencentcloudapi.tiia.v20190529.models.AssessQualityResponse;
import io.prometheus.client.Histogram;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 活体人脸采集
 */
@Service
@Transactional
public class AliyunLiveFaceDetectService {

    // secretid
    private static String TENGXUNYUN_SECRETID = "AKIDAD2cy5yc2DAsKWTAblfKkvfEYdT3OTIr";
    // secretkey
    private static String TENGXUNYUN_SECRETKEY = "MSIrBG4cuwkJdiZAWdV8WfYCbmiOp1yi";
    // endpoint
    private static String TENGXUNYUN_ENDPOINT = "iai.tencentcloudapi.com";
    // 腾讯云人脸比对校验值，大于50分表示比对成功，先写死不走etcd配置
    public static Float TENGXUNYUN_SIMILARITY_SCORE = 50F;
    // 腾讯云图片质量
    public static Integer IMG_QUALITY_TENCENT_CLARITY_SCORE = 50;

//    private static DefaultProfile profile;
    // 阿里云人脸比对校验值，大于95分表示比对成功
    public static Float ALIYUN_SIMILARITY_SCORE;
    public static String regionId;
//    private static String bizType;
//    public static String PHOTO_URL_PRE_DOMAIN = "https://${bucket}.jiaoliuqu.com/";   //转存到七牛云后生成的url链接前缀
//    public static String BUCKET = CommConst.AVATAR_BUCKET;
    public static String accessKeyId;
    public static String secret;
    // 阿里云client
//    public static IAcsClient client;
    // 腾讯云client
    public static IaiClient laiClient;
    public static TiiaClient tiiaclient;
    private static Integer FACE_DETECT_LIMIT_TIME;
    private static Integer FACE_DETECT_LIMIT_TIME_MOBILE = 3;
    private static Integer FACE_DETECT_LIMIT_TIME_RETRIEVE = 3;
    private static Integer FACE_DETECT_LIMIT_TIME_HIGH_RISK = 3;
    private static Integer FACE_DETECT_LIMIT_TIME_CASH = 3;
    private static Integer FACE_DETECT_LIMIT_TIME_WITHDRAW = 5;
//    //人脸模糊度，分值越大越模糊
//    private static Float FACE_BLUR;
//    //人脸校验分数，分数越高，质量越好
//    private static Float FACEQUAL;
//    //人脸类型，None：不是人脸；Face：人脸；Profile：侧脸（左右转头超过30°）
//    private static List<String> FACETYPE;
//    //人脸完整度，分数越高，人脸越完整
//    private static Integer FACE_INTEGRITY;

//    public static final String QINIU_IMAGE_UPLOAD_FOLDER_NAME = "aliyunFaceDetect";    //转存到七牛云后url中的文件夹名字
    private static String ALI_VERIFY_PHOTO_URL_KEY = "aliyunVerifyPhotoUrl_${accountUuid}";
    private static String ALI_BIZ_ID_KEY = "aliyunBizId_${accountUuid}";
    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunLiveFaceDetectService.class);
    private static String ALIYUN_FACE_VERIFY_LIMIT_KEY = "aliyunFaceVerifyLimit_${accountUuid}";
    private static String ALIYUN_FACE_VERIFY_LIMIT_KEY_CHANGE_MOBILE = "aliyunFaceVerifyLimit_mobile_${accountUuid}";
    private static String ALIYUN_FACE_VERIFY_LIMIT_KEY_RETRIEVE_ACCOUNT = "aliyunFaceVerifyLimit_retrieve_${accountUuid}";
    private static String ALIYUN_FACE_VERIFY_LIMIT_KEY_HIGH_RISK = "aliyunFaceVerifyLimit_highRisk_${accountUuid}";
    private static String ALIYUN_FACE_VERIFY_LIMIT_KEY_CASH = "aliyunFaceVerifyLimit_cash_${accountUuid}";
    private static String ALIYUN_FACE_VERIFY_LIMIT_KEY_WITHDRAW = "aliyunFaceVerifyLimit_withdraw_${accountUuid}";
    private static String FACE_VERIFY_INIT_CERTIFY_LIMIT_KEY = "faceVerifyInitCertifyLimit_${accountUuid}";
//    private final static String ALIYUN_PIC_COMPARE_SUCCESS_CODE = "1";
//    private final static String ALIYUN_FACE_DETECT_FLAG = "Face";


    public static final Integer PROFILE_SEQ_NUM = 1;  //1表示头像相册
//    private static final Integer NEED_CHANGE_PROFILE = 1;
    private static final Integer UN_FACE_CERTIFICATION = 0; //未进行照片对比标识
    public static final String NO_FACE_ERROR_CODE = "Error.NoFaceDetected";
    private static String ALIYUN_FACE_DETECT_LOCK_REDIS_KEY = "aliyunFaceDetectLockKey_${accountUuid}";
    public static Float DEFAULT_SIMILARITY_SCORE = 95.0f;  //存在活体认证成功，图片和底图对比失败的情况，这种时候的相似度默认为95.0
    public static Float UN_FACE_VERIFY = -1f;  //为进行人脸照片对比标志
    public static Double UN_FACE_VERIFY_DOUBLE = -1.0d;

    /**
     * 没有人脸
     */
    private static String NoHumanFace = "InvalidParameterValue.NoFaceInPhoto";

    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;
    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private QiniuService qiniuService;
    @Autowired
    private AliyunFacePhotoCompareLogService aliyunFacePhotoCompareLogService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private BuryService buryService;
    @Autowired
    private TengxunyunApiClient tengxunyunApiClient;
    @Autowired
    private PhotoCompareLogService photoCompareLogService;
    @Autowired
    private AccountsCertificationService accountsCertificationService;
    @Autowired
    private LiveFaceDetectLogService liveFaceDetectLogService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private AccountsChatCertificationDao accountsChatCertificationDao;
    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;
    @Autowired
    private AsyncTrackingService asyncTrackingService;
    @Autowired
    private UserCertificationLogService userCertificationLogService;
    @Autowired
    @Qualifier("lockStringRedisTemplate")
    private StringRedisTemplate lockStringRedisTemplate;
    @Autowired
    private EventTrackReporter eventTrackReporter;

    @Autowired
    private AccountBehaviorReporter behaviorReporter;

    @Autowired
    private CompareFaceService compareFaceService;

    public static List<String> COMPARE_UNCATCH_ERR_CODE = Arrays.asList(
            "InvalidParameterValue.NoFaceInPhoto",
            "InvalidParameterValue.ImageEmpty",
            "InvalidParameterValue.NoFaceInGroups",
            "InvalidParameterValue.UploadFaceNumExceed",
            "LimitExceeded.ErrorFaceNumExceed",
            "FailedOperation.SearchFacesExceed",
            "FailedOperation.FaceQualityNotQualifiedFailedOperation.FaceQualityNotQualified",
            "FailedOperation.FaceSizeTooSmall"
    );

    /**
     * 获取活体人脸验证的token
     * 参考文档：https://help.aliyun.com/document_detail/127687.html?spm=a2c4g.********.6.587.43a151d6EMOyHD
     *
     * @return
     */
    public static void initConf(String conf) {
        Map<String, Object> map = JsonUtils.stringToObject2(conf, new TypeReference<Map<String, Object>>() {
        });
        ALIYUN_SIMILARITY_SCORE = MapUtils.getFloat(map, "aliyunSimilarityScore", 90.0f);
//        PHOTO_URL_PRE_DOMAIN = MapUtils.getString(map, "photoUrlPreDomain", "https://${bucket}.jiaoliuqu.com/");
        regionId = MapUtils.getString(map, "regionId", "cn-hangzhou");
//        bizType = MapUtils.getString(map, "bizType", "tqpeople");
//        BUCKET = MapUtils.getString(map, "bucket", CommConst.AVATAR_BUCKET);
        accessKeyId = MapUtils.getString(map, "accessKeyId");
        secret = MapUtils.getString(map, "secret");
        FACE_DETECT_LIMIT_TIME = MapUtils.getInteger(map, "limitTimes", 4);
        FACE_DETECT_LIMIT_TIME_MOBILE = MapUtils.getInteger(map, "mobile_limitTimes", 3);
        FACE_DETECT_LIMIT_TIME_HIGH_RISK = MapUtils.getInteger(map, "highRisk_limitTimes", 3);
        FACE_DETECT_LIMIT_TIME_CASH = MapUtils.getInteger(map, "cash_limitTimes", 3);
        FACE_DETECT_LIMIT_TIME_WITHDRAW = MapUtils.getInteger(map, "withdraw_limitTimes", 5);
//        FACE_BLUR = MapUtils.getFloat(map, "faceBlur", 2.0F);
//        FACEQUAL = MapUtils.getFloat(map, "facequal", 50.0F);
//        FACETYPE = Splitter.on(",")
//                .omitEmptyStrings()
//                .trimResults()
//                .splitToList(
//                        MapUtils.getString(map, "facetype", "Face")
//                );
//        FACE_INTEGRITY = MapUtils.getInteger(map, "faceIntegrity", 70);
        TENGXUNYUN_SIMILARITY_SCORE = MapUtils.getFloat(map, "tenxunyunSimilarityScore", 50F);
        IMG_QUALITY_TENCENT_CLARITY_SCORE = MapUtils.getInteger(map, "imgQualityTencentClarityScore", 50);
//        profile = DefaultProfile.getProfile(
//                regionId,    // 固定cn-hangzhou
//                accessKeyId,      // 您的Access Key ID
//                secret);  // 您的Access Key Secret

//        client = new DefaultAcsClient(profile);
    }

    /**
     * 创建腾讯云client
     */
    static {
        Credential cred = new Credential(TENGXUNYUN_SECRETID, TENGXUNYUN_SECRETKEY);

        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(TENGXUNYUN_ENDPOINT);

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        laiClient = new IaiClient(cred, "ap-guangzhou", clientProfile);
    }

    static {
        Credential cred = new Credential(TENGXUNYUN_SECRETID, TENGXUNYUN_SECRETKEY);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("tiia.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        tiiaclient = new TiiaClient(cred, "ap-beijing", clientProfile);
    }

    /**
     * 获取SDK配置
     *
     * @param accountUuid
     * @return
     */
    public Map<String, Object> initFaceDetectConf(String accountUuid) {
        if (StringUtils.isEmpty(accountUuid)) {
            return Maps.newHashMap();
        }
        AccountsPhoto accountAvatar = accountsPhotoService.getAccountAvatar(accountUuid, null, false);
        if (accountAvatar == null) {
            throw new ServiceException(CodeStatus.NEED_AVATAR);
        }
        String profileUrl = ToolsService.addPhotoUrlPreDomain(accountAvatar.getPhoto_url());
        // 接口调用限制 腾讯云限制该接口每个用户 QPS 1， 这边加一个2秒限制
        String initCerifyLimit = FACE_VERIFY_INIT_CERTIFY_LIMIT_KEY.replace("${accountUuid}", accountUuid);
        boolean lock = redisLockUtil.lock(initCerifyLimit, 2000);
        if (!lock) {
            throw new ServiceException(CodeStatus.DETECT_TOO_FAST);
        }

        try {
            //1. 接入方服务端发起认证请求，获得认证token，接口文档：https://help.aliyun.com/document_detail/127470.html
            String bizId = generateBizId2();
            String aliyunFaceVerifyLimitKey = ALIYUN_FACE_VERIFY_LIMIT_KEY.replace("${accountUuid}", accountUuid);

            int limit = 0;
            String limitStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceVerifyLimitKey);
            if (StringUtils.isNotBlank(limitStr)) {
                limit = Integer.parseInt(limitStr);
            }
            boolean isLimit = limit > FACE_DETECT_LIMIT_TIME;

            //超过当天的检测次数，不能进行活体检测
            if (isLimit) {
                throw new ServiceException(CodeStatus.DETECT_OVER_TIMES_LIMIT);
            }

            try {
                return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.REAL_PERSON);
            } catch (ServerException e) {
                LOGGER.error("调用获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            } catch (ClientException e) {
                LOGGER.warn("调用获取腾讯云活体人脸检测token接口异常, 再次尝试获取, 用户uuid={}", accountUuid, e);
                try {
                    return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.REAL_PERSON);
                } catch (ServiceException se) {
                    throw se;
                } catch (Exception e1) {
                    LOGGER.error("重试获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                    throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
                }
            } catch (ServiceException se) {
                throw se;
            } catch (Exception e) {
                LOGGER.warn("获取腾讯云活体人脸检测token失败,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            }
        } finally {
            redisLockUtil.unLock(initCerifyLimit);
        }
    }

    public Map<String, Object> initFaceDetectConfForChangeMobile(String accountUuid) {
        return initLiveFaceDetect("ChangeMobile", accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY_CHANGE_MOBILE, FACE_DETECT_LIMIT_TIME_MOBILE);
    }

    public Map<String, Object> initFaceDetectConfForHighRisk(String accountUuid, String detectScene) {
        return initLiveFaceDetect(detectScene, accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY_HIGH_RISK, FACE_DETECT_LIMIT_TIME_HIGH_RISK);
    }

    public Map<String, Object> initLiveFaceDetect(String detectScene, String accountUuid, String limitKey, Integer maxLimit) {
        if (StringUtils.isEmpty(accountUuid)) {
            return Maps.newHashMap();
        }
        AccountsPhoto accountAvatar = accountsPhotoService.getAccountAvatar(accountUuid, null, false);
        if (accountAvatar == null) {
            throw new ServiceException(CodeStatus.NEED_AVATAR);
        }
        String profileUrl = ToolsService.addPhotoUrlPreDomain(accountAvatar.getPhoto_url());
        // 接口调用限制 腾讯云限制该接口每个用户 QPS 1， 这边加一个2秒限制
        String initCertifyLimit = FACE_VERIFY_INIT_CERTIFY_LIMIT_KEY.replace("${accountUuid}", accountUuid);
        boolean lock = redisLockUtil.lock(initCertifyLimit, 2000);
        if (!lock) {
            throw new ServiceException(CodeStatus.DETECT_TOO_FAST);
        }

        try {
            //1. 接入方服务端发起认证请求，获得认证token，接口文档：https://help.aliyun.com/document_detail/127470.html
            String bizId = generateBizId2();
            String aliyunFaceVerifyLimitKey = limitKey.replace("${accountUuid}", accountUuid);

            int limit = 0;
            String limitStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceVerifyLimitKey);
            if (StringUtils.isNotBlank(limitStr)) {
                limit = Integer.parseInt(limitStr);
            }

            //超过当天的检测次数，不能进行活体检测
            if (limit > maxLimit) {
                throw new ServiceException(CodeStatus.DETECT_OVER_TIMES_LIMIT_2);
            }

            try {
                return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.CHANGE_MOBILE);
            } catch (ServerException e) {
                LOGGER.error("调用获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            } catch (ClientException e) {
                LOGGER.warn("调用获取腾讯云活体人脸检测token接口异常, 再次尝试获取, 用户uuid={}", accountUuid, e);
                try {
                    return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.CHANGE_MOBILE);
                } catch (Exception e1) {
                    LOGGER.error("重试获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                    throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
                }
            } catch (Exception e) {
                LOGGER.warn("获取腾讯云活体人脸检测token失败,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            }
        } finally {
            redisLockUtil.unLock(initCertifyLimit);
        }
    }

    public Map<String, Object> initFaceDetectConfForCashMonitor(String accountUuid) {
        if (StringUtils.isEmpty(accountUuid)) {
            return Maps.newHashMap();
        }

        // 判断是否实名认证
        if (!accountsInfoService.isZhimaCertification(accountUuid)) {
            throw new ServiceException(CodeStatus.NO_CERTIFICATION_2);
        }

        String certificationPhotoUrl = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
        // 判断用户是否有留底照片
        if (StringUtils.isBlank(certificationPhotoUrl)) {
            throw new ServiceException(CodeStatus.PHOTO_NO_EXISTS);
        }

        String profileUrl = ToolsService.addAccountPrivateUrlPreDomain(certificationPhotoUrl);
        // 接口调用限制 腾讯云限制该接口每个用户 QPS 1， 这边加一个2秒限制
        String initCerifyLimit = FACE_VERIFY_INIT_CERTIFY_LIMIT_KEY.replace("${accountUuid}", accountUuid);
        boolean lock = redisLockUtil.lock(initCerifyLimit, 2000);
        if (!lock) {
            throw new ServiceException(CodeStatus.DETECT_TOO_FAST);
        }

        try {
            //1. 接入方服务端发起认证请求，获得认证token，接口文档：https://help.aliyun.com/document_detail/127470.html
            String bizId = generateBizId2();
            String aliyunFaceVerifyLimitKey = ALIYUN_FACE_VERIFY_LIMIT_KEY_CASH.replace("${accountUuid}", accountUuid);

            int limit = 0;
            String limitStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceVerifyLimitKey);
            if (StringUtils.isNotBlank(limitStr)) {
                limit = Integer.parseInt(limitStr);
            }
            boolean isLimit = limit > FACE_DETECT_LIMIT_TIME_CASH;

            //超过当天的检测次数，不能进行活体检测
            if (isLimit) {
                throw new ServiceException(CodeStatus.DETECT_OVER_TIMES_LIMIT_3);
            }

            try {
                return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.CASH_MONITOR);
            } catch (ServerException e) {
                LOGGER.error("调用获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            } catch (ClientException e) {
                LOGGER.warn("调用获取腾讯云活体人脸检测token接口异常, 再次尝试获取, 用户uuid={}", accountUuid, e);
                try {
                    return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.CASH_MONITOR);
                } catch (Exception e1) {
                    LOGGER.error("重试获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                    throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
                }
            } catch (Exception e) {
                LOGGER.warn("获取腾讯云活体人脸检测token失败,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            }
        } finally {
            redisLockUtil.unLock(initCerifyLimit);
        }
    }

    private static String generateBizId2() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取腾讯云活体认证token
     *
     * @param accountUuid
     * @param profileUrl 由于业务不同，可能是底图，也可能是头像
     * @param orderNo
     * @return
     * @throws Exception
     */
    private Map<String, Object> getTengxunyunVerifyToken(String accountUuid, String profileUrl, String orderNo, KafkaManufacturerCallDto.BelongBiz belongBiz) throws Exception {
        Map<String, Object> result = Maps.newHashMap();
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();
        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        String appid = WBappid.getAppid();
        String nonceTicket = tengxunyunApiClient.getNonceTicket(appid, tengxunyunVoucher.getAccessToken(), accountUuid);

        String nonce = generateBizId2();
        List<String> values = Lists.newArrayList();
        values.add("1.0.0");
        values.add(appid);
        values.add(nonce);
        values.add(accountUuid);
        String sign = TengxunyunApiClient.sign(values, nonceTicket);

        String aliyunVerifyPhotoUrlKey = ALI_VERIFY_PHOTO_URL_KEY.replace("${accountUuid}", accountUuid);
        String aliyunBizIdKey = ALI_BIZ_ID_KEY.replace("${accountUuid}", accountUuid);

        accountStringRedisTemplate.opsForValue().set(aliyunVerifyPhotoUrlKey, profileUrl, 10, TimeUnit.MINUTES);
        accountStringRedisTemplate.opsForValue().set(aliyunBizIdKey, orderNo, 10, TimeUnit.MINUTES);

        result.put("userid", accountUuid);
        result.put("nonce", nonce);
        result.put("sign", sign);
        result.put("orderno", orderNo);

        LOGGER.info("腾讯活体检测参数uuid={}.result={}", accountUuid, JSON.toJSONString(result));

        SpringContextHolder.getBean(AccountManufacturerCallProducer.class).send(
                KafkaManufacturerCallDto.tencentFaceIdentify(
                        accountUuid,
                        belongBiz,
                        "1",
                        Boolean.TRUE.toString())
        );

        return result;
    }

    /**
     * 获取腾讯云活体人脸验证结果
     *
     * @param accountUuid
     * @param basePhotoUrlNoUsed
     * @param source
     * @return
     */
    public Map<String, Object> checkFaceDetectResult(String accountUuid, String basePhotoUrlNoUsed, String source) {
        LOGGER.info("checkFaceDetectResult, uuid={}, basePhotoUrlNoUsed={}", accountUuid, basePhotoUrlNoUsed);

        boolean isShowDialog = false;

        Boolean result = false;
        Map<String, Object> map = Maps.newHashMap();
        if (StringUtils.isEmpty(accountUuid)) {
            return map;
        }

        LiveFaceDetectResultStatusEnum liveFaceDetectResultStatusEnum = LiveFaceDetectResultStatusEnum.DEFAULT;
        LiveFaceDetectResultTypeEnum liveFaceDetectResultTypeEnum = LiveFaceDetectResultTypeEnum.DEFAULT;
        LiveFaceDetectStatusEnum liveFaceDetectStatusEnum = LiveFaceDetectStatusEnum.DEFAULT;
        LiveFaceDetectTypeEnum liveFaceDetectTypeEnum = LiveFaceDetectTypeEnum.TENCENT;

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        TengxunyunApi tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
        String appid = WBappid.getAppid();

        DescribeVerifyResultRequest verifyResultRequest = new DescribeVerifyResultRequest();
        verifyResultRequest.setRegionId(regionId);
        verifyResultRequest.setSysProtocol(ProtocolType.HTTPS);
        String aliyunBizIdKey = ALI_BIZ_ID_KEY.replace("${accountUuid}", accountUuid);
        String orderno = accountStringRedisTemplate.opsForValue().get(aliyunBizIdKey);
        if (StringUtils.isEmpty(orderno)) {
            //可能是客户端请求多次，导致bizId之前已经被用了，这边直接提示用户
            throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
        }

        String nonce = generateBizId2();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderno);
        values.add("1.0.0");
        values.add(nonce);
        String sign = TengxunyunApiClient.sign(values, tengxunyunVoucher.getSignTicket());

        String serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce, orderno, sign);
        LOGGER.info("腾讯活体serverGetLiveResult【{}】", serverGetLiveResult);

        TxGetLiveResultResponse txGetLiveResultResponse =
                JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<TxGetLiveResultResponse>() {
                });
        String code = txGetLiveResultResponse.getCode();
        // 活体检测时的照片，Base 64 位编码
        String photo = null;
        String imgName = "";

        if ("0".equals(code)) {
            result = true;
            liveFaceDetectStatusEnum = LiveFaceDetectStatusEnum.SUCCESS;
            photo = txGetLiveResultResponse.getResult().getPhoto();
        } else {
            result = false;
            liveFaceDetectStatusEnum = LiveFaceDetectStatusEnum.FAILURE;
        }

        if (StringUtils.isNotBlank(photo)) {
            /*
             *  2023.11.20 由于腾讯云新版客户端SDK，不会返回给客户端图片。
             *  如果basePhotoUrl没值，可能是客户端SDK问题，也可能是新版SDK
             *
             *  1. 先校验，如果basePhotoUrl为空，则获取腾讯云返回图片
             *  2. 校验图片
             */
            LOGGER.info("checkFaceDetectResult 获取底图");
            PictureInfoVo uploadFile = qiniuService.uploadFileLiveFace(Encodes.decodeBase64(photo));
            imgName = uploadFile.getImg_name();
        }

        if(StringUtils.isBlank(imgName)) {
            // 传空说明sdk调用失败，只增加次数
            addFaceVerifyTimes(accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY, KafkaManufacturerCallDto.BelongBiz.REAL_PERSON);
            map.put("verify_msg", CodeStatus.VERIFY_FALI_SDK_ERROR_MSG.getReasonPhrase());
            map.put("veryfy_status", "0");
            map.put("change_photo", "0");
            return map;
        }

        //需推送状态到统计系统，0-认证失败，1-认证成功
        Integer verifyStatus = 0;
        //异步保存图片和返回的信息
        // 只记录日志，会清除域名
        String verifyPhotoUrl = getVerifyPhotoUrl(accountUuid);

        LOGGER.info("获取活体人脸验证结果,用户uuid={},检测结果={}", accountUuid, result);
        // 调用标记 true成功 false失败异常
        Boolean invokeFlag = true;
        try {
            // 活体成功的情况
            if (result) {
                // 判断是否已经业务级实名认证
                Object rpcObj = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "chat_real_certification");
                boolean isChatCertification = (null != rpcObj && Objects.equals(CommonEnableStatus.ENABLE.getStatus(), Integer.parseInt(rpcObj + "")));
                if (isChatCertification) {
                    AccountsChatCertification chatCertification = accountsChatCertificationDao.getByAccountUuid(accountUuid);
                    String pic1 = ToolsService.addAccountPrivateUrlPreDomain(null == chatCertification ? "" : chatCertification.getCertificationPhotoUrl());
                    String pic2 = ToolsService.addAccountPrivateUrlPreDomain(imgName);
                    LOGGER.info("faceDetect=[{}]真人与业务级实名对比图，pic1=[{}],pic2=[{}]", null == chatCertification ? "null" : chatCertification.getId(), pic1, pic2);
                    if (null != chatCertification && StringUtils.isNotBlank(pic1) && StringUtils.isNotBlank(pic2)) {
                        // 2022/6/13 compare with chat real certification photo
                        // 2024.07.04 对比前底图获取是有图片地址去比较
                        String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
                        String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);

                        CompareFaceDto facePicPCompare = comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.UNKNOWN);
                        // 保存到日志
                        result = facePicPCompare.isSimilarity();
                        photoCompareLogService.addLog(accountUuid, pic1, pic2, result ? 1 : 0, facePicPCompare.getScore(), facePicPCompare.getMsg(), PhotoCompareTypeEnum.FACE_DETECT.getValue());

                        if (!result) {
                            isShowDialog = true;
                            ShowDialogUtil.throwShowDialog(CopyWritingConst.REAL_PERSON_AVATAR_DIFFERENT_INFO, "知道了", "", "", "");
                        }
                    }
                }

                if (result) {
                    liveFaceDetectResultStatusEnum = LiveFaceDetectResultStatusEnum.SUCCESS;
                    verifyStatus = 1;
                    // 成功
                    processResponseV3(accountUuid, imgName, source);
                    String aliyunFaceDetectLockKey = ALIYUN_FACE_DETECT_LOCK_REDIS_KEY.replace("${accountUuid}", accountUuid);
                    //认证过程加锁，获取图片的时候需要用到
                    accountStringRedisTemplate.opsForValue().set(aliyunFaceDetectLockKey, aliyunFaceDetectLockKey, 2, TimeUnit.MINUTES);
                    map.put("verify_msg", CodeStatus.VERIFY_SUCCESS_MSG.getReasonPhrase());
                    map.put("veryfy_status", verifyStatus + "");
                    // 客户端是否需要换图片 1更换 0不更换
                    map.put("change_photo", "0");
                }
            } else {
                // result false 情况说明 活体失败
                liveFaceDetectResultStatusEnum = LiveFaceDetectResultStatusEnum.FAILURE;
                liveFaceDetectResultTypeEnum = LiveFaceDetectResultTypeEnum.LIVE_FACE_DETECT_API_FAILURE;
                // 失败
                map.put("verify_msg", CodeStatus.VERIFY_FALI_MSG.getReasonPhrase());
                map.put("veryfy_status", verifyStatus + "");
                // 客户端是否需要换图片 1更换 0不更换
                map.put("change_photo", "1");

                LOGGER.warn("腾讯人脸活体认证失败,status={},用户uuid={}", 0, accountUuid);
            }

            handlerAfterFaceVerify(accountUuid, verifyStatus);
            return map;
        } catch (Exception e) {
            invokeFlag = false;
            if (isShowDialog) {
                LOGGER.warn("人脸认证过程出现异常,用户uuid={}", accountUuid, e);
                throw e;
            } else {
                LOGGER.error("人脸认证过程出现异常,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            }
        } finally {
            if (invokeFlag) {
                liveFaceDetectLogService.addLiveFaceDetectLog(accountUuid, imgName, verifyPhotoUrl, liveFaceDetectTypeEnum.getValue(), liveFaceDetectStatusEnum.getValue(), orderno,
                        liveFaceDetectResultStatusEnum.getValue(), liveFaceDetectResultTypeEnum.getValue(), "");
            }
        }
    }

    /**
     * 获取腾讯云活体人脸验证结果
     *
     * @param accountUuid
     * @param verifyPhotoUrl
     * @return
     */
    public void detectFaceForChangeMobile(String accountUuid, String verifyPhotoUrl) {
        compareLiveFaceDetect(accountUuid, "changeMobile", verifyPhotoUrl, ALIYUN_FACE_VERIFY_LIMIT_KEY_CHANGE_MOBILE);
    }

    public void detectFaceForHighRisk(String accountUuid, String detectScene, String verifyPhotoUrl) {
        compareLiveFaceDetect(accountUuid, detectScene, verifyPhotoUrl, ALIYUN_FACE_VERIFY_LIMIT_KEY_HIGH_RISK);
    }

    public void compareLiveFaceDetect(String accountUuid, String detectScene, String verifyPhotoUrlNoUsed, String limitKey) {
        LOGGER.info("compareLiveFaceDetect uuid: {}, scene: {}, verifyPhotoUrlNoUsed: {}", accountUuid, detectScene, verifyPhotoUrlNoUsed);

        // 增加调用次数
        addFaceVerifyTimes(accountUuid, limitKey, KafkaManufacturerCallDto.BelongBiz.CHANGE_MOBILE);

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        TengxunyunApi tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
        String appid = WBappid.getAppid();

        DescribeVerifyResultRequest verifyResultRequest = new DescribeVerifyResultRequest();
        verifyResultRequest.setRegionId(regionId);
        verifyResultRequest.setSysProtocol(ProtocolType.HTTPS);
        String aliyunBizIdKey = ALI_BIZ_ID_KEY.replace("${accountUuid}", accountUuid);
        String orderno = accountStringRedisTemplate.opsForValue().get(aliyunBizIdKey);
        if (StringUtils.isEmpty(orderno)) {
            //可能是客户端请求多次，导致bizId之前已经被用了，这边直接提示用户
            throw new ServiceException(CodeStatus.VERIFY_FALI_MOBILE_MSG_2);
        }

        String nonce = generateBizId2();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderno);
        values.add("1.0.0");
        values.add(nonce);
        String sign = TengxunyunApiClient.sign(values, tengxunyunVoucher.getSignTicket());

        String serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce, orderno, sign);
        LOGGER.info("腾讯活体serverGetLiveResult【{}】", serverGetLiveResult);

        TxGetLiveResultResponse txGetLiveResultResponse =
            JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<TxGetLiveResultResponse>() {
            });
        String code = txGetLiveResultResponse.getCode();

        // 认证成功
        if ("0".equals(code)) {
            /*
             *  2023.11.20 由于腾讯云新版客户端SDK，不会返回给客户端图片。
             *  如果verifyPhotoUrl没值，可能是客户端SDK问题，也可能是新版SDK
             *
             *  1. 先校验，如果verifyPhotoUrl为空，则获取腾讯云返回图片
             *  2. 校验图片
             */
            // 活体检测时的照片，Base 64 位编码
            String photo = txGetLiveResultResponse.getResult().getPhoto();
            String imgName = "";
            if(StringUtils.isBlank(imgName) && StringUtils.isNotBlank(photo)){
                LOGGER.info("compareLiveFaceDetect 获取底图");
                PictureInfoVo uploadFile = qiniuService.uploadFileLiveFace(Encodes.decodeBase64(photo));
                imgName = uploadFile.getImg_name();
            }

            String pic1 = ToolsService.addAccountPrivateUrlPreDomain(imgName);
            String pic2 = "";

            if (StringUtils.isBlank(imgName)) {
                // 传空说明sdk调用失败，只增加次数
                throw new ServiceException(CodeStatus.VERIFY_FALI_SDK_ERROR_MSG);
            }

            int compareType = 0;

            if (accountsInfoService.isZhimaCertification(accountUuid)) {
                pic2 = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
                compareType = 1;

                // 特殊处理：已实名认证用户无底图，尝试获取真人底图比对
                if (StringUtils.isBlank(pic2)) {
                    if (accountsInfoService.isFaceCertification(accountUuid)) {
                        pic2 = aliyunLiveFaceDetectDao.getBasePhotoUrlByAccountUuid(accountUuid);
                        compareType = 2;
                    }
                }
            } else if (accountsInfoService.isFaceCertification(accountUuid)) {
                pic2 = aliyunLiveFaceDetectDao.getBasePhotoUrlByAccountUuid(accountUuid);
                compareType = 2;
            }

            LOGGER.info("compareLiveFaceDetect - uuid: {}, pic1: {}, pic2: {}, compareType: {}", accountUuid, pic1, pic2, compareType);

            // 若底图为空，则返回认证失败
            if (StringUtils.isBlank(pic2)) {
                userCertificationLogService.logLiveCertFail(accountUuid, pic1, "", 0.0f);

                LOGGER.warn("uuid: {} compareLiveFaceDetect failed, pic2 is blank", accountUuid);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MOBILE_MSG_2);
            }

            String fullPic2 = ToolsService.addAccountPrivateUrlPreDomain(pic2);

            // 2024.07.04 对比前底图获取是有图片地址去比较
            String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
            String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);

            CompareFaceDto facePicPCompare = comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.LIVING_ORGANISM);

            // the face compared result is similarity
            if (facePicPCompare.isSimilarity()) {
                LOGGER.info("uuid: {} change mobile face detect success", accountUuid);
                userCertificationLogService.logLiveCertSuccess(accountUuid, pic1, fullPic2, facePicPCompare.getScore());

                BuryService.pushToAccountLivingCert(accountUuid, CertTypeEnum.LIVE_CERT);
                return;
            }

            // throw exception if certificate error or the result of pictures comparison isn't similar
            userCertificationLogService.logLiveCertFail(accountUuid, pic1, fullPic2, facePicPCompare.getScore());
        }
        // 2024.08.08 修复bug，只有对比通过，才不会走到这边
        throw new ServiceException(CodeStatus.VERIFY_FALI_MOBILE_MSG_2);
    }

    /**
     *
     * @param accountUuid
     * @param verifyPhotoUrlNoUsed 没用了
     */
    public void detectFaceForCashMonitor(String accountUuid, String verifyPhotoUrlNoUsed) {
        LOGGER.info("detectFaceForCashMonitor, uuid={}, verifyPhotoUrlNoUsed={}", accountUuid, verifyPhotoUrlNoUsed);

        // 增加调用次数
        addFaceVerifyTimes(accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY_CASH, KafkaManufacturerCallDto.BelongBiz.CASH_MONITOR);

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        TengxunyunApi tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
        String appid = WBappid.getAppid();

        DescribeVerifyResultRequest verifyResultRequest = new DescribeVerifyResultRequest();
        verifyResultRequest.setRegionId(regionId);
        verifyResultRequest.setSysProtocol(ProtocolType.HTTPS);
        String aliyunBizIdKey = ALI_BIZ_ID_KEY.replace("${accountUuid}", accountUuid);
        String orderno = accountStringRedisTemplate.opsForValue().get(aliyunBizIdKey);
        if (StringUtils.isEmpty(orderno)) {
            //可能是客户端请求多次，导致bizId之前已经被用了，这边直接提示用户
            throw new ServiceException(CodeStatus.VERIFY_FALI_MOBILE_MSG_2);
        }

        String nonce = generateBizId2();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderno);
        values.add("1.0.0");
        values.add(nonce);
        String sign = TengxunyunApiClient.sign(values, tengxunyunVoucher.getSignTicket());

        String serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce, orderno, sign);
        LOGGER.info("腾讯活体serverGetLiveResult【{}】", serverGetLiveResult);

        TxGetLiveResultResponse txGetLiveResultResponse =
            JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<TxGetLiveResultResponse>() {
            });
        String code = txGetLiveResultResponse.getCode();

        // 认证成功
        if ("0".equals(code)) {

            /*
             *  2023.11.20 由于腾讯云新版客户端SDK，不会返回给客户端图片。
             *  如果verifyPhotoUrl没值，可能是客户端SDK问题，也可能是新版SDK
             *
             *  1. 先校验，如果verifyPhotoUrl为空，则获取腾讯云返回图片
             *  2. 校验图片
             */
            // 活体检测时的照片，Base 64 位编码
            String photo = txGetLiveResultResponse.getResult().getPhoto();
            String imgName = "";
            if(StringUtils.isNotBlank(photo)){
                LOGGER.info("detectFaceForCashMonitor 获取底图");
                PictureInfoVo uploadFile = qiniuService.uploadFileLiveFace(Encodes.decodeBase64(photo));
                imgName = uploadFile.getImg_name();
            }

            String pic1 = ToolsService.addAccountPrivateUrlPreDomain(imgName);
            String pic2 = "";

            if (accountsInfoService.isZhimaCertification(accountUuid)) {
                pic2 = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
            }

            LOGGER.info("finance monitor face detect - pic1: {}, pic2: {}", pic1, pic2);

            // 2024.07.04 对比前底图获取是有图片地址去比较
            String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
            String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);

            CompareFaceDto facePicPCompare = comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.UNKNOWN);

            // the face compared result is similarity
            if (facePicPCompare.isSimilarity()) {
                LOGGER.info("uuid: {} finance monitor face detect success", accountUuid);
                return;
            }
        }

        // throw exception if certificate error or the result of pictures comparison isn't similar
        throw new ServiceException(CodeStatus.VERIFY_FALI_MOBILE_MSG_3);
    }

    /**
     * 提现扫脸
     *
     * @param accountUuid
     * @return
     */
    public Map<String, Object> initFaceDetectConfForWithdraw(String accountUuid) {
        if (StringUtils.isEmpty(accountUuid)) {
            return Maps.newHashMap();
        }

        // 底图
        String basePhotoUrl = null;

        if (accountsInfoService.isZhimaCertification(accountUuid)) {
            basePhotoUrl = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
        }
        if (StringUtils.isBlank(basePhotoUrl) && accountsInfoService.isFaceCertification(accountUuid)) {
            basePhotoUrl = aliyunLiveFaceDetectDao.getBasePhotoUrlByAccountUuid(accountUuid);
        }

        if (StringUtils.isBlank(basePhotoUrl)) {
            throw new ServiceException(CodeStatus.PHOTO_NO_EXISTS);
        }

        String profileUrl = ToolsService.addAccountPrivateUrlPreDomain(basePhotoUrl);
        // 接口调用限制 腾讯云限制该接口每个用户 QPS 1， 这边加一个2秒限制
        String initCerifyLimit = FACE_VERIFY_INIT_CERTIFY_LIMIT_KEY.replace("${accountUuid}", accountUuid);
        boolean lock = redisLockUtil.lock(initCerifyLimit, 2000);
        if (!lock) {
            throw new ServiceException(CodeStatus.DETECT_TOO_FAST);
        }

        try {
            //1. 接入方服务端发起认证请求，获得认证token
            String bizId = generateBizId2();
            String aliyunFaceVerifyLimitKey = ALIYUN_FACE_VERIFY_LIMIT_KEY_WITHDRAW.replace("${accountUuid}", accountUuid);

            int limit = 0;
            String limitStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceVerifyLimitKey);
            if (StringUtils.isNotBlank(limitStr)) {
                limit = Integer.parseInt(limitStr);
            }
            boolean isLimit = limit > FACE_DETECT_LIMIT_TIME_WITHDRAW;

            //超过当天的检测次数，不能进行活体检测
            if (isLimit) {
                throw new ServiceException(CodeStatus.DETECT_OVER_TIMES_LIMIT_WITHDRAW);
            }

            try {
                return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.WITHDRAW);
            } catch (ServerException e) {
                LOGGER.error("调用获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            } catch (ClientException e) {
                LOGGER.warn("调用获取腾讯云活体人脸检测token接口异常, 再次尝试获取, 用户uuid={}", accountUuid, e);
                try {
                    return getTengxunyunVerifyToken(accountUuid, profileUrl, bizId, KafkaManufacturerCallDto.BelongBiz.WITHDRAW);
                } catch (Exception e1) {
                    LOGGER.error("重试获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                    throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
                }
            } catch (Exception e) {
                LOGGER.warn("获取腾讯云活体人脸检测token失败,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
            }
        } finally {
            redisLockUtil.unLock(initCerifyLimit);
        }
    }

    public Map<String, Object> detectFaceForWithdraw(String accountUuid) {
        LOGGER.info("detectFaceForWithdraw, uuid={}, verifyPhotoUrlNoUsed={}", accountUuid);

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        TengxunyunApi tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
        String appid = WBappid.getAppid();

        DescribeVerifyResultRequest verifyResultRequest = new DescribeVerifyResultRequest();
        verifyResultRequest.setRegionId(regionId);
        verifyResultRequest.setSysProtocol(ProtocolType.HTTPS);
        String aliyunBizIdKey = ALI_BIZ_ID_KEY.replace("${accountUuid}", accountUuid);
        String orderno = accountStringRedisTemplate.opsForValue().get(aliyunBizIdKey);
        if (StringUtils.isEmpty(orderno)) {
            //可能是客户端请求多次，导致bizId之前已经被用了，这边直接提示用户
            throw new ServiceException(CodeStatus.VERIFY_FALI_MOBILE_MSG_2);
        }

        String nonce = generateBizId2();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderno);
        values.add("1.0.0");
        values.add(nonce);
        String sign = TengxunyunApiClient.sign(values, tengxunyunVoucher.getSignTicket());

        String serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce, orderno, sign);
        LOGGER.info("腾讯活体serverGetLiveResult【{}】", serverGetLiveResult);

        TxGetLiveResultResponse txGetLiveResultResponse =
            JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<TxGetLiveResultResponse>() {
            });
        String code = txGetLiveResultResponse.getCode();

        // 认证成功
        if ("0".equals(code)) {

            /*
             *  2023.11.20 由于腾讯云新版客户端SDK，不会返回给客户端图片。
             *  如果verifyPhotoUrl没值，可能是客户端SDK问题，也可能是新版SDK
             *
             *  1. 先校验，如果verifyPhotoUrl为空，则获取腾讯云返回图片
             *  2. 校验图片
             */
            // 活体检测时的照片，Base 64 位编码
            String photo = txGetLiveResultResponse.getResult().getPhoto();
            String imgName = "";
            if(StringUtils.isNotBlank(photo)){
                LOGGER.info("detectFaceForWithdraw 获取底图");
                PictureInfoVo uploadFile = qiniuService.uploadFileLiveFace(Encodes.decodeBase64(photo));
                imgName = uploadFile.getImg_name();
            }

            String pic1 = ToolsService.addAccountPrivateUrlPreDomain(imgName);
            // 底图
            String pic2 = "";

            if (accountsInfoService.isZhimaCertification(accountUuid)) {
                pic2 = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
            }
            if (StringUtils.isBlank(pic2) && accountsInfoService.isFaceCertification(accountUuid)) {
                pic2 = aliyunLiveFaceDetectDao.getBasePhotoUrlByAccountUuid(accountUuid);
            }

            LOGGER.info("withdraw face detect - pic1: {}, pic2: {}", pic1, pic2);

            // 2024.07.04 对比前底图获取是有图片地址去比较
            String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
            String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);

            CompareFaceDto facePicPCompare = comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.WITHDRAWAL_CERTIFICATION);

            // the face compared result is similarity
            if (facePicPCompare.isSimilarity()) {
                LOGGER.info("uuid: {} withdraw face detect success", accountUuid);

                Map<String, Object> map = Maps.newHashMap();
                map.put("verify_msg", CodeStatus.WITHDRAW_VERIFY_SUCCESS_MSG.getReasonPhrase());
                map.put("veryfy_status", "1");

                // 记录缓存，给业务侧查询，标记用户做了该操作
                ValueOperations<String, String> opsForValue = lockStringRedisTemplate.opsForValue();
                String redisKey = RedisKeyConstant.CONTROL_DETECT_FACE_FOR_WITHDRAW.setArg(accountUuid);
                opsForValue.set(redisKey, "1", 5, TimeUnit.MINUTES);
                LOGGER.info("提现扫脸记录缓存。accountUuid={}", accountUuid);
                userCertificationLogService.logWithdrawSuccess(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(pic1), ToolsService.addAccountPrivateUrlPreDomain(pic2), facePicPCompare.getScore());
                return map;
            }else {
                userCertificationLogService.logWithdrawFail(accountUuid, AvatarHandleService.getAvatarSrcPhotoUrl(pic1), ToolsService.addAccountPrivateUrlPreDomain(pic2), facePicPCompare.getScore());
            }

        }
        // 失败 增加调用次数
        addFaceVerifyTimes(accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY_WITHDRAW, KafkaManufacturerCallDto.BelongBiz.WITHDRAW);

        // throw exception if certificate error or the result of pictures comparison isn't similar
        throw new ServiceException(CodeStatus.WITHDRAW_VERIFY_FALI_MSG);
    }

    /**
     * 是否完成扫脸提现
     * @param accountUuid
     * @return
     */
    public boolean isCompleteDetectFaceForWithdraw(String accountUuid) {
        ValueOperations<String, String> opsForValue = lockStringRedisTemplate.opsForValue();
        String redisKey = RedisKeyConstant.CONTROL_DETECT_FACE_FOR_WITHDRAW.setArg(accountUuid);
        String value = opsForValue.get(redisKey);
        if(StringUtils.isBlank(value)) {
            return false;
        }else {
            return true;
        }
    }

    /**
     * 活体认证的次数 + 1
     *
     * @param accountUuid
     */
    private void addFaceVerifyTimes(String accountUuid, String limitKey, KafkaManufacturerCallDto.BelongBiz belongBiz) {
        //每天进行活体认证的次数是有限的
        String aliyunFaceVerifyLimitKey = limitKey.replace("${accountUuid}", accountUuid);
        int limit = 0;
        String limitStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceVerifyLimitKey);
        if (StringUtils.isNotBlank(limitStr)) {
            limit = Integer.parseInt(limitStr);
        }

        long todayRemainSec = TimeFormatUtil.getTodayRemainSec();
        if (todayRemainSec != 0) {
            accountStringRedisTemplate.opsForValue().set(aliyunFaceVerifyLimitKey, String.valueOf(limit + 1), todayRemainSec, TimeUnit.SECONDS);
        }

        SpringContextHolder.getBean(AccountManufacturerCallProducer.class).send(
                KafkaManufacturerCallDto.tencentFaceIdentify(
                        accountUuid,
                        belongBiz,
                        "2",
                        Boolean.TRUE.toString())
        );
    }

    private void handlerAfterFaceVerify(String accountUuid, Integer verifyStatus) {
        addFaceVerifyTimes(accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY, KafkaManufacturerCallDto.BelongBiz.REAL_PERSON);
//        BuryService.pushToStatisticQueue(accountUuid, verifyStatus);
    }

    /**
     * 填充性别属性
     * 2024.07.11 业务已下线直接给默认值
     *
     * @param detect
     */
    private void fillGenderField(AliyunLiveFaceDetect detect) {
        detect.setAccount_gender(LiveFaceDetectGenderEnum.DEFAULT.getValue());
        detect.setBase_photo_gender(LiveFaceDetectGenderEnum.DEFAULT.getValue());
        detect.setGender_compare_result(LiveFaceDetectGenderCompareResultEnum.DEFAULT.getValue());
    }

    /**
     * 处理活体成功逻辑（这边仅处理活体，不处理比对和真人认证）
     *
     * @param accountUuid
     * @param basePhotoUrl
     */
    private void processResponseV3(String accountUuid, String basePhotoUrl, String source) {
        basePhotoUrl = removePhotoUrlPreDomain(basePhotoUrl);
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        if (null != detect) {
            detect.setVerify_photo_url(basePhotoUrl);
            detect.setBase_photo_url(basePhotoUrl);
            detect.setCreate_time(DateUtil.currentTimeSeconds());
            detect.setUpdate_time(DateUtil.currentTimeSeconds());
            fillGenderField(detect);
            detect.setSource(source);
            aliyunLiveFaceDetectDao.merge(detect);
        } else {
            detect = new AliyunLiveFaceDetect();
            detect.setAccount_uuid(accountUuid);
            detect.setBase_photo_url(basePhotoUrl);
            detect.setVerify_photo_url(basePhotoUrl);
            detect.setCreate_time(DateUtil.currentTimeSeconds());
            detect.setUpdate_time(DateUtil.currentTimeSeconds());
            fillGenderField(detect);
            detect.setSource(source);
            detect = aliyunLiveFaceDetectDao.merge(detect);
        }

        setFaceVerifyStatustCache(accountUuid, AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus());

        // 活体认证事件埋点
        EventTrackingDO tracking = EventTrackingDO.create(accountUuid, AccountEvent.LIVE_FACE_CERT);
        kafkaSinkUtil.push(EventConst.EVENT_TRACKING_TOPIC, tracking);
        behaviorReporter.report(accountUuid, tracking);
    }

    /**
     * 处理真人认证成功 （已经完成了活体，再进行图片比对）
     *
     * @param accountUuid
     * @param verifyPhotoUrl
     */
    public void processResponseV4(String accountUuid, String verifyPhotoUrl) {
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        detect.setVerify_photo_url(verifyPhotoUrl);
        aliyunLiveFaceDetectDao.merge(detect);
    }

    /**
     * 头像、相册审核通过处理
     * 埋点相关处理
     *
     * @param accountUuid
     */
    public void processResponseV5(String accountUuid) {
        buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.REAL_AVATAR_CONFIRMED.getType());

        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        // 队列延迟消费 保证该事务提交
        Map<String, Object> map = Maps.newHashMap();
        map.put("detectId", detect.getId());
        map.put("aliyunTempPhotoUrl", detect.getBase_photo_url());
        map.put("accountUuid", accountUuid);
        MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(AliyunFacePhotoStoreTask.MQ_NAME, map, 3L);
        if (mqResponse.fail()) {
            LOGGER.error("活体人脸检测底图转存处理推入队列aliyun_facephoto_store_queue失败,{}, 失败原因:{}-{}", map, mqResponse.getCode(), mqResponse.getMsg());
        }

        // 真人认证推活动中台
        asyncTrackingService.pushRealPersonCertToActivityKafka(accountUuid,
                SoaBaseParams.fromThread().getAppcode(),
                SoaBaseParams.fromThread().getCloned(),
                SoaBaseParams.fromThread().getToken(),
                SoaBaseParams.fromThread().getPlatform_id());
    }

    /**
     * 活体认证失败的逻辑处理(兼容)
     *
     * @param accountUuid
     * @param verifyPhotoUrl
     * @param basePhotoUrl
     */
    @Async
    public void processFailResponseV3(String accountUuid, String verifyPhotoUrl, String basePhotoUrl, Float similarityScore) {
        AccountsPhoto photo = null;
        List<AccountsPhoto> list = accountsPhotoService.findByUuid(accountUuid);
        for (AccountsPhoto accountsPhoto : list) {
            if (verifyPhotoUrl.contains(accountsPhoto.getPhoto_url())) {
                photo = accountsPhoto;
                break;
            }
        }
        if (photo == null) {
            LOGGER.warn("活体认证失败时,未找到该用户的相册图片记录,用户uuid={},图片地址={}", accountUuid, verifyPhotoUrl);
            return;
        }
//        String qiniuPhotoUrl = getCustomImageUrl(basePhotoUrl);
        photo.setVerify_status(AliyunLiveFaceDetectStatus.FACE_AUTHORIZED_FAIL.getStatus());
        saveAliyunFacePhotoCompareLog(photo, basePhotoUrl, 0.0f, false);

        String liveFaceUrl = ToolsService.addAccountPrivateUrlPreDomain(basePhotoUrl);
        String avatarUrl = ToolsService.addPhotoUrlPreDomain(verifyPhotoUrl);
        userCertificationLogService.logRealPhotoFail(accountUuid, avatarUrl, liveFaceUrl, similarityScore);
    }


    /**
     * 人脸对比对口 https://cloud.tencent.com/document/product/867/44987
     */
    public CompareFaceDto compareFace(String picOne, String picTwo, String uuid, RiskCertificationTypeEnum certType) {
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_COUNTER);
        Histogram.Timer timer = MonitorService.getHistogramTimer(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_RT_HISTOGRAM);
        picOne = AccountsPhotoService.replacePhotoDomainCompare(picOne);
        picTwo = AccountsPhotoService.replacePhotoDomainCompare(picTwo);
        CompareFaceDto dto;
        try {
            // 人脸比对接口
            dto = compareFaceService.compare(picOne, picTwo, uuid);
            // 是否开启接口数据比对窗口
            if (compareFaceService.isOpenInterfaceCompare()) {
                CompareUserDTO user = new CompareUserDTO(uuid, certType.getType());
                compareFaceService.interfaceCompare(dto, user);
            }
        } finally {
            if (timer != null) {
                timer.observeDuration();
            }

            SpringContextHolder.getBean(AccountManufacturerCallProducer.class).send(
                    KafkaManufacturerCallDto.tencentFaceDetect(
                            "",
                            KafkaManufacturerCallDto.BelongBiz.UNDEFINED,
                            "",
                            Boolean.TRUE.toString())
            );
        }
        return dto;
    }

    /**
     * 腾讯云是否人脸，true-是人脸，false-非人脸或者腾讯云服务异常
     * 参考文档：https://cloud.tencent.com/document/product/867/32800
     *
     * @return
     */
    public Boolean detectIsFaceTengxun(String accountUuid, String photoUrl) {
        DetectFaceRequest req = new DetectFaceRequest();
        req.setUrl(photoUrl);
        try {
            DetectFaceResponse resp = laiClient.DetectFace(req);
            LOGGER.info("resp={}", JSON.toJSONString(resp));
        } catch (TencentCloudSDKException e) {
            if (!NoHumanFace.equals(e.getErrorCode())) {
                LOGGER.warn("调用腾讯云人脸检测接口失败,原因：{},图片url：{}", e, photoUrl);
            }
            return false;
        }
        SpringContextHolder.getBean(AccountManufacturerCallProducer.class).send(
                KafkaManufacturerCallDto.tencentFaceDetect(
                        accountUuid,
                        KafkaManufacturerCallDto.BelongBiz.UNDEFINED,
                        "",
                        Boolean.TRUE.toString())
        );
        return true;
    }

    /**
     * 检测是否是人脸
     * 如果校验到多张脸，取max(facequal)
     * 0-未认证(未知)，1-认证成功，2-认证失败
     */
    public Integer detectIsFaceV2(String accountUuid, String photoUrl) {
        Boolean picIsFace = picIsFace(accountUuid, photoUrl);
        if (picIsFace) {
            return 1;
        }
        return 2;
    }

    /**
     * 保存活体人脸验证的底图和该用户相册图片与底图的对比认证V2（事务时间更短，第三方检测移出方法外）
     *
     * @param accountUuid
     * @param qiniuPhotoUrl
     * @param detectId
     * @param updatePhotoList
     * @param aliyunFacePhotoCompareLogs
     */
    public void processSaveAndCompareV2(String accountUuid, List<AccountsPhoto> photoList, String qiniuPhotoUrl, Long detectId, Long successVerifyPhotoCount, JSONArray updatePhotoList, List<AliyunFacePhotoCompareLog> aliyunFacePhotoCompareLogs) {
        //保存活体人脸验证底图，这边的qiniuPhotoUrl + PHOTO_URL_PRE_DOMAIN可以得到图片的完整访问路径
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.findById(detectId);
        if (null == detect) {
            return;
        }
        detect.setBase_photo_url(removePhotoUrlPreDomain(qiniuPhotoUrl));
        aliyunLiveFaceDetectDao.merge(detect);

        //该用户相册与底图的认证，需保存认证的日志
        if (CollectionUtils.isNotEmpty(photoList)) {
            for (int i = 0; i < updatePhotoList.size(); i++) {
                JSONObject photoJson = updatePhotoList.getJSONObject(i);
                // 根据id 和图片url判断
                accountsPhotoService.updateVerifyStatusByIdAndPhotoUrl(photoJson.getLong("id"), photoJson.getInteger("verifyStatus"), photoJson.getString("errorMsg"), photoJson.getString("photoUrl"));
            }
            saveAliyunFacePhotoCompareLog(aliyunFacePhotoCompareLogs);

            //推入队列给bbs消费
            pushVerifySuccessPhotoCountToBbs(accountUuid, successVerifyPhotoCount);
            //图片认证成功后，修改图片排序策略的分值
            accountsPhotoService.setAccountsPhotoToRedis(photoList, accountUuid);
            String aliyunFaceDetectLockKey = ALIYUN_FACE_DETECT_LOCK_REDIS_KEY.replace("${accountUuid}", accountUuid);
            //认证结束后进行解锁操作
            accountStringRedisTemplate.opsForValue().set(aliyunFaceDetectLockKey, aliyunFaceDetectLockKey, 1, TimeUnit.MILLISECONDS);

            // 判断用户是否无真人认证图片
            accountsService.checkAccountEmptyFacePhoto(accountUuid);
        }
    }

    private void pushVerifySuccessPhotoCountToBbs(String accountUuid, Long successVerifyPhotoCount) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("account_uuid", accountUuid);
        param.put("type", "real_avatar_confirmed");
        param.put("real_num", successVerifyPhotoCount);
        MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_MODIFY_GROW_SCORE, param, null);
        if (mqResponse.fail()) {
            LOGGER.error("相册认证行为推入modify_grow_score队列失败,{}, 失败原因:{}-{}", param, mqResponse.getCode(), mqResponse.getMsg());
            return;
        }
        LOGGER.info("相册认证推入modify_grow_score,用户uuid={},成功认证个数={}", accountUuid, successVerifyPhotoCount);
    }

    /**
     * 批量保存对比日志
     *
     * @param logs
     */
    private void saveAliyunFacePhotoCompareLog(List<AliyunFacePhotoCompareLog> logs) {
        aliyunFacePhotoCompareLogService.merge(logs);
    }

    /**
     *
     * @param photo
     * @param basePhotoUrl
     * @param similarityScore
     * @param ifSaveGender  2024.07.11 无用了，不用判断
     */
    private void saveAliyunFacePhotoCompareLog(AccountsPhoto photo, String basePhotoUrl, Float similarityScore, Boolean ifSaveGender) {
        AliyunFacePhotoCompareLog log = new AliyunFacePhotoCompareLog();
        log.setAccount_uuid(photo.getAccount_uuid());
        log.setAccounts_photo_id(photo.getId());
        log.setBase_photo_url(basePhotoUrl);
        log.setSimilarity_score(similarityScore);
        log.setStatus(photo.getVerify_status());
        //直接保存即可，PHOTO_URL_PRE_DOMAIN+这边的url即可直接得到绝对路径的图片地址
        log.setVerify_photo_url(photo.getPhoto_url());
        log.setCreate_time(DateUtil.currentTimeSeconds());
        log.setError_msg(photo.getError_msg());
        //log.setUpdate_time(DateUtil.currentTimeSeconds());
        log.setAccount_gender(LiveFaceDetectGenderEnum.DEFAULT.getValue());
        log.setBase_photo_gender(LiveFaceDetectGenderEnum.DEFAULT.getValue());
        log.setGender_compare_result(LiveFaceDetectGenderCompareResultEnum.DEFAULT.getValue());
        aliyunFacePhotoCompareLogService.merge(log);
    }

    /**
     * 设置活体人脸验证结果
     *
     * @param accountUuid
     */
    private void setFaceVerifyStatustCache(String accountUuid, Integer faceVerifyStatus) {
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();
        hashValue.put("face_certification", String.valueOf(faceVerifyStatus));
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);
    }

    /**
     * 是否真人认证 通过
     *
     * @param accountUuid
     * @return
     */
    private Boolean isRealPersonCertificationStatusSuccess(String accountUuid) {
        String redisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Object realPersonCertificationStatus = accountStringRedisTemplate.opsForHash().get(redisKey, "real_person_certification");
        return String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus()).equals(realPersonCertificationStatus);
    }

    /**
     * 设置头像是否认证的缓存
     *
     * @param accountsPhoto
     * @param profileVerifyStatus
     * @param profileErrorMsg
     */
    public void setProfileVerifyStatustCache(AccountsPhoto accountsPhoto, Integer profileVerifyStatus, String profileErrorMsg) {
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountsPhoto.getAccount_uuid());
        Map<String, String> hashValue = new HashMap<>();
        if (AccountsPhoto.Status.PASS.getValue() == accountsPhoto.getStatus()) {
            hashValue.put("profile_verify_status", String.valueOf(profileVerifyStatus));
        } else {
            hashValue.put("pre_profile_verify_status", String.valueOf(profileVerifyStatus));
        }

        if (StringUtils.isNotEmpty(profileErrorMsg)) {
            hashValue.put("profile_error_msg", profileErrorMsg);
        }
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);
    }


    /**
     * 认证单张图片
     *
     * @param photo
     */
    public AccountsPhoto compareSimplePhoto(AccountsPhoto photo) {
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(photo.getAccount_uuid());
        String accountUuid = photo.getAccount_uuid();
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"profile_verify_status", "face_certification"}, "1", false, false).get(accountUuid);
        String auditSuccess = String.valueOf(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        Boolean isFaceDetect = auditSuccess.equals(infoMap.get("profile_verify_status")) || auditSuccess.equals(infoMap.get("face_certification"));
        if (null == detect || !isFaceDetect) {
            return photo;
        }
        if (null != photo.getVerify_status() && !UN_FACE_CERTIFICATION.equals(photo.getVerify_status())) {
            return photo;
        }

        String fullPhotoUrl = ToolsService.addPhotoUrlPreDomain(photo.getPhoto_url());
        // 2024.07.04 对比前底图获取是有图片地址去比较
        String privateBasePic = ToolsService.getPrivateBasePic(detect.getBase_photo_url());
        CompareFaceDto dto = comparePic(fullPhotoUrl, privateBasePic, accountUuid, RiskCertificationTypeEnum.UNKNOWN);
        boolean similarity = dto.isSimilarity();
        Float similarityScore = dto.getScore();

        if (similarity) {
            photo.setVerify_status(AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus());
            photo.setError_msg("");
        } else {
            photo.setVerify_status(AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus());
            //默认的检测错误提示
            String errorMsg = CodeStatus.ALIYUN_VERIFY_FALI_MSG.getReasonPhrase();
            if (NO_FACE_ERROR_CODE.equals(dto.getCode())) {
                errorMsg = CodeStatus.ALIYUN_NON_FACE_DETECT.getReasonPhrase();
            }
            photo.setError_msg(errorMsg);
        }
        try {
            accountsPhotoService.updateVerifyStatusById(photo.getId(), photo.getVerify_status(), photo.getError_msg());
        } catch (Exception e) {
            LOGGER.warn("图片审核后，更新活体认证图片状态失败,用户uuid={},图片id={}", photo.getAccount_uuid(), photo.getId(), e);
        }

        saveAliyunFacePhotoCompareLog(photo, detect.getBase_photo_url(), similarityScore, true);
        //更新到用户相册缓存
        accountsPhotoService.updatePhotoCache(photo);
        // 判断用户是否无真人认证图片
        accountsService.checkAccountEmptyFacePhoto(accountUuid);
        return photo;
    }


    /**
     * 客户端引导认证时进行图片对比
     *
     * @param accountUuid 用户uuid
     * @param photoUrl    图片url
     * @return
     */
    public Map<String, Object> getPhotoCompareResult(String accountUuid, String photoUrl) {
        Map<String, Object> result = Maps.newHashMap();
        int verifyStatus = 0;
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        Float similarityScore = -1F;
        if (null != detect) {
            similarityScore = 0.0f;

            // 2024.07.04 对比前底图获取是有图片地址去比较
            String privateBasePic = ToolsService.getPrivateBasePic(detect.getBase_photo_url());

            CompareFaceDto dto = comparePic(photoUrl, privateBasePic, accountUuid, RiskCertificationTypeEnum.UNKNOWN);
//
            verifyStatus = dto.isSimilarity() ? 1 : 0;
            similarityScore = dto.getScore();
        }

        result.put("verify_status", String.valueOf(verifyStatus));
        result.put("score", String.valueOf(similarityScore));
        return result;
    }


    /**
     * 批量认证审核通过的图片
     *
     * @param photoList
     */
    @Async
    public void batchVerifyFacePhoto(List<AccountsPhoto> photoList) {
        if (CollectionUtils.isEmpty(photoList)) {
            return;
        }
        photoList.stream().forEach(photo -> {
            compareSimplePhoto(photo);
        });
        Map<String, Long> map = Maps.newHashMap();
        for (AccountsPhoto photo : photoList) {
            if (AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(photo.getVerify_status())) {
                LOGGER.info("用户图片认证成功,推入队列,用户uuid={},图片id={}", photo.getAccount_uuid(), photo.getId());
                Long count = map.get(photo.getAccount_uuid());
                if (null == count) {
                    map.put(photo.getAccount_uuid(), 1L);
                } else {
                    map.put(photo.getAccount_uuid(), count + 1);
                }
            }
        }
       /* Map<String,Long> map = photoList.stream().filter(s->AliyunLiveFaceDetectStatus.AUTHORIZED.equals(s.getVerify_status())).collect(
                Collectors.groupingBy(AccountsPhoto::getAccount_uuid, Collectors.counting()));*/
        map.forEach((uuid, count) -> {
            Long successCount = accountsPhotoService.getCountByVerifyStatus(uuid, AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus());
            pushVerifySuccessPhotoCountToBbs(uuid, successCount + count);
        });

    }

    /**
     * 获取用户相册认证状态
     *
     * @param accountUuid
     * @return
     */
    public Map<String, Object> getPhotoInfoByAccountUuid(String accountUuid) {
        Map<String, Object> result = Maps.newHashMap();
        if (StringUtils.isEmpty(accountUuid)) {
            return result;
        }
        String aliyunFaceDetectLockKey = ALIYUN_FACE_DETECT_LOCK_REDIS_KEY.replace("${accountUuid}", accountUuid);
        //如果图片还在自动比对中，返回在比对的状态
        boolean isLock = false;
        String isLockStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceDetectLockKey);
        if (StringUtils.isNotEmpty(isLockStr)) {
            isLock = true;
        }
        Map<String, Map<String, Object>> accountInfo = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"pre_profile_verify_status", "pre_profile_error_msg", "profile_verify_status", "profile_error_msg", "face_certification", "avatar_status"}, "1", false, false);
        Map<String, Object> infoMap = accountInfo.get(accountUuid);
        Integer profileVerifyStatus = MapUtils.getInteger(infoMap, "profile_verify_status");
        Integer aliyunFaceCertification = MapUtils.getInteger(infoMap, "face_certification", 0);
        String profileErrorMsg = MapUtils.getString(infoMap, "profile_error_msg", "");
        if (null == profileVerifyStatus) {
            profileVerifyStatus = 0;
        }
        Map<String, List<Map<String, String>>> accountCoversMap = accountsInfoService.mgetAccountsCover(new String[]{accountUuid}, new boolean[]{false});
        List<Map<String, String>> list = accountCoversMap.get(accountUuid);
        List<Map<String, Object>> imageList = Lists.newArrayList();
        for (Map<String, String> map : list) {
            Map<String, Object> photoMap = Maps.newHashMap();
            photoMap.put("id", map.get("id"));
            photoMap.put("pic_url", map.get("pic_url"));
            Integer verifyStatus = MapUtils.getInteger(map, "verify_status", 0);
            photoMap.put("verify_status", verifyStatus);
            photoMap.put("error_msg", MapUtils.getString(map, "error_msg"));
            //防止缓存error_msg为空的情况，下发默认的信息
            if (AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus().equals(verifyStatus) && StringUtils.isEmpty(MapUtils.getString(map, "error_msg"))) {
                photoMap.put("error_msg", CodeStatus.ALIYUN_VERIFY_FALI_MSG.getReasonPhrase());
            }

            if (isLock) {
                photoMap.put("verify_status", AliyunLiveFaceDetectStatus.AUTHORIZING.getStatus());
            }
            imageList.add(photoMap);
        }
        result.put("img_list", imageList);
        result.put("face_certification", aliyunFaceCertification);
        Integer preProfileVerifyStatus = MapUtils.getInteger(infoMap, "pre_profile_verify_status");
        String preProfileErrorMsg = MapUtils.getString(infoMap, "pre_profile_error_msg", "");
        Integer avatarStatus = MapUtils.getInteger(infoMap, "avatar_status");
        LOGGER.info("getPhotoInfoByAccountUuid接口获取真人认证信息,用户uuid={},preProfileVerifyStatus={},profileVerifyStatus={}", accountUuid, preProfileVerifyStatus, profileVerifyStatus);
        if (null != preProfileVerifyStatus && null != avatarStatus && avatarStatus == 0) {
            profileVerifyStatus = preProfileVerifyStatus;
            profileErrorMsg = preProfileErrorMsg;
        }
        result.put("profile_error_msg", profileErrorMsg);
        result.put("profile_verify_status", profileVerifyStatus);
        return result;
    }

    /**
     * 根据分数获取是否认证成功接口
     *
     * @param similarityScore
     * @return
     */
    public Integer isVerifySuccess(Double similarityScore) {
        Integer verifyResult = AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus();
        if (UN_FACE_VERIFY_DOUBLE.equals(similarityScore)) {
            return verifyResult;
        }
        if (null == similarityScore) {
            return verifyResult;
        }
        float similarityScoreFloat = Float.parseFloat(String.valueOf(similarityScore));
        if (similarityScoreFloat >= getSimilarityScoreLimit()) {
            verifyResult = AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus();
        } else {
            verifyResult = AliyunLiveFaceDetectStatus.AUTHORIZED_FAIL.getStatus();
        }
        return verifyResult;
    }

    /**
     * 批量存储对比认证的图片
     *
     * @param accountUuid
     * @param photoList
     * @param similarityScoreMap
     */
    @Async
    public void batchSave(String accountUuid, List<AccountsPhoto> photoList, Map<String, Float> similarityScoreMap) {
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        if (null == detect) {
            return;
        }
        long totalVerifySuccessCount = 0;
        for (AccountsPhoto photo : photoList) {
            if (null != similarityScoreMap.get(photo.getPhoto_url())) {
                saveAliyunFacePhotoCompareLog(photo, detect.getBase_photo_url(), similarityScoreMap.get(photo.getPhoto_url()), true);
            }
            if (AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(photo.getVerify_status())) {
                totalVerifySuccessCount++;
            }
        }

        AccountsPhoto accountsPhoto = accountsPhotoService.findAvatarByUuid(accountUuid);
        if (null != accountsPhoto && AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus().equals(accountsPhoto.getVerify_status())) {
            totalVerifySuccessCount++;
        }
        pushVerifySuccessPhotoCountToBbs(accountUuid, totalVerifySuccessCount);

    }

    @Async
    public void singleSave(AccountsPhoto accountsPhoto, Double similarityScore) {
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountsPhoto.getAccount_uuid());
        if (null == detect) {
            return;
        }
        float scoreFloat = Float.parseFloat(String.valueOf(similarityScore));
        saveAliyunFacePhotoCompareLog(accountsPhoto, detect.getBase_photo_url(), scoreFloat, true);
    }

    public List<AliyunLiveFaceDetect> getByPage(Integer offset, Integer limit) {
        return aliyunLiveFaceDetectDao.getByPage(offset, limit);
    }

    /**
     * 图片是否包含人脸
     *
     * @param accountUuid
     * @param photoUrl
     * @return
     */
    public Boolean picIsFace(String accountUuid, String photoUrl) {
        return detectIsFaceTengxun(accountUuid, photoUrl);
    }

    /**
     * 对比两张图的相似度
     *
     * @param picOne
     * @param picTwo
     * @return
     */
    public CompareFaceDto comparePic(String picOne, String picTwo, String uuid, RiskCertificationTypeEnum certType) {
        return compareFace(picOne, picTwo, uuid, certType);
    }

    /**
     * 获取相似分限值
     *
     * @return
     */
    private Float getSimilarityScoreLimit() {
        return TENGXUNYUN_SIMILARITY_SCORE;
    }


    /**
     * 移除图片前缀
     *
     * @param photoUrl
     * @return
     */
    public static String removePhotoUrlPreDomain(String photoUrl) {
        return AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
    }

    /**
     * 获取活体比对结果
     *
     * @param accountUuid
     * @param version 0-旧版本，1-2024.07.10新版本，返回私有
     * @return
     */
    public Map<String, String> compareAndCheckFaceDetect(String accountUuid, Integer version) {
        Map<String, String> result = new HashMap<>();
        String compareStatusKey = "compare_status";
        String compareMsgKey = "compare_msg";
        String avatarUrlKey = "avatar_url";
        String liveFaceUrlKey = "live_face_url";

        // 获取 用户头像
        AccountsPhoto accountAvatar = accountsPhotoService.getAccountAvatar(accountUuid, null, false);
        if (accountAvatar == null) {
            throw new ServiceException(CodeStatus.NEED_AVATAR);
        }
        // 底图
        AliyunLiveFaceDetect liveFaceDetect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        if (liveFaceDetect == null) {
            // 未进行活体
            throw new ServiceException(CodeStatus.NOT_LIVE_FACE_DETECT);
        }

        String avatarUrl = ToolsService.addPhotoUrlPreDomain(accountAvatar.getPhoto_url());
        result.put(avatarUrlKey, accountAvatar.getPhoto_url());

        String liveFaceUrl = "";
        String liveFaceUrlCompare = "";
        if(Objects.equals(version, 0)){
            // 传给风控，带域名
            liveFaceUrl = ToolsService.addPhotoUrlPreDomain(liveFaceDetect.getBase_photo_url());
            // 给客户端，旧版本不带域名
            result.put(liveFaceUrlKey, liveFaceDetect.getBase_photo_url());
            // 对比用，需要可访问
            liveFaceUrlCompare = liveFaceUrl;
        }else {
            // 传给风控，带域名，无token
            liveFaceUrl = ToolsService.addAccountPrivateUrlPreDomain(liveFaceDetect.getBase_photo_url());
            // 给客户端，新版本，带域名+token
            result.put(liveFaceUrlKey, ToolsService.getPrivateBasePic(liveFaceDetect.getBase_photo_url()));
            // 对比用，需要可访问，带域名+token
            liveFaceUrlCompare = result.get(liveFaceUrlKey);
        }

        if (accountsInfoService.isRealPersonCertification(accountUuid)) {
            result.put(compareStatusKey, "1");
//            result.put(compareMsgKey, "您已完成真人认证");
            // 2024.06.20 此为旧版真人认证通过返回文案，若能再次进入该页面，说明用户头像非真人，可能处于审核中，遂直接返回审核文案
            result.put(compareMsgKey, "AI智能审核通过，请耐心等待人工复审结果");
            return result;
        }

        // 2024.09.20 此处可能出现用户操作与审核并发执行情况，可能出现数据错乱
        try {
            // 1.活体底图和头像比对
            // 增加对比底图和认证图是否相似
            // 2024.07.04 对比前底图获取是有图片地址去比较
            CompareFaceDto comparePic = comparePic(avatarUrl, liveFaceUrlCompare, accountUuid, RiskCertificationTypeEnum.REAL_PEOPLE);
            boolean similarity = comparePic.isSimilarity();

            if (similarity) {
                // 1.3 比对成功 保存结果 推队列
                AccountsPhoto photo = accountsPhotoService.getAccountAvatar(accountUuid, null, false);
                if (photo != null && !AccountsPhotoService.PHOTO_UNCHECK_STATUS_ARR.contains(photo.getStatus())) {
                    // 图片状态变更为审核中
                    photo.setStatus(AccountsPhoto.Status.NO_CHECK.getValue());
                    accountsPhotoService.merge(photo);
                    // 移除j61审核队列
                    String content = PhotoOrignCheckEnum.NOT_REAL_PERSON_AVATAR.getValue() + "|" + photo.getId() + "|" + AvatarHandleService.getAvatarOfSavePhoto(photo.getPhoto_url());
                    String pushReviewMd5 = Md5Util.encryptSHA1(content);
                    accountStringRedisTemplate.delete(RedisKeyConstant.ACCOUNT_PHOTO_PUSH_REVIEW.setArg(pushReviewMd5));
                    LOGGER.info("移除审核uuid={}.id={}.url={}.content={}.key={}", photo.getAccount_uuid(), photo.getPhoto_url(), content, pushReviewMd5);
                    processResponseV4(accountUuid, accountAvatar.getPhoto_url());
                }

                // 设置真人认证审核状态
                accountsPhotoService.setRealPersonCertificationStatusCache(accountUuid, RealPersonCertificationStatusEnum.AUDITING);
                // 埋点上报触发真人认证动作
                Map<String, Object> payload = new HashMap<>(2);
                payload.put("position", "认证");
                eventTrackReporter.report(accountUuid, "my_info_fillout_new", payload);

                // 推送到图片审核
                accountsService.checkAccountEmptyFacePhoto(accountUuid);

                accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "avatar_same_with_face", "1");

                // 真人认证事件埋点
                EventTrackingDO tracking = EventTrackingDO.create(accountUuid, AccountEvent.REAL_PERSON_CERT);
                kafkaSinkUtil.push(EventConst.EVENT_TRACKING_TOPIC, tracking);
                behaviorReporter.report(accountUuid, tracking);

                userCertificationLogService.logRealPhotoSuccess(accountUuid, avatarUrl, liveFaceUrl, comparePic.getScore());

//                BuryService.pushToAccountLivingCert(accountUuid);
                AliyunFacePhotoCompareLog log = new AliyunFacePhotoCompareLog();
                log.setVerify_photo_url(accountAvatar.getPhoto_url());
                log.setBase_photo_url(liveFaceDetect.getBase_photo_url());
                if (comparePic != null && comparePic.getScore() != null) {
                    Float similarityScore = comparePic.getScore();
                    if (!AliyunLiveFaceDetectService.UN_FACE_VERIFY.equals(similarityScore) && similarityScore >= 0f) {
                        if (similarityScore >= AliyunLiveFaceDetectService.ALIYUN_SIMILARITY_SCORE) {
                            log.setStatus(AliyunLiveFaceDetectStatus.AUTHORIZED.getStatus());
                        } else {
                            log.setStatus(AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus());
                        }
                        log.setSimilarity_score(similarityScore);
                    }
                } else {
                    log.setSimilarity_score(0f);
                    log.setStatus(AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus());
                }
                log.setError_msg("");
                log.setAccounts_photo_id(accountAvatar.getId());
                log.setCreate_time(DateUtil.currentTimeSeconds());
                log.setAccount_uuid(accountUuid);
                log.setGender_compare_result(-1);
                log.setBase_photo_gender(-1);
                log.setGender_compare_result(-1);
                aliyunFacePhotoCompareLogService.merge(log);

                result.put(compareStatusKey, "1");
                result.put(compareMsgKey, "AI智能审核通过，请耐心等待人工复审结果");
                // 2024.05.24 后期可以去掉
                accountsPhotoService.pushMsgToActivityKafka(accountUuid,CommConst.MP_TAQU_EVENT_NOTIFY, CommConst.REAL_PERSON_MACHINE_VERIFY);
                // 2024.05.24 新增推入业务侧
                buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.NEW_REAL_AVATAR_CONFIRMED.getType());
            } else if (NoHumanFace.equals(comparePic.getCode())) {
                // 1.2 如果头像没有人脸
                //    com.tencentcloudapi.common.exception.TencentCloudSDKException: 图片中没有人脸。
                result.put(compareStatusKey, "3");
                result.put(compareMsgKey, "当前头像未符合条件要求，请重新编辑头像");
                accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "avatar_same_with_face", "0");
                //认证失败时的处理逻辑
                applicationContext.getBean(AliyunLiveFaceDetectService.class).processFailResponseV3(accountUuid, accountAvatar.getPhoto_url(), liveFaceDetect.getBase_photo_url(), comparePic.getScore());
            } else {
                // 1.1 如果头像 与 活体底图不一致
                result.put(compareStatusKey, "2");
                result.put(compareMsgKey, "当前头像与人脸扫描结果非同一个人，请重新编辑头像");
                accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "avatar_same_with_face", "0");
                //认证失败时的处理逻辑
                applicationContext.getBean(AliyunLiveFaceDetectService.class).processFailResponseV3(accountUuid, accountAvatar.getPhoto_url(), liveFaceDetect.getBase_photo_url(), comparePic.getScore());
            }
        } catch (Exception e) {
            LOGGER.error("人脸认证过程出现异常,用户uuid={}", accountUuid, e);
            throw new ServiceException(CodeStatus.VERIFY_FALI_MSG);
        }
        return result;
    }

    /**
     * 获取真人认证状态
     *
     * @param accountUuid
     * @return
     */
    public Map<String, String> getRealPersonCertification(String accountUuid) {
        Boolean realPersonCertificationStatusSuccess = isRealPersonCertificationStatusSuccess(accountUuid);
        Map<String, String> result = new HashMap<>();
        result.put("real_person_certification", realPersonCertificationStatusSuccess ? "1" : "0");
        return result;
    }

    /**
     * 获取用户认证状态： 真人+实名
     *
     * @param accountUuid
     * @return
     */
    public Map<String, String> getAccountCertification(String accountUuid) {
        Boolean zhimaCertification = accountsInfoService.isZhimaCertification(accountUuid);
        Boolean faceCertification = accountsInfoService.isRealPersonCertification(accountUuid);
        Map<String, String> result = new HashMap<>();
        result.put("zhima_certification", zhimaCertification ? "1" : "0");
        result.put("face_certification", faceCertification ? "1" : "0");
        return result;
    }

    /**
     * 图片质量检测
     *
     * @param photoUrl
     * @return
     */
    public TencentImgQualityDTO getTencentImgQualityResult(String accountUuid, String photoUrl) {
        Boolean callResult = Boolean.TRUE;
        MonitorService.incCounterMetrices(PrometheusMetricsEnum.TENCENT_IMG_QUALITY_COUNTER);
        Histogram.Timer timer = MonitorService.getHistogramTimer(PrometheusMetricsEnum.TENCENT_IMG_QUALITY_RT_HISTOGRAM);
        try {
            // 实例化一个请求对象,每个接口都会对应一个request对象
            AssessQualityRequest req = new AssessQualityRequest();
            req.setImageUrl(photoUrl);
            // 返回的resp是一个AssessQualityResponse的实例，与请求对象对应
            AssessQualityResponse resp = null;
            resp = tiiaclient.AssessQuality(req);
            String response = AssessQualityResponse.toJsonString(resp);
            TencentImgQualityDTO imgQualityDTO = JSON.parseObject(response, TencentImgQualityDTO.class);
            if (imgQualityDTO.getClarityScore() == null) {
                LOGGER.warn("图片质量检测.失败.uuid={}.url={}.resp={}", accountUuid, photoUrl, response);
                return null;
            } else {
                LOGGER.info("图片质量检测.成功.uuid={}.url={}.resp={}", accountUuid, photoUrl, response);
            }
            // 输出json格式的字符串回包
            return imgQualityDTO;
        } catch (Exception e) {
            if (e instanceof TencentCloudSDKException) {
                callResult = Boolean.FALSE;
                TencentCloudSDKException tce = (TencentCloudSDKException) e;
                LOGGER.warn("图片质量检测失败.uuid={}.photoUrl={}.error={}.", accountUuid, photoUrl, tce.toString(), tce);
            } else {
                LOGGER.warn("图片质量检测失败.uuid={}.photoUrl={}", accountUuid, photoUrl, e);
            }
            MonitorService.incCounterMetrices(PrometheusMetricsEnum.TENCENT_IMG_QUALITY_FAIL_COUNTER);
            throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
        } finally {
            if (timer != null) {
                timer.observeDuration();
            }
            SpringContextHolder.getBean(AccountManufacturerCallProducer.class).send(
                    KafkaManufacturerCallDto.tencentPicAnalysis(
                            accountUuid,
                            KafkaManufacturerCallDto.BelongBiz.UNDEFINED,
                            "",
                            callResult.toString())
            );
        }
    }

    /**
     * 获取活体底图
     *
     * @param accountUuid
     * @return
     */
    public String getBasePhotoUrlByAccountUuid(String accountUuid) {
        String basePhotoUrl = "";
        if (StringUtils.isBlank(accountUuid)) {
            return basePhotoUrl;
        }
        basePhotoUrl = aliyunLiveFaceDetectDao.getBasePhotoUrlByAccountUuid(accountUuid);
        if (StringUtils.isNotBlank(basePhotoUrl)) {
            basePhotoUrl = AvatarHandleService.getAvatarSrcPhotoUrl(basePhotoUrl);
        }
        return basePhotoUrl;
    }

    /**
     * 获取活体底图（从私有空间）
     *
     * @param accountUuid
     * @return
     */
    public String getBasePhotoUrlPrivateByAccountUuid(String accountUuid) {
        String basePhotoUrl = "";
        if (StringUtils.isBlank(accountUuid)) {
            return basePhotoUrl;
        }
        basePhotoUrl = aliyunLiveFaceDetectDao.getBasePhotoUrlByAccountUuid(accountUuid);
        if (StringUtils.isNotBlank(basePhotoUrl)) {
            basePhotoUrl = ToolsService.getPrivateBasePic(basePhotoUrl);
        }
        return basePhotoUrl;
    }

    /**
     * 获取活体底图
     *
     * @param accountUuidArr
     * @return
     */
    @Deprecated
    public Map<String, String> listBasePhotoUrlByAccountUuid(String[] accountUuidArr) {
        Map<String, String> map = Maps.newHashMap();
        for (String accountUuid : accountUuidArr) {
            String basePhotoUrl = getBasePhotoUrlByAccountUuid(accountUuid);
            map.put(accountUuid, basePhotoUrl);
        }
        return map;
    }

    /**
     * 解除真人认证
     *
     * @param accountUuid
     */
    public void cancelRealPersonCertification(String accountUuid, Integer certWhiteList, String operator) {
        AliyunLiveFaceDetect liveFaceDetect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        if (liveFaceDetect == null) {
            LOGGER.warn("取消真人认证失败.未查询到活体数据.uuid={}.", accountUuid);
            return;
        }
        // 1、数据库 - 相册
        accountsPhotoService.updateVerifyStatusAndStatusByUuid(accountUuid, AliyunLiveFaceDetectStatus.UNAUTHORIZED.getStatus(), "取消真人认证");

        // 2、缓存 - 用户、相册
        Map<String, String> cacheInfo = new HashMap<>();
        cacheInfo.put("real_person_certification", "0");
        // TODO: 2023/2/17 real_person_certification store to db
        cacheInfo.put("profile_verify_status", "0");
        cacheInfo.put("pre_profile_verify_status", "0");
        cacheInfo.put("face_certification", "0");
        cacheInfo.put("avatar_same_with_face", "0");
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), cacheInfo);

        accountsPhotoService.mGetAccountsPhotoAllByDB(new String[]{accountUuid}, new boolean[]{false});
        // 推送日志
        String remark = TemplateStringConst.getContent(TemplateStringConst.CERT_LOG_REMARK_REAL_PERSON_CANCEL, accountUuid, DateUtil.dateToString20(new Date()));
        BuryService.pushToCertLog(accountUuid, certWhiteList, CertTypeEnum.REAL_PERSON.getValue(), operator, AccountsCertLog.OperatorTypeEnum.CANCEL.getValue(), liveFaceDetect.getBase_photo_url(), remark);

        accountsService.checkAccountEmptyFacePhoto(accountUuid);
    }

    /**
     * @param accountUuid
     * @param version 版本，0-默认；1-增加判断比对不一致
     */
    public void precheckLiveFaceDetect(String accountUuid, Integer version) {
        AccountsPhoto accountAvatar = accountsPhotoService.getAccountAvatar(accountUuid, null, false);
        if (accountAvatar == null) {
            // 不正常
            LOGGER.warn("用户无头像，不正常，accountUuid={}", accountUuid);
            throw new ServiceException(CodeStatus.NEED_UPLOAD_AVATAR);
        }else if(StringUtils.isBlank(accountAvatar.getPhoto_url()) ) {
            // 不正常
            LOGGER.warn("用户头像地址为空，accountUuid={}", accountUuid);
            throw new ServiceException(CodeStatus.NEED_UPLOAD_AVATAR);
        }else if(AvatarHandleService.isDefAvatar(accountAvatar.getPhoto_url())) {
            // 正常业务
            throw new ServiceException(CodeStatus.NEED_UPLOAD_AVATAR);
        }

        // 2023.01.10 新增判断活体比对不一致  https://o15vj1m4ie.feishu.cn/wiki/CP87wW3q1iTjydkPkQncXWMbn1e
        if(version >= 1) {
            Boolean isAvatarSameWithFace = accountsInfoService.isAvatarSameWithFace(accountUuid);
            if(!isAvatarSameWithFace) {
                // 正常业务
                throw new ServiceException(CodeStatus.NEED_UPLOAD_AVATAR);
            }
        }

    }


    /**
     * 白名人脸结果校验
     * @param accountUuid
     * @param source
     * @return
     */
    public Map<String, Object> checkWhitelistFaceDetectResult(String accountUuid, String basePhotoUrlNoUsed, String source){
        LOGGER.info("checkWhitelistFaceDetectResult, uuid={}, basePhotoUrlNoUsed={}", accountUuid, basePhotoUrlNoUsed);
        if (StringUtils.isEmpty(accountUuid)) {
            return Maps.newHashMap();
        }
        String orderno=getOrderno(accountUuid);
        TxGetLiveResultResponse txGetLiveResultResponse=serverGetLiveResult(accountUuid, orderno);
        String code = getTxGetLiveResultResponseCode(txGetLiveResultResponse);
        String imgName = getImgName(txGetLiveResultResponse);
        if(StringUtils.isBlank(imgName)) {
            // 为空说明sdk调用失败，只增加次数
            addFaceVerifyTimes(accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY, KafkaManufacturerCallDto.BelongBiz.REAL_PERSON);
            return getResultMap(CodeStatus.VERIFY_FALI_SDK_ERROR_MSG.getReasonPhrase(),"0","0");
        }
        try{
            // 需要通过域名判断拼接
            String verifyPhotoUrl = getVerifyPhotoUrl(accountUuid);
            if(!"0".equals(code)){
                LOGGER.warn("Whitelist腾讯人脸活体认证失败,status={},用户uuid={}", 0, accountUuid);
                afterFaceVerify(accountUuid, imgName, verifyPhotoUrl, LiveFaceDetectStatusEnum.FAILURE, orderno,
                        LiveFaceDetectResultStatusEnum.FAILURE, LiveFaceDetectResultTypeEnum.LIVE_FACE_DETECT_API_FAILURE,0);
                return getResultMap(CodeStatus.UNBIND_VERIFY_FALI_MSG.getReasonPhrase(),"0","1");
            }
            LOGGER.info("Whitelist获取活体人脸验证结果,用户uuid={},检测结果={}", accountUuid, code);
            //业务级实名认证校验
            checkChatCertification(accountUuid, imgName);

            // 2024.07.04 对比前底图获取是有图片地址去比较
            String privateBasePic = ToolsService.getPrivateBasePic(imgName);

            // 此处需要得到能访问的地址
            String verifyPhotoUrlReal = ToolsService.getCanUsedUrlByHost(verifyPhotoUrl);
            CompareFaceDto facePicPCompare = comparePic(verifyPhotoUrlReal, privateBasePic, accountUuid, RiskCertificationTypeEnum.UNKNOWN);
            // 保存到日志
            boolean result = facePicPCompare.isSimilarity();
            photoCompareLogService.addLog(accountUuid, imgName, verifyPhotoUrl, result ? 1 : 0, facePicPCompare.getScore(), facePicPCompare.getMsg(), PhotoCompareTypeEnum.FACE_DETECT.getValue());
            if(!result){
                LOGGER.warn("comparePic fail uuid;{}|imgName:{}|verifyPhotoUrl:{}", accountUuid,imgName,verifyPhotoUrl);
                afterFaceVerify(accountUuid, imgName, verifyPhotoUrl, LiveFaceDetectStatusEnum.FAILURE, orderno,
                        LiveFaceDetectResultStatusEnum.FAILURE, LiveFaceDetectResultTypeEnum.LIVE_FACE_DETECT_API_FAILURE,0);
                return getResultMap(CodeStatus.UNBIND_VERIFY_FALI_MSG.getReasonPhrase(),"0","1");
            }
            afterFaceVerify(accountUuid, imgName, verifyPhotoUrl, LiveFaceDetectStatusEnum.SUCCESS, orderno,
                    LiveFaceDetectResultStatusEnum.SUCCESS, LiveFaceDetectResultTypeEnum.DEFAULT, 1);
            return getResultMap(CodeStatus.VERIFY_SUCCESS_MSG.getReasonPhrase(),"1","0");
        }catch (ServiceException se){
            throw se;
        }catch (Exception e){
            throw new ServiceException(CodeStatus.UNBIND_VERIFY_FALI_MSG);
        }
    }

    private void addLiveFaceDetectLog(String accountUuid, String basePhotoUrl, String verifyPhotoUrl, LiveFaceDetectStatusEnum liveFaceDetectStatus, String orderno,
                                      LiveFaceDetectResultStatusEnum liveFaceDetectResultStatus, LiveFaceDetectResultTypeEnum liveFaceDetectResultType){
        LiveFaceDetectTypeEnum liveFaceDetectType = LiveFaceDetectTypeEnum.TENCENT;
        liveFaceDetectLogService.addLiveFaceDetectLog(accountUuid, basePhotoUrl, verifyPhotoUrl, liveFaceDetectType.getValue(), liveFaceDetectStatus.getValue(), orderno,
                liveFaceDetectResultStatus.getValue(), liveFaceDetectResultType.getValue(), "");
    }

    private String getOrderno(String accountUuid){
        String aliyunBizIdKey = ALI_BIZ_ID_KEY.replace("${accountUuid}", accountUuid);
        String orderno = accountStringRedisTemplate.opsForValue().get(aliyunBizIdKey);
        return orderno;
    }

    private String getVerifyPhotoUrl(String accountUuid){
        //异步保存图片和返回的信息
        String aliyunVerifyPhotoUrlKey = ALI_VERIFY_PHOTO_URL_KEY.replace("${accountUuid}", accountUuid);
        String verifyPhotoUrl = accountStringRedisTemplate.opsForValue().get(aliyunVerifyPhotoUrlKey);
        return verifyPhotoUrl;
    }

//    @Deprecated
//    private void setAliyunFaceDetectLock(String accountUuid){
//        String aliyunFaceDetectLockKey = ALIYUN_FACE_DETECT_LOCK_REDIS_KEY.replace("${accountUuid}", accountUuid);
//        //认证过程加锁，获取图片的时候需要用到
//        memberStringRedisTemplate.opsForValue().set(aliyunFaceDetectLockKey, aliyunFaceDetectLockKey, 2, TimeUnit.MINUTES);
//    }

    private void afterFaceVerify(String accountUuid, String basePhotoUrl, String verifyPhotoUrl, LiveFaceDetectStatusEnum liveFaceDetectStatus, String orderno,
                                 LiveFaceDetectResultStatusEnum liveFaceDetectResultStatus, LiveFaceDetectResultTypeEnum liveFaceDetectResultType, Integer verifyStatus){
        handlerAfterFaceVerify(accountUuid, verifyStatus);
        addLiveFaceDetectLog(accountUuid, basePhotoUrl, verifyPhotoUrl, liveFaceDetectStatus, orderno,
                liveFaceDetectResultStatus, liveFaceDetectResultType);
    }

    private TxGetLiveResultResponse serverGetLiveResult(String accountUuid,String orderno){
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();
        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        TengxunyunApi tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
        String appid = WBappid.getAppid();

        DescribeVerifyResultRequest verifyResultRequest = new DescribeVerifyResultRequest();
        verifyResultRequest.setRegionId(regionId);
        verifyResultRequest.setSysProtocol(ProtocolType.HTTPS);
        if (StringUtils.isEmpty(orderno)) {
            //可能是客户端请求多次，导致bizId之前已经被用了，这边直接提示用户
            throw new ServiceException(CodeStatus.UNBIND_VERIFY_FALI_MSG);
        }

        String nonce = generateBizId2();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderno);
        values.add("1.0.0");
        values.add(nonce);
        String sign = TengxunyunApiClient.sign(values, tengxunyunVoucher.getSignTicket());

        String serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce, orderno, sign);
        LOGGER.info("腾讯活体serverGetLiveResultV2【{}】", serverGetLiveResult);

        TxGetLiveResultResponse txGetLiveResultResponse =
                JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<TxGetLiveResultResponse>() {
                });
        return txGetLiveResultResponse;
    }

    private String getTxGetLiveResultResponseCode(TxGetLiveResultResponse txGetLiveResultResponse){
        if(txGetLiveResultResponse==null){
            return null;
        }
        return txGetLiveResultResponse.getCode();
    }

    private String getImgName(TxGetLiveResultResponse txGetLiveResultResponse){
        String imgName = "";
        if(txGetLiveResultResponse==null || txGetLiveResultResponse.getResult()==null){
            return imgName;
        }
        // 活体检测时的照片，Base 64 位编码
        String photo=txGetLiveResultResponse.getResult().getPhoto();
        if(StringUtils.isBlank(photo)){
            return imgName;
        }
        /*
         *  2023.11.20 由于腾讯云新版客户端SDK，不会返回给客户端图片。
         *  如果basePhotoUrl没值，可能是客户端SDK问题，也可能是新版SDK
         *
         *  1. 先校验，如果basePhotoUrl为空，则获取腾讯云返回图片
         *  2. 校验图片
         */
        PictureInfoVo uploadFile = qiniuService.uploadFileLiveFace(Encodes.decodeBase64(photo));
        imgName = uploadFile.getImg_name();
        LOGGER.info("checkFaceDetectResult 获取底图 imgName:{}",imgName);
        return imgName;
    }

    private Map<String, Object> getResultMap(String verifyMsg,String veryfyStatus,String changePhoto){
        Map<String, Object> resultMap=Maps.newHashMap();
        resultMap.put("verify_msg", verifyMsg);
        resultMap.put("veryfy_status", veryfyStatus);
        // 客户端是否需要换图片 1更换 0不更换
        resultMap.put("change_photo", changePhoto);
        return resultMap;
    }

    private void checkChatCertification(String accountUuid, String basePhotoUrl){
        // 判断是否已经业务级实名认证
        Object rpcObj = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "chat_real_certification");
        boolean isChatCertification = (null != rpcObj && Objects.equals(CommonEnableStatus.ENABLE.getStatus(), Integer.parseInt(rpcObj + "")));
        if (isChatCertification) {
            AccountsChatCertification chatCertification = accountsChatCertificationDao.getByAccountUuid(accountUuid);
            String pic1 = ToolsService.addAccountPrivateUrlPreDomain(null == chatCertification ? "" : chatCertification.getCertificationPhotoUrl());
            String pic2 = ToolsService.addAccountPrivateUrlPreDomain(basePhotoUrl);
            LOGGER.info("Whitelist faceDetect=[{}]真人与业务级实名对比图，pic1=[{}],pic2=[{}]", null == chatCertification ? "null" : chatCertification.getId(), pic1, pic2);
            if (null != chatCertification && StringUtils.isNotBlank(pic1) && StringUtils.isNotBlank(pic2)) {
                // 2022/6/13 compare with chat real certification photo
                // 2024.07.04 对比前底图获取是有图片地址去比较
                String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
                String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);
                CompareFaceDto facePicPCompare = comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.UNKNOWN);
                // 保存到日志
                boolean result = facePicPCompare.isSimilarity();
                photoCompareLogService.addLog(accountUuid, pic1, pic2, result ? 1 : 0, facePicPCompare.getScore(), facePicPCompare.getMsg(), PhotoCompareTypeEnum.FACE_DETECT.getValue());

                if (!result) {
                    ShowDialogUtil.throwShowDialog(CopyWritingConst.REAL_PERSON_AVATAR_DIFFERENT_INFO, "知道了", "", "", "");
                }
            }
        }
    }

    public Map<String, Object> initWhitelistFaceDetectConf(String accountUuid) {
        if (StringUtils.isEmpty(accountUuid)) {
            return Maps.newHashMap();
        }
        //todo 取B帐号底图
        String basePhotoUrl=getBasePhotoUrlByUuid(accountUuid);
        if(StringUtils.isBlank(basePhotoUrl)){
            throw new ServiceException(CodeStatus.NEED_BASE_PHOTO);
        }
        // 接口调用限制 腾讯云限制该接口每个用户 QPS 1， 这边加一个2秒限制
        String initCerifyLimit = FACE_VERIFY_INIT_CERTIFY_LIMIT_KEY.replace("${accountUuid}", accountUuid);
        boolean lock = redisLockUtil.lock(initCerifyLimit, 2000);
        if (!lock) {
            throw new ServiceException(CodeStatus.DETECT_TOO_FAST);
        }
        try {
            //1. 接入方服务端发起认证请求，获得认证token，接口文档：https://help.aliyun.com/document_detail/127470.html
            String bizId = generateBizId2();
            String aliyunFaceVerifyLimitKey = ALIYUN_FACE_VERIFY_LIMIT_KEY.replace("${accountUuid}", accountUuid);

            int limit = 0;
            String limitStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceVerifyLimitKey);
            if (StringUtils.isNotBlank(limitStr)) {
                limit = Integer.parseInt(limitStr);
            }
            boolean isLimit = limit > FACE_DETECT_LIMIT_TIME;

            //超过当天的检测次数，不能进行活体检测
            if (isLimit) {
                throw new ServiceException(CodeStatus.DETECT_OVER_TIMES_LIMIT);
            }

            try {
                return getTengxunyunVerifyToken(accountUuid, basePhotoUrl, bizId, KafkaManufacturerCallDto.BelongBiz.REAL_PERSON);
            } catch (ServerException e) {
                LOGGER.error("调用获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.UNBIND_VERIFY_FALI_MSG);
            } catch (ClientException e) {
                LOGGER.warn("调用获取腾讯云活体人脸检测token接口异常, 再次尝试获取, 用户uuid={}", accountUuid, e);
                try {
                    return getTengxunyunVerifyToken(accountUuid, basePhotoUrl, bizId, KafkaManufacturerCallDto.BelongBiz.REAL_PERSON);
                } catch (ServiceException se) {
                    throw se;
                } catch (Exception e1) {
                    LOGGER.error("重试获取腾讯云活体人脸检测token接口异常,用户uuid={}", accountUuid, e);
                    throw new ServiceException(CodeStatus.UNBIND_VERIFY_FALI_MSG);
                }
            } catch (ServiceException se) {
                throw se;
            } catch (Exception e) {
                LOGGER.warn("获取腾讯云活体人脸检测token失败,用户uuid={}", accountUuid, e);
                throw new ServiceException(CodeStatus.UNBIND_VERIFY_FALI_MSG);
            }
        } finally {
            redisLockUtil.unLock(initCerifyLimit);
        }
    }


    private String getBasePhotoUrlByUuid(String accountUuid){
        String basePhotoUrl = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
        // 特殊处理：已实名认证用户无底图，尝试获取真人底图比对
        if (!StringUtils.isBlank(basePhotoUrl)) {
            return ToolsService.addAccountPrivateUrlPreDomain(basePhotoUrl);
        }
        if (accountsInfoService.isFaceCertification(accountUuid)) {
            basePhotoUrl = aliyunLiveFaceDetectDao.getBasePhotoUrlByAccountUuid(accountUuid);
        }
        return ToolsService.addAccountPrivateUrlPreDomain(basePhotoUrl);
    }

    public Map<String, Object> initFaceDetectForRetrieve(String accountUuid) {
        int limit = FACE_DETECT_LIMIT_TIME_RETRIEVE;
        if (LocalConfUtil.getLocalEnv().contains("test")) {
            limit = 300;
        }

        return initLiveFaceDetect("RetrieveAccount", accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY_RETRIEVE_ACCOUNT, limit);
    }

    public void compareDetectFaceForRetrieve(String accountUuid) {
        LOGGER.info("RetrieveAccount, uuid={}", accountUuid);

        // 增加调用次数
        addFaceVerifyTimes(accountUuid, ALIYUN_FACE_VERIFY_LIMIT_KEY_RETRIEVE_ACCOUNT, KafkaManufacturerCallDto.BelongBiz.RETRIEVE_ACCOUNT);

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        TengxunyunApi tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
        String appid = WBappid.getAppid();

        DescribeVerifyResultRequest verifyResultRequest = new DescribeVerifyResultRequest();
        verifyResultRequest.setRegionId(regionId);
        verifyResultRequest.setSysProtocol(ProtocolType.HTTPS);
        String aliyunBizIdKey = ALI_BIZ_ID_KEY.replace("${accountUuid}", accountUuid);
        String orderno = accountStringRedisTemplate.opsForValue().get(aliyunBizIdKey);
        if (StringUtils.isEmpty(orderno)) {
            //可能是客户端请求多次，导致bizId之前已经被用了，这边直接提示用户
            throw new ServiceException(CodeStatus.VERIFY_FALI_MOBILE_MSG_2);
        }

        String nonce = generateBizId2();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderno);
        values.add("1.0.0");
        values.add(nonce);
        String sign = TengxunyunApiClient.sign(values, tengxunyunVoucher.getSignTicket());

        String serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce, orderno, sign);
        LOGGER.info("腾讯活体serverGetLiveResult【{}】", serverGetLiveResult);

        TxGetLiveResultResponse txGetLiveResultResponse =
                JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<TxGetLiveResultResponse>() {
                });
        String code = txGetLiveResultResponse.getCode();

        // 认证成功
        if ("0".equals(code)) {

            /*
             *  2023.11.20 由于腾讯云新版客户端SDK，不会返回给客户端图片。
             *  如果verifyPhotoUrl没值，可能是客户端SDK问题，也可能是新版SDK
             *
             *  1. 先校验，如果verifyPhotoUrl为空，则获取腾讯云返回图片
             *  2. 校验图片
             */
            // 活体检测时的照片，Base 64 位编码
            String photo = txGetLiveResultResponse.getResult().getPhoto();
            String imgName = "";
            if(StringUtils.isNotBlank(photo)){
                LOGGER.info("detectFaceForRetrieve 获取底图");
                PictureInfoVo uploadFile = qiniuService.uploadFileLiveFace(Encodes.decodeBase64(photo));
                imgName = uploadFile.getImg_name();
            }

            String pic1 = ToolsService.addAccountPrivateUrlPreDomain(imgName);
            String pic2 = "";

            if (accountsInfoService.isZhimaCertification(accountUuid)) {
                pic2 = accountsCertificationService.getCertificationPhotoUrlByUuid(accountUuid);
            }
            if (StringUtils.isBlank(pic2)) {
                LOGGER.info("实名用户底图缺失:{}", accountUuid);
                throw new ServiceException(CodeStatus.NO_CERTIFICATION_2);
            }

            LOGGER.info("retrieve account face detect - pic1: {}, pic2: {}", pic1, pic2);
            // 2024.07.04 对比前底图获取是有图片地址去比较
            String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
            String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);

            CompareFaceDto facePicPCompare = comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.UNKNOWN);

            // the face compared result is similarity
            if (facePicPCompare.isSimilarity()) {
                LOGGER.info("uuid: {} retrieve face detect success", accountUuid);
                return;
            }
        }

        // throw exception if certificate error or the result of pictures comparison isn't similar
        throw new ServiceException("实名校验失败");
    }
}
