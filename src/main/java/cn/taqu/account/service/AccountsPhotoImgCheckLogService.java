package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.dao.AccountsPhotoImgCheckLogDao;
import cn.taqu.account.model.AccountsPhotoImgCheckLog;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-14 11:34
 */
@Service
public class AccountsPhotoImgCheckLogService {

    @Autowired
    private AccountsPhotoImgCheckLogDao accountsPhotoImgCheckLogDao;

    @Transactional
    public void merge(AccountsPhotoImgCheckLog AccountsPhotoImgCheckLog){
        accountsPhotoImgCheckLogDao.merge(AccountsPhotoImgCheckLog);
    }
}
