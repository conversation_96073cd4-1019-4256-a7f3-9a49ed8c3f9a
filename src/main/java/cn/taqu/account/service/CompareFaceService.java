package cn.taqu.account.service;

import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.dto.CompareUserDTO;

/**
 * 人脸比对方法
 *
 * <AUTHOR>
 * @date 2025/7/12 07:45
 */
public interface CompareFaceService {

    /**
     * 人脸比对方法【没指定策略，根据配置】
     */
    CompareFaceDto compare(String picOne, String picTwo, String uuid);

    /**
     * 【开关】
     * 是否开启接口数据比对。由于腾讯图片识别接口数据误判、误杀等各种场景下数据与真实情况存在较大误差。
     * 因此需要植入一个数据比对接口来验证哪个接口稳定性更好。
     */
    Boolean isOpenInterfaceCompare();

    /**
     * 接口结果比对
     *
     * @param data: 现接口结果
     * @param user: 用户信息
     */
    void interfaceCompare(CompareFaceDto data, CompareUserDTO user);

}
