package cn.taqu.account.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.taqu.account.dao.AccountFreezeAdminLogoutLogDao;
import cn.taqu.account.dto.approve.ModifyApprovalDto;
import cn.taqu.account.event.EventId;
import cn.taqu.account.event.EventTrackReporter;
import cn.taqu.account.model.AccountFreezeAdminLogoutLog;
import cn.taqu.account.utils.DataCtlHelper;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import com.google.common.collect.Maps;
import com.taqu.mp.account.client.MPAccountClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Service
public class AccountFreezeAdminLogoutLogService {

    @Autowired
    private AccountFreezeAdminLogoutLogDao accountFreezeAdminLogoutLogDao;

    @Autowired
    private AccountsInfoService accountsInfoService;

    @Autowired
    private AccountsService accountsService;

    @Autowired
    private MPAccountClient mpAccountClient;

    @Autowired
    private EventTrackReporter eventTrackReporter;

    public List<Map<String, Object>> getAccountFreezeList(Map<String, Object> params, int page, int pageSize) {
        Long startTime = MapUtils.getLong(params, "start_time");
        Long endTime = MapUtils.getLong(params, "end_time");
        String uuid = MapUtils.getString(params, "uuid");

        Map<String, Object> reqParam = new HashMap<>();
        reqParam.put("createTimeStart", startTime == null ? null : startTime * 1000);
        reqParam.put("createTimeEnd", endTime == null ? null : endTime * 1000);
        reqParam.put("executeState", 0);
        reqParam.put("uuid", uuid);
        reqParam.put("pageNo", page);
        reqParam.put("pageSize", pageSize);

        Map<String, Object> resMap = mpAccountClient.freezeAccountList(reqParam);

        List<String> uuids = new ArrayList<>();

        JSONArray array = (JSONArray) resMap.get("list");
        if (array != null && array.size() > 0) {
            for (JSONObject jsonObject : array.jsonIter()) {
                String resUuid = jsonObject.getStr("uuid");
                if (StringUtils.isNotBlank(resUuid)) {
                    uuids.add(resUuid);
                }
            }
        }

        if (CollectionUtils.isEmpty(uuids)) {
            return new ArrayList<>();
        }

        Map<String, Map<String, Object>> uuidInfos = accountsInfoService.getInfoByUuid(
                uuids.toArray(new String[0]),
                new String[]{"account_name", "avatar"},
                "1", true, false);

        return uuids.stream().map(key -> {
            Map<String, Object> uuidInfo = uuidInfos.get(key);
            Map<String, Object> map = new HashMap<>();
            map.put("account_name", MapUtils.getString(uuidInfo, "account_name"));
            map.put("uuid", key);
            map.put("avatar", MapUtils.getString(uuidInfo, "avatar"));
            return map;
        }).collect(Collectors.toList());
    }

    public List<Map<String, Object>> getAccountFreezeAdminLogoutLog(Map<String, Object> params, int page, int pageSize) {
        Long startTime = MapUtils.getLong(params, "start_time");
        Long endTime = MapUtils.getLong(params, "end_time");
        String uuid = MapUtils.getString(params, "uuid");
        String operator = MapUtils.getString(params, "operator");

        PageData<AccountFreezeAdminLogoutLog> pageData = accountFreezeAdminLogoutLogDao.getFreezeAdminLogoutLogList(uuid, operator, startTime, endTime, page, pageSize);
        List<AccountFreezeAdminLogoutLog> dataList = pageData.getData();

        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        Map<String, Map<String, Object>> uuidInfos = accountsInfoService.getInfoByUuid(
                dataList.stream().map(AccountFreezeAdminLogoutLog::getAccountUuid).toArray(String[]::new),
                new String[]{"account_name", "avatar"},
                "1", true, false);

        return dataList.stream().map(log -> {
            Map<String, Object> uuidInfo = uuidInfos.get(log.getAccountUuid());
            Map<String, Object> map = new HashMap<>();
            map.put("account_name", MapUtils.getString(uuidInfo, "account_name"));
            map.put("uuid", log.getAccountUuid());
            map.put("avatar", MapUtils.getString(uuidInfo, "avatar"));
            map.put("operator", log.getOperator());
            map.put("create_time", log.getCreateTime());
            return map;
        }).collect(Collectors.toList());
    }

    @Transactional
    public void freezeLogout(String uuid) {
        Map<String, Object> infoMap = accountsInfoService.singleGetInfo(uuid, new String[]{"account_status"});
        Integer accountStatus = MapUtils.getInteger(infoMap, "account_status");

        if (accountStatus == null) {
            throw new ServiceException("logout_empty", "用户已被注销，请勿重复操作");
        }

        // 直接注销
        accountsService.destroyByAccountUuid(uuid, "", false, false);

        AccountFreezeAdminLogoutLog accountFreezeAdminLogoutLog = create(uuid, SoaBaseParams.fromThread().getToken());
        accountFreezeAdminLogoutLogDao.merge(accountFreezeAdminLogoutLog);

        // 注销埋点上报
        HashMap<String, String> report = Maps.newHashMap();
        report.put("status", "成功注销");
        eventTrackReporter.report("", uuid, EventId.DESTROY_MOBILE_TYPE, report);
    }

    public void createFreezeLogoutApproval(ModifyApprovalDto dto) {
        String modifyData = dto.getModifyData();
        String uuid = DataCtlHelper.extractString(modifyData, 0);
        dto.setBizId(uuid);
        dto.setApprovalCode(DataCtlHelper.getCancelApprovalCode());
        dto.setOperationType("update");
        dto.setOriginSys(SoaBaseParams.fromThread().getOriginSystem());
        ModifyApprovalDto.CallBackUrlInfo urlInfo = new ModifyApprovalDto.CallBackUrlInfo();
        urlInfo.setService("freeze");
        urlInfo.setMethod("freezeLogout");
        urlInfo.setUrl(DataCtlHelper.selfUrl());
        urlInfo.setParamType(ModifyApprovalDto.ParamType.soaForm.name());
        List<ModifyApprovalDto.FormParam> formParams = new ArrayList<>();
        ModifyApprovalDto.FormParam param = new ModifyApprovalDto.FormParam();
        param.setId("originSysName");
        param.setValue("新海豹业务后台");
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("operationFullName");
        String buttonName = SoaService.getMenuName(dto.getOriginSys(), dto.getButtonCode());
        param.setValue(buttonName);
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("fileUrl");
        param.setValue(dto.getFileUrl());
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("applyReason");
        param.setType("textarea");
        param.setValue(dto.getApplyReason());
        formParams.add(param);
        dto.setApprovalFormParam(JsonUtils.objectToString(formParams));

        dto.setCallBackUrlInfo(urlInfo);
        dto.setOpTime(System.currentTimeMillis());
        SoaService.createModifyApproval(dto);
    }

    private AccountFreezeAdminLogoutLog create(String uuid, String token) {
        AccountFreezeAdminLogoutLog log = new AccountFreezeAdminLogoutLog();
        log.setAccountUuid(uuid);
        log.setOperator(token);
        log.setCreateTime(DateUtil.currentTimeSeconds());
        log.setUpdateTime(DateUtil.currentTimeSeconds());
        return log;
    }

}
