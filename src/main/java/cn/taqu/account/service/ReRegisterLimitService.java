package cn.taqu.account.service;

import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;

/**
 * 再注册限制服务
 *
 * <AUTHOR>
 * @date 2021/07/20
 */
public class ReRegisterLimitService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReRegisterLimitService.class);
    /**
     * 注销再注册限制天数 eg：90天 则90天后才可以再注册
     */
    public static Integer CANCELLATION_LENGTH = 90;

    /**
     * 魅力等级限制
     */
    public  static  Integer CHARM_RATING = 20;

    /**
     *  白名单超时时间
     */

    public static   long TIMEOUT = 48*60*60;

    /**
     * dingding机器人提醒
     */
    public static  String webhookKey = "https://oapi.dingtalk.com/robot/send?access_token=933b61ce40cff540d966b4e9b4ed297d55ce11fd343aacddce650d422bd26dc4";


    public static void setConf(String conf) {
        try {
            Map<String, Object> confMap = JsonUtils.stringToObject2(conf, new TypeReference<Map<String, Object>>() {
            });
            CHARM_RATING = MapUtils.getInteger(confMap, "charmRing", 20);
            CANCELLATION_LENGTH = MapUtils.getInteger(confMap, "cancellationLength",90);
            TIMEOUT =MapUtils.getLongValue(confMap, "timeout",48*60*60);
            webhookKey =MapUtils.getString(confMap, "webhookKey");
            LOGGER.info("获取注销再注册限制天数={},魅力值等级：{},白名单超时时间：{}", CANCELLATION_LENGTH, CANCELLATION_LENGTH,TIMEOUT);
        } catch (Exception e) {
            LOGGER.error("获取注销再注册限制配置失败", e);
        }
    }

}
