package cn.taqu.account.service;

import cn.taqu.account.vo.resp.HomepageBackgroundResp;

import java.util.List;

/**
 * 首页背景
 *
 * <AUTHOR>
 * @date 2024/11/25 10:20 上午
 */
public interface HomepageBackgroundService {

    /**
     * 获取背景配置
     *
     * @param accountUuid
     * @return
     */
    List<HomepageBackgroundResp> listBackground(String accountUuid);

    /**
     * 设置背景图片
     *
     * @param accountUuid
     * @param bgId
     */
    void setBackground(String accountUuid, Integer bgId);

    /**
     * 获取背景图片
     *
     * @param accountUuid
     */
    HomepageBackgroundResp getBackground(String accountUuid);

}
