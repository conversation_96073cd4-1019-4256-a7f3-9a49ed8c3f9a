package cn.taqu.account.service;

import cn.taqu.account.dao.UnbindWhitelistDao;
import cn.taqu.account.dto.approve.ApprovalStateDto;
import cn.taqu.account.dto.approve.ApprovalStateQueryDto;
import cn.taqu.account.dto.approve.ModifyApprovalDto;
import cn.taqu.account.dto.approve.ViewApprovalDto;
import cn.taqu.account.manager.AccountsCertificationManager;
import cn.taqu.account.manager.AccountsChatCertificationManager;
import cn.taqu.account.model.AccountsCertification;
import cn.taqu.account.model.AccountsChatCertification;
import cn.taqu.account.model.UnbindWhitelist;
import cn.taqu.account.utils.DataCtlHelper;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.taqu.mp.account.dto.UserRegInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UnbindWhitelistService {

    @Autowired
    private UnbindWhitelistDao unbindWhitelistDao;

    @Autowired
    private AccountsCertificationService accountsCertificationService;

    @Autowired
    private AccountsCertificationManager accountsCertificationManager;

    @Autowired
    private AccountsChatCertificationManager accountsChatCertificationManager;

    @Autowired
    private AccountsService accountsService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private AccountsCertService accountsCertService;

    @Autowired
    private SoaService soaService;

    public static Integer TIME=7;

    public static String WHITELIST_UNBIND_LINK;

    public static void initConfig(String conf) {
        try{
            Map<String, Object> map = JsonUtils.stringToObject(conf, new TypeReference<Map<String, Object>>() {
            });
            WHITELIST_UNBIND_LINK = MapUtils.getString(map, "whitelistUnbindLink", "");
            TIME=MapUtils.getInteger(map, "time", 7);
        }catch (Exception e){
            log.error("白名单解绑配置错误", e);
        }
    }

    @Transactional
    public void addUnbindWhitelist(String accountUuid,String identityNo,String operateName){
        UserRegInfo userRegInfo = soaService.getUserRegInfo(accountUuid);
        if(userRegInfo == null || userRegInfo.getCloned()==null){
            throw new ServiceException("未检索到相关记录，请核实uuid");
        }
        Integer cloned=userRegInfo.getCloned();
        String oldUuid=getOldUuid(identityNo, cloned);
        if(StringUtils.isBlank(oldUuid)){
            throw new ServiceException("未检索到相关记录，请核实身份证号");
        }
        UnbindWhitelist unbindWhitelist=new UnbindWhitelist();
        Integer riskStatus=checkBlacklist(oldUuid);
        unbindWhitelist.setAccountUuid(accountUuid);
        unbindWhitelist.setOldUuid(oldUuid);
        unbindWhitelist.setIdentityNo(identityNo);
        
        unbindWhitelist.setIdentityNoCipher(EncryptUtil.encrypt(identityNo));
        unbindWhitelist.setIdentityNoDigest(EncryptUtil.sm3(identityNo));
        
        // 根据开关判断是否不写入
        if(!ToolsService.unbindWhitelistSwitchWrite.isOn(true)) {
            log.info("unbind_whitelist不写加密字段");
            unbindWhitelist.setIdentityNo("");
        }
        
        String[] uuids={accountUuid,oldUuid};
        Map<String, String> result = accountsService.getNameByUuid(uuids);
        unbindWhitelist.setUserName(result.get(accountUuid));
        unbindWhitelist.setOldName(result.get(oldUuid));
        long currentTime=System.currentTimeMillis();
        unbindWhitelist.setUpdateTime(currentTime);
        unbindWhitelist.setCreateTime(currentTime);
        unbindWhitelist.setRiskStatus(riskStatus);
        unbindWhitelist.setChannel(cloned);
        unbindWhitelist.setOperateName(operateName);
        unbindWhitelist.setLink("/");
        if(riskStatus != 0){
            unbindWhitelist.setStatus(-1);
            unbindWhitelistDao.merge(unbindWhitelist);
            return;
        }
        unbindWhitelist.setStatus(0);
        UnbindWhitelist whitelistTemp=unbindWhitelistDao.merge(unbindWhitelist);
        Long id = whitelistTemp.getId();
        String link = getWhitelistLink(id);
        whitelistTemp.setLink(link);
        unbindWhitelistDao.merge(whitelistTemp);
        sendSystemMessage(accountUuid,link);
    }

    public String addUnbindWhitelistApprove(ModifyApprovalDto dto) {
        String modifyData = dto.getModifyData();
        String accountUuid = DataCtlHelper.extractString(modifyData, 0, "accountUuid");
        dto.setBizId(accountUuid);
        dto.setApprovalCode(DataCtlHelper.getEditApprovalCode());
        dto.setOperationType("add");
        dto.setOriginSys(SoaBaseParams.fromThread().getOriginSystem());
        ModifyApprovalDto.CallBackUrlInfo urlInfo = new ModifyApprovalDto.CallBackUrlInfo();
        urlInfo.setService("unbindWhitelist");
        urlInfo.setMethod("addUnbindWhitelist");
        urlInfo.setUrl(DataCtlHelper.selfUrl());
        urlInfo.setParamType(ModifyApprovalDto.ParamType.soaForm.name());
        List<ModifyApprovalDto.FormParam> formParams = new ArrayList<>();
        ModifyApprovalDto.FormParam param = new ModifyApprovalDto.FormParam();
        param.setId("originSysName");
        param.setValue("新海豹业务后台");
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("operationFullName");
        String buttonName = SoaService.getMenuName(dto.getOriginSys(), dto.getButtonCode());
        param.setValue(buttonName);
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("fileUrl");
        param.setValue(dto.getFileUrl());
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("applyReason");
        param.setType("textarea");
        param.setValue(dto.getApplyReason());
        formParams.add(param);
        dto.setApprovalFormParam(JsonUtils.objectToString(formParams));

        dto.setCallBackUrlInfo(urlInfo);
        dto.setOpTime(System.currentTimeMillis());
        SoaService.createModifyApproval(dto);
        return accountUuid;
    }

    public void viewUnbindWhitelistApprove(ViewApprovalDto dto) {
        dto.setOriginSys(SoaBaseParams.fromThread().getOriginSystem());
        dto.setApprovalCode(DataCtlHelper.getViewApprovalCode());
        dto.setDataActionType(1);
        dto.setChargeDataType(0);
        dto.setOpTime(System.currentTimeMillis());

        List<ModifyApprovalDto.FormParam> formParams = new ArrayList<>();
        ModifyApprovalDto.FormParam param = new ModifyApprovalDto.FormParam();
        param.setId("originSysName");
        param.setValue("新海豹业务后台");
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("operationFullName");
        String buttonName = SoaService.getMenuName(dto.getOriginSys(), dto.getButtonCode());
        param.setValue(buttonName);
        formParams.add(param);

        param = new ModifyApprovalDto.FormParam();
        param.setId("applyReason");
        param.setType("textarea");
        param.setValue(dto.getApplyReason());
        formParams.add(param);
        dto.setApprovalFormParam(JsonUtils.objectToString(formParams));
        SoaService.createViewApproval(dto);
    }

    public ApprovalStateDto approvalState(ApprovalStateQueryDto req) {
        req.setApprovalCode(DataCtlHelper.getViewApprovalCode());
        req.setOriginSys(SoaBaseParams.fromThread().getOriginSystem());
        return SoaService.getApprovalState(req);
    }


    /**
     * 查询当前身份证绑定的uuid
     * @param identityNo
     * @param cloned
     * @return
     */
    private String getOldUuid(String identityNo,Integer cloned){
        String oldUuid=getAccountsCertificationUuid(identityNo, cloned);
        if(StringUtils.isBlank(oldUuid)){
            oldUuid=getAccountsChatCertificationUuid(identityNo, cloned);
        }
        return oldUuid;
    }

    /**
     * 通过身份证获取实名用户uuid
     * @param identityNo
     * @param cloned
     * @return
     */
    private String getAccountsCertificationUuid(String identityNo,Integer cloned){
        List<AccountsCertification> list=accountsCertificationManager.listAccountCertificationsByIdentifyNo(identityNo, cloned);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        AccountsCertification accountsCertification=list.get(0);
        String oldUuid=accountsCertification.getAccountUuid();
        return oldUuid;
    }

    private String getAccountsChatCertificationUuid(String identityNo,Integer cloned){
        List<AccountsChatCertification> list=accountsChatCertificationManager.listAccountCertificationsByIdentifyNo(identityNo, cloned);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        AccountsChatCertification accountsCertification=list.get(0);
        String oldUuid=accountsCertification.getAccountUuid();
        return oldUuid;
    }

    private String getWhitelistLink(Long id){
        String link=WHITELIST_UNBIND_LINK+"?whitelist_id="+id;
        try {
            link=URLEncoder.encode(link,"UTF-8");
        } catch (Exception e) {
            log.warn("getWhitelistLink fail id:{}|link:{}",id,link,e);
        }
        return link;
    }

    private Integer checkBlacklist(String accountUuid){
        List<String> uuidList= Lists.newArrayList(accountUuid);
        //后台操作appcode默认为1
        Integer appCode=1;
        List<Map<String, Object>> blacklist= SoaService.getBlacklistByUuid(uuidList,appCode);
        log.info("checkBlacklist accountUuid:{}|blacklist:{}", accountUuid, JsonUtils.objectToString(blacklist));
        if(CollectionUtils.isEmpty(blacklist)){
            return 0;
        }
        for(Map temp: blacklist){
            String tempUuid= MapUtils.getString(temp,"account_uuid");
            if(accountUuid.equals(tempUuid)){
                String unlockTime= MapUtils.getString(temp,"unlockTime");
                if("-1".equals(unlockTime)){
                    return -1;
                }
            }
        }
        return 0;
    }

    public List<UnbindWhitelist> getUnbindWhitelists(String accountUuid){
        return unbindWhitelistDao.getUnbindWhitelists(accountUuid);
    }

    public UnbindWhitelist getUnbindWhitelistById(Long id){
        return unbindWhitelistDao.getUnbindWhitelistById(id);
    }

    public Integer checkUnbindWhitelist(UnbindWhitelist unbindWhitelist, String accountUuid){
        if(unbindWhitelist==null){
            return -1;
        }
        Integer status=unbindWhitelist.getStatus();
        if(status!=0 && status!=1){
            return -1;
        }
        if(!accountUuid.equals(unbindWhitelist.getAccountUuid())){
            return -1;
        }
        Long time=unbindWhitelist.getCreateTime()+TIME*24*3600*1000;
        if(time<System.currentTimeMillis()){
            return -2;
        }
        return status;
    }


    public  Integer checkUnbindWhitelist(Long whitelistId, String accountUuid){
        if(whitelistId==null){
            return -1;
        }
        UnbindWhitelist unbindWhitelist=getUnbindWhitelistById(whitelistId);
        return checkUnbindWhitelist(unbindWhitelist, accountUuid);
    }

    public void updateUnbindStatus(Long whitelistId, Integer status){
        unbindWhitelistDao.updateUnbindWhitelistStatusById(whitelistId,status);
    }

    @Transactional
    public void cancelCert(String oldUuid,Long whitelistId){
        String token = SoaBaseParams.fromThread().getToken();
        List<Integer> cancelCertTypes = Lists.newArrayList(1);
        accountsCertService.cancelCert(oldUuid, cancelCertTypes, token);
        accountsCertificationService.cancelCertificationForChat(oldUuid, true, token);
        updateUnbindStatus(whitelistId, 1);
    }


    public void sendSystemMessage(String accountUuid,String link) {

        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info = "您可点击 %s ，在7天内解绑历史账号的实名认证，如有疑问可咨询客服。";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "这里");
        contentReplace.put("r", "m=web&a=url&ul="+link);
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

//        JSONObject contentReplace1 = new JSONObject();
//        contentReplace1.put("w", "这里可以跳转至认证页面");
//        contentReplace1.put("r", "m=web&a=url&ul="+WHITELIST_BIND_LINK);
//        contentReplace1.put("c", "#0000FF");
//        contentReplaces.add(contentReplace1);


        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "老帐号解绑");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);
        messageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
    }
    
//
//    private void sendSystemMessage(String accountUuid, String templateCode, Integer cloned, Integer appCode){
//        NoticeDto noticeDto=SystemNoticeUtil.buildNoticeDto(accountUuid,templateCode,cloned,appCode);
//        systemNoticeService.sendSystemNotice(noticeDto);
//
//    }
    
}
