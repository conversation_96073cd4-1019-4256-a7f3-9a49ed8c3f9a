package cn.taqu.account.service;

import cn.taqu.account.dto.RiskPictureCheckReqDTO;
import cn.taqu.account.dto.RiskPictureCheckRespDTO;
import cn.taqu.account.sentinel.fallback.CommonExceptionHandler;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 风控安全审核（https://o15vj1m4ie.feishu.cn/wiki/Mfigwy61YiMl0VkmsXYctDpcnQG）
 *
 * <AUTHOR>
 * @date 2023/12/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskPushService {

    private static final SoaServer RISK_SAFE_CENTER = new SoaServer("/soa/java/riskPunish");

    /**
     * 风控安全审核 @link https://api.admin.internal.taqu.cn/docs/api/api-1fsitpem4qdr2
     *
     * @param request
     * @return
     */
    @SentinelResource(value = "account_risk_retrieve_mobile",
            blockHandlerClass = CommonExceptionHandler.class,
            blockHandler = "pictureCheckBlockHandler")
    public RiskPictureCheckRespDTO checkRetrieveMobile(RiskPictureCheckReqDTO request) {
        SoaResponse response = SoaClientFactory.create(RISK_SAFE_CENTER).call("behaviorCaptcha", "getPublicCaptchaPermit", new Object[]{request});
        log.info("【找回账号，风控请求】req: {}, resp:{}", JsonUtils.objectToString(request), response.getData());
        if (response.fail() || StringUtils.isBlank(response.getData())) {
            RiskPictureCheckRespDTO resp = new RiskPictureCheckRespDTO();
            resp.setPermitStatus(-1);
            return resp;
        }
        return JSON.parseObject(response.getData(), RiskPictureCheckRespDTO.class);
    }

}
