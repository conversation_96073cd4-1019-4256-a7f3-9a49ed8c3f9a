package cn.taqu.account.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.core.log.Log;
import cn.taqu.core.utils.Identities;

/**
 * uuid相关
 * 
 * <AUTHOR>
 * @date 2021/02/25
 */
@Deprecated
@Service
public class UUIDService {
    
    private static final String PREFIX = "J";
	
	@Autowired
	@Qualifier("uuidStringRedisTemplate")
	private StringRedisTemplate uuidStringRedisTemplate;
	
	/**
	 * 生成12位UUID.生成UUID成功,返回生成的UUID;否则返回null.
	 * @Title generateUUID
	 * @return
	 * <AUTHOR>
	 * 2018年1月16日 上午10:02:50
	 */
	public String generateUUID() {
		// uuid为12位
		char[] uuidChars = new char[12];

		// 当前日期
		Date date = new Date();
		
		// 第一个字符为年后两位转换，18年开始， 18-a 19-b
		int y = Integer.parseInt(new SimpleDateFormat("yy").format(date));
		uuidChars[0] = (char) (y + 79);
		// 第二个字符为月转换，1-a，2-b
		int m = Integer.parseInt(new SimpleDateFormat("M").format(date));
		uuidChars[1] = (char) (m + 96);
		// 第三个字符为日转换，1-1，2-2，... 10-a，11-b
		int d = Integer.parseInt(new SimpleDateFormat("d").format(date));
		uuidChars[2] =(char)(d >= 10 ? (d - 10 + 97) : (d + 48));
		
		// 剩下9个字符字符从0-9,a-z中随机取
		Random random = new Random();
		for (int i = 0; i < 9; i++) {
			// 开始生成uuid剩下字符
			for (int index = 3; index < uuidChars.length; index++) {
				int next = random.nextInt(36);
				uuidChars[index] = (char) (next >= 10 ? (next - 10 + 97) : (next + 48));
			}
			String uuid = new String(uuidChars);
			// 判断uuid redis缓存中是否已经存在此uuid
			String redisKey = RedisKeyConstant.UUID_UNIQUE_USER.setArg(uuid);
			boolean setSuccess = uuidStringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1");
			// redis缓存中不存在此uuid，则将此uuid保存到redis中并返回，否则重新生成(最多重新生成9次，如果9次生成的uuid都能在redis中找到，则返回null，表示生成uuid失败)
			if (setSuccess) {
				//uuid默认缓存1天
				uuidStringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
				return uuid;
			} else {
				// 生成的uuid重复，记录一条日志
				if (i < 8) {
					Log.warn("第{}次生成UUID重复,UUID为{}", i + 1, uuid);
					continue;
				}
				// 连续9次生成uuid失败，记录error日志
				Log.error("连续{}次生成UUID重复,UUID生成失败", i + 1);
				break;
			}
		}

		return null;
	}
	
	/**
	 * 生成33位token
	 * 规则：J + uuid
	 * 
	 * @return 生成后的新token
	 */
	public static String generateTokenUUID() {
	    return PREFIX + Identities.uuid2();
	}
	
}
