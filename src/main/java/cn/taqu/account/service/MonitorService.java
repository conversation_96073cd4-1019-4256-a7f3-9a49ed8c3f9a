package cn.taqu.account.service;

import cn.taqu.account.common.PrometheusMetricsEnum;
import cn.taqu.account.common.PrometheusTypeEnum;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 监控类
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-09 17:23
 */
@Service
@Slf4j
public class MonitorService {

    private static final Map<String, Counter> COUNTER_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Histogram> HISTOGRAM_MAP = new ConcurrentHashMap<>();
    private static final String LABEL = "_label";

    @PostConstruct
    public void a(){
        for (PrometheusMetricsEnum metricsEnum: PrometheusMetricsEnum.values()) {
            if(Objects.equals(metricsEnum.getPrometheusTypeEnum().getType(), PrometheusTypeEnum.COUNTER.getType())){
                COUNTER_MAP.put(metricsEnum.getName(), buildCounter(metricsEnum));
                COUNTER_MAP.put(metricsEnum.getName()+LABEL,buildCounterLabelNames(metricsEnum));
            }else if(Objects.equals(metricsEnum.getPrometheusTypeEnum().getType(), PrometheusTypeEnum.HISTOGRAM.getType())){
                HISTOGRAM_MAP.put(metricsEnum.getName(), buildHistogram(metricsEnum));
            }
        }
        log.info("COUNTER_MAP={}", COUNTER_MAP);
        log.info("HISTOGRAM_MAP={}", HISTOGRAM_MAP);
    }

//    // 腾讯 图片比对 RT
//    private static final Histogram TENCENT_IMG_COMPARE_RT_HISTOGRAM = buildHistogram(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_RT_HISTOGRAM);
//    // 腾讯 图片比对 Request
//    private static final Counter TENCENT_IMG_COMPARE_COUNTER = buildCounter(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_COUNTER);
//    // 腾讯 图片比对 FAIL Request
//    private static final Counter TENCENT_IMG_COMPARE_FAIL_COUNTER = buildCounter(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_FAIL_COUNTER);
//    private static final Counter TENCENT_IMG_COMPARE_IGNORE_FAIL_COUNTER = buildCounter(PrometheusMetricsEnum.TENCENT_IMG_COMPARE_IGNORE_FAIL_COUNTER);
//    // 腾讯 图片质量 RT
//    private static final Histogram TENCENT_IMG_QUALITY_RT_HISTOGRAM = buildHistogram(PrometheusMetricsEnum.TENCENT_IMG_QUALITY_RT_HISTOGRAM);
//    // 腾讯 图片质量 Request
//    private static final Counter TENCENT_IMG_QUALITY_COUNTER = buildCounter(PrometheusMetricsEnum.TENCENT_IMG_QUALITY_COUNTER);
//    // 腾讯 图片质量 FAIL Request
//    private static final Counter TENCENT_IMG_QUALITY_FAIL_COUNTER = buildCounter(PrometheusMetricsEnum.TENCENT_IMG_QUALITY_FAIL_COUNTER);
//    // 相册上传接口统计
//    private static final Counter HTTP_REQUEST_SET_COVER_V2_COUNTER = buildCounter(PrometheusMetricsEnum.HTTP_REQUEST_SET_COVER_V2_COUNTER);
//    // 头像上传接口统计
//    private static final Counter HTTP_REQUEST_SET_AVATAR_V3_COUNTER = buildCounter(PrometheusMetricsEnum.HTTP_REQUEST_SET_AVATAR_V3_COUNTER);
//    // 真人认证相关接口统计
//    private static final Counter INIT_FACE_DETECT_CONF_COUNTER = buildCounter(PrometheusMetricsEnum.INIT_FACE_DETECT_CONF_COUNTER);
//    private static final Counter CHECK_FACE_DETECT_RESULT_COUNTER = buildCounter(PrometheusMetricsEnum.CHECK_FACE_DETECT_RESULT_COUNTER);
//    // 实名相关接口统计
//    private static final Counter CERTIFICATION_INIT_CERTIFY_COUNTER = buildCounter(PrometheusMetricsEnum.CERTIFICATION_INIT_CERTIFY_COUNTER);
//    private static final Counter CERTIFICATION_INIT_CERTIFY_V2_COUNTER = buildCounter(PrometheusMetricsEnum.CERTIFICATION_INIT_CERTIFY_V2_COUNTER);
//    private static final Counter CERTIFICATION_SYNC_CERTIFY_COUNTER = buildCounter(PrometheusMetricsEnum.CERTIFICATION_SYNC_CERTIFY_COUNTER);
//    private static final Counter CERTIFICATION_SYNC_CERTIFY_V2_COUNTER = buildCounter(PrometheusMetricsEnum.CERTIFICATION_SYNC_CERTIFY_V2_COUNTER);
//    // 手机闪验
//    private static final Counter MOBILE_FLASH_SUCCESS_COUNTER = buildCounter(PrometheusMetricsEnum.MOBILE_FLASH_SUCCESS_COUNTER);
//    private static final Counter MOBILE_FLASH_FAILURE_COUNTER = buildCounter(PrometheusMetricsEnum.MOBILE_FLASH_FAILURE_COUNTER);
    /**
     * 构造Counter
     * @param metricsEnum
     * @return
     */
    private static Counter buildCounter(PrometheusMetricsEnum metricsEnum){
        return Counter.build(metricsEnum.getName(), metricsEnum.getHelp()).register();
    }

    private static Counter buildCounterLabelNames(PrometheusMetricsEnum metricsEnum){
        return Counter.build(metricsEnum.getName()+LABEL, metricsEnum.getHelp()).labelNames("version","type").register();
    }

    /**
     * 构造Higtogram
     * @param metricsEnum
     * @return
     */
    private static Histogram buildHistogram(PrometheusMetricsEnum metricsEnum){
        return Histogram.build(metricsEnum.getName(), metricsEnum.getHelp()).register();
    }

    /**
     * 增加counter inc
     * @param metricsEnum
     */
    public static void incCounterMetrices(PrometheusMetricsEnum metricsEnum) {
        if(metricsEnum == null){
            return;
        }
        Counter counter = COUNTER_MAP.get(metricsEnum.getName());
        if(counter != null){
            counter.inc();
        }else{
            log.warn("incMetrices.err.未找到类型.name={}", metricsEnum.getName());
        }
    }


    /**
     * 增加counter inc
     *
     * @param metricsEnum
     */
    public static void incCounterMetrices(PrometheusMetricsEnum metricsEnum, String version, String type) {
        if(metricsEnum == null){
            return;
        }
        log.debug("incCounterMetrices={},version={},type={}",metricsEnum.getName(),version,type);
        Counter counter = COUNTER_MAP.get(metricsEnum.getName()+LABEL);
        if(counter != null){
            counter.labels(version,type).inc();
        }else{
            log.warn("incCounterMetrices.err.未找到类型.name={}", metricsEnum.getName());
        }
    }

    /**
     * 获取 histogram 计时
     * @param metricsEnum
     * @return
     */
    public static Histogram.Timer getHistogramTimer(PrometheusMetricsEnum metricsEnum){
        Histogram.Timer timer = null;
        if(metricsEnum == null){
            return timer;
        }
        Histogram histogram = HISTOGRAM_MAP.get(metricsEnum.getName());
        if(histogram != null){
            timer = histogram.startTimer();
        }else{
            log.warn("getTimer.err.未找到类型.name={}", metricsEnum.getName());
        }
        return timer;
    }

}
