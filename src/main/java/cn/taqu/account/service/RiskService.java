package cn.taqu.account.service;

import cn.taqu.account.sentinel.fallback.CommonExceptionHandler;
import cn.taqu.core.protocol.SoaBaseParams;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-22 18:26
 */
@Slf4j
@Service
public class RiskService {

    /**
     * 校验openId是否被风控 true-已被封禁 false-正常使用
     * @param type
     * @param openId
     * @return
     */
    @SentinelResource(value = "account_risk_checkOpenIdRisk",
            blockHandlerClass = CommonExceptionHandler.class,
            blockHandler="checkOpenIdRiskBlockHandler")
    public Boolean checkOpenIdRisk(String type, String openId){
        if(StringUtils.isBlank(type) || StringUtils.isBlank(openId)){
            return false;
        }
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Map<String, Object> banInfoMap = SoaService.isOpenIdBanDestroy(sbp.getAppcode(), sbp.getCloned(), type, openId);
        if(MapUtils.getBoolean(banInfoMap, "isBanDestroy", true)){
            return true;
        }
        return false;
    }

}
