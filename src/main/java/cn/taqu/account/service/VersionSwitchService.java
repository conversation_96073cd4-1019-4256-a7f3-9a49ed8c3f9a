package cn.taqu.account.service;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.taqu.account.dto.SwitchDto20220302;
import cn.taqu.account.dto.SwitchDto20231201;
import cn.taqu.account.dto.SwitchDto20241129;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-01 13:39
 */
@Service
@Slf4j
public class VersionSwitchService {

    public static SwitchDto20220302 SWITCH_20220302;

    public static SwitchDto20231201 SWITCH_20231201;
    
    public static SwitchDto20241129 SWITCH_20241129;

    /**
     * 初始化版本开关
     * @param jsonValue
     */
    public static void initSwitch20220302(String jsonValue){
        try {
            JSONObject json = JSON.parseObject(jsonValue);
            Integer openAlipayAuth = MapUtils.getInteger(json, "openAlipayAuth", 0);
            Integer iosVersion = MapUtils.getInteger(json, "iosVersion", 79999);
            Integer androidVersion = MapUtils.getInteger(json, "androidVersion", 79999);
            Integer cancelCertTimes = MapUtils.getInteger(json, "cancelCertTimes", 3);
            String cancelCertLimitMsg = MapUtils.getString(json, "cancelCertLimitMsg", "您当前账号本月已达%s次解除上限，请次月再试");
            SWITCH_20220302 = new SwitchDto20220302(openAlipayAuth, iosVersion, androidVersion, cancelCertTimes, cancelCertLimitMsg);
        } catch (Exception e){
            log.error("初始化20220302版本开关失败.json={}", jsonValue, e);
        }
    }

    /**
     * 初始化版本开关
     * @param jsonValue
     */
    public static void initSwitch20231201(String jsonValue){
        try {
            JSONObject json = JSON.parseObject(jsonValue);

            Long iosVersion = MapUtils.getLong(json, "iosVersion", 8040L);
            Long androidVersion = MapUtils.getLong(json, "androidVersion", 10990L);
            SWITCH_20231201 = new SwitchDto20231201(iosVersion, androidVersion);
        } catch (Exception e){
            log.error("初始化20220302版本开关失败.json={}", jsonValue, e);
        }
    }
    
    /**
     * 初始化版本开关
     * @param jsonValue
     */
    public static void initSwitch20241129(String jsonValue){
        try {
            JSONObject json = JSON.parseObject(jsonValue);
            
            Long iosVersion = MapUtils.getLong(json, "iosVersion", 79999L);
            Long androidVersion = MapUtils.getLong(json, "androidVersion", 79999L);
            SWITCH_20241129 = new SwitchDto20241129(iosVersion, androidVersion);
        } catch (Exception e){
            log.error("初始化20241129版本开关失败.json={}", jsonValue, e);
        }
    }
    

}
