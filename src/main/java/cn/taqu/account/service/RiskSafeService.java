package cn.taqu.account.service;

import cn.hutool.core.lang.ObjectId;
import cn.hutool.crypto.digest.MD5;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RiskSafeIdConstant;
import cn.taqu.account.dto.*;
import cn.taqu.account.sentinel.fallback.CommonExceptionHandler;
import cn.taqu.account.utils.EnvUtil;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LoggerUtils;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.SpringContextHolder;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 风控安全审核（https://o15vj1m4ie.feishu.cn/wiki/Mfigwy61YiMl0VkmsXYctDpcnQG）
 *
 * <AUTHOR>
 * @date 2023/12/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskSafeService implements ApplicationListener<ApplicationReadyEvent> {

    private final KafkaSinkUtil kafkaSinkUtil;

    public static SoaClient riskClient;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        SoaServer RISK_SAFE_CENTER = new SoaServer("/soa/java/riskSafeCenter");
        riskClient = SoaClientFactory.create(RISK_SAFE_CENTER);
        riskClient.setTryLimit(1);
    }

    public static String genBizId(String content) {
        return MD5.create().digestHex(content);
    }

    /**
     * 风控安全审核（https://api.admin.internal.taqu.cn/docs/api/api-1eh48gmvrqft6）
     *
     * @param request
     * @return
     */
    @SentinelResource(value = "account_risk_checkOpenIdRisk",
        blockHandlerClass = CommonExceptionHandler.class,
        blockHandler = "checkBlockHandler")
    public static RiskSafeCheckResponseDTO check(RiskSafeCheckRequestDTO request) {
        SoaResponse response = riskClient.call("safeProtocol", "check", request);
        log.info("[风控安全审核] - accountUuid: {}, request: {}, response: {}", request.getSenderUuid(), JSON.toJSONString(request), response.getData());
        if (response.fail() || StringUtils.isBlank(response.getData())) {
            // 请求失败降级处理
            log.warn("[风控安全审核]请求失败降级处理 - accountUuid: {}, request: {}", request.getSenderUuid(), JSON.toJSONString(request));
            SpringContextHolder.getBean(KafkaSinkUtil.class).push(EnvUtil.getTopicName("risk_safe_center_protocol_check"), request);

            // 返回待审核数据
            RiskSafeCheckResponseDTO dto = new RiskSafeCheckResponseDTO();
            dto.setRequestId(SoaBaseParams.fromThread().getDistinctRequestId());
            dto.setSafeId(request.getSafeId());
            dto.setBizId(request.getBizId());
            dto.setSenderUuid(request.getSenderUuid());
            dto.setReceiverUuid(request.getReceiverUuid());
            dto.setHitTypeCode("wait");
            dto.setLabel1("");
            dto.setLabel2("");
            dto.setLabel3("");
            return dto;
        }
        RiskSafeCheckResponseDTO dto = JSON.parseObject(response.getData(), RiskSafeCheckResponseDTO.class);
        if (StringUtils.isBlank(dto.getHitTypeCode())) {
            dto.setHitTypeCode("wait");
        }
        return dto;
    }


    /**
     * 风控相似度检测
     *
     * @param request
     * @return
     */
//    public static ImgVectorDetectRespDto imageVectorDetect(ImgVectorDetectDto request) {
//        SoaResponse response = riskClient.call("milvus", "imageUpload", request);
//        if (response.fail() || StringUtils.isBlank(response.getData())) {
//            log.warn("风控图片向量检测失败 code = {}, msg = {}", response.getCode(), response.getMsg());
//            // 风控调用失败 直接通过
//            ImgVectorDetectRespDto resp = new ImgVectorDetectRespDto();
//            resp.setHitTypeCode(ImgVectorDetectRespDto.PASS);
//            resp.setBizId(request.getBizId());
//            return resp;
//        }
//        ImgVectorDetectRespDto resp = JsonUtils.stringToObject(response.getData(), ImgVectorDetectRespDto.class);
//        if (StringUtils.isBlank(resp.getHitTypeCode())) {
//            resp.setHitTypeCode(ImgVectorDetectRespDto.PASS);
//        }
//        log.info("风控向量检测响应 {}", resp);
//        return resp;
//    }

    public static ImgVectorDetectRespDto imageVectorDetect(ImgVectorDetectDto request) {
        SoaResponse response = riskClient.call("milvus", "imageQuery", request);
        if (response.fail() || StringUtils.isBlank(response.getData())) {
            log.warn("风控图片向量查询失败 code = {}, msg = {}", response.getCode(), response.getMsg());
            // 风控调用失败 直接通过
            ImgVectorDetectRespDto resp = new ImgVectorDetectRespDto();
            resp.setHitTypeCode(ImgVectorDetectRespDto.PASS);
            resp.setBizId(request.getBizId());
            return resp;
        }
        ImgVectorDetectRespDto resp = JsonUtils.stringToObject(response.getData(), ImgVectorDetectRespDto.class);
        if (StringUtils.isBlank(resp.getHitTypeCode())) {
            resp.setHitTypeCode(ImgVectorDetectRespDto.PASS);
        }
        log.info("风控向量查询响应 {}", resp);
        return resp;
    }

    public static void imageVectorAdd(ImgVectorDetectDto request) {
        SoaResponse response = riskClient.call("milvus", "imageAdd", request);
        if (response.fail()) {
            log.warn("风控图片向量存储失败 code = {}, msg = {}", response.getCode(), response.getMsg());
        } else {
            log.info("风控向量存储成功 {}", request);
        }
    }

    public static void imageVectorDelete(ImgVectorRemoveDto request) {
        String method = RiskSafeIdConstant.SIMILAR_PHOTO_DETECT.equals(request.getSafeId()) ? "imageDeleteByBizIds" : "imageDelete";
        SoaResponse response = riskClient.call("milvus", method, request);
        if (response.fail()) {
            log.warn("风控图片向量删除失败 code = {}, msg = {}", response.getCode(), response.getMsg());
        } else {
            log.info("风控向量删除成功 {}", request);
        }
    }

    /**
     * 文本校验（替代旧方法：SoaService.textReviewFromRiskDetect）
     *
     * @param accountUuid
     * @param content
     * @param smid
     * @param type
     * @param appcode
     * @param cloned
     * @return
     */
    public static ShumeiContentCheckDTO textReviewFromRiskDetect(String accountUuid, String content, String smid, String bizId, String type, Integer appcode, Integer cloned) {
        return textReviewFromRiskDetect(accountUuid, content, null, smid, bizId, type, appcode, cloned);
    }

    /**
     * 文本校验（替代旧方法：SoaService.textReviewFromRiskDetect）
     *
     * @param accountUuid
     * @param content
     * @param photoUrls
     * @param smid
     * @param bizId
     * @param type
     * @param appcode
     * @param cloned
     * @return
     */
    public static ShumeiContentCheckDTO textReviewFromRiskDetect(String accountUuid, String content, List<String> photoUrls, String smid, String bizId, String type, Integer appcode, Integer cloned) {
        RiskSafeCheckRequestDTO request = new RiskSafeCheckRequestDTO();
        request.setAppCode(appcode);
        request.setCloned(cloned);
        request.setSafeId(RiskSafeIdConstant.getSaveId(type));
        request.setBizId(StringUtils.isNotBlank(bizId) ? bizId : ObjectId.next());
        request.setDeviceId(StringUtils.isBlank(smid) ? CommConst.OLD_CLIENT_PACKAGE : smid);
        request.setPostTime(System.currentTimeMillis() / 1000);
        request.setSenderUuid(accountUuid);
        request.setContent(content);
        request.setImageUrl(photoUrls);
        return check(request).toCheckDTO();
    }

    /**
     * 语音校验（替代旧方法：SoaService.audioReviewFromRiskDetect）
     *
     * @param accountUuid
     * @param bizId
     * @param audioUrl
     * @param type
     * @param smid
     * @param appcode
     * @param cloned
     * @return
     */
    public static ShumeiContentCheckDTO audioReviewFromRiskDetect(String accountUuid, Long bizId, String audioUrl, Integer audioTime, String type, String smid, Integer appcode, Integer cloned) {
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("audioTime", audioTime);
        RiskSafeCheckRequestDTO request = new RiskSafeCheckRequestDTO();
        request.setAppCode(appcode);
        request.setCloned(cloned);
        request.setSafeId(RiskSafeIdConstant.getSaveId(type));
        request.setBizId(String.valueOf(bizId));
        request.setDeviceId(StringUtils.isBlank(smid) ? CommConst.OLD_CLIENT_PACKAGE : smid);
        request.setPostTime(System.currentTimeMillis() / 1000);
        request.setSenderUuid(accountUuid);
        request.setVoiceFileUrl(Arrays.asList(audioUrl));
        request.setExtra(extraMap);
        return check(request).toCheckDTO();
    }

    /**
     * 图片校验（替代旧方法：SoaService.imgReviewFromRiskDetect）
     *
     * @param appcode
     * @param cloned
     * @param accountUuid
     * @param type
     * @param photoUrls
     * @param smid
     * @param photoId
     * @param extra
     * @return
     */
    public static ShumeiImgCheckResponseDTO imgReviewFromRiskDetect(Integer appcode, Integer cloned, String accountUuid, String type, List<String> photoUrls, String smid, Long photoId, Object extra) {
        RiskSafeCheckRequestDTO request = new RiskSafeCheckRequestDTO();
        request.setAppCode(appcode);
        request.setCloned(cloned);
        request.setSafeId(RiskSafeIdConstant.getSaveId(type));
        request.setDeviceId(StringUtils.isBlank(smid) ? CommConst.OLD_CLIENT_PACKAGE : smid);
        if (RiskSafeIdConstant.getRealPersonId().contains(request.getSafeId())) {
            Map<String, Object> throughMap = new HashMap<>();
            throughMap.put("photoUrl", photoUrls.get(0));
            request.setThrough(throughMap);
            request.setBizId(Objects.nonNull(photoId) ? getAccountPhotoBizId(photoId, photoUrls.get(0)) : ObjectId.next());
        } else {
            request.setBizId(Objects.nonNull(photoId) ? String.valueOf(photoId) : ObjectId.next());
        }
        request.setPostTime(System.currentTimeMillis() / 1000);
        request.setSenderUuid(accountUuid);
        request.setImageUrl(photoUrls);
        request.setExtra(extra);
        return check(request).toImgCheckDTO();
    }

    /**
     * 删除审核
     *
     * @param appcode
     * @param type
     * @param operator
     * @param bizIds
     */
    public void deleteByBizIds(Integer appcode, String type, String operator, List<Long> bizIds) {
        if (CollectionUtils.isNotEmpty(bizIds)) {
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (Long bizId : bizIds) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("safeId", RiskSafeIdConstant.getSaveId(type));
                dataMap.put("appCode", appcode);
                dataMap.put("bizId", String.valueOf(bizId));
                dataMap.put("operator", operator);
                dataMap.put("tracerId", SoaBaseParams.fromThread().getDistinctRequestId());
                dataMap.put("postTime", System.currentTimeMillis() / 1000);
                dataList.add(dataMap);
            }
            log.info("[风控安全审核]删除审核,appcode:{},type:{},operator:{},bizIds:{}", appcode, type, operator, bizIds);
            kafkaSinkUtil.push(EnvUtil.getTopicName("risk_safe_center_delete_review_task"), dataList);
        }
    }

    /**
     * 查询bizId的审核状态
     *
     * @param appcode
     * @param type
     * @param bizIdList
     */
    public static Map<String, RiskSafeHitTypeResponseDTO> queryBizStatus(Integer appcode, String type, Collection<String> bizIdList) {
        if (CollectionUtils.isEmpty(bizIdList)) {
            return Collections.emptyMap();
        }
        Map<String, RiskSafeHitTypeResponseDTO> resultMap = new HashMap<>();
        List<List<String>> partition = Lists.partition(new ArrayList<>(bizIdList), 50);
        partition.forEach(list -> {
            JSONObject json = new JSONObject();
            json.put("appCode", appcode);
            json.put("safeId", RiskSafeIdConstant.getSaveId(type));
            json.put("bizIdList", list);
            Object[] form = {json};
            String service = "reviewCommonQuery";
            String method = "queryBizStatus";
            try {
                SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.RISK_REVIEW).call(service, method, form);
                log.info("[风控安全卡审核查询] - request: {}, response: {}", JSON.toJSONString(json), response.getData());
                if (response.fail()) {
                    throw new ServiceException(LoggerUtils.soaRequestFail("riskReview", service, method, response.getCode(), response.getMsg()));
                }
                if (StringUtils.isNotBlank(response.getData())) {
                    resultMap.putAll(JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String, RiskSafeHitTypeResponseDTO>>() {}));
                }
            } catch (Exception e) {
                log.warn("[风控安全卡审核查询]请求失败 - request: {}, errorMsg: {}", JSON.toJSONString(json), e.getMessage(), e);
            }
        });
        return resultMap;
    }

    /**
     * 获取头像/相册bizId
     *
     * @param photoId
     * @param photoUrl
     * @return
     */
    public static String getAccountPhotoBizId(Long photoId, String photoUrl) {
        return photoId + "-" + Md5Util.encode(AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl));
    }
}
