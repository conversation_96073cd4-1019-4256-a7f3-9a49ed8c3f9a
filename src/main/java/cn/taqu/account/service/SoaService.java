package cn.taqu.account.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.taqu.account.common.BanOpenIdTypeEnum;
import cn.taqu.account.common.BlackListTypeEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.*;
import cn.taqu.account.dto.approve.ApprovalStateDto;
import cn.taqu.account.dto.approve.ApprovalStateQueryDto;
import cn.taqu.account.dto.approve.ModifyApprovalDto;
import cn.taqu.account.dto.approve.ViewApprovalDto;
import cn.taqu.core.common.client.*;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LoggerUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taqu.mp.account.client.MPAccountClient;
import com.taqu.mp.account.constant.CamelCaseBizConstant;
import com.taqu.mp.account.constant.SnakeCaseBizConstant;
import com.taqu.mp.account.dto.UserRegInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static cn.taqu.core.common.client.SoaServer.JAVA.SSOMS;

/**
 * soa请求相关类
 *
 * <AUTHOR>
 * @date 2020/12/01
 */
@Service
@Slf4j
public class SoaService {

//    public static final SoaServer USER_GRADE = new SoaServer("/soa/go/taqu-user-grade");

    private static final SoaServer BBS = new SoaServer("/soa/php/bbs");

    /**
     * 家族系统
     */
    private static final SoaServer FAMILY_SYSTEM = new SoaServer("/soa/php/family");

    /**
     * j105
     */
    private static final SoaServer FINANCE_PAY_CENTER = new SoaServer("/soa/java/financePayCenter");

    /**
     * 交易系统
     */
    public static final SoaServer FINANCE_TRADE = new SoaServer("/soa/java/financeTrade");


    private static final SoaServer MP_OPERATION_LOG = new SoaServer("/soa/java/mpOperationLog");

    @Autowired
    private MPAccountClient mpAccountClient;

    /**
     * 根据单点子系统用户id获取姓名
     *
     * @param idSet
     * @return
     */
    public static Map<Long,String> getRealnameByIdList(Set<Long> idSet){
        Map<Long,String> result = Maps.newHashMap();

        SoaResponse response = SoaClientFactory.create(SSOMS).call("bizuser", "getRealnameByIdList", idSet);
        if (response.fail()) {
            log.error("根据id获取单点用户名称失败，失败码:{}， 失败原因:{}", response.getCode(), response.getMsg());
            return result;
        } else {
            result = JsonUtils.stringToObject(response.getData(), new TypeReference<Map<Long, String>>() {});
        }
        return result;
    }

    /**
     * 获取用户所在家族uuid
     *
     * @param accountUuid
     * @return
     */
    @Deprecated
    public static String getAccountFamily(String accountUuid) {
        String result = "";
        Object[] form = {
                accountUuid,
        };
        SoaResponse soaResponse = SoaClientFactory.create(FAMILY_SYSTEM).call("Family", "getAccountFamily", form);

        if (soaResponse.fail()) {
            log.error("soa获取用户家族uuid失败, {}-{}", soaResponse.getCode(), soaResponse.getMsg());
            return result;
        }
        String data = soaResponse.getData();
        if (StringUtils.isNotBlank(data) && !Objects.equals(data, "[]")) {
            Map<String, Object> map = JsonUtils.stringToObject(data, new TypeReference<Map<String, Object>>() {
            });
            result = MapUtils.getString(map, "family_uuid", "");
        }
        return result;
    }

    @Deprecated
    public static Map<String, Object> getCallerConsortia(String accountUuid) {
        Map<String, Object> result = Maps.newHashMap();
        if (StringUtils.isBlank(accountUuid)) {
            return result;
        }
        try {
            SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.CALL_SYSTEM);
            SoaResponse soaResponse = soaClient.call("CallMain", "getCallerConsortia", accountUuid);
            if (soaResponse.fail()) {
                log.error("调用获取聊主公会接口失败,msg={}", JsonUtils.objectToString(soaResponse));
                return result;
            }
            if (StringUtils.isNotBlank(soaResponse.getData()) && !Objects.equals(soaResponse.getData(), "[]")) {
                result = JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<Map<String, Object>>() {
                });
            }
        } catch (Exception e) {
            log.error("调用获取聊主公会接口失败", e);
        }

        return result;
    }

    /**
     * 获取用户配置
     * https://api.admin.internal.taqu.cn/docs/api/api-1cpscthtk9866
     *
     * @param accountUuid
     * @return
     */
    @Deprecated
    public static Map<String, Object> getAccountConfig(String accountUuid) {
        Map<String, Object> result = Maps.newHashMap();
        if (StringUtils.isBlank(accountUuid)) {
            return result;
        }
        try {
            SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.FORUM_V5);
            SoaResponse soaResponse = soaClient.call("AccountJava", "getAccountConfig", accountUuid);
            if (soaResponse.fail()) {
                log.error("调用获取用户配置接口失败,msg={}", JsonUtils.objectToString(soaResponse));
                return result;
            }
            result = JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("调用获取用户配置接口失败", e);
        }

        return result;
    }

    /**
     * 获取用户配置
     *
     * @param accountUuid
     * @return
     */
    @Deprecated
    public static Map<String, Object> getInfoForJava(String accountUuid) {
        Map<String, Object> result = Maps.newHashMap();
        if (StringUtils.isBlank(accountUuid)) {
            return result;
        }
        try {
            SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.LIVE_V1);
            SoaResponse soaResponse = soaClient.call("Account", "getInfoForJava", accountUuid);
            if (soaResponse.fail()) {
                log.error("getInfoForJava调用获取用户配置接口失败,msg={}", JsonUtils.objectToString(soaResponse));
                return result;
            }
            result = JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("getInfoForJava调用获取用户配置接口失败", e);
        }

        return result;
    }

    /**
     * 用户是否在黑名单内
     *
     * @param accountUuid
     * @return boolean
     */
    public static boolean isInAccountsBlackList(Integer appcode, String accountUuid) {
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.RISK_PUNISH).call("accountBlacklist", "batchInAccountBlacklist", 1, Collections.singleton(accountUuid));
        if(soaResponse.fail()) {
            log.error("调用大宝鉴检测用户是否在黑名单中失败, uuid:{}, 失败原因:{}-{}", accountUuid, soaResponse.getCode(), soaResponse.getMsg());
        }
        Set<String> blacklist = JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<Set<String>>() {});
        return blacklist.contains(accountUuid);
    }

//    /**
//     * 检查聊主是否开启电话转接：true-开启,false-未开启
//     *
//     * @return
//     */
//    @Deprecated
//    public static Boolean isOpenCallTel(String accountUuid) {
//        Boolean result = false;
//        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.PHP.CALL_SYSTEM).call("CallTel", "isOpenCallTel", accountUuid);
//        if (soaResponse.fail()) {
//            log.error("soa获取聊主是否开启电话转接失败, {}-{}", soaResponse.getCode(), soaResponse.getMsg());
//            return result;
//        }
//        try {
//            result = Boolean.parseBoolean(soaResponse.getData());
//        } catch (Exception e) {
//            log.warn("格式化获取聊主是否开启电话转接失败数据失败,数据格式={}", JSON.toJSON(soaResponse.getData()), e);
//        }
//        return result;
//    }

    /**
     * 获取用户是否开启免打扰,0-关闭，1-开启
     *
     * @param accountUuids
     * @return
     */
    @Deprecated
    public static Map<String, Integer> getUserPushConfig(List<String> accountUuids) {
        Map<String, Integer> result = Maps.newHashMap();
        Object[] form = {
                accountUuids, "is_scheduled"
        };
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.PUSH).call("pushConfig", "getSingleConfig", form);
        if (soaResponse.fail()) {
            log.error("获取用户是否开启免打扰设置失败,用户uuid={}", JsonUtils.objectToString(accountUuids));
        }
        try {
            result = JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<Map<String, Integer>>() {
            });
        } catch (Exception e) {
            log.warn("获取用户是否开启免打扰格式不正确={}", JSON.toJSON(soaResponse), e);
        }
        return result;
    }

    /**
     * 通用黑名单信息查询
     *
     * @param fields 字段
     * @param paramsMap    参数map
     *                     eg：{[]
     *                     {
     *                       "accountUuid":"sssxsxsx",
     *                       "token":"50762e7f238393f04a992c82d4fe1851"
     *                     }}
     * @return {@link Map<String, Object>}
     */
    public static Map<String,Object> universalBlacklistInfo(String[] fields,Map<String,Object> paramsMap) {
        Map<String,Object> failResult = new HashMap<>();
        failResult.put("ACCOUNT_BLACK_LIST",Maps.newHashMap());
        failResult.put("TOKEN_BLACK_LIST","false");
        Object[] form = {
                fields ,paramsMap
        };
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM).call("blacklist", "universalBlacklistInfo",form);
        if(soaResponse.fail()) {
            log.error("调用大宝鉴通用黑名单信息查询失败,参数:{}, 失败原因:{}-{}",paramsMap, soaResponse.getCode(), soaResponse.getMsg());
            return failResult;
        }
        return JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<Map<String, Object>>() {});
    }

    /**
     * 通用黑名单信息查询
     *
     * @param fields 字段
     * @param paramsMap    参数map
     *                     eg：{[]
     *                     {
     *                       "accountUuid":"sssxsxsx",
     *                       "token":"50762e7f238393f04a992c82d4fe1851"
     *                     }}
     * @return {@link Map<String, Object>}
     */
    public static Map<String,Object> universalBlacklistInfoV3(String[] fields,Map<String,Object> paramsMap) {
        Object[] form = {
            fields ,paramsMap
        };
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.RISK_PUNISH).call("accountBlacklist", "universalBlacklistInfo",form);
        if(soaResponse.fail()) {
            log.error("调用j63通用黑名单信息查询失败,参数:{}, 失败原因:{}-{}",paramsMap, soaResponse.getCode(), soaResponse.getMsg());
            Map<String,Object> failResult = new HashMap<>();
            failResult.put(BlackListTypeEnum.ACCOUNT_BLACK_LIST.name(), Maps.newHashMap());
            failResult.put(BlackListTypeEnum.TOKEN_BLACK_LIST.name(), "false");
            return failResult;
        }
        return JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<Map<String, Object>>() {});
    }

    /**
     * 通用黑名单信息查询
     *
     * @param fields 字段
     * @param paramsMap    参数map
     *                     eg：{[]
     *                     {
     *                       "accountUuid":"sssxsxsx",
     *                       "token":"50762e7f238393f04a992c82d4fe1851"
     *                     }}
     * @return {@link Map<String, Object>}
     */
    public static Map<String,Object> universalBlacklistInfoV2(String[] fields, Map<String,Object> paramsMap) {
        Object[] form = {
                fields ,paramsMap
        };
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM).call("blacklist", "universalBlacklistInfo",form);
        if(soaResponse.fail()) {
            log.error("调用大宝鉴通用黑名单信息查询失败,参数:{}, 失败原因:{}-{}",paramsMap, soaResponse.getCode(), soaResponse.getMsg());
            return null;
        }
        return JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<Map<String, Object>>() {});
    }


    /**
     * 查询用户是否有在他趣消费或者充值过，1-有充值过，0-未充值过
     */
    @Deprecated
    public static Map<String, Object> liveGetInfo(String accountUuid) {
        Map<String, Object> result= Maps.newHashMap();
        Object[] form = {
                accountUuid
        };
        SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.LIVE_V1);
        SoaResponse response = soaClient.call("Account", "getInfo", form);
        if (response.fail()) {
            log.error("查询是否是付费用户失败,用户uuid={},errorCode={},errorMsg={}", accountUuid, response.getCode(), response.getMsg());
            return result;
        }
        try {
            result = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("查询是否是付费用户异常,用户uuid={},返回的数据格式={}", accountUuid, JSON.toJSON(response), e);
        }

        return result;
    }

    /**
     * 获取用户贵族资料
     * @param accountUuid
     */
    @Deprecated
    public static Map<String, Object> getAccountNoble(String accountUuid) {
        Map<String, Object> result= Maps.newHashMap();
        Object[] form = {
            accountUuid, new String[]{"noble_code","gold_name"}
        };
        SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.LIVE_V1);
        SoaResponse response = soaClient.call("Noble", "getAccountNoble", form);
        if (response.fail()) {
            log.warn("获取用户贵族资料失败,用户uuid:{},错误码:{},错误信息:{}", accountUuid, response.getCode(), response.getMsg());
        }
        try {
            result = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("获取用户贵族资料失败,用户uuid={},返回的数据格式={}", accountUuid, JSON.toJSON(response), e);
        }
        return result;
    }

    /**
     * 获取魅力值
     *
     * @param accountUuid
     * @return
     */
//    public static Integer getCharmScoreByUuid(String accountUuid) {
//        Integer level = 0;
//        Object[] form = {
//                accountUuid
//        };
//        SoaResponse soaResponse = SoaClientFactory.create(SoaService.USER_GRADE).call("Charm", "getCharmLevelByUuid", form);
//        if (soaResponse.fail()) {
//            log.warn("请求soa获取魅力值失败, 用户uuid：{}，{}-{},返回0", accountUuid,soaResponse.getCode(), soaResponse.getMsg());
//            return level;
//        }
//        level = Integer.valueOf(soaResponse.getData());
//        return level;
//    }

    /**
     * j74 通用soa  https://api.admin.internal.taqu.cn/docs/api/api-1ds32co4jvqo4
     *
     * @param uri
     * @param filterConditions
     * @param page
     * @param sort
     * @return
     */
    public static String postDateApiExecuteV2(String uri, Map<String, Object> filterConditions, Map<String, Integer> page, Map<String, String> sort) {
        String url = "[数据系统]";
        String service = "api";
        String fn = "executeV2";
        Object[] form = {uri, filterConditions, page, sort};
        try {
            SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.DATA_API_TEMP);
            SoaResponse response = soaClient.call(service, fn, form);
            if (response.fail()) {
                log.warn("SOA请求错误！url:{},service:{},fn:{},code:{},msg:{}", url, service, fn, response.getCode(), response.getMsg());
            } else {
                log.info("executeV2 response {}", response.getData());
                return response.getData();
            }
        } catch (Exception e) {
            log.warn("soa api/executeV2", e);
            return null;
        }
        return null;
    }

    /**
     * 根据ip获取用户位置信息
     * @param ip
     * @return
     */
    public static JSONObject getLocationInfoByUuidV2(String ip) {
        JSONObject result = null;
        Object[] form = {ip};

        String service = "mpgeo";
        String method = "getAddressByIp";
        SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.MP_GEO).call(service, method, form);
        if (response.fail()) {
            log.error("调用j79根据ip查询位置信息失败, ip={}, code:{}, 失败原因:{}.", ip, response.getCode(), response.getMsg());
            return null;
        } else {
            String respData = response.getData();
            if(StringUtils.isBlank(respData)){
                log.warn("调用j79根据ip查询位置信息接口调用成功, ip={}, data={}.", ip, JSON.toJSONString(respData));
                return null;
            }else{
                result = JSON.parseObject(respData);
                log.info("调用j79根据ip查询位置信息接口调用成功, ip={}, result={}.", ip, JSON.toJSONString(result));
            }
        }
        return result;
    }

    /**
     * 头像审核跳过回调
     * @param checkType
     * @param photoId
     * @param photoUrl
     * @param operaterToken
     */
    public static void sendReviewPhotoSkip(Integer checkType, Long photoId, String photoUrl, String operaterToken) {
        JSONObject json = new JSONObject();
        json.put("checkType", checkType);
        json.put("photoId", photoId);
        json.put("photoUrl", AvatarHandleService.getAvatarOfSavePhoto(photoUrl));
        json.put("status", 4);
        json.put("operator", "");
        json.put("punishType", null);
        json.put("punishReason", "");

        Object[] form = {json};
        String service = "avatarPictureReview";
        String method = "reviewCallBack";
        SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.RISK_REVIEW).call(service, method, form);
        if (response.fail()) {
            log.error("调用reviewCallBack跳过失败.type={}.photoId={}.photoUrl={}.code:{}, 失败原因:{}.", checkType, photoId, photoUrl, response.getCode(), response.getMsg());
        } else {
            log.info("调用reviewCallBack跳过成功.type={}.photoId={}.photoUrl={}", checkType, photoId, photoUrl);
        }
    }

    /**
     * 校验openId是否存在封禁后注销
     * @param appcode
     * @param cloned
     * @param type
     * @param openId
     * @return
     */
    public static Map<String,Object> isOpenIdBanDestroy(Integer appcode, Integer cloned, String type, String openId){
        if(!BanOpenIdTypeEnum.isContainValue(type)){
            log.warn("未知openId类型.type={}.openId={}", type, openId);
            HashMap<String, Object> respMap = new HashMap<>();
            respMap.put("isBanDestroy", true);
            return respMap;
        }
        Object[] form = {appcode, cloned, type, openId};
        String service = "banDestroyControl";
        String method = "isOpenIdBanDestroy";
        SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.RISK_CONTROL).call(service, method, form);
        if (response.fail()) {
            log.error("调用isOpenIdBanDestroy失败.type={}.appcode={}.cloned={}.失败={}.原因={}.", type, appcode, cloned, response.getCode(), response.getMsg());
            throw new ServiceException(CodeStatus.OPENID_BAN_SOA_FAIL);
        } else {
            log.info("调用isOpenIdBanDestroy成功.type={}.appcode={}.cloned={}.data={}", type, appcode, cloned, response.getData());
        }
        String body = response.getData();
        Map<String,Object> respMap = JsonUtils.stringToObject(body, new TypeReference<Map<String,Object>>() {});
        return respMap;
    }

    /**
     * 图片检测
     */
    public static void updateQiniuFileStatus(String photoUrl, Integer status){
        String service = "qiniu";
        String method = "updateStatus";
        Object[] form = {1, AccountsPhotoService.DEFAULT_AVATAR_BUCKET, photoUrl, status};
        SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.PICTURE).call(service, method, form);
        if (response.fail()) {
            throw new ServiceException(LoggerUtils.soaRequestFail("picture", service, method, response.getCode(), response.getMsg()));
        }
        if (StringUtils.isNotBlank(response.getData())) {
            Map<String,Object> respMap = JsonUtils.stringToObject(response.getData(), new TypeReference<Map<String,Object>>() {});
            if(!MapUtils.getBoolean(respMap, "updateSuccess", false)){
                log.warn("七牛图片变更状态失败.photoUrl={}", photoUrl);
            }
        }
    }

    /**
     * 操作日志批量记录
     * @param action
     * @param recordAdminLogContentDtos
     * @param operator
     */
    public static void batchRecordAdminLogByAction(String action, List<RecordAdminLogContentDto> recordAdminLogContentDtos, String operator){
        String service = "OperationLog";
        String method = "batchRecordAdminLogByAction";

        List<Map<String, String>> contentArr = new ArrayList<>();
        for (RecordAdminLogContentDto recordAdminLogContentDto: recordAdminLogContentDtos) {
            Map<String, String> content = new HashMap<>();
            content.put("content_id", recordAdminLogContentDto.getContent_id());
            content.put("content", recordAdminLogContentDto.getContent());
            contentArr.add(content);
        }

        Object[] form = {action, contentArr, operator};
        SoaResponse response = SoaClientFactory.create(SoaServer.PHP.FORUM_V5).call(service, method, form);
        if (response.fail()) {
            log.warn("batchRecordAdminLogByAction.error.action={}.contents={}.opreator={}.code={}.msg={}", action, JSONObject.toJSONString(contentArr), operator, response.getCode(), response.getMsg());
        }else{
            log.info("batchRecordAdminLogByAction.success.action={}.contents={}.opreator={}", action, JSONObject.toJSONString(contentArr), operator);
        }
    }

    /**
     * 高风险用户 - 真人认证验证
     *
     * @param accountUuid
     */
    public static Pair<Boolean, Integer> riskNeedLivingCert(String accountUuid) {
        boolean needLivingCert = false;
        Integer riskStatus = CommConst.NO_0; // 风险状态
        String service = "accountLimit";
        String method = "getLimitStatusByUuid";

        Object[] form = {CommConst.APPCODE_TAQU, SoaBaseParams.fromThread().getCloned(), accountUuid};
        SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.RISK_PUNISH).call(service, method, form);
        if (response.fail()) {
            log.warn("验证风险账号调用失败.uuid={}.code={}.msg={}", accountUuid, response.getCode(), response.getMsg());
        } else {
            Map<String, Object> result = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String, Object>>() {
            });
            needLivingCert = Objects.equals(MapUtils.getString(result, "need_living_cert", "0"), CommConst.YES_1.toString());
            riskStatus = MapUtils.getInteger(result, "risk_status", 0); // risk_status = 2为高风险用户，需进去验证页面
            log.info("验证风险账号调用成功.uuid={}.data={}", accountUuid, response.getData());
        }
        return new Pair<>(needLivingCert, riskStatus);
    }

    /**
     * 登录注册是否管控
     *
     * @param type  RegisterLoginControlIsControlReqDto.type
     * @param model RegisterLoginControlIsControlReqDto.model
     * @return
     */
    public static RegisterLoginControlIsControlRespDto registerLoginControlIsControl(String type, String model) {
        RegisterLoginControlIsControlRespDto respDto = null;
        try {
            SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
            Integer appcode = soaBaseParams.getAppcode();
            Integer cloned = soaBaseParams.getCloned();
            String platformName = soaBaseParams.getPlatform_name();
            Long appVersion = soaBaseParams.getApp_version();
            String token = soaBaseParams.getToken();

            RegisterLoginControlIsControlReqDto reqDto = RegisterLoginControlIsControlReqDto.builder().appCode(appcode).cloned(cloned)
                .model(model).platform(platformName).token(token).type(type).version(appVersion).build();
            String service = "registerLoginControl";
            String method = "isControl";
            Object[] form = {reqDto};
            SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.RISK_PUNISH).call(service, method, form);
            if (response.fail()) {
                log.warn("调用j63判断是否管控失败，code={}，msg={}", response.getCode(), response.getMsg());
            } else {
                respDto = JsonUtils.stringToObject2(response.getData(), new TypeReference<RegisterLoginControlIsControlRespDto>() {
                });
                log.info("调用j63判断是否管控成功，data={}", response.getData());
            }
        } catch (Exception e) {
            log.warn("调用j63判断是否管控失败", e);
        }
        return respDto;
    }

    // TODO 接入sentinel需要处理
    public static Map<String, Object> userABExpList(String accountUuid) {
        Map<String, Object> result;

        String service = "abTestApiServer";
        String method = "getUserExpList";

        SoaClient client = SoaClientFactory.create(SoaServer.JAVA.AB_TEST);
        client.setTryLimit(1);
        String resData = "{\"bisols\": [],\"expireTime\": 300,\"exps\": [],\"gisols\": [],\"userExpList\": []}";

        try {
            SoaResponse response = client.call(service, method, accountUuid);

            if (response.fail() || StringUtils.isBlank(response.getData())) {
                log.warn("调用【j91】失败。获取ab数据失败, 已降级, uuid={}, code={}, msg={}", accountUuid, response.getCode(), response.getMsg());
            } else {
                resData = response.getData();
            }
            log.info("获取ab实验列表调用成功.uuid={}.data={}", accountUuid, response.getData());
        } catch (Exception e) {
            log.warn("调用【j91】失败。获取ab数据失败, 已降级, uuid={}", accountUuid, e);
        }
        result = JsonUtils.stringToObject2(resData, new TypeReference<Map<String, Object>>() {
        });
        return result;
    }

    // TODO 接入sentinel需要处理
    public static AbTestRespDto getUserExp(String accountUuid, String expCode, Integer appcode, Integer cloned, String token) {
        AbTestRespDto abTestRespDto = new AbTestRespDto();

        String service = "abTestApiServer";
        String method = "getUserExp";

        Object[] form = {accountUuid, expCode, appcode, cloned, token};

        SoaClient client = SoaClientFactory.create(SoaServer.JAVA.AB_TEST);
        client.setTryLimit(2);
        // 默认结果
        String resData = "{\"expCode\":\"\",\"varias\":\"\",\"exp\":\"\",\"accountUuids\":null}";

        try {
            SoaResponse response = client.call(service, method, form);

            if (response.fail() || StringUtils.isBlank(response.getData())) {
                log.warn("调用【j91】失败。获取ab数据失败, 已降级, uuid={}, code={}, msg={}", accountUuid, response.getCode(), response.getMsg());
            } else {
                resData = response.getData();
            }
        } catch (Exception e) {
            log.warn("调用【j91】失败。获取ab数据失败, 已降级, uuid={}", accountUuid, e);
        }

        abTestRespDto = JsonUtils.stringToObject(resData, new TypeReference<AbTestRespDto>() {
        });
//        log.info("获取ab实验调用成功.uuid={}.data={}", accountUuid, response.getData());
        return abTestRespDto;
    }

    private static final ConnectParam labelApiConnectParam = new ConnectParam(500, 500, 500);

    public static void userLabelSync(String uuid, Integer sexType, Long createTime, Integer cityId) {
        SoaClient client = SoaClientFactory.create(SoaServer.JAVA.USER_PROFILE);
        client.setTryLimit(1);

        Map<String, String> labelMap = new HashMap<>();
        labelMap.put("s8", sexType != null ? sexType.toString() : "");
        labelMap.put("r3", createTime != null ? createTime.toString() : "");
        labelMap.put("alcid", cityId != null ? cityId.toString() : "");
        labelMap.put("tpid", cityId != null ? cityId.toString() : "");

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("userId", uuid);
        paramsMap.put("param", labelMap);

        log.info("用户标签同步开始.uuid={}, param={}", uuid, JSON.toJSONString(labelMap));

        try {
            SoaResponse soaResponse = client.call(labelApiConnectParam, "userLabel", "sync", paramsMap);
            if (soaResponse.fail()) {
                log.warn("用户标签同步失败.uuid={}.code={}.msg={}", uuid, soaResponse.getCode(), soaResponse.getMsg());
            } else {
                log.info("用户标签同步成功.uuid={}", uuid);
            }
        } catch (Exception e) {
            log.warn("用户标签同步异常.uuid={}.msg={}", uuid, e.getMessage(), e);
        }
    }

    public String getUuidByMobileMP(String mobile) {
        String accountUuid = "";
        if(StringUtils.isNumeric(mobile)) {
            Map<String, Object> queryUser = mpAccountClient.queryUser(mobile, 2);
            accountUuid = MapUtils.getString(queryUser, "uuid", "");
            if(StringUtils.isBlank(accountUuid)) {
                log.warn("未查询到uuid, mobile={}, queryUser={}", mobile, JsonUtils.objectToString(queryUser));
            }
        }
        return accountUuid;
    }

    public List<String> getUuidByPhoneWithoutClonedRestrict(String mobile) {
        if(StringUtils.isNumeric(mobile)) {
            List<Map<String, Object>> queryUser = mpAccountClient.queryUserByPhone(mobile);
            if(CollectionUtils.isNotEmpty(queryUser)) {
                return queryUser.stream()
                        .filter(map -> !Objects.equals(MapUtils.getInteger(map, "status"), 0))
                        .map(map -> MapUtils.getString(map, "uuid", "")).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    public String getMobileByUuidMP(String uuid) {
        String mobile = "";
        Map<String, Object> queryUser = mpAccountClient.queryUser(uuid, 1);
        mobile = MapUtils.getString(queryUser, "phone", "");
        if(StringUtils.isBlank(mobile)) {
            log.warn("未查询到mobile, uuid={}, queryUser={}", uuid, JsonUtils.objectToString(queryUser));
        }
        return mobile;
    }

    public Map<String, Object> getMobileByUuidsMP(List<String> uuids) {
        Map<String, Object> queryUser = mpAccountClient.getMobileByUuids(uuids);

        if(Objects.equals(CamelCaseBizConstant.NEED_REGISTER_VALUE , queryUser.get(SnakeCaseBizConstant.NEED_REGISTER))) {
            log.warn("未查询到数据, uuids={}, queryUser={}", uuids, JsonUtils.objectToString(queryUser));
        }

        return queryUser;
    }

    public Map<String, Object> getUuidsByMobileMP(List<String> mobiles) {
        Map<String, Object> queryUser = mpAccountClient.getUuidsByMobile(mobiles);

        if(Objects.equals(CamelCaseBizConstant.NEED_REGISTER_VALUE , queryUser.get(SnakeCaseBizConstant.NEED_REGISTER))) {
            log.warn("未查询到数据, mobiles={}, queryUser={}", mobiles, JsonUtils.objectToString(queryUser));
        }

        return queryUser;
    }

    public static void batchDeleteAccountIllAvatarLog(String accountUuid, Object data, String operator) {
        Object[] form = {Arrays.asList(data), operator};
        String service = "account";
        String method = "batchDeleteAccountIllAvatarLog";
        try {
            SoaResponse response = SoaClientFactory.create(BBS).call(service, method, form);
            log.info("[头像审核回调]增加头像审核拒绝日志 - accountUuid: {}, request: {}, response: {}", accountUuid, form, response.getData());
            if (response.fail()) {
                throw new ServiceException(LoggerUtils.soaRequestFail("bbs", service, method, response.getCode(), response.getMsg()));
            }
        } catch (Exception e) {
            log.warn("[头像审核回调]增加头像审核拒绝日志 - accountUuid: {}, request: {}, errorMsg: {}", accountUuid, form, e.getMessage(), e);
        }
    }

    public static void batchIgnoreAccountIllAvatarLog(String accountUuid, Object data, String operator) {
        Object[] form = {data, operator};
        String service = "account";
        String method = "batchIgnoreAccountIllAvatarLog";
        try {
            SoaResponse response = SoaClientFactory.create(BBS).call(service, method, form);
            log.info("[头像审核回调]增加头像审核通过日志 - accountUuid: {}, request: {}, response: {}", accountUuid, form, response.getData());
            if (response.fail()) {
                throw new ServiceException(LoggerUtils.soaRequestFail("bbs", service, method, response.getCode(), response.getMsg()));
            }
        } catch (Exception e) {
            log.warn("[头像审核回调]增加头像审核通过日志 - accountUuid: {}, request: {}, errorMsg: {}", accountUuid, form, e.getMessage(), e);
        }
    }

    public static Integer getPostCount(String accountUuid) {
        Object[] form = { accountUuid, 0 };
        String service = "AccountPostApi";
        String method = "xGetNumByAccount";
        try {
            SoaResponse response = SoaClientFactory.create(BBS).call(service, method, form);
            if (response.fail()) {
                return 0;
            }
            log.info("获取用户帖子soa，uuid = {}，响应 = {}", accountUuid, response.getData());
            if (NumberUtil.isNumber(response.getData())) {
                return Integer.parseInt(response.getData());
            }
            return 0;
        } catch (Exception e) {
            return 0;
        }
    }

    public Map<String, UserRegInfo> getUserRegInfo(List<String> uuids) {
        SoaBaseParams soaParams = SoaBaseParams.fromThread();
        if (soaParams.getAppcode() == null) {
            soaParams.setAppcode("1");
        }
        if (soaParams.getCloned() == null) {
            soaParams.setCloned("1");
        }

        List<List<String>> lists = ListUtil.split(uuids, 100);
        Map<String, UserRegInfo> userRegInfoMap = new HashMap<>();

        for (List<String> list : lists) {
            mpAccountClient.queryUserClonedBatch(list).forEach(userRegInfo -> {
                userRegInfoMap.put(userRegInfo.getUuid(), userRegInfo);
            });
        }

        return userRegInfoMap;
    }

    public UserRegInfo getUserRegInfo(String uuid) {
        SoaBaseParams soaParams = SoaBaseParams.fromThread();
        if (soaParams.getAppcode() == null) {
            soaParams.setAppcode("1");
        }
        if (soaParams.getCloned() == null) {
            soaParams.setCloned("1");
        }

        List<UserRegInfo> userRegInfos = mpAccountClient.queryUserClonedBatch(Collections.singletonList(uuid));
        if (CollectionUtils.isEmpty(userRegInfos)) {
            return null;
        }
        return userRegInfos.get(0);
    }

    /**
     * 通过uuid获取封禁信息
     * @param uuids
     * @param appCode
     * @return
     */
    public static List<Map<String,Object>> getBlacklistByUuid(List<String> uuids, Integer appCode) {
        Object[] form = {
                appCode, uuids
        };
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM).call("blacklist", "getBlackListByUuids",form);
        if(soaResponse.fail()) {
            log.error("调用大宝鉴，通过uuid获取封禁信息失败,参数:{}, 失败原因:{}-{}",uuids, soaResponse.getCode(), soaResponse.getMsg());
            return null;
        }
        return JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<List<Map<String,Object>>>() {});
    }

    /**
     * 获取文件私有地址
     *
     * @param bucket
     * @param url
     * @param appcode
     * @param expireTime
     * @return
     */
    public static String privateDownloadUrl(String bucket, String url, Integer appcode, Long expireTime) {
        Object[] form = {
            bucket, url, appcode, expireTime
        };
        SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.PICTURE).call("picture", "privateDownloadUrl",form);
        if(soaResponse.fail()) {
            log.warn("调用图片系统获取私有地址异常, 失败原因:{}-{}", soaResponse.getCode(), soaResponse.getMsg());
            return url;
        }
        return soaResponse.getData();
    }

    /**
     * @param realName
     * @param oldRewardAccount
     * @return
     */
    public static Boolean aliAliRealNameConsult(String realName, String oldRewardAccount) {
        ConsultsDTO consultsDTO = new ConsultsDTO();
        consultsDTO.setAccountRealName(realName);
        consultsDTO.setWithdrawalAccount(oldRewardAccount);
        Object[] form = {
            consultsDTO
        };
        SoaResponse soaResponse = SoaClientFactory.create(FINANCE_PAY_CENTER).call("ali", "aliRealNameConsult",form);
        if(soaResponse.fail() || StringUtils.isBlank(soaResponse.getData())) {
            log.warn("调用j105校验支付宝账号失败, 失败原因:{}-{}", soaResponse.getCode(), soaResponse.getMsg());
            return null;
        }else {
            String data = soaResponse.getData();
            Map<String, Object> map = JsonUtils.stringToObject(data, new TypeReference<Map<String, Object>>() {});
            return MapUtils.getBoolean(map, "successFlg", null);
        }
    }

    /**
     * 是否白名单
     * @param appcode
     * @param cloned
     * @param accountUuid
     * @param model
     * @return
     */
    public boolean isWhitelist(Integer appcode, Integer cloned, String accountUuid, String model) {
        HashMap<String, Object> params = Maps.newHashMap();
        params.put("appCode", appcode);
        params.put("cloned", cloned);
        params.put("accountUuid", accountUuid);
        params.put("model", model);

        try {
            SoaResponse soaResponse = SoaClientFactory.create(SoaServer.JAVA.RISK_PUNISH).call("riskWhiteList", "inWhiteList", new Object[]{params});
            if(soaResponse.fail()) {
                log.error("调用j63通用白名单信息查询失败,参数:{}, 失败原因:{}-{}", JsonUtils.objectToString(params), soaResponse.getCode(), soaResponse.getMsg());
                return false;
            }
            String data = soaResponse.getData();
            return Boolean.parseBoolean(data);
        } catch (Exception e) {
            log.error("调用j63通用白名单信息查询失败,参数:{}", JsonUtils.objectToString(params), e);
            return false;
        }
    }

    /**
     * 是否有提现记录
     *
     * @param alipayDigest
     * @param identityDigest
     * @param clonedList
     * @return
     */
    public List<String> withdrawRecord(String alipayDigest, ArrayList<Integer> clonedList) {
        HashMap<String, Object> params = Maps.newHashMap();
        params.put("withdrawAccountSm3", alipayDigest);
        params.put("clonedList", clonedList);

        try {
            SoaResponse soaResponse = SoaClientFactory.create(FINANCE_TRADE).call("withdrawalManage", "queryAccountIdCard", new Object[]{params});
            if (soaResponse.fail()) {
                log.error("调用交易系统查询提现记录失败,参数:{}, 失败原因:{}-{}", JsonUtils.objectToString(params), soaResponse.getCode(), soaResponse.getMsg());
                return Lists.newArrayList();
            }
            String data = soaResponse.getData();
            log.info("调用交易系统查询提现记录:{}, result:{}", JsonUtils.objectToString(params), data);
            return JsonUtils.stringToObject(data, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.error("调用交易系统查询提现记录失败,参数:{}", JsonUtils.objectToString(params), e);
            return Lists.newArrayList();
        }
    }

    public static void createModifyApproval(ModifyApprovalDto dto){
        SoaResponse response = SoaClientFactory.create(MP_OPERATION_LOG).call("dataModify", "approvalCreate", dto);
        if (response.fail()) {
            throw new ServiceException("发起飞书审批失败：" + response.getMsg());
        }
    }

    public static void createViewApproval(ViewApprovalDto dto){
        SoaResponse response = SoaClientFactory.create(MP_OPERATION_LOG).call("dataViewOutput", "viewData", dto);
        if (response.fail()) {
            throw new ServiceException("发起飞书审批失败：" + response.getMsg());
        }
    }

    public static ApprovalStateDto getApprovalState(ApprovalStateQueryDto dto){
        SoaResponse response = SoaClientFactory.create(MP_OPERATION_LOG).call("dataApproval", "getStatusByInfo", dto);
        if (response.fail()) {
            throw new ServiceException("发起飞书审批失败：" + response.getMsg());
        }
        return JsonUtils.stringToObject(response.getData(), ApprovalStateDto.class);
    }

    public static String getMenuName(String originSysCode, String code){
        SoaResponse response = SoaClientFactory.create(SSOMS).call("resource", "getFullNameCodeByServiceAndResourceCode", originSysCode, code);
        if (response.fail()) {
            throw new ServiceException("获取菜单名失败：" + response.getMsg());
        }
        Map<String, Object> result = JsonUtils.stringToObject(response.getData(), new TypeReference<Map<String, Object>>() {});
        if (CollectionUtil.isNotEmpty(result)) {
            return String.valueOf(result.getOrDefault("namePath", "")).replaceAll("//", "/");
        } else {
            return "";
        }
    }
}
