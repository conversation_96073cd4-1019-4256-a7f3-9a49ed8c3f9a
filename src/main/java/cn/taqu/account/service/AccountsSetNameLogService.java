package cn.taqu.account.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dao.AccountsSetNameLogDao;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.AccountsSetNameLog;
import cn.taqu.account.vo.CommonPage;
import cn.taqu.core.utils.DateUtil;

/**
 * 用户创建/修改昵称记录Service
 * <AUTHOR>
 * 2017年3月14日 上午11:08:31
 */
@Service
@Transactional
public class AccountsSetNameLogService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsSetNameLog> {

	@Autowired
	private AccountsSetNameLogDao accountsSetNameLogDao;
	@Autowired
	private AccountsManager accountsManager;

	/**
	 * 添加名称设置日志
	 * @param account_uuid 用户uuid
	 * @param account_name_old 旧昵称
	 * @param account_name_new 新昵称
	 * @param type 1-创建 2-修改
	 * @param oper 操作者 用户uuid 或 后台用户id，该操作者目前给社区显示，由于权限问题获取不到社区的后台用户id，现在直接存loginname
	 * @param oper_type 操作类型：1-app操作，2-后台操作
	 * <AUTHOR>
	 * 2017年3月23日 下午5:05:13
	 */
	public void addLog(String account_uuid, String account_name_old, String account_name_new, Integer defaultNickName, Integer oldDefaultNickName, Integer type, String oper, Integer oper_type){
		AccountsSetNameLog accountsSetNameLog = new AccountsSetNameLog();
		accountsSetNameLog.setAccount_name_new(account_name_new);
		accountsSetNameLog.setAccount_name_old(account_name_old);
		accountsSetNameLog.setDefault_nick_name(defaultNickName == null ? CommConst.NO_0 : defaultNickName);
		accountsSetNameLog.setOld_default_nick_name(oldDefaultNickName == null ? CommConst.NO_0 : oldDefaultNickName);
		accountsSetNameLog.setAccount_uuid(account_uuid);
		accountsSetNameLog.setCreate_time(DateUtil.currentTimeSeconds());
		accountsSetNameLog.setType(type);
		accountsSetNameLog.setOper(StringUtils.isEmpty(oper) ? "" : oper);
		accountsSetNameLog.setOper_type(oper_type);
		accountsSetNameLogDao.merge(accountsSetNameLog);
	}

	/**
	 *
	 * @param pageNumber
	 * @param pageSize
	 * @param account_uuid
	 * @return
	 * <AUTHOR>
	 * 2017年5月8日 下午2:23:44
	 */
	public CommonPage getNicknameLog(Integer pageNumber, Integer pageSize, String account_uuid) {
//		Sort sort = new Sort(Sort.Direction.DESC, "create_time");
//		Pageable newPageable = PageUtil.newPageable(pageNumber - 1, pageSize,sort);

		List<AccountsSetNameLog> list = accountsSetNameLogDao.getNicknameLogByUuid(account_uuid, (pageNumber - 1)*pageSize , pageSize);
		long total = accountsSetNameLogDao.getNicknameLogByUuidCount(account_uuid);

		List<Map<String, String>> rows = this.getNicknameLogResult(list);

		CommonPage<Map<String, String>> commonPage = new CommonPage<>();
		commonPage.setNowpage(pageNumber);
		commonPage.setPagesize(pageSize);
		commonPage.setRows(rows);
		commonPage.setTotal(total);

		return commonPage;
	}

	private List<Map<String, String>> getNicknameLogResult(List<AccountsSetNameLog> list) {
		List<Map<String, String>> rows = Lists.newArrayList();
		if(list != null && list.size() > 0){
			String accountUuid = list.get(0).getAccount_uuid();
			String accountName = accountsManager.getAccountNamebyUuid(accountUuid);
			for (AccountsSetNameLog accountsSetNameLog : list) {
				Map<String, String> map = new HashMap<String, String>();
				String accountNameOld = accountsSetNameLog.getAccount_name_old();
				String accountNameNew = accountsSetNameLog.getAccount_name_new();
				String oper = accountsSetNameLog.getOper();//操作者 用户uuid 或 后台用户id
				Integer operType = accountsSetNameLog.getOper_type();//操作类型：1-app操作，2-后台操作
				Integer type = accountsSetNameLog.getType();//操作类型: 1-创建 2-修改
				Long createTime = accountsSetNameLog.getCreate_time();

				operType = operType == null ? 1 : operType;
				type = type == null ? 2 : type;

				map.put("account_name_old", accountNameOld);
				map.put("account_name_new", accountNameNew);
				map.put("oper", oper);
				//现在社区后台的昵称历史用operation_user作为操作者，这边先添加进行兼容
				map.put("operation_user",oper);
				map.put("oper_name", operType == 1 ? accountName : oper);
				map.put("oper_type", operType.toString());
				map.put("oper_time", createTime == null? "0" : createTime.toString());
				map.put("type", type.toString());

				rows.add(map);
			}
		}

		return rows;
	}

	public List<AccountsSetNameLog> getByUuid(String accountUuid, int limit) {
		return accountsSetNameLogDao.getByUuid(accountUuid, limit);
	}

}
