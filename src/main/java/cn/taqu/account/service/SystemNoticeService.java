package cn.taqu.account.service;

import cn.hutool.core.lang.UUID;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.notice.MsgContent;
import cn.taqu.account.dto.notice.MsgTemplate;
import cn.taqu.account.dto.notice.NoticeDto;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SystemNoticeService {

    public static void processSystemNotice(String accountUuid, String templateCode,Integer cloned, Integer appCode){
        NoticeDto data=buildNoticeDto(accountUuid, templateCode, cloned, appCode);
        sendSystemNotice(data);
    }
    
    private static NoticeDto buildNoticeDto(String accountUuid, String templateCode, Integer cloned, Integer appCode) {
        String uuid= UUID.fastUUID().toString().replaceAll("-","");
        NoticeDto noticeDto = new NoticeDto();
        noticeDto.setUnqRequestId(uuid);
        noticeDto.setAppcode(appCode);
        noticeDto.setCloned(cloned);
        noticeDto.setPushUuid(accountUuid);
        noticeDto.setSystemMsgType("system_link_text");
        noticeDto.setSystemMsgChannel("safe_msg");
        noticeDto.setPushTagCode(templateCode);
        noticeDto.setPushFlag(0);
        MsgContent msgContent = new MsgContent();
        //0:删除,1:正常
        msgContent.setTemplateFlag(1);
        MsgTemplate msgTemplate = new MsgTemplate();
        msgTemplate.setTemplateCode(templateCode);
        msgContent.setTemplate(msgTemplate);
//        if(StringUtils.isNotBlank(url)){
//            msgContent.setRelaction(url);
//        }
        noticeDto.setSystemMsgContent(msgContent);
        return noticeDto;
    }

    public static void sendSystemNotice(NoticeDto data){
        Object[] form = {data};
        SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.PUSH_EXECUTE);
        SoaResponse response = soaClient.post().call("systemNotice", "processSystemNotice", form);
        log.info("sendSystemNotice form:{}|response:{}", JsonUtils.objectToString(form), JsonUtils.objectToString(response));
    }

    public static NoticeDto buildWhitelistNoticeDto(String accountUuid, String templateCode, Integer cloned, Integer appCode) {
        String uuid= UUID.fastUUID().toString().replaceAll("-","");
        NoticeDto noticeDto = new NoticeDto();
        noticeDto.setUnqRequestId(uuid);
        noticeDto.setAppcode(appCode);
        noticeDto.setCloned(cloned);
        noticeDto.setPushUuid(accountUuid);
        noticeDto.setSystemMsgType("system_link_text");
        noticeDto.setSystemMsgChannel("system_msg");
        noticeDto.setPushTagCode(templateCode);
        noticeDto.setPushFlag(0);
        MsgContent msgContent = new MsgContent();
        //0:删除,1:正常
        msgContent.setTemplateFlag(1);
        MsgTemplate msgTemplate = new MsgTemplate();
        msgTemplate.setTemplateCode(templateCode);
        msgContent.setTemplate(msgTemplate);
//        if(StringUtils.isNotBlank(url)){
//            msgContent.setRelaction(url);
//        }
        noticeDto.setSystemMsgContent(msgContent);
        return noticeDto;
    }

    public static void processSystemNoticeUseTemplate(String accountUuid, String pushTagCode, String templateCode, Integer cloned){
        String uuid= UUID.fastUUID().toString().replaceAll("-","");
        NoticeDto noticeDto = new NoticeDto();
        noticeDto.setUnqRequestId(uuid);
        noticeDto.setAppcode(CommConst.APPCODE_TAQU);
        noticeDto.setCloned(cloned);
        noticeDto.setPushUuid(accountUuid);
        noticeDto.setSystemMsgType("system_link_text");
        noticeDto.setSystemMsgChannel("system_msg");
        noticeDto.setPushTagCode(pushTagCode);
        noticeDto.setPushFlag(0);
        MsgContent msgContent = new MsgContent();
        msgContent.setTemplateFlag(CommConst.YES_1);
        MsgTemplate msgTemplate = new MsgTemplate();
        msgTemplate.setTemplateCode(templateCode);
        msgContent.setTemplate(msgTemplate);
        noticeDto.setSystemMsgContent(msgContent);
        sendSystemNotice(noticeDto);
    }

}
