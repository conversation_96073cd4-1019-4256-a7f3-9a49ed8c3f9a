package cn.taqu.account.service;

import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.dao.AccountsInfoDao;
import cn.taqu.account.dao.AccountsTqcoinLogsDao;
import cn.taqu.account.model.AccountsInfo;
import cn.taqu.account.model.AccountsTqcoinLogs;
import cn.taqu.account.search.AccountsTqcoinLogsSearch;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;

/**
 * 趣币记录Service
 *
 * @author:laikunzhen
 */
@Deprecated
@Service
@Transactional
public class AccountsTqcoinLogsService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsTqcoinLogs> {

    @Autowired
    private AccountsService accountsService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsTqcoinLogsDao accountsTqcoinLogsDao;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    private AccountsInfoService accountsInfoService;

    /**
     * 趣币打赏日志
     *
     * @param accountUuid         用户账号uuid
     * @param transferAccountUuid 交易用户uuid
     * @param operator            操作员id
     * @param tqcoinNum           变化的趣币数(可为负数)
     * @param type                类型
     * @param param               额外参数信息(帖子ID)
     * @param contentUuid         内容uuid(与param对应，不过此处存放的是uuid)
     * @param info                记录信息，关于趣币变化的文字描述信息
     */
    @Transactional
    public void addLog(Long accountId, String accountUuid, String transferAccountUuid, Long operator, Integer tqcoinNum, Integer type, Long param, String contentUuid, String info) {
        // 处理 截断info长度
        if(StringUtils.isNotBlank(info) && info.length() > 99) {
            info = info.substring(0, 99);
        }

        AccountsTqcoinLogs accountsTqcoinLogs = new AccountsTqcoinLogs();
        accountsTqcoinLogs.setAccount_id(accountId);
        accountsTqcoinLogs.setOperator(operator);
        accountsTqcoinLogs.setTqcoin_num(tqcoinNum);
        accountsTqcoinLogs.setType(type);
        accountsTqcoinLogs.setParam(param);
        accountsTqcoinLogs.setInfo(info);
        accountsTqcoinLogs.setCreate_time(DateUtil.currentTimeSeconds());
        accountsTqcoinLogs.setAccount_uuid(accountUuid);
        accountsTqcoinLogs.setContent_uuid(contentUuid);
        accountsTqcoinLogs.setTransfer_account_uuid(StringUtils.trimToEmpty(transferAccountUuid));
        accountsTqcoinLogsDao.merge(accountsTqcoinLogs);
    }

    /**
     * 根据accountUuid获取趣币数量，支持批量查询
     *
     * @param accountUuids
     * @return
     * @Title getQuCoin
     * <AUTHOR>
     * @Date 2015年9月29日 下午2:44:35
     */
    @Transactional
    public Map<String, Long> getQuCoin(String... accountUuids) {
        Map<String, Long> coinMap = new HashMap<>();
        // 不查缓存，直接给默认值 2024.05.16
        for (String accountUuid : accountUuids) {
            coinMap.put(accountUuid, 0L);
        }
        return coinMap;
    }

    /**
     * 根据uuid查询趣币数，返回以uuid为key，以趣币数为value的map
     *
     * @param uuids
     * @return
     */
    public Map<String, Long> getQuCoinByUuid(String[] uuids) {
        return this.getQuCoin(uuids);
    }

    public Long getQuCoinByAccountUuid(String accountUuid) {
        Long quCoin = this.getQuCoin(accountUuid).get(accountUuid);
        return quCoin == null ? 0 : quCoin;
    }

    /**
     * 根据uuid增加(减少)用户趣币
     *
     * @param accountUuid         要增加或减少趣币的用户uuid
     * @param coinNum
     * @param type
     * @param operator
     * @param info
     * @param contentUuid
     * @param param
     * @param transferAccountUuid 如果趣币是从A账户转移到B账户，该字段表示的就是与accountUuid相对的另一个账号的uuid，诸如签到送趣币等非用户与用户之间的趣币交易时，该值为空即可
     * @return
     */
    public Integer addQuCoinByUuid(String accountUuid, Integer coinNum, Integer type, Long operator, String info, String contentUuid, Long param, String transferAccountUuid) {
        long accountId = accountsService.getAccountIdByUuid(accountUuid);
        if (accountId <= 0) {
            throw new ServiceException(CodeStatus.USER_NO_EXISTS);
        }
        int finalCoin = this.addQuCoin(accountId, accountUuid, transferAccountUuid, coinNum, type, operator == null ? 0 : operator, info, contentUuid == null ? "" : contentUuid, param == null ? 0 : param);
        // 不写缓存 2024.05.16
//        accountStringRedisTemplate.opsForHash().increment(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), UuidInfoField.TQCOIN, coinNum);
        return finalCoin;
    }

    /**
     * 增加(减少)用户趣币
     *
     * @param accountId
     * @param accountUuid
     * @param transferAccountUuid
     * @param coinNum
     * @param type
     * @param operator
     * @param info
     * @param contentUuid
     * @param param
     * @return
     */
    private Integer addQuCoin(Long accountId, String accountUuid, String transferAccountUuid, Integer coinNum, Integer type, Long operator, String info, String contentUuid, Long param) {
        if (coinNum == 0) {
            throw new ServiceException("zero_coin_num", "趣币数不能为0");
        }

        Long tqcoin = accountsInfoDao.getTqcoinByAccountUuid(accountUuid);
        if (tqcoin == null) {
            AccountsInfo accountsInfo = accountsInfoService.findOrCreate(null);
            accountsInfo.setAccount_id(accountId == null || accountId == 0 ? accountsService.getAccountIdByUuid(accountUuid) : accountId);
            accountsInfo.setAccount_uuid(accountUuid);
            accountsInfo.setCreate_time(DateUtil.currentTimeSeconds());
            accountsInfoDao.merge(accountsInfo);
            tqcoin = 0L;
        }
        if (coinNum > 0) {
            accountsInfoDao.addQuCoinNumByAccountUuid(coinNum.longValue(), accountUuid);
        } else {
            int count = accountsInfoDao.subQuCoinNumByAccountUuid(Math.abs(coinNum.longValue()), accountUuid);
            if (count <= 0) {
                throw new ServiceException(CodeStatus.QU_COIN_NO_ENOUGH);
            }
        }
        this.addLog(accountId, accountUuid, transferAccountUuid, operator, coinNum, type, 0L, contentUuid, info);
        return tqcoin.intValue() + coinNum;
    }

    public PageData<AccountsTqcoinLogs> pageQuery(AccountsTqcoinLogsSearch search, Integer pageNumber, Integer pageSize) {
        Collection<String> fields = Arrays.asList("id", "account_id", "tqcoin_num", "type", "info", "param", "create_time", "operator", "account_uuid", "content_uuid");
        Sql sql = Sql.build(AccountsTqcoinLogs.class, fields);
        if (search != null && StringUtils.isNotBlank(search.getAccount_uuid()))
            sql = sql.eq("account_uuid", search.getAccount_uuid());
        if (search != null && search.getType() != null)
            sql = sql.eq("type", search.getType());
        if (search != null && search.getParam() != null)
            sql = sql.eq("param", search.getParam());
        if (search != null && StringUtils.isNotBlank(search.getContent_uuid()))
            sql = sql.eq("content_uuid", search.getContent_uuid());
        if (search != null && search.getStart_time() != null) {
            sql = sql.ge("create_time", search.getStart_time());
        } else {
            long start = DateUtil.changeMonth(new Date(), -1).getTime() / 1000;
            sql = sql.ge("create_time", start); //未传时间默认一个月前
        }
        if (search != null && search.getEnd_time() != null) {
            sql = sql.lt("create_time", search.getEnd_time());
        } else {
            sql = sql.lt("create_time", DateUtil.currentTimeSeconds());
        }
        if (search != null && search.getOperator_ids() != null && !search.getOperator_ids().isEmpty())
            sql = sql.in("operator", search.getOperator_ids());
        sql = sql.orderBy("create_time desc");
        return accountsTqcoinLogsDao.queryForPage(sql, pageNumber, pageSize, false);
    }

}
