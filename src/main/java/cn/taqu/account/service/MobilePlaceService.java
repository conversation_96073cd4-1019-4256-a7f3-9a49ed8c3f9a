package cn.taqu.account.service;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.utils.HttpUtils;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.ValidateUtil;

/**
 * 查询号码归属地
 * 服务提供商：阿里云市场->深圳华辰网络科技有限公司
 * https://market.aliyun.com/products/********/cmapi022206.html?spm=5176.**********.101.8.63417218lYIVNz#sku=yuncode1620600000
 */
@Service
public class MobilePlaceService {
    private static Logger LOGGER = LoggerFactory.getLogger(MobilePlaceService.class);
//    private static HttpClient httpClient;
    private static Map<String, String> HEADERS = new HashMap<>();
    private static String HOST = "https://api04.aliyun.venuscn.com";
    private static String PATH = "/mobile";
    private static String METHOD = "GET";

    public static void paserConfig(String json) {
        Map<String, String> config = JsonUtils.stringToObject2(json, new TypeReference<Map<String, String>>() {});
        String url = MapUtils.getString(config, "url");
        String appcode = MapUtils.getString(config, "appcode");
        if(StringUtils.isBlank(url)) {
            LOGGER.error("手机号码归属地查询地址未配置");
            return;
        }
        if(StringUtils.isBlank(appcode)) {
            LOGGER.error("手机号码归属地查询appcode未配置");
            return;
        }
        MobilePlaceService.HOST = url;
        MobilePlaceService.HEADERS.put("Authorization", "APPCODE " + appcode);
    }

    /**
     * 获取手机号码归属地，返回值：
     * <code>
     * {
     *     "province": "省名称",
     *     "city": "市名称"
     * }
     * </code>
     * @param mobile
     * @return
     */
    public Map<String, String> getPlace(String mobile) {
        if(StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
            throw new ServiceException(CodeStatus.MOBILE_INVALID);
        }
        // 构造返回结果
        Map<String, String> result = Maps.newHashMap();
        result.put("province", "");
        result.put("city", "");
        
        // 构造请求参数
        Map<String, String> querys = Maps.newHashMap();
        querys.put("mobile", mobile);
        String entity = "";
        try {
            HttpResponse response = HttpUtils.doGet(HOST, PATH, METHOD, HEADERS, querys);
            entity = EntityUtils.toString(response.getEntity());
        } catch (Exception ae) {
            LOGGER.warn("从阿里云获取号码归属地失败, 查询归属地请求异常", ae);
            throw new ServiceException("mobile_place_req_failure", ae.getMessage());
        }

        Map<String, Object> responseMap = JsonUtils.stringToObject2(entity, new TypeReference<Map<String, Object>>() {});
        int ret = MapUtils.getIntValue(responseMap, "ret");
        if(ret != 200) {
            LOGGER.warn("从阿里云获取号码归属地失败, 返回数据:{}", entity);
//            40001   手机号码不能为空    
//            40002   手机号码格式错误    
//            40003   查询号段不存在
            if(ret == 40003) {
                return result;
            }
            
            throw new ServiceException("ret_code_failure", "获取号码归属地失败: " + ret + " - " + MapUtils.getString(responseMap, "msg"));
        }

        Map<String, Object> data = (Map<String, Object>)responseMap.get("data");
        LOGGER.debug("获取到手机号码归属地, 手机号:{}, 归属地信息:{}", mobile, data);
        if(data == null || data.isEmpty()) {
            LOGGER.warn("从阿里云获取号码归属地失败, 返回数据：", entity);
            throw new ServiceException("data_is_null", "获取号码归属地失败, 未能获取到号码归属地");
        }
        result.put("province", MapUtils.getString(data, "prov", ""));
        result.put("city", MapUtils.getString(data, "city", ""));
        return result;
    }
}
