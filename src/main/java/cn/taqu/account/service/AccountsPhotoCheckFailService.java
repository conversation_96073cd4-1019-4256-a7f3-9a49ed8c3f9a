package cn.taqu.account.service;

import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsPhotoCheckFailDao;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.AccountsPhotoCheckFail;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.base.BaseServiceImpl;

@Service
public class AccountsPhotoCheckFailService extends BaseServiceImpl<Long, AccountsPhotoCheckFail> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsPhotoCheckFailService.class);

    @Autowired
    private AccountsPhotoCheckFailDao accountsPhotoCheckFailDao;

    @Autowired
    private RedisLockUtil redisLockUtil;

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    public String findCheckFailAvatarByUuid(String accountUuid){
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException("check_param_fail", "用户id不能为空");
        }
        String checkFailAvatarRedisKey = RedisKeyConstant.ACCOUNT_AVATAR_PHOTO_CHECK_FAIL.setArg(accountUuid);
        String value=accountStringRedisTemplate.opsForValue().get(checkFailAvatarRedisKey);
        if(StringUtils.isNotBlank(value)){
            return getPhotoUrl(value);
        }
        String checkFailAvatarLock = RedisKeyConstant.ACCOUNT_AVATAR_PHOTO_CHECK_FAIL_LOCK.setArg(accountUuid);
        if(!redisLockUtil.lock(checkFailAvatarLock, 1000)){
            throw new ServiceException(CodeStatus.GET_LOCK_FAIL);
        }
        try{
            value=accountStringRedisTemplate.opsForValue().get(checkFailAvatarRedisKey);
            if(StringUtils.isNotBlank(value)){
                return getPhotoUrl(value);
            }
            AccountsPhotoCheckFail accountsPhotoCheckFail=accountsPhotoCheckFailDao.findCheckFailAvatarByUuid(accountUuid);
            if(accountsPhotoCheckFail==null){
                accountsPhotoCheckFail=new AccountsPhotoCheckFail();
                accountsPhotoCheckFail.setAccount_uuid(accountUuid);
            }
            accountStringRedisTemplate.opsForValue().set(checkFailAvatarRedisKey, JSON.toJSONString(accountsPhotoCheckFail));
            accountStringRedisTemplate.expire(checkFailAvatarRedisKey,1, TimeUnit.DAYS);
            return accountsPhotoCheckFail.getPhoto_url();
        }finally {
            redisLockUtil.unLock(checkFailAvatarLock);
        }
    }

    private String getPhotoUrl(String value){
        try{
            AccountsPhotoCheckFail accountsPhotoCheckFail=JSON.parseObject(value,AccountsPhotoCheckFail.class);
            return accountsPhotoCheckFail.getPhoto_url();
        }catch (Exception e){
            logger.warn("getPhotoUrl fail value:{}",value);
        }
        return null;
    }

    /**
     * 保存违规头像
     * @param accountAvatar
     *
     */
    @Transactional
    public void saveCheckFailAvatar(AccountsPhoto accountAvatar ){
        if(accountAvatar==null){
            return;
        }
        AccountsPhotoCheckFail accountsPhotoCheckFail=new AccountsPhotoCheckFail();
        BeanUtils.copyProperties(accountAvatar,accountsPhotoCheckFail);
        accountsPhotoCheckFail.setType(0);
        accountsPhotoCheckFail.setId(null);
        accountsPhotoCheckFailDao.merge(accountsPhotoCheckFail);
        String accountUuid=accountAvatar.getAccount_uuid();
        String checkFailAvatarRedisKey = RedisKeyConstant.ACCOUNT_AVATAR_PHOTO_CHECK_FAIL.setArg(accountUuid);
        accountStringRedisTemplate.delete(checkFailAvatarRedisKey);
    }

}
