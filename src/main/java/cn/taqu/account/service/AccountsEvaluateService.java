package cn.taqu.account.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.model.PersonalityLabel;

@Deprecated
@Service
public class AccountsEvaluateService {
	private static final Logger LOGGER = LoggerFactory.getLogger(AccountsEvaluateService.class);
//	@Autowired
//	private AccountsInfoService accountsInfoService;
	@Autowired
	private PersonalityLabelService personalityLabelService;
//	@Autowired
//	private AccountsEvaluateDao accountsEvaluateDao;
//	@Autowired
//	private AccountsLabelService accountsLabelService;
	@Autowired
	@Qualifier("accountStringRedisTemplate")
	private StringRedisTemplate accountStringRedisTemplate;

	/**
	 * 获取from->to的评价
	 * 
	 * @param fromAccountUuid
	 * @param toAccountUuid
	 * @return
	 */
	public Map<String, String> getAccountEvaluate(String fromAccountUuid, String toAccountUuid) {
		Map<Object, Object> hashValues = accountStringRedisTemplate.opsForHash()
				.entries(RedisKeyConstant.ACCOUNT_EVALUATE.setArg(fromAccountUuid, toAccountUuid));
		Map<String, String> result = new HashMap<>();
		if (hashValues != null && !hashValues.isEmpty()) {
			result.put("type", MapUtils.getString(hashValues, "type"));// 评价类型 1:喜欢; 2:不喜欢
			result.put("label_ids", MapUtils.getString(hashValues, "label_ids"));// 评价的id，多个评价用英文逗号分隔
			result.put("is_evaluate", "1");// 是否评价过，0:否; 1:是;
		} else {
			result.put("is_evaluate", "0");// 是否评价过，0:否; 1:是;
		}
		return result;
	}

	/**
	 * 获取用户收到的评价及评价数量
	 * 
	 * @param accountUuid
	 */
	public Map<String, Object> getAccountEvaluatedCount(String accountUuid) {
		String accountEvaluate = RedisKeyConstant.ACCOUNT_EVALUATE_SORT.setArg(accountUuid);
		Set<ZSetOperations.TypedTuple<String>> set = accountStringRedisTemplate.opsForZSet()
				.reverseRangeByScoreWithScores(accountEvaluate, 1, 1_0000_0000D, 0, 100);
		List<Map<String, String>> labelList = new ArrayList<>(set.size());
		for (ZSetOperations.TypedTuple<String> typedTuple : set) {
			Double count = typedTuple.getScore();
			String id = typedTuple.getValue();
			Map<String, String> item = new HashMap<>();
			PersonalityLabel personalityLabe = personalityLabelService.getByIdFromDb(Long.valueOf(id));
			if (personalityLabe == null || !Objects.equals(personalityLabe.getType(), 4)) {
				continue;
			}
//			LOGGER.info("获取用户收到的评价和数量,用户uuid={},标签id={},type={},second_type={}",accountUuid,personalityLabe.getId(),personalityLabe.getType(),personalityLabe.getSecond_type());
			item.put("id", id);
			item.put("count", String.valueOf(count.intValue()));
			item.put("name", StringUtils.trimToEmpty(personalityLabe.getContent()));
			item.put("type", personalityLabe.getSecond_type().toString());
			labelList.add(item);
		}

		List<Object> evaluateTypeCountValues = accountStringRedisTemplate.opsForHash().multiGet(
				RedisKeyConstant.ACCOUNT_GET_EVALUATE_TYPE.setArg(accountUuid),
				Arrays.asList("like_count", "dislike_count"));
		Map<String, Object> result = new HashMap<>();
		result.put("like_count", evaluateTypeCountValues == null || evaluateTypeCountValues.get(0) == null ? "0"
				: evaluateTypeCountValues.get(0).toString());
		result.put("dislike_count", evaluateTypeCountValues == null || evaluateTypeCountValues.get(1) == null ? "0"
				: evaluateTypeCountValues.get(1).toString());
		result.put("label_list", labelList);
		return result;
	}

}
