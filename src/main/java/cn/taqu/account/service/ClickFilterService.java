package cn.taqu.account.service;

import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import cn.taqu.account.constant.RedisKeyConstant;

/**
 * redis 控制多次点击情况。
 * <AUTHOR>
 * 2018年3月28日 上午10:26:02
 */
@Service
public class ClickFilterService {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(ClickFilterService.class);

	@Autowired
	@Qualifier("accountStringRedisTemplate")
	private StringRedisTemplate accountStringRedisTemplate;
	
	private static final long DEFAULT_TIME = 1;
	private static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.SECONDS;
	
	public static final String TYPE_ACCOUNTS_LABEL = "accounts_label";
	
	public static final String TYPE_SET_ACCOUNTS_LIFE = "setAccountsLife";
	
	public static final String TYPE_DEL_ACCOUNTS_LIFE = "delAccountsLife";
	
	
	/**
	 * 是否是有效点击
	 * 
	 * @param type 类型，新增类型在 @ClickFilterService 中配置，并取用
	 * @param unique 为用户区别标识，可用uuid，ticket，token这类唯一标识标记
	 * @return true-有效点击，false-非有效点击
	 * <AUTHOR>
	 * 2018年3月28日 上午11:15:23
	 */
	public boolean isValidClick(String type, String unique){
		return isValidClick(type, unique, DEFAULT_TIME);
	}
	
	/**
	 * 是否是有效点击
	 * 
	 * @param type 类型，新增类型在 @ClickFilterService 中配置，并取用
	 * @param unique 为用户区别标识，可用uuid，ticket，token这类唯一标识标记
	 * @param timeout 有效点击间隔时间，默认单位：秒
	 * @return true-有效点击，false-非有效点击
	 * <AUTHOR>
	 * 2018年3月28日 上午11:15:23
	 */
	public boolean isValidClick(String type, String unique, long timeout){
		return isValidClick(type, unique, timeout, DEFAULT_TIME_UNIT);
	}
	
	/**
	 * 是否是有效点击
	 * 
	 * @param type 类型，新增类型在 @ClickFilterService 中配置，并取用
	 * @param unique 为用户区别标识，可用uuid，ticket，token这类唯一标识标记
	 * @param timeout 有效点击间隔时间
	 * @param timeUnit 计时单位
	 * @return true-有效点击，false-非有效点击
	 * <AUTHOR>
	 * 2018年3月28日 上午11:15:23
	 */
	public boolean isValidClick(String type, String unique, long timeout, TimeUnit timeUnit){
		boolean flag = checkParameter(type, unique);
		
		if(!flag){
			return !flag;
		}
		
		try{
			String key = RedisKeyConstant.CLICK_FILTER_TYPE_KEY.setArg(type, unique);
			long times = accountStringRedisTemplate.opsForValue().increment(key, 1);
			accountStringRedisTemplate.expire(key, timeout, timeUnit);
			if(times > 1){
				flag = false;
			}
		}catch(Exception e){
			LOGGER.warn("校验有效点击，reids操作异常", e);
		}
		
		return flag;
	}
	
	/**
	 * 是否通过校验
	 * 
	 * @param type
	 * @param unique
	 * @return
	 * <AUTHOR>
	 * 2018年3月28日 上午11:37:43
	 */
	private boolean checkParameter(String type, String unique){
		if(StringUtils.isBlank(type)){
			LOGGER.warn("校验有效点击，传入type异常，type={}",type);
			return false;
		}
		if(StringUtils.isBlank(unique)){
			LOGGER.warn("校验有效点击，传入unique异常，unique={}",unique);
			return false;
		}
		
		return true;
	}
	
	
}
