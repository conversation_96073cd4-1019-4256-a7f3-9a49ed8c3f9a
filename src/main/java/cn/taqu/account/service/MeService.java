package cn.taqu.account.service;

import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * app中，'我'→ 最下面对应的列表
 * <AUTHOR>
 * 2017年7月21日 下午3:28:29
 */
@Service
public class MeService {
	private static Logger logger = LoggerFactory.getLogger(MeService.class);
	private static Map<String, List<Map<String, Object>>> configMap;

	public static void parseWeConfig(String configJson) {
		try {
			configMap = JsonUtils.stringToObject2(configJson, new TypeReference<Map<String, List<Map<String, Object>>>>() {});
		} catch (Exception e) {
			logger.error("解析etcd中的me/v2配置异常", e);
		}
	}

	/**
	 * '我'页面 定制入口
	 * 注意：暂无后台，写死，后期将要做成有后台可配置的
	 * 按照php代码书写
	 * @param newUi 根据客户端传过来的参数显示不同的UI
	 * @param version 版本
	 * @param platformId 平台id 1:android; 2:ios
	 * @return
	 * <AUTHOR>
	 * 2017年7月21日 下午3:30:11
	 */
	public List<Map<String, String>> me(String newUi, String version, Integer platformId) {
		//List<Map<String, String>> result = new ArrayList<>();
		//List<Map<String, String>> bannerList = this.getConfigByPlatformId(configMap.get("bannerList"), platformId);
		List<Map<String, String>>  meList = this.getConfigByPlatformId(configMap.get("meList"), platformId);
		//result.addAll(bannerList);
		//result.addAll(meList);
		//return result;
		return  meList;
	}

	/**
	 * '我'页面 接口v2版本，下发的数据相比{@linkplain #me(String, String, Integer)}接口，多了bannerList数据
	 * @param platformId
	 * @return
	 */
	public Map<String, Object> meV2(Integer platformId) {
		List<Map<String, String>> bannerList = this.getConfigByPlatformId(configMap.get("bannerList"), platformId);
		List<Map<String, String>>  meList = this.getConfigByPlatformId(configMap.get("meList"), platformId);
		Map<String, Object> result = new HashMap<>();
		result.put("banner_list", bannerList);
		result.put("me_list", meList);
		return result;
	}

	private Map<String, String> createMeItem(String icon, String title, String relaction) {
		Map<String, String> item = Maps.newLinkedHashMap();
		if(icon != null) {
			item.put("icon", icon);
		}
		if(title != null) {
			item.put("title", title);
		}
		if(relaction != null) {
			item.put("relaction", relaction);
		}
		return item;
	}

	private List<Map<String, String>> getConfigByPlatformId(List<Map<String, Object>> configListMap, Integer platformId) {
		List<Map<String, String>> resultList = new ArrayList<>();
		if(configListMap != null) {
			configListMap.forEach(config -> {
				int configPlatformId = MapUtils.getIntValue(config, "platformId");
				if(configPlatformId == 0 || Objects.equals(configPlatformId, platformId)
						|| Objects.equals(2, configPlatformId) && (Objects.equals(2, platformId) || Objects.equals(3, platformId))) {

					//临时处理ios他趣plus版本不下发"他趣肆玩杯"
					boolean isIosPlus = !Objects.equals(1, platformId)
							&& Objects.equals(SoaBaseParams.fromThread().getAppcode(), 1)
							&& Objects.equals(SoaBaseParams.fromThread().getCloned(), 4);
					if(isIosPlus && Objects.equals(MapUtils.getString(config, "relaction"), "m=mallCup&a=cover")) {
						return;
					}

					resultList.add(this.createMeItem(
							MapUtils.getString(config, "icon"),
							MapUtils.getString(config, "title"),
							MapUtils.getString(config, "relaction")
					));
				}
			});
		}
		return resultList;
	}
}