package cn.taqu.account.service;

import cn.taqu.account.constant.AbRuleCode;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.AbTestRespDto;
import cn.taqu.account.soa.ICityAbTestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ab实验工具类
 *
 * <AUTHOR>
 * 2024年11月29日下午2:54:52
 */
@Slf4j
@Service
public class AbTestService {

    @Autowired
    private ICityAbTestService iCityAbTestService;

    /**
     * https://o15vj1m4ie.feishu.cn/wiki/VFsXwMYgeixaV6kAShPcrNTXnkh
     * 客户端实验。
     * 判断客户端版本后，新版才调用查询，不要查多了，影响实验用户量
     *
     * @param accountUuid
     * @param cloned
     * @return 是否属于实验组 true-是，false-否
     * <p>
     * 新版需求换成城市实验，该方法已废弃，先留一个迭代看看   ********
     * https://o15vj1m4ie.feishu.cn/wiki/GtQlwvf2Yi0XNzkByRAcmf11ngh
     */
    public boolean isProfileOptimizationUiExperimentalGroup(String accountUuid, Integer cloned) {
        AbTestRespDto abTestRespDto = SoaService.getUserExp(accountUuid, AbRuleCode.ProfileOptimizationUi.KEY, CommConst.APPCODE_TAQU, cloned, "");
        return AbRuleCode.ProfileOptimizationUi.isExperimentalGroup(abTestRespDto.getVarias());
    }

    /**
     * https://o15vj1m4ie.feishu.cn/wiki/PfN3woreSiJFLWkcNCKcMGP9nrd
     * 提现ab实验限制
     *
     * @param accountUuid
     * @param cloned
     * @return 是否属于实验组 true-是，false-否
     */
    public static boolean isWithdrawalLimitExpGroup(String accountUuid, Integer cloned) {
        AbTestRespDto abTestRespDto = SoaService.getUserExp(accountUuid, AbRuleCode.WithdrawalTest.KEY, CommConst.APPCODE_TAQU, cloned, "");
        boolean expGroup = AbRuleCode.WithdrawalTest.isExpGroup(abTestRespDto.getVarias());
        log.info("提现限制实验组对比:{}, {}", accountUuid, expGroup);
        return expGroup;
    }


    // ********************* 城市实验相关（go） *******************************

    /**
     * 资料卡实验
     */
    final static String PROFILE_EXP_CODE = "profile_city1";

    /**
     * 资料卡控制组
     */
    final static String PROFILE_CONTROL = "profile_center";

    /**
     * 新版ab城市实验
     *
     * @param uuid
     * @return
     */
    public boolean isProfileCityExp(String uuid, String cityId, Integer appcode, Integer cloned, Integer platformId, Long appVersion) {
        CityAbTestService.Response response = iCityAbTestService.getTargetExpCache(uuid, PROFILE_EXP_CODE, cityId, appcode, cloned, platformId, appVersion);
        return !StringUtils.isBlank(response.getVaries()) && !PROFILE_CONTROL.equals(response.getVaries());
    }

}
