package cn.taqu.account.service;

import cn.hutool.core.date.DateUtil;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsPhotoDao;
import cn.taqu.account.dao.AliyunLiveFaceDetectDao;
import cn.taqu.account.model.AccountsInfo;
import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.AliyunLiveFaceDetect;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Service
@Slf4j
public class RealPersonService {

    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;

    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;

    @Autowired
    private AccountsInfoService accountsInfoService;

    @Autowired
    private AccountsPhotoDao accountsPhotoDao;

    @Transactional
    public void setUserRealPersonCertification(String accountUuid, String photoUrl) {
        AccountsPhoto accountAvatar = accountsPhotoDao.getAccountAvatar(accountUuid, null, false);
        accountAvatar.setPhoto_url(photoUrl);
        accountAvatar.setVerify_status(1);
        accountAvatar.setStatus(2);
        accountsPhotoDao.merge(accountAvatar);

        Map<String, String> photoMap = new HashMap<>();
        photoMap.put("id", accountAvatar.getId().toString());
        photoMap.put("photo_url", accountAvatar.getPhoto_url());
        photoMap.put("status", accountAvatar.getStatus().toString());
        photoMap.put("seq", accountAvatar.getSeq().toString());
        photoMap.put("bucket", accountAvatar.getBucket());
        photoMap.put("verify_status", StringUtil.nullNumberToEmptyString(accountAvatar.getVerify_status()));
        photoMap.put("error_msg", accountAvatar.getError_msg());
        photoMap.put("face_score", StringUtil.nullNumberToEmptyString(accountAvatar.getFace_score()));
        photoMap.put("like_num", StringUtil.nullNumberToEmptyString(accountAvatar.getLike_num()));
        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO.setArg(accountUuid);
        accountStringRedisTemplate.opsForHash().put(redisKey, "1", JsonUtils.objectToString(photoMap));

        AliyunLiveFaceDetect faceDetect = aliyunLiveFaceDetectDao.findOneByAccountUuid(accountUuid);
        if (faceDetect == null) {
            faceDetect = new AliyunLiveFaceDetect();
        }
        faceDetect.setAccount_uuid(accountUuid);
        faceDetect.setVerify_photo_url(photoUrl);
        faceDetect.setBase_photo_url(photoUrl);
        faceDetect.setSource("1");
        faceDetect.setCreate_time(DateUtil.currentSeconds());
        faceDetect.setUpdate_time(DateUtil.currentSeconds());
        aliyunLiveFaceDetectDao.merge(faceDetect);

        AccountsInfo info = accountsInfoService.findOrCreate(accountUuid);
        info.setReal_person_certification(1);
        info.setReal_photo_certification(1);
        info.setPerson_face_avatar(1);
        accountsInfoService.save(info);

        Map<String, String> cacheInfo = new HashMap<>();
        cacheInfo.put("real_person_certification", "1");
        cacheInfo.put("profile_verify_status", "1");
        cacheInfo.put("pre_profile_verify_status", "1");
        cacheInfo.put("face_certification", "1");
        cacheInfo.put("avatar_same_with_face", "1");
        cacheInfo.put("avatar", photoUrl);
        cacheInfo.put("avatar_origin", photoUrl);
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), cacheInfo);


    }
}
