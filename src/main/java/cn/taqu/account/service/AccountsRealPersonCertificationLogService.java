package cn.taqu.account.service;

import cn.taqu.account.dao.AccountsRealPersonCertificationLogDao;
import cn.taqu.account.dao.AliyunLiveFaceDetectDao;
import cn.taqu.account.model.AccountsRealPersonCertificationLog;
import cn.taqu.account.model.AliyunLiveFaceDetect;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-20 10:16
 */
@Slf4j
@Service
@Transactional
public class AccountsRealPersonCertificationLogService {
    @Autowired
    private AccountsRealPersonCertificationLogDao accountsRealPersonCertificationLogDao;
    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;

    /**
     * 保存真人认证记录
     * @param accountUuid
     */
    public void addLog(String accountUuid){
        AliyunLiveFaceDetect aliyunLiveFaceDetect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
        if(aliyunLiveFaceDetect == null){
            log.warn("真人认证查询活体记录失败.uuid={}", accountUuid);
        }
        String source = aliyunLiveFaceDetect.getSource();
        Long now = DateUtil.currentTimeSeconds();
        AccountsRealPersonCertificationLog realPersonCertificationLog = new AccountsRealPersonCertificationLog();
        realPersonCertificationLog.setAccountUuid(accountUuid)
                .setLiveFaceDetectTime(aliyunLiveFaceDetect.getCreate_time()).setRealPersonCertificationTime(now)
                .setSource(StringUtils.isBlank(source) ? "" : source)
                .setCreateTime(now).setUpdateTime(now);
        accountsRealPersonCertificationLogDao.merge(realPersonCertificationLog);
    }
}
