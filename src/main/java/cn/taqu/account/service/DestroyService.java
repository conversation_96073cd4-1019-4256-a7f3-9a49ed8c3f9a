package cn.taqu.account.service;

import cn.taqu.account.dto.DestroyReasonConfig;
import cn.taqu.account.vo.req.SetDestroyReasonReq;
import cn.taqu.account.vo.resp.DestroyCertainItem;
import cn.taqu.account.vo.resp.FreezeToastResp;

import java.util.List;

/**
 * 注销相关
 *
 * <AUTHOR>
 * @date 2025/4/7 15:52
 */
public interface DestroyService {

    /**
     * 原因配置
     *
     * @param uuid
     * @return
     */
    List<DestroyReasonConfig> reasonConfig(String uuid);

    /**
     * 注销原因提交
     *
     * @param req
     */
    void submitReason(SetDestroyReasonReq req);

    /**
     * 冻结提示
     *
     * @param uuid
     * @return
     */
    FreezeToastResp freezeToast(String uuid);

    /**
     * 确认页面
     * @param accountUuid
     * @return
     */
    List<DestroyCertainItem> certain(String accountUuid);
}
