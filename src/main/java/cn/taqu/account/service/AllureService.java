package cn.taqu.account.service;

import cn.taqu.account.common.AllureSceneEnum;
import cn.taqu.account.dto.AllureCacheDTO;
import cn.taqu.account.vo.resp.AllureResp;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

/**
 * 新引力
 *
 * <AUTHOR>
 * @date 2024/11/25 10:20 上午
 */
public interface AllureService {

    /**
     * 获取吸引力
     */
    AllureResp getAllure(String accountUuid);

    AllureResp getAllureV2(String accountUuid);

    /**
     * 刷新吸引力
     *
     * @desc 底下方法会校验用户版本refreshAllureValidVersion
     */
    AllureCacheDTO refreshAllure(String accountUuid);

    /**
     * 吸引力刷新 v2
     * @param uuid   用户
     * @param scene  场景
     * @return       当前项打分（非总分）
     */
    CompletableFuture<Integer> refreshAllureV2(String uuid, AllureSceneEnum scene, String content);

    /**
     * 算法评分回调
     * @param uuid
     * @param scene
     * @param score
     */
    void algorithmScoreCallback(String uuid, AllureSceneEnum scene, BigDecimal score);

    /**
     * 刷新吸引力(校验版本)
     */
    AllureCacheDTO refreshAllureValidVersion(String accountUuid);

    /**
     * 删除吸引力缓存
     *
     * @param uuid
     * @desc 提供给测试用
     */
    void deleteAllureCache(String uuid);

}
