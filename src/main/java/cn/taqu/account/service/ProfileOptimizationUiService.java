package cn.taqu.account.service;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.dto.ClonedPlanformAppVersionDto;
import cn.taqu.account.dto.InfoFiledCacheDTO;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * https://o15vj1m4ie.feishu.cn/wiki/VFsXwMYgeixaV6kAShPcrNTXnkh
 * 私信_Profile优化一些公共处理的类
 *
 * <AUTHOR>
 * 2024年11月29日下午4:38:27
 */
@Slf4j
@Service
public class ProfileOptimizationUiService {

    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AbTestService abTestService;
    @Autowired
    private AccountBaseInfoManager accountBaseInfoManager;

    /**
     * 服务端的请求，需要查询缓存数据
     *
     * @param accountUuid
     * @return 是否满足条件 true-是；false-否
     */
    public boolean isProfileOptimizationUiTargetAccountByServer(String accountUuid) {
        ClonedPlanformAppVersionDto clonedPlanformAppVersionDto = accountsInfoService.getAccountClonedPlanformAppVersion(accountUuid);
        if (!clonedPlanformAppVersionDto.isValid()) {
            // 无效数据
            log.warn("基础资料不全, accountUuid={}", accountUuid);
            return false;
        }

        return isProfileExp(accountUuid);
    }

    /**
     * 实验判断
     *
     * @param accountUuid
     * @return
     */
    public boolean isProfileExp(String accountUuid) {
        // 需要查询缓存数据，不获取网关信息，规避soa链路上数据丢失以及城市ab实验不准问题。
        InfoFiledCacheDTO info = accountBaseInfoManager.getInfoByUuid(accountUuid,
                new String[]{UuidInfoField.CITY_ID, UuidInfoField.LOGIN_APPCODE, UuidInfoField.LOGIN_CLONED, UuidInfoField.PLATFORM_ID, UuidInfoField.APP_VERSION});
        if (info == null) {
            return false;
        }

        String cityId = info.getCityId();
        Integer appcode = info.getLoginAppcode();
        Integer cloned = info.getLoginCloned();
        Integer platformId = info.getPlatformId();
        Long appVersion = info.getAppVersion();

        // 年轻包（闪糖）实验是本地固化的，默认走true。
        if (ClonedGroupEnum.YOUNG_GROUP.contains(cloned)) {
            return true;
        }

        boolean isExp;
        // 主包城市实验（目前只需要处理马甲包：1）
        if (Objects.equals(cloned, 1)) {
            isExp = isProfileMainGroup(platformId, appVersion);
        } else {
            // 女包ab实验
            isExp = isProfileGirlGroup(accountUuid, cityId, appcode, cloned, platformId, appVersion);
        }
        LogUtil.info4Gray("profile实验判断,isExp:{}, uuid:{},cityId:{},appcode:{},cloned:{},platformId:{},appVersion:{}", isExp, accountUuid, cityId, appcode, cloned, platformId, appVersion);
        return isExp;
    }

    /**
     * 主包资料卡城市实验处理
     *
     * <p>
     * 条件：新版 + AB实验命)中
     *
     * @param platformId
     * @param appVersion
     * @return
     */
    public boolean isProfileMainGroup(Integer platformId, Long appVersion) {
        return VersionSwitchService.SWITCH_20241129.isGeVersion(platformId, appVersion);
        // 2025年5月2期固化了
        //abTestService.isProfileCityExp(uuid, cityId, appcode, cloned, platformId, appVersion);
    }

    /**
     * 女包资料卡ab实验处理
     *
     * <p>
     * 条件：新版 + AB实验命)中
     *
     * @param uuid
     * @param cloned
     * @param platformId
     * @param appVersion
     * @return
     */
    public boolean isProfileGirlGroup(String uuid, String cityId, Integer appcode, Integer cloned, Integer platformId, Long appVersion) {
        return VersionSwitchService.SWITCH_20241129.isGeVersion(platformId, appVersion)
                && abTestService.isProfileOptimizationUiExperimentalGroup(uuid, cloned);
    }
}
