package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.vo.CommonPage;

/**
 * 用户日志相关查询Service
 * <AUTHOR>
 * 2017年5月5日 下午3:54:17
 */
@Service
@Transactional
public class AccountsLogService {

	@Autowired
	private AccountsSetNameLogService accountsSetNameLogService;

	public CommonPage getNicknameLog(Integer pageNumber, Integer pageSize, String account_uuid) {
		return accountsSetNameLogService.getNicknameLog(pageNumber, pageSize, account_uuid);
	}

}
