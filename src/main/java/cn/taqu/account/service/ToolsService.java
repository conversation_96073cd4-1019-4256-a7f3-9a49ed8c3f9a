package cn.taqu.account.service;

import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.account.bo.SwitchOnOff;
import cn.taqu.account.constant.CommConst;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 工具类型service，主要用于其他service中一些数据的通用处理
 * @author:<PERSON><PERSON><PERSON>
 * @date:2017年3月8日 上午10:52:30
 */
@Slf4j
public class ToolsService {
    
    /**
     * etcd 活体底图是否上传avatar bucket
     */
    public static SwitchOnOff uploadAvatarBucketLiveFaceSwitch = new SwitchOnOff("true", "true");
    
    /**
     * etcd 实名底图是否上传avatar bucket
     */
    public static SwitchOnOff uploadAvatarBucketCertificationSwitch = new SwitchOnOff("true", "true");
    
    /**
     * etcd 底图是否使用私有地址
     */
    public static SwitchOnOff useAccountPrivateBucketSwitch = new SwitchOnOff("false", "true");
    
    /**
     * etcd accounts_location_info 是否使用加密字段
     */
    public static SwitchOnOff accountsLocationInfoSwitchEncryption = new SwitchOnOff("false", "false");
    
    /**
     * etcd accounts_location_info 是否写入原字段
     */
    public static SwitchOnOff accountsLocationInfoSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd login_action_info 是否使用加密字段
     */
    public static SwitchOnOff loginActionInfoSwitchEncryption = new SwitchOnOff("false", "false");
    
    /**
     * etcd login_action_info 是否写入原字段
     */
    public static SwitchOnOff loginActionInfoSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd register_action_info 是否使用加密字段
     */
    public static SwitchOnOff registerActionInfoSwitchEncryption = new SwitchOnOff("false", "false");
    
    /**
     * etcd register_action_info 是否写入原字段
     */
    public static SwitchOnOff registerActionInfoSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd logout_action_info_log 是否写入原字段
     */
    public static SwitchOnOff logoutActionInfoLogSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_certification_change_log 是否使用加密字段
     */
    public static SwitchOnOff accountsCertificationChangeLogSwitchEncryption = new SwitchOnOff("false", "false");
    
    /**
     * etcd accounts_certification_change_log 是否写入原字段
     */
    public static SwitchOnOff accountsCertificationChangeLogSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd reregister_whitelist 是否使用加密字段
     */
    public static SwitchOnOff reregisterWhitelistSwitchEncryption = new SwitchOnOff("false", "false");
    
    /**
     * etcd reregister_whitelist 是否写入原字段
     */
    public static SwitchOnOff reregisterWhitelistSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd unbind_whitelist 是否使用加密字段
     */
    public static SwitchOnOff unbindWhitelistSwitchEncryption = new SwitchOnOff("false", "false");
    
    /**
     * etcd unbind_whitelist 是否写入原字段
     */
    public static SwitchOnOff unbindWhitelistSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_certification 是否使用加密字段
     */
    public static SwitchOnOff accountsCertificationSwitchEncryption = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_certification 是否写入原字段
     */
    public static SwitchOnOff accountsCertificationSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_certification.reward_account 是否使用加密字段
     */
    public static SwitchOnOff rewardAccountSwitchEncryption = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_chat_certification 是否使用加密字段
     */
    public static SwitchOnOff accountsChatCertificationSwitchEncryption = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_chat_certification 是否写入原字段
     */
    public static SwitchOnOff accountsChatCertificationSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_destroy_log 是否使用加密字段
     */
    public static SwitchOnOff accountsDestroyLogSwitchEncryption = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts_destroy_log 是否写入原字段
     */
    public static SwitchOnOff accountsDestroyLogSwitchWrite = new SwitchOnOff("true", "true");
    
    /**
     * etcd accounts 是否使用加密字段
     */
    public static SwitchOnOff accountsSwitchEncryption = new SwitchOnOff("false", "false");
    
    /**
     * etcd accounts 是否写入原字段
     */
    public static SwitchOnOff accountsSwitchWrite = new SwitchOnOff("true", "true");
    
    
    public static void initUploadAvatarBucketLiveFaceSwitch(String json) {
        log.info("initUploadAvatarBucketLiveFaceSwitch, json={}", json);
        Map<String, String> map = JsonUtils.stringToObject(json, new TypeReference<Map<String, String>>() {});
        if(map != null) {
            uploadAvatarBucketLiveFaceSwitch = new SwitchOnOff(map.get("online"), map.get("gray"));
        }
    }
    
    public static void initUploadAvatarBucketCertificationSwitch(String json) {
        log.info("initUploadAvatarBucketCertificationSwitch, json={}", json);
        Map<String, String> map = JsonUtils.stringToObject(json, new TypeReference<Map<String, String>>() {});
        if(map != null) {
            uploadAvatarBucketCertificationSwitch = new SwitchOnOff(map.get("online"), map.get("gray"));
        }
    }
    
    public static void initUseAccountPrivateBucketSwitch(String json) {
        log.info("initUseAccountPrivateBucketSwitch, json={}", json);
        Map<String, String> map = JsonUtils.stringToObject(json, new TypeReference<Map<String, String>>() {});
        if(map != null) {
            useAccountPrivateBucketSwitch = new SwitchOnOff(map.get("online"), map.get("gray"));
        }
    }
    
    public static void accountsLocationInfoSwitchEncryption(String json) {
        log.info("accountsLocationInfoSwitchEncryption, json={}", json);
        accountsLocationInfoSwitchEncryption = initSwitch(accountsLocationInfoSwitchEncryption, json);
    }
    
    public static void accountsLocationInfoSwitchWrite(String json) {
        log.info("accountsLocationInfoSwitchWrite, json={}", json);
        accountsLocationInfoSwitchWrite = initSwitch(accountsLocationInfoSwitchWrite, json);
    }
    
    public static void loginActionInfoSwitchEncryption(String json) {
        log.info("loginActionInfoSwitchEncryption, json={}", json);
        loginActionInfoSwitchEncryption = initSwitch(loginActionInfoSwitchEncryption, json);
    }
    
    public static void loginActionInfoSwitchWrite(String json) {
        log.info("loginActionInfoSwitchWrite, json={}", json);
        loginActionInfoSwitchWrite = initSwitch(loginActionInfoSwitchWrite, json);
    }
    
    public static void registerActionInfoSwitchEncryption(String json) {
        log.info("registerActionInfoSwitchEncryption, json={}", json);
        registerActionInfoSwitchEncryption = initSwitch(registerActionInfoSwitchEncryption, json);
    }
    
    public static void registerActionInfoSwitchWrite(String json) {
        log.info("registerActionInfoSwitchWrite, json={}", json);
        registerActionInfoSwitchWrite = initSwitch(registerActionInfoSwitchWrite, json);
    }
    
    public static void logoutActionInfoLogSwitchWrite(String json) {
        log.info("logoutActionInfoLogSwitchWrite, json={}", json);
        logoutActionInfoLogSwitchWrite = initSwitch(logoutActionInfoLogSwitchWrite, json);
    }
    
    public static void accountsCertificationChangeLogSwitchEncryption(String json) {
        log.info("accountsCertificationChangeLogSwitchEncryption, json={}", json);
        accountsCertificationChangeLogSwitchEncryption = initSwitch(accountsCertificationChangeLogSwitchEncryption, json);
    }
    
    public static void accountsCertificationChangeLogSwitchWrite(String json) {
        log.info("accountsCertificationChangeLogSwitchWrite, json={}", json);
        accountsCertificationChangeLogSwitchWrite = initSwitch(accountsCertificationChangeLogSwitchWrite, json);
    }
    
    public static void reregisterWhitelistSwitchEncryption(String json) {
        log.info("reregisterWhitelistSwitchEncryption, json={}", json);
        reregisterWhitelistSwitchEncryption = initSwitch(reregisterWhitelistSwitchEncryption, json);
    }
    
    public static void reregisterWhitelistSwitchWrite(String json) {
        log.info("reregisterWhitelistSwitchWrite, json={}", json);
        reregisterWhitelistSwitchWrite = initSwitch(reregisterWhitelistSwitchWrite, json);
    }
    
    public static void unbindWhitelistSwitchEncryption(String json) {
        log.info("unbindWhitelistSwitchEncryption, json={}", json);
        unbindWhitelistSwitchEncryption = initSwitch(unbindWhitelistSwitchEncryption, json);
    }
    
    public static void unbindWhitelistSwitchWrite(String json) {
        log.info("unbindWhitelistSwitchWrite, json={}", json);
        unbindWhitelistSwitchWrite = initSwitch(unbindWhitelistSwitchWrite, json);
    }
    
    public static void accountsCertificationSwitchEncryption(String json) {
        log.info("accountsCertificationSwitchEncryption, json={}", json);
        accountsCertificationSwitchEncryption = initSwitch(accountsCertificationSwitchEncryption, json);
    }
    
    public static void accountsCertificationSwitchWrite(String json) {
        log.info("accountsCertificationSwitchWrite, json={}", json);
        accountsCertificationSwitchWrite = initSwitch(accountsCertificationSwitchWrite, json);
    }
    
    public static void rewardAccountSwitchEncryption(String json) {
        log.info("accountsCertificationSwitchEncryption, json={}", json);
        rewardAccountSwitchEncryption = initSwitch(rewardAccountSwitchEncryption, json);
    }
    
    public static void accountsChatCertificationSwitchEncryption(String json) {
        log.info("accountsChatCertificationSwitchEncryption, json={}", json);
        accountsChatCertificationSwitchEncryption = initSwitch(accountsChatCertificationSwitchEncryption, json);
    }
    
    public static void accountsChatCertificationSwitchWrite(String json) {
        log.info("accountsChatCertificationSwitchWrite, json={}", json);
        accountsChatCertificationSwitchWrite = initSwitch(accountsChatCertificationSwitchWrite, json);
    }
    
    public static void accountsDestroyLogSwitchEncryption(String json) {
        log.info("accountsDestroyLogSwitchEncryption, json={}", json);
        accountsDestroyLogSwitchEncryption = initSwitch(accountsDestroyLogSwitchEncryption, json);
    }
    
    public static void accountsDestroyLogSwitchWrite(String json) {
        log.info("accountsDestroyLogSwitchWrite, json={}", json);
        accountsDestroyLogSwitchWrite = initSwitch(accountsDestroyLogSwitchWrite, json);
    }
    
    public static void accountsSwitchEncryption(String json) {
        log.info("accountsSwitchEncryption, json={}", json);
        accountsSwitchEncryption = initSwitch(accountsSwitchEncryption, json);
    }
    
    public static void accountsSwitchWrite(String json) {
        log.info("accountsSwitchWrite, json={}", json);
        accountsSwitchWrite = initSwitch(accountsSwitchWrite, json);
    }
    
    /**
     * 都支持放量
     * 
     * @param switchOnOff
     * @param json
     * @return
     */
    private static SwitchOnOff initSwitch(SwitchOnOff switchOnOff, String json) {
        Map<String, String> map = JsonUtils.stringToObject(json, new TypeReference<Map<String, String>>() {});
        if(map != null) {
            return new SwitchOnOff(map.get("online"), map.get("gray"),  MapUtils.getBoolean(map, "onlineDefault", null),
                MapUtils.getIntValue(map, "partTotal", 0), MapUtils.getIntValue(map, "partAllow", 0));
        }
        return switchOnOff;
    }

    public static String URL_PRE_DOMAIN = "https://${bucket}.jiaoliuqu.com/";   //转存到七牛云后生成的url链接前缀

	/**
	 * 提取url中的uri，当version为空或0时原样返回，否则返回uri
	 * @param url
	 * @param version
	 */
	static String urlToUri(String url, String version) {
		if(StringUtils.isBlank(url)) {
			return url;
		}
		
		if(StringUtils.isBlank(version) || Objects.equals("0", version)) {
			return StringUtils.removeStart(url, "/");
		}

		String uri = url;
		try {
			URL _url = new URL(url);
			uri = _url.getFile();
		} catch (Exception e) {
			uri = url;
		}
		return StringUtils.removeStart(uri, "/");
	}

	static String getFirstNoBlankValue(List<String> values) {
		if(values == null) {
			return null;
		}

		for(String value : values) {
			if(StringUtils.isNotBlank(value)) {
				return value;
			}
		}

		return null;
	}

//	private static final Pattern TICKET_PATTERN = Pattern.compile("^\\w{32}$");
//	@Deprecated  // 这个不要再调啦
//	static boolean isValidTicket(String ticket) {
//		if(StringUtils.isBlank(ticket)) {
//			return false;
//		}
//		return TICKET_PATTERN.matcher(ticket).matches();
//	}

	/**
	 * @param appcode
	 * @return
	 */
	public static Integer normalizedAppcode(Integer appcode) {
		//现在只会有1
		return 1;
	}

	public static String parseAatarFromAppAvatar(String appAvatarString) {
		String finalAvatar = appAvatarString;
		//客户端上传上来的图片有可能是直接一个url也可能是{"height":"306","img_name":"taqu_android_post_01520513232146.jpg","width":"306"}这种格式的
		if(StringUtils.isNotBlank(appAvatarString)){
			try{
				Map<String, String> map = JsonUtils.mapper().readValue(JsonUtils.toEmptyJsonObject(appAvatarString), new TypeReference<Map<String, String>>() {});
				if(map != null && map.get("img_name") != null){
					finalAvatar = map.get("img_name");
				} else {
					log.warn("app传入头像处理错误：defined_avatar={}", appAvatarString);
				}
			}catch(Exception e){
			    log.warn("app传入头像处理异常：defined_avatar={}",appAvatarString,e);
			}
		}
		return finalAvatar;
	}

	public static int calAgeFromBirth(long birth) {
		String[] nowDateArray = DateUtil.dateToString10(new Date()).split("-");
		String[] birthArray = DateUtil.dateToString10(DateUtil.fromSecond(birth)).split("-");

		int age = Integer.parseInt(nowDateArray[0]) - Integer.parseInt(birthArray[0]);
		if(age <= 0) {
			return 0;
		}

		int nowMonth = Integer.parseInt(nowDateArray[1]);
		int birthMonth = Integer.parseInt(birthArray[1]);
		if(nowMonth < birthMonth) {
			return age - 1;
		}
		if(nowMonth > birthMonth) {
			return age;
		}

		int nowDay = Integer.parseInt(nowDateArray[2]);
		int birthDay = Integer.parseInt(birthArray[2]);
		if(nowDay < birthDay) {
			return age - 1;
		}
		return age;
	}
	
    /**
     * 获取私有访问地址
     * 
     * @param basePic
     * @return
     */
    public static String getPrivateBasePic(String basePic) {
        // 使用私有空间开启
        if(ToolsService.useAccountPrivateBucketSwitch.isOn(true)) {
            basePic = addAccountPrivateUrlPreDomain(basePic);
            return SoaService.privateDownloadUrl(CommConst.PRIVATE_AVATAR_BUCKET_TYPE, basePic, CommConst.APPCODE_TAQU, 1800L); 
        }else {
            return addPhotoUrlPreDomain(basePic);
        }
    }
    
    /**
     * 拼接图片地址
     * 
     * @param photoUrl
     * @param bucket
     * @return
     */
    private static String addPhotoUrlPreDomain(String photoUrl, String bucket) {
        if (StringUtils.isBlank(photoUrl)) {
            return "";
        }
        photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
        String prefix = URL_PRE_DOMAIN.replace("${bucket}", bucket);
        photoUrl = prefix + photoUrl;
        return photoUrl;
    }
	
    /**
     * 给图片加域名前缀
     *
     * @param photoUrl
     * @return
     */
    public static String addPhotoUrlPreDomain(String photoUrl) {
        return addPhotoUrlPreDomain(photoUrl, CommConst.AVATAR_BUCKET);
    }
    
    /**
     * 给图片加域名前缀
     *
     * @param photoUrl
     * @return
     */
    public static String addAccountPrivateUrlPreDomain(String photoUrl) {
        // 使用私有空间开启
        if(ToolsService.useAccountPrivateBucketSwitch.isOn(true)) {
            return addPhotoUrlPreDomain(photoUrl, CommConst.PRIVATE_AVATAR_SRC_BUCKET);
        }else {
            return addPhotoUrlPreDomain(photoUrl);
        }
    }
    
    /**
     * 判断使用哪个域名生成可访问的图片地址
     * 
     * @param url
     * @return
     */
    public static String getCanUsedUrlByHost(String url) {
        if(url.contains(CommConst.AVATAR_BUCKET)) {
            return addPhotoUrlPreDomain(url);
        }else if(url.contains(CommConst.PRIVATE_AVATAR_SRC_BUCKET)) {
            return getPrivateBasePic(url);
        }else {
            log.warn("未匹配到域名，使用默认域名，url={}", url);
            return addPhotoUrlPreDomain(url);
        }
    }
    
}
