package cn.taqu.account.service;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.config.biz.RetrieveAccountConfig;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.RiskAccountStatusCheckReqDTO;
import cn.taqu.account.dto.RiskPictureCheckReqDTO;
import cn.taqu.account.dto.RiskPictureCheckRespDTO;
import cn.taqu.account.utils.LimitTimeUtils;
import cn.taqu.account.vo.RetrieveAccountStatusVO;
import cn.taqu.account.vo.RetrieveMobileInfoVO;
import cn.taqu.account.vo.RetrieveRiskUrlVO;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.ValidateUtil;
import com.taqu.mp.account.client.MPAccountClient;
import com.taqu.mp.account.constant.SnakeCaseBizConstant;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 通过手机号找回账号
 */
@Service
public class MobileRetrieveService {
    private static Logger logger = LoggerFactory.getLogger(AccountsPersonalInfoService.class);

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private MPAccountClient mpAccountClient;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private RiskPushService riskPushService;
    @Autowired
    private AccountsCertificationService accountsCertificationService;
    @Autowired
    private RiskSafeStatusService riskSafeStatusService;

    /**
     * 手机号校验【用于手机找回场景】`
     *
     * @param mobile
     * @return
     */
    public RetrieveRiskUrlVO validMobileForRetrieve(String mobile) {
        RetrieveRiskUrlVO respVo = new RetrieveRiskUrlVO();
        respVo.setIsPass(true);

        // 校验用户手机号码情况
        String uuid = validMobileStatus(mobile);
        // 手机号阈值校验
        validMobileThreshold(mobile);
        // ip阈值校验
        validIpThreshold();
        // 单独token限制
        validTokenThreshold();
        // token+uuid阈值校验
        validDeviceAndThreshold(uuid);
        // 风控校验
        String link = validRisk(mobile);

        respVo.setLink(link);
        return respVo;
    }

    private void validTokenThreshold() {
        String token = SoaBaseParams.fromThread().getToken();
        if (StringUtils.isBlank(token)) {
            throw new ServiceException(CodeStatus.DESTROYED_GET_INFO_ERR);
        }

        // 获取限制模式
        LimitTimeUtils.LimitMode limitMode = LimitTimeUtils.of(RetrieveAccountConfig.retrieveConfig.getTokenLimitMode());
        // 业务key
        String timeKey = limitMode.timeKey();
        // 限制阈值
        Pair<Integer, TimeUnit> leaseTime = limitMode.leaseTime(RetrieveAccountConfig.retrieveConfig.getTokenLimitThreshold());

        String key = RedisKeyConstant.RETRIEVE_TOKEN_LIMIT.setArg(token, timeKey);
        Long count = accountStringRedisTemplate.opsForValue().increment(key, 1);
        // 找回次数限制
        if (count > leaseTime.getLeft()) {
            logger.warn("token找回上线:token={}, count:{}, limit:{}", token, count, leaseTime.getLeft());
            throw new ServiceException("今日找回已达上限");
        }
        // 设置过期时间
        if (count <= 1) {
            accountStringRedisTemplate.expire(key, leaseTime.getLeft(), leaseTime.getRight());
        }
    }

    /**
     * ip阈值校验
     */
    private void validIpThreshold() {
        String ip = SoaBaseParams.fromThread().getIp();
        if (StringUtils.isBlank(ip)) {
            throw new ServiceException(CodeStatus.DESTROYED_GET_INFO_ERR);
        }

        // 获取限制模式
        LimitTimeUtils.LimitMode limitMode = LimitTimeUtils.of(RetrieveAccountConfig.retrieveConfig.getIpLimitMode());
        // 业务key
        String timeKey = limitMode.timeKey();
        // 限制阈值
        Pair<Integer, TimeUnit> leaseTime = limitMode.leaseTime(RetrieveAccountConfig.retrieveConfig.getIpLimitThreshold());

        String key = RedisKeyConstant.RETRIEVE_ACCOUNT_IP_LIMIT.setArg(ip, timeKey);
        Long count = accountStringRedisTemplate.opsForValue().increment(key, 1);
        // 找回次数限制
        if (count > leaseTime.getLeft()) {
            logger.warn("ip找回上线:ip={}, count:{}, limit:{}", ip, count, leaseTime.getLeft());
            throw new ServiceException("今日找回已达上限");
        }
        // 设置过期时间
        if (count <= 1) {
            accountStringRedisTemplate.expire(key, leaseTime.getLeft(), leaseTime.getRight());
        }
    }

    /**
     * 手机号阈值校验
     *
     * @param mobile
     */
    private void validMobileThreshold(String mobile) {
        // 获取限制模式
        LimitTimeUtils.LimitMode limitMode = LimitTimeUtils.of(RetrieveAccountConfig.retrieveConfig.getMobileLimitMode());
        // 业务key
        String timeKey = limitMode.timeKey();
        // 限制阈值
        Pair<Integer, TimeUnit> leaseTime = limitMode.leaseTime(RetrieveAccountConfig.retrieveConfig.getMobileLimitThreshold());

        String key = RedisKeyConstant.RETRIEVE_ACCOUNT_MOBILE_LIMIT.setArg(mobile, timeKey);
        Long count = accountStringRedisTemplate.opsForValue().increment(key, 1);
        logger.info("账号手机限制:{}, {}", mobile, key);
        // 找回次数限制
        if (count > leaseTime.getLeft()) {
            throw new ServiceException("今日找回已达上限");
        }
        // 设置过期时间
        if (count <= 1) {
            accountStringRedisTemplate.expire(key, leaseTime.getLeft(), leaseTime.getRight());
        }
    }

    /**
     * 风控校验
     *
     * @param
     */
    private String validRisk(String mobile) {
        String token = SoaBaseParams.fromThread().getToken();
        if (StringUtils.isBlank(token)) {
            throw new ServiceException(CodeStatus.DESTROYED_GET_INFO_ERR);
        }
        String bizId = getRiskBizId(mobile, token);
        RiskPictureCheckReqDTO req = new RiskPictureCheckReqDTO();
        req.setBizId(bizId);
        req.setIp(SoaBaseParams.fromThread().getIp());
        RiskPictureCheckRespDTO resp = riskPushService.checkRetrieveMobile(req);
        if (resp == null) {
            throw new ServiceException(CodeStatus.COMMON_ERROR_CODE);
        }

        // 判断看看是否需要校验
        if (RiskPictureCheckRespDTO.PermitStatus.NEED_VALID.match(resp.getPermitStatus())) {
            return resp.getCaptchaUrl();
        }

        if (RiskPictureCheckRespDTO.PermitStatus.PASS.match(resp.getPermitStatus())) {
            return "";
        }

        // 抛出提示
        if (Objects.equals(resp.getIsToast(), 1)) {
            throw new ServiceException(resp.getToastMsg());
        }

        throw new ServiceException(CodeStatus.COMMON_ERROR_CODE);
    }

    /**
     * 获取风控id
     *
     * @param mobile
     * @param token
     * @return
     */
    private String getRiskBizId(String mobile, String token) {
        Integer cloned = Optional.ofNullable(SoaBaseParams.fromThread().getCloned()).orElse(1);
        return Md5Util.encode(cloned + mobile + token);
    }

    /**
     * 校验风控图片结果
     *
     * @param
     */
    private Boolean validRiskPictureResult(String mobile) {
        String token = SoaBaseParams.fromThread().getToken();
        if (StringUtils.isBlank(token)) {
            return false;
        }

        String bizId = getRiskBizId(mobile, token);
        RiskPictureCheckReqDTO req = new RiskPictureCheckReqDTO();
        req.setBizId(bizId);
        req.setIp(SoaBaseParams.fromThread().getIp());

        RiskPictureCheckRespDTO resp = riskPushService.checkRetrieveMobile(req);
        if (resp == null) {
            logger.warn("【找回手机】校验风控图片结果获取异常, {}", mobile);
            return false;
        }

        // 通过校验
        if (RiskPictureCheckRespDTO.PermitStatus.PASS.match(resp.getPermitStatus())) {
            return true;
        }

        // 抛出提示
        if (Objects.equals(resp.getIsToast(), 1)) {
            throw new ServiceException(resp.getToastMsg());
        }

        return false;
    }

    /**
     * 实名校验
     */
    private Pair<Boolean, String> validRealName(String uuid) {
        // 获取用户信息
        Map<String, Object> userMap = accountsInfoService.getInfoByUuid(new String[]{uuid}, new String[]{"uuid", "account_name", UuidInfoField.ZHIMA_CERTIFICATION}, "1", false, false).get(uuid);
        boolean realName = "1".equals(MapUtils.getString(userMap, UuidInfoField.ZHIMA_CERTIFICATION));
        String nickname = MapUtils.getString(userMap, "account_name", "");
        // 名字加密
        if (nickname.length() == 2) {
            nickname = nickname.substring(0, 1) + "*";
        } else if (nickname.length() >= 2) {
            nickname = nickname.substring(0, 1) + "*" + nickname.substring(nickname.length() - 1);
        }

        // 实名需要再次校验地图
        if (realName) {
            // 底图缺失
            String photo = accountsCertificationService.getCertificationPhotoUrlByUuid(uuid);
            if (StringUtils.isBlank(photo)) {
                logger.info("实名底图缺失:{}", uuid);
                realName = false;
            }
        }
        return Pair.of(realName, nickname);
    }

    /**
     * 校验手机状态
     *
     * @param mobile
     */
    private String validMobileStatus(String mobile) {
        if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
            throw new ServiceException(CodeStatus.MOBILE_INVALID);
        }

        Map<String, Object> mobileMap = mpAccountClient.queryUser(mobile, 2);
        boolean needRegister = "1".equals(MapUtils.getString(mobileMap, SnakeCaseBizConstant.NEED_REGISTER));
        if (needRegister) {
            logger.warn("用户手机找回，手机号不存在:{}", mobile);
            throw new ServiceException("请核实信息");
        }

        return MapUtils.getString(mobileMap, "uuid");
    }

    /**
     * 设备、频控校验
     */
    private void validDeviceAndThreshold(String uuid) {
        String token = validDevice();

        // 获取限制模式
        LimitTimeUtils.LimitMode limitMode = LimitTimeUtils.of(RetrieveAccountConfig.retrieveConfig.getTokenUuidLimitMode());
        // 业务key
        String timeKey = limitMode.timeKey();
        // 限制阈值
        Pair<Integer, TimeUnit> leaseTime = limitMode.leaseTime(RetrieveAccountConfig.retrieveConfig.getTokenUuidLimitThreshold());

        String key = RedisKeyConstant.RETRIEVE_ACCOUNT_TOKEN_LIMIT.setArg(uuid, timeKey, token);
        Long count = accountStringRedisTemplate.opsForValue().increment(key, 1);
        // 找回次数限制
        if (count > leaseTime.getLeft()) {
            logger.warn("设备频控找回上线:token={}, count:{}, limit:{}", token, count, leaseTime.getLeft());
            throw new ServiceException("今日找回已达上限");
        }
        // 设置过期时间
        if (count <= 1) {
            accountStringRedisTemplate.expire(key, leaseTime.getLeft(), leaseTime.getRight());
        }
    }

    /**
     * 设备校验
     */
    private String validDevice() {
        String token = SoaBaseParams.fromThread().getToken();
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        if (StringUtils.isBlank(token) || cloned == null) {
            throw new ServiceException("设备异常，请重试");
        }

        return token;
    }

    /**
     * 图形校验是否通过【用于手机找回场景】
     *
     * @param mobile
     * @return
     */
    public RetrieveMobileInfoVO validPictureResultForRetrieve(String mobile) {
        // 校验用户手机号码情况
        String uuid = validMobileStatus(mobile);
        // 比对校验结果
        Boolean isPass = validRiskPictureResult(mobile);
        // 实名校验
        Pair<Boolean, String> isRealName = validRealName(uuid);

        RetrieveMobileInfoVO respVo = new RetrieveMobileInfoVO();
        respVo.setIsPass(isPass);
        if (!isPass) {
            logger.info("风控ip检测没过:{}", mobile);
            return respVo;
        }

        respVo.setUuid(uuid);
        respVo.setMobile(mobile);
        respVo.setIsRealName(isRealName.getLeft());
        respVo.setNickname(isRealName.getRight());
        return respVo;
    }


    /**
     * 状态校验
     *
     * @param uuid
     */
    private void validRiskStatus(String uuid) {
        RiskAccountStatusCheckReqDTO req = new RiskAccountStatusCheckReqDTO();
        req.setAccountUuid(uuid);
        req.setAppCode(SoaBaseParams.fromThread().getAppcode());
        req.setToken(SoaBaseParams.fromThread().getToken());
        req.setSafeId("ACCOUNT_RETRIEVAL");
        Boolean result = riskSafeStatusService.checkAccountStatus(req);
        if (result) {
            throw new ServiceException("账号违规中，请到期后重试");
        }
    }

    /**
     * 检查账号状态
     *
     * @param mobile
     * @return
     */
    public RetrieveAccountStatusVO validAccountStatus(String mobile, String uuid) {
        // 校验用户手机号码情况
        validMobileStatus(mobile);

        // 校验用户风控状态
        validRiskStatus(uuid);

        // 实名校验
        Pair<Boolean, String> isRealName = validRealName(uuid);

        RetrieveAccountStatusVO vo = new RetrieveAccountStatusVO();
        vo.setIsRealName(isRealName.getLeft());
        vo.setAccountRiskStatus(false);
        return vo;
    }
}
