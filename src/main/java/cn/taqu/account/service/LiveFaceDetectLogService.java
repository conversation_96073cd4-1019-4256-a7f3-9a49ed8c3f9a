package cn.taqu.account.service;

import cn.taqu.account.dao.LiveFaceDetectLogDao;
import cn.taqu.account.model.LiveFaceDetectLog;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 活体认证日志
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-18 13:36
 */
@Slf4j
@Service
public class LiveFaceDetectLogService {

    @Autowired
    private LiveFaceDetectLogDao liveFaceDetectLogDao;

    /**
     * 添加活体日志
     * @param accountUuid
     * @param basePhotoUrl
     * @param verifyPhotoUrl
     * @param liveFaceDetectType
     * @param liveFaceDetectStatus
     * @param orderNo
     * @param resultStatus
     * @param resultType
     * @param remark
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void addLiveFaceDetectLog(String accountUuid, String basePhotoUrl, String verifyPhotoUrl,
                                     Integer liveFaceDetectType, Integer liveFaceDetectStatus, String orderNo,
                                     Integer resultStatus, Integer resultType, String remark){
        Long now = DateUtil.currentTimeSeconds();
        // 去掉前缀
        basePhotoUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(basePhotoUrl);
        verifyPhotoUrl = AliyunLiveFaceDetectService.removePhotoUrlPreDomain(verifyPhotoUrl);
        
        LiveFaceDetectLog faceDetectLog = new LiveFaceDetectLog();
        faceDetectLog.setAccountUuid(accountUuid).setBasePhotoUrl(basePhotoUrl).setVerifyPhotoUrl(verifyPhotoUrl).setCreateTime(now).setUpdateTime(now)
                .setLiveFaceDetectType(liveFaceDetectType).setLiveFaceDetectStatus(liveFaceDetectStatus).setOrderNo(orderNo)
                .setResultStatus(resultStatus).setResultType(resultType).setRemark(remark);
        liveFaceDetectLogDao.merge(faceDetectLog);
    }
}
