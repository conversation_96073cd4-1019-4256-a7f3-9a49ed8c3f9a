package cn.taqu.account.service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.taqu.account.common.ModifyGrowScoreEnum;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RegStyle;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.protocol.SoaBaseParams;
import lombok.extern.slf4j.Slf4j;

/**
 * 社区迭代任务支持，某些行为推队列
 */
@Service
@Slf4j
public class ModifyGrowScoreService {


    public void sendModifyGrowScoreMq(String accountUuid, ModifyGrowScoreEnum modifyGrowScoreEnum, RegStyle regStyle) {
        //游客访问的时候accountUuid为空，不进行推送
        if(StringUtils.isEmpty(accountUuid)){
            return;
        }
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Map<String, Object> param = new HashMap<>();
        param.put("account_uuid", accountUuid);
        param.put("type", modifyGrowScoreEnum.getValue());
        param.put("platform_id",sbp.getPlatform_id());
        param.put("channel",sbp.getChannel());
        param.put("app_version",sbp.getApp_version());
        param.put("cloned",sbp.getCloned());

        //注册单独推送
        if(ModifyGrowScoreEnum.REGISTER.equals(modifyGrowScoreEnum)){
            //推送注册行为
            MqResponse regMqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_RESUME_COMPUTING_GROW_SCORE, param, null);
            if (regMqResponse.fail()) {
                log.error("推入resume_computing_grow_score,{}, 失败原因:{}-{}", param, regMqResponse.getCode(), regMqResponse.getMsg());
                return;
            }
            log.info("推入resume_computing_grow_score,用户uuid:{},用户行为:{}",accountUuid,ModifyGrowScoreEnum.REGISTER.getValue());
            modifyGrowScoreEnum = getPushRegStype(regStyle);
            //苹果注册暂不推送
            if(RegStyle.Apple.equals(regStyle)){
                return;
            }

            param.put("type", modifyGrowScoreEnum.getValue());

            //推送注册的类型
            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_RESUME_COMPUTING_GROW_SCORE, param, 30L);
            if (mqResponse.fail()) {
                log.error("推入resume_computing_grow_score,{}, 失败原因:{}-{}", param, mqResponse.getCode(), mqResponse.getMsg());
                return;
            }

            //********修复线上问题，如果是手机注册的，再推一条队列
            if(Objects.equals(modifyGrowScoreEnum.getValue(),ModifyGrowScoreEnum.BIND_MOBILE.getValue())){
                MqResponse mqResponse2 = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_COMPLETE_FOURM_TASK, param, 3L);
                if (mqResponse2.fail()) {
                    log.error("推入resume_computing_grow_score,{}, 失败原因:{}-{}", param, mqResponse2.getCode(), mqResponse2.getMsg());
                    return;
                }
            }
//            log.info("推入modify_grow_score队列成功,用户uuid:{},用户行为:{}",accountUuid,modifyGrowScoreEnum.getValue());
        }else {
            MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_MODIFY_GROW_SCORE, param, null);
            if (mqResponse.fail()) {
                log.error("推入modify_grow_score队列失败,{}, 失败原因:{}-{}", param, mqResponse.getCode(), mqResponse.getMsg());
                return;
            }
//            log.info("推入modify_grow_score队列成功,用户uuid:{},用户行为:{}",accountUuid,modifyGrowScoreEnum.getValue());
        }

    }

    public void sendModifyGrowScoreMq(String accountUuid, ModifyGrowScoreEnum modifyGrowScoreEnum){
        sendModifyGrowScoreMq(accountUuid, modifyGrowScoreEnum, null);
    }

    //根据regStyle获取推送的类型
    private ModifyGrowScoreEnum getPushRegStype(RegStyle regStyle){
        if(RegStyle.Mobile.equals(regStyle) || RegStyle.Mobile_Auto.equals(regStyle)
        || RegStyle.Flash.equals(regStyle) || RegStyle.Flash_Auto.equals(regStyle)){
            return ModifyGrowScoreEnum.BIND_MOBILE;
        }else if(RegStyle.QQ.equals(regStyle)){
            return ModifyGrowScoreEnum.BIND_QQ;
        }else if(RegStyle.WeChat.equals(regStyle)){
            return ModifyGrowScoreEnum.BIND_WECHAT;
        }else if(RegStyle.WeiBo.equals(regStyle)){
            return ModifyGrowScoreEnum.BIND_WEIBO;
        }else if(RegStyle.Apple.equals(regStyle)){
            return ModifyGrowScoreEnum.BIND_APPLE;
        }else {
            return ModifyGrowScoreEnum.UN_KNOWN;
        }
    }

}
