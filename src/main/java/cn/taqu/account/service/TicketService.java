package cn.taqu.account.service;

import cn.taqu.account.vo.GetByTicketVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.Md5Util;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * ticket相关
 * 
 * <AUTHOR>
 * @date 2021/02/25
 */
@Slf4j
@Service
public class TicketService {
	@Autowired
	private AccountsInfoService accountsInfoService;
	
	/**
	 * 根据username生成ticket,如果username为空，则随机生成(11位,数字+字母), 然后将username加上时间戳进行md5运算生成ticket
	 * @Title generateTicket
	 * @param username 用户名
	 * @return 返回生成的ticket
	 * <AUTHOR>
	 * @Date 2015年9月28日 下午1:40:53
	 */
	public String generateTicket(String username) {
		if (username == null || username.trim().length() == 0) {
			char[] nameChars = new char[11];
			Random random = new Random();
			for (int i = 0; i < nameChars.length; i++) {
				int sc = random.nextInt(36);
				nameChars[i] = (char) (sc >= 10 ? (sc - 10 + 97) : (sc + 48));
			}
			username = new String(nameChars);
		}
		String ticket = Md5Util.encode(username + System.currentTimeMillis());
		return ticket;
	}

	/**
	 * 校验ticket是否合法，合法返回1，不合法返回0
	 * @param ticketId
	 * @return
	 */
	@Deprecated
	public int isTicketValid(String ticketId) {
//		if(!ToolsService.isValidTicket(ticketId)) {
//			return 0;
//		}
		try {
			GetByTicketVo vo = accountsInfoService.getByTicket(ticketId);
			if(vo.getUuid()==null) {
				log.warn("empty uuid: {}, vo: {}", ticketId, vo);
				return 0;
			}
		} catch (ServiceException e) {
			log.warn("exception: {}", ticketId);
			return 0;
		}
		return 1;
	}
	
	/**
	 * 通过长度区分是uuid or ticket
	 * @param ticketId
	 * @return
	 */
    public static boolean isTicket(String ticketId) {
        boolean isTicket = false;
        
        if(StringUtils.isNotBlank(ticketId) && ticketId.length() > 20) {
            isTicket = true;
        }
        
        return isTicket;
    }

}
