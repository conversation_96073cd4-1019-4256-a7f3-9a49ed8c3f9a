package cn.taqu.account.service;

import cn.taqu.account.etcd.PulsarFactory;
import cn.taqu.account.event.EventConst;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.account.vo.LocationInfoVO;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static cn.taqu.account.event.EventConst.EVENT_NOTIFY_PULSAR_TOPIC;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Service
public class AsyncTrackingService {

    @Autowired
    private RedisLockUtil redisLockUtil;

    @Autowired
    private AccountsLocationInfoService accountsLocationInfoService;

    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;

    @Autowired
    private PulsarReporter reporter;

    @Component
    static class PulsarReporter implements PulsarFactory.ConfigListener {
        private volatile Producer<String> producer;

        public PulsarReporter(PulsarFactory factory) {
            this.producer = factory.producer(EventConst.resolveTopic(EVENT_NOTIFY_PULSAR_TOPIC));
            factory.addListener(this);
        }

        @Override
        public void onChange(PulsarFactory factory, PulsarFactory.Config config) {
            Producer<String> origin = producer;
            this.producer = factory.producer(EventConst.resolveTopic(EVENT_NOTIFY_PULSAR_TOPIC));
            origin.closeAsync();
        }

        void report(String uuid, Object body) {
            try {
                producer.newMessage()
                    .key(uuid)
                    .eventTime(System.currentTimeMillis())
                    .value(JsonUtils.objectToString(body))
                    .sendAsync().whenComplete((id, e) -> {
                        if (e != null) {
                            log.warn("Pulsar推送失败，topic = " + EventConst.resolveTopic(EVENT_NOTIFY_PULSAR_TOPIC), e);
                        }
                    });
            } catch (Exception e) {
                log.warn("Pulsar推送失败，topic = " + EventConst.resolveTopic(EVENT_NOTIFY_PULSAR_TOPIC), e);
            }
        }
    }



    @Async("asyncTrackingExecutor")
    public void trackingAccountLocationInfo(String accountUuid, String ip, String longitude, String latitude, Long cityId) {
        trackingLocationInfo(accountUuid, ip, longitude, latitude, cityId);
    }

    public Integer syncTrackingAccountLocationInfo(String accountUuid) {
        try {
            SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
            String ip = soaBaseParams.getIp();
            String latitude = soaBaseParams.getLatitude();
            String longitude = soaBaseParams.getLongitude();
            Integer city = soaBaseParams.getCity();

            Long cityId = city == null ? null : Long.valueOf(city);
            return trackingLocationInfo(accountUuid, ip, longitude, latitude, cityId);
        } catch (Exception e) {
            log.warn("syncTrackingAccountLocationInfo error, accountUuid:{}", accountUuid, e);
        }
        return null;
    }

    private Integer trackingLocationInfo(String accountUuid, String ip, String longitude, String latitude, Long cityId) {
        if (accountUuid == null) {
            return null;
        }
        String prefix = "trackingAccountLocationInfo_";
        if (redisLockUtil.lock(prefix + accountUuid, "1", 1000 * 60 * 5)) {
            LocationInfoVO locationInfoVO = accountsLocationInfoService.saveAccountLocationInfo(accountUuid, ip, longitude, latitude, cityId);
            if(locationInfoVO != null && StringUtils.isNotBlank(locationInfoVO.getCityId())) {
                return Integer.valueOf(locationInfoVO.getCityId());
            }
        }
        return null;
    }

    @Async
    public void pushUserLoginToActivityKafka(String uuid, Integer appcode, Integer cloned, String token, Integer platformId) {
        String topicName = "mp_taqu_event_notify";

        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("uuid", uuid);

        Map<String, Object> eventInfo = new HashMap<>();
        eventInfo.put("appcode", appcode);
        eventInfo.put("cloned", cloned);
        eventInfo.put("token", token);
        eventInfo.put("platform_id", platformId);

        Map<String, Object> eventBody = new HashMap<>();
        eventBody.put("tracer_id", SoaBaseParams.fromThread().getDistinctRequestId());
        eventBody.put("distinct_id", SoaBaseParams.fromThread().getDistinctRequestId());
        eventBody.put("event_code", "user_login");
        eventBody.put("event_ts", System.currentTimeMillis());
        eventBody.put("business_type", 99);
        eventBody.put("scene", 100);
        eventBody.put("user_info", userInfo);
        eventBody.put("event_info", eventInfo);

        kafkaSinkUtil.pushToOnlineBusinessKafka(true, topicName, eventBody);
        reporter.report(uuid, eventBody);
    }

    @Async
    public void pushRealPersonCertToActivityKafka(String uuid, Integer appcode, Integer cloned, String token, Integer platformId) {
        String topicName = "mp_taqu_event_notify";

        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("uuid", uuid);

        Map<String, Object> eventInfo = new HashMap<>();
        eventInfo.put("appcode", appcode);
        eventInfo.put("cloned", cloned);
        eventInfo.put("token", token);
        eventInfo.put("platform_id", platformId);

        Map<String, Object> eventBody = new HashMap<>();
        eventBody.put("tracer_id", SoaBaseParams.fromThread().getDistinctRequestId());
        eventBody.put("distinct_id", SoaBaseParams.fromThread().getDistinctRequestId());
        eventBody.put("event_code", "real_person_verify");
        eventBody.put("event_ts", System.currentTimeMillis());
        eventBody.put("business_type", 99);
        eventBody.put("user_info", userInfo);
        eventBody.put("event_info", eventInfo);

        kafkaSinkUtil.pushToOnlineBusinessKafka(true, topicName, eventBody);
        reporter.report(uuid, eventBody);
    }
}
