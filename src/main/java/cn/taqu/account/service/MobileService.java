package cn.taqu.account.service;

import cn.taqu.account.client.mp.MPAccountRequest;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RegStyle;
import cn.taqu.account.dao.AccountsInfoDao;
import cn.taqu.account.event.EventTrackReporter;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.Accounts;
import cn.taqu.account.model.AccountsInfo;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.ShowDialogUtil;
import cn.taqu.account.utils.mq.AntiSpamMqUtil;
import cn.taqu.account.vo.GetByTicketVo;
import cn.taqu.account.vo.LoginVo;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.StringUtil;
import cn.taqu.core.utils.ValidateUtil;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taqu.mp.account.client.MPAccountClient;
import com.taqu.mp.account.constant.CamelCaseBizConstant;
import com.taqu.mp.account.constant.SnakeCaseBizConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Service
@Transactional
public class MobileService {
    private static Logger logger = LoggerFactory.getLogger(AccountsPersonalInfoService.class);

    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private AntiSpamMqUtil antiSpamMqUtil;
    @Autowired
    private AntiSpamService antiSpamService;
    @Autowired
    private AccountsDestroyLogService accountsDestroyLogService;
    @Autowired
    private VerifyRecordService verifyRecordService;
    @Autowired
    private BuryService buryService;
    @Autowired
    private ModifyGrowScoreService modifyGrowScoreService;
    @Autowired
    private RiskService riskService;
    @Autowired
    private AccountsCertificationService accountsCertificationService;
    @Autowired
    private EventTrackReporter eventTrackReporter;

    /**
     * 手机验证码过期时间
     */
    @Value("${verifycode.validTime}")
    private Integer verifycodeValidTime;

    /**
     * 一天内同一个手机号最多允许发送短信条数
     */
    @Value("${verify.code.mobile.limit.daily}")
    private Integer verifyCodeMobileLimitDaily;

    /**
     * 一天内同一个设备号最多允许发送短信条数
     */
    @Value("${verify.code.device.limit.daily}")
    private Integer verifyCodeDeviceLimitDaily;

    /**
     * 重置密码时，允许发送的验证码的次数
     */
    @Value("${reset.send.verifycode.times.limit}")
    private Integer resetSendVerifycodeTimesLimit;

    /**
     * 注册时，允许发送的验证码的次数
     */
    @Value("${register.send.verifycode.times.limit}")
    private Integer registerSendVerifycodeTimesLimit;

    /**
     * 修改手机时，允许发送的验证码的次数
     */
    @Value("${modify.send.verifycode.times.limit}")
    private Integer modifySendVerifycodeTimesLimit;

    /**
     * 绑定手机时，允许发送的验证码的次数
     */
    @Value("${bind.send.verifycode.times.limit}")
    private Integer bindSendVerifycodeTimesLimit;

    /**
     * 登录时，允许发送的验证码次数
     */
    @Value("${login.send.verifycode.times.limit}")
    private Integer loginSendVerifycodeTimesLimit;

    /**
     * 语音认证手机时，允许发送的验证码的次数
     */
    @Value("${certification.send.verifycode.times.limit}")
    private Integer certificationSendVerifycodeTimesLimit;

    @Autowired
    private MPAccountClient mpAccountClient;

    /**
     * 查询手机号时是否使用加密的手机字段，需添加到etcd
     */
//    public static Boolean USE_ENCRYPT_MOBILE = false;

    /**
     * 接码时间限制范围
     */
    public static int SPACE_MINUTE = 10;

    public final static String THIRD_CONTINUE_LOGIN = "continueLogin";
    public final static String THIRD_REWARD_LOGIN = "rewardLogin";
    public final static String NO_REWARD_LOGIN = "noRewardLogin";

    public void sendVoiceCertifyCode(String ticketId) {
        if (StringUtils.isBlank(ticketId)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        GetByTicketVo ticketVo = accountsInfoService.getByTicketIfGuestExpire(ticketId);
        String mobile = accountsManager.getMobileByUuid(ticketVo.getAccount_uuid());

        if (StringUtils.isBlank(mobile)) {
            throw new ServiceException(CodeStatus.NEED_CHECK_MOBILE);
        }

        verifyRecordService.sendVerify(VerifyRecordService.Code.VOICE_VERIFY, VerifyRecordService.SmsType.VOICE_VCODE, mobile);
    }

    private int bindMobileCheck(String mobile, String continueLogin) {

        int checkStatus = this.checkMobileStatus(mobile, false);
        if (checkStatus == 1) {
            //如果接口传递了1进来,则走新流程,当手机号被绑定的时候,不报错,而是返回一个loginCode,让客户端获得这个code后再去登录该手机号
            // continuelogin = continueLogin ,走以下流程
            // 闪验手机->通过:绑定该手机号
            //        ->不通过:返回一个loginCode,可以去获取新的登录
            if (THIRD_CONTINUE_LOGIN.equals(continueLogin)) {
                String randomStr = Md5Util.generate10Key() + "";
                accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile), randomStr, 300, TimeUnit.SECONDS);
            } else {
                throw new ServiceException(CodeStatus.MOBILE_EXISTS);
            }

        }
        if (checkStatus == 2) {
            throw new ServiceException(CodeStatus.BIND_MOBILE_DESTROYED);
        }
        return checkStatus;
    }

    /**
     * @param uuid   唯一标识
     * @param mobile 手机号
     * @param code   从redis中获取的验证码
     * @return
     * @Title:updateInfo
     * @Description:手机关联操作中判断手机号能够使用后的操作
     * @author:huangyuehong
     * @Date:2015年9月18日 上午10:55:49
     */
    @Transactional(readOnly = false)
    public JsonResult updateInfoByRelated(String uuid, String mobile, String code, boolean isChange, String smid) {
        this.setAccountMobile(uuid, mobile, code, isChange, smid);

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("data", CodeStatus.MOBILE_RELATED_SUCCESS.value());
        resultMap.put("msg", CodeStatus.MOBILE_RELATED_SUCCESS.getReasonPhrase());
        resultMap.put("uuid", uuid);
        resultMap.put("account_uuid", uuid);
        accountsService.toMobilePlace(uuid, mobile);
        return JsonResult.success(resultMap);
    }

    /**
     * @param uuid      唯一标识
     * @param newMobile 新的手机号码
     * @param code      redis中获取的验证码
     * @return
     * @Title:updateInfoByChange
     * @Description:修改手机操作中判断手机号能够使用后的操作
     * @author:huangyuehong
     * @Date:2015年9月25日 下午2:21:50
     */
    @Transactional(readOnly = false)
    public void updateInfoByChange(String uuid, String newMobile, String code, String smid) {
        this.setAccountMobile(uuid, newMobile, code, Boolean.TRUE, smid);
        accountsService.toMobilePlace(uuid, newMobile);
    }

    /**
     * @param mobile         手机号
     * @param resetIfNoCheck 如果未被验证是否将手机号置为空, true:是;false:否;
     * @return 0:手机号可用; 1:已机号已被绑定; 2: 手机号已注销
     */
    @Transactional(readOnly = false)
    public int checkMobileStatus(String mobile, boolean resetIfNoCheck) {
        // 此处原来有问题，可能导致数据与缓存数据不一致。已修改 2021.04.30
        // 查询手机号关联的账号
        String accountUuid = accountsService.getUuidByMobile(mobile);
        // 如果有关联账号
        if (StringUtils.isNotBlank(accountUuid)) {
            // 逻辑巡查：到达这里的都是正常用户或者注销冷静期的，已注销的上面getUuidByMobile获取不到了

            // 查询所有账号的最新一个注销时间
            Long lastDestroyTime = accountsDestroyLogService.getLastValidCreateTime(accountUuid);
            // 如果在有效注销时间内，则返回手机号已注销
            if (accountsDestroyLogService.isTimeInValidDestroy(lastDestroyTime)) {
                return 2;
            }
            // 如果有查到关联账号，但是已经可以再使用
            // 如果accounts_info中存在is_check_mobile=1的记录则表明该account_uuid对应的mobile已经被验证
            Sql sql = Sql.build("select count(is_check_mobile) from accounts_info").eq("is_check_mobile", 1, false).eq("account_uuid", accountUuid);
            Integer checkMobileCount = accountsInfoDao.queryForInteger(sql);
            // 如果有已校验的账号，则用户手机号已绑定
            if (checkMobileCount > 0) {
                return 1;
            }
            // 未被验证是否则手机号置为空
            if (resetIfNoCheck) {
                logger.info("将手机号重置为空,uuid={}", accountUuid);
                // 手机号全部置为空
                accountsManager.setMobileNullInUuid(Lists.newArrayList(accountUuid));

                // 缓存一并修改 2021.04.30 校验字段也要改  旧的只改了mobile
                //加入到缓存
                Map<String, String> hashValues = new HashMap<>();
                hashValues.put(UuidInfoField.MOBILE, "");
//                hashValues.put(UuidInfoField.MOBILE_CIPHER, "");
                // 2024.05.27 不记录缓存
//                hashValues.put(UuidInfoField.IS_CHECK_MOBILE, "0");
//                hashValues.put(UuidInfoField.IS_BIND_MOBILE, "0");

                String redisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid.trim());
                accountStringRedisTemplate.opsForHash().putAll(redisKey, hashValues);

            }
        }

        return 0;
    }

    /**
     * 设置注册验证码校验标识
     *
     * @param mobile
     * @param regStyle
     */
    public void setRegVerify(String mobile, RegStyle regStyle) {
        accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.IS_REG_VERIFY.setArg(mobile), regStyle.name(), this.verifycodeValidTime == null ? 1800 : this.verifycodeValidTime, TimeUnit.SECONDS);
    }

    /**
     * 设置注册验证码校验标识
     *
     * @param mobile
     * @param verifyCode
     */
    public void setMobileRegVerify(String mobile, String verifyCode, Long time) {
        accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.MOBILE_REG_VERIFY.setArg(mobile), verifyCode + "|" + time, this.verifycodeValidTime == null ? 1800 : this.verifycodeValidTime, TimeUnit.SECONDS);
    }

    /**
     * 根据account_uuid批量查询手机号
     *
     * @param accountUuids account_uuid数组
     * @return
     */
    public Map<String, String> getMobileByUuids(String[] accountUuids) {
        Map<String, String> accountMobileMap = new HashMap<>();
        if (accountUuids == null) {
            return accountMobileMap;
        }

        List<String> missAccountUuid = new ArrayList<>();
        for (String accountUuid : accountUuids) {
            if (accountUuid == null) {
                continue;
            }
            String redisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
            Object mobile = accountStringRedisTemplate.opsForHash().get(redisKey, UuidInfoField.MOBILE);
            if (mobile == null) {
                missAccountUuid.add(accountUuid);
            } else if (ValidateUtil.isMobile(mobile.toString())) {
                accountMobileMap.put(accountUuid, mobile.toString());
            }
        }

        //缓存没有查询数据库
        if (!missAccountUuid.isEmpty()) {
            Map<String, Object> mobileInfos = mpAccountClient.getMobileByUuids(missAccountUuid);
            mobileInfos.forEach((accountUuid, mobile) -> {
                String mobileStr = mobile != null ? mobile.toString() : "";
                String redisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
                accountStringRedisTemplate.opsForHash().put(redisKey, UuidInfoField.MOBILE, StringUtil.nullToEmptyWithTrim(mobileStr));

                if (mobile != null && ValidateUtil.isMobile(mobileStr)) {
                    accountMobileMap.put(accountUuid, mobileStr.trim());
                }
            });
        }

        return accountMobileMap;
    }

    public Map<String, Object> handleCheckRegCode(LoginVo loginVo) {
        Map<String, Object> resultMap = new TreeMap<>();
        resultMap.put("nickname", StringUtil.nullToEmptyWithTrim(loginVo.getNickname()));
        resultMap.put("is_bind_mobile", StringUtil.nullNumberToEmptyString(loginVo.getIs_bind_mobile()));
        resultMap.put("is_check_mobile", StringUtil.nullNumberToEmptyString(loginVo.getIs_check_mobile()));
        resultMap.put("tqcoin", StringUtil.nullNumberToEmptyString(loginVo.getTqcoin()));
        resultMap.put("account_actor", StringUtil.nullNumberToEmptyString(loginVo.getAccount_actor()));
        resultMap.put("account_level", StringUtil.nullNumberToEmptyString(loginVo.getAccount_level()));
        resultMap.put("medal_name", StringUtil.nullToEmptyWithTrim(loginVo.getHonor_name()));
        resultMap.put("ticket_id", StringUtil.nullToEmptyWithTrim(loginVo.getTicket_id()));
        resultMap.put("account_type", StringUtil.nullNumberToEmptyString(loginVo.getAccount_type()));
        resultMap.put("sex_type", StringUtil.nullNumberToEmptyString(loginVo.getSex_type()));
        resultMap.put("avatar", StringUtil.nullToEmptyWithTrim(loginVo.getAvatar()));
        resultMap.put("account_uuid", StringUtil.nullToEmptyWithTrim(loginVo.getUuid()));
        resultMap.put("email", StringUtil.nullToEmptyWithTrim(loginVo.getEmail()));
        resultMap.put("mobile", StringUtil.mobileSecret(loginVo.getMobile()));
        resultMap.put("group_type", StringUtil.nullNumberToEmptyString(loginVo.getGroup_type()));
        resultMap.put("driver_level", StringUtils.defaultIfBlank(loginVo.getDriver_level(), "C"));

        //若果是版主显示版主头衔图片
        if (loginVo.getAccount_actor() != null && loginVo.getAccount_actor() > 0) {
            resultMap.put("account_medal", StringUtil.nullToEmptyWithTrim(loginVo.getMedal_url()));
        }
        //版主则获取所管理的版块列表
        Integer groupType = loginVo.getGroup_type();

        if (groupType != null && groupType == 2) {
            List<Long> category_ids = loginVo.getCategory_ids();
            List<Map<String, String>> group_circle = Lists.newArrayList();
            if (category_ids != null && category_ids.size() > 0) {
                for (Long id : category_ids) {
                    Map<String, String> map = Maps.newHashMap();
                    map.put("category_id", id.toString());
                    group_circle.add(map);
                }
            }

            resultMap.put("group_circle", group_circle);
        }

        return resultMap;
    }

    /**
     * 手机号是否绑定（0-否，1-是）
     *
     * @param mobile
     * @return
     */
    public static Integer isBindMobile(String mobile) {
        Integer isBindMobile = CommConst.YES_1;
        if(StringUtils.isBlank(mobile)) {
            isBindMobile = CommConst.NO_0;
        }

        return isBindMobile;
    }


    /**
     * 修改手机号，用于替换 {@linkplain #(String, String, String, int)}
     *
     * @param ticketId
     * @param password
     * @param mobile
     * @param verify
     */
    @Transactional
    public void modifyMobile(String ticketId, String password, String mobile, String verify, String deviceToken, String shumeiDeviceId) {
        if (StringUtils.isBlank(ticketId)) {
            throw new ServiceException(CodeStatus.TICKET_EXPIRE);
        }
        GetByTicketVo infoByTicket = accountsInfoService.getByTicketIfGuestExpire(ticketId);

        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.MODIFY_MOBILE, mobile, verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }

        if (riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Mobile.getValue(), mobile)) {
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }

        String uuid = infoByTicket.getAccount_uuid();
        boolean pwStatus = accountsService.checkPasswordByUuid(password, uuid);
        if (!pwStatus) {
            throw new ServiceException(CodeStatus.MODIFY_MOBILE_PW_NO_PASS);
        }

        bindMobileCheck(mobile, "no continueLogin");
//        String oldMobile = accountsManager.getMobileByUuid(uuid);
        // 更新手机号
        updateInfoByChange(uuid, mobile, "0", shumeiDeviceId);

        // 推入队列通知反垃圾系统
        antiSpamMqUtil.pushToAccountActionMq(uuid, "", AccountActionTypeEnum.EDIT_INFO, null);
        //更换手机成功调用，避免绑定手机号后还要经过一次短信验证
        antiSpamService.verifyValidHandle(infoByTicket.getAccount_uuid());
        // 更换完手机号推入队列
//        BuryService.updateMobilePushToMultiMq(uuid, oldMobile, mobile);
    }

    /**
     * 绑定手机号并且更新缓存
     *
     * @param uuid
     * @param mobile
     * @param code
     */
    private void setAccountMobile(String uuid, String mobile, String code, boolean isChange, String smid) {
        if (MPAccountRequest.isRequestMP() && isChange) {
            mpAccountClient.changePhone(uuid, mobile, smid);
        }

        // 绑定手机号
        accountsManager.bindMobileByUuid(mobile, EncryptUtil.encrypt(mobile), EncryptUtil.sm3(mobile), uuid);
        // 将手机号设为已验证
        AccountsInfo accountsInfo = accountsInfoService.findOrCreate(uuid);
        accountsInfo.setIs_check_mobile(1);
        accountsInfoService.save(accountsInfo);
        //将手机号原先绑定mobile清空
        accountsService.setMobileNull(mobile, uuid);
        // 更新verify_record表，通过mobile和code作为条件更新status为1及modify_time为当前时间，更新失败依然返回成功
//        verifyRecordDao.updateModifyInfo(DateUtil.currentTimeSeconds(), mobile, Long.parseLong(code));

        // 更新缓存
        accountsManager.setMobileCache(uuid, mobile);

    }


    /**
     * 绑定手机号
     *
     * @param accountUuid
     * @param mobile
     * @param verify
     * @param deviceToken
     * @param shumeiDeviceId
     * @param continueLogin  是否继续登录,当等于1的时候,使用提示而不是报异常,并且返回一个随机的code用来进行登录校验
     */
    @Transactional
    public String bindMobile(String accountUuid, String mobile, String verify, String deviceToken, String shumeiDeviceId, String continueLogin) {
        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.BIND_MOBILE, mobile, verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }
        return processBindMobile(accountUuid, null, mobile, deviceToken, shumeiDeviceId, continueLogin, BindMobileTypeEnum.BIND, Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifyMobileV2(String accountUuid, String mobile, String verify, String shumeiDeviceId) {
        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.MODIFY_MOBILE, mobile, verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }

        if (riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Mobile.getValue(), mobile)) {
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }

        bindMobileCheck(mobile, "no continueLogin");
//        String oldMobile = accountsManager.getMobileByUuid(accountUuid);
        // 更新手机号
        updateInfoByChange(accountUuid, mobile, "0", shumeiDeviceId);

        // 推入队列通知反垃圾系统
        antiSpamMqUtil.pushToAccountActionMq(accountUuid, "", AccountActionTypeEnum.EDIT_INFO, null);
        //更换手机成功调用，避免绑定手机号后还要经过一次短信验证
        antiSpamService.verifyValidHandle(accountUuid);
        // 更换完手机号推入队列
//        BuryService.updateMobilePushToMultiMq(accountUuid, oldMobile, mobile);
    }

    /**
     * 是否继续登录,1是 0否,兼容之前的逻辑.
     * 因为绑定手机号在20年44周迭代的时候有个需求.手机号已经通过验证以后,但是已经被绑定.需要有个操作让他继续登录
     * 所以生成个随机码,然后在提示手机号被占用,并且continueLogin=1的时候,进行特殊处理.
     * 最后把randomStr返回给客户端
     *
     * @param accountUuid
     * @param mobile
     * @param deviceToken
     * @param shumeiDeviceId
     * @param continueLogin
     * @return
     */
    public String processBindMobile(String accountUuid,
                                    Integer sexType,
                                    String mobile,
                                    String deviceToken,
                                    String shumeiDeviceId,
                                    String continueLogin,
                                    BindMobileTypeEnum bindMobileTypeEnum,
                                    boolean isChange) {
        logger.info("processBindMobile:uuid:{},mobile:{},deviceToken:{},shumei:{},continue:{}", accountUuid, "***", deviceToken, shumeiDeviceId, continueLogin);
        int checkStatus = bindMobileCheck(mobile, continueLogin);
        //特殊处理,如果已经被绑定,并且需要继续登录的场景下,下面流程也不用走了
        if (checkStatus == 1 && THIRD_CONTINUE_LOGIN.equals(continueLogin)) {
            String randomStr = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile));
            if (StringUtils.isNotBlank(randomStr)) {
                return THIRD_REWARD_LOGIN;
            }
        }

        // 可以校验
        accountsManager.getMobileByUuid(accountUuid);

        updateInfoByRelated(accountUuid, mobile, "0", isChange, shumeiDeviceId);
        accountsService.deleteWaBindMobileCache(accountUuid);

        // 推入队列通知反垃圾系统
        antiSpamMqUtil.pushToAccountActionMq(accountUuid, "", AccountActionTypeEnum.EDIT_INFO, null);
        //绑定手机成功调用，避免绑定手机号后还要经过一次短信验证
        antiSpamService.verifyValidHandle(accountUuid);
        //绑定手机号的时候推入一次社区行为队列
        modifyGrowScoreService.sendModifyGrowScoreMq(accountUuid, ModifyGrowScoreEnum.BIND_MOBILE);
        //********线上修复，手机号注册或者是绑定手机号，同时再推入一个队列，沛煌说的
        sendCompleteForumTaskMq(accountUuid);

        buryService.bindMobileBuryToAdmp(accountUuid, sexType, DateUtil.currentTimeSeconds(), bindMobileTypeEnum);

        return NO_REWARD_LOGIN;
    }

    public void sendCompleteForumTaskMq(String accountUuid) {
        //同时推入一个
        Map<String, Object> param = new HashMap<>();
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        param.put("account_uuid", accountUuid);
        param.put("type", ModifyGrowScoreEnum.BIND_MOBILE.getValue());
        param.put("platform_id", sbp.getPlatform_id());
        param.put("channel", sbp.getChannel());
        param.put("app_version", sbp.getApp_version());
        param.put("cloned", sbp.getCloned());

        MqResponse mqResponse2 = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(CommConst.MQ_COMPLETE_FOURM_TASK, param, null);
        if (mqResponse2.fail()) {
            logger.error("推入complete_fourm_task,{}, 失败原因:{}-{}", param, mqResponse2.getCode(), mqResponse2.getMsg());
            return;
        }
    }

    public Map<String, String> captchaPlatformVerify(String phone, String verify, Long time) {
        String verifyRegKey = RedisKeyConstant.MOBILE_REG_SUCCESS.setArg(verify);
        List<String> mobileAndTimes = accountStringRedisTemplate.opsForList().range(verifyRegKey, 0, -1);
        if (CollectionUtils.isEmpty(mobileAndTimes)) {
            return null;
        }
        Pattern phonePattern = Pattern.compile(phone.replace("****", "\\d{4}"));

        for (String mobileAndTime : mobileAndTimes) {
            String[] regInfo = mobileAndTime.split("\\|");
            String mobile = regInfo[0];
            // 先匹配手机号
            if (phonePattern.matcher(mobile).matches()) {
                long verifyTime = Long.parseLong(regInfo[1]);
                // 匹配接码时间，当前匹配范围：接码时间前推10分钟
                if ((time - SPACE_MINUTE * 60L) <= verifyTime && verifyTime <= (time + SPACE_MINUTE * 60L)) {
                    Map<String, String> res = new HashMap<>();
                    res.put("uuid", regInfo[2]);
                    res.put("regTime", regInfo[3]);

                    logger.info("match platform verify user: {}", regInfo[2]);
                    return res;
                }
            }
        }
        return null;
    }

    public void verifyBindMobile(String accountUuid, String verify) {
        Accounts accounts = accountsManager.getByUuid(accountUuid);
        if (StringUtils.isBlank(accounts.getMobile())) {
            throw new ServiceException(CodeStatus.MOBILE_NOT_EXISTS);
        }
        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.MODIFY_MOBILE, accounts.getMobile(), verify)) {
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }
    }

    public Map<String, Object> regVerifyMobileByCode(String type, String mobile, String verify, String accessToken) {
        logger.info("regVerifyMobileByCode type:{}, mobile:{}, verify:{}, accessToken:{}", type, mobile, verify, accessToken);

        // 校验验证码
        if (Objects.equals(type, "1")) {
            if (StringUtils.isBlank(mobile) || StringUtils.isBlank(verify)) {
                throw new ServiceException(CodeStatus.MOBILE_NOT_EXISTS);
            }

            if (!verifyRecordService.checkVerify(VerifyRecordService.Code.REG_LOGIN, mobile, verify)) {
                throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
            }
        }

        // 第三方闪验
        if (Objects.equals(type, "2")) {
            if (StringUtils.isBlank(accessToken)) {
                throw new ServiceException(CodeStatus.MOBILE_NOT_EXISTS);
            }

            Map<String, Object> mpRes = mpAccountClient.getPhoneNumByFlash(accessToken);
            mobile = MapUtils.getString(mpRes, "phone");
        }

        if (StringUtils.isBlank(mobile)) {
            throw new ServiceException(CodeStatus.MOBILE_NOT_EXISTS);
        }

        // 校验手机号是否已注册
        Map<String, Object> mpRes = mpAccountClient.queryUser(mobile, 2);
        boolean needRegister = "1".equals(MapUtils.getString(mpRes, SnakeCaseBizConstant.NEED_REGISTER));

        String uuid = MapUtils.getString(mpRes, "uuid");

        Map<String, Object> resMap = new HashMap<>();
        resMap.put("mobile", mobile);
        resMap.put("continue_login_type",NO_REWARD_LOGIN);

        // 手机号已注册，跳转登录
        if (!needRegister && StringUtils.isNotBlank(uuid)) {
            String randomStr = String.valueOf(Md5Util.generate10Key());
            accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile), randomStr, 300, TimeUnit.SECONDS);

            resMap.put("random_str", randomStr);
            resMap.put("continue_login_type",THIRD_REWARD_LOGIN);
        } else {
            // 第三方注册绑定手机号验证码校验
            if (Objects.equals(type, "1")) {
                this.setRegVerify(mobile, RegStyle.Mobile);
            }
        }

        logger.info("regVerifyMobileByCode resMap:{}", JSON.toJSONString(resMap));
        return resMap;
    }

    /**
     * 闪验绑定手机号
     *
     * @param accountUuid
     * @param accessToken
     * @param shumeiDeviceId
     * @param continueLogin  是否继续登录,当等于1的时候,使用提示而不是报异常,并且返回一个随机的code用来进行登录校验
     * @return
     */
    public Map<String, Object> quickBindMobile(String accountUuid, String accessToken, String shumeiDeviceId, String continueLogin) {
        /*
         * 由于手机号需要业务校验，所以只能将闪验绑定手机号拆成多步骤
         *  1.获取手机（j79）
         *  2.校验手机号（j2）
         *  3.绑定（j79）
         */

        // 获取手机号
        Map<String, Object> phoneNumByFlashRes = mpAccountClient.getPhoneNumByFlash(accessToken);
        String mobile = MapUtils.getString(phoneNumByFlashRes, CamelCaseBizConstant.PHONE, null);

//       String mobile = flashClient.getMobileByFlash(accessToken);
        if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
            logger.warn("闪验绑定手机号返回手机号:{} 格式错误, accessToken:{}", mobile, accessToken);
            throw new ServiceException(CodeStatus.FLASH_MOBILE_ERROR);
        }

        if (riskService.checkOpenIdRisk(BanOpenIdTypeEnum.Mobile.getValue(), mobile)) {
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }

        Map<String, Object> bind = mpAccountClient.changePhoneV2(accountUuid, mobile, shumeiDeviceId);
        boolean mpAlreadyBind =  Objects.equals( CamelCaseBizConstant.ALREADY_BIND_VALUE, bind.get(SnakeCaseBizConstant.ALREADY_BIND));

        if (mpAlreadyBind) {
            logger.warn("mp: mobile already bind.");
            if (THIRD_CONTINUE_LOGIN.equals(continueLogin)) {
                String randomStr = Md5Util.generate10Key() + "";
                accountStringRedisTemplate.opsForValue().set(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile), randomStr, 300, TimeUnit.SECONDS);

                Map<String, Object> result = Maps.newHashMap();
                result.put("bind_status", CommonEnableStatus.DISABLE.getStatus());
                result.put("random_str", randomStr);
                result.put("continue_login_type", THIRD_REWARD_LOGIN);

                return result;
            } else {
                throw new ServiceException(CodeStatus.MOBILE_EXISTS);
            }
        }else {
            mobile = MapUtils.getString(bind, CamelCaseBizConstant.PHONE);
            if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
                logger.warn("绑定手机号返回手机号异常:{} ", mobile);
                throw new ServiceException(CodeStatus.FLASH_MOBILE_ERROR);
            }
        }

        /*
         * TODO 2022.10.09 该方法也会调用中台绑定手机号，先修复闪验绑定bug，后续再观察该问题
         */
        String rewardLoginType = processBindMobile(accountUuid, null, mobile, "", shumeiDeviceId, continueLogin, BindMobileTypeEnum.BIND, Boolean.FALSE);
        Map<String, Object> result = Maps.newHashMap();

        //如果需要继续登录,则跳转到登录页面
        if (THIRD_REWARD_LOGIN.equals(rewardLoginType)) {
            result.put("bind_status", CommonEnableStatus.DISABLE.getStatus());
            result.put("random_str", accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.THIRD_LOGIN_CONTINUE_LOGIN.setArg(mobile)));
        } else {
            result.put("bind_status", CommonEnableStatus.ENABLE.getStatus());
        }
        result.put("continue_login_type", rewardLoginType);
        return result;
    }

    /**
     * 校验验证码，确认手机号更棒【用于手机找回场景】
     *
     * @param accountUuid
     * @param mobile
     * @param verify
     * @param shumeiDeviceId
     */
    public void verifyCodeToBindMobileForRetrieve(String accountUuid, String mobile, String verify, String shumeiDeviceId, String oldMobile) {
        if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
            throw new ServiceException(CodeStatus.MOBILE_INVALID);
        }

        // 获取实名信息
        Map<String, String> certInfo = accountsCertificationService.getCertInfoByUuid(accountUuid);
        String identityNo = certInfo.get("identity_no");
        if (StringUtils.isBlank(identityNo)) {
            throw new ServiceException("非实名用户，找回失败！");
        }

        // 比对当前更改前是否已经有人先更改了
        String dbMobile = accountsService.getMobileByUuid(accountUuid);
        if (!Objects.equals(dbMobile, oldMobile)) {
            throw new ServiceException("你输入的账号有误");
        }

        accountsCertificationService.validRetrieveLimit(accountUuid, identityNo, 0L);
        this.modifyMobileV2(accountUuid, mobile, verify, shumeiDeviceId);
        accountsCertificationService.validRetrieveLimit(accountUuid, identityNo, 1L);

        // 更棒成功推送kafka
        eventTrackReporter.report(accountUuid, "account_retrieval_change_phone_success", null);
    }
}
