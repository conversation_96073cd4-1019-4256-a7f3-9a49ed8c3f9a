package cn.taqu.account.service;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.PersonalityLabelEnum;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsLabelDao;
import cn.taqu.account.event.EventTrackReporter;
import cn.taqu.account.event.UserLabelEvent;
import cn.taqu.account.model.AccountsLabel;
import cn.taqu.account.model.PersonalityLabel;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * 2017年12月12日 上午10:46:33
 */
@Service
@Transactional
public class AccountsLabelService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsLabel> {

	@Autowired
	private AccountsInfoService accountsInfoService;
	@Autowired
	private AccountsLabelDao accountsLabelDao;
	@Autowired
	private PersonalityLabelService personalityLabelService;
	@Autowired
	private ClickFilterService clickFilterService;
	@Autowired
	@Qualifier("accountStringRedisTemplate")
	private StringRedisTemplate accountStringRedisTemplate;
	@Resource
	private EventTrackReporter eventTrackReporter;

	/**
	 * 获取用户已选择的指定类型的标签，返回数据格式如下：
	 * <code>
	 *     <pre>
	 *         {
	 *             "uuid1": {
	 * 	 *             "like_type_label": [
	 * 	 *             		{"id":"喜欢类型标签id1", "name":"喜欢类型标签名称1"},
	 * 	 *             		{"id":"喜欢类型标签id2", "name":"喜欢类型标签名称2"},
	 * 	 *             		...
	 * 	 *             ],
	 * 	 *             "description_label": [
	 * 	 *             		{"id":"自我描述标签id1", "name":"自我描述标签名称1"},
	 * 	 *             		{"id":"自我描述标签id2", "name":"自我描述标签名称2"},
	 * 	 *             		...
	 * 	 *             ],
	 * 	 *             "hobby_label": [
	 * 	 *             		{"id":"兴趣爱好标签id1", "name":"兴趣爱好标签名称1"},
	 * 	 *             		{"id":"兴趣爱好标签id2", "name":"兴趣爱好标签名称2"},
	 * 	 *             		...
	 * 	 *             ]
	 * 	 *         },
	 * 	 			"uuid2": {
	 * 	 *             "like_type_label": [
	 * 	 *             		{"id":"喜欢类型标签id1", "name":"喜欢类型标签名称1"},
	 * 	 *             		{"id":"喜欢类型标签id2", "name":"喜欢类型标签名称2"},
	 * 	 *             		...
	 * 	 *             ],
	 * 	 *             "description_label": [
	 * 	 *             		{"id":"自我描述标签id1", "name":"自我描述标签名称1"},
	 * 	 *             		{"id":"自我描述标签id2", "name":"自我描述标签名称2"},
	 * 	 *             		...
	 * 	 *             ],
	 * 	 *             "hobby_label": [
	 * 	 *             		{"id":"兴趣爱好标签id1", "name":"兴趣爱好标签名称1"},
	 * 	 *             		{"id":"兴趣爱好标签id2", "name":"兴趣爱好标签名称2"},
	 * 	 *             		...
	 * 	 *             ]
	 * 	 *         }
	 *         }
	 *     </pre>
	 * </code>
	 * @param accountUuidList 用户uuid
	 * @param type 标签类型 0-全部, 1-喜欢类型，2-自我描述，3-兴趣爱好
	 * @return
	 */
	public Map<String, Map<String, List<Map<String, String>>>> mGetTypeLabelByUuid(List<String> accountUuidList, Integer type) {
		final Map<String, Map<String, List<Map<String, String>>>> resultMap = new LinkedHashMap<>();

		Map<String, Map<String, List<Map<String, String>>>> accountLabelDataMap = Maps.newHashMap();

		// 2020.06.15 判断 accountUuidList长度，如果小于5，就不查所有
		if(accountUuidList.size() >= 5) {
			accountLabelDataMap = this.mGetAccountLabelByUuid(accountUuidList, Objects.equal(type, 3) ? 2 : type);
		}else {
			accountLabelDataMap = this.mGetAccountLabelByUuidNotAll(accountUuidList, Objects.equal(type, 3) ? 2 : type);
		}

		accountLabelDataMap.forEach((accountUuid, accountLabelData) -> {
			//用户已选择的标签列表
			List<Map<String, String>> accountLabelList = accountLabelData.get("account_label");
			//喜欢类型标签列表
			List<Map<String, String>> likeTypeLabelList = Objects.equal(type, 1) || Objects.equal(type, 0) ? accountLabelData.get("like_type_label") : null;
			List<Map<String, String>> descriptionLabelList = Objects.equal(type, 2) || Objects.equal(type, 0) ? accountLabelData.get("description_label") : null;
			List<Map<String, String>> hobbyLabelList = Objects.equal(type, 3) || Objects.equal(type, 0) ? accountLabelData.get("hobby_label") : null;

			Map<String, List<Map<String, String>>> userMap = new HashMap<>();
			if(accountLabelList == null) {
				resultMap.put(accountUuid, userMap);
				return;
			}

			//以下操作为从用户已选择的标签中找到指定类型的标签
			Set<String> accountLabelIdSet = new HashSet<>();
			for(Map<String, String> accountLabel : accountLabelList) {
				accountLabelIdSet.add(MapUtils.getString(accountLabel, "id", ""));
			}

			for(int i=0; likeTypeLabelList!=null && i<likeTypeLabelList.size(); i++) {
				Map<String, String> label = likeTypeLabelList.get(i);
				if(accountLabelIdSet.contains(label.get("id"))) {
					userMap.compute("like_type_label", (k, v) -> {
						if(v == null) {
							v = Lists.newArrayList(label);
						} else {
							v.add(label);
						}
						return v;
					});
				}
			}

			for(int i=0; descriptionLabelList!=null && i<descriptionLabelList.size(); i++) {
				Map<String, String> label = descriptionLabelList.get(i);
				if(accountLabelIdSet.contains(label.get("id"))) {
					userMap.compute("description_label", (k, v) -> {
						if(v == null) {
							v = Lists.newArrayList(label);
						} else {
							v.add(label);
						}
						return v;
					});
				}
			}

			for(int i=0; hobbyLabelList!=null && i<hobbyLabelList.size(); i++) {
				Map<String, String> label = hobbyLabelList.get(i);
				if(accountLabelIdSet.contains(label.get("id"))) {
					userMap.compute("hobby_label", (k, v) -> {
						if(v == null) {
							v = Lists.newArrayList(label);
						} else {
							v.add(label);
						}
						return v;
					});
				}
			}
			resultMap.put(accountUuid, userMap);
		});
		return resultMap;
	}

	public Map<String, List<Map<String, String>>> getAccountLabelForNew(String accountUuid, Integer type) {
		return this.mGetAccountLabelByUuid(Arrays.asList(accountUuid), type).get(accountUuid);
	}

	public Map<String,  Map<String, List<Map<String, String>>>> mGetAccountLabelByUuid(List<String> accountUuidList, Integer type) {
		Map<String, Map<String, List<Map<String, String>>>> resultMap = new LinkedHashMap<>();
		List<PersonalityLabel> labelList = personalityLabelService.findListByCache(type);

		if (labelList.isEmpty()) {
			labelList.addAll(personalityLabelService.findList());
		}
		//查询用户性别
		Map<String, Map<String, Object>> accountsInfoMap = accountsInfoService.getInfoByUuid(accountUuidList.toArray(new String[]{}), new String[]{"sex_type"}, "1", false, false);
		//查询用户标签
		Map<String, List<PersonalityLabel>> accountLabelListMap = mGetAccountLabelByType(accountUuidList, type);

		accountsInfoMap.forEach((accountUuid, accountsInfo) -> {
			int sexType = MapUtils.getIntValue(accountsInfo, "sex_type");//性别，1:男; 2:女; null:未知
			Map<String, List<Map<String, String>>> userMap = Maps.newHashMap();

			// 筛选出需要的列表
			List<PersonalityLabel> accountLabelList = accountLabelListMap.get(accountUuid);
			List<PersonalityLabel> likeTypeLabelList;//喜欢类型标签
			List<PersonalityLabel> descriptionLabelList;//自我描述标签
			List<PersonalityLabel> hobbyLabelList;//爱好标签
			List<Map<String, String>> accountLabelObjList = Lists.newArrayList();

			if (type == 1 || type == 0) {
				likeTypeLabelList = this.filterLabelList(labelList, sexType, 1);
				this.sortLabelList(likeTypeLabelList);

				List<Map<String, String>> likeTypeLabelObjList = Lists.newArrayList();
				for (PersonalityLabel personalityLabel : likeTypeLabelList) {
					Map<String, String> m = Maps.newHashMap();
					m.put("id", String.valueOf(personalityLabel.getId()));
					m.put("name", personalityLabel.getContent());
					m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
					likeTypeLabelObjList.add(m);
				}
				userMap.put("like_type_label", likeTypeLabelObjList);

			}
			if (type == 2 || type == 0) {
				descriptionLabelList = this.filterLabelList(labelList, sexType, 2);
				hobbyLabelList = this.filterLabelList(labelList, sexType, 3);
				this.sortLabelList(descriptionLabelList);
				this.sortLabelList(hobbyLabelList);

				List<Map<String, String>> descriptionLabelObjList = Lists.newArrayList();
				for (PersonalityLabel personalityLabel : descriptionLabelList) {
					Map<String, String> m = Maps.newHashMap();
					m.put("id", String.valueOf(personalityLabel.getId()));
					m.put("name", personalityLabel.getContent());
					m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
					descriptionLabelObjList.add(m);
				}
				userMap.put("description_label", descriptionLabelObjList);

				List<Map<String, String>> hobbyLabelObjList = Lists.newArrayList();
				for (PersonalityLabel personalityLabel : hobbyLabelList) {
					Map<String, String> m = Maps.newHashMap();
					m.put("id", String.valueOf(personalityLabel.getId()));
					m.put("name", personalityLabel.getContent());
					m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
					hobbyLabelObjList.add(m);
				}
				userMap.put("hobby_label", hobbyLabelObjList);
			}

			for (PersonalityLabel personalityLabel : accountLabelList) {
				Map<String, String> m = Maps.newHashMap();
				m.put("id", String.valueOf(personalityLabel.getId()));
				m.put("name", personalityLabel.getContent());
				m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
				accountLabelObjList.add(m);
			}
			userMap.put("account_label", accountLabelObjList);
			resultMap.put(accountUuid, userMap);
		});

		return resultMap;
	}

	public Map<String,  Map<String, List<Map<String, String>>>> mGetAccountLabelByUuidNotAll(List<String> accountUuidList, Integer type) {
		Map<String, Map<String, List<Map<String, String>>>> resultMap = new LinkedHashMap<>();
		//TODO 查询所有标签 查询所有太慢
//		List<PersonalityLabel> labelList = personalityLabelService.findListByCache();

//		if (labelList.isEmpty()) {
//			labelList.addAll(personalityLabelService.findList());
//		}
		//查询用户性别
		Map<String, Map<String, Object>> accountsInfoMap = accountsInfoService.getInfoByUuid(accountUuidList.toArray(new String[]{}), new String[]{"sex_type"}, "1", false, false);
		//查询用户标签
		Map<String, List<PersonalityLabel>> accountLabelListMap = mGetAccountLabelByType(accountUuidList, type);

		accountsInfoMap.forEach((accountUuid, accountsInfo) -> {
			int sexType = MapUtils.getIntValue(accountsInfo, "sex_type");//性别，1:男; 2:女; null:未知
			Map<String, List<Map<String, String>>> userMap = Maps.newHashMap();

			// 筛选出需要的列表
			List<PersonalityLabel> accountLabelList = accountLabelListMap.get(accountUuid);
			List<PersonalityLabel> likeTypeLabelList;//喜欢类型标签
			List<PersonalityLabel> descriptionLabelList;//自我描述标签
			List<PersonalityLabel> hobbyLabelList;//爱好标签
			List<Map<String, String>> accountLabelObjList = Lists.newArrayList();

			if (type == 1 || type == 0) {
				likeTypeLabelList = this.filterLabelList(accountLabelList, sexType, 1);
				this.sortLabelList(likeTypeLabelList);

				List<Map<String, String>> likeTypeLabelObjList = Lists.newArrayList();
				for (PersonalityLabel personalityLabel : likeTypeLabelList) {
					Map<String, String> m = Maps.newHashMap();
					m.put("id", String.valueOf(personalityLabel.getId()));
					m.put("name", personalityLabel.getContent());
					m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
					likeTypeLabelObjList.add(m);
				}
				userMap.put("like_type_label", likeTypeLabelObjList);

			}
			if (type == 2 || type == 0) {
				descriptionLabelList = this.filterLabelList(accountLabelList, sexType, 2);
				hobbyLabelList = this.filterLabelList(accountLabelList, sexType, 3);
				this.sortLabelList(descriptionLabelList);
				this.sortLabelList(hobbyLabelList);

				List<Map<String, String>> descriptionLabelObjList = Lists.newArrayList();
				for (PersonalityLabel personalityLabel : descriptionLabelList) {
					Map<String, String> m = Maps.newHashMap();
					m.put("id", String.valueOf(personalityLabel.getId()));
					m.put("name", personalityLabel.getContent());
					m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
					descriptionLabelObjList.add(m);
				}
				userMap.put("description_label", descriptionLabelObjList);

				List<Map<String, String>> hobbyLabelObjList = Lists.newArrayList();
				for (PersonalityLabel personalityLabel : hobbyLabelList) {
					Map<String, String> m = Maps.newHashMap();
					m.put("id", String.valueOf(personalityLabel.getId()));
					m.put("name", personalityLabel.getContent());
					m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
					hobbyLabelObjList.add(m);
				}
				userMap.put("hobby_label", hobbyLabelObjList);
			}

			for (PersonalityLabel personalityLabel : accountLabelList) {
				Map<String, String> m = Maps.newHashMap();
				m.put("id", String.valueOf(personalityLabel.getId()));
				m.put("name", personalityLabel.getContent());
				m.put("description", StringUtils.defaultString(personalityLabel.getDescription(), ""));
				accountLabelObjList.add(m);
			}
			userMap.put("account_label", accountLabelObjList);
			resultMap.put(accountUuid, userMap);
		});

		return resultMap;
	}


	/**
	 * 获取用户标签列表（查询所有使用，性别相符的设置）
	 *
	 * @param accountUuidList
	 * @param type
	 *            1-喜欢类型 2-个性标签
	 * @return
	 * <AUTHOR> 2017年12月15日 上午8:55:12
	 */
	public Map<String,  List<PersonalityLabel>> mGetAccountLabelByType(List<String> accountUuidList, Integer type) {
		final Map<String, List<PersonalityLabel>> resultMap = new LinkedHashMap<>();

		//查询用户性别
		Map<String, Map<String, Object>> accountsInfoMap = accountsInfoService.getInfoByUuid(accountUuidList.toArray(new String[]{}), new String[]{"sex_type"}, "1", false, false);
		accountsInfoMap.forEach((accountUuid, accountsInfo) -> {
			List<PersonalityLabel> list = Lists.newArrayList();
			List<Long> idList = Lists.newArrayList();
			int sexType = MapUtils.getIntValue(accountsInfo, "sex_type");//性别，1:男; 2:女; null:未知

			// 查询用户标签
			HashOperations<String, Object, Object> opsForHash = accountStringRedisTemplate.opsForHash();
			String redisKey = RedisKeyConstant.ACCOUNT_LABEL.setArg(accountUuid);
			if (type == 1 || type == 0) {
				String likeTypeLabel;
				Object object = opsForHash.get(redisKey, "like_type_label");
				if (object == null) {
					AccountsLabel accountsLabel = accountsLabelDao.findByAccountUuid(accountUuid);
					if (accountsLabel == null) {
						resultMap.put(accountUuid, list);
						return;
					}
					likeTypeLabel = accountsLabel.getLike_type_label();
					opsForHash.put(redisKey, "like_type_label", likeTypeLabel);
					accountStringRedisTemplate.expire(redisKey, 7, TimeUnit.DAYS);
				} else {
					likeTypeLabel = String.valueOf(object);
				}

				idList.addAll(getLabelSet(likeTypeLabel));
			}

			if (type == 2 || type == 0) {
				String personalityLabel;
				Object object = opsForHash.get(redisKey, "personality_label");
				if (object == null) {
					AccountsLabel accountsLabel = accountsLabelDao.findByAccountUuid(accountUuid);
					if (accountsLabel == null) {
						resultMap.put(accountUuid, list);
						return;
					}
					personalityLabel = accountsLabel.getPersonality_label();
					opsForHash.put(redisKey, "personality_label", personalityLabel);
					accountStringRedisTemplate.expire(redisKey, 7, TimeUnit.DAYS);
				} else {
					personalityLabel = String.valueOf(object);
				}

				idList.addAll(getLabelSet(personalityLabel));
			}

			for (Long id : idList) {
				ValueOperations<String, String> opsForValue = accountStringRedisTemplate.opsForValue();
				String plstr = opsForValue.get(RedisKeyConstant.PERSONALITY_LABEL_DATA.setArg(id));
				boolean flagHasRedis = false;
				if (StringUtils.isNotBlank(plstr)) {
					PersonalityLabel pl = JsonUtils.stringToObject(plstr, PersonalityLabel.class);
					if (pl != null) {
						list.add(pl);
						flagHasRedis = true;
					}
				}

				if (!flagHasRedis) {
					PersonalityLabel plDB = personalityLabelService.getByIdFromDb(id);
					if (plDB != null) {
						list.add(plDB);
					}
				}
			}

			list = this.filterLabelList(list, sexType);
			resultMap.put(accountUuid, list);
		});

		return resultMap;
	}

	/**
	 * 根据类型获取标签id
	 * @param accountUuid
	 * @param type 1-喜欢类型 2-个性标签
	 * @return
	 */
	public List<Long> getLabelIdByType(String accountUuid, Integer type) {
		List<PersonalityLabel> labelList = this.mGetAccountLabelByType(Arrays.asList(accountUuid), type).get(accountUuid);
		int size = labelList == null ? 0 : labelList.size();
		List<Long> labelIdList = new ArrayList<>();
		for(int i = 0; i<size; i++) {
			PersonalityLabel label = labelList.get(i);
			Long labelId = label.getId();
			if(labelId == null) {
				continue;
			}
			labelIdList.add(labelId);
		}
		return labelIdList;
	}

	private void sortLabelList(List<PersonalityLabel> list) {
		if(list != null && list.size() > 1) {
			Comparator<PersonalityLabel> comparator = (o1, o2) -> {
				long count1 = o1.getUse_count() == null ? 0 : o1.getUse_count().longValue();
				long count2 = o2.getUse_count() == null ? 0 : o2.getUse_count().longValue();
				int countP = Long.compare(count2, count1);
				if(countP == 0) {
					long updateTime1 = o1.getUpdate_time() == null ? 0 : o1.getUpdate_time();
					long updateTime2 = o2.getUpdate_time() == null ? 0 : o2.getUpdate_time();
					return Long.compare(updateTime2, updateTime1);
				}
				return countP;
			};
			Collections.sort(list, comparator);
		}
	}

	/**
	 *
	 * @param labelList
	 * @param sexType 1-男，2-女
	 * @param type  1-喜欢类型，2-自我描述，3-兴趣爱好。
	 * @return
	 * <AUTHOR>
	 * 2017年12月14日 下午5:43:54
	 */
	private List<PersonalityLabel> filterLabelList(List<PersonalityLabel> labelList, int sexType, Integer type) {
		List<PersonalityLabel> list = Lists.newArrayList();
		for (PersonalityLabel personalityLabel : labelList) {
			Long id = personalityLabel.getId();
			if(id == null){
				continue;
			}
			Integer personalityLabelType = personalityLabel.getType();
			Integer personalityLabelTypeSex = personalityLabel.getSex();
			Integer status = personalityLabel.getStatus();
			if(Objects.equal(status, 1)){
				continue;
			}
			//不是通用，性别又不相符，下一个
			if(!Objects.equal(personalityLabelTypeSex, 3) && !Objects.equal(sexType, personalityLabelTypeSex)){
				continue;
			}
			//类别不对，下一个
			if(!Objects.equal(personalityLabelType, type)){
				continue;
			}
			list.add(personalityLabel);
		}
		return list;
	}

	/**
	 *
	 * @param labelList
	 * @param sexType 1-男，2-女
	 * @return
	 * <AUTHOR>
	 * 2017年12月14日 下午5:43:54
	 */
	private List<PersonalityLabel> filterLabelList(List<PersonalityLabel> labelList, int sexType) {
		List<PersonalityLabel> list = Lists.newArrayList();
		for (PersonalityLabel personalityLabel : labelList) {
			Long id = personalityLabel.getId();
			if(id == null){
				continue;
			}
			Integer personalityLabelTypeSex = personalityLabel.getSex();
			Integer status = personalityLabel.getStatus();
			if(Objects.equal(status, 1)){
				continue;
			}
			//不是通用，性别又不相符，下一个
			if(!Objects.equal(personalityLabelTypeSex, 3) && !Objects.equal(sexType, personalityLabelTypeSex)){
				continue;
			}

			list.add(personalityLabel);
		}
		return list;
	}

	/**
	 * 计算并返回标签匹配情况
	 * @param uuid1
	 * @param uuid2List
	 * @return 0:没有匹配; 1:uuid2是uuid1喜欢的人; 2:uuid1是uuid2喜欢的人; 3:有共同的爱好;
	 */
	public Map<String, Integer> mGetLabelMatchValue(String uuid1, List<String> uuid2List) {
		Map<String, Integer> resultMap = new LinkedHashMap<>();

		if(StringUtils.isBlank(uuid1) || uuid2List.isEmpty()) {
			return resultMap;
		}

		List<String> allUuidList = new ArrayList<>(uuid2List);
		if(!allUuidList.contains(uuid1)) {
			allUuidList.add(uuid1);
		}
		if(allUuidList.size()<2) {
			return resultMap;
		}

		Map<String, Map<String, List<Map<String, String>>>> accountLabelMap = this.mGetTypeLabelByUuid(allUuidList, 0);
		Map<String, List<Map<String, String>>> account1Label = accountLabelMap.get(uuid1);

		uuid2List.forEach(uuid2 -> {
			if(Objects.equal(uuid1, uuid2)) {
				resultMap.put(uuid2, 0);
				return;
			}

			Map<String, List<Map<String, String>>> account2Label = accountLabelMap.get(uuid2);
			if(account2Label.isEmpty()) {
				resultMap.put(uuid2, 0);
				return;
			}

			if(this.hasSameNameInLabelMap(account1Label.get("like_type_label"), account2Label.get("description_label"))) {
				resultMap.put(uuid2, 1);
				return;
			}

			if(this.hasSameNameInLabelMap(account2Label.get("like_type_label"), account1Label.get("description_label"))) {
				resultMap.put(uuid2, 2);
				return;
			}

			if(this.hasSameNameInLabelMap(account1Label.get("hobby_label"), account2Label.get("hobby_label"))) {
				resultMap.put(uuid2, 3);
				return ;
			}
		});

		return resultMap;
	}

	private boolean hasSameNameInLabelMap(List<Map<String, String>> labelMapList1, List<Map<String, String>> labelMapList2) {
		if(labelMapList1 == null || labelMapList1.isEmpty() || labelMapList2 == null || labelMapList2.isEmpty()) {
			return false;
		}

		for(Map<String, String> labelMap1 : labelMapList1) {
			String labelName1 = MapUtils.getString(labelMap1, "name", "").trim();
			if(labelName1.isEmpty()) {
				continue;
			}
			for(Map<String, String> labelMap2 : labelMapList2) {
				String labelName2 = MapUtils.getString(labelMap2, "name", "").trim();
				if(labelName1.equals(labelName2)) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 *
	 * @param accountUuid
	 * @param type  类型 1-喜欢类型，2-个性标签
	 * @param ids  标签id 字符串，逗号分隔
	 * <AUTHOR>
	 * 2017年12月13日 下午2:16:50
	 */
	public void setAccountLabel(String accountUuid, Integer type, String ids) {
		boolean validClick = clickFilterService.isValidClick(ClickFilterService.TYPE_ACCOUNTS_LABEL, accountUuid);
		if(validClick){
			this.setAccountLabelByUuid(accountUuid, type, ids);
		}else{
			throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
		}
	}

	/**
	 * 根据用户uuid设置标签
	 * @param accountUuid 用户uuid
	 * @param type 类型 1-喜欢类型，2-个性标签
	 * @param ids 标签id 字符串，逗号分隔
	 */
	public void setAccountLabelByUuid(String accountUuid, Integer type, String ids) {
		List<Long> idSetAdd = Lists.newArrayList();
		List<Long> idSetSel = Lists.newArrayList();
		List<Long> oldDataList = Lists.newArrayList();
		idSetAdd.addAll(this.getLabelSet(ids));

		AccountsLabel accountsLabelSel = accountsLabelDao.findByAccountUuid(accountUuid);
		if (accountsLabelSel == null) {
			accountsLabelSel = new AccountsLabel();
			accountsLabelSel.setAccount_uuid(accountUuid);
			accountsLabelSel.setLike_type_label("");
			accountsLabelSel.setPersonality_label("");
			accountsLabelSel.setFriendship_prefer_label("");
		}

		if (type == 1) {
			String likeTypeLabel = accountsLabelSel.getLike_type_label();
			idSetSel.addAll(this.getLabelSet(likeTypeLabel));
			accountsLabelSel.setLike_type_label(ids);
		}
		if (type == 2) {
			String personalityLabel = accountsLabelSel.getPersonality_label();
			idSetSel.addAll(this.getLabelSet(personalityLabel));
			accountsLabelSel.setPersonality_label(ids);
		}
		// 交友偏好
		if (PersonalityLabelEnum.FRIENDSHIP_PREFERENCES.match(type)) {
			String friendshipPreferLabel = accountsLabelSel.getFriendship_prefer_label();
			idSetSel.addAll(this.getLabelSet(friendshipPreferLabel));
			accountsLabelSel.setFriendship_prefer_label(ids);
		}
		oldDataList.addAll(idSetSel);
		accountsLabelDao.merge(accountsLabelSel);

		// 修改缓存
		HashOperations<String, Object, Object> opsForHash = accountStringRedisTemplate.opsForHash();
		String redisKey = RedisKeyConstant.ACCOUNT_LABEL.setArg(accountUuid);
		Map<String, String> hashMap = Maps.newHashMap();
		hashMap.put("like_type_label", accountsLabelSel.getLike_type_label());
		hashMap.put("personality_label", accountsLabelSel.getPersonality_label());
		hashMap.put("friendship_prefer_label", accountsLabelSel.getFriendship_prefer_label());
		opsForHash.putAll(redisKey, hashMap);
		accountStringRedisTemplate.expire(redisKey, 7, TimeUnit.DAYS);
		this.updateUseCount(idSetAdd, idSetSel);

		// 发送埋点
		List<PersonalityLabel> oldData = personalityLabelService.findListByCache(oldDataList.stream().map(String::valueOf).collect(Collectors.toList()), null);
		List<PersonalityLabel> newData = personalityLabelService.findListByCache(this.getLabelSet(ids).stream().map(String::valueOf).collect(Collectors.toList()), null);
		sendEvent(accountUuid, oldData, newData);
	}

	public Collection<? extends Long> getLabelSet(String ids) {
		Set<Long> set = Sets.newHashSet();
		String[] split = ids.split(",");
		try {
			for (String string : split) {
				if (!Objects.equal(string, "")) {
					set.add(Long.parseLong(string));
				}
			}
			return set;
		} catch (Exception e) {
			logger.error("标签字符串转数组，ids异常，ids={}", ids, e);
			throw new ServiceException(SysCodeStatus.ERROR);
		}
	}

	/**
	 * 更新标签使用次数，并返回删除掉的标签id列表
	 *
	 * @param idSetAdd 用户新设置的标签id列表
	 * @param idSetSel 用户原来设置的标签id列表
	 * @return
	 */
	protected void updateUseCount(Collection<Long> idSetAdd, Collection<Long> idSetSel) {
		List<Long> idSetDel = Lists.newArrayList();
		//修改标签使用次数
		try {
			ValueOperations<String, String> opsForValue = accountStringRedisTemplate.opsForValue();
			//计数修改
			idSetDel.addAll(idSetSel);
			idSetSel.retainAll(idSetAdd);
			idSetAdd.removeAll(idSetSel);//计数 +1
			idSetDel.removeAll(idSetSel);//计数 -1

			for (Long integer : idSetAdd) {
				opsForValue.increment(RedisKeyConstant.LABEL_USE_COUNT.setArg(integer), 1L);
			}
			for (Long integer : idSetDel) {
				opsForValue.increment(RedisKeyConstant.LABEL_USE_COUNT.setArg(integer), -1L);
			}
		} catch (Exception e) {
			logger.warn("修改标签使用次数缓存发生异常。", e);
		}
	}

	/**
	 * 编辑标签埋点上报
	 *
	 * @param oldDataList 旧标签（全量）
	 * @param newDataList 新标签（全量）
	 */
	public void sendEvent(String accountUuid, List<PersonalityLabel> oldDataList, List<PersonalityLabel> newDataList) {
		// 移除标签
		ArrayList<PersonalityLabel> removeList = Lists.newArrayList();
		// 新增标签
		ArrayList<PersonalityLabel> addList = Lists.newArrayList();

		// 本次新增
		List<PersonalityLabel> addCollect = newDataList.stream().filter(newData ->
				oldDataList.stream().noneMatch(oldData ->
						java.util.Objects.equals(oldData.getId(), newData.getId())))
				.collect(Collectors.toList());
		addList.addAll(addCollect);

		// 本次踢出
		List<PersonalityLabel> removeCollect = oldDataList.stream().filter(oldData ->
				newDataList.stream().noneMatch(newData ->
						java.util.Objects.equals(oldData.getId(), newData.getId())))
				.collect(Collectors.toList());
		removeList.addAll(removeCollect);
		doSendEvent(addList, accountUuid, "新增");
		doSendEvent(removeList, accountUuid, "删除");
	}

	/**
	 * 埋点处理
	 *
	 * @param list
	 * @param accountUuid
	 * @param operation
	 */
	private void doSendEvent(List<PersonalityLabel> list, String accountUuid, String operation) {
		long milli = Instant.now().toEpochMilli();
		if (CollectionUtils.isNotEmpty(list)) {
			logger.info("标签{}, uuid:{}, size:{}", operation, accountUuid, list.size());
			list.forEach(data -> {
				// 埋点发送
				UserLabelEvent event = new UserLabelEvent();
				event.setId(data.getId());
				event.setType(data.getType());
				event.setContent(data.getContent());
				event.setCreateTime(milli);
				event.setChangeType(operation);
				eventTrackReporter.report(accountUuid, "user_social_preference_selection", event);
			});
		}
	}

    /**
     * 旧标签获取
     *
     * @param uuid
     * @return
     */
    public List<String> getLabel(String uuid) {
        ArrayList<String> labels = Lists.newArrayList();
        List<String> uuids = Arrays.asList(uuid);
        Map<String, List<PersonalityLabel>> likeTypeLabels = this.mGetAccountLabelByType(uuids, 1);
        Map<String, List<PersonalityLabel>> personalityLabels = this.mGetAccountLabelByType(uuids, 2);
        Map<String, Map<String, List<Map<String, String>>>> userHobbyLabels = this.mGetTypeLabelByUuid(uuids, 3);
        List<PersonalityLabel> like = likeTypeLabels.get(uuid);
        List<PersonalityLabel> personality = personalityLabels.get(uuid);
        List<Map<String, String>> hobby = userHobbyLabels.get(uuid).get("hobby_label");
        if (CollectionUtils.isNotEmpty(like)) {
            labels.addAll(like.stream().map(PersonalityLabel::getContent).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(personality)) {
            labels.addAll(personality.stream().map(PersonalityLabel::getContent).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(hobby)) {
            labels.addAll(hobby.stream().map(item -> item.get("name")).collect(Collectors.toList()));
        }

        return labels;
    }
}
