package cn.taqu.account.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.taqu.account.dao.AccountsRegAttributionDao;
import cn.taqu.account.model.AccountsRegAttribution;
import cn.taqu.core.utils.DateUtil;

/**
 * 用户注册归因
 * 
 * <AUTHOR>
 * @date 2021/03/09
 */
@Service
public class AccountsRegAttributionService {

	@Autowired
	private AccountsRegAttributionDao accountsRegAttributionDao;

    /**
     * 添加或更新
     * 
     * @param accountUuid
     * @param token
     * @param appVersion
     * @param appcode
     * @param cloned
     * @param channel
     * @param platformId
     * @param mediaCode
     * @param creativeLabel
     * @param gid
     * @param pid
     */
	@Transactional
    public void addOrUpdate(String accountUuid, String token, Integer appVersion, Integer appcode, Integer cloned,
        String channel, Integer platformId, String mediaCode, String creativeLabel, Long gid, Long pid) {
        AccountsRegAttribution accountsRegAttribution = accountsRegAttributionDao.getByAccountUuid(accountUuid);
        Long time = DateUtil.currentTimeSeconds();
        if(accountsRegAttribution == null) {
            accountsRegAttribution = new AccountsRegAttribution();
            accountsRegAttribution.setCreateTime(time);
        }
        
        accountsRegAttribution.setAccountUuid(accountUuid);
        accountsRegAttribution.setAppcode(appcode);
        accountsRegAttribution.setAppVersion(appVersion);
        accountsRegAttribution.setChannel(channel);
        accountsRegAttribution.setCloned(cloned);
        accountsRegAttribution.setCreativeLabel(creativeLabel);
        accountsRegAttribution.setMediaCode(mediaCode);
        accountsRegAttribution.setPlatformId(platformId);
        accountsRegAttribution.setToken(token);
        accountsRegAttribution.setUpdateTime(time);
        accountsRegAttribution.setGid(gid);
        accountsRegAttribution.setPid(pid);
        
        accountsRegAttributionDao.merge(accountsRegAttribution);
    }

//    /**
//     * @param accountUuid
//     * @return
//     */
//	@Deprecated
//    public String getAccountCreativeLabel(String accountUuid) {
//        String accountCreativeLabel = "";
//        AccountsRegAttribution accountsRegAttribution = accountsRegAttributionDao.getByAccountUuid(accountUuid);
//        if(accountsRegAttribution != null && StringUtils.isNotBlank(accountsRegAttribution.getCreativeLabel())) {
//            accountCreativeLabel = accountsRegAttribution.getCreativeLabel();
//        }
//        return accountCreativeLabel;
//    }
    
    /**
     * 用户是否存在表中
     * 
     * @param accountUuid
     * @return
     */
    public boolean existsByAccountUuid(String accountUuid) {
        return accountsRegAttributionDao.exists("account_uuid", accountUuid, Lists.newArrayList("id"));
    }

}
