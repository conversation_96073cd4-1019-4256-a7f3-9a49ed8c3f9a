package cn.taqu.account.service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.utils.StringUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsLikeLabelDao;
import cn.taqu.account.model.AccountsLikeLabel;
import cn.taqu.account.model.LikeLabel;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;

/**
 * 用户喜欢的标签
 *
 * <AUTHOR>
 * @date 2020/05/13 17:47
 */
@Service
public class AccountsLikeLabelService {
	private static final Logger LOGGER = LoggerFactory.getLogger(AccountsLikeLabelService.class);
	@Autowired
	private LikeLabelService likeLabelService;
	@Autowired
	private AccountsLikeLabelDao accountsLikeLabelDao;
	@Autowired
	private RedisLockUtil redisLockUtil;

	@Autowired
	@Qualifier("accountStringRedisTemplate")
	private StringRedisTemplate accountStringRedisTemplate;

	/**
	 *
	 * <AUTHOR>
	 * @date 2020/05/12 20:43
	 * @param type
	 * @param page
	 * @param keyWord
	 * @param accountUuid 选填，如果不存在，则不匹配是否绑定
	 * @return
	 */
	public List<Map<String, String>> listLikeLabelPage(Integer type, Integer page, String keyWord, String accountUuid) {
		// emoji校验
		if (StringUtil.hasUcs4(keyWord)) {
			throw new ServiceException(CodeStatus.EMOJI_ERROR);
		}

		List<Map<String, String>> reList = Lists.newArrayList();

		PageData<LikeLabel> pageData = likeLabelService.listLikeLabelPage(type, page, keyWord);
		// 查询用户 喜欢的类型
		List<Long> idList = Lists.newArrayList();
		if(StringUtils.isNotBlank(accountUuid)) {
		    idList = accountsLikeLabelDao.listIdByAccountUuid(accountUuid, type);
		}

		List<LikeLabel> data = pageData.getData();
		String isAdd = null;
		for (LikeLabel likeLabel : data) {
			HashMap<String, String> map = Maps.newHashMap();
			map.put("name", likeLabel.getName());
			map.put("pic_url", likeLabel.getPicUrl());
			map.put("id", likeLabel.getId().toString());
			map.put("extra", likeLabel.getExtra());
			if(idList.contains(likeLabel.getId())) {
				isAdd = "1";
			}else {
				isAdd = "0";
			}
			map.put("is_add", isAdd);
			reList.add(map);
		}

		return reList;
	}

	@Transactional
	public void setOne(String accountUuid, Integer type, Long id) {
		List<Long> idList = accountsLikeLabelDao.listIdByAccountUuid(accountUuid, type);
		if(idList.size() >= 10) {
			throw new ServiceException("最多只能添加10个哦～");
		}
		Long currentTimeSeconds = null;
		String lock = RedisKeyConstant.REDIS_LOCK_KEY.setArg("AccountsLikeLabelService_setOne", accountUuid);

		if(redisLockUtil.lock(lock, 2000)) {
    		try {
    			Sql sql = Sql.build(AccountsLikeLabel.class, AccountsLikeLabelDao.FIELDS_ALL);
    			sql.eq("account_uuid", accountUuid).eq("label_id", id);
    			List<AccountsLikeLabel> queryList = accountsLikeLabelDao.query(sql);
    			if(queryList != null && queryList.size() > 0) {
    				throw new ServiceException("该标签已添加了哦～");
    			}

    			currentTimeSeconds = DateUtil.currentTimeSeconds();
    			AccountsLikeLabel accountsLikeLabel = new AccountsLikeLabel();
    			accountsLikeLabel.setAccountUuid(accountUuid);
    			accountsLikeLabel.setCreateTime(currentTimeSeconds);
    			accountsLikeLabel.setLabelId(id);
    			accountsLikeLabel.setStickType(AccountsLikeLabel.StickType.NO.value);
    			accountsLikeLabel.setType(type);
    			accountsLikeLabel.setSeq(0);
    			accountsLikeLabel.setUpdateTime(currentTimeSeconds);

    			accountsLikeLabel = accountsLikeLabelDao.merge(accountsLikeLabel);
    			updateCache(accountsLikeLabel);
			} finally {
            	redisLockUtil.unLock(lock);
            }
    	};

//		BuryService.sendLikeLableUpdateToPostSearchStatistics(accountUuid, type, currentTimeSeconds);
	}

	private void updateCache(AccountsLikeLabel accountsLikeLabel) {
		if(accountsLikeLabel != null) {
			String key = RedisKeyConstant.ACCOUNT_LIKE_LABEL.setArg(accountsLikeLabel.getAccountUuid(),accountsLikeLabel.getType());
			HashOperations<String, Object, Object> opsForHash = accountStringRedisTemplate.opsForHash();
			opsForHash.put(key, accountsLikeLabel.getLabelId().toString(), JsonUtils.objectToString2(accountsLikeLabel));
			accountStringRedisTemplate.expire(key, 1, TimeUnit.DAYS);
		}else {
    		LOGGER.warn("传入的AccountsLikeLabel为null");
    	}
	}

	private void updateCache(List<AccountsLikeLabel> list) {
		for (AccountsLikeLabel accountsLikeLabel : list) {
			updateCache(accountsLikeLabel);
		}
	}

	/**
	 * 删除缓存
	 *
	 * <AUTHOR>
	 * @date 2020/05/18 21:26
	 * @param accountUuid
	 * @param type
	 * @param ids
	 */
	private void deleteCacheHashKey(String accountUuid, Integer type, String[] ids) {
		String key = RedisKeyConstant.ACCOUNT_LIKE_LABEL.setArg(accountUuid, type);
		HashOperations<String, Object, Object> opsForHash = accountStringRedisTemplate.opsForHash();
		Object[] objects = new Object[ids.length];
		for (int i = 0; i < ids.length; i++) {
			objects[i] = ids[i];
		}
		opsForHash.delete(key, objects);
	}

	@Transactional
	public void batchDel(String accountUuid, Integer type, String ids) {
		Sql sql = Sql.build("delete from accounts_like_label");
		String[] idArr = ids.split(",");
		sql.eq("account_uuid", accountUuid).in("label_id", idArr);
		accountsLikeLabelDao.update(sql);

		deleteCacheHashKey(accountUuid, type, idArr);

//		BuryService.sendLikeLableUpdateToPostSearchStatistics(accountUuid, type, DateUtil.currentTimeSeconds());
	}

	@Transactional
	public void batchStick(String accountUuid, Integer type, String ids) {
		String[] stickIds = ids.split(",");
		Map<Long, Integer> seqMap = Maps.newHashMap();
		Integer index = stickIds.length;
		for (String stickId : stickIds) {
			if(StringUtils.isNotBlank(stickId) ) {
				seqMap.put(Long.valueOf(stickId), index);
				index--;
			}
		}
		Long updateTime = DateUtil.currentTimeSeconds();

		Sql sql = Sql.build(AccountsLikeLabel.class, AccountsLikeLabelDao.FIELDS_ALL);
		sql.eq("account_uuid", accountUuid).eq("type", type);
		List<AccountsLikeLabel> list = accountsLikeLabelDao.query(sql);
		for (AccountsLikeLabel accountsLikeLabel : list) {
			Integer seq = seqMap.get(accountsLikeLabel.getLabelId());
			if(seq != null) {
				accountsLikeLabel.setSeq(seq);
				accountsLikeLabel.setStickType(AccountsLikeLabel.StickType.YES.value);
				accountsLikeLabel.setUpdateTime(updateTime);
			}
		}

		accountsLikeLabelDao.merge(list);
		updateCache(list);

//		BuryService.sendLikeLableUpdateToPostSearchStatistics(accountUuid, type, updateTime);
	}

	@Transactional
	public List<Map<String, String>> listAccountsLikeLabel(String accountUuid, Integer type) {
		List<Map<String, String>> reMapList = Lists.newArrayList();
		if(StringUtils.isBlank(accountUuid)) {
			return reMapList;
		}
		List<AccountsLikeLabel> list = Lists.newArrayList();
		// 先查缓存
		String key = RedisKeyConstant.ACCOUNT_LIKE_LABEL.setArg(accountUuid, type);
		HashOperations<String, Object, Object> opsForHash = accountStringRedisTemplate.opsForHash();
		Map<Object, Object> cacheResult = opsForHash.entries(key);
//		LOGGER.info("从缓存获取AccountsLikeLabel，key={}, cacheResult={}", key, cacheResult);
		if(cacheResult == null || cacheResult.isEmpty()) {
			// 查数据库
			Sql sql = Sql.build(AccountsLikeLabel.class, AccountsLikeLabelDao.FIELDS_ALL);
			sql.eq("account_uuid", accountUuid).eq("type", type);
			list = accountsLikeLabelDao.query(sql);
//			LOGGER.info("从数据库获取AccountsLikeLabel，sql={}, list={}", sql.getSql(), JSON.toJSONString(list));
			updateCache(list);
		}else {
			for(Map.Entry<Object, Object> entry : cacheResult.entrySet()) {
				String value = entry.getValue().toString();
				if(StringUtils.isNotBlank(value)) {
					try {
						list.add(JsonUtils.stringToObject2(value, new TypeReference<AccountsLikeLabel>() {
						}));
					} catch (Exception e) {
						LOGGER.warn("从缓存获取AccountsLikeLabel异常，cacheResult={}, json={}", JSON.toJSONString(cacheResult), value, e);
					}
				}
			}
		}

		if(list != null && list.size() > 0 ) {
			Set<Long> labelIds = list.stream().map(item -> item.getLabelId()).collect(Collectors.toSet());
			List<LikeLabel> likeLabelList = likeLabelService.listUseByIds(type, labelIds);

			// 排序过滤
			List<AccountsLikeLabel> stickList = list.stream()
					.filter(item -> Objects.equals(item.getStickType(), 1))
					.sorted(Comparator.comparing(AccountsLikeLabel::getSeq).reversed()).sorted(Comparator.comparing(AccountsLikeLabel::getUpdateTime).reversed())
					.collect(Collectors.toList());
			List<AccountsLikeLabel> noStickList = list.stream()
					.filter(item -> Objects.equals(item.getStickType(), 0))
					.sorted(Comparator.comparing(AccountsLikeLabel::getCreateTime))
					.collect(Collectors.toList());
			for (AccountsLikeLabel accountsLikeLabel : stickList) {
				for (LikeLabel likeLabel : likeLabelList) {
					if(Objects.equals(accountsLikeLabel.getLabelId(), likeLabel.getId())) {
						HashMap<String, String> map = Maps.newHashMap();
						map.put("name", likeLabel.getName());
						map.put("pic_url", likeLabel.getPicUrl());
						map.put("id", likeLabel.getId().toString());
						map.put("extra", likeLabel.getExtra());
						reMapList.add(map);
					}
				}
			}
			for (AccountsLikeLabel accountsLikeLabel : noStickList) {
				for (LikeLabel likeLabel : likeLabelList) {
					if(Objects.equals(accountsLikeLabel.getLabelId(), likeLabel.getId())) {
						HashMap<String, String> map = Maps.newHashMap();
						map.put("name", likeLabel.getName());
						map.put("pic_url", likeLabel.getPicUrl());
						map.put("id", likeLabel.getId().toString());
						map.put("extra", likeLabel.getExtra());
						reMapList.add(map);
					}
				}
			}
		}

		return reMapList;
	}

	public Map<String, List<Map<String, String>>> listAccountsLikeLabelAll(String accountUuid) {
		Map<String, List<Map<String, String>>> map = Maps.newHashMap();

		map.put(LikeLabel.Type.TV.mapKey, listAccountsLikeLabel(accountUuid, LikeLabel.Type.TV.value));
		// 音乐暂无
//		map.put(LikeLabel.Type.MUSIC.mapKey, listAccountsLikeLabel(accountUuid, LikeLabel.Type.MUSIC.value));
		map.put(LikeLabel.Type.STAR.mapKey, listAccountsLikeLabel(accountUuid, LikeLabel.Type.STAR.value));
		map.put(LikeLabel.Type.GAME.mapKey, listAccountsLikeLabel(accountUuid, LikeLabel.Type.GAME.value));

		return map;
	}

	@Async
	public void deleteAccountsLikeLabel(Long labelId) {
		LOGGER.info("删除用户关联的标签labelId={}",labelId);

		Sql sql = Sql.build(AccountsLikeLabel.class, Lists.newArrayList("id"));
		sql.eq("label_id", labelId);
		List<Long> idList = accountsLikeLabelDao.queryForList(sql, Long.class);
		if(idList != null && idList.size() > 0) {
			LOGGER.info("需要删除用户关联的总数为：{}。",idList.size());
			List<List<Long>> partition = Lists.partition(idList, 1000);
			for (List<Long> clist : partition) {
				deleteAccountsLikeLabel(clist);
			}
		}
		LOGGER.info("删除完成。",idList.size());
	}

	@Transactional
	public void deleteAccountsLikeLabel(List<Long> idList) {
		accountsLikeLabelDao.removeByIds(idList);
	}

	public Map<String, Map<String, List<Map<String, String>>>> listAccountsLikeLabelAllBatch(String[] accountUuids) {
		Map<String, Map<String, List<Map<String, String>>>> map = Maps.newHashMap();
		for (String accountUuid : accountUuids) {
			map.put(accountUuid, this.listAccountsLikeLabelAll(accountUuid));
		}
		return map;
	}


}
