package cn.taqu.account.service;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsCallerDao;
import cn.taqu.account.model.AccountsCaller;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.SpringContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Deprecated
@Service
public class AccountsCallerService {
    @Autowired
    private AccountsCallerDao accountsCallerDao;
    @Autowired
    private RedisLockUtil redisLockUtil;

    public void save(AccountsCaller accountsCaller) {
        if(StringUtils.isBlank(accountsCaller.getAccount_uuid())) {
            throw new ServiceException("uuid_empty", "uuid不能为空");
        }
        AccountsCaller dbEntity = accountsCallerDao.findByUuid(accountsCaller.getAccount_uuid());
        if(dbEntity == null) {
            dbEntity = new AccountsCaller();
            dbEntity.setCreate_time(DateUtil.currentTimeSeconds());
            dbEntity.setAccount_uuid(accountsCaller.getAccount_uuid());
        }
        if(accountsCaller.getVoice_status() != null) {
            dbEntity.setVoice_status(accountsCaller.getVoice_status());
        }
        if(accountsCaller.getVideo_status() != null) {
            dbEntity.setVideo_status(accountsCaller.getVideo_status());
        }
        dbEntity.setVoice_status(dbEntity.getVoice_status() == null ? 0 : dbEntity.getVoice_status());
        dbEntity.setVideo_status(dbEntity.getVideo_status() == null ? 0 : dbEntity.getVideo_status());

        accountsCallerDao.merge(dbEntity);
    }

    /**
     * 设置语音聊主状态
     * @param accountUuid
     * @param voiceStatus
     */
    public void setVoiceStatus(String accountUuid, Integer voiceStatus) {
        AccountsCaller accountsCaller = new AccountsCaller();
        accountsCaller.setAccount_uuid(accountUuid);
        accountsCaller.setVoice_status(voiceStatus);
        String lockKey = RedisKeyConstant.REDIS_LOCK_KEY.setArg("accountsCallerSave", accountUuid);
        redisLockUtil.executeWithLock(lockKey, 5000, () -> {
        	SpringContextHolder.getBean(AccountsCallerService.class).save(accountsCaller);
        	return true;
        });
    }

    /**
     * 设置视频聊主状态
     * @param accountUuid
     * @param videoStatus
     */
    public void setVideoStatus(String accountUuid, Integer videoStatus) {
        AccountsCaller accountsCaller = new AccountsCaller();
        accountsCaller.setAccount_uuid(accountUuid);
        accountsCaller.setVideo_status(videoStatus);
        String lockKey = RedisKeyConstant.REDIS_LOCK_KEY.setArg("accountsCallerSave", accountUuid);
        redisLockUtil.executeWithLock(lockKey, 5000, () -> {
        	SpringContextHolder.getBean(AccountsCallerService.class).save(accountsCaller);
        	return true;
        });
    }

}
