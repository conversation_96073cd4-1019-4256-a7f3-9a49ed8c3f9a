package cn.taqu.account.service;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsDressDao;
import cn.taqu.account.model.AccountsDress;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 用户个性装扮Service
 *
 * <AUTHOR>
 *         2017年8月1日 上午10:38:31
 */
@Service
@Transactional
public class AccountsDressService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsDress> {

    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsDressDao accountsDressDao;
    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;

    /**
     * 装扮/取消装扮
     *
     * @param uuid
     * @param dressId
     * @param status    1:已装扮;2:已过期;3:用户自行卸下装扮
     * @param dressType 装饰类型[1:头像挂件;2:帖子背景,3:聊天气泡]
     */
    public void setDressStatus(String uuid, Long dressId, Integer status, Integer dressType) {

    	// 2019.11.19 社区反馈跑脚本卸下装扮时，注册用户有返回 登陆已过期 情况，暂时处理卸下装扮不判断
    	if(!Objects.equals(status, 2)) {
    		accountsInfoService.guestTicketExpire(uuid);
    	}
        
        //校验
        dressType = (dressType == null) ? 1 : dressType;//默认头像挂件
        if ((dressType != 1 && dressType != 2 && dressType != 3) || (status != 1 && status != 2 && status != 3)) {
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR.value());
        }

        //判断用户是否存在
        long accountId = accountsService.getAccountIdByUuid(uuid);
        if (accountId == 0) {
            throw new ServiceException(CodeStatus.PERSON_INFO_NOT_FOUND.value());
        }
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        //查询是否有装扮信息
        AccountsDress accountsDress = accountsDressDao.getByUuid(uuid, dressType);
        if (accountsDress == null) {
            accountsDress = new AccountsDress();
            accountsDress.setAccount_uuid(uuid);
            accountsDress.setCreate_time(currentTimeSeconds);
        }

        //卸下时判断dreesId是否相同，不相同时不做处理
        if (status != 1 && !Objects.equals(dressId, accountsDress.getDress_id())) {
            return;
        }

        accountsDress.setDress_id(dressId);
        accountsDress.setStatus(status);
        accountsDress.setDress_type(dressType);
        accountsDress.setUpdate_time(currentTimeSeconds);
        accountsDressDao.merge(accountsDress);
        //更新缓存，缓存中只存储status为1的dressId
        String redusKeyUuid = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid);
        String hashKey = null;
        switch (dressType) {
            case 1:
                hashKey = "avatar_dress_id";
                break;
            case 2:
                hashKey = "post_dress_id";
                break;
            case 3:
                hashKey = "chat_dress_id";
                break;
        }

        if (status == 1) {
            accountStringRedisTemplate.opsForHash().put(redusKeyUuid, hashKey, String.valueOf(dressId));
        } else {
            accountStringRedisTemplate.opsForHash().delete(redusKeyUuid, hashKey);
        }
    }
}
