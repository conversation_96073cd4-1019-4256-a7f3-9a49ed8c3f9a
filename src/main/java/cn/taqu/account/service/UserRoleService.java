package cn.taqu.account.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.taqu.account.cron.MobilePlaceTask;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;

/**
 * 用户角色查询
 */
public class UserRoleService {

    private static Logger LOGGER = LoggerFactory.getLogger(MobilePlaceTask.class);

    /**
     * 判断是否是公会房主，0-不是房主，1-普通房主，2-公会房主
     *
     * @param accountUuid
     * @return
     */
    @Deprecated
    public static Integer getTypeOfChat(String accountUuid) {
        // 默认不是房主
        Integer result = 0;
        SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.LIVE_V1);
        Object[] form = {
                accountUuid
        };

        SoaResponse response = soaClient.call("ForumChatRoom", "isChat", form);
        if (response.fail()) {
            LOGGER.error("获取{}是否房主失败,原因: {}.", accountUuid, response.getMsg());
            // 请求失败当作是非房主处理
            return result;
        }
        try {
            Map<String,Object> dataMap = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String,Object>>() {});
            String stauts = MapUtils.getString(dataMap,"status");
            // 是房主
            if("1".equals(stauts)){
                result = 1;
            }
            Long consortiaId = MapUtils.getLong(dataMap,"consortia_id", 0L);
            // 公会聊主
            if(!Objects.equals(consortiaId, 0L)) {
                result = 2;
            }
            
        }catch (Exception e){
            LOGGER.error("获取是否是房主数据格式不正确,数据格式={}",JSON.toJSON(response), e);
        }

        return result;
    }

    /**
     * 获取主播的类型，0-不是主播，1-非公会主播，2-公会主播
     * @return
     */
    @Deprecated
    public static Integer getTypeOfHost(String accountUuid){
        Integer result = 0;

        Map<String,Integer> map = batchGetTypeOfHost(Lists.newArrayList(accountUuid));
        if(null != map.get(accountUuid)){
            result = map.get(accountUuid);
        }
        return result;
    }

    /**
     * 批量获取是否主播,type:0-非主播，1-素人主播，2-公会主播
     * @param accountUuidList
     * @return Map<uuid,type>
     */
    @Deprecated
    public static Map<String,Integer> batchGetTypeOfHost(List<String> accountUuidList) {
        Map<String,Integer> result = Maps.newHashMap();
        if(CollectionUtils.isEmpty(accountUuidList)){
            return result;
        }

        SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.LIVE_V1);
        SoaResponse response = soaClient.call("Host", "batchGetHostLiveInfo", accountUuidList);
        if (response.fail()) {
            LOGGER.error("获取{}是否主播失败,原因: {}.", accountUuidList, response.getMsg());

            // 请求失败当作是主播处理
            return result;
        }
        Map<String, Object> map = JSON.parseObject(response.getData(), Map.class);
        if(MapUtils.isNotEmpty(map)){
            accountUuidList.stream().forEach(uuid->{
                Integer type = 0;
                Map<String,Object> infoMap = (Map<String,Object>)map.get(uuid);
                //live_status  不等于0 就是主播
                Integer liveStatus = MapUtils.getInteger(infoMap,"live_status");
                //personal 1 就是 素人 0就是公会主播
                Integer personal = MapUtils.getInteger(infoMap,"personal");
                if(null != liveStatus && liveStatus != 0){
                    if(null != personal && personal == 1){
                        type = 1;
                    }else if(null != personal && personal ==0){
                        type = 0;
                    }
                }
                result.put(uuid,type);
            });
        }
        return result;
    }

    

}
