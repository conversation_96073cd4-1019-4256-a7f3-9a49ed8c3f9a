package cn.taqu.account.service;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.constant.h5link.CustomerServiceH5Mapping;
import cn.taqu.account.dto.ClonedPlanformAppVersionDto;
import cn.taqu.account.soa.AccountWealthLevelSoaService;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.Identities;
import cn.taqu.core.utils.JsonUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/5/28 下午2:50
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerService {

    private final StringRedisTemplate accountBizStringRedisTemplate;

    @Resource
    private AccountsInfoService infoService;

    @Resource
    private AccountWealthLevelSoaService wealthLevelSoaService;

    @Resource
    private SoaService soaService;

    public Map<String, String> linkInfo(String uuid, String mobileModel) {
        String visitorId = exchangeVisitorId(uuid);
        String modelKey = "account:customer:service:model:" + visitorId;
        accountBizStringRedisTemplate.opsForValue().set(modelKey, mobileModel, 5, TimeUnit.MINUTES);
        String nickname = infoService.getAccountNameByAccountUuidRedis(uuid);
        nickname = nickname + "_" + uuid;
        return ImmutableMap.of("link", CustomerServiceH5Mapping.resolveLink(visitorId, nickname));
    }

    public String exchangeVisitorId(String uuid) {
        String key = "account:customer:service:vid:" + uuid;
        String visitorId = accountBizStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(visitorId)) {
            // key不存在 生成双向映射
            visitorId = Identities.uuid2();
            String reverseKey = "account:customer:service:uuid:" + visitorId;
            accountBizStringRedisTemplate.opsForValue().set(key, visitorId, 14, TimeUnit.DAYS);
            accountBizStringRedisTemplate.opsForValue().set(reverseKey, uuid, 14, TimeUnit.DAYS);
        } else {
            String reverseKey = "account:customer:service:uuid:" + visitorId;
            accountBizStringRedisTemplate.expire(key, 7, TimeUnit.DAYS);
            accountBizStringRedisTemplate.expire(reverseKey, 7, TimeUnit.DAYS);
        }
        return visitorId;
    }

    public String exchangeUuid(String visitorId) {
        String key = "account:customer:service:uuid:" + visitorId;
        return accountBizStringRedisTemplate.opsForValue().get(key);
    }

    public UserInfo userInfo(String visitorId, String phone) {
        String uuid;
        if (StringUtils.isNotBlank(visitorId)) {
            uuid = exchangeUuid(visitorId);
        } else {
            // 先主包 女包 闪糖
            uuid = tryGetUuid(phone, "1", "1");
            if (uuid == null) {
                uuid = tryGetUuid(phone, "1", "4");
            }
            if (uuid == null) {
                uuid = tryGetUuid(phone, "1", "80");
            }
        }

        if (StringUtils.isEmpty(uuid)) {
            UserInfo userInfo = new UserInfo();
            userInfo.setUuid("not_found");
            userInfo.setName("游客");
            return userInfo;
        }

        String modelKey = "account:customer:service:model:" + visitorId;
        String mobileModel = accountBizStringRedisTemplate.opsForValue().get(modelKey);
        UserInfo userInfo = new UserInfo();
        userInfo.setVisitorId(visitorId);
        userInfo.setMobileModel(mobileModel);
        userInfo.setCustomerFlag(uuid);
        userInfo.setUuid(uuid);
        Map<String, Object> map = infoService.getUserInfo(uuid, UuidInfoField.ACCOUNT_NAME, UuidInfoField.SEX_TYPE, UuidInfoField.APP_VERSION, UuidInfoField.IP_PRO, UuidInfoField.IP_CITY);
        userInfo.setName(MapUtils.getString(map, UuidInfoField.ACCOUNT_NAME));
        userInfo.setSex("1".equals(MapUtils.getString(map, UuidInfoField.SEX_TYPE)) ? "男" : "女");
        String mobile = soaService.getMobileByUuidMP(uuid);
        userInfo.setPhone(mobileMask(mobile));
        userInfo.setAppVersion(MapUtils.getString(map, UuidInfoField.APP_VERSION));
        String province = MapUtils.getString(map, UuidInfoField.IP_PRO);
        String city = MapUtils.getString(map, UuidInfoField.IP_CITY);
        String location = "";
        if (StringUtils.isNotBlank(province)) {
            location = location.concat(province).concat("省");
        }
        if (StringUtils.isNotBlank(city)) {
            location = location.concat(city).concat("市");
        }
        userInfo.setLocation(location);
        Integer level = wealthLevelSoaService.get(uuid);
        userInfo.setLevel(level);
        log.info("三方用户信息响应 {}", JsonUtils.objectToString(userInfo));
        return userInfo;
    }

    public String tryGetUuid(String mobile, String appcode, String cloned) {
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        baseParams.setAppcode(appcode);
        baseParams.setCloned(cloned);

        try {
            return infoService.getUuidByMobile(mobile);
        } catch (ServiceException e) {
            return null;
        }
    }

    public void notifyUser(String visitorId, String notifyContent) {
        String uuid = exchangeUuid(visitorId);
        ClonedPlanformAppVersionDto versionDto = infoService.getAccountClonedPlanformAppVersion(uuid);
        if (versionDto != null) {
            SoaBaseParams baseParams = SoaBaseParams.fromThread();
            baseParams.setCloned(String.valueOf(versionDto.getCloned()));
            baseParams.setApp_version(String.valueOf(versionDto.getAppVersion()));
        }
        String nickname = infoService.getAccountNameByAccountUuidRedis(uuid);
        nickname = uuid + "_" + nickname;
        HashMap<String, Object> messageMap = Maps.newHashMap();
        messageMap.put("target_type", "uuid");
        messageMap.put("target", uuid);
        messageMap.put("targets", Collections.emptyList());
        messageMap.put("fields", Arrays.asList(notifyContent, CustomerServiceH5Mapping.resolveLink(visitorId, nickname), uuid));
        messageMap.put("event", "customer_service_msg_notify");
        MqResponse response = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("go_im_send_com_msg", messageMap, null);
        if (response.fail()) {
            log.warn("im客服消息提醒失败,用户uuid:{}, msg: {}", uuid, response.getMsg());
        } else {
            log.info("im客服消息提醒成功,用户uuid:{}", uuid);
        }
    }

    private static String mobileMask(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return "";
        }
        if (mobile.length() < 6) {
            return mobile;
        }
        StringBuilder sb = new StringBuilder(mobile);
        int length = sb.length();
        for (int i = 3; i < length - 4; i++) {
            sb.setCharAt(i, '*');
        }
        return sb.toString();
    }

    @Data
    public static class UserInfo {

        private String customerFlag;

        private String visitorId;

        private String uuid;

        private String name;

        private String sex;

        private String phone;

        private String appVersion;
        /**
         * 福建省-厦门市
         */
        private String location;

        /**
         * 财富等级
         */
        private Integer level;

        /**
         * 手机型号
         */
        private String mobileModel;


    }
}
