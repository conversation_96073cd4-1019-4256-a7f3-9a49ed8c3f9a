package cn.taqu.account.service;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsMedalInfoDao;
import cn.taqu.account.model.AccountsForumProfile;
import cn.taqu.account.model.AccountsMedalInfo;
import cn.taqu.account.search.AccountsMedalInfoSearch;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.PageUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 用户头衔信息表
 */
@Service
public class AccountsMedalInfoService {
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsMedalService accountsMedalService;
    @Autowired
    private AccountsForumProfileService accountsForumProfileService;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsMedalInfoDao accountsMedalInfoDao;

    /**
     * 分页查询
     * @param search
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<Map<String, String>> pageQuery(AccountsMedalInfoSearch search, Integer pageNumber, Integer pageSize) {
        Sql sql = Sql.build(AccountsMedalInfo.class, Arrays.asList("account_uuid", "medal_id", "end_time")).eq("status", 1);
        if(search != null && StringUtils.isNotBlank(search.account_uuid)) {
            sql = sql.eq("account_uuid", search.account_uuid);
        }
        if(search != null && search.medal_id != null) {
            sql = sql.eq("medal_id", search.medal_id);
        }

        PageData<AccountsMedalInfo> page = accountsMedalInfoDao.queryForPage(sql, pageNumber, pageSize);

        Map<Long, String> tempMedalName = new HashMap<>();
        List<String> tempUUidList = new ArrayList<>();
        List<Map<String, String>> result = new ArrayList<>();
        for(AccountsMedalInfo accountsMedalInfo : page.getData()) {
            String accountUuid = accountsMedalInfo.getAccount_uuid();
            Long medalId = accountsMedalInfo.getMedal_id();
            String medalName = tempMedalName.get(medalId);
            Long endTime = accountsMedalInfo.getEnd_time();
            if(medalName == null) {
                if(medalId == null) {
                    medalName = "";
                } else {
                    Map<String, String> medalInfo = accountsMedalService.getInfo(medalId, "medal_name");
                    medalName = MapUtils.getString(medalInfo, "medal_name", "");
                }
                tempMedalName.put(medalId, medalName);
            }
            tempUUidList.add(accountUuid);

            Map<String, String> item = new HashMap<>();
            item.put("account_uuid", accountUuid);
            item.put("medal_name", medalName);
            item.put("effect_days", endTime==null || endTime==0 ? "" : String.valueOf((endTime - DateUtil.getTodayEndSecond())/(86400)));

            result.add(item);
        }

        Map<String, Map<String, Object>> accountInfos = accountsInfoService.getInfoByUuid(tempUUidList.toArray(new String[tempUUidList.size()]), new String[]{"account_name"}, "1", true, true);
        for(Map<String, String> item : result) {
            String accountUuid = item.get("account_uuid");
            item.put("account_name", MapUtils.getString(accountInfos.get(accountUuid), "account_name", ""));
        }

        return new PageImpl<>(result, PageUtil.newPageable(pageNumber, pageSize), page.getTotal());
    }

    public List<String> findUuidByMedalId(Long medalId) {
        if(medalId == null || medalId <= 0) {
            return new ArrayList<>();
        }
        return accountsMedalInfoDao.findUuidByMedalId(medalId);
    }

    /**
     * 设置用户头衔
     * @param accountUuid
     * @param accountActor
     */
    @Transactional
    public void setAccountActor(String accountUuid, Integer accountActor, int effectDays) {
        if(accountActor == null || accountActor <= 0) {
            throw new ServiceException("error_actor", "错误的头衔id["+accountActor+"]");
        }
        if(effectDays <= 0) {
        	throw new ServiceException("error_effect_days", "天数不能小等于0天");
        }
        if(effectDays > 5000) {
        	throw new ServiceException("error_effect_days", "天数不能超过5000天");
        }

        accountsInfoService.guestTicketExpire(accountUuid);
        AccountsForumProfile accountsForumProfile = accountsForumProfileService.findOrCreate(accountUuid);
        if(accountsForumProfile.getId() == null) {
            accountsForumProfile.setAccount_actor(accountActor);
            accountsForumProfileService.merge(accountsForumProfile);
        } else {
            accountsForumProfileService.setAccountActor(accountUuid, accountActor);
        }
        this.updateDbMedal(accountUuid, accountActor.longValue(), effectDays);
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "account_actor", accountActor.toString());
        accountsPersonalInfoService.setDriverLevel(accountUuid, "V", true);
    }

    private void updateDbMedal(String accountUuid, Long medalId, int effectDays) {
        long endTime = DateUtil.getTodayEndSecond() + effectDays * 86400;
        if(accountsMedalInfoDao.accountExists(accountUuid)) {
            accountsMedalInfoDao.updateAccountMedal(accountUuid, medalId, 1, endTime, DateUtil.currentTimeSeconds());
            return;
        }
        

        AccountsMedalInfo accountsMedal = new AccountsMedalInfo();
        accountsMedal.setAccount_uuid(accountUuid);
        accountsMedal.setMedal_id(medalId==null ? 0 : medalId.longValue());
        accountsMedal.setUpdate_time(DateUtil.currentTimeSeconds());
        accountsMedal.setStatus(1);
        accountsMedal.setEnd_time(endTime);
        accountsMedalInfoDao.merge(accountsMedal);
    }
    
    /**
     * 回收头衔
     * @param accountUuid 用户uuid
     * @param accountActor 头衔id
     * @param status 回收状态 2:手动回收; 3:过期自动回收
     */
    @Transactional
    public void revokeAccountActor(String accountUuid, Integer accountActor, int status) {
        accountsInfoService.guestTicketExpire(accountUuid);
        if(!accountsMedalInfoDao.accountExists(accountUuid)) {
            throw new ServiceException("medal_no_exists", "用户还未拥有头衔");
        }
        accountsMedalInfoDao.updateAccountMedal(accountUuid, accountActor.longValue(), status, 0L, DateUtil.currentTimeSeconds());

        AccountsForumProfile accountsForumProfile = accountsForumProfileService.findOrCreate(accountUuid);
        if(accountsForumProfile.getId() == null) {
            accountsForumProfile.setAccount_actor(0);
            accountsForumProfileService.merge(accountsForumProfile);
        } else {
            accountsForumProfileService.setAccountActor(accountUuid, 0);
        }
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "account_actor", "0");
        accountsPersonalInfoService.setDriverLevel(accountUuid, "C", true);
    }

    /**
     * 延长有效期
     * @param accountUuid
     * @param effectDays
     */
    public void extendEffectDays(String accountUuid, int effectDays) {
        Sql sql = Sql.build(AccountsMedalInfo.class, Arrays.asList("id", "medal_id", "end_time"))
                .eq("account_uuid", accountUuid, false)
                .eq("status", 1);
        AccountsMedalInfo accountsMedalInfo = accountsMedalInfoDao.get(sql);
        if(accountsMedalInfo == null) {
            throw new ServiceException("medal_no_exists", "用户还未拥有该头衔");
        }

        Long beforeEndTime = accountsMedalInfo.getEnd_time();
        if(beforeEndTime == null || beforeEndTime == 0) {
            beforeEndTime = DateUtil.getTodayEndSecond();
        }
        long endTime = beforeEndTime + effectDays * 86400;
        
        if(endTime > Integer.MAX_VALUE) {
        	if(beforeEndTime > Integer.MAX_VALUE) {
        		throw new ServiceException("error_effect_days", "无法再延长有效期");
        	}else {
        		long days = ((long)Integer.MAX_VALUE - beforeEndTime) / 86400;
        		throw new ServiceException("error_effect_days", "有效期还能延长"+(days-1)+"天");
        	}
        }
        
        accountsMedalInfoDao.updateAccountMedal(accountUuid, accountsMedalInfo.getMedal_id(), 1, endTime, DateUtil.currentTimeSeconds());
    }
    
    @Transactional
    public void batchUnbindAccountMedal(List<String> accountUuidList) {
        accountsMedalInfoDao.batchUnbindAccountMedal(accountUuidList, DateUtil.currentTimeSeconds());
    }

    public List<AccountsMedalInfo> getByEndTime(Long expireTime, int size) {
        Sql sql = Sql.build("select account_uuid, medal_id from accounts_medal_info where end_time=? and status=? limit ?", expireTime, 1, size);
        return accountsMedalInfoDao.query(sql, AccountsMedalInfo.class);
    }
}
