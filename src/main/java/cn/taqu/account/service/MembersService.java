/**
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 *
 * @Project Name:tq-account
 * @ClassName:MembersService.java
 * @Package:cn.taqu.account.service
 * @Description:
 * @author:laik<PERSON><PERSON>
 * @date:2015年9月21日
 */
package cn.taqu.account.service;

import cn.taqu.account.common.PrometheusMetricsEnum;
import cn.taqu.account.common.TokenNewOldStatusEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.MembersAppInfoDao;
import cn.taqu.account.dao.MembersDao;
import cn.taqu.account.dao.TokenNewOldRelationDao;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.model.Members;
import cn.taqu.account.model.MembersAppInfo;
import cn.taqu.account.model.TokenNewOldRelation;
import cn.taqu.account.vo.MembersVo;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.StringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName:MembersService.java
 * @author:laikunzhen
 * @date:2015年9月21日
 */
@Service
@Slf4j
public class MembersService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.Members> {
    final Logger LOGGER = LoggerFactory.getLogger(MembersService.class);

    @Autowired
    private BuryService buryService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    private MembersDao membersDao;
    @Autowired
    private MembersAppInfoDao membersAppInfoDao;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private TokenNewOldRelationDao tokenNewOldRelationDao;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private StringRedisTemplate accountBizStringRedisTemplate;


    /**
     * 注册设备,注册过程中用于记录客户端的设备信息,<b>该接口只做为内部接口使用，禁止客户程序直接调用</b>
     * ServiceException异常不回滚事务
     *
     *
     * @Title regMembers
     * @param token 设备token
     * @param appVersion 应用版本号
     * @param appcode 应用类型码，例如1:性价比;2:蓝色妖姬等
     * @param platformId 平台码1:android;2:iphone;3:ipad
     * @param place 应用渠道码，例如tongbu,91zhushou
     * @return 注册设备成功返回设备实体
     * <AUTHOR>
     * @Date 2015年9月30日 下午2:14:54
     */
    @Transactional(noRollbackFor = ServiceException.class)
    public MembersVo regMembers(String token, Long appVersion, Integer appcode, Integer cloned, Integer platformId, String place) {
        MembersVo membersVo = new MembersVo();
        Long nowTime = DateUtil.currentTimeSeconds();

        Members members = membersDao.findOneByToken(token);
        // 没有登记过
        if (members == null) {
            //继续注册设备控制
            String tempKey = "member:reg:times:" + token;
            long times = accountStringRedisTemplate.opsForValue().increment(tempKey, 1);
            accountStringRedisTemplate.expire(tempKey, 5, TimeUnit.SECONDS);
            if (times > 1) {
                throw new ServiceException("member_reg_frequency", "设备注册请求过于频繁");
            }
            members = new Members();
            members.setToken(token);
            members.setCreate_time(nowTime);
            members = membersDao.merge(members);

            membersVo.setId(members.getId());
            membersVo.setFlag(false);

            MembersAppInfo appInfo = new MembersAppInfo();
            appInfo.setMember_id(members.getId());
            appInfo.setApp_version(appVersion);
            appInfo.setAppcode(appcode);
            appInfo.setCloned(cloned);
            appInfo.setPlatform_id(platformId);
            appInfo.setCreate_time(nowTime);
            appInfo.setPlace(place);
            appInfo.setIs_jailbroken(0);
            appInfo.setNotice_enable(1);
            membersAppInfoDao.merge(appInfo);

            //新注册设备并且是ios的推tqmq
            if(!Objects.equals(1, platformId)) {
                Map<String, String> data = new HashMap<>();
                data.put("token", token);
                data.put("app_version", String.valueOf(appVersion));
                MqResponse mqResponse = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("new_device_to_live", data, 30L);
                if(mqResponse.fail()) {
                    logger.error("新注册设备推送直播队列异常, {}-{}", mqResponse.getCode(), mqResponse.getMsg());
                }
            }
            // 风控埋点 只有新增才推
            buryService.toAntispam(token, nowTime, platformId, "equipmentActivation");
        } else {//有登录过
            // 根据member_id更新member_app_info表
            membersAppInfoDao.updateByMemberId(appVersion, appcode, cloned, place, platformId, nowTime, members.getId());
            membersVo.setId(members.getId());
            membersVo.setFlag(true);
        }
        // 数据画像中心埋点
        buryService.toDataCenter("device", "reg", String.valueOf(membersVo.getId()), appcode, null);

        String memberCreateTimeKey = RedisKeyConstant.MEMBER_CREATE_TIME.setArg(token);
        Long expire = accountStringRedisTemplate.getExpire(memberCreateTimeKey);
        if(expire == -2) {//key不存在
            accountStringRedisTemplate.opsForValue().set(memberCreateTimeKey, String.valueOf(members.getCreate_time()));
        }

        if(expire < 0) {//没有过期时间
            accountStringRedisTemplate.expire(memberCreateTimeKey, 15, TimeUnit.DAYS);
        }

        this.addTokenActiveDateList(token, nowTime);
        try {
            String tokenPre = String.valueOf(token.charAt(0));
            String appVersionStr = String.valueOf(appVersion);
            logger.debug("tokenPre={},appVersionStr={}", tokenPre, appVersionStr);
            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_TOKEN_ALL, appVersionStr, tokenPre);
        }catch (Exception e){
            log.warn("普罗米修斯监控异常e=",e);
        }
        return membersVo;
    }

    /**
     * 设备活跃日期历史记录
     * @param token
     * @param time
     */
    private void addTokenActiveDateList(String token, Long time) {
        // TODO 废弃功能准备下线
        String key = RedisKeyConstant.TOKEN_ACTIVE_DATE_LIST.setArg(token);
        accountStringRedisTemplate.opsForZSet().add(key, DateUtil.dateToString8(DateUtil.fromSecond(time)), time);
        //数据太多时，删除至只剩100条
        long count = accountStringRedisTemplate.opsForZSet().zCard(key);
        if(count > 100) {
            accountStringRedisTemplate.opsForZSet().removeRange(key, 0, count - 100);
        }
        accountStringRedisTemplate.expire(key, 100, TimeUnit.DAYS);

        // 先双写，一个月后下线旧pod
        syncNewRedis(token, time);
    }

    /**
     * 设备活跃日期历史记录
     * @param token
     * @param time
     */
    private void syncNewRedis(String token, Long time) {
        String key = RedisKeyConstant.TOKEN_ACTIVE_DT.setArg(token);
        accountBizStringRedisTemplate.opsForZSet().add(key, DateUtil.dateToString8(DateUtil.fromSecond(time)), time);
        //数据太多时，删除至只剩3条
        long count = accountBizStringRedisTemplate.opsForZSet().zCard(key);
        if(count > 3) {
            accountBizStringRedisTemplate.opsForZSet().removeRange(key, 0, count - 3);
        }
        accountBizStringRedisTemplate.expire(key, 30, TimeUnit.DAYS);
    }

    /**
     * 获取设备token的前一个活跃时间，非今天的
     * @param token
     * @return
     */
    public Long getTokenPrevActiveTime(String token) {
        int dateIntValue = Integer.valueOf(DateUtil.dateToString8(new Date()));
        String key = RedisKeyConstant.TOKEN_ACTIVE_DATE_LIST.setArg(token);
        Set<ZSetOperations.TypedTuple<String>> typedTupleSet = accountStringRedisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 2);
        // TODO 待切换成新实例
        //String key = RedisKeyConstant.TOKEN_ACTIVE_DT.setArg(token);
        //Set<ZSetOperations.TypedTuple<String>> typedTupleSet = accountBizStringRedisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 2);

        for(ZSetOperations.TypedTuple<String> typedTuple : typedTupleSet) {
            if(Integer.valueOf(typedTuple.getValue() ) != dateIntValue) {
                return typedTuple.getScore().longValue();
            }
        }
        return 0L;
    }

    /**
     * 根新设备关联的账号信息<br/>
     * 此方法设置为总是开启新事务，保证此事务的异常不会影响其他调用者
     * @Title bindMembers
     * @param accountUuid 账号id
     * @return 根据成功返回null, 否则返回相应的错误{@link CodeStatus}
     * <AUTHOR>
     * @Date 2015年9月28日 下午8:09:41
     */
    @Transactional
    public void bindMembers(String accountUuid) {
        // 根据token查询设备id
        String token = RequestParams.getSoa_basic_java().getToken();
        if (token == null || (token = token.trim()).length() == 0) {
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }
        Long memberId = membersDao.getIdByToken(token);
        if (memberId == null) {
            throw new ServiceException(CodeStatus.NO_MEMBER_FOUND);
        }
        boolean result = this.updateBindMembers(accountUuid, memberId);
        if (!result) {
            throw new ServiceException(CodeStatus.UPDATE_MEMBER_ID_FAILURE);
        }
    }

    /**
     * 根据accountUuid修改memberid的值，修改成功返回true,否则返回false
     * @Title updateBindMembers
     * @param accountUuid
     * @param member_id
     * @return
     * <AUTHOR>
     * @Date 2015年9月28日 下午7:37:21
     */
    @Transactional
    public boolean updateBindMembers(String accountUuid, Long member_id) {
        accountsManager.updateMemberByUuid(member_id, accountUuid);
        String redisKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        accountStringRedisTemplate.opsForHash().put(redisKey, "member_id", member_id.toString());
        return true;
    }

    public Long getCreateTimeFromCache(String token) {
        Object createTime = accountStringRedisTemplate.opsForValue().get(RedisKeyConstant.MEMBER_CREATE_TIME.setArg(token));
        if(createTime != null && !createTime.toString().trim().isEmpty()) {
            return StringUtil.toLong(createTime.toString().trim(), 0L);
        }
        Long time = membersDao.getCreateTimeByToken(token);
        return time==null ? 0 : time;
    }

    public String getTokenById(Long memberId, boolean master) {
        return membersDao.getTokenById(memberId, master);
    }

    /**
     * 根据uuid查询账号对应的设备token，返回以uuid为key，以token为value的map
     * @param uuids
     * @return
     */
    public Map<String, String> getTokenByUuids(String[] uuids) {
        if (uuids == null || uuids.length == 0) {
            log.warn("无效uuid");
            return new HashMap<>(0);
        }
        // 请求数量跟踪
        if (uuids.length >= 50) {
            log.info("请求uuids较大:{}", uuids.length);
        }

        Map<String, Map<String, Object>> uuidMemberIdMap = accountsInfoService.getInfoByUuid(uuids, new String[]{"member_id"}, "1", true, true);
        Set<Long> memberIds = new HashSet<>(uuidMemberIdMap.size());
        Map<Long, List<String>> memberIdUuidMap = new HashMap<>();
        for (Map.Entry<String, Map<String, Object>> entry : uuidMemberIdMap.entrySet()) {
            String uuid = entry.getKey();
            Long memberId = MapUtils.getLong(entry.getValue(), "member_id", null);
            if (memberId == null) {
                continue;
            }

            memberIds.add(memberId);
            List<String> uuidList = memberIdUuidMap.get(memberId);
            if (uuidList == null) {
                uuidList = new ArrayList<>();
                memberIdUuidMap.put(memberId, uuidList);
            }
            uuidList.add(uuid);
        }

        Map<String, String> result = new HashMap<>(memberIds.size());
        if (memberIds.isEmpty()) {
            return result;
        }

        List<Object[]> members = this.membersDao.mGetTokenByIdList(memberIds);
        for (Object[] member : members) {
            Number id = (Number) member[0];
            String token = StringUtils.trimToEmpty((String) member[1]);
            List<String> uuidList = memberIdUuidMap.get(id.longValue());
            for (String uuid : uuidList) {
                result.put(uuid, token);
            }
        }

        return result;
    }

    /**
     * 根据用户uuid批量用户设备信息
     * @param accountUuids
     * @return
     */
    public Map<String, Map<String, String>> mGetInfoByAccountUuids(String[] accountUuids) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        Map<String, Map<String, Object>> uuidMemberIdMap = accountsInfoService.getInfoByUuid(accountUuids, new String[]{"member_id"}, "1", true, true);
        List<Long> memberIdList = new ArrayList<>(uuidMemberIdMap.size());
        Map<Long, Set<String>> memberUuidMap = new HashMap<>();
        for(Map.Entry<String, Map<String, Object>> entry : uuidMemberIdMap.entrySet()) {
            Long memberId = MapUtils.getLongValue(entry.getValue(), "member_id", 0);
            String accountUuid = entry.getKey();
            memberIdList.add(memberId);
            Map<String, String> memberIdMap = new HashMap<>();
            memberIdMap.put("member_id", memberId.toString());
            result.put(accountUuid, memberIdMap);

            Set<String> accountUuidSet = memberUuidMap.get(memberId);
            if(accountUuidSet == null) {
                accountUuidSet = new HashSet<>();
                memberUuidMap.put(memberId, accountUuidSet);
            }
            accountUuidSet.add(accountUuid);
        }
        if(memberIdList.isEmpty()) {
            return result;
        }

        List<Members> membersList = membersDao.findByMemberIds(memberIdList);
        List<Map<String, String>> membersInfoList = this.mGetInfoFromMembers(membersList);
        for(Map<String, String> memberInfo : membersInfoList) {
            Long memberId = MapUtils.getLongValue(memberInfo, "member_id", 0);
            Set<String> accountUuidSet = memberUuidMap.get(memberId);
            if(accountUuidSet != null) {
                for(String accountUuid : accountUuidSet) {
                    result.put(accountUuid, memberInfo);
                }
            }
        }

        return result;

    }

    /**
     * 根据token批量获取设备信息，返回以token为key, 以设备信息为value的map
     * @param tokenList
     * @return
     */
    public Map<String, Map<String, String>> mGetInfoByTokenList(List<String> tokenList) {
        List<Members> membersList = membersDao.findByTokens(tokenList);
        List<Map<String, String>> membersInfoList = this.mGetInfoFromMembers(membersList);
        Map<String, Map<String, String>> result = new HashMap<>(membersInfoList.size());
        for(Map<String, String> membersInfo : membersInfoList) {
            String token = MapUtils.getString(membersInfo, "token", "");
            result.put(token, membersInfo);
        }
        return result;
    }

    /**
     * 根据设备id批量获取设备信息，返回以设备id为key, 以设备信息为value的map
     * @param memberIdList
     * @return
     */
    public Map<Long, Map<String, String>> mGetInfoByMemberIdList(List<Long> memberIdList) {
        List<Members> membersList = membersDao.findByMemberIds(memberIdList);
        List<Map<String, String>> membersInfoList = this.mGetInfoFromMembers(membersList);
        Map<Long, Map<String, String>> result = new HashMap<>(membersInfoList.size());
        for(Map<String, String> membersInfo : membersInfoList) {
            Long memberId = MapUtils.getLongValue(membersInfo, "member_id");
            result.put(memberId, membersInfo);
        }
        return result;
    }

    /**
     * 根据设备id获取设备信息，返回以设备id为key, 以设备信息为value的map
     * @param memberIdList
     * @param returnToken 是否返回token 0:否; 1:是;
     * @return
     */
    public Map<Long, Map<String, String>> mGetInfoByMemberIdList(List<Long> memberIdList, String returnToken) {
        Map<Long, Map<String, String>> result = new HashMap<>();

        List<MembersAppInfo> membersAppInfoList = membersAppInfoDao.findByMemberIds(memberIdList);
        for(MembersAppInfo membersAppInfo : membersAppInfoList) {
            Map<String, String> itemMap = new HashMap<>();
            itemMap.put("app_version", StringUtil.nullNumberToEmptyString(membersAppInfo.getApp_version()));
            itemMap.put("appcode", StringUtil.nullNumberToEmptyString(membersAppInfo.getAppcode()));
            itemMap.put("place", StringUtils.trimToEmpty(membersAppInfo.getPlace()));
            itemMap.put("platform_id", StringUtil.nullNumberToEmptyString(membersAppInfo.getPlatform_id()));
            itemMap.put("create_time", StringUtil.nullNumberToEmptyString(membersAppInfo.getCreate_time()));
            itemMap.put("member_id", StringUtil.nullNumberToEmptyString(membersAppInfo.getMember_id()));
            itemMap.put("is_jailbroken", StringUtil.nullNumberToEmptyString(membersAppInfo.getIs_jailbroken()));
            result.put(membersAppInfo.getMember_id(), itemMap);
        }

        if(Objects.equals("1", returnToken)) {
            List<Object[]> membersList = membersDao.mGetTokenByIdList(memberIdList);
            for(Object[] members : membersList) {
                Long memberId = Long.valueOf(members[0].toString());
                Map<String, String> itemMap = result.get(memberId);
                if(itemMap!=null) {
                    itemMap.put("token", members[1]==null?"":members[1].toString());
                }
            }
        }

        return result;
    }

    private List<Map<String, String>> mGetInfoFromMembers(List<Members> membersList) {
        List<Map<String, String>> result = new ArrayList<>();
        if (membersList.isEmpty()) {
            return result;
        }

        //token对应的member记录，同一token存在多个member记录，取最大的那一个memberId的记录
        Map<String, Members> tokenMemberMap = new HashMap<>();
        for(Members members : membersList) {
            String token = members.getToken();
            Members maxIdMember = tokenMemberMap.get(token);
            Long maxId = maxIdMember==null ? null : maxIdMember.getId();
            Long id = members.getId();
            if(maxId == null || maxId < id) {
                tokenMemberMap.put(token, members);
            }
        }

        List<Members> finalMembersList = new ArrayList<>(tokenMemberMap.size());
        Set<Long> memberIdSet = new HashSet<>(tokenMemberMap.size());
        for(Map.Entry<String, Members> entry : tokenMemberMap.entrySet()) {
            Members members = entry.getValue();
            memberIdSet.add(members.getId());
            finalMembersList.add(members);

        }

        List<MembersAppInfo> membersAppInfoList = membersAppInfoDao.findByMemberIds(memberIdSet);
        for(Members members : finalMembersList) {
            Map<String, String> itemMap = new HashMap<>();
            itemMap.put("member_id", StringUtil.nullNumberToEmptyString(members.getId()));
            itemMap.put("create_time", StringUtil.nullNumberToEmptyString(members.getCreate_time()));
            itemMap.put("token", StringUtils.trimToEmpty(members.getToken()));

            for(MembersAppInfo membersAppInfo : membersAppInfoList) {
                if(Objects.equals(members.getId(), membersAppInfo.getMember_id())) {
                    itemMap.put("app_version", StringUtil.nullNumberToEmptyString(membersAppInfo.getApp_version()));
                    itemMap.put("appcode", StringUtil.nullNumberToEmptyString(membersAppInfo.getAppcode()));
                    itemMap.put("place", StringUtils.trimToEmpty(membersAppInfo.getPlace()));
                    itemMap.put("platform_id", StringUtil.nullNumberToEmptyString(membersAppInfo.getPlatform_id()));
                    break;
                }
            }

            result.add(itemMap);
        }

        return result;
    }

    @Transactional
	public void reportAppInfo(String token, Integer platformId, Long appVersion, Integer appcode, Integer cloned, String channel, String accountUuid, Integer noticeEnable) {
        //接口频繁调用控制
        String tempKey = "token:report:times:" + token;
        long times = accountStringRedisTemplate.opsForValue().increment(tempKey, 1);
        accountStringRedisTemplate.expire(tempKey, 5, TimeUnit.SECONDS);
        if (times > 1) {
            return;
        }
        MembersVo membersVo = this.regMembers(token, appVersion, appcode, cloned, platformId, channel);
        membersAppInfoDao.updateNoticeEnable(noticeEnable, membersVo.getId());

        accountsMemberInfoService.createOrUpdateByUuid(accountUuid, token, appVersion, appcode, cloned, channel, platformId);
    }

    /**
     * 记录ios系统版本
     *
     * @param token
     * @param iosSysVersion
     */
	public void regIosSysVersion(String token, String iosSysVersion) {
	    if(StringUtils.isNotBlank(token) && StringUtils.isNotBlank(iosSysVersion)) {
	        logger.info("记录版本={}", iosSysVersion);
	        // 记录ios版本号
	        String redisKey = RedisKeyConstant.TOKEN_IOS_SYS_VERSION.setArg(token);
            accountStringRedisTemplate.opsForValue().set(redisKey, iosSysVersion);
	    }
	}

	/**
	 *
	 * @param newToken
	 * @param oldToken
	 * @return 返回map， 包含old_token、token_status(1-)
	 */
	@Deprecated
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> convertToken(String newToken, String oldToken) {
        Map<String, String> result = Maps.newHashMap();
        String token = "";
        String tokenStatus = TokenNewOldStatusEnum.NORMAL.value;

        Long time = DateUtil.currentTimeSeconds();
        // 2020.11.05增加判断 如果新旧token一致，则旧token置空
        if(Objects.equals(newToken, oldToken)) {
            oldToken = "";
        }

        // 都不为空
        if(StringUtils.isNotBlank(newToken) && StringUtils.isNotBlank(oldToken)) {
            List<String> oldTokenList = tokenNewOldRelationDao.getOldTokenListLimit2(newToken);
            if( CollectionUtils.isNotEmpty(oldTokenList)) {
                // 存在1个不相等的 或者 存在2个以上
                if((oldTokenList.size() > 1) || (oldTokenList.size() == 1 && !Objects.equals(oldTokenList.get(0), oldToken))){
                    tokenStatus = TokenNewOldStatusEnum.HAS_RELATION.value;
                }
            }

            TokenNewOldRelation sel = tokenNewOldRelationDao.getByNewOldToken(newToken, oldToken);
            if(sel == null) {
                tokenNewOldRelationDao.insertIgnore(newToken, oldToken, time, time);
            }

            token = oldToken;
        }

        // 新不为空，旧为空
        if(StringUtils.isNotBlank(newToken) && StringUtils.isBlank(oldToken)) {
            //  新token查询查询2个
            List<String> oldTokenList = tokenNewOldRelationDao.getOldTokenListLimit2(newToken);
            // 如果 空
            if(CollectionUtils.isEmpty(oldTokenList)) {
                token = "";
            }else if(oldTokenList.size() == 1) {
                // 如果只有1条 返回
                token = oldTokenList.get(0);
            }else {
                // 如果不止1条 生成新的
                String generateToken = UUIDService.generateTokenUUID();
                tokenNewOldRelationDao.insertIgnore(newToken, generateToken, time, time);
                LOGGER.warn("生成新token：newToken={}, generateToken={}", newToken, generateToken);
                token = generateToken;
                tokenStatus = TokenNewOldStatusEnum.GENERATE_TOKEN.value;
            }
        }

        result.put("old_token", token);
        result.put("token_status", tokenStatus);
        return result;
    }

    /**
     * @param
     * @return
     */
    public Map<String, String> getIosSysVersionByUuid(String accountUuid) {
        Map<String, String> result = Maps.newHashMap();
        Map<String, Map<String, Object>> usersResultMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"app_version","platform_id","member_id"}, "1", true,true);
        Map<String, Object> map = usersResultMap.get(accountUuid);

        result.put("app_version", MapUtils.getString(map,"app_version", ""));
        result.put("platform_id", MapUtils.getString(map,"platform_id", ""));
        result.put("ios_sys_version", "");

        Long memberId = MapUtils.getLong(map, "member_id", null);
        if(memberId != null) {
            String token = getTokenById(memberId, true);
            if(StringUtils.isNotBlank(token)) {
                String redisKey = RedisKeyConstant.TOKEN_IOS_SYS_VERSION.setArg(token);
                String iosSysVersion = accountStringRedisTemplate.opsForValue().get(redisKey);
                result.put("ios_sys_version", StringUtils.defaultString(iosSysVersion, ""));
            }
        }
        return result;
    }

}
