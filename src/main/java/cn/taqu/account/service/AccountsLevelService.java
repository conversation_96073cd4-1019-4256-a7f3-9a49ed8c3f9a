package cn.taqu.account.service;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsLevelDao;
import cn.taqu.account.model.AccountsLevel;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.StringUtil;

@Deprecated
@Service
@Transactional
public class AccountsLevelService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsLevel> {

    @Autowired
    @Qualifier("levelStringRedisTemplate")
    private StringRedisTemplate levelStringRedisTemplate;
    @Autowired
    private AccountsLevelDao accountsLevelDao;

    public AccountsLevel getByIdFromDb(Long id) {
        return accountsLevelDao.getOneById(id);
    }

    private void updateCache(AccountsLevel accountsLevel) {
        Integer appcode = ToolsService.normalizedAppcode(accountsLevel.getAppcode());
        Map<String, String> hashValues = Maps.newHashMap();
        hashValues.put("id", StringUtil.nullNumberToEmptyString(accountsLevel.getId()));
        hashValues.put("level_num", StringUtil.nullNumberToEmptyString(accountsLevel.getLevel_num()));
        hashValues.put("min_level_score", StringUtil.nullNumberToEmptyString(accountsLevel.getMin_level_score()));
        hashValues.put("max_level_score", StringUtil.nullNumberToEmptyString(accountsLevel.getMax_level_score()));
        hashValues.put("privilege_id", StringUtil.nullNumberToEmptyString(accountsLevel.getPrivilege_id()));
        hashValues.put("honor_man", StringUtils.trimToEmpty(accountsLevel.getHonor_man()));
        hashValues.put("honor_women", StringUtils.trimToEmpty(accountsLevel.getHonor_women()));
        hashValues.put("honor_unsex", StringUtils.trimToEmpty(accountsLevel.getHonor_unsex()));
        hashValues.put("desc", StringUtils.trimToEmpty(accountsLevel.getDesc()));
        hashValues.put("privilege_desc", StringUtils.trimToEmpty(accountsLevel.getPrivilege_desc()));
        hashValues.put("appcode", appcode.toString());
        levelStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.LEVEL_INFO.setArg(accountsLevel.getId()), hashValues);
        levelStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.LEVEL_NUM_INFO.setArg(appcode, accountsLevel.getLevel_num()), hashValues);
    }

    /**
     * 判断new_score对应的等级是否与old_score对应的等级相等，如果相等则返回-1，否则返回new_score对应的等级
     *
     * @param oldScore 原来积分
     * @param newScore 新的积分
     * @param appcode  应用类型码 1:他趣
     * @return
     * @Title isUp
     * <AUTHOR>
     * @Date 2015年9月25日 下午12:06:14
     */
    public int isUp(Long oldScore, Long newScore, Integer appcode) {
        //获取等级列表
        List<AccountsLevel> levelList = this.findAll(appcode);

        int oldLevelNum = 0;
        int newLevelNum = 0;
        int lastLevelNum = 0;//最高等级
        long maxLevelScore = 0;//已设置的等级最高分值
        for (AccountsLevel level : levelList) {
            int levelNum = level.getLevel_num();
            long minScore = level.getMin_level_score();
            long maxScore = level.getMax_level_score();

            if (maxLevelScore < maxScore) {
                maxLevelScore = maxScore;
                lastLevelNum = levelNum;
            }
            if (oldScore >= 0 && minScore <= oldScore && oldScore < maxScore) {
                oldLevelNum = levelNum;
            }
            if (minScore <= newScore && newScore < maxScore) {
                newLevelNum = levelNum;
            }
        }

        if (newScore >= maxLevelScore) {
            return lastLevelNum;
        }
        return newLevelNum == oldLevelNum ? -1 : newLevelNum;
    }

    /**
     * 根据level id获取经验等级相关配置，可通过optionResult参数来指定需要获取哪个字段的信息，如果没有获取到返回异常
     *
     * @param levelId  account_level表的id
     * @param sex_type 性别1:男;2:女;其他:未知;
     * @param fields   (选填)指定要返回的字段
     * @return 返回获取到的信息，
     * @Title getInfo
     * <AUTHOR>
     * @Date 2015年9月28日 上午10:53:41
     */
//    @Transactional
    public Map<String, String> getInfo(Long levelId, Integer sex_type, String... fields) {
        return this.getInfoSmart(levelId, true, false, sex_type, null, fields);
    }

    @Transactional
    public Map<String, String> getInfoFromMasterDB(Long levelId, Integer sex_type, String... fields) {
        return this.getInfoSmart(levelId, true, true, sex_type, null, fields);
    }

    /**
     * 根据levelNum获取经验等级相关配置，可通过optionResult参数来指定需要获取哪个字段的信息，如果没有获取到返回异常
     *
     * @param levelNum
     * @param sexType
     * @param appcode   应用类型码 1:他趣
     * @param fields
     * @return
     * @Title getInfoByLevelNum
     * <AUTHOR>
     * @Date 2015年10月9日 下午3:15:37
     */
    public Map<Object, Object> getInfoByLevelNum(Integer levelNum, Integer sexType, Integer appcode, String... fields) {
        appcode = ToolsService.normalizedAppcode(appcode);
        if (fields != null) {
            int len = fields.length;
            //需要将level_desc转换为desc
            for (int i = 0; i < len; i++) {
                String o = fields[i];
                if ("level_desc".equals(o)) {
                    fields[i] = "desc";
                }
            }
        }

        Map<String, String> levelInfo = this.getInfoSmart(levelNum.longValue(), false, false, sexType, appcode, fields);
        Map<Object, Object> levelNumInfo = new HashMap<>();
        levelNumInfo.putAll(levelInfo);
        if (levelNumInfo.containsKey("desc")) {
            levelNumInfo.put("level_desc", levelNumInfo.get("desc"));
            levelNumInfo.remove("desc");
        }
        return levelNumInfo;
    }

    /**
     * @param levelNum
     * @param appcode  应用类型码 1:他趣
     * @return
     */
    public long findLevelIdByLevelNum(int levelNum, Integer appcode) {
        long levelId = MapUtils.getLong(this.getInfoByLevelNum(levelNum, null, appcode, "id"), "id", 0L);
        return levelId;
    }

    public int findLevelNumByLevelId(long levelId) {
        return MapUtils.getInteger(this.getInfo(levelId, null, "level_num"), "level_num", 0);
    }

//    @Transactional
    private Map<String, String> getInfoSmart(Long level, boolean isLevelId, boolean masterDb, Integer sexType, Integer appcode, String... fields) {
        appcode = ToolsService.normalizedAppcode(appcode);
        //2016.10.19 修改
        if (level == null) {
            logger.error("AccountsLevelService.getInfoSmart传入的level为null, isLevelId:{}", isLevelId);
            throw new ServiceException(CodeStatus.LEVEL_INFO_NO_FOUND);
        }

        String key = null;
        if (isLevelId) {//如果是levelId
            level = Math.max(level, 1L);//小于1的当1处理
            key = RedisKeyConstant.LEVEL_INFO.setArg(level);
        } else {//如果是levelNum
            level = Math.max(level, 0L);//小于0的当0处理
            key = RedisKeyConstant.LEVEL_NUM_INFO.setArg(appcode, level);
        }

        Map<Object, Object> redisLevel = levelStringRedisTemplate.opsForHash().entries(key);
        Map<String, String> resultMap = new HashMap<>();
        if (redisLevel == null || redisLevel.isEmpty()) {
            AccountsLevel accountsLevel;
            if (isLevelId) {
                if (masterDb) {
                    accountsLevel = accountsLevelDao.getOneFromMasterDbById(level);
                } else {
                    accountsLevel = accountsLevelDao.getOneById(level);
                }
            } else {
                if (masterDb) {
                    accountsLevel = accountsLevelDao.getOneFromMasterDbByLevelNum(level.intValue(), appcode);
                } else {
                    accountsLevel = accountsLevelDao.getOneByLevelNum(level.intValue(), appcode);
                }
            }
            if (accountsLevel == null) {
                return resultMap;
            }

            redisLevel = new HashMap<>();
            redisLevel.put("id", accountsLevel.getId().toString());
            redisLevel.put("honor_unsex", accountsLevel.getHonor_unsex() == null ? "" : accountsLevel.getHonor_unsex().trim());
            redisLevel.put("honor_man", accountsLevel.getHonor_man() == null ? "" : accountsLevel.getHonor_man().trim());
            redisLevel.put("honor_women", accountsLevel.getHonor_women() == null ? "" : accountsLevel.getHonor_women().trim());
            redisLevel.put("level_num", accountsLevel.getLevel_num().toString());
            redisLevel.put("desc", accountsLevel.getDesc() == null ? "" : accountsLevel.getDesc().trim());
            redisLevel.put("min_level_score", accountsLevel.getMin_level_score().toString());
            redisLevel.put("max_level_score", accountsLevel.getMax_level_score().toString());
            redisLevel.put("privilege_id", accountsLevel.getPrivilege_id().toString());
            redisLevel.put("privilege_desc", StringUtils.trimToEmpty(accountsLevel.getPrivilege_desc()));
            this.updateCache(accountsLevel);
        }

        if (redisLevel == null || redisLevel.isEmpty()) {
            throw new ServiceException(CodeStatus.LEVEL_INFO_NO_FOUND);
        }

        // 设置返回结果
        if (fields == null || fields.length == 0) {
            for (Map.Entry<Object, Object> entry : redisLevel.entrySet()) {
                resultMap.put(entry.getKey().toString(), entry.getValue().toString());
            }
        } else {
            for (String f : fields) {
                if ("honor_name".equals(f)) {
                    if (sexType != null && sexType == 1) {
                        resultMap.put(f, MapUtils.getString(redisLevel, "honor_man", ""));
                        continue;
                    }
                    if (sexType != null && sexType == 2) {
                        resultMap.put(f, MapUtils.getString(redisLevel, "honor_women", ""));
                        continue;
                    }
                    resultMap.put(f, MapUtils.getString(redisLevel, "honor_unsex", ""));
                    continue;
                }

                resultMap.put(f, MapUtils.getString(redisLevel, f, ""));
            }
        }
        return resultMap;
    }

    /**
     * 获取所有等级信息
     *
     * @param appcode 应用类型码 1:他趣
     */
    private List<AccountsLevel> findAll(Integer appcode) {
        appcode = ToolsService.normalizedAppcode(appcode);
        List<AccountsLevel> levelList;
        // 先从缓存中读取
        String levelString = levelStringRedisTemplate.opsForValue().get(RedisKeyConstant.ACCOUNT_LEVEL_SYN_ALL.setArg(appcode));
        // 缓存里没有查询数据库并设置到缓存
        if (levelString == null || levelString.trim().length() == 0) {
            Collection<String> fields = Arrays.asList("id", "level_num", "min_level_score", "max_level_score", "honor_man", "honor_women", "honor_unsex", "`desc`", "privilege_id", "create_time", "update_time", "privilege_desc", "appcode");
            levelList = accountsLevelDao.query(Sql.build(AccountsLevel.class, fields).eq("appcode", appcode, false));
            levelString = JsonUtils.objectToString(levelList);
            //2016.10.13 设置过期时间，30秒
            levelStringRedisTemplate.opsForValue().set(RedisKeyConstant.ACCOUNT_LEVEL_SYN_ALL.setArg(appcode), levelString, 30, TimeUnit.SECONDS);
        } else {
            levelList = JsonUtils.stringToObject(levelString, new TypeReference<List<AccountsLevel>>() {});
        }
        return levelList;
    }

}
