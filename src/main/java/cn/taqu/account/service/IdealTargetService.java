package cn.taqu.account.service;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.TimedCache;
import cn.taqu.account.common.*;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsIdealSampleDao;
import cn.taqu.account.dao.AccountsIdealTargetDao;
import cn.taqu.account.dao.AccountsIdealTargetLogDao;
import cn.taqu.account.dao.TransactionWrapper;
import cn.taqu.account.dto.AccountIdealTargetDto;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.dto.ShumeiContentCheckDTO;
import cn.taqu.account.model.AccountsIdealTarget;
import cn.taqu.account.model.AccountsIdealTargetLog;
import cn.taqu.account.model.AccountsMajorLog;
import cn.taqu.account.vo.AccountIdealTargetVo;
import cn.taqu.account.vo.IdealSampleVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.taqu.account.constant.CodeStatus.AUDIT_ONGOING;

/**
 * 我的理想型
 * <AUTHOR>
 * @date 2024/11/27 下午5:54
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IdealTargetService {

    @Getter
    private final AccountsIdealTargetLogDao logDao;

    private final AccountsIdealTargetDao targetDao;

    private final AccountsIdealSampleDao sampleDao;

    private final StringRedisTemplate accountBizStringRedisTemplate;

    private final Cache<Integer, List<IdealSampleVo>> sampleCache = new TimedCache<>(180_000);

    @Resource
    private BuryService buryService;

    public void save(String uuid, String content, String smid) {
        String lockKey = "account:ideal:target:lock:" + uuid;
        Boolean test = accountBizStringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1");
        if (BooleanUtils.isFalse(test)) {
            throw new ServiceException(AUDIT_ONGOING);
        }
        accountBizStringRedisTemplate.expire(lockKey, 3, TimeUnit.SECONDS);
        try {
            AccountsIdealTarget target = Optional.ofNullable(targetDao.getByUuid(uuid)).orElseGet(() -> {
                AccountsIdealTarget t = new AccountsIdealTarget();
                t.setAccountUuid(uuid);
                return t;
            });
            if (content.equals(target.getIdealTarget())) {
                // 内容相同 不做处理
                return;
            }

            Integer count = logDao.count(uuid, CommonAuditStatus.AUDITING.getStatus());
            if (count != null && count > 0) {
                throw new ServiceException(AUDIT_ONGOING);
            }
            AccountsIdealTargetLog targetLog = new AccountsIdealTargetLog();
            targetLog.setAccountUuid(uuid);
            targetLog.setIdealTarget(content);
            targetLog.setStatus(CommonAuditStatus.AUDITING.getStatus());
            long now = DateUtil.currentTimeSeconds();
            targetLog.setCreateTime(now);
            targetLog.setUpdateTime(now);
            targetLog.setAuditTime(now);
            TransactionWrapper.me().wrap(() -> {
                val po = logDao.merge(targetLog);
                String bizId = po.getId().toString();
                // 风控
                SoaBaseParams baseParams = SoaBaseParams.fromThread();
                ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(uuid, content, Collections.emptyList(), smid, bizId, RiskDetectEnum.TEXT_IDEAL.name(), baseParams.getAppcode(), baseParams.getCloned());
                boolean riskPass = RiskDetectRiskLevelEnum.PASS.name().equals(contentCheckDTO.getSuggestion()) || RiskDetectRiskLevelEnum.WAIT.name().equals(contentCheckDTO.getSuggestion());
                if (!riskPass) {
                    throw new ServiceException(CodeStatus.CONTENT_RISK_FAILED.value(), contentCheckDTO.getToast());
                }
                if (target.getIdealTarget() == null) {
                    target.setIdealTarget("");
                }
                target.setIdealTargetOrigin(content);
                targetDao.upsert(target);
            });
            // 刷新缓存
            refreshCache(target, CommonAuditStatus.AUDITING.getStatus());
        } finally {
            accountBizStringRedisTemplate.delete(lockKey);
        }
    }

    public AccountIdealTargetVo info(String uuid, boolean visitor) {
        String key = RedisKeyConstant.ACCOUNT_IDEAL.setArg(uuid);
        String json = accountBizStringRedisTemplate.opsForValue().get(key);
        AccountIdealTargetDto target;
        AccountIdealTargetVo result = new AccountIdealTargetVo();
        if (StringUtils.isNotBlank(json)) {
            target = JsonUtils.stringToObject(json, AccountIdealTargetDto.class);
            // 缓存续期
            accountBizStringRedisTemplate.expire(key, 1, TimeUnit.HOURS);
        } else {
            AccountsIdealTarget po = targetDao.getByUuid(uuid);
            if (po == null) {
                result.setIdealTarget("");
                result.setIdealTargetStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
                return result;
            }
            target = JsonUtils.mapper().convertValue(po, AccountIdealTargetDto.class);
            target.setStatus(StringUtils.equals(po.getIdealTarget(), po.getIdealTargetOrigin()) ? CommonAuditStatus.AUDIT_SUCCESS.getStatus() : CommonAuditStatus.AUDITING.getStatus());
            accountBizStringRedisTemplate.opsForValue().set(key, JsonUtils.objectToString(target), 1, TimeUnit.HOURS);
        }
        if (visitor) {
            result.setIdealTarget(Optional.ofNullable(target.getIdealTarget()).orElse(""));
            result.setIdealTargetStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        } else {
            result.setIdealTarget(Optional.ofNullable(target.getIdealTargetOrigin()).orElse(""));
            result.setIdealTargetStatus(target.getStatus());
        }
        return result;
    }

    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]专业审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        if (Objects.nonNull(riskSafeHitTypeEnum)) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    auditPass(dto);
                    break;
                case BLOCK:
                    auditReject(dto);
                    break;
                default:
                    break;
            }
        }
    }

    public List<IdealSampleVo> samples(Integer sexType) {
        return sampleCache.get(sexType, false, () -> sampleDao.listBySex(sexType).stream().map(s -> {
            IdealSampleVo sample = new IdealSampleVo();
            sample.setSampleId(s.getId());
            sample.setIdealTarget(s.getSample());
            return sample;
        }).collect(Collectors.toList()));
    }

    /**
     * 审核通过
     * @param resp
     */
    private void auditPass(RiskSafeCheckResponseDTO resp) {
        Long id = Long.parseLong(resp.getBizId());
        AccountsIdealTargetLog targetLog = logDao.getById(id);
        if (targetLog == null || !CommonAuditStatus.AUDITING.getStatus().equals(targetLog.getStatus())) {
            return;
        }
        targetLog.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        targetLog.setAuditTime(DateUtil.currentTimeSeconds());
        targetLog.setRiskRespData(JsonUtils.objectToString(resp));
        AccountsIdealTarget idealTarget = TransactionWrapper.me().wrap(() -> {
            logDao.merge(targetLog);
            String accountUuid = targetLog.getAccountUuid();
            AccountsIdealTarget target = targetDao.getByUuid(accountUuid);
            // 客态更新
            target.setIdealTarget(targetLog.getIdealTarget());
            targetDao.upsert(target);
            return target;
        });
        // 缓存刷新
        refreshCache(idealTarget, CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        // 推社区
        buryService.toBbsFinishIncentiveTask(targetLog.getAccountUuid(), FinishIncentiveTaskEnum.MY_LOVE_TA.getType());
    }

    /**
     * 审核不通过
     * @param resp
     */
    private void auditReject(RiskSafeCheckResponseDTO resp) {
        Long id = Long.parseLong(resp.getBizId());
        AccountsIdealTargetLog targetLog = logDao.getById(id);
        if (targetLog == null) {
            log.warn("【理想型】审核日志不存在 {}", id);
            return;
        }
        if (CommonAuditStatus.AUDIT_FAIL.getStatus().equals(targetLog.getStatus())) {
            log.warn("【理想型】已拒绝数据，跳过 {} {}", id, targetLog.getStatus());
            return;
        }
        String accountUuid = targetLog.getAccountUuid();
        AccountsIdealTarget target = targetDao.getByUuid(accountUuid);
        if (target == null) {
            log.warn("【理想型】不存在 {}", accountUuid);
            return;
        }
        if (!target.getIdealTargetOrigin().equals(targetLog.getIdealTarget())) {
            if (target.getIdealTarget().equals(targetLog.getIdealTarget())) {
                target.setIdealTarget("");
                targetDao.upsert(target);
                refreshCache(target, CommonAuditStatus.AUDITING.getStatus());
            }
            return;
        }
        if (CommonAuditStatus.AUDIT_SUCCESS.getStatus().equals(targetLog.getStatus())) {
            target.setIdealTargetOrigin("");
            target.setIdealTarget("");
        } else {
            // 保留上一次审核通过的结果 即当前客态结果
            target.setIdealTargetOrigin(targetLog.getIdealTarget());
        }
        targetLog.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
        targetLog.setAuditTime(DateUtil.currentTimeSeconds());
        targetLog.setRiskRespData(JsonUtils.objectToString(resp));
        TransactionWrapper.me().wrap(() -> {
            logDao.merge(targetLog);
            // 保留上一次审核通过的结果 即当前客态结果
            target.setIdealTargetOrigin(target.getIdealTarget());
            targetDao.upsert(target);
        });
        // 缓存刷新
        refreshCache(target, CommonAuditStatus.AUDIT_SUCCESS.getStatus());
    }

    private void refreshCache(AccountsIdealTarget po, Integer status) {
        AccountIdealTargetDto cache = JsonUtils.mapper().convertValue(po, AccountIdealTargetDto.class);
        cache.setStatus(status);
        String key = RedisKeyConstant.ACCOUNT_IDEAL.setArg(po.getAccountUuid());
        accountBizStringRedisTemplate.opsForValue().set(key, JsonUtils.objectToString(cache), 1, TimeUnit.HOURS);
    }
}
