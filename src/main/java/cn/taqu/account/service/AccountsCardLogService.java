package cn.taqu.account.service;

import cn.taqu.account.dao.AccountsCardLogDao;
import cn.taqu.account.model.AccountsCardLog;
import cn.taqu.account.search.AccountsCardLogSearch;
import cn.taqu.core.orm.PageData;
import cn.taqu.core.orm.Sql;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.PageUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class AccountsCardLogService {
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsCardLogDao accountsCardLogDao;

    @Transactional
    public AccountsCardLog addLog(String accountUuid, Long cardId, String level, Long createTime, Long endTime, int type, String operateName, String remark) {
        Long validDay = 0L;
        if(createTime != null && endTime != null && createTime>0 && endTime>0) {
            validDay = DateUtil.chaDay(DateUtil.fromSecond(createTime), DateUtil.fromSecond(endTime)) + 1;
        }

        AccountsCardLog log = new AccountsCardLog();
        log.setAccountUuid(accountUuid);
        log.setCardId(cardId);
        log.setLevel(level);
        log.setValidDay(validDay);
        log.setType(type);
        log.setOperateName(operateName);
        log.setRemark(remark);
        log.setCreateTime(DateUtil.currentTimeSeconds());
        accountsCardLogDao.merge(log);
        return log;
    }

    /**
     * 获取靓号历史使用用户
     * @param cardId
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<Map<String, Object>> getCardUsedHistory(Long cardId, Integer pageNumber, Integer pageSize) {
        AccountsCardLogSearch search = new AccountsCardLogSearch();
        search.setCard_id(cardId);
        search.setFields(Arrays.asList("account_uuid", "create_time"));
        PageData<AccountsCardLog> pageData = this.pageQuery(search, pageNumber, pageSize);
        List<AccountsCardLog> logList = pageData.getData();

        int size = logList == null ? 0 : logList.size();
        String[] uuidArray = new String[size];
        for(int i=0; i<size; i++) {
            uuidArray[i] = logList.get(i).getAccountUuid();
        }
        Map<String, Map<String, Object>> accountInfoMap = accountsInfoService.getInfoByUuid(uuidArray, new String[]{"account_name"}, "1", false, false);

        List<Map<String, Object>> mapList = new ArrayList<>(size);
        for(int i=0; i<size; i++) {
            AccountsCardLog log = logList.get(i);
            String uuid = log.getAccountUuid();

            Map<String, Object> map = new HashMap<>(3);
            map.put("account_uuid", uuid);
            map.put("create_time", log.getCreateTime());
            map.put("account_name", MapUtils.getString(accountInfoMap.get(uuid), "account_name", ""));
            mapList.add(map);
        }

        return new PageImpl<>(mapList, PageUtil.newPageable(pageNumber, pageSize), pageData.getTotal());
    }

    public List<Map<String, String>> giveRecvLog(String uuid, int page, int limit) {
        if(StringUtils.isBlank(uuid)) {
            return Collections.EMPTY_LIST;
        }

        List<AccountsCardLog> accountsCardLogList = this.accountsCardLogDao.getGifeLogByUuid(uuid, page, limit);
        int size = accountsCardLogList == null ? 0 : accountsCardLogList.size();
        List<Map<String, String>> result = new ArrayList<>(size);
        Set<String> operUuidSet = new HashSet<>();
        for(int i=0; i<size; i++) {
            AccountsCardLog accountsCardLog = accountsCardLogList.get(i);
            operUuidSet.add(accountsCardLog.getOperateName());
        }
        Map<String, Map<String, Object>> accountInfoMap = accountsInfoService.getInfoByUuid(operUuidSet.toArray(new String[]{}), new String[]{"account_name"}, "1", false, false);

        for(int i=0; i<size; i++) {
            AccountsCardLog accountsCardLog = accountsCardLogList.get(i);
            String level = accountsCardLog.getLevel();
            Integer type = accountsCardLog.getType();
            String accountName = MapUtils.getString(accountInfoMap.get(accountsCardLog.getOperateName()), "account_name", "");
            Long validDay = accountsCardLog.getValidDay();

            Map<String, String> map = new HashMap<>();
            map.put("create_time", String.valueOf(accountsCardLog.getCreateTime()));
            map.put("card_id", String.valueOf(accountsCardLog.getCardId()));
            String text;
            if(Objects.equals(AccountsCardLog.Type.GIVEN, type)) {
                text = String.format("赠送%s级靓号 %s 给 %s (%d天)", level, "%s", accountName, validDay);
            } else {
                text = String.format("收到 %s 赠送的%s级靓号 %s (%d天)", accountName, level, "%s", validDay);
            }
            map.put("text", text);

            result.add(map);
        }
        return result;
    }

    /**
     * 查询靓号历史操作日志
     * @param search
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<AccountsCardLog> getAccountCardLog(AccountsCardLogSearch search, Integer pageNumber, Integer pageSize) {
        search = search == null ? new AccountsCardLogSearch() : search;
        search.setFields(Arrays.asList("card_id", "account_uuid", "type", "create_time", "operate_name", "remark", "level"));
        PageData<AccountsCardLog> page = this.pageQuery(search, pageNumber, pageSize);
        return new PageImpl<>(page.getData(), PageUtil.newPageable(pageNumber, pageSize), page.getTotal());
    }

    private PageData<AccountsCardLog> pageQuery(AccountsCardLogSearch search, Integer pageNumber, Integer pageSize) {
        Sql sql = Sql.build(AccountsCardLog.class, search.getFields())
                .eq("card_id", search.getCard_id())
                .eq("account_uuid", search.getAccount_uuid())
                .ge("create_time", search.getBegin_time())
                .lt("create_time", search.getEnd_time());
        sql = sql.orderBy("create_time desc, id desc");
        PageData<AccountsCardLog> page = this.accountsCardLogDao.queryForPage(sql, pageNumber, pageSize);
        return page;
    }
}
