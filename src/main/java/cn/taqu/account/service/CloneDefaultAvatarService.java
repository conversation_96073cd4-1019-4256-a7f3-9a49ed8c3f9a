package cn.taqu.account.service;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import cn.taqu.core.protocol.SoaBaseParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Service
public class CloneDefaultAvatarService {

    @Autowired
    private AvatarHandleService avatarHandleService;

    public static Map<Integer, Map<Integer, String>> avatarMap = new HashMap<>();

    public static void initAvatar(String raw) {
        log.info("clone default avatar: {}", raw);
        avatarMap = JSONUtil.toBean(raw, new TypeReference<Map<Integer, Map<Integer, String>>>() {
        }, true);
        System.out.println();
    }

    public static boolean needReplaceDefaultAvatar() {
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        return needReplaceDefaultAvatar(cloned);
    }

    public static boolean needReplaceDefaultAvatar(Integer cloned) {
        return avatarMap.containsKey(cloned);
    }

    public static String getDefaultAvatar(Integer sexType) {
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        return getDefaultAvatar(cloned, sexType);
    }

    public boolean isDefaultAvatar(String avatar) {
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        StringUtils.removeStart(avatarHandleService.getAvatarByVersion(avatar, "1"), "/");
        return isDefaultAvatar(cloned, avatar);
    }

    public static String getDefaultAvatar(Integer cloned, Integer sexType) {
        Map<Integer, String> avatars = avatarMap.get(cloned);
        if (avatars != null) {
            return avatars.get(sexType);
        }
        return null;
    }

    private static boolean isDefaultAvatar(Integer cloned, String avatar) {
        Map<Integer, String> avatars = avatarMap.get(cloned);
        if (avatars != null) {
            for (String defUrl : avatars.values()) {
                if (avatar.equals(defUrl) || avatar.contains(defUrl)) {
                    return true;
                }
            }
        }
        return false;
    }
}
