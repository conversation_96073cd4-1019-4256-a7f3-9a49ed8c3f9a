package cn.taqu.account.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.taqu.account.common.*;
import cn.taqu.core.utils.SpringContextHolder;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsCertificationDao;
import cn.taqu.account.model.AccountsCertification;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-14 14:45
 */
@Slf4j
@Service
@Transactional
public class AccountsCertService {

    @Autowired
    private AccountsCertificationService accountsCertificationService;
    @Autowired
    private AccountsCertificationDao accountsCertificationDao;
    @Autowired
    private AliyunLiveFaceDetectService aliyunLiveFaceDetectService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    /**
     * 解除账号认证
     * @param accountUuid
     * @param cancelCertTypes
     */
    public void cancelCert(String accountUuid, List<Integer> cancelCertTypes, String operator){
        if(CollectionUtils.isEmpty(cancelCertTypes)){
            return;
        }
        if(cancelCertTypes.contains(CertTypeEnum.REAL_NAME.getValue())){
            AccountsCertification certification = accountsCertificationDao.getByAccountUuid(accountUuid);
            if(certification != null && Objects.equals(certification.getIsChecked(), CommConst.YES_1)){
                accountsCertificationService.cancelCertification(accountUuid, false, operator);
            }
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), UuidInfoField.ZHIMA_CERTIFICATION, "0");
            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), UuidInfoField.IDENTITY_NO_BIRTH, "");
            // 2024.03.22 不处理，后续直接删缓存
//            accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), UuidInfoField.GENDER_CERTIFICATION, "0");
            MonitorService.incCounterMetrices(PrometheusMetricsEnum.CANCEL_CERT_REAL_NAME_COUNTER);
        }
        if(cancelCertTypes.contains(CertTypeEnum.REAL_PERSON.getValue())){
            aliyunLiveFaceDetectService.cancelRealPersonCertification(accountUuid, CommConst.NO_0, operator);
            MonitorService.incCounterMetrices(PrometheusMetricsEnum.CANCEL_CERT_REAL_PERSON_COUNTER);
        }
        if(cancelCertTypes.contains(CertTypeEnum.REWARD_ACCOUNT.getValue())){
            Boolean cancelSuccess = accountsCertificationService.cancelRewardAccount(accountUuid, CommConst.NO_0, operator);
            if(cancelSuccess){
                MonitorService.incCounterMetrices(PrometheusMetricsEnum.CANCEL_CERT_REWARD_ACCOUNT_COUNTER);
            }
        }
        if(cancelCertTypes.contains(CertTypeEnum.CHAT_REAL_NAME.getValue())){
            accountsCertificationService.cancelCertificationForChat(accountUuid, false, operator);
        }
        SpringContextHolder.getBean(AllureService.class).refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
    }

    /**
     * 认证前置条件
     * @param accountUuid
     * @param certType
     * @return
     */
    public Map<String, String> needToDoBeforeCert(String accountUuid, Integer certType){
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"real_person_certification"}, "1", false, false).get(accountUuid);
        Boolean hasRealPerson = Objects.equals(MapUtils.getString(infoMap, "real_person_certification", CommonAuditStatus.AUDIT_FAIL.getStatus().toString()), CommonAuditStatus.AUDIT_SUCCESS.getStatus().toString());

        Map<String, String> result = new HashMap<>();
        // 该接口查询的默认值 0
        result.put("need_to_do_real_person_certification", "0");
        // 实名
        if(Objects.equals(certType, CertTypeEnum.REAL_NAME.getValue())) {
            // 默认值
            result.put("need_to_do_real_person_certification", "1");
            // 已真人认证
            if (hasRealPerson) {
                result.put("need_to_do_real_person_certification", "0");
            } else {
                // 未真人认证 且白名单用户不需要真人
//                Boolean exist = accountsCertWhiteListService.isUuidInCertWhiteList(accountUuid);
//                if (exist) {
//                    result.put("need_to_do_real_person_certification", "0");
//                }else{
                    result.put("need_to_do_real_person_certification", "1");
//                }
            }
        }
        return result;
    }
}
