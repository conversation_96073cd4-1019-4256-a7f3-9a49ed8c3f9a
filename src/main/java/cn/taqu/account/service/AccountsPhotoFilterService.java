package cn.taqu.account.service;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 头像特殊处理过滤
 *
 * <AUTHOR>
 * @date 2020/01/20 14:28
 */
@Service
@Transactional
public class AccountsPhotoFilterService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsPhotoFilterService.class);

    // 2020-01-20 20:00:00
//    private static Long CHECK_TIME = 1579521600L;

    public static int accountsPhotoFilterNum = 1;

	/**
	 * 系统图片
	 */
	private static List<String> DEFAULT_URL_LIST = Lists.newArrayList(
			"avatar/new_avatar_asexual.png", "avatar/newavatarmale.jpg", "avatar/newavatarfemale.jpg", "avatar/36536f87ea91a7d6b1986a335aee463c.jpg", "avatar/528f351eb9e8cd2d9fd23972abeb69bb.jpg",
			"avatar/default/1.jpg", "avatar/default/2.jpg", "avatar/default/3.jpg", "avatar/default/4.jpg", "avatar/default/5.jpg",
			"avatar/default/6.jpg", "avatar/default/7.jpg", "avatar/default/8.jpg", "avatar/default/9.jpg", "avatar/default/10.jpg",
			"avatar/default/11.jpg", "avatar/default/12.jpg", "avatar/default/13.jpg", "avatar/default/14.jpg", "avatar/default/15.jpg",
			"avatar/default/16.jpg", "avatar/illegal_avatar.png",
			"/avatar/new_avatar_asexual.png","/avatar/newavatarmale.jpg", "/avatar/newavatarfemale.jpg", "/avatar/36536f87ea91a7d6b1986a335aee463c.jpg", "/avatar/528f351eb9e8cd2d9fd23972abeb69bb.jpg",
			"/avatar/default/1.jpg", "/avatar/default/2.jpg", "/avatar/default/3.jpg", "/avatar/default/4.jpg", "/avatar/default/5.jpg",
			"/avatar/default/6.jpg", "/avatar/default/7.jpg", "/avatar/default/8.jpg", "/avatar/default/9.jpg", "/avatar/default/10.jpg",
			"/avatar/default/11.jpg", "/avatar/default/12.jpg", "/avatar/default/13.jpg", "/avatar/default/14.jpg", "/avatar/default/15.jpg",
			"/avatar/default/16.jpg", "/avatar/illegal_avatar.png",
			"//avatar/new_avatar_asexual.png","//avatar/newavatarmale.jpg", "//avatar/newavatarfemale.jpg", "//avatar/36536f87ea91a7d6b1986a335aee463c.jpg", "//avatar/528f351eb9e8cd2d9fd23972abeb69bb.jpg",
			"//avatar/default/1.jpg", "//avatar/default/2.jpg", "//avatar/default/3.jpg", "//avatar/default/4.jpg", "//avatar/default/5.jpg",
			"//avatar/default/6.jpg", "//avatar/default/7.jpg", "//avatar/default/8.jpg", "//avatar/default/9.jpg", "//avatar/default/10.jpg",
			"//avatar/default/11.jpg", "//avatar/default/12.jpg", "//avatar/default/13.jpg", "//avatar/default/14.jpg", "//avatar/default/15.jpg",
			"//avatar/default/16.jpg", "//avatar/illegal_avatar.png"
	);


	/**
	 * 是否默认图片
	 *
	 * <AUTHOR>
	 * @date 2020/07/01 15:28
	 * @param avatar
	 * @return
	 */
	public static boolean isSystemAvatar(String avatar) {
		boolean flag = false;
		String removeAvatar = StringUtils.remove(avatar, AccountsService.regReplaceAvatarPart);
		if(DEFAULT_URL_LIST.contains(removeAvatar)) {
			flag = true;
		}
		if(!flag && AvatarHandleService.isDefAvatar(removeAvatar)) {
		    flag = true;
		}
		
		return flag;
	}

}
