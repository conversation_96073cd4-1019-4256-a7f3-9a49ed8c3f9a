package cn.taqu.account.service;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountSnListDao;
import cn.taqu.account.model.AccountSnList;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.log.Log;
import cn.taqu.core.utils.DateUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class AccountSnListService {
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountSnListDao accountSnListDao;
    @Autowired
    private StringRedisTemplate snStringRedisTemplate;

    @Transactional
    public Map<String, Map<String, String>> getAccountSnByUuid(String fromUuid, String toUuid) {
        Map<String, String> ticketMap = new HashMap<>();
        Map<String, Object> ticketAccountInfo = accountsInfoService.getInfoByUuid(new String[]{fromUuid}, new String[]{UuidInfoField.IS_CHECK_MOBILE}, "1", false, false).get(fromUuid);
        ticketMap.put("account_uuid", fromUuid);
        ticketMap.put("account_sn", this.getOrCreateSn(fromUuid).getAccountSn());
        ticketMap.put("is_check_mobile", MapUtils.getString(ticketAccountInfo, UuidInfoField.IS_CHECK_MOBILE, "0"));

        Map<String, String> uuidMap = new HashMap<>();
        Map<String, Object> uuidAccountInfo = accountsInfoService.getInfoByUuid(new String[]{toUuid}, new String[]{UuidInfoField.IS_CHECK_MOBILE}, "1", false, false).get(toUuid);
        uuidMap.put("account_uuid", toUuid);
        uuidMap.put("account_sn", this.getOrCreateSn(toUuid).getAccountSn());
        uuidMap.put("is_check_mobile", MapUtils.getString(uuidAccountInfo, UuidInfoField.IS_CHECK_MOBILE, "0"));

        Map<String, Map<String, String>> result = new HashMap<>();
        result.put(fromUuid, ticketMap);
        result.put(toUuid, uuidMap);
        return result;
    }

    /**
     * 根据uuid获取accountSn，key为用户uuid, value为accountSn
     * @param accountUuids
     * @return
     */
    @Transactional
    public Map<String, String> getByUuids(String[] accountUuids) {
        Map<String, String> result = new LinkedHashMap<>();
        for(String accountUuid : accountUuids) {
            if(StringUtils.isBlank(accountUuid)) {
                continue;
            }
            result.put(accountUuid, this.getOrCreateSn(accountUuid).getAccountSn());
        }
        return result;
    }

    @Transactional
    public AccountSnList create(String accountUuid) {
        String accountSn = this.generateSn();
        if(accountSn == null) {
            return null;
        }

        AccountSnList accountSnList = new AccountSnList();
        accountSnList.setAccountUuid(accountUuid);
        accountSnList.setAccountSn(accountSn);
        accountSnList = accountSnListDao.merge(accountSnList);
        this.toCache(accountSnList);
        return accountSnList;
    }

    /**
     * 根据account_sn批量获取用户信息
     * @param accountSnArray
     * @return
     */
    @Transactional
    public Map<String, Map<String, String>> getAccountInfoBySn(String[] accountSnArray) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        List<AccountSnList> accountSnListList =  accountSnListDao.getBySnList(Arrays.asList(accountSnArray));
        for(AccountSnList accountSnList : accountSnListList) {
            Map<String, String> accountInfo = new HashMap<>();
            accountInfo.put("account_uuid", StringUtils.trimToEmpty(accountSnList.getAccountUuid()));
            result.put(StringUtils.trimToEmpty(accountSnList.getAccountSn()), accountInfo);
        }
        return result;
    }

    @Transactional
    public AccountSnList getOrCreateSn(String accountUuid) {
        List<Object> hashKeys = new ArrayList<>();
        hashKeys.add("account_sn");
        hashKeys.add("id");
        List<Object> values = snStringRedisTemplate.opsForHash().multiGet(RedisKeyConstant.ACCOUNT_SN_INFO.setArg(accountUuid), hashKeys);
        if(values != null && values.size() == hashKeys.size()) {
            String accountSn = (String)values.get(0);
            String id = (String)values.get(1);

            if(StringUtils.isNotBlank(accountSn) && StringUtils.isNotBlank(id)) {
                AccountSnList accountSnList = new AccountSnList();
                accountSnList.setAccountSn(accountSn);
                accountSnList.setId(Long.valueOf(id));
                accountSnList.setAccountUuid(accountUuid);
                return accountSnList;
            }
        }

        AccountSnList accountSnList = accountSnListDao.findByAccountUuid(accountUuid);
        if(accountSnList != null) {
            this.toCache(accountSnList);
            return accountSnList;
        }

        accountSnList = this.create(accountUuid);
        if(accountSnList == null) {
            throw new ServiceException(CodeStatus.CREATE_SN_FAIL);
        }

        return accountSnList;
    }

    private void toCache(AccountSnList accountSnList) {
        Map<String, String> uuidHashValue = new HashMap<>();
        uuidHashValue.put("account_sn", accountSnList.getAccountSn());
        uuidHashValue.put("id", accountSnList.getId().toString());
        String key = RedisKeyConstant.ACCOUNT_SN_INFO.setArg(accountSnList.getAccountUuid());
        snStringRedisTemplate.opsForHash().putAll(key, uuidHashValue);
        snStringRedisTemplate.expire(key, 7, TimeUnit.DAYS);
    }

    /**
     * 随机生成16位的不重复的sn
     * @return
     */
    private String generateSn() {
        Random random = new Random();
        char[] snChars = new char[16];

        // 当前日期
        Date endDate = Calendar.getInstance().getTime();
        Date startDate = DateUtil.string8ToDate("********");
        // 计算两个日期相差天数
        int intervalDays = DateUtil.getIntervalDays(startDate, endDate);
        // 第一个字符为相差天数除以9取余
        snChars[0] = (char)(intervalDays % 9 + 49);

        for (int i = 0; i < 9; i++) {
            for (int j = 0; j < 8; j++) {
                snChars[j + 1] = (char) (random.nextInt(10) + 48);
            }

            String accountSn = new String(snChars);
            String redisKey = RedisKeyConstant.SN_UNIQUE_USER.setArg(accountSn);
            String redisSn = snStringRedisTemplate.opsForValue().get(redisKey);

            if (redisSn == null) {
                snStringRedisTemplate.opsForValue().set(redisKey, "1");
                return accountSn.trim();
            } else {
                if (i < 8) {
                    Log.warn("第{}次生成account_sn重复,account_sn为{}", i + 1, accountSn);
                    continue;
                }
                Log.error("连续{}次生成account_sn重复,account_sn生成失败", i + 1);
                break;
            }
        }
        return null;
    }
}
