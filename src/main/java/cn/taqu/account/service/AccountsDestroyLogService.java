package cn.taqu.account.service;

import cn.taqu.account.dao.AccountsDestroyLogDao;
import cn.taqu.account.etcd.TqUserGradeClient;
import cn.taqu.account.manager.AccountsDestroyLogManager;
import cn.taqu.account.manager.AccountsThirdPartyManager;
import cn.taqu.account.model.AccountsDestroyLog;
import cn.taqu.account.model.AccountsThirdParty;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.utils.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 用户注销记录表
 *
 * <AUTHOR>
 * @date 2020/12/01
 */
@Slf4j
@Service
@Transactional
public class AccountsDestroyLogService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsDestroyLog> {
    @Autowired
    private AccountsDestroyLogDao accountsDestroyLogDao;

    @Autowired
    private AccountsThirdPartyManager accountsThirdPartyManager;

    @Autowired
    private AccountsDestroyLogManager accountsDestroyLogManager;

    @Autowired
    private TqUserGradeClient userGradeClient;

    @Transactional(rollbackFor = Exception.class)
    public void addLog(String accountUuid, String accountName, String mobile, String reason,boolean isScript) {
        AccountsDestroyLog accountsDestroyLog = new AccountsDestroyLog();
        accountsDestroyLog.setAccount_uuid(accountUuid);
        accountsDestroyLog.setAccount_name(accountName);
        accountsDestroyLog.setMobile(mobile);
        accountsDestroyLog.setMobile_cipher(EncryptUtil.encrypt(mobile));
        accountsDestroyLog.setMobile_digest(EncryptUtil.sm3(mobile));
        accountsDestroyLog.setReason(reason);
        accountsDestroyLog.setCreate_time(DateUtil.currentTimeSeconds());
        accountsDestroyLog.setUpdate_time(0L);
        accountsDestroyLog.setStatus(1);
        this.invalidByUuid(accountUuid);

        if(!ToolsService.accountsDestroyLogSwitchWrite.isOn(true)) {
            accountsDestroyLog.setMobile("");
        }
        
        // 记录所有绑定状态
        List<AccountsThirdParty> thirdParts = accountsThirdPartyManager.findValidByAccountUuid(accountUuid);
        thirdParts.forEach(item -> {
            String type = item.getType();
            if ("WeChat".equals(type)) {
                accountsDestroyLog.setWechatId(item.getOpen_id());
            } else if ("QQ".equals(type)) {
                accountsDestroyLog.setQqId(item.getOpen_id());
            } else if ("Apple".equals(type)) {
                accountsDestroyLog.setAppleId(item.getOpen_id());
            } else if ("Visitor".equals(type)) {
                accountsDestroyLog.setVisitorId(item.getOpen_id());
            } else if ("WeiBo".equals(type)) {
                accountsDestroyLog.setWeiboId(item.getOpen_id());
            }
        });
        accountsDestroyLog.setNeedToLimitState(1);
        // 查出魅力值等级,判断是否免除再注册时间校验;
        // 脚本注销的也设置为免校验
        Integer charmScore = userGradeClient.getCharmLevelByUuid(accountUuid);
        accountsDestroyLog.setCharmRating(charmScore);

        if (charmScore >= ReRegisterLimitService.CHARM_RATING || isScript) {
            accountsDestroyLog.setNeedToLimitState(0);
        }
        accountsDestroyLogDao.merge(accountsDestroyLog);
    }

    @Transactional
    public void invalidByUuid(String accountUuid) {
        accountsDestroyLogDao.updateStatusByUuid(accountUuid, 1, 0);
    }

    public Long getLastValidCreateTime(String uuid) {
        return this.getLastValidCreateTime(Arrays.asList(uuid));
    }

    public Long getLastValidCreateTime(List<String> uuidList) {
        return accountsDestroyLogDao.getLastCreateTime(uuidList, 1);
    }

    /**
     * 判断用户是否有生效的注销请求
     *
     * @return
     */
    public boolean isAccountHadValidDestroy(String accountUuid) {
        Long lastDestroyTime = this.getLastValidCreateTime(accountUuid);
        boolean result = this.isTimeInValidDestroy(lastDestroyTime);
        if (BooleanUtils.isFalse(result)) {
            log.info("注销时间限制:{}, lastDestroyTime:{}", accountUuid, lastDestroyTime);
        }

        return result;
    }

    /**
     * 判断时间是否在生效在90天后的注销请求生效周期内
     *
     * @return
     */
    public boolean isTimeInValidDestroy(Long time) {
        return time != null && DateUtil.getTodayEndSecond() - time > 90 * 24 * 60 * 60;
    }

    public boolean isMobileDestroy(Integer cloned, String mobile) {
        Long createTime = accountsDestroyLogManager.getLastCreateTimeByMobile(cloned, Arrays.asList(mobile), 1);
        return createTime != null;
    }

    public Integer countByMobile(Integer cloned, String mobile) {
        return accountsDestroyLogManager.countByMobile(cloned, mobile);
    }

    /**
     * mobile是否存在
     *
     * @param mobile 用户手机号
     * @return true-存在；false-不存在；
     * <AUTHOR>
     * @date 2021/02/02 09:59
     */
    public Boolean isExistMobile(Integer cloned, String mobile) {
        String accountId = accountsDestroyLogManager.getUuidByMobile(cloned, mobile);
        if (StringUtils.isBlank(accountId)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 手机号是否注销过
     *
     * @param mobileArr
     * @return
     */
    public Map<String, Boolean> hasDestroyInMobile(Integer cloned,String[] mobileArr) {
        Map<String, Boolean> result = Maps.newHashMap();
        for (String mobile : mobileArr) {
            // 默认未注销过
            result.put(mobile, false);
        }

        // 查询是否有生效的注销记录
        List<String> list = accountsDestroyLogManager.listMobileHasDestroyLog(cloned, Lists.newArrayList(mobileArr), AccountsDestroyLog.status.VALID.getValue());
        for (String mobile : list) {
            result.put(mobile, true);
        }

        return result;
    }

}
