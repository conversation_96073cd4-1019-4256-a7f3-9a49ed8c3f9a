package cn.taqu.account.service;

import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.common.RiskDetectEnum;
import cn.taqu.account.common.RiskDetectRiskLevelEnum;
import cn.taqu.account.common.RiskSafeHitTypeEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsMajorDao;
import cn.taqu.account.dao.AccountsMajorLogDao;
import cn.taqu.account.dao.SchoolDao;
import cn.taqu.account.dao.TransactionWrapper;
import cn.taqu.account.dto.AccountMajorDto;
import cn.taqu.account.dto.AccountSchoolDto;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.dto.ShumeiContentCheckDTO;
import cn.taqu.account.model.AccountsMajor;
import cn.taqu.account.model.AccountsMajorLog;
import cn.taqu.account.model.School;
import cn.taqu.account.vo.SchoolListVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.taqu.account.constant.CodeStatus.AUDIT_ONGOING;
import static cn.taqu.account.constant.CodeStatus.INVALID_SCHOOL;

/**
 * <AUTHOR>
 * @date 2024/11/26 下午3:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SchoolService {

    private final SchoolDao schoolDao;

    @Getter
    private final AccountsMajorLogDao majorLogDao;

    private final AccountsMajorDao majorDao;

    private final StringRedisTemplate accountBizStringRedisTemplate;

    @Resource
    private AllureService allureService;

    public SchoolListVo guess(String school) {
        List<School> list = schoolDao.selectLike(school, 20);
        SchoolListVo result = new SchoolListVo();
        if (CollectionUtils.isNotEmpty(list)) {
            result.setList(list.stream().map(l -> {
                SchoolListVo.Row row = new SchoolListVo.Row();
                row.setSchoolId(l.getId());
                row.setSchoolName(l.getSchoolName());
                return row;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    public void submit(String uuid, Long schoolId, String major, String smid) {
        School school = schoolDao.getById(schoolId);
        if (school == null) {
            throw new ServiceException(INVALID_SCHOOL);
        }
        String lockKey = "account:school:submit:lock:" + uuid;
        Boolean test = accountBizStringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1");
        if (BooleanUtils.isFalse(test)) {
            throw new ServiceException(AUDIT_ONGOING);
        }
        accountBizStringRedisTemplate.expire(lockKey, 3, TimeUnit.SECONDS);
        try {
            Integer count = majorLogDao.count(uuid, CommonAuditStatus.AUDITING.getStatus());
            if (count != null && count > 0) {
                throw new ServiceException(AUDIT_ONGOING);
            }
            AccountsMajor am = Optional.ofNullable(majorDao.getByUuid(uuid)).orElseGet(() -> {
                AccountsMajor m = new AccountsMajor();
                m.setAccountUuid(uuid);
                return m;
            });
            if (StringUtils.isEmpty(major)) {
                am.setSchoolId(schoolId);
                am.setMajor("");
                am.setMajorOrigin("");
                majorDao.upsert(am);
                // 刷新缓存
                refreshCache(am, CommonAuditStatus.AUDIT_SUCCESS.getStatus());
            }
            if (major.equals(am.getMajor())) {
                // 仅更新学校
                am.setSchoolId(schoolId);
                majorDao.upsert(am);
                // 刷新缓存
                refreshCache(am, CommonAuditStatus.AUDIT_SUCCESS.getStatus());
                return;
            }
            // 专业发生变更 风控
            AccountsMajorLog majorLog = new AccountsMajorLog();
            majorLog.setAccountUuid(uuid);
            majorLog.setStatus(CommonAuditStatus.AUDITING.getStatus());
            majorLog.setMajor(major);
            long now = DateUtil.currentTimeSeconds();
            majorLog.setCreateTime(now);
            majorLog.setUpdateTime(now);
            majorLog.setAuditTime(now);
            TransactionWrapper.me().wrap(() -> {
                AccountsMajorLog po = majorLogDao.merge(majorLog);
                String bizId = po.getId().toString();
                // 风控
                SoaBaseParams baseParams = SoaBaseParams.fromThread();
                ShumeiContentCheckDTO contentCheckDTO = RiskSafeService.textReviewFromRiskDetect(uuid, major, Collections.emptyList(), smid, bizId, RiskDetectEnum.TEXT_MAJOR.name(), baseParams.getAppcode(), baseParams.getCloned());
                boolean riskPass = RiskDetectRiskLevelEnum.PASS.name().equals(contentCheckDTO.getSuggestion()) || RiskDetectRiskLevelEnum.WAIT.name().equals(contentCheckDTO.getSuggestion());
                if (!riskPass) {
                    throw new ServiceException(CodeStatus.CONTENT_RISK_FAILED.value(), contentCheckDTO.getToast());
                }
                am.setSchoolId(schoolId);
                am.setMajorOrigin(major);
                if (am.getMajor() == null) {
                    am.setMajor("");
                }
                majorDao.upsert(am);
            });
            // 缓存保存
            refreshCache(am, CommonAuditStatus.AUDITING.getStatus());
        } finally {
            accountBizStringRedisTemplate.delete(lockKey);
        }
    }

    public AccountSchoolDto info(String uuid, boolean visitor) {
        String key = RedisKeyConstant.ACCOUNT_MAJOR.setArg(uuid);
        String json = accountBizStringRedisTemplate.opsForValue().get(key);
        AccountMajorDto major;
        AccountSchoolDto result = new AccountSchoolDto();
        if (StringUtils.isNotBlank(json)) {
            major = JsonUtils.stringToObject(json, AccountMajorDto.class);
            // 缓存续期
            accountBizStringRedisTemplate.expire(key, 1, TimeUnit.HOURS);
        } else {
            AccountsMajor po = majorDao.getByUuid(uuid);
            if (po == null) {
                result.setMajor("");
                result.setSchoolName("");
                result.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
                return result;
            }
            major = JsonUtils.mapper().convertValue(po, AccountMajorDto.class);
            major.setStatus(StringUtils.equals(major.getMajor(), major.getMajorOrigin()) ? CommonAuditStatus.AUDIT_SUCCESS.getStatus() : CommonAuditStatus.AUDITING.getStatus());
            accountBizStringRedisTemplate.opsForValue().set(key, JsonUtils.objectToString(major), 1, TimeUnit.HOURS);
        }

        School school = schoolDao.getById(major.getSchoolId());
        result.setSchoolId(major.getSchoolId());
        result.setSchoolName(school.getSchoolName());
        if (visitor) {
            result.setMajor(major.getMajor());
            result.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        } else {
            result.setMajor(major.getMajorOrigin());
            result.setStatus(major.getStatus());
        }
        return result;
    }

    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]专业审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        if (Objects.nonNull(riskSafeHitTypeEnum)) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    auditPass(dto);
                    break;
                case BLOCK:
                    auditReject(dto);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 审核通过
     * @param resp
     */
    private void auditPass(RiskSafeCheckResponseDTO resp) {
        Long id = Long.parseLong(resp.getBizId());
        AccountsMajorLog majorLog = majorLogDao.getById(id);
        if (majorLog == null || !CommonAuditStatus.AUDITING.getStatus().equals(majorLog.getStatus())) {
            return;
        }
        majorLog.setStatus(CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        majorLog.setAuditTime(DateUtil.currentTimeSeconds());
        majorLog.setRiskRespData(JsonUtils.objectToString(resp));
        AccountsMajor am = TransactionWrapper.me().wrap(() -> {
            majorLogDao.merge(majorLog);
            String accountUuid = majorLog.getAccountUuid();
            AccountsMajor major = majorDao.getByUuid(accountUuid);
            // 客态更新
            major.setMajor(majorLog.getMajor());
            major.setAccountUuid(accountUuid);
            majorDao.upsert(major);
            return major;
        });
        // 缓存更新
        refreshCache(am, CommonAuditStatus.AUDIT_SUCCESS.getStatus());
        allureService.refreshAllure(majorLog.getAccountUuid());
    }

    /**
     * 审核不通过
     * @param resp
     */
    private void auditReject(RiskSafeCheckResponseDTO resp) {
        Long id = Long.parseLong(resp.getBizId());
        AccountsMajorLog majorLog = majorLogDao.getById(id);
        if (majorLog == null) {
            log.warn("【专业】审核日志不存在 {}", id);
            return;
        }
        if (CommonAuditStatus.AUDIT_FAIL.getStatus().equals(majorLog.getStatus())) {
            log.info("【专业】已拒绝数据，跳过 {} {}", id, majorLog.getStatus());
            return;
        }
        AccountsMajor major = majorDao.getByUuid(majorLog.getAccountUuid());
        if (major == null) {
            log.warn("【专业】不存在 {}", majorLog.getAccountUuid());
            return;
        }
        if (!major.getMajorOrigin().equals(majorLog.getMajor())) {
            if (major.getMajor().equals(majorLog.getMajor())) {
                // 仅更新客态
                major.setMajor("");
                majorDao.upsert(major);
                refreshCache(major, CommonAuditStatus.AUDITING.getStatus());
            }
            return;
        }
        if (CommonAuditStatus.AUDIT_SUCCESS.getStatus().equals(majorLog.getStatus())) {
            major.setMajor("");
            major.setMajorOrigin("");
        } else {
            // 保留上一次审核通过的结果 即当前客态结果
            major.setMajorOrigin(major.getMajor());
        }
        majorLog.setStatus(CommonAuditStatus.AUDIT_FAIL.getStatus());
        majorLog.setAuditTime(DateUtil.currentTimeSeconds());
        majorLog.setRiskRespData(JsonUtils.objectToString(resp));
        TransactionWrapper.me().wrap(() -> {
            majorLogDao.merge(majorLog);
            major.setAccountUuid(majorLog.getAccountUuid());
            majorDao.upsert(major);
        });
        // 缓存
        refreshCache(major, CommonAuditStatus.AUDIT_SUCCESS.getStatus());
    }

    private void refreshCache(AccountsMajor po, Integer status) {
        AccountMajorDto cache = JsonUtils.mapper().convertValue(po, AccountMajorDto.class);
        cache.setStatus(status);
        String key = RedisKeyConstant.ACCOUNT_MAJOR.setArg(po.getAccountUuid());
        accountBizStringRedisTemplate.opsForValue().set(key, JsonUtils.objectToString(cache), 1, TimeUnit.HOURS);
    }
}
