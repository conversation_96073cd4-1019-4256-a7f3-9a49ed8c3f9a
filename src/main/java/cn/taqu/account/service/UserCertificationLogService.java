package cn.taqu.account.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import cn.taqu.account.dto.UserBindingEventDTO;
import cn.taqu.account.event.AccountCertEvent;
import cn.taqu.account.event.UserBindingEventReporter;
import cn.taqu.account.utils.EnvUtil;
import cn.taqu.account.utils.KafkaSinkUtil;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.LocalConfUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Service
public class UserCertificationLogService {

    public static final String USER_BINDING_TOPIC = "user_binding_event";

    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;
    @Autowired
    private AccountCertEvent accountCertEvent;
    @Autowired
    private UserBindingEventReporter bindingEventReporter;

    public void logRealName(boolean isSuccess, String uuid, String authenticateImage, String baseImage, Float similarityScore, String identityNo) {
        if (isSuccess) {
            if (StringUtils.isNotBlank(identityNo)) {
                UserBindingEventDTO be = new UserBindingEventDTO();
                be.setEventType(2);
                be.setUuid(uuid);
                be.setEventTime(System.currentTimeMillis());
                SoaBaseParams baseParams = SoaBaseParams.fromThread();
                be.setAppCode(baseParams.getAppcode());
                be.setCloned(baseParams.getCloned());
                be.setCipher(MD5.create().digestHex(identityNo));
                bindingEventReporter.report(uuid, be);
//                kafkaSinkUtil.push(USER_BINDING_TOPIC, uuid, be);
            }

            logRealNameSuccess(uuid, authenticateImage, baseImage, similarityScore);
        } else {
            logRealNameFail(uuid, authenticateImage, baseImage, similarityScore);
        }
    }
    
    public void logRealNameBiz(boolean isSuccess, String uuid, String authenticateImage, String baseImage, Float similarityScore, String identityNo) {
        log.info("业务级实名认证，uuid={}", uuid);
        if (isSuccess) {
            if (StringUtils.isNotBlank(identityNo)) {
                UserBindingEventDTO be = new UserBindingEventDTO();
                be.setEventType(3);
                be.setUuid(uuid);
                be.setEventTime(System.currentTimeMillis());
                SoaBaseParams baseParams = SoaBaseParams.fromThread();
                be.setAppCode(baseParams.getAppcode());
                be.setCloned(baseParams.getCloned());
                be.setCipher(MD5.create().digestHex(identityNo));
                bindingEventReporter.report(uuid, be);
//                kafkaSinkUtil.push(USER_BINDING_TOPIC, uuid, be);
            }

            logRealNameBizSuccess(uuid, authenticateImage, baseImage, similarityScore);
        } else {
            logRealNameBizFail(uuid, authenticateImage, baseImage, similarityScore);
        }
    }

    public void logRealNameSuccess(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, 1, authenticateImage, baseImage, "CERTIFICATION", similarityScore);
        report(param);
    }

    /**
     * 队列上报
     *
     * @param param
     */
    private void report(Map<String, Object> param) {
        //kafkaSinkUtil.push(getTopicNameByEnv(), param);
        accountCertEvent.report(param.get("accountUuid").toString(), param);
    }

    public void logRealNameFail(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, StringUtils.isBlank(baseImage) ? 3 : 2, authenticateImage, baseImage, "CERTIFICATION", similarityScore);
        report(param);
    }
    
    public void logRealNameBizSuccess(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, 1, authenticateImage, baseImage, "BUSINESS_CERTIFICATION", similarityScore);
        report(param);
    }
    
    public void logRealNameBizFail(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, StringUtils.isBlank(baseImage) ? 3 : 2, authenticateImage, baseImage, "BUSINESS_CERTIFICATION", similarityScore);
        report(param);
    }

    public void logRealPhotoSuccess(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, 1, authenticateImage, baseImage, "REAL_PEOPLE", similarityScore);
        report(param);
    }

    public void logRealPhotoFail(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, StringUtils.isBlank(baseImage) ? 3 : 2, authenticateImage, baseImage, "REAL_PEOPLE", similarityScore);
        report(param);
    }

    public void logLiveCertSuccess(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, 1, authenticateImage, baseImage, "LIVING_ORGANISM", similarityScore);
        report(param);
    }

    public void logLiveCertFail(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, StringUtils.isBlank(baseImage) ? 3 : 2, authenticateImage, baseImage, "LIVING_ORGANISM", similarityScore);
        report(param);
    }

    public void logPhotoAlbumSuccess(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, 1, authenticateImage, baseImage, "PHOTO_ALBUM", similarityScore);
        report(param);
    }

    public void logPhotoAlbumFail(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, StringUtils.isBlank(baseImage) ? 3 : 2, authenticateImage, baseImage, "PHOTO_ALBUM", similarityScore);
        report(param);
    }

    public void logAccountLifeSuccess(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, 1, authenticateImage, baseImage, "ACCOUNT_LIFE", similarityScore);
        report(param);
    }

    public void logAccountLifeFail(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, StringUtils.isBlank(baseImage) ? 3 : 2, authenticateImage, baseImage, "ACCOUNT_LIFE", similarityScore);
        report(param);
    }
    
    public void logWithdrawSuccess(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, 1, authenticateImage, baseImage, "WITHDRAWAL_CERTIFICATION", similarityScore);
        report(param);
    }
    public void logWithdrawFail(String uuid, String authenticateImage, String baseImage, Float similarityScore) {
        Map<String, Object> param = logParam(uuid, StringUtils.isBlank(baseImage) ? 3 : 2, authenticateImage, baseImage, "WITHDRAWAL_CERTIFICATION", similarityScore);
        report(param);
    }

    public static Map<String, Object> logParam(String uuid, Integer status, String authenticateImage, String baseImage, String type, Float similarityScore) {
//        {
//            "accountUuid":"",
//            "status":1,// 1: 已认证；2：认证失败；3：无底图失败
//            "authenticateTime":**********,
//            "authenticateImage":""// 认证图片
//            "baseImage":"" // 对比的底图
//            "type":"CERTIFICATION" //CERTIFICATION:实名；REAL_PEOPLE：真人;LIVING_ORGANISM：活体
//        }
        Map<String, Object> map = new HashMap<>();
        map.put("accountUuid", uuid);
        map.put("status", status);
        map.put("authenticateTime", DateUtil.currentSeconds());
        map.put("authenticateImage", authenticateImage);
        map.put("baseImage", baseImage);
        map.put("similarityScore", similarityScore);
        map.put("type", type);
        return map;
    }

    public static String getTopicNameByEnv() {
        String onlineTopic = "risk_certification_log";

        return EnvUtil.GRAY_LIST.contains(LocalConfUtil.getLocalEnv()) ? onlineTopic + "_gray" : onlineTopic;
    }
}
