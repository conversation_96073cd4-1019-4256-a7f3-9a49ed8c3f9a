package cn.taqu.account.service;

import java.util.Set;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;

import cn.taqu.core.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 登录注册限制
 * 
 * <AUTHOR>
 * 2024年9月6日上午10:23:35
 */
@Slf4j
public class LoginRegLimitService {
    
    public static final String CANNOT_LOGIN_REG_NOTICE = "很抱歉，本应用已停止运行";

    private static LoginRegLimitConfig loginRegLimitConfig;
    
    @Data
    private static class LoginRegLimitConfig{
        private Set<Integer> cannotLoginRegClonedList = Sets.newHashSet();
    }
    
    /**
     * 配置参数
     * @param conf
     */
    public static void setConf(String conf){
        try {
            LoginRegLimitConfig loginRegLimitConfigNew = JsonUtils.stringToObject2(conf, new TypeReference<LoginRegLimitConfig>() {});
            loginRegLimitConfig = loginRegLimitConfigNew;
            log.info("登录注册限制={}", conf);
        }catch (Exception e){
            log.error("获取登录注册限制配置失败", e);
        }
    }
    
    /**
     * 
     * @return
     */
    public static Set<Integer> getCannotLoginRegClonedList(){
        return loginRegLimitConfig.getCannotLoginRegClonedList();
    }
    
    public static boolean isCanLoginReg(Integer cloned) {
        boolean isCanLoginReg = true;
        if(getCannotLoginRegClonedList().contains(cloned)) {
            isCanLoginReg = false;
            log.info("限制登录注册，cloned={}", cloned);
        }
        return isCanLoginReg;
    }
    
}
