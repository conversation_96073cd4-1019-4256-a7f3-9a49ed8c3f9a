package cn.taqu.account.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.crypto.digest.MD5;
import cn.taqu.account.bo.SwitchOnOff;
import cn.taqu.account.client.FujianGovApiClient;
import cn.taqu.account.client.FujianGovApiClient.CheckResidenceInfoResponse;
import cn.taqu.account.client.TengxunyunApiClient;
import cn.taqu.account.client.api.tengxunyun.TengxunyunApi;
import cn.taqu.account.client.api.tengxunyun.TengxunyunVoucher;
import cn.taqu.account.client.api.tengxunyun.TengxunyunWBappid;
import cn.taqu.account.client.api.tengxunyun.TxSyncResponse;
import cn.taqu.account.common.*;
import cn.taqu.account.config.biz.RetrieveAccountConfig;
import cn.taqu.account.constant.*;
import cn.taqu.account.dao.*;
import cn.taqu.account.dto.*;
import cn.taqu.account.etcd.CareModelConfig;
import cn.taqu.account.etcd.VersionControlConfig;
import cn.taqu.account.event.*;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.manager.AccountsCertificationManager;
import cn.taqu.account.manager.AccountsChatCertificationManager;
import cn.taqu.account.model.*;
import cn.taqu.account.mq.AccountManufacturerCallProducer;
import cn.taqu.account.search.ChatRealCertificationLogSearch;
import cn.taqu.account.utils.*;
import cn.taqu.account.vo.PictureInfoVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.*;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserCertdocCertverifyConsultRequest;
import com.alipay.api.request.AlipayUserCertdocCertverifyPreconsultRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserCertdocCertverifyConsultResponse;
import com.alipay.api.response.AlipayUserCertdocCertverifyPreconsultResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taqu.mp.account.dto.UserRegInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.taqu.account.service.UserCertificationLogService.USER_BINDING_TOPIC;

/**
 * Created by cqa on 2017/10/25.
 */
@Slf4j
@Service
public class AccountsCertificationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsCertificationService.class);

    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsCertificationLogService accountsCertificationLogService;
    @Autowired
    private AccountsCertificationDao accountsCertificationDao;
    @Autowired
    private BuryService buryService;
    @Autowired
    private ModifyGrowScoreService modifyGrowScoreService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    @Qualifier("lockStringRedisTemplate")
    private StringRedisTemplate lockStringRedisTemplate;
    @Autowired
    private VerifyRecordService verifyRecordService;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private TengxunyunApiClient tengxunyunApiClient;
    @Autowired
    private AliyunLiveFaceDetectService aliyunLiveFaceDetectService;
    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;
    @Autowired
    private PhotoCompareLogService photoCompareLogService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private RiskService riskService;
    @Autowired
    private AccountsCertificationHistoryService accountsCertificationHistoryService;
    @Autowired
    private ChatRealCertificationLogDao chatRealCertificationLogDao;
    @Autowired
    private AccountsChatCertificationDao accountsChatCertificationDao;
    @Autowired
    private AccountsCertificationManager accountsCertificationManager;
    @Autowired
    private AccountsChatCertificationManager accountsChatCertificationManager;
    @Autowired
    private SoaService soaService;
    @Autowired
    private KafkaSinkUtil kafkaSinkUtil;
    @Autowired
    private UserCertificationLogService userCertificationLogService;
    @Autowired
    private QiniuService qiniuService;
    @Autowired
    private FujianGovApiClient fujianGovApiClient;
    @Autowired
    private EventTrackReporter eventTrackReporter;
    @Autowired
    private AccountBehaviorReporter behaviorReporter;
    @Autowired
    private AccountBaseInfoManager accountBaseInfoManager;
    @Autowired
    private CareModelService careModelService;

    //    private static IAcsClient alipayClientV3 = null;
    // 配置到etcd 支付宝实名一致校验
    private static AlipayClient alipayDefaultClient = null;
    // 支付宝实名一致校验 的 appid
    public static String ALIPAY_DEFAULT_CLIENT_APPID = "";
    //    private static Map<String, SchemaUrl> appSchemaUrlMap = new HashMap<>();
    //配置到etcd，支付宝认证信息是否使用加密字段:true-使用加密字段,false-不使用加密字段
//    private static Boolean USE_ENCRYPT_INFO = false;
    //配置到etcd，认证信息是否直接查库:true-使用,false-不使用
//    private static String SCENE_ID = "**********";
    // 实名认证次数
    private static String CERTIFICATION_LIMIT_KEY = "certificationLimit_${accountUuid}";
    // 业务级实名认证次数
    private static String CHAT_CERTIFICATION_LIMIT_KEY = "chatCertificationLimit_${accountUuid}";
    private static Integer CERTIFICATION_LIMIT_TIME;
    private static Integer CERTIFICATION_SHOW_SWITCH;

    /**
     * 支付宝账号查询开关 线上 true-查询判断状态，false-查询不判断状态（现状）
     */
    private static boolean REWARD_ACCOUNT_QUERY_SWITCH_ONLINE;
    /**
     * 支付宝账号查询开关 灰度 true-查询判断状态，false-查询不判断状态（现状）
     */
    private static boolean REWARD_ACCOUNT_QUERY_SWITCH_GRAY;

    /**
     * 修改支付宝地址 线上 h5地址
     */
    private static String REWARD_ACCOUNT_UPDATE_H5_ONLINE;
    /**
     * 修改支付宝地址 灰度 h5地址
     */
    private static String REWARD_ACCOUNT_UPDATE_H5_GRAY;

    // etcd 控制开关 双重校验（真人和实名） 默认关闭
    public static Boolean DOUBLE_CHECK = false;

    // 香港居民居住证前缀
    public static String HONG_KONG_IDENTITY_PREFIX = "810000";
    // 澳门居民居住证前缀
    public static String MACAO_IDENTITY_PREFIX = "820000";
    // 台湾居民居住证前缀
    public static String TAIWAN_IDENTITY_PREFIX = "830000";

//    public static List<String> CERTIFICATION_TYPE_LIST = Lists.newArrayList("1","2");

    public static String ID_RETRIEVE_LIMIT_TEXT = "身份证每个月仅支持找回%s次";

    private static final String reward_account_invalid_sysmsg = "reward_account_invalid_sysmsg";
    private static final String rewardAccountInvalidSysMsg = "rewardAccountInvalidSysMsg";
    private static final String rewardAccountInvalidSysMsgGray = "rewardAccountInvalidSysMsgGray";
    private static final String reward_account_invalid_notice = "reward_account_invalid_notice";
    private static final String rewardAccountInvalidNotice = "rewardAccountInvalidNotice";

    /**
     * 获取支付账号查询开关
     *
     * @return
     */
    private String getRewardAccountInvalidSysMsg() {
        String str = rewardAccountInvalidSysMsg;
        if (EnvUtil.isGray()) {
            str = rewardAccountInvalidSysMsgGray;
        }
        return str;
    }

    /**
     * 初始化认证配置
     *
     * @param conf
     */
    public static void initCertificationConf(String conf) {
        try {
            Map<String, Object> map = JsonUtils.stringToObject(conf, new TypeReference<Map<String, Object>>() {
            });
            DOUBLE_CHECK = MapUtils.getBoolean(map, "doubleCheck", false);
            CERTIFICATION_LIMIT_TIME = MapUtils.getInteger(map,"limitTimes",5);
            // - `show_switch` 控制是否显示【非中国大陆居民实名】开关；1-打开；0-关闭
            CERTIFICATION_SHOW_SWITCH = MapUtils.getInteger(map, "certificationShowSwitch", 1) == CommConst.YES_1 ? CommConst.YES_1 : CommConst.NO_0;

            REWARD_ACCOUNT_QUERY_SWITCH_ONLINE = MapUtils.getBooleanValue(map, "rewardAccountQuerySwitchOnline", false);
            REWARD_ACCOUNT_QUERY_SWITCH_GRAY = MapUtils.getBooleanValue(map, "rewardAccountQuerySwitchGray", false);

            REWARD_ACCOUNT_UPDATE_H5_ONLINE = MapUtils.getString(map, "rewardAccountUpdateH5Online", "m=web&a=url&hide_nav=1&ul=https://web.whtaqu.cn/vueiii/trunk/branch/synthetical/modify-account/index.html#/alipay-withdraw");
            REWARD_ACCOUNT_UPDATE_H5_GRAY= MapUtils.getString(map, "rewardAccountUpdateH5Gray", "m=web&a=url&hide_nav=1&ul=https://web.whtaqu.cn/vueiii/trunk/branch/synthetical/modify-account/pre/index.html#/alipay-withdraw");

        } catch (Exception e) {
            LOGGER.error("设置double check(真人和实名)开关失败", e);
        }
    }

    /**
     * 获取支付账号修改h5
     *
     * @return
     */
    public static String getRewardAccountUpdateH5() {
        String h5 = REWARD_ACCOUNT_UPDATE_H5_ONLINE;
        if (EnvUtil.isGray()) {
            h5 = REWARD_ACCOUNT_UPDATE_H5_GRAY;
        }
        return h5;
    }

    /**
     * 获取支付账号查询开关
     *
     * @return
     */
    private boolean getRewardAccountQuerySwitch() {
        boolean flag = REWARD_ACCOUNT_QUERY_SWITCH_ONLINE;
        if (EnvUtil.isGray()) {
            flag = REWARD_ACCOUNT_QUERY_SWITCH_GRAY;
        }
        return flag;
    }

    private static String OPERATOR_NAME = "用户${accountUUid}";

    /**
     * 初始化支付宝实名证件信息比对验证配置出错
     * @param configJson
     */
    public static void alipayDefaultClient(String configJson){
        Map<Object, Object> configMap = JsonUtils.stringToObject(configJson, Map.class);
        if(MapUtils.isEmpty(configMap)){
            LOGGER.error("支付宝实名证件信息比对验证配置出错.");
            return;
        }
        // String serverUrl, String appId, String privateKey, String format, String charset, String alipayPublicKey, String signType
        String serverUrl = MapUtils.getString(configMap, "serverUrl", "");
        String appId = MapUtils.getString(configMap, "appId", "");
        String privateKey = MapUtils.getString(configMap, "privateKey", "");
        String format = MapUtils.getString(configMap, "format", "");
        String charset = MapUtils.getString(configMap, "charset", "");
        String alipayPublicKey = MapUtils.getString(configMap, "alipayPublicKey", "");
        String signType = MapUtils.getString(configMap, "signType", "");
        ALIPAY_DEFAULT_CLIENT_APPID = appId;
        alipayDefaultClient = new DefaultAlipayClient(serverUrl, appId, privateKey, format, charset, alipayPublicKey, signType);
    }

    /**
     * 获取腾讯云认证客户端所需要数据
     *
     * @param accountUuid
     * @param certNo
     * @param certName
     * @param cloned
     * @param platformId
     * @param channel
     * @return
     */
    public Map<String, String> tengxunyunInitCert(String accountUuid, String certNo, String certName, Integer cloned, Integer platformId, String env, String channel, KafkaManufacturerCallDto.BelongBiz belongBiz) {
        Map<String, String> result = Maps.newHashMap();

        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        String appid = WBappid.getAppid();
        String signTicket = tengxunyunVoucher.getSignTicket();
        String nonceTicket = tengxunyunApiClient.getNonceTicket(appid, tengxunyunVoucher.getAccessToken(), accountUuid);

        String nonce = TengxunyunApiClient.generateNonce();
        String orderNo = TengxunyunApiClient.generateOrderNo();
        List<String> values = Lists.newArrayList();
        values.add("1.0.0");
        values.add(appid);
        values.add(nonce);
        values.add(accountUuid);
        String faceIdSign = TengxunyunApiClient.sign(Lists.newArrayList(values), signTicket);
        String returnSign = TengxunyunApiClient.sign(Lists.newArrayList(values), nonceTicket);

        // 获取faceid
        String faceid = tengxunyunApiClient.getFaceid(appid, orderNo, certName, certNo, accountUuid, faceIdSign, nonce);

        // 返回给客户端的数据
        result.put("userid", accountUuid);
        result.put("nonce", nonce);
        result.put("sign", returnSign);
        result.put("orderno", orderNo);
        result.put("faceid", faceid);



        SpringContextHolder.getBean(AccountManufacturerCallProducer.class).send(
                KafkaManufacturerCallDto.tencentFaceIdentify(
                        accountUuid,
                        belongBiz,
                        "1",
                        Boolean.TRUE.toString())
        );

        return result;
    }

    /**
     * 对支付宝认证敏感字段进行加密
     * @param keyValueMap
     * @return
     */
    private Map<String,Object> encryptCertInfo(Map<String,Object> keyValueMap, SwitchOnOff switchOnOff){
        Map<String,String> originalInfoMap = Maps.newHashMap();
        originalInfoMap.put("real_name",MapUtils.getString(keyValueMap,"real_name"));
        originalInfoMap.put("identity_no",MapUtils.getString(keyValueMap,"identity_no"));
        originalInfoMap.put("reward_account",MapUtils.getString(keyValueMap,"reward_account"));
        Map<String,String> encryptMap = EncryptUtil.batchEncrypt(originalInfoMap);
        Map<String,String> digestMap = EncryptUtil.batchSm3(originalInfoMap);
        keyValueMap.put("real_name_cipher", encryptMap.get("real_name"));
        keyValueMap.put("real_name_digest",digestMap.get("real_name"));
        keyValueMap.put("identity_no_cipher", encryptMap.get("identity_no"));
        keyValueMap.put("identity_no_digest",digestMap.get("identity_no"));
        if(StringUtils.isBlank(MapUtils.getString(keyValueMap,"reward_account"))){
            keyValueMap.put("reward_account_cipher", "");
            keyValueMap.put("reward_account_digest", "");
        }else{
            keyValueMap.put("reward_account_cipher", encryptMap.get("reward_account"));
            keyValueMap.put("reward_account_digest",digestMap.get("reward_account"));
        }

        if(!switchOnOff.isOn(true)) {
            keyValueMap.put("real_name", "");
            keyValueMap.put("identity_no", "");
            keyValueMap.put("reward_account", "");
        }

        return keyValueMap;
    }

    private static boolean ifGrownUp(String num)   {
        int year = Integer.parseInt(num.substring(6, 10));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date update;
		try {
			update = sdf.parse(String.valueOf(year + 18) + num.substring(10, 14));
		} catch (ParseException e) {
			LOGGER.warn("实名认证身份证校验失败",e);
			return true;
		}
        Date today = new Date();
        return today.after(update);
    }

    /**
     * 支付宝实人认证是否成功，true-成功，false-失败
     * 文档地址：https://help.aliyun.com/document_detail/149349.html?spm=a2c4g.********.2.13.3da17880qBAuVz#reference-2381276
     * @param orderno
     * @return
     */
    public TxSyncResponse TengxuyunQueryCert(String orderno, String accountUuid, KafkaManufacturerCallDto.BelongBiz belongBiz) throws Exception{
//        Boolean result = false;
        TxSyncResponse txSyncResponse = tengxuyunQueryCert(orderno);
//        String code = txSyncResponse.getCode();
//        if("0".equals(code)){
//            result = true;
//        }
        SpringContextHolder.getBean(AccountManufacturerCallProducer.class).send(
                KafkaManufacturerCallDto.tencentFaceIdentify(
                        accountUuid,
                        belongBiz,
                        "2",
                        Boolean.TRUE.toString())
        );
        return txSyncResponse;
    }

    /**
     * 认证是否成功，直接返回结果map
     * @param orderno
     * @return
     */
    public TxSyncResponse tengxuyunQueryCert(String orderno){
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        TengxunyunWBappid WBappid = TengxunyunApiClient.getConfig(platformId, cloned, LocalConfUtil.getLocalEnv(), env, channel);
        TengxunyunVoucher tengxunyunVoucher = tengxunyunApiClient.getTengxunyunVoucher(WBappid);
        TengxunyunApi tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
        String appid = WBappid.getAppid();

        String nonce = TengxunyunApiClient.generateNonce();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderno);
        values.add("1.0.0");
        values.add(nonce);
        String sign = TengxunyunApiClient.sign(values, tengxunyunVoucher.getSignTicket());

        String serverSync = tengxunyunApi.serverSync(appid, "1.0.0", nonce, orderno, sign, "2");
        LOGGER.info("腾讯实名serverSync【{}】", serverSync);

        TxSyncResponse txSyncResponse =
                JsonUtils.stringToObject(serverSync, new TypeReference<TxSyncResponse>() {});
        return txSyncResponse;
    }

    /**
     * 解密real_name、identity_no字段并返回
     * 已使用加密字段
     *
     * @param bizNo
     * @return
     */
    private Object[] getDecryptInfoByBizNo(String bizNo){
        Object[] accountCertification = accountsCertificationDao.findInfoByBizNo(bizNo);
        Long certificationId = accountCertification == null || accountCertification[0] == null ? null : Long.valueOf(accountCertification[0].toString());
        if (certificationId == null) {
            throw new ServiceException(CodeStatus.BIZ_NO_NO_EXISTS);
        }
        String realNameCipher = accountCertification[2] == null ? "" : accountCertification[2].toString();
        String identityNoCipher = accountCertification[3] == null ? "" : accountCertification[3].toString();
        if(StringUtils.isNotEmpty(realNameCipher)) {
            accountCertification[2] = EncryptUtil.decrypt(realNameCipher);
        }
        if(StringUtils.isNotEmpty(identityNoCipher)){
            accountCertification[3] = EncryptUtil.decrypt(identityNoCipher);
        }
        return accountCertification;
    }

    /**
     * 解密real_name、identity_no字段并返回
     * @param bizNo
     * @return
     */
    private Object[] getDecryptInfoByBizNoForChat(String bizNo){
        Object[] accountCertification = accountsChatCertificationDao.findInfoByBizNo(bizNo);
        Long certificationId = accountCertification == null || accountCertification[0] == null ? null : Long.valueOf(accountCertification[0].toString());
        if (certificationId == null) {
            throw new ServiceException(CodeStatus.BIZ_NO_NO_EXISTS);
        }
        String realNameCipher = accountCertification[2] == null ? "" : accountCertification[2].toString();
        String identityNoCipher = accountCertification[3] == null ? "" : accountCertification[3].toString();
        if(StringUtils.isNotEmpty(realNameCipher)) {
            accountCertification[2] = EncryptUtil.decrypt(realNameCipher);
        }
        if(StringUtils.isNotEmpty(identityNoCipher)){
            accountCertification[3] = EncryptUtil.decrypt(identityNoCipher);
        }
        return accountCertification;
    }

    private Integer getCheckedIdentityNoCount(String identityNo, Integer cloned) {
        return accountsCertificationManager.getCheckedIdentityNoCount(cloned, identityNo);
    }

    private Integer getCheckedIdentityNoCountForChat(Integer cloned, String identityNo) {
        return accountsChatCertificationManager.getCheckedIdentityNoCount(cloned, identityNo);
    }

    /**
     * 取消认证
     *
     * @param accountUuid
     * @return
     */
    @Transactional
    public Map<String, String> cancelCertification(String accountUuid, String operator) {
        return this.cancelCertification(accountUuid, false, operator);
    }

    @Transactional
    public Map<String, String> cancelCertification(String accountUuid, boolean fromAccountDestroy, String operator) {
        Object[] accountCertification = accountsCertificationDao.findIdByAccountUuid(accountUuid);
        Long certificationId = accountCertification == null || accountCertification[0] == null ? null : Long.valueOf(accountCertification[0].toString());
        Integer certificationChecked = accountCertification == null || accountCertification[1] == null ? null : Integer.valueOf(accountCertification[1].toString());
        String realNameCipher = accountCertification == null || accountCertification[2] == null ? null : accountCertification[2].toString();
        String identityNoCipher = accountCertification == null || accountCertification[3] == null ? null : accountCertification[3].toString();
        String certificationPhotoUrl = accountCertification == null || accountCertification[4] == null ? "" : accountCertification[4].toString();
        String rewardAccountCipher = accountCertification == null || accountCertification[5] == null ? "" : accountCertification[5].toString();
        String rewardAccountStatus = accountCertification == null || accountCertification[6] == null ? "" : accountCertification[6].toString();
        if (certificationId == null || certificationChecked == null || certificationChecked != 1) {
            if (fromAccountDestroy) {
                if(certificationId != null && Objects.equals(certificationChecked, 0)){
                    Map<String, Object> keyValueMap = new HashMap<>();
                    if(fromAccountDestroy){
                        keyValueMap.put("identity_no_status", CommConst.NO_0);
                    }
                    keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
                    LOGGER.warn("该用户还未认证，取消身份证占用，用户uuid:{}", accountUuid);
                    accountsCertificationDao.updateById(keyValueMap, certificationId, accountUuid);
                }
                LOGGER.warn("该用户还未认证，无需取消认证，用户uuid:{}", accountUuid);

                return null;
            } else {
                throw new ServiceException(CodeStatus.ACCOUNT_NO_CERTIFICATION);
            }
        }
        int isChecked = CommConst.NO_0;
        Map<String, Object> keyValueMap = new HashMap<>(4);
        keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
        keyValueMap.put("is_checked", isChecked);
        if(fromAccountDestroy){
            keyValueMap.put("identity_no_status", CommConst.NO_0);
        }
        // 2024.11.12 有支付宝账号，且处于绑定状态，则解绑
        if(StringUtils.isNotBlank(rewardAccountCipher) && rewardAccountStatus.equals(String.valueOf(AccountsCertification.RewardAccountStatus.BIND.getValue()))) {
            keyValueMap.put("reward_account_status", AccountsCertification.RewardAccountStatus.UNBIND.getValue());
        }
        accountsCertificationDao.updateById(keyValueMap, certificationId, accountUuid);
        accountsPersonalInfoService.setZhimaCertification(accountUuid, isChecked);

        Integer whiteList = CommConst.NO_0;

        String realName = EncryptUtil.decrypt(realNameCipher);
        String identityNo = EncryptUtil.decrypt(identityNoCipher);

        if(fromAccountDestroy){
            BuryService.pushToCertificationLog(accountUuid, realName, identityNo, AccountsCertificationChangeLog.LogTypeEnum.UNBIND.getValue(), AccountsCertificationChangeLog.OperatorTypeEnum.DESTROY.getValue(), accountUuid, whiteList, certificationPhotoUrl);
        }else{
            BuryService.pushToCertificationLog(accountUuid, realName, identityNo, AccountsCertificationChangeLog.LogTypeEnum.UNBIND.getValue(), AccountsCertificationChangeLog.OperatorTypeEnum.BACKSTAGE.getValue(), operator, whiteList, certificationPhotoUrl);
        }
        Map<String, String> resultMap = this.getByUuids(new String[]{accountUuid}).get(accountUuid);
        SpringContextHolder.getBean(AllureService.class).refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
        return resultMap;
    }

    @Transactional
    public Map<String, String> cancelCertificationForChat(String accountUuid, boolean fromAccountDestroy, String operator) {
        Object[] accountCertification = accountsChatCertificationDao.findIdByAccountUuid(accountUuid);
        Long certificationId = accountCertification == null || accountCertification[0] == null ? null : Long.valueOf(accountCertification[0].toString());
        Integer certificationChecked = accountCertification == null || accountCertification[1] == null ? null : Integer.valueOf(accountCertification[1].toString());
        String realNameCipher = accountCertification == null || accountCertification[2] == null ? null : accountCertification[2].toString();
        String identityNoCipher = accountCertification == null || accountCertification[3] == null ? null : accountCertification[3].toString();
        String certificationPhotoUrl = accountCertification == null || accountCertification[4] == null ? "" : accountCertification[4].toString();
        if (certificationId == null || certificationChecked == null || certificationChecked != 1) {
            if (fromAccountDestroy) {
                if(certificationId != null && Objects.equals(certificationChecked, 0)){
                    Map<String, Object> keyValueMap = new HashMap<>();
                    keyValueMap.put("identity_no_status", CommConst.NO_0);
                    keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
                    LOGGER.warn("该用户还未认证，取消身份证占用，用户uuid:{}", accountUuid);
                    accountsChatCertificationDao.updateById(keyValueMap, certificationId);
                }
                LOGGER.warn("该用户还未认证，无需取消认证，用户uuid:{}", accountUuid);
                return null;
            } else {
                throw new ServiceException(CodeStatus.ACCOUNT_NO_CERTIFICATION);
            }
        }
        int isChecked = 0;
        Map<String, Object> keyValueMap = new HashMap<>(4);
        keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
        keyValueMap.put("is_checked", isChecked);
        if(fromAccountDestroy){
            keyValueMap.put("identity_no_status", CommConst.NO_0);
        }
        accountsChatCertificationDao.updateById(keyValueMap, certificationId);
        accountsPersonalInfoService.setChatRealCertification(accountUuid, isChecked);

        Integer whiteList = CommConst.NO_0;

        String realName = EncryptUtil.decrypt(realNameCipher);
        String identityNo = EncryptUtil.decrypt(identityNoCipher);

        if(fromAccountDestroy){
            BuryService.pushToCertificationLog(accountUuid, realName, identityNo, AccountsCertificationChangeLog.LogTypeEnum.UNBIND.getValue(), AccountsCertificationChangeLog.OperatorTypeEnum.DESTROY.getValue(), accountUuid, whiteList, certificationPhotoUrl);
        }else{
            BuryService.pushToCertificationLog(accountUuid, realName, identityNo, AccountsCertificationChangeLog.LogTypeEnum.UNBIND.getValue(), AccountsCertificationChangeLog.OperatorTypeEnum.BACKSTAGE.getValue(), operator, whiteList, certificationPhotoUrl);
        }
        return this.getByUuids(new String[]{accountUuid}).get(accountUuid);
    }

    /**
     * 获取已认证的身份证关联的accountUuid
     *
     * @param identityNo
     * @return
     */
    public String getCheckedAccountUuidByIdentityNo(String identityNo) {
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        String result = "";
        List<String> list;
        list = accountsCertificationManager.listRelateAccountByIdentityNo(cloned, identityNo, 1);

        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }

        list = accountsChatCertificationManager.listRelateAccountByIdentityNo(cloned, identityNo, 1);

        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return result;
    }

    /**
     * 根据uuid批量查询（有解密）
     *
     * @param accountUuids
     * @return
     */
    public Map<String, Map<String, String>> getByUuids(String[] accountUuids) {
        return this.getByUuidsDB(accountUuids);
    }

    /**
     * 根据uuid批量查询（有解密）
     *
     * @return
     */
    public Map<String, String> getByUuid(String accountUuid) {
        return MapUtils.getMap(this.getByUuidsDB(new String[] {accountUuid}), accountUuid, null);
    }

    public Map<String, String> getCertInfoByUuid(String uuid) {
        Map<String, String> res = new HashMap<>();
        if (StringUtils.isBlank(uuid)) {
            return res;
        }

        // 获取应用级实名认证
        AccountsCertification accountsCertification = accountsCertificationDao.getByAccountUuid(uuid);
        if (accountsCertification != null) {
            String realName = accountsCertification.getRealName();
            String realNameCipher = accountsCertification.getRealNameCipher();
            String identityNo = accountsCertification.getIdentityNo();
            String identityNoCipher = accountsCertification.getIdentityNoCipher();
            Integer isChecked = accountsCertification.getIsChecked();

            if (Objects.equals(1, isChecked)) {
                // 使用加密
                if(ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
                    realName = EncryptUtil.decrypt(realNameCipher);
                    identityNo = EncryptUtil.decrypt(identityNoCipher);
                }

                res.put("cert_type", "APP");
                res.put("real_name", realName);
                res.put("identity_no", identityNo);
                return res;
            }
        }

        // 获取业务级级实名认证
        AccountsChatCertification accountsChatCertification = accountsChatCertificationDao.getByAccountUuid(uuid);
        if (accountsChatCertification != null) {
            String realName = accountsChatCertification.getRealName();
            String realNameCipher = accountsChatCertification.getRealNameCipher();
            String identityNo = accountsChatCertification.getIdentityNo();
            String identityNoCipher = accountsChatCertification.getIdentityNoCipher();
            Integer isChecked = accountsChatCertification.getIsChecked();

            if (Objects.equals(1, isChecked)) {
                // 使用加密
                if(ToolsService.accountsChatCertificationSwitchEncryption.isOn(true)) {
                    realName = EncryptUtil.decrypt(realNameCipher);
                    identityNo = EncryptUtil.decrypt(identityNoCipher);
                }

                res.put("cert_type", "BIZ");
                res.put("real_name", realName);
                res.put("identity_no", identityNo);
                return res;
            }
        }

        res.put("cert_type", "");
        res.put("real_name", "");
        res.put("identity_no", "");
        return res;
    }

    /**
     * 从数据库获取数据
     *
     * 重要：如果用户未认证，会将数据置为空
     *
     * @param accountUuids
     * @return
     */
    private Map<String, Map<String, String>> getByUuidsDB(String[] accountUuids) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        Map<String,Map<String,String>> userCipherMap = Maps.newHashMap();
        for (String accountUuid : accountUuids) {
            if (StringUtils.isBlank(accountUuid)) {
                continue;
            }

            accountUuid = accountUuid.trim();

            Object[] accountsCertification = accountsCertificationDao.findByAccountUuid(accountUuid);
            if (accountsCertification != null) {
                String realName = accountsCertification[1] == null ? "" : accountsCertification[1].toString();
                String identityNo = accountsCertification[2] == null ? "" : accountsCertification[2].toString();
                String bizNo = accountsCertification[3] == null ? "" : accountsCertification[3].toString();
                String isChecked = accountsCertification[4] == null ? "" : accountsCertification[4].toString();
                String rewardAccount = accountsCertification[5] == null ? "" : accountsCertification[5].toString();
                String realNameCipher = accountsCertification[6] == null ? "" : accountsCertification[6].toString();
                String identityNoCipher = accountsCertification[7] == null ? "" : accountsCertification[7].toString();
                String rewardAccountCipher = accountsCertification[8] == null ? "" : accountsCertification[8].toString();
                String identityNoBirth = accountsCertification[9] == null ? "" : accountsCertification[9].toString();
                String rewardAccountChecked = accountsCertification[10] == null ? "0" : accountsCertification[10].toString();
                String rewardAccountStatus = accountsCertification[11] == null ? "0" : accountsCertification[11].toString();

                Map<String, String> keyValueMap = new HashMap<>();
                keyValueMap.put("real_name", realName);
                keyValueMap.put("identity_no", identityNo);
                keyValueMap.put("biz_no", bizNo);
                keyValueMap.put("reward_account", rewardAccount);
                keyValueMap.put("is_checked", isChecked);
                keyValueMap.put("identity_no_birth", identityNoBirth);
                keyValueMap.put("open_alipay_auth", VersionSwitchService.SWITCH_20220302.getOpenAlipayAuth() + "");
                keyValueMap.put("reward_account_checked", rewardAccountChecked);
                keyValueMap.put("reward_account_status", rewardAccountStatus);

                //用户加密的map
                Map<String,String> cipherMap = Maps.newHashMap();
                cipherMap.put("real_name_cipher",realNameCipher);
                cipherMap.put("identity_no_cipher",identityNoCipher);
                cipherMap.put("reward_account_cipher",rewardAccountCipher);

                userCipherMap.put(accountUuid,cipherMap);

                // 认证成功，更新缓存
                if(Objects.equals(isChecked, "1")) {
                    keyValueMap.put("identity_no_birth", identityNoBirth == null ? "" : identityNoBirth);
                }

                result.put(accountUuid, keyValueMap);
                continue;
            }

            Map<String, String> noCertificationMap = new HashMap<>(5);
            noCertificationMap.put("real_name", "");
            noCertificationMap.put("identity_no", "");
            noCertificationMap.put("biz_no", "");
            noCertificationMap.put("reward_account", "");
            noCertificationMap.put("is_checked", "0");
            noCertificationMap.put("identity_no_birth", "");
            noCertificationMap.put("open_alipay_auth", VersionSwitchService.SWITCH_20220302.getOpenAlipayAuth() + "");
            noCertificationMap.put("reward_account_checked", CommConst.NO_0.toString());
            noCertificationMap.put("reward_account_status", AccountsCertification.RewardAccountStatus.DEFAULT.getValue() + "");
            result.put(accountUuid, noCertificationMap);
        }

        Map<String,String> totalCipherMap = Maps.newHashMap();
        userCipherMap.forEach((uuid,cipherMap)->{
            totalCipherMap.put(cipherMap.get("real_name_cipher"),cipherMap.get("real_name_cipher"));
            totalCipherMap.put(cipherMap.get("identity_no_cipher"),cipherMap.get("identity_no_cipher"));
            totalCipherMap.put(cipherMap.get("reward_account_cipher"),cipherMap.get("reward_account_cipher"));
        });
        Map<String,String> originalMap = EncryptUtil.batchDecrypt(totalCipherMap);
        userCipherMap.forEach((uuid,cipherMap)->{
            Map<String,String> resultMap = result.get(uuid);
            if(MapUtils.isNotEmpty(resultMap)){
                String realNameCipher = MapUtils.getString(cipherMap,"real_name_cipher","");
                String identityNoCipher = MapUtils.getString(cipherMap,"identity_no_cipher","");
                String rewardAccountCipher = MapUtils.getString(cipherMap,"reward_account_cipher","");
                resultMap.put("real_name",MapUtils.getString(originalMap,realNameCipher,""));
                resultMap.put("identity_no",MapUtils.getString(originalMap,identityNoCipher,""));
                resultMap.put("reward_account",MapUtils.getString(originalMap,rewardAccountCipher,""));
            }
        });
//        }

        //未认证，所有返回字段设置为空
        for (Map.Entry<String, Map<String, String>> entry : result.entrySet()) {
            Map<String, String> certificationInfo = entry.getValue();
            int isChecked = MapUtils.getIntValue(certificationInfo, "is_checked");
            if (isChecked == 1) {
                if(getRewardAccountQuerySwitch()) {
                    // 非绑定有效状态，不返回支付宝账号
                    int rewardAccountStatus = MapUtils.getIntValue(certificationInfo, "reward_account_status");
                    if(!Objects.equals(rewardAccountStatus, AccountsCertification.RewardAccountStatus.BIND.getValue())) {
                        certificationInfo.put("reward_account", "");
                    };
                }

                continue;
            }
            for (Map.Entry<String, String> certificationEntry : certificationInfo.entrySet()) {
                String key = certificationEntry.getKey();
                if(!Objects.equals(key, "open_alipay_auth") && !Objects.equals(key, "reward_account_checked") && !Objects.equals(key, "reward_account_status")){
                    certificationEntry.setValue(Objects.equals("is_checked", key) ? "0" : "");
                }
            }
        }

        return result;
    }

    /**
     * 根据uuid批量查询
     *
     * @param accountUuids
     * @return 返回 reward_account_cipher
     */
    public Map<String, Map<String, String>> listInfoByAccountUuid(String[] accountUuids) {
        Map<String, Map<String, String>> result = Maps.newHashMap();
        for (String accountUuid : accountUuids) {
            if (StringUtils.isBlank(accountUuid)) {
                continue;
            }

            accountUuid = accountUuid.trim();

            AccountsCertification accountsCertification = accountsCertificationDao.getByAccountUuid(accountUuid);
            if (accountsCertification != null) {
                String rewardAccountCipher = accountsCertification.getRewardAccountCipher();
                Integer isChecked = accountsCertification.getIsChecked();
                Integer rewardAccountStatus = accountsCertification.getRewardAccountStatus();

                if(isChecked != 1) {
                    continue;
                }
                Map<String, String> keyValueMap = new HashMap<>(5);
                keyValueMap.put("reward_account_cipher", rewardAccountCipher);
                // 非绑定有效状态，不返回支付宝账号
                if(getRewardAccountQuerySwitch() && !Objects.equals(rewardAccountStatus, AccountsCertification.RewardAccountStatus.BIND.getValue())) {
                    keyValueMap.put("reward_account_cipher", "");
                };

                result.put(accountUuid, keyValueMap);
            }
        }

        return result;
    }

    public Map<String, Map<String, String>> getRelateAccountByUuids(String[] accountUuids) {
        Map<String, Map<String, String>> result = this.getByUuids(accountUuids);

        Map<String, UserRegInfo> userRegInfo = soaService.getUserRegInfo(Arrays.asList(accountUuids));

        result.forEach((uuid, certificationInfoMap) -> {
            String identityNo = certificationInfoMap.get("identity_no");
            String isChecked = certificationInfoMap.get("is_checked");

            UserRegInfo regInfo = userRegInfo.get(uuid);
            if (regInfo == null) {
                LOGGER.warn("用户注册信息不存在，uuid:{}", uuid);
                return;
            }

            if(Objects.equals(isChecked, "1")) {
                certificationInfoMap.put("relate_uuid", this.getLastUuidByIdentityNo(regInfo.getCloned(), identityNo, uuid));
            }else {
                certificationInfoMap.put("relate_uuid", "");
            }

        });

        return result;
    }

    private String getLastUuidByIdentityNo(Integer cloned, String identityNo, String excludeUuid) {
        if (StringUtils.isBlank(identityNo)) {
            return "";
        }
        String relateUuid = accountsCertificationManager.getLastUuidByIdentityNo(cloned, identityNo, excludeUuid);
        return StringUtils.trimToEmpty(relateUuid);
    }

    /**
     * 更新身份证生日
     *
     * @param accountUuid
     * @param identityNo
     * @param isChecked
     */
    private void saveToCacheIdentityNoBirth(String accountUuid, String identityNo, Integer isChecked) {
        // 认证成功，更新缓存
        if(Objects.equals(isChecked, 1)) {
            String accountKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);

            Long identityNoBirth = getBirthByIdentityNo(identityNo.trim());
            accountStringRedisTemplate.opsForHash().put(accountKey, UuidInfoField.IDENTITY_NO_BIRTH, identityNoBirth == null ? "" : identityNoBirth.toString());
        }
    }

    /**
     * 修改支付宝账号
     * @param accountUuid
     * @param rewardAccount
     * @param operatorName
     * @param isAlipayCheck 是否完成支付宝验证
     */
    @Transactional
    public void updateRewardAccount(String accountUuid, String rewardAccount, String operatorName, Boolean isAlipayCheck) {
        Map<String, String> map = getByUuid(accountUuid);
        String isChecked = MapUtils.getString(map, "is_checked");
        String oldRewardAccount = MapUtils.getString(map, "reward_account");
        String realName = MapUtils.getString(map, "real_name");
        String identityNo = MapUtils.getString(map, "identity_no");

        if (!Objects.equals(isChecked, "1")) {
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.NO_ACCOUNT_CERTIFICATION);
            throw new ServiceException(CodeStatus.NO_CERTIFICATION_2);
        }
        if (oldRewardAccount.equalsIgnoreCase(rewardAccount)) {
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.NO_CHANGE);
            throw new ServiceException(CodeStatus.CERTIFICATION_NO_CHANGE);
        }
        if(riskService.checkOpenIdRisk(BanOpenIdTypeEnum.AliPay.getValue(), rewardAccount)){
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.RISK);
            throw new ServiceException(CodeStatus.OPENID_ALREADY_BAN);
        }

        Boolean rewardAccountIsCheckSuccess = null;
        try {
            rewardAccountIsCheckSuccess = SoaService.aliAliRealNameConsult(realName, rewardAccount);
        } catch (Exception e) {
            log.warn("请求j105校验支付账号异常，accountUuid={}", accountUuid, e);
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.SYSTEM_ERROR);
            throw new ServiceException(CodeStatus.BIND_SYSTEM_ERROR);
        }
        if(rewardAccountIsCheckSuccess == null ) {
            log.warn("请求j105校验支付账号异常，accountUuid={}", accountUuid);
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.SYSTEM_ERROR);
            throw new ServiceException(CodeStatus.BIND_SYSTEM_ERROR);
        }

        // 新增支付宝校验支付宝账号 + 姓名
        if (!rewardAccountIsCheckSuccess) {
            log.warn("实名信息不一致，accountUuid={}", accountUuid);
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.ALIPAY_ACCOUNT_CHECK_FAIL);
            throw new ServiceException(CodeStatus.ALIPAY_CHECK_ACCOUNT_FAIL);
        }

        // 提现管控，非白名单校验
        if (!isWhitelist(accountUuid) && AbTestService.isWithdrawalLimitExpGroup(accountUuid, SoaBaseParams.fromThread().getCloned())) {
            // 提现历史记录管控
            InfoFiledCacheDTO info = accountBaseInfoManager.getInfoByUuid(accountUuid, new String[]{UuidInfoField.LOGIN_CLONED});
            Integer cloned = info.getLoginCloned();
            ClonedGroupEnum group = ClonedGroupEnum.getClonedGroup(cloned);
            if (hasWithdrawRecord(rewardAccount, identityNo, Lists.newArrayList(group.getClonedList()), accountUuid)) {
                log.warn("该支付宝已绑定平台其它实名账号，accountUuid={}", accountUuid);
                reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.HAS_BOUND_OTHERS);
                throw new ServiceException(CodeStatus.ALIPAY_ACCOUNT_IS_RISK);
            }
        }

        String rewardAccountCipher = EncryptUtil.encrypt(rewardAccount);
        String rewardAccountDigest = EncryptUtil.sm3(rewardAccount);
        Integer rewardAccountChecked = isAlipayCheck != null && isAlipayCheck ? 1 : 0;

        Integer accountCloned = accountsInfoService.getAccountCloned(accountUuid);
        // 查询支付宝账号相同的账号(支付账号处于绑定状态)
        Set<String> accountUuidSet = accountsCertificationManager.listAccountUuidByRewardAccountDigest(accountCloned, rewardAccountDigest, AccountsCertification.RewardAccountStatus.BIND.getValue());
        accountUuidSet.remove(accountUuid);

        accountsCertificationDao.updateRewardAccount(rewardAccountChecked, AccountsCertification.RewardAccountStatus.BIND.getValue(), rewardAccountCipher, rewardAccountDigest, accountUuid);
        accountsCertificationLogService.create(accountUuid, oldRewardAccount, rewardAccount, operatorName);

        updateRewardAccountStatusToInvalid(accountUuidSet);

        String remark = TemplateStringConst.getContent(TemplateStringConst.CERT_LOG_REMARK_REWARD_ACCOUNT_BIND, accountUuid, DateUtil.dateToString20(new Date()), oldRewardAccount, rewardAccount, isAlipayCheck ? "是" : "否");
        BuryService.pushToCertLog(accountUuid, CommConst.NO_0, CertTypeEnum.REWARD_ACCOUNT.getValue(), isAlipayCheck? accountUuid: SoaBaseParams.fromThread().getToken(), AccountsCertLog.OperatorTypeEnum.BIND.getValue(), getCertificationPhotoUrlByUuid(accountUuid), remark);
        reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.SUCCESS, null);
        // 支付宝绑定推送
        UserBindingEventDTO be = new UserBindingEventDTO();
        be.setEventType(4);
        be.setSubType(StringUtils.isBlank(oldRewardAccount) ? 1 : 2);
        be.setUuid(accountUuid);
        be.setEventTime(System.currentTimeMillis());
        SoaBaseParams baseParams = SoaBaseParams.fromThread();
        be.setAppCode(baseParams.getAppcode());
        be.setCloned(baseParams.getCloned());
        be.setCipher(MD5.create().digestHex(rewardAccount));
        SpringContextHolder.getBean(UserBindingEventReporter.class).report(accountUuid, be);
//        kafkaSinkUtil.push(USER_BINDING_TOPIC, accountUuid, be);
    }


    /**
     * TODO 账号是否有提现记录
     *
     * @param rewardAccount
     * @param identityNo
     * @param clonedList
     * @return
     */
    private boolean hasWithdrawRecord(String rewardAccount, String identityNo, ArrayList<Integer> clonedList, String accountUuid) {
        // 取反情况, x -> X，X -> x
        String oppositeIdentityDigest = null;
        if (identityNo.endsWith("x")) {
            oppositeIdentityDigest = EncryptUtil.sm3(identityNo.toUpperCase());
        } else if (identityNo.endsWith("X")) {
            oppositeIdentityDigest = EncryptUtil.sm3(identityNo.toLowerCase());
        }

        String alipayDigest = EncryptUtil.sm3(rewardAccount);
        String identityDigest = EncryptUtil.sm3(identityNo);
        List<String> identityList = soaService.withdrawRecord(alipayDigest, clonedList);
        if (CollectionUtils.isNotEmpty(identityList)) {
            String finalOppositeIdentityDigest = oppositeIdentityDigest;
            boolean result = identityList.stream().anyMatch(id -> {
                // 原本请求数据
                if (Objects.equals(id, identityDigest)) {
                    return false;
                }
                // 进行大小写转化后比对，历史逻辑中存在x、X小写混用
                return !StringUtils.isNotBlank(finalOppositeIdentityDigest) || !Objects.equals(id, finalOppositeIdentityDigest);
            });
            if (result) {
                log.warn("该支付宝账号存在其他体现记录：alipay:{}, identityList:{}, iden:{}, uuid:{}", alipayDigest, identityList, identityDigest, accountUuid);
            }
            return result;
        }
        return false;
    }

    /**
     * 是否白名单
     *
     * @param accountUuid
     * @return
     */
    private boolean isWhitelist(String accountUuid) {
        Integer appcode = SoaBaseParams.fromThread().getAppcode();
        Integer cloned = SoaBaseParams.fromThread().getCloned();
        boolean blackList = soaService.isWhitelist(appcode, cloned, accountUuid, "alipay_bind_limit");
        if (BooleanUtils.isTrue(blackList)) {
            log.info("提现绑定白名单用户:{}", accountUuid);
        }
        return blackList;
    }

    /**
     * 更新用户支付账号状态至不可用
     *
     * @param accountUuidSet
     */
    private void updateRewardAccountStatusToInvalid(Set<String> accountUuidSet) {
        if (accountUuidSet.size() > 0) {
            // 处理相同支付宝账号旧账号用户
            log.info("处理相同支付宝账号旧账号用户, list={}", JsonUtils.objectToString(accountUuidSet));
            // 此处直接更新，极端情况可能老用户同时修改了支付宝账号，概率极低，忽略
            // 更新支付宝账号绑定状态
            for (String accountUuid : accountUuidSet) {
                int success = accountsCertificationDao.updateRewardAccountStatus(accountUuid, AccountsCertification.RewardAccountStatus.BIND_INVALID.getValue(), AccountsCertification.RewardAccountStatus.BIND.getValue());
                if (success > 0) {
                    try {
                        Integer cloned = accountsInfoService.getAccountCloned(accountUuid);
                        String mobile = accountsService.getMobileByUuid(accountUuid);
                        // 发消息
                        SystemNoticeService.processSystemNoticeUseTemplate(accountUuid, reward_account_invalid_sysmsg, getRewardAccountInvalidSysMsg(), cloned);
                        // 发短信
                        MessageService.processBusinessSmsUseTemplate(cloned, mobile, reward_account_invalid_notice, rewardAccountInvalidNotice, null);
                    } catch (Exception e) {
                        log.warn("通知发送失败", e);
                    }
                }else {
                    log.warn("处理相同支付宝账号旧账号用户未更新成功，accountUuid={}", JsonUtils.objectToString(accountUuidSet));
                }
            }
        }
    }


    /**
     * 修改支付宝账号
     * @param accountUuid
     * @param account
     * @param vcode
     * @param isAlipayCheck 是否完成支付宝验证
     */
    @Transactional
    public void updateRewardAccountByCode(String accountUuid,String account,String vcode, Boolean isAlipayCheck){
        // 直接从用户中台获取手机号
        String mobile = accountsService.getMobileByUuid(accountUuid);

        if(StringUtils.isEmpty(mobile)){
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.NO_MOBILE);
            throw new ServiceException(CodeStatus.NEED_CHECK_MOBILE);
        }
        if (!verifyRecordService.checkVerify(VerifyRecordService.Code.MODIFY_REWARD_ACCOUNT, mobile, vcode)) {
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.CHECK_VCODE_FAIL);
            throw new ServiceException(CodeStatus.VERIFYCODE_FAILURE);
        }
        updateRewardAccount(accountUuid,account,OPERATOR_NAME.replace("${accountUUid}",accountUuid), isAlipayCheck);
    }

    /**
     * 比V1多了验证
     * @param accountUuid
     * @param account
     * @param vcode
     */
    @Transactional
    public void updateRewardAccountByCodeV2(String accountUuid, String account, String vcode) {
        // 验证该账户是否完成了SDK
        String redisKey = RedisKeyConstant.ACCOUNT_CERTIFICATION_REWARD_ACCOUNT_INIT.setArg(accountUuid);
        List<Object> keys = new ArrayList<>(accountStringRedisTemplate.opsForHash().keys(redisKey));
        if(CollectionUtils.isEmpty(keys)){
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.ALIPAY_SDK_CHECK_TIMEOUT);
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_TIMEOUT);
        }
        List<Object> values = accountStringRedisTemplate.opsForHash().multiGet(redisKey, keys);
        Map<String, String> verifyInfo = new HashMap<>();
        for (int i = 0; i < values.size(); i++) {
            Object val = values.get(i);
            verifyInfo.put(keys.get(i).toString(), val == null ? "" : val.toString());
        }
        String rewardAccount = verifyInfo.get("reward_account");
        String passed = verifyInfo.get("passed");
        if(!Objects.equals(rewardAccount, account) || !Objects.equals(passed, CommConst.YES_1.toString())){
            reportUpdateRewardAccountEvent(accountUuid, WithdrawBindAilipayEvent.Status.FAIL, WithdrawBindAilipayEvent.FailReason.ALIPAY_SDK_CHECK_FAIL);
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_AUTH_FAIL);
        }
        this.updateRewardAccountByCode(accountUuid, account, vcode, true);
    }

    /**
     * 修改支付宝账号服务端埋点
     *
     * @param accountUuid
     * @param status
     * @param failReason
     */
    private void reportUpdateRewardAccountEvent(String accountUuid, WithdrawBindAilipayEvent.Status status, WithdrawBindAilipayEvent.FailReason failReason) {
        WithdrawBindAilipayEvent withdrawBindAilipayEvent = new WithdrawBindAilipayEvent();
        withdrawBindAilipayEvent.setStatus(status.getText());
        if(failReason != null){
            withdrawBindAilipayEvent.setFailReason(failReason.getText());
        }
        String token = SoaBaseParams.fromThread().getToken();
        eventTrackReporter.report(token, accountUuid, EventId.WITHDRAW_BIND_AILIPAY, withdrawBindAilipayEvent);
    }

    /**
     * 获取认证时的图片
     * @param accountUuid
     * @return
     */
    public String getCertificationPhotoUrlByUuid(String accountUuid) {
        String certificationPhotoUrl = accountsCertificationDao.getCertificationPhotoUrlByUuid(accountUuid);
        return StringUtils.isBlank(certificationPhotoUrl) ? "" : certificationPhotoUrl;
    }

    /**
     * 获取认证时的图片
     * @param accountUuid
     * @return
     */
    public String getCertificationPhotoUrlBizByUuid(String accountUuid) {
        String certificationPhotoUrl = accountsChatCertificationDao.getCertificationPhotoUrlByUuid(accountUuid);
        return StringUtils.isBlank(certificationPhotoUrl) ? "" : certificationPhotoUrl;
    }

    /**
     * v2
     *
     * @param certName
     * @param certNo
     * @return
     */
    @Transactional
    public Map<String, String> initCertifyV2(String accountUuid, String certName, String certNo, Integer certType) {
        LOGGER.info("real name certification init order: {}, {}, {}", StringUtils.isBlank(certName), StringUtils.isBlank(certNo), certType);

        Boolean isRealPersonCertification = accountsInfoService.isRealPersonCertification(accountUuid);
        if (!isRealPersonCertification) {
            throw new ServiceException(CodeStatus.CERTIFICATION_NO_REAL_PERSON);
        }

        // 判断次数
        Boolean isLimit = isCertificationLimitOrTimesAdd(accountUuid);
        // 超过当天的检测次数
        if (isLimit) {
            throw new ServiceException(CodeStatus.CERTIFICATION_TIMES_LIMIT);
        }

        // 从业务级实名认证中获取身份证和姓名
        if (certType == BizConst.NOT_NEED_CERTIFICATION_INFO && StringUtils.isBlank(certNo)) {
            AccountsChatCertification chatCertification = accountsChatCertificationDao.getByAccountUuid(accountUuid);
            if (chatCertification == null || chatCertification.getIsChecked() == null || chatCertification.getIsChecked() != BizConst.CHAT_CERTIFICATION) {
                LOGGER.warn("获取业务级实名认证为空, 麻烦确定用户是否是后台直接改状态!");
                throw new ServiceException(CodeStatus.BIZ_NO_NO_EXISTS);
            }
            if(ToolsService.accountsChatCertificationSwitchEncryption.isOn(true)) {
                certName = EncryptUtil.decrypt(chatCertification.getRealNameCipher());
                certNo = EncryptUtil.decrypt(chatCertification.getIdentityNoCipher());
            }else {
                certName = chatCertification.getRealName();
                certNo = chatCertification.getIdentityNo();
            }
        }

        //校验身份证号
        if (!isIdentityNo(certNo, CertificationIdentityTypeEnum.IDENTITY.getValue())) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        if (certNo.length() != 18) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        // 校验身份年龄是否满
        if (!ifGrownUp(certNo)) {
            throw new ServiceException(CodeStatus.IDENTITY_NONAGE);
        }
        // 2021.06.25 处理少数民族名字，腾讯云接收半角点
        certName = certName.replaceAll("•", "·");

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        //验证
        AccountsCertification ac = accountsCertificationDao.getByAccountUuid(accountUuid);
        Long certificationId = (ac == null || ac.getId() == null) ? null : Long.valueOf(ac.getId().toString());
        Integer certificationChecked = (ac == null || ac.getIsChecked() == null) ? null : Integer.valueOf(ac.getIsChecked().toString());

        if (certificationId != null && Objects.equals(certificationChecked, 1)) {
            throw new ServiceException(CodeStatus.ACCOUNT_ALREADY_CERTIFICATION);
        }

        LOGGER.info("查询到认证信息，json={}", JsonUtils.objectToString(ac));

        if (getCheckedIdentityNoCount(certNo, cloned) > 0) {//身份证已绑定
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION);
        }

        // 判断身份证是否被其他账号的业务级认证绑定了
        if (accountsChatCertificationManager.countIdentityInUse(cloned, certNo, accountUuid) > 0) {
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION_IN_CHAT);
        }

        //新的认证方式
        Map<String, String> certResultMap = tengxunyunInitCert(accountUuid, certNo, certName, cloned, platformId, env, channel, KafkaManufacturerCallDto.BelongBiz.APP_CERTIFICATION);

        //保存至数据库并更新缓存
        Map<String, Object> keyValueMap = Maps.newHashMap();
        keyValueMap.put("real_name", certName);
        keyValueMap.put("identity_no", certNo);
        keyValueMap.put("identity_no_birth", getBirthByIdentityNo(certNo));
        keyValueMap.put("biz_no", certResultMap.get("orderno"));
        keyValueMap.put("reward_account", "");
        keyValueMap.put("is_checked", CommConst.NO_0);
        keyValueMap.put("reward_account_checked", CommConst.NO_0);
        keyValueMap.put("reward_account_status", AccountsCertification.RewardAccountStatus.DEFAULT.getValue());
        keyValueMap.put("identity_type", 1);

        // 处理加密字段
        keyValueMap = encryptCertInfo(keyValueMap, ToolsService.accountsCertificationSwitchWrite);
        if (certificationId == null) {
            keyValueMap.put("create_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("account_uuid", accountUuid);
            accountsCertificationDao.addKeyValue(keyValueMap, accountUuid);
        } else {
            keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
            accountsCertificationDao.updateById(keyValueMap, certificationId, accountUuid);
        }

        return certResultMap;
    }

    /**
     * 业务级实名认证
     * 原来方法名 initCertifyForChat
     *
     * @param accountUuid
     * @param certName
     * @param certNo
     * @param source
     * @return
     */
    @Transactional
    public Map<String, String> initCertifyForBiz(String accountUuid, String certName, String certNo, Integer source) {
        // 判断次数
        Boolean isLimit = isCertificationLimitOrTimesAddForChat(accountUuid);
        // 超过当天的检测次数
        if (isLimit) {
            throw new ServiceException(CodeStatus.CERTIFICATION_TIMES_LIMIT);
        }

        //校验身份证号
        if (!isIdentityNo(certNo, CertificationIdentityTypeEnum.IDENTITY.getValue())) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        if (certNo.length() != 18) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        // 校验身份年龄是否满18
        if (!ifGrownUp(certNo)) {
//            throw new ServiceException(CodeStatus.IDENTITY_NONAGE);
            throw new ServiceException(CodeStatus.CHAT_CERTIFICATION_ADULT_VERIFY_ERROR);
        }
        int age = IdcardUtil.getAgeByIdCard(certNo);
        // 校验身份年龄是否大于60
        if (age >= CareModelConfig.LIMIT_AGE) {
            throw new ServiceException(CodeStatus.CHAT_CERTIFICATION_AGED_VERIFY_ERROR);
        }

        // 2021.06.25 处理少数民族名字，腾讯云接收半角点
        certName = certName.replaceAll("•", "·");

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();
        Integer platformId = sbp.getPlatform_id();
        String env = sbp.getEnv();
        String channel = sbp.getChannel();

        //验证
        AccountsChatCertification ac = accountsChatCertificationDao.getByAccountUuid(accountUuid);
        Long certificationId = (ac == null || ac.getId() == null) ? null : Long.valueOf(ac.getId().toString());
        Integer certificationChecked = (ac == null || ac.getIsChecked() == null) ? null : Integer.valueOf(ac.getIsChecked().toString());

        if (certificationId != null && certificationChecked != null && certificationChecked == 1) {
            throw new ServiceException(CodeStatus.ACCOUNT_ALREADY_CERTIFICATION);
        }

        LOGGER.info("查询到业务级实名认证信息，json={}",JsonUtils.objectToString(ac));

        if (getCheckedIdentityNoCount(certNo, cloned) > 0) {//身份证在 全局真人实名 已绑定
            LOGGER.warn("the input id_card had already bind in global certification");
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION);
        }

        if (getCheckedIdentityNoCountForChat(cloned, certNo) > 0) { // 身份证在 业务级实名 已绑定
            LOGGER.warn("the input id_card had already bind in chat room certification");
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION);
        }

        //新的认证方式
        Map<String,String> certResultMap = tengxunyunInitCert(accountUuid, certNo, certName, cloned, platformId, env, channel, KafkaManufacturerCallDto.BelongBiz.CHAT_CERTIFICATION);

        //保存至数据库并更新缓存
        Map<String, Object> keyValueMap = Maps.newHashMap();
        keyValueMap.put("real_name", certName);
        keyValueMap.put("identity_no", certNo);
        keyValueMap.put("identity_no_birth", getBirthByIdentityNo(certNo));
        keyValueMap.put("biz_no", certResultMap.get("orderno"));
        keyValueMap.put("reward_account", "");
        keyValueMap.put("is_checked", 0);
        keyValueMap.put("reward_account_checked", CommConst.NO_0);
        keyValueMap.put("identity_type", 1);
        encryptCertInfo(keyValueMap, ToolsService.accountsChatCertificationSwitchWrite);
        if (certificationId == null) {
            keyValueMap.put("create_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("account_uuid", accountUuid);
            keyValueMap.put("source", source);
            accountsChatCertificationDao.addKeyValue(keyValueMap);
        } else {
            keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("source", source);
            accountsChatCertificationDao.updateById(keyValueMap, certificationId);
        }

        return certResultMap;
    }

    public void syncCertifyV2(String orderno) {
        Object[] accountCertification = getDecryptInfoByBizNo(orderno);
        Long certificationId = accountCertification == null || accountCertification[0] == null ? null : Long.valueOf(accountCertification[0].toString());
        if (certificationId == null) {
            throw new ServiceException(CodeStatus.BIZ_NO_NO_EXISTS);
        }
        String accountUuid = accountCertification[1] == null ? "" : accountCertification[1].toString();
        String realName = accountCertification[2] == null ? "" : accountCertification[2].toString();
        String identityNo = accountCertification[3] == null ? "" : accountCertification[3].toString();
        Integer isChecked = accountCertification[4] == null ? null : Integer.valueOf(accountCertification[4].toString());
        if (isChecked == 1) {
            // 用户已认证过 直接返回成功
            LOGGER.warn("已认证用户，调用了实名认证。accountUuid={}", accountUuid);
            return;
        }

        try {
            //新的查询腾讯云实人认证结果调用方式
            TxSyncResponse txSyncResponse = TengxuyunQueryCert(orderno, accountUuid, KafkaManufacturerCallDto.BelongBiz.APP_CERTIFICATION);

            boolean passed = txSyncResponse.isSuccess();
            // 图片文件名
            String imgName = "";

            if(passed ){
                // 2023.11.20 从腾讯云返回结果中获取图片
                String photo = txSyncResponse.getResult().getPhoto();
                LOGGER.info("syncCertifyV2 获取底图");
                PictureInfoVo uploadFile = qiniuService.uploadFileCertification(Encodes.decodeBase64(photo));
                // 2024.07.12 不带域名的文件名
                imgName = uploadFile.getImg_name();
            }

            // 审核通过 再校验一次阿里云 判断用户是否同一个人
            // 触发条件 腾讯云审核通过，开启双验证，图片不为空
            if(passed && DOUBLE_CHECK && StringUtils.isNotBlank(imgName)){
                // 判断是否已经真人认证
                Object rpcObj = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "face_certification");
                boolean isCertification = (null != rpcObj && Objects.equals(CommonEnableStatus.ENABLE.getStatus(), Integer.parseInt(rpcObj + "")));
                if(isCertification){
                    // 获取图片
                    AliyunLiveFaceDetect faceDetect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
                    // 带域名，但是不可访问的地址
                    String pic1 = ToolsService.addAccountPrivateUrlPreDomain(null == faceDetect ? "" : faceDetect.getBase_photo_url());
                    // 带域名，但是不可访问的地址
                    String pic2 = ToolsService.addAccountPrivateUrlPreDomain(imgName);
                    LOGGER.info("faceDetect=[{}]真人与实名对比图，pic1=[{}],pic2=[{}]", null == faceDetect? "null" : faceDetect.getId(), pic1, pic2);
                    if(null != faceDetect && StringUtils.isNotBlank(pic1) && StringUtils.isNotBlank(pic2)){
                        // 2024.07.04 对比前底图获取是有图片地址去比较
                        // 活体底图，可访问
                        String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
                        // 实名底图，可访问
                        String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);
                        CompareFaceDto facePicPCompare = aliyunLiveFaceDetectService.comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.CERTIFICATION);
                        // 保存到日志
                        passed = facePicPCompare.isSimilarity();
                        photoCompareLogService.addLog(accountUuid, pic1, pic2, passed ? 1:0, facePicPCompare.getScore(), facePicPCompare.getMsg(), PhotoCompareTypeEnum.CERTIFICATION.getValue());

                        // 记录认证日志
                        userCertificationLogService.logRealName(passed, accountUuid, pic1, pic2, facePicPCompare.getScore(), identityNo);

                        if (!passed) {
                            // 推送小秘书
                            pushRealNameFailInconsistentSystemMsg(accountUuid);
                            throw new ServiceException("certify_fail_inconsistent", StringUtils.defaultString(CodeStatus.CERTIFY_FAIL_INCONSISTENT.getReasonPhrase()));
                        }
                    }

                }
            }

            if (!passed) {
                // 推送小秘书
                pushRealNameFailSystemMsg(accountUuid);
                throw new ServiceException("certify_fail", StringUtils.defaultString(CodeStatus.CERTIFY_FAIL.getReasonPhrase()));
            }

            //认证成功
            isChecked = 1;
            Map<String, Object> keyValueMap = new HashMap<>(4);
            keyValueMap.put("is_checked", isChecked);
            keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("identity_no_status", CommConst.NO_0);
            keyValueMap.put("certification_photo_url", imgName);
            final String img = imgName;
            final Integer isc = isChecked;
            TransactionWrapper.me().wrap(() -> {
                accountsCertificationDao.updateById(keyValueMap, certificationId,accountUuid);
                accountsPersonalInfoService.setZhimaCertification(accountUuid, isc);
                accountsCertificationHistoryService.saveOrUpdate(accountUuid, CertificationIdentityTypeEnum.IDENTITY.getValue(), CommConst.NO_0, accountUuid, img);
            });
            this.saveToCacheIdentityNoBirth(accountUuid, identityNo, isChecked);
            certifySuccessPushMq(accountUuid, identityNo, realName, imgName);
            // 加入关怀模式
            addCareModel(accountUuid);
        } catch (Exception e) {
            if(CodeStatus.CERTIFY_FAIL.getReasonPhrase().equals(e.getMessage())){
                LOGGER.warn("腾讯云认证不通过,orderno：{}",orderno);
            } else if(CodeStatus.CERTIFY_FAIL_INCONSISTENT.getReasonPhrase().equals(e.getMessage())){
                LOGGER.warn("实名认证与真人认证不一致；orderno：{}, account_uuid", orderno, accountUuid);
                throw new ServiceException(CodeStatus.CERTIFY_FAIL_INCONSISTENT);
            } else {
                LOGGER.error("调用腾讯云认证查询接口异常；orderno：{}", orderno, e);
            }
            throw new ServiceException(CodeStatus.CERTIFY_FAIL);
        }
    }

    /**
     * 加入关怀模式
     *
     * @param accountUuid
     */
    private void addCareModel(String accountUuid) {
        // >= 60岁 && 大陆公民（由上文了解到这个方法值开给大陆公民） 要进入关怀模式
        Integer realAge = accountBaseInfoManager.getRealAge(accountUuid);
        SoaBaseParams params = SoaBaseParams.fromThread();
        Integer cloned = params.getCloned();
        if (ClonedGroupEnum.MAIN_GROUP.contains(cloned)) {
            if (!VersionControlConfig.isPass(VersionControlConfig.Scene.WITHDRAW_LIMIT_60, params.getPlatform_id(), params.getApp_version())) {
                LogUtil.info4Gray("版本过低,scene={},version={}", VersionControlConfig.Scene.WITHDRAW_LIMIT_60.name(), params.getApp_version());
                return;
            }
        } else if (ClonedGroupEnum.GIRL_GROUP.contains(cloned)) {
            if (!VersionControlConfig.isPass(VersionControlConfig.Scene.GIRL_GROUP_WITHDRAW_LIMIT_60, params.getPlatform_id(), params.getApp_version())) {
                LogUtil.info4Gray("版本过低,scene={},version={}", VersionControlConfig.Scene.GIRL_GROUP_WITHDRAW_LIMIT_60.name(), params.getApp_version());
                return;
            }
        }

        if (realAge >= CareModelConfig.LIMIT_AGE) {
            careModelService.addCareList(accountUuid);
        }
    }

    /**
     * 业务级实名认证
     * 原方法名 syncCertifyForChat
     *
     * @param orderno
     * @param source 来源
     */
    @Transactional
    public void syncCertifyForBiz(String orderno, Integer source) {
        // check status of account chat real certification, if status had checked, return this request.
        Object[] accountCertification = getDecryptInfoByBizNoForChat(orderno);
        Long certificationId = accountCertification == null || accountCertification[0] == null ? null : Long.valueOf(accountCertification[0].toString());
        if (certificationId == null) {
            throw new ServiceException(CodeStatus.BIZ_NO_NO_EXISTS);
        }
        String accountUuid = accountCertification[1] == null ? "" : accountCertification[1].toString();
        String realName = accountCertification[2] == null ? "" : accountCertification[2].toString();
        String identityNo = accountCertification[3] == null ? "" : accountCertification[3].toString();
        Integer isChecked = accountCertification[4] == null ? null : Integer.valueOf(accountCertification[4].toString());
        if (isChecked == 1) {
            // 用户已认证过 直接返回成功
            LOGGER.warn("已业务级认证用户，调用了业务级实名认证。accountUuid={}", accountUuid);
            return;
        }

        try {
            // 是否记录日志
            boolean logFlag = false;
            String imgName = "";

            //新的查询腾讯云实人认证结果调用方式
            TxSyncResponse txSyncResponse = TengxuyunQueryCert(orderno, accountUuid, KafkaManufacturerCallDto.BelongBiz.CHAT_CERTIFICATION);

            boolean passed = txSyncResponse.isSuccess();

            if(passed){
                // 2023.11.20 从腾讯云返回结果中获取图片
                String photo = txSyncResponse.getResult().getPhoto();
                LOGGER.info("syncCertifyForChat 获取底图");
                PictureInfoVo uploadFile = qiniuService.uploadFileCertification(Encodes.decodeBase64(photo));
                imgName = uploadFile.getImg_name();
            }

            // 真人认证底图
            String pic1 ="";
            // 实名底图
            String pic2 = ToolsService.addAccountPrivateUrlPreDomain(imgName);
            // 对比分数
            Float score = 0F;

            // 审核通过 再校验一次阿里云 判断用户是否同一个人
            // 触发条件 腾讯云审核通过，开启双验证，图片不为空
            if(passed && StringUtils.isNotBlank(imgName)){
                // 判断是否已经真人认证
                Object rpcObj = accountStringRedisTemplate.opsForHash().get(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid), "face_certification");
                boolean isCertification = (null != rpcObj && Objects.equals(CommonEnableStatus.ENABLE.getStatus(), Integer.parseInt(rpcObj + "")));
                if (isCertification) {
                    // 获取图片
                    AliyunLiveFaceDetect faceDetect = aliyunLiveFaceDetectDao.getInfoByAccountUuid(accountUuid);
                    pic1 = ToolsService.addAccountPrivateUrlPreDomain(null == faceDetect ? "" : faceDetect.getBase_photo_url());
                    LOGGER.info("faceDetect=[{}]真人与业务级实名对比图，pic1=[{}],pic2=[{}]", null == faceDetect ? "null" : faceDetect.getId(), pic1, pic2);
                    if (null != faceDetect && StringUtils.isNotBlank(pic1) && StringUtils.isNotBlank(pic2)) {
                        // 2024.07.04 对比前底图获取是有图片地址去比较
                        // 活体底图
                        String privateBasePic1 = ToolsService.getPrivateBasePic(pic1);
                        // 实名底图
                        String privateBasePic2 = ToolsService.getPrivateBasePic(pic2);
                        CompareFaceDto facePicPCompare = aliyunLiveFaceDetectService.comparePic(privateBasePic1, privateBasePic2, accountUuid, RiskCertificationTypeEnum.BUSINESS_CERTIFICATION);
                        passed = facePicPCompare.isSimilarity();
                        score = facePicPCompare.getScore();

                        // 保存到日志
                        photoCompareLogService.addLogForChat(accountUuid, pic1, pic2, passed ? 1 : 0, score, facePicPCompare.getMsg(), PhotoCompareTypeEnum.CERTIFICATION.getValue(), source);
                        logFlag = true;

                        if (!passed) {
                            // 推送小秘书
                            pushRealNameFailInconsistentSystemMsg(accountUuid);
                            throw new ServiceException("certify_fail_inconsistent", StringUtils.defaultString(CodeStatus.CERTIFY_FAIL_INCONSISTENT.getReasonPhrase()));
                        }
                    }
                }
            }

            LOGGER.info("biz real certification result - uuid: {}, imgName: {}, log: {}, pass: {}", accountUuid, imgName, logFlag, passed);

            //不管认证成功或失败都记录日志，用于后台查询
            if (!logFlag) {
                photoCompareLogService.addLogForChat(accountUuid, "", imgName, passed ? 1 : 0, score, "", PhotoCompareTypeEnum.CERTIFICATION.getValue(), source);
            }

            if (!passed) {
                throw new ServiceException("certify_fail", StringUtils.defaultString(CodeStatus.CERTIFY_FAIL.getReasonPhrase()));
            }

            // 记录日志给风控
            userCertificationLogService.logRealNameBiz(passed, accountUuid, pic1, pic2, score, identityNo);

            //认证成功
            isChecked = 1;
            Map<String, Object> keyValueMap = new HashMap<>(4);
            keyValueMap.put("is_checked", isChecked);
            keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("identity_no_status", CommConst.YES_1);
            keyValueMap.put("certification_photo_url", imgName);
            accountsChatCertificationDao.updateById(keyValueMap, certificationId);
            accountsPersonalInfoService.setChatRealCertification(accountUuid, isChecked);

            certifySuccessPushMqForChat(accountUuid, identityNo, realName);
            LOGGER.info("account uuid: {} finish biz real certification.", accountUuid);

        } catch (Exception e) {
            if(CodeStatus.CERTIFY_FAIL.getReasonPhrase().equals(e.getMessage())){
                LOGGER.warn("腾讯云认证不通过,orderno：{}",orderno);
            } else if(CodeStatus.CERTIFY_FAIL_INCONSISTENT.getReasonPhrase().equals(e.getMessage())){
                LOGGER.warn("业务级实名认证与真人认证不一致；orderno：{}, account_uuid: {}", orderno, accountUuid);
                throw new ServiceException(CodeStatus.CERTIFY_FAIL_INCONSISTENT);
            } else {
                LOGGER.error("调用腾讯云认证查询接口异常；orderno：{}", orderno, e);
            }
            throw new ServiceException(CodeStatus.CERTIFY_FAIL);
        }
    }

    /**
     * 实名认证后，推入埋点
     *
     * @param accountUuid
     * @param identityNo
     * @param realName
     * @param imgName
     */
    private void certifySuccessPushMq(String accountUuid, String identityNo, String realName, String imgName){
        // 推送用户修改记录
        Integer whiteList = CommConst.NO_0;
        BuryService.pushToCertificationLog(accountUuid, realName, identityNo, AccountsCertificationChangeLog.LogTypeEnum.BIND.getValue(), AccountsCertificationChangeLog.OperatorTypeEnum.USER.getValue(), accountUuid, whiteList, imgName);
        //埋点，用于处理认证成功后的同步、认证奖励相关
        BuryService.pushDataToHandleZhimaAuthDealQueue(accountUuid);
        //同时推送一条社区行为mq
        modifyGrowScoreService.sendModifyGrowScoreMq(accountUuid, ModifyGrowScoreEnum.ALI_CERTIFICATION);
        // 2022.01.13 实名认证推风控
        BuryService.toAntispamTagRealName(accountUuid, Md5Util.encode(identityNo), Md5Util.encode(realName));
        buryService.toBbsFinishIncentiveTask(accountUuid, FinishIncentiveTaskEnum.REAL_NAME.getType());

        BuryService.pushToAccountLivingCert(accountUuid, CertTypeEnum.REAL_NAME);

        EventTrackingDO tracking = EventTrackingDO.create(accountUuid, AccountEvent.REAL_NAME_CERT);
        kafkaSinkUtil.push(EventConst.EVENT_TRACKING_TOPIC, tracking);
        behaviorReporter.report(accountUuid, tracking);
        SpringContextHolder.getBean(AllureService.class).refreshAllureV2(accountUuid, AllureSceneEnum.CARD, null);
    }


    /**
     * 实名认证后，推入埋点
     *
     * @param accountUuid
     * @param identityNo
     * @param realName
     */
    private void certifySuccessPushMqForChat(String accountUuid, String identityNo, String realName){
        // 2022.01.13 实名认证推风控
        BuryService.toAntispamTagRealName(accountUuid, Md5Util.encode(identityNo), Md5Util.encode(realName));
    }

    /**
     * 初始化 预咨询接口
     * @param accountUuid
     * @param rewardAccount
     * @return
     */
    public Map<String, String> initCertverifyRewardAccount(String accountUuid, String rewardAccount) {
        Map<String, String> result = new HashMap<>();

        String redisKey = RedisKeyConstant.ACCOUNT_CERTIFICATION_REWARD_ACCOUNT_INIT.setArg(accountUuid);
        Boolean exist = accountStringRedisTemplate.hasKey(redisKey);
        if(exist){
            accountStringRedisTemplate.delete(redisKey);
        }

        AccountsCertification certification = accountsCertificationDao.getByAccountUuid(accountUuid);
        if(certification == null || !Objects.equals(certification.getIsChecked(), CommConst.YES_1)){
            throw new ServiceException(CodeStatus.NO_CERTIFICATION);
        }
        if(StringUtils.isBlank(rewardAccount)){
            if(ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
                rewardAccount = EncryptUtil.decrypt(certification.getRewardAccountCipher());
            }else {
                rewardAccount = certification.getRewardAccount();
            }
        }

        String realName = "";
        String identityNo = "";
        if(ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
            realName = EncryptUtil.decrypt(certification.getRealNameCipher());
            identityNo = EncryptUtil.decrypt(certification.getIdentityNoCipher());
        }else {
            realName = certification.getRealName();
            identityNo = certification.getIdentityNo();
        }

        Map<String, String> consltResult = this.certverifyPreconsult(accountUuid, realName, identityNo, "", rewardAccount);
        String verifyId = MapUtils.getString(consltResult, "verify_id", "");
        if(StringUtils.isBlank(verifyId)){
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_BUSY);
        }
        result.put("verify_id", verifyId);
        // state 防止重放攻击
        String state = Base64.getEncoder().encodeToString((accountUuid + System.currentTimeMillis()).getBytes());
        String alipayUrl = String.format(CommConst.ALIPAY_ID_VERIFY_URL_TEMPLATE, ALIPAY_DEFAULT_CLIENT_APPID, verifyId, state);
        result.put("alipay_url", alipayUrl);
        // 预检测结果放到缓存
        Map<String, String> cache = new HashMap<>();
        cache.put("account_uuid", accountUuid);
        cache.put("real_name", realName);
        cache.put("identity_no", identityNo);
        cache.put("reward_account", rewardAccount);
        cache.put("verify_id", verifyId);
        cache.put("passed", CommConst.NO_0.toString());

        accountStringRedisTemplate.opsForHash().putAll(redisKey, cache);
        accountStringRedisTemplate.expire(redisKey, 10L, TimeUnit.MINUTES);

        return result;
    }

    /**
     * 预咨询接口
     * @param userName
     * @param certNo
     * @param mobile
     * @param logonId
     * @return
     */
    private Map<String, String> certverifyPreconsult(String accountUuid, String userName, String certNo, String mobile, String logonId) {
        Map<String, String> result = new HashMap<>();
        Map<String, String> bizContent = new HashMap<>();
        bizContent.put("user_name", userName);
        bizContent.put("cert_type", "IDENTITY_CARD");
        bizContent.put("cert_no", certNo);
        bizContent.put("mobile", mobile);
        bizContent.put("logon_id", logonId);
        bizContent.put("ext_info", "{}");
        AlipayUserCertdocCertverifyPreconsultRequest request = new AlipayUserCertdocCertverifyPreconsultRequest();
        request.setBizContent(JSON.toJSONString(bizContent));
        try {
            AlipayUserCertdocCertverifyPreconsultResponse response = alipayDefaultClient.execute(request);
            if(Objects.equals(response.getCode(), AlipayConst.GATEWAY_SUCCESS_CODE)){
                LOGGER.info("支付宝实名验证预咨询成功uuid={}.verifyId={}", accountUuid, response.getVerifyId());
                result.put("verify_id", response.getVerifyId());
            }else{
                LOGGER.warn("支付宝实名验证预咨询失败uuid={}.code={}.message={}", accountUuid, response.getCode(), response.getMsg());
                MonitorService.incCounterMetrices(PrometheusMetricsEnum.ALIPAY_REWARD_ACCOUNT_CERT_PRE_COUNTER);
                throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_BUSY);
            }
        } catch (AlipayApiException e) {
            String errCode = e.getErrCode();
            String message = e.getMessage();
            LOGGER.warn("支付宝实名验证.预咨询失败.uuid={}.code={}.message={}", accountUuid, errCode, message);
            MonitorService.incCounterMetrices(PrometheusMetricsEnum.ALIPAY_REWARD_ACCOUNT_CERT_PRE_FAIL_COUNTER);
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_BUSY);
        }
        return result;
    }

    /**
     * 获取支付宝验证结果
     * @param verifyId
     * @param verifyInfo
     * @param accessToken
     * @return
     */
    private CertverifyConsultDto certverifyConsult(String accountUuid, String verifyId, Map<String, String> verifyInfo, String accessToken){
        if(StringUtils.isBlank(verifyId)){
            LOGGER.warn("支付宝实名验证.参数有误.verifyId=空.verifyInfo={}", verifyInfo == null ? "null" : JSON.toJSONString(verifyInfo));
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_BUSY);
        }
        CertverifyConsultDto certverifyConsultDto = null;
        Map<String, String> bizContent = new HashMap<>();
        bizContent.put("verify_id", verifyId);
        AlipayUserCertdocCertverifyConsultRequest request = new AlipayUserCertdocCertverifyConsultRequest();
        request.setBizContent(JSON.toJSONString(bizContent));
        AlipayUserCertdocCertverifyConsultResponse response = null;
        try {
            response = alipayDefaultClient.execute(request,accessToken);
            LOGGER.info("支付宝实名验证.返回结果.data={}", JSON.toJSONString(response));
            if(Objects.equals(response.getCode(), AlipayConst.GATEWAY_SUCCESS_CODE)){
                if(Objects.equals(response.getPassed(), "T")){
                    // 通过
                    certverifyConsultDto = new CertverifyConsultDto().setPassed(true).setVerifyId(verifyId).setVerifyInfo(MapUtils.isEmpty(verifyInfo) ? "" : JSON.toJSONString(verifyInfo))
                            .setRespnseCode(response.getCode()).setResponseMsg(response.getMsg()).setResponseStr(JSON.toJSONString(response));
                }else{
                    // 拒绝
                    certverifyConsultDto = new CertverifyConsultDto().setPassed(false).setVerifyId(verifyId).setVerifyInfo(MapUtils.isEmpty(verifyInfo) ? "" : JSON.toJSONString(verifyInfo))
                            .setRespnseCode(response.getFailReason()).setResponseMsg(response.getFailParams()).setResponseStr(JSON.toJSONString(response));
                }
                LOGGER.info("支付宝实名验证咨询成功uuid={}.verifyId={}.passed={}", accountUuid, verifyId, response.getPassed());
                MonitorService.incCounterMetrices(PrometheusMetricsEnum.ALIPAY_REWARD_ACCOUNT_CERT_COUNTER);
            }else{
                LOGGER.warn("支付宝实名验证咨询失败uuid={}.code={}.message={}", accountUuid, response.getCode(), response.getMsg());
                MonitorService.incCounterMetrices(PrometheusMetricsEnum.ALIPAY_REWARD_ACCOUNT_CERT_FAIL_COUNTER);
                throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_BUSY);
            }
        } catch (AlipayApiException e) {
            String errCode = e.getErrCode();
            String message = e.getMessage();
            LOGGER.warn("支付宝实名验证.咨询失败.uuid={}.code={}.message={}", accountUuid, errCode, message);
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_BUSY);
        }
        return certverifyConsultDto;
    }

    /**
     * 验证支付宝实名验证结果
     * @param accountUuid
     * @param rewardAccount
     * @param alipayAuthCode
     * @return
     */
    public Map<String, String> checkCertverifyRewardAccount(String accountUuid, String rewardAccount, String alipayAuthCode) {
        AlipaySystemOAuthTokenDTO systemOAuthToken = getCertverifyConsultToken(alipayAuthCode);
        if(systemOAuthToken == null){
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_AUTH_FAIL);
        }
        // 换取授权码
        String accessToken = systemOAuthToken.getAccessToken();
        if(StringUtils.isBlank(accessToken)){
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_AUTH_FAIL);
        }
        // 2024.08.08 以下注释，应该不需要打开，业务流程有优化
        // 咨询结果
        Map<String, String> syncResult = syncCertverifyRewardAccount(accountUuid, accessToken);
        String passed = MapUtils.getString(syncResult, "passed", "0");
        if(!Objects.equals(passed, "1")){
            // 你填写的支付宝账号与实名人不一致，请重新输入
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_RESULT_F_EMPTY);
        }
        return syncResult;
    }

    /**
     * 换取授权访问令牌
     * @param code
     * @return
     */
    private AlipaySystemOAuthTokenDTO getCertverifyConsultToken(String code) {
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setGrantType("authorization_code");
        request.setCode(code);
        request.setRefreshToken("");
        AlipaySystemOauthTokenResponse response = null;
        AlipaySystemOAuthTokenDTO alipaySystemOAuthToken = null;
        try {
            response = alipayDefaultClient.execute(request);
            alipaySystemOAuthToken = new AlipaySystemOAuthTokenDTO().setAccessToken(response.getAccessToken()).setAuthTokenType(response.getAuthTokenType()).setRefreshToken(response.getRefreshToken())
                    .setAlipayUserId(response.getAlipayUserId()).setUserId(response.getUserId()).setExpiresIn(response.getExpiresIn()).setReExpiresIn(response.getReExpiresIn());
            LOGGER.info("支付宝实名验证.换取授权访问令牌成功.code={}.response={}", code, JSON.toJSONString(response));
            return alipaySystemOAuthToken;
        } catch (AlipayApiException e) {
            String errCode = e.getErrCode();
            String message = e.getMessage();
            LOGGER.warn("支付宝实名验证.换取授权访问令牌失败.code={}.message={}", errCode, message);
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_AUTH_FAIL);
        }
    }

    /**
     * 咨询接口
     * @param accountUuid
     * @return
     */
    public Map<String, String> syncCertverifyRewardAccount(String accountUuid, String accessToken) {
        if(StringUtils.isBlank(accessToken)){
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_AUTH_FAIL);
        }
        Map<String, String> result = new HashMap<>();
        String redisKey = RedisKeyConstant.ACCOUNT_CERTIFICATION_REWARD_ACCOUNT_INIT.setArg(accountUuid);
        Boolean exist = accountStringRedisTemplate.hasKey(redisKey);
        if(!exist){
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_TIMEOUT);
        }
        List<Object> keys = new ArrayList<>(accountStringRedisTemplate.opsForHash().keys(redisKey));
        List<Object> values = accountStringRedisTemplate.opsForHash().multiGet(redisKey, keys);
        Map<String, String> verifyInfo = new HashMap<>();
        for (int i = 0; i < values.size(); i++) {
            Object val = values.get(i);
            verifyInfo.put(keys.get(i).toString(), val == null ? "" : val.toString());
        }
        String verifyId = verifyInfo.get("verify_id");
        CertverifyConsultDto certverifyConsult = certverifyConsult(accountUuid, verifyId, verifyInfo, accessToken);
        if(certverifyConsult == null){
            LOGGER.info("支付宝实名验证.失败.uuid={}.verifyId={}.verifyInfo={}", accountUuid, verifyInfo, JSON.toJSONString(verifyInfo));
            throw new ServiceException(CodeStatus.CERTVERIFY_CONSULT_BUSY);
        }
        Boolean pass = certverifyConsult.getPassed();
        result.put("passed", pass ? "1" : "0");
        accountStringRedisTemplate.opsForHash().putAll(redisKey, result);
        return result;
    }

    /**
     * 取消提现账号
     * @param accountUuid
     */
    @Transactional
    public Boolean cancelRewardAccount(String accountUuid, Integer certWhiteList, String operator) {
        AccountsCertification certification = accountsCertificationDao.getByAccountUuid(accountUuid);
        if(certification == null){
            LOGGER.warn("解除提现账号失败.未查询到实名信息.uuid={}.", accountUuid);
            return false;
        }
        String rewardAccount = certification.getRewardAccount();
        if(ToolsService.rewardAccountSwitchEncryption.isOn(true)) {
            rewardAccount = EncryptUtil.decrypt(certification.getRewardAccountCipher());
        }

        if(StringUtils.isBlank(rewardAccount)){
            LOGGER.warn("解除提现账号失败.未查询到绑定账号信息.uuid={}.", accountUuid);
            return false;
        }
        Map<String, Object> values = new HashMap<>();
        values.put("reward_account", "");
        values.put("reward_account_checked", CommConst.NO_0);
        values.put("reward_account_status", AccountsCertification.RewardAccountStatus.DEFAULT.getValue());
        values.put("reward_account_digest", "");
        values.put("reward_account_cipher", "");
        accountsCertificationDao.updateById(values, certification.getId(),accountUuid);
        // 推送日志
        String remark = TemplateStringConst.getContent(TemplateStringConst.CERT_LOG_REMARK_REWARD_ACCOUNT_CANCEL, accountUuid, DateUtil.dateToString20(new Date()), rewardAccount, certification.getRewardAccountChecked());
        BuryService.pushToCertLog(accountUuid, certWhiteList, CertTypeEnum.REWARD_ACCOUNT.getValue(), operator, AccountsCertLog.OperatorTypeEnum.CANCEL.getValue(), certification.getCertificationPhotoUrl(), remark);
        return true;
    }

    public Map<String, String> checkMyCertverifyRewardAccount(String accountUuid, String alipayAuthCode) {
        AccountsCertification certification = accountsCertificationDao.getByAccountUuid(accountUuid);
        if(certification == null ){
            throw new ServiceException(CodeStatus.NO_CERTIFICATION);
        }

        String rewardAccount = certification.getRewardAccount();
        if(ToolsService.rewardAccountSwitchEncryption.isOn(true)) {
            rewardAccount = EncryptUtil.decrypt(certification.getRewardAccountCipher());
        }

        if(StringUtils.isBlank(rewardAccount)){
            throw new ServiceException(CodeStatus.NO_REWARD_ACCOUNT);
        }

        Map<String, String> result = this.checkCertverifyRewardAccount(accountUuid, rewardAccount, alipayAuthCode);
        String passed = MapUtils.getString(result, "passed", "0");
        if(Objects.equals(passed, "1")){
            accountsCertificationDao.updateRewardAccountChecked(CommConst.YES_1, accountUuid);
            // 添加日志
            try{
                String remark = TemplateStringConst.getContent(TemplateStringConst.CERT_LOG_REMARK_REWARD_ACCOUNT_CHECKED, accountUuid, DateUtil.dateToString20(new Date()), rewardAccount);
                BuryService.pushToCertLog(accountUuid, CommConst.NO_0, CertTypeEnum.REWARD_ACCOUNT.getValue(), accountUuid, AccountsCertLog.OperatorTypeEnum.BIND.getValue(), getCertificationPhotoUrlByUuid(accountUuid), remark);
            } catch (Exception e) {
                LOGGER.warn("验证账号成功.日志新年增失败.uuid={}", accountUuid, e);
            }
        }
        return result;

    }

    /**
     * 身份认证
     *
     * @param uuid
     * @param realName
     * @param identityNo
     * @return
     */
    public Boolean validRealName(String uuid, String realName, String identityNo) {
        if (StringUtils.isBlank(uuid) || StringUtils.isBlank(realName) || StringUtils.isBlank(identityNo)) {
            log.warn("请求数据异常:{}, {}, {}", uuid, realName, identityNo);
            return false;
        }

        if (!ValidateUtil.isIdentityNo(identityNo)) {
            throw new ServiceException("身份证件号格式不正确");
        }

        // 找回次数校验
        validRetrieveLimit(uuid, identityNo, 0L);
        // 获取实名信息
        Map<String, String> certInfo = getCertInfoByUuid(uuid);
        if (!Objects.equals(realName, certInfo.get("real_name"))) {
            throw new ServiceException("信息不存在，请您重新检查填写信息是否正确");
        }

        if (!Objects.equals(identityNo, certInfo.get("identity_no"))) {
            throw new ServiceException("信息不存在，请您重新检查填写信息是否正确");
        }

        log.info("手机账号找回，实名认证成功！{}", uuid);
        return true;
    }

    /**
     * 找回次数校验
     *
     * @param identityNo
     */
    public void validRetrieveLimit(String uuid, String identityNo, Long incr) {
        // 获取限制模式
        LimitTimeUtils.LimitMode limitMode = LimitTimeUtils.of(RetrieveAccountConfig.retrieveConfig.getIdentifyLimitMode());
        // 业务key
        String timeKey = limitMode.timeKey();
        // 限制阈值
        Pair<Integer, TimeUnit> leaseTime = limitMode.leaseTime(RetrieveAccountConfig.retrieveConfig.getIdentifyLimitThreshold());

        String key = RedisKeyConstant.RETRIEVE_ACCOUNT_IDENTIFY_LIMIT.setArg(uuid, timeKey, identityNo);
        Long count = accountStringRedisTemplate.opsForValue().increment(key, incr);
        log.info("当前uuid:{}, 找回次数:{}, 限制次数:{}", uuid, count, leaseTime.getLeft());
        // 找回次数限制
        if (incr == 0) {
            // 预扣减
            count++;
        }
        if (count > RetrieveAccountConfig.retrieveConfig.getIdentifyLimitThreshold()) {
            throw new ServiceException(String.format(ID_RETRIEVE_LIMIT_TEXT, RetrieveAccountConfig.retrieveConfig.getIdentifyLimitThreshold()));
        }
        // 设置过期时间
        if (count <= 1) {
            accountStringRedisTemplate.expire(key, leaseTime.getLeft(), leaseTime.getRight());
        }
    }

    @Setter
    @Getter
    private static class SchemaUrl {
        private String schema;
        private Long iosVersionLimit;
        private Map<String, String> host;
    }

    public List<String> listRelateAccountByIdentityNo(Integer cloned, String identityNo) {
        return accountsCertificationManager.listRelateAccountByIdentityNo(cloned, identityNo, null);
    }

    /**
     * 获取认证方式
     * 2023.10.27 固化腾讯云
     *
     * @return 1-阿里云，2-腾讯云
     */
    public static Map<String, String> getCertificationTypeMap() {
        Map<String, String> result = Maps.newHashMap();
        result.put("type", "2");
        return result;
    }

    /**
     * 判断身份证名字是否相同，要处理大小写和· 圆角半角
     *
     * @return
     */
    @Deprecated
    private static boolean isSameCertName(String certName1, String certName2) {
        if(StringUtils.isBlank(certName1) || StringUtils.isBlank(certName2)) {
            return false;
        }
        String str1 = certName1.toLowerCase().replaceAll("•", "·");
        String str2 = certName2.toLowerCase().replaceAll("•", "·");
        return Objects.equals(str1, str2);
    }

    /**
     * 根据身份证获取
     *
     * @param identityNo
     * @return
     */
    private static Long getBirthByIdentityNo(String identityNo) {
        Long birth = null;
        if (StringUtils.isBlank(identityNo)) {
            return birth;
        }
        String dateStr;
        if (identityNo.length() == 15) {
            dateStr = "19" + identityNo.substring(6, 12);
        } else if (identityNo.length() == 18) {
            dateStr = identityNo.substring(6, 14);
        } else {// 默认是合法身份证号，但不排除有意外发生
            return birth;
        }

        Date date = DateUtil.StringToDate(dateStr, "yyyyMMdd");
        if(date == null){
            return birth;
        }
        return DateUtil.toSecond(date);
    }

    /**
     * 是否今日达到次数限制，否则c次数+1
     * @param accountUuid
     * @return
     */
    private Boolean isCertificationLimitOrTimesAdd(String accountUuid){
        // 判断次数
        String certificationLimitKey = CERTIFICATION_LIMIT_KEY.replace("${accountUuid}", accountUuid);
        Integer limit = 0;
        String limitStr = accountStringRedisTemplate.opsForValue().get(certificationLimitKey);
        if (StringUtils.isNotBlank(limitStr)) {
            limit = Integer.parseInt(limitStr);
        }
        Boolean isLimit = limit > CERTIFICATION_LIMIT_TIME ? true : false;
        // 超过当天的检测次数
        if (isLimit) {
            return isLimit;
        }
        long todayRemainSec = TimeFormatUtil.getTodayRemainSec();
        if(todayRemainSec != 0) {
            accountStringRedisTemplate.opsForValue().set(certificationLimitKey, String.valueOf(limit + 1), todayRemainSec, TimeUnit.SECONDS);
        }
        return false;
    }

    /**
     * 是否今日达到次数限制，否则c次数+1
     * @param accountUuid
     * @return
     */
    private Boolean isCertificationLimitOrTimesAddForChat(String accountUuid){
        // 判断次数
        String certificationLimitKey = CHAT_CERTIFICATION_LIMIT_KEY.replace("${accountUuid}", accountUuid);
        int limit = 0;
        String limitStr = accountStringRedisTemplate.opsForValue().get(certificationLimitKey);
        if (StringUtils.isNotBlank(limitStr)) {
            limit = Integer.parseInt(limitStr);
        }
        // 超过当天的检测次数
        if (limit > CERTIFICATION_LIMIT_TIME) {
            return true;
        }
        long todayRemainSec = TimeFormatUtil.getTodayRemainSec();
        if(todayRemainSec != 0) {
            accountStringRedisTemplate.opsForValue().set(certificationLimitKey, String.valueOf(limit + 1), todayRemainSec, TimeUnit.SECONDS);
        }
        return false;
    }

    /**
     * 小秘书推送：不是同一个人
     * @param accountUuid
     */
    private void pushRealNameFailInconsistentSystemMsg(String accountUuid){
        Integer sexType = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);
        String tips = "";
        if(sexType != null && sexType == 1){
            tips = "您当前的实名认证未通过，失败原因是：系统检测到您当前认证的人像与真人认证的人像非同一个人，请您确认真人认证与实名认证人像为同一人，通过实名认证后会大幅度提高您在平台上的交友成功率";
        }else{
            tips = "您当前的实名认证未通过，失败原因是：系统检测到您当前认证的人像与真人认证的人像非同一个人，请您确认真人认证与实名认证人像为同一人，若再次不符合将限制实名认证和提现功能";
        }
        Map<String, String> content = Maps.newHashMap();
        content.put("content", tips);
        content.put("relaction", "");
        content.put("is_local_push", "1");
        messageService.systemNotice(accountUuid, content, "", "text", 1);
    }

    /**
     * 小秘书推送：实名失败
     * @param accountUuid
     */
    private void pushRealNameFailSystemMsg(String accountUuid){
        Integer sexType = accountsInfoService.getSexTypeByAccountUuidRedis(accountUuid);
        String tips = "";
        if(sexType != null && sexType == 1){
            tips = "您当前的实名认证未通过，失败原因是：系统检测您提交的身份证和当前认证的人像不是同一个人，请使用本人身份证或本人进行实名认证，通过实名认证后会大幅度提高您在平台上的交友成功率";
        }else{
            tips = "您当前的实名认证未通过，失败原因是：系统检测到您提交的身份证和当前认证的人像不是同一个人，请使用本人身份证或本人进行实名认证，若再次不符合将限制实名认证和提现功能";
        }
        Map<String, String> content = Maps.newHashMap();
        content.put("content", tips);
        content.put("relaction", "");
        content.put("is_local_push", "1");
        messageService.systemNotice(accountUuid, content, "", "text", 1);
    }

    public List<Map<String, Object>> getChatRealCertificationList(int pageNum, int pageSize, ChatRealCertificationLogSearch search) {
        List<ChatRealCertificationLog> chatRealCertifications = chatRealCertificationLogDao.getChatRealCertificationLogByPage(search, pageNum, pageSize);

        if (CollectionUtils.isEmpty(chatRealCertifications)) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> res = new ArrayList<>();
        for (ChatRealCertificationLog chatRealCertification : chatRealCertifications) {
            Map<String, Object> map = new HashMap<>();
            map.put("account_uuid", chatRealCertification.getAccount_uuid());
            map.put("certification_time", chatRealCertification.getCreate_time());
            map.put("real_person_certification_url", ToolsService.getPrivateBasePic(chatRealCertification.getFace_photo_url()));
            map.put("chat_real_certification_url",ToolsService.getPrivateBasePic(chatRealCertification.getCertification_photo_url()));
            map.put("certification_status", chatRealCertification.getStatus());
            map.put("nickname", accountsInfoService.getAccountNameByAccountUuidRedis(chatRealCertification.getAccount_uuid()));
            map.put("sex_type", accountsInfoService.getSexTypeByAccountUuidRedis(chatRealCertification.getAccount_uuid()));

            res.add(map);
        }

        return res;
    }

    public Map<String, Object> isNeedCertInfo(String accountUuid) {
        Map<String, Object> res = new HashMap<>();
        AccountsChatCertification chatCertification = accountsChatCertificationDao.getByAccountUuid(accountUuid);
        int certStatus = chatCertification != null && chatCertification.getIsChecked() != null ?
                chatCertification.getIsChecked() :
                BizConst.NOT_CHAT_CERTIFICATION;
        res.put("need_cert", certStatus == BizConst.CHAT_CERTIFICATION ? BizConst.NOT_NEED_CERTIFICATION_INFO : BizConst.NEED_CERTIFICATION_INFO);
        if (certStatus == BizConst.CHAT_CERTIFICATION) {
            res.put("title", CopyWritingConst.REAL_NAME_CERTIFICATION_USE_CHATROOM_INFO);
            res.put("identity_type", chatCertification.getIdentityType());
        }

        // 控制是否显示【非中国大陆居民实名】开关；1-打开；0-关闭
        res.put("show_switch", String.valueOf(CERTIFICATION_SHOW_SWITCH));
        return res;
    }

    public Map<String, Object> initCertifyConfigFoBiz() {
        Map<String, Object> res = new HashMap<>();
        // 控制是否显示【非中国大陆居民实名】开关；1-打开；0-关闭
        res.put("show_switch", String.valueOf(CERTIFICATION_SHOW_SWITCH));
        return res;
    }

    public AccountsCertification getAccountsCertification(String accountUuid){
        return accountsCertificationDao.getByAccountUuid(accountUuid);
    }

    public Long getIdentityNoBirth(String accountUuid){
        AccountsCertification accountsCertification=accountsCertificationDao.getByAccountUuid(accountUuid);
        String identityNo = accountsCertification.getIdentityNo();
        String identityNoCipher = accountsCertification.getIdentityNoCipher();

        if(accountsCertification==null || (StringUtils.isBlank(identityNo) && StringUtils.isBlank(identityNoCipher)) ){
            return null;
        }
        if(ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
            identityNo=EncryptUtil.decrypt(identityNoCipher);
        }

        return getBirthByIdentityNo(identityNo);
    }

    /**
     * 获取用户生日（应用实名 > 业务实名 > 用户设置）
     * @param uuidList
     * @return
     */
    public Map<String,String> getIdentityNoBirthByUuids(List<String> uuidList){
        // 初始化返回结果
        Map<String,String> ageMap=initAgeMap(uuidList);
        // 查getInfoByUuid缓存
        Map<String, Map<String, Object>> map=getUserInfo(uuidList);
        // getInfoByUuid有值直接使用，无值过滤出需要查库的数据
        List<String> certificationUuids=setAndGetCertificationUuids(ageMap, map);
        // 查库设置值
        setCertificationBirth(ageMap,certificationUuids, true);
        // 过滤出数据也没有值的情况
        List<String> uuidChatList=getNoNoBirthUuids(ageMap);
        // 设置业务实名数据
        setChatCertificationBirth(ageMap,uuidChatList);
        List<String> uuidInfoList=getNoNoBirthUuids(ageMap);
        // 使用用户的设置的生日
        setAccountsInfoBirth(map, ageMap, uuidInfoList);
        return ageMap;
    }

    /**
     * 获取用户生日（应用实名 > 用户设置）
     * @param uuidList
     * @return
     */
    public Map<String,String> getAppIdentityNoBirthByUuids(List<String> uuidList){
        // 初始化返回结果
        Map<String,String> ageMap=initAgeMap(uuidList);
        // 查getInfoByUuid缓存
        Map<String, Map<String, Object>> map=getUserInfo(uuidList);
        // getInfoByUuid有值直接使用，无值过滤出需要查库的数据
        List<String> certificationUuids=setAndGetCertificationUuids(ageMap, map);
        // 查库设置值
        setCertificationBirth(ageMap,certificationUuids, true);
        // 过滤出数据也没有值的情况
        List<String> uuidInfoList=getNoNoBirthUuids(ageMap);
        // 使用用户的设置的生日
        setAccountsInfoBirth(map, ageMap, uuidInfoList);
        return ageMap;
    }

    private Map<String,String> initAgeMap(List<String> uuidList){
        Map<String,String> ageMap=Maps.newHashMap();
        if(CollectionUtils.isEmpty(uuidList)){
            return ageMap;
        }
        for(String uuid:uuidList){
            ageMap.put(uuid,"");
        }
        return ageMap;
    }

    private List<String> setAndGetCertificationUuids(Map<String,String> ageMap, Map<String, Map<String, Object>> map){
        List<String> uuidList=Lists.newArrayList();
        if(CollectionUtil.isEmpty(map)){
            return uuidList;
        }
        for(String uuid:map.keySet()){
            Map<String, Object> tempMap=map.get(uuid);
            if(CollectionUtil.isEmpty(tempMap)){
                continue;
            }
            Integer zhimaCertification=MapUtils.getInteger(tempMap,UuidInfoField.ZHIMA_CERTIFICATION, null);
            if(zhimaCertification!=null && zhimaCertification==1){
                // 获取缓存
                Long identityNoBirth = MapUtils.getLong(tempMap, UuidInfoField.IDENTITY_NO_BIRTH, null);
                if(identityNoBirth == null) {
                    // 有实名认证，但是这个字段没值有两种情况 1.后台手动标记实名，没真做实名。2.可能是异常数据
                    uuidList.add(uuid);
                }else {
                    ageMap.put(uuid, String.valueOf(identityNoBirth));
                }
            }
        }
        return uuidList;
    }

    private List<String> getNoNoBirthUuids(Map<String,String> ageMap){
        List<String> uuidList=Lists.newArrayList();
        for(String uuid:ageMap.keySet()){
            String birth=ageMap.get(uuid);
            if(StringUtils.isBlank(birth)){
                uuidList.add(uuid);
            }
        }
        return uuidList;
    }

    private void setCertificationBirth(Map<String,String> ageMap,List<String> uuidList, boolean updateCache){
        if(CollectionUtils.isEmpty(uuidList)) {
            return;
        }
        log.info("setCertificationBirth查询实名信息，uuidList={}", JsonUtils.objectToString(uuidList));
        List<AccountsCertification> accountsCertificationList=accountsCertificationDao.getAccountsCertificationByUuids(uuidList);
        if(CollectionUtils.isEmpty(accountsCertificationList)){
            return;
        }
        for(AccountsCertification temp:accountsCertificationList){
            if(temp==null){
                continue;
            }
            String tempUuid=temp.getAccountUuid();
            Long tempBirth=temp.getIdentityNoBirth();
            Integer isChecked = temp.getIsChecked();
            if(Objects.equals(isChecked, 1)) {
                String tempBirthStr=tempBirth==null?"":String.valueOf(tempBirth);
                ageMap.put(tempUuid,tempBirthStr);

                if(updateCache){
                    // 正常情况不会到这边，记个日志
                    log.info("缓存数据可能异常，写入用户实名生日，uuid={}", tempUuid);
                    String accountKey = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(tempUuid);
                    accountStringRedisTemplate.opsForHash().put(accountKey, UuidInfoField.IDENTITY_NO_BIRTH, tempBirth == null ? "" : tempBirth.toString());
                }
            }
        }
    }

    private void setChatCertificationBirth(Map<String,String> ageMap,List<String> uuidList){
        if(CollectionUtil.isEmpty(uuidList)){
            return;
        }
        List<AccountsChatCertification> accountsCertificationList=accountsChatCertificationDao.getAccountsChatCertificationByUuids(uuidList);
        if(CollectionUtils.isEmpty(accountsCertificationList)){
            return;
        }
        for(AccountsChatCertification temp:accountsCertificationList){
            if(temp==null){
                continue;
            }
            String tempUuid=temp.getAccountUuid();
            Long tempBirth=temp.getIdentityNoBirth();
            String tempBirthStr=tempBirth==null?"":String.valueOf(tempBirth);
            ageMap.put(tempUuid,tempBirthStr);
        }
    }

    /**
     * 获取生日、是否实名、实名生日
     *
     * @param uuidList
     * @return
     */
    private Map<String, Map<String, Object>> getUserInfo(List<String> uuidList){
        String[] fields = {UuidInfoField.BIRTH, UuidInfoField.ZHIMA_CERTIFICATION, UuidInfoField.IDENTITY_NO_BIRTH};
        String[] uuidArray=new String[uuidList.size()];
        uuidArray=uuidList.toArray(uuidArray);
        Map<String, Map<String, Object>> map=accountsInfoService.getInfoByUuid(uuidArray,fields,"1",false,false);
        return map;
    }

    private void setAccountsInfoBirth(Map<String, Map<String, Object>> map, Map<String,String> ageMap, List<String> uuidList){
        if(CollectionUtil.isEmpty(map)){
            return;
        }
        for(String uuid:uuidList){
            Map<String, Object> tempMap=map.get(uuid);
            if(CollectionUtil.isEmpty(tempMap)){
                continue;
            }
            String value=MapUtils.getString(tempMap,UuidInfoField.BIRTH);
            if(StringUtils.isBlank(value)){
                continue;
            }
            ageMap.put(uuid,value);
        }
    }

    /**
     * 校验外国人及港澳台通行证
     *
     * @param accountUuid
     * @param identityType
     * @param approveType  1-应用级实名，2-业务级实名
     * @param certName
     * @param certNo
     * @param source
     * @param certType
     * @return
     */
    @Transactional
    public void initCertifyForNonIdCard(String accountUuid, Integer approveType, Integer identityType, String certName, String certNo,
        Integer source, Integer certType) {

        LOGGER.info("initCertifyForNonIdCard: {}, {}, {}", StringUtils.isBlank(certName), StringUtils.isBlank(certNo), certType);

        if(!Objects.equals(identityType, CertificationIdentityTypeEnum.TAIWAN.getValue())
            && !Objects.equals(identityType, CertificationIdentityTypeEnum.HONG_KONG_MACAO.getValue())) {
            throw new ServiceException(CodeStatus.CERTIFICATION_DEVELOPING);
        }

        // 需要判断是应用级实名认证，还是业务级实名认证
        if(approveType == 1) {
            initCertifyForNonIdCardForApp(accountUuid, identityType, certName, certNo, certType);
        }else if(approveType == 2){
            initCertifyForNonIdCardForBiz(accountUuid, identityType, certName, certNo, source);
        }

    }

    /**
     * 应用级实名认证（港澳台）
     * @param accountUuid
     * @param identityType
     * @param certName
     * @param certNo
     * @param certType
     */
    private void initCertifyForNonIdCardForApp(String accountUuid, Integer identityType, String certName, String certNo,
        Integer certType) {
        Boolean isRealPersonCertification = accountsInfoService.isRealPersonCertification(accountUuid);
        if (!isRealPersonCertification) {
            throw new ServiceException(CodeStatus.CERTIFICATION_NO_REAL_PERSON);
        }

        // 判断次数
        Boolean isLimit = isCertificationLimitOrTimesAdd(accountUuid);
        // 超过当天的检测次数
        if (isLimit) {
            throw new ServiceException(CodeStatus.CERTIFICATION_TIMES_LIMIT);
        }

        // 从业务级实名认证中获取身份证和姓名
        if (certType == BizConst.NOT_NEED_CERTIFICATION_INFO && StringUtils.isBlank(certNo)) {
            AccountsChatCertification chatCertification = accountsChatCertificationDao.getByAccountUuid(accountUuid);
            if (chatCertification == null || chatCertification.getIsChecked() == null || chatCertification.getIsChecked() != BizConst.CHAT_CERTIFICATION) {
                LOGGER.warn("获取业务级实名认证为空, 麻烦确定用户是否是后台直接改状态!");
                throw new ServiceException(CodeStatus.BIZ_NO_NO_EXISTS);
            }

            if(ToolsService.accountsChatCertificationSwitchEncryption.isOn(true)) {
                certName = EncryptUtil.decrypt(chatCertification.getRealNameCipher());
                certNo = EncryptUtil.decrypt(chatCertification.getIdentityNoCipher());
            }else {
                certName = chatCertification.getRealName();
                certNo = chatCertification.getIdentityNo();
            }
        }

        //校验身份证号
        if (!isIdentityNo(certNo, identityType)) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        if (certNo.length() != 18) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        // 校验身份年龄是否满
        if (!ifGrownUp(certNo)) {
            throw new ServiceException(CodeStatus.IDENTITY_NONAGE);
        }

        // 2021.06.25 处理少数民族名字
        certName = certName.replaceAll("•", "·");

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();

        //验证
        AccountsCertification ac = accountsCertificationDao.getByAccountUuid(accountUuid);
        Long certificationId = (ac == null || ac.getId() == null) ? null : Long.valueOf(ac.getId().toString());
        Integer certificationChecked = (ac == null || ac.getIsChecked() == null) ? null : Integer.valueOf(ac.getIsChecked().toString());

        if (certificationId != null && Objects.equals(certificationChecked, 1)) {
            throw new ServiceException(CodeStatus.ACCOUNT_ALREADY_CERTIFICATION);
        }

        LOGGER.info("查询到认证信息，json={}", JsonUtils.objectToString(ac));

        if (getCheckedIdentityNoCount(certNo, cloned) > 0) {//身份证已绑定
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION);
        }

        // 判断身份证是否被其他账号的业务级认证绑定了
        if (accountsChatCertificationManager.countIdentityInUse(cloned, certNo, accountUuid) > 0) {
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION_IN_CHAT);
        }

        // 新的认证方式
        CheckResidenceInfoResponse checkResidenceInfo = fujianGovApiClient.checkResidenceInfo(certName, certNo);

        //需要根据结果处理
        Integer isChecked = checkResidenceInfo.isSuccess()? CommConst.YES_1 : CommConst.NO_0;

        //认证成功
        isChecked = 1;
        Map<String, Object> keyValueMap = new HashMap<>();
        keyValueMap.put("is_checked", isChecked);
        keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
        keyValueMap.put("identity_no_status", CommConst.NO_0);
        keyValueMap.put("certification_photo_url", "");
        keyValueMap.put("real_name", certName);
        keyValueMap.put("identity_no", certNo);
        keyValueMap.put("identity_no_birth", getBirthByIdentityNo(certNo));
        keyValueMap.put("reward_account", "");
        keyValueMap.put("reward_account_checked", CommConst.NO_0);
        keyValueMap.put("reward_account_status", AccountsCertification.RewardAccountStatus.DEFAULT.getValue());
        keyValueMap.put("biz_no", checkResidenceInfo.getOrderNo());
        keyValueMap.put("account_uuid", accountUuid);
        keyValueMap.put("identity_type", identityType);
        keyValueMap = encryptCertInfo(keyValueMap, ToolsService.accountsCertificationSwitchWrite);
        if (certificationId == null) {
            keyValueMap.put("create_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("account_uuid", accountUuid);
            accountsCertificationDao.addKeyValue(keyValueMap, accountUuid);
        } else {
            keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
            accountsCertificationDao.updateById(keyValueMap, certificationId, accountUuid);
        }

        accountsPersonalInfoService.setZhimaCertification(accountUuid, isChecked);
        accountsCertificationHistoryService.saveOrUpdate(accountUuid, identityType, CommConst.NO_0, accountUuid, "");
        this.saveToCacheIdentityNoBirth(accountUuid, certNo, isChecked);
        certifySuccessPushMq(accountUuid, certNo, certName, "");
    }

    /**
     * 业务级实名认证（港澳台）
     * @param accountUuid
     * @param identityType
     * @param certName
     * @param certNo
     * @param source
     */
    private void initCertifyForNonIdCardForBiz(String accountUuid, Integer identityType, String certName, String certNo,
        Integer source) {
        // 判断次数
        Boolean isLimit = isCertificationLimitOrTimesAddForChat(accountUuid);
        // 超过当天的检测次数
        if (isLimit) {
            throw new ServiceException(CodeStatus.CERTIFICATION_TIMES_LIMIT);
        }

        // 校验身份证号
        if (!isIdentityNo(certNo, identityType)) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        if (certNo.length() != 18) {
            throw new ServiceException(CodeStatus.IDENTITY_INVALID);
        }
        // 校验身份年龄是否满18
        if (!ifGrownUp(certNo)) {
            throw new ServiceException(CodeStatus.IDENTITY_NONAGE);
        }

        // 2021.06.25 处理少数民族名字，腾讯云接收半角点
        certName = certName.replaceAll("•", "·");

        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer cloned = sbp.getCloned();

        // 验证
        AccountsChatCertification ac = accountsChatCertificationDao.getByAccountUuid(accountUuid);
        Long certificationId = (ac == null || ac.getId() == null) ? null : Long.valueOf(ac.getId().toString());
        Integer certificationChecked =
            (ac == null || ac.getIsChecked() == null) ? null : Integer.valueOf(ac.getIsChecked().toString());

        if (certificationId != null && certificationChecked != null && certificationChecked == 1) {
            throw new ServiceException(CodeStatus.ACCOUNT_ALREADY_CERTIFICATION);
        }

        LOGGER.info("查询到业务级实名认证信息，json={}", JsonUtils.objectToString(ac));

        if (getCheckedIdentityNoCount(certNo, cloned) > 0) {// 身份证在 全局真人实名 已绑定
            LOGGER.warn("the input id_card had already bind in global certification");
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION);
        }

        if (getCheckedIdentityNoCountForChat(cloned, certNo) > 0) { // 身份证在 业务级实名 已绑定
            LOGGER.warn("the input id_card had already bind in chat room certification");
            throw new ServiceException(CodeStatus.IDENTITY_NO_ALREADY_CERTIFICATION);
        }

        // 新的认证方式
        CheckResidenceInfoResponse checkResidenceInfo = fujianGovApiClient.checkResidenceInfo(certName, certNo);

        // 需要根据结果处理
        Integer isChecked = checkResidenceInfo.isSuccess() ? CommConst.YES_1 : CommConst.NO_0;

        // 保存至数据库并更新缓存
        Map<String, Object> keyValueMap = Maps.newHashMap();
        keyValueMap.put("real_name", certName);
        keyValueMap.put("identity_no", certNo);
        keyValueMap.put("identity_no_birth", getBirthByIdentityNo(certNo));
        keyValueMap.put("identity_no_status", isChecked);
        keyValueMap.put("biz_no", checkResidenceInfo.getOrderNo());
        keyValueMap.put("reward_account", "");
        keyValueMap.put("is_checked", isChecked);
        keyValueMap.put("reward_account_checked", CommConst.NO_0);
        keyValueMap.put("certification_photo_url", "");
        keyValueMap.put("identity_type", identityType);
        encryptCertInfo(keyValueMap, ToolsService.accountsChatCertificationSwitchWrite);
        if (certificationId == null) {
            keyValueMap.put("create_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("account_uuid", accountUuid);
            keyValueMap.put("source", source);
            accountsChatCertificationDao.addKeyValue(keyValueMap);
        } else {
            keyValueMap.put("update_time", DateUtil.currentTimeSeconds());
            keyValueMap.put("source", source);
            accountsChatCertificationDao.updateById(keyValueMap, certificationId);
        }

        // 记录日志给风控
        userCertificationLogService.logRealNameBiz(checkResidenceInfo.isSuccess(), accountUuid, "", "", 0F, certNo);

        accountsPersonalInfoService.setChatRealCertification(accountUuid, isChecked);

        certifySuccessPushMqForChat(accountUuid, certNo, certName);

        LOGGER.info("account uuid: {} finish biz real certification.", accountUuid);

    }

    /**
     * 身份证号校验
     *
     * @param certNo 身份证号
     * @param identityType  CertificationIdentityTypeEnum
     * @return
     */
    public static boolean isIdentityNo(String certNo, Integer identityType) {
        if(!ValidateUtil.isIdentityNo(certNo)) {
            return false;
        }
        if(CertificationIdentityTypeEnum.TAIWAN.getValue() == identityType) {
            if(!certNo.startsWith(TAIWAN_IDENTITY_PREFIX)) {
                return false;
            }
        }else if (CertificationIdentityTypeEnum.HONG_KONG_MACAO.getValue() == identityType) {
            if(!certNo.startsWith(HONG_KONG_IDENTITY_PREFIX) && !certNo.startsWith(MACAO_IDENTITY_PREFIX)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 两个uuid的实名身份证是否一致
     *
     * @param accountUuid1
     * @param accountUuid2
     * @return 0-不一致，1-一致
     */
    public Integer isSameIdentityNo(String accountUuid1, String accountUuid2) {
        Integer isSameIdentityNo = CommConst.NO_0;

        AccountsCertification accountsCertification1 = accountsCertificationDao.getByAccountUuid(accountUuid1);
        AccountsCertification accountsCertification2 = accountsCertificationDao.getByAccountUuid(accountUuid2);

        String identityNo1 = "";
        String identityNo2 = "";
        if (ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
            identityNo1 = EncryptUtil.decrypt(accountsCertification1.getIdentityNoCipher());
            identityNo2 = EncryptUtil.decrypt(accountsCertification2.getIdentityNoCipher());
        } else {
            identityNo1 = accountsCertification1.getIdentityNo();
            identityNo2 = accountsCertification2.getIdentityNo();
        }

        if (accountsCertification1 != null && accountsCertification2 != null
                && Objects.equals(identityNo1, identityNo2)
                && Objects.equals(accountsCertification1.getIsChecked(), CommConst.YES_1)
                && Objects.equals(accountsCertification2.getIsChecked(), CommConst.YES_1)
        ) {
            isSameIdentityNo = CommConst.YES_1;
        }

        return isSameIdentityNo;
    }

    /**
     * @param accountUuid
     * @return
     */
    public boolean isRewardAccountInvalidNotice(String accountUuid) {
        // 减少请求数据库，先查用户是否实名，有实名再查支付宝账号状态
        Map<String, Object> accountInfoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{ UuidInfoField.ZHIMA_CERTIFICATION}, "1", false, true).get(accountUuid);
        Integer zhimaCertification = MapUtils.getInteger(accountInfoMap, UuidInfoField.ZHIMA_CERTIFICATION, null);
        if(!Objects.equals(zhimaCertification, CommConst.YES_1)) {
            // 未实名，直接返回
            return false;
        }
        AccountsCertification accountsCertification = accountsCertificationDao.getByAccountUuid(accountUuid);
        if(accountsCertification == null ){
            // 测试账号直接标记实名，没有记录，所有要判null
            return false;
        }
        Integer rewardAccountStatus = accountsCertification.getRewardAccountStatus();
        if(!Objects.equals(rewardAccountStatus, AccountsCertification.RewardAccountStatus.BIND_INVALID.getValue())) {
            return false;
        }

        ValueOperations<String, String> opsForValue = lockStringRedisTemplate.opsForValue();
        String redisKey = RedisKeyConstant.CONTROL_REWARD_ACCOUNT_INVALID_NOTICE.setArg(accountUuid);
        String value = opsForValue.get(redisKey);
        if(StringUtils.isNotBlank(value)) {
            log.info("频控：支付宝账号失效已提醒过。accountUuid={}", accountUuid);
            return false;
        }

        log.info("支付宝账号失效提醒。accountUuid={}", accountUuid);
        opsForValue.set(redisKey, "1", 180, TimeUnit.DAYS);

        return true;
    }

}
