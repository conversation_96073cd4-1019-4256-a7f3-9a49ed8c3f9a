package cn.taqu.account.service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.JsonUtils;

/**
 * 注册限制
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
@Service
public class RegisterLimitService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RegisterLimitService.class);
    /**
     * 相同手机号、微博、微信、QQ、手机token条件下的注册限制次数，0表示不限制
     */
    private static Integer REGISTER_LIMIT_TIME = 3;
    /**
     * 不限制次数 0
     */
    public static Integer REGISTER_NOT_LIMIT_TIME = 0;
    /**
     * 是否开启注册限制，true-开启，false-关闭
     */
    private static Boolean SWITCH = true;
    /**
     * 注册设备token白名单，在白名单的token不受注册限制。目前主要给测试机加入白名单使用
     */
    private static Set<String> WHITE_TOKEN_SET = new HashSet<>();

    /**
     * 配置参数
     * @param conf
     */
    public static void setConf(String conf){
        try {
            Map<String, Object> confMap = JsonUtils.stringToObject2(conf, new TypeReference<Map<String, Object>>() {});
            SWITCH = MapUtils.getBoolean(confMap,"on",true);
            REGISTER_LIMIT_TIME = MapUtils.getInteger(confMap,"registerLimitTime",3);
            String whiteTokenString = MapUtils.getString(confMap,"whiteTokenString");
            WHITE_TOKEN_SET = new HashSet<>(Arrays.asList(whiteTokenString.split(",")));
            LOGGER.info("获取注册白名单token信息={}", JSON.toJSON(WHITE_TOKEN_SET));
        }catch (Exception e){
            LOGGER.error("获取注册限制配置失败",e);
        }
    }

    /**
     * 获取是否需要进行注册限制信息，registerLimitTime为0表示不限制
     * @return
     */
    public Integer getRegisterLimitTime(){
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String token = Optional.ofNullable(soaBaseParams.getToken()).orElse("");
        // 是否白名单
        boolean isWhiteToken = WHITE_TOKEN_SET.contains(token);
        return (!SWITCH || isWhiteToken) ? REGISTER_NOT_LIMIT_TIME : REGISTER_LIMIT_TIME;
    }

    /**
     * 是否设备白名单
     * @return
     */
    public Boolean isRegWhiteToken(){
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String token = Optional.ofNullable(soaBaseParams.getToken()).orElse("");
        return WHITE_TOKEN_SET.contains(token);
    }
}
