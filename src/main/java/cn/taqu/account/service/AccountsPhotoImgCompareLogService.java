package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.dao.AccountsPhotoImgCompareLogDao;
import cn.taqu.account.model.AccountsPhotoImgCompareLog;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-01-14 11:34
 */
@Service
public class AccountsPhotoImgCompareLogService {

    @Autowired
    private AccountsPhotoImgCompareLogDao accountsPhotoImgCompareLogDao;

    @Transactional
    public void merge(AccountsPhotoImgCompareLog accountsPhotoImgCompareLog){
        accountsPhotoImgCompareLogDao.merge(accountsPhotoImgCompareLog);
    }
}
