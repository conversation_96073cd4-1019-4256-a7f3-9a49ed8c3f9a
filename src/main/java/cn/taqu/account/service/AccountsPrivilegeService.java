package cn.taqu.account.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsPrivilegeDao;
import cn.taqu.account.model.AccountsPrivilege;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/27.
 */
@Service
@Transactional
public class AccountsPrivilegeService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsPrivilege> {

    @Autowired
    private AccountsPrivilegeDao accountsPrivilegeDao;
    @Autowired
    @Qualifier("privilegeStringRedisTemplate")
    private StringRedisTemplate privilegeStringRedisTemplate;

    @Transactional
    public AccountsPrivilege create(String privilegeConfig) {
        AccountsPrivilege accountsPrivilege = new AccountsPrivilege();
        accountsPrivilege.setPrivilege_config(StringUtils.trimToEmpty(privilegeConfig));
        accountsPrivilege.setCreate_time(DateUtil.currentTimeSeconds());
        return this.accountsPrivilegeDao.merge(accountsPrivilege);
    }

    /**
     * 获取头衔权限
     * @param privilegeId
     * @param fields
     * @return
     */
    public Map<String, String> getMedalPrivilege(Long privilegeId, String...fields) {
        Map<String, String> defaultPrivilege = this.defaultDriverPriv();
        if(privilegeId == null || privilegeId == 0) {
            return defaultPrivilege;
        }
        Map<String, String> medalPrivilege = this.getPrivilegeFromCache(privilegeId, "m", fields);
        if(fields == null || fields.length == 0) {
            defaultPrivilege.putAll(medalPrivilege);
            return defaultPrivilege;
        }
        return medalPrivilege;
    }

    private Map<String, String> defaultDriverPriv() {
        return MapUtils.putAll(new HashMap<String, String>(), new String[]{
                "modify_avatar_enable", "0",
                "say_hello", "0",
                "letter_enable", "0",
                "review_enable", "0",
                "reply_enable", "0",
                "personal_profile", "0",
                "circle1_post_enable", "0",
                "circle2_post_enable", "0",
                "post_img_enable", "0",
                "moment_img_enable", "0",
                "letter_img", "0",
                "review_img_enable", "0",
                "post_enable", "0",
                "is_vote", "0",
                "highest_vote_option", "0",
                "say_hello_num", "0",
                "driver_del_post_review", "0",
                "word_post_no_check", "0",
                "is_video", "0",
                "moment_enable", "0",
                "no_barriers_msg", "0"
        });
    }

    /**
     * 从缓存中获取指定的权限配置项
     * @param privilegeId 权限id
     * @param type 权限所属于类型 l:等级权限; m:头衔权限; d:驾照公里数权限
     * @param fields 指定返回的配置项，选填
     * @return
     */
    private Map<String, String> getPrivilegeFromCache(Long privilegeId, String type, String...fields) {
        String key = RedisKeyConstant.PRIVILEGE_INFO.setArg(type, privilegeId);
        Map<String, String> result = Maps.newHashMap();

        if(fields!=null && fields.length>0) {
            List<Object> hashKeyList = Lists.newArrayList();
            for(String field : fields) {
                if(StringUtils.isNotBlank(field)) {
                    hashKeyList.add(field);
                }
            }
            List<Object> hashValueList = privilegeStringRedisTemplate.opsForHash().multiGet(key, hashKeyList);
            int size = hashKeyList.size();
            for(int i=0; i<size; i++) {
                String hashKey = (String)hashKeyList.get(i);
                Object hashValue = hashValueList.get(i);
                result.put(hashKey, hashValue==null?"0":hashValue.toString());
            }
        } else {
            Map<Object, Object> hashMaps = privilegeStringRedisTemplate.opsForHash().entries(key);
            if(hashMaps != null) {
                for(Map.Entry<Object, Object> entry : hashMaps.entrySet()) {
                    String hashKey = (String)entry.getKey();
                    String hashValue = (String)entry.getValue();
                    result.put(hashKey, hashValue==null ? "0" : hashValue);
                }
            }
        }

        return result;
    }

    /**
     * 更新权限配置
     * @param privilegeId 权限id
     * @param privilegeConfig 权限配置项map
     * @param type 权限所属于类型 l:等级权限; m:头衔权限
     * @return
     */
    public AccountsPrivilege update(Long privilegeId, Map<String, Object> privilegeConfig, String type, boolean createWhenNoExists) {
        AccountsPrivilege accountsPrivilege = accountsPrivilegeDao.getOneById(privilegeId);
        if(accountsPrivilege == null) {
            if(!createWhenNoExists) {
                throw new ServiceException("privilege_not_exists", "该权限id不存在");
            }
            accountsPrivilege = this.create("");
        }

        Map<String, String> finalPrivilegeConfig = Maps.newHashMap();
        if(privilegeConfig!=null) {
            for(Map.Entry<String, Object> entry : privilegeConfig.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                finalPrivilegeConfig.put(key, value == null ? "" : value.toString().trim());
            }
        }

        accountsPrivilege.setUpdate_time(DateUtil.currentTimeSeconds());
        accountsPrivilege.setPrivilege_config(StringUtils.trimToEmpty(JsonUtils.objectToString(finalPrivilegeConfig)));
        accountsPrivilege = accountsPrivilegeDao.merge(accountsPrivilege);
        this.updateCache(privilegeId, finalPrivilegeConfig, type);
        return accountsPrivilege;
    }

    private void updateCache(Long privilegeId, Map<String, String> privilegeConfig, String type) {
        privilegeStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.PRIVILEGE_INFO.setArg(type, privilegeId), privilegeConfig);
    }

    public void deleteCache(Long privilegeId, String type) {
        privilegeStringRedisTemplate.delete(RedisKeyConstant.PRIVILEGE_INFO.setArg(type, privilegeId));
    }
}
