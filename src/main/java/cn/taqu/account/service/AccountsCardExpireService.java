package cn.taqu.account.service;

import cn.taqu.account.dao.AccountsCardExpireDao;
import cn.taqu.account.model.AccountsCardExpire;
import cn.taqu.core.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 过期靓号service
 */
@Service
public class AccountsCardExpireService {

    @Autowired
    private AccountsCardExpireDao accountsCardExpireDao;

    /**
     * 存在记录，更新过期时间；不存在，插入一条记录
     * @param accountUuid
     * @param cardId
     * @param level
     * @param endTime
     * @return
     */
    @Transactional
    public int create(String accountUuid, Long cardId, String level, Long endTime) {
        return accountsCardExpireDao.insert(accountUuid, cardId, level, endTime);
    }

    /**
     * 根据uuid获取数据
     * @param accountUuid 用户uuid
     * @param page 页数 从1开始
     * @param limit 每次取多少条
     * @return
     */
    public List<Map<String, String>> getByUuid(String accountUuid, int page, int limit) {
        if(StringUtils.isBlank(accountUuid)) {
            return Collections.EMPTY_LIST;
        }

        List<AccountsCardExpire> accountsCardExpireList = accountsCardExpireDao.getByUuid(accountUuid, page, limit);
        int size = accountsCardExpireList == null ? 0 : accountsCardExpireList.size();
        List<Map<String, String>> result = new ArrayList<>(size);
        for(int i=0; i<size; i++) {
            AccountsCardExpire accountsCardExpire = accountsCardExpireList.get(i);
            Map<String, String> map = new HashMap<>();
            map.put("card_id", String.valueOf(accountsCardExpire.getCardId()));
            map.put("level", String.valueOf(accountsCardExpire.getLevel()));
            map.put("end_date", DateUtil.dateToString("yyyy/MM/dd", DateUtil.fromSecond(accountsCardExpire.getEndTime())));
            result.add(map);
        }
        return result;
    }
}
