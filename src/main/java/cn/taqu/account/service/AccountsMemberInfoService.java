package cn.taqu.account.service;

import cn.hutool.core.util.RandomUtil;
import cn.taqu.account.common.AccountActiveStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsMemberInfoDao;
import cn.taqu.account.dto.AccountAndTokenInfoDto;
import cn.taqu.account.model.AccountsMemberInfo;
import cn.taqu.account.model.Region;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class AccountsMemberInfoService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsMemberInfo> {
    /**
     *"用户最近活跃日期"的缓存个数
     */
    private static final int COUNT_OF_ACCOUNT_ACTIVE_CACHES = AccountActiveStatus.COUNT_OF_ACCOUNT_ACTIVE_CACHES.getValue();
    //昵称复审
    public static int NICK_NAME_REVIEW = 1;
    //头像复审
    public static int AVATAR_REVIEW = 1;
    //个人简介复审
    public static int PERSONAL_INFO_REVIEW = 1;

    @Autowired
    private AccountsMemberInfoDao accountsMemberInfoDao;
    @Autowired
    private BuryService buryService;
    @Autowired
    private RegionService regionService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsActiveLogService accountsActiveLogService;
    @Autowired
    private NickNameRiskVerifyService nickNameRiskVerifyService;
    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private AccountsPersonalInfoService accountsPersonalInfoService;

    @Transactional
    public void createOrUpdateByUuid(String accountUuid, String token, Long appVersion, Integer appcode, Integer cloned, String channel, Integer platformId) {

        if(StringUtils.isBlank(accountUuid)) {
            return;
        }

        AccountsMemberInfo accountsMemberInfo = this.createOrUpdate(accountUuid, token, appVersion, appcode, cloned, channel, platformId);
        if(accountsMemberInfo != null) {
            this.addToRecentOpen(accountUuid, accountsMemberInfo.getLastOpenTime());
        }else {
            logger.warn("accountsMemberInfo为null");
        }
        this.updateAppVersionCache(accountUuid, appVersion, platformId, channel);

        // 2020.03.05 更新用户位置
        accountsService.updateAccountExtraInfo(accountUuid);

        return;
    }

    /**
     * 设置用户最后登录版本号/平台
     *
     * @param accountUuid
     * @param appVersion
     * @param platformId
     * @param channel
     */
    public void updateAppVersionCache(String accountUuid, Long appVersion, Integer platformId, String channel) {
        String key = RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid);
        Map<String, String> hashValue = new HashMap<>();
        hashValue.put("app_version", appVersion == null ? "0" : appVersion.toString());
        hashValue.put("platform_id", platformId == null ? "0" : platformId.toString());
        hashValue.put("channel", StringUtils.trimToEmpty(channel));
        accountStringRedisTemplate.opsForHash().putAll(key, hashValue);
    }

    /**
     * 记录下最近打开设备的账号uuid
     *
     * @param accountUuid
     */
    @Deprecated
    private void addToRecentOpen(String accountUuid, Long time) {
//        String key = RedisKeyConstant.RECENT_OPEN_ACCOUNT.getPattern();
//        memberStringRedisTemplate.opsForZSet().add(key, accountUuid.trim(), time);
//        //数据太多时，删除至只剩1000条
//        long count = memberStringRedisTemplate.opsForZSet().zCard(key);
//        if (count > 2000) {//之所以到2000条再删除，就是避免掉大于1000条后，每增加一条都要删除一次
//            memberStringRedisTemplate.opsForZSet().removeRange(key, 0, count - 1000);
//        }
    }

    /**
     * 创建或更新
     *
     * @param accountUuid
     * @param token
     * @param appVersion
     * @param appcode
     * @param channel
     * @param platformId
     * @return
     */
    @Transactional
    public AccountsMemberInfo createOrUpdate(String accountUuid, String token, Long appVersion, Integer appcode, Integer cloned, String channel, Integer platformId) {
        if (StringUtils.isBlank(accountUuid) || StringUtils.isBlank(token)) {
            return null;
        }
        AccountsMemberInfo accountsMemberInfo = this.accountsMemberInfoDao.findByAccountUuid(accountUuid);
        Long currentTimeSeconds = DateUtil.currentTimeSeconds();
        if (accountsMemberInfo == null) {
            String accountMemberKey = "account:member:" + StringUtils.trimToEmpty(accountUuid);
            boolean isAbsent = accountStringRedisTemplate.opsForValue().setIfAbsent(accountMemberKey, "1");
            accountStringRedisTemplate.expire(accountMemberKey, 1, TimeUnit.MINUTES);
            if (!isAbsent) {
                return null;
            }
            accountsMemberInfo = new AccountsMemberInfo();
            accountsMemberInfo.setAccountUuid(accountUuid);
            accountsMemberInfo.setCreateTime(currentTimeSeconds);
        }

        SoaBaseParams soaBaseParams = RequestParams.getSoa_basic_java();

        accountsMemberInfo.setToken(token);
        accountsMemberInfo.setAppVersion(appVersion == null ? 0 : appVersion);
        accountsMemberInfo.setAppcode(appcode == null ? 0 : appcode);
        accountsMemberInfo.setCloned(cloned == null ? 1 : cloned);
        accountsMemberInfo.setChannel(StringUtils.trimToEmpty(channel));
        accountsMemberInfo.setPlatformId(platformId == null ? 0 : platformId);
        accountsMemberInfo.setLastOpenTime(currentTimeSeconds);
        accountsMemberInfo.setLastActiveTime(currentTimeSeconds);
        accountsMemberInfo.setUpdate_time(currentTimeSeconds);
        accountsMemberInfo = this.accountsMemberInfoDao.merge(accountsMemberInfo);
        this.activeTimeToDateCenter(accountUuid, currentTimeSeconds, accountsMemberInfo.getCreateTime());
        this.cacheActiveTime(accountUuid, currentTimeSeconds);
        return accountsMemberInfo;
    }

    @Transactional
    public void updateLastActiveTime(String uuid, Long time, Integer cityId) {
        int count = accountsMemberInfoDao.updateLastActiveTime(uuid, time);
        if (count == 0) {
            logger.warn("更新用户:{}最后活跃时间失败，用户绑定设备记录可能不存在", uuid);
            return;
        }

        this.addToRecentOpen(uuid, time);
        this.cacheActiveTime(uuid, time);

        String city = "";
        if (cityId != null && cityId != 0) {
            Region region = regionService.findByIdList(Arrays.asList(cityId.longValue())).get(cityId.longValue());
            if (region != null) {
                city = StringUtils.trimToEmpty(region.getRegion_name());
            }
        }
        this.activeTimeToDateCenter(uuid, time, DateUtil.currentTimeSeconds());
        accountStringRedisTemplate.opsForHash().put(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), "city", city);
        //超过90天未活跃，推多消费队列
        pushToAccountActiveTime(uuid, time);
        //用户活跃日期, 写入redis
        this.addAccountActiveDateList(uuid, time);
        // 活跃用户uuid，写入redis
        this.addAccountActiveUuidList(uuid);
        //用户活跃日期, 写入DB
        this.accountsActiveLogService.insertIgnore(uuid, time);
    }

    /**
     * 超过90天未活跃，推多消费队列
     */
    private void pushToAccountActiveTime(String uuid, Long time) {
        Integer activeTime = getAccountActiveTime(uuid, 1).get(0);
        if (!Objects.equals(0, activeTime)) {
            long start = DateUtil.toSecond(DateUtil.string8ToDate(String.valueOf(activeTime)));
            long end = time;
            if (end - start > 90 * 24 * 60 * 60) {
                BuryService.pushToAccountActiveTime(uuid, activeTime);
            }
        }
    }

    /**
     * 用户活跃日期历史记录, 写入redis
     *
     * @param accountUuid
     * @param time
     */
    private void addAccountActiveDateList(String accountUuid, Long time) {
        String key = RedisKeyConstant.ACCOUNT_ACTIVE_DATE_LIST.setArg(accountUuid);
        accountStringRedisTemplate.opsForZSet().add(key, DateUtil.dateToString8(DateUtil.fromSecond(time)), time);
        //数据太多时，删除
        long count = accountStringRedisTemplate.opsForZSet().zCard(key);
        if (count > COUNT_OF_ACCOUNT_ACTIVE_CACHES) {
            accountStringRedisTemplate.opsForZSet().removeRange(key, 0, count - COUNT_OF_ACCOUNT_ACTIVE_CACHES - 1);
        }
        accountStringRedisTemplate.expire(key, 100, TimeUnit.DAYS);
    }

    /**
     * 如果当前uuid写入【用户活跃日期历史记录】 Redis的数量超过COUNT_OF_ACCOUNT_ACTIVE_CACHES，则写到Redis中
     *
     * @param uuid uuid
     */
    private void addAccountActiveUuidList(String uuid) {
        try {
            String key = RedisKeyConstant.ACCOUNT_ACTIVE_DATE_LIST.setArg(uuid);
            long count = accountStringRedisTemplate.opsForZSet().zCard(key);
            // 如果Redis中该uuid的活跃记录满20条，再进行添加
            if (count >= COUNT_OF_ACCOUNT_ACTIVE_CACHES) {
                String part = this.getRedisPark(uuid);
                accountStringRedisTemplate.opsForSet().add(RedisKeyConstant.ACCOUNT_ACTIVE_UUID.setArg(part), uuid);
                accountStringRedisTemplate.expire(RedisKeyConstant.ACCOUNT_ACTIVE_UUID.setArg(part), 2, TimeUnit.DAYS);
            }
        }catch (Exception e){
            logger.error("账户活跃uuid,写入Redis失败", e);
        }
    }

    /**
     * 根据uuid生成12个分区
     *
     * @param uuid uuid
     * @return 分区号 （12个分区 a-l）
     */
    private String getRedisPark(String uuid) {
        if (uuid.length() == 12 || uuid.length() == 16) {
            return uuid.substring(1, 2);
        } else if (uuid.length() == 11) {
            char c = uuid.charAt(0);
            while (c > 108) {
                c = (char) ((int) c - 12);
            }
            return c + "";
        } else {
            return RandomUtil.randomChar("abcdefghijkl") + "";
        }
    }

    /**
     * 获取用户的前一个活跃时间，非今天的
     *
     * @param accountUuid
     * @return
     */
    public Long getAccountPrevActiveTime(String accountUuid) {
        int dateIntValue = Integer.valueOf(DateUtil.dateToString8(new Date()));
        String key = RedisKeyConstant.ACCOUNT_ACTIVE_DATE_LIST.setArg(accountUuid);
        Set<ZSetOperations.TypedTuple<String>> typedTupleSet = accountStringRedisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 2);
        for (ZSetOperations.TypedTuple<String> typedTuple : typedTupleSet) {
            if (Integer.valueOf(typedTuple.getValue()) != dateIntValue) {
                return typedTuple.getScore().longValue();
            }
        }
        return 0L;
    }

    /**
     * 根据上一次活跃天数（非今天）与今天零点比较
     * @param accountUuid
     * @return
     */
    public Long getNoActiveDaysByPrevActiveTime(String accountUuid){
        Long accountPrevActiveTime = getAccountPrevActiveTime(accountUuid);
        if(accountPrevActiveTime == 0){
            return accountPrevActiveTime;
        }
        Long todayBeginSecond = DateUtil.getTodayBeginSecond();
        Long noActiveDays = (todayBeginSecond - accountPrevActiveTime) / (60 * 60 * 24);
        return noActiveDays;
    }

    /**
     * 获取用户最近活跃日期
     *
     * @param accountUuid
     * @param count
     * @return
     */
    public List<Integer> getAccountActiveTime(String accountUuid, Integer count) {
        if (count > COUNT_OF_ACCOUNT_ACTIVE_CACHES) {
            count = COUNT_OF_ACCOUNT_ACTIVE_CACHES;
        }
        List<Integer> result = Lists.newArrayList();
        String key = RedisKeyConstant.ACCOUNT_ACTIVE_DATE_LIST.setArg(accountUuid);
        Set<ZSetOperations.TypedTuple<String>> typedTupleSet = accountStringRedisTemplate.opsForZSet().reverseRangeWithScores(key, 0, count - 1);
        for (ZSetOperations.TypedTuple<String> typedTuple : typedTupleSet) {
            result.add(Integer.parseInt(typedTuple.getValue()));
        }

        //缓存读取不到数据，可能已过期，读数据库
        if (CollectionUtils.isEmpty(result)) {
            result = getAccountActiveTimeFromDB(accountUuid, count);
        }

        while (result.size() < count) {
            result.add(0);
        }
        return result;
    }

    /**
     * 缓存读取数据，如果缓存不存在，直接返回，不查询数据库
     *
     * @param accountUuids
     * @param count
     * @return
     */
    public Map<String, List<Long>> mGetAccountActiveTime(List<String> accountUuids, Integer count) {
        if (count > COUNT_OF_ACCOUNT_ACTIVE_CACHES) {
            count = COUNT_OF_ACCOUNT_ACTIVE_CACHES;
        }
        long start = 0L;
        long end = count - 1L;
        RedisSerializer<String> stringRedisSerializer = accountStringRedisTemplate.getStringSerializer();
        List<Object> valueList = accountStringRedisTemplate.executePipelined((RedisConnection connection) -> {
            accountUuids.forEach(accountUuid -> {
                String key = RedisKeyConstant.ACCOUNT_ACTIVE_DATE_LIST.setArg(accountUuid);
                connection.zRevRangeWithScores(stringRedisSerializer.serialize(key), start, end);
            });
            return null;
        }, stringRedisSerializer);

        Map<String, List<Long>> result = Maps.newHashMap();
        for (int i = 0; i < valueList.size(); i++) {
            Set<DefaultTypedTuple> tuples = (Set<DefaultTypedTuple>) valueList.get(i);
            String accountUuid = accountUuids.get(i);
            List<Long> list;
            if (CollectionUtils.isNotEmpty(tuples)) {
                list = tuples.stream()
                        .map(DefaultTypedTuple::getScore)
                        .map(Double::longValue)
                        .collect(Collectors.toList());
            } else {
                list = Lists.newArrayList();
            }

            while (list.size() < count) {
                list.add(0L);
            }
            result.put(accountUuid, list);
        }

        return result;
    }

    /**
     * 读取数据库，获取用户最近活跃日期
     */
    public List<Integer> getAccountActiveTimeFromDB(String accountUuid, Integer count) {
        List<Integer> list = accountsActiveLogService.findByUuid(accountUuid);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        list.sort(Comparator.reverseOrder());
        Set<ZSetOperations.TypedTuple<String>> tuples = Sets.newHashSet();
        for (Integer time : list.subList(0, COUNT_OF_ACCOUNT_ACTIVE_CACHES > list.size() ? list.size() : COUNT_OF_ACCOUNT_ACTIVE_CACHES)) {
            String date8Str = String.valueOf(time);
            Long second = DateUtil.toSecond(DateUtil.string8ToDate(date8Str));
            tuples.add(new DefaultTypedTuple<>(date8Str, second.doubleValue()));
        }
        String key = RedisKeyConstant.ACCOUNT_ACTIVE_DATE_LIST.setArg(accountUuid);
        accountStringRedisTemplate.opsForZSet().add(key, tuples);
        accountStringRedisTemplate.expire(key, 100, TimeUnit.DAYS);

        return Lists.newArrayList(list.subList(0, count > list.size() ? list.size() : count));
    }

    /**
     * 获取未活跃天数，每天第一次获取时返回，否则返回0
     *
     * @param accountUuid
     * @return
     */
    public Integer getNoActiveDays(String accountUuid) {
        String date8 = DateUtil.dateToString8(new Date());
        String noActiveDayGetKey = RedisKeyConstant.NO_ACTIVE_DAY_GET.setArg(date8, accountUuid);
        if (Objects.equals(true, accountStringRedisTemplate.hasKey(noActiveDayGetKey))) {
            return 0;
        }

        long prevActiveTime = this.getAccountPrevActiveTime(accountUuid);
        if (prevActiveTime == 0L) {
            return 0;
        }
        long intervalTime = DateUtil.currentTimeSeconds() - prevActiveTime;
        int noActiveDays = (int) (intervalTime / 86400);
        accountStringRedisTemplate.opsForValue().set(noActiveDayGetKey, String.valueOf(noActiveDays), 1, TimeUnit.DAYS);
        return noActiveDays;
    }

    private void activeTimeToDateCenter(String accountUuid, Long activeTime, Long createTime) {
        SoaBaseParams sbp = SoaBaseParams.fromThread();
        Integer appcode = sbp.getAppcode();

        //数据画像中心埋点
        Map<String, Object> extra = new HashMap<>();
        extra.put("account_uuid", accountUuid);
        extra.put("last_open_time", activeTime);
        extra.put("create_time", createTime);
        buryService.toDataCenter("account_last_open_device", "add", accountUuid, appcode, extra);
    }

    private void cacheActiveTime(String accountUuid, Long time) {
        String key = RedisKeyConstant.ACCOUNT_ACTIVE_TIME.setArg(accountUuid);
        accountStringRedisTemplate.opsForValue().set(key, time.toString(), 180, TimeUnit.DAYS);
    }

    /**
     * 查询用户活跃时间
     *
     * @param accountUuids
     * @return
     */
    public Map<String, String> findActiveTime(String[] accountUuids) {
        Map<String, String> result = new HashMap<>();
        List<String> keys = new ArrayList<>();
        for (String uuid : accountUuids) {
            if (StringUtils.isNotBlank(uuid)) {
                keys.add(RedisKeyConstant.ACCOUNT_ACTIVE_TIME.setArg(uuid.trim()));
            }
        }

        List<String> values = accountStringRedisTemplate.opsForValue().multiGet(keys);
        int index = 0;
        for (String uuid : accountUuids) {
            if (StringUtils.isNotBlank(uuid)) {
                result.put(uuid, StringUtils.trimToEmpty(values.get(index++)));
            }
        }
        return result;
    }

    /**
     * 处理90天未活跃用户，头像、昵称、个人简介重新审核
     */
    @Transactional
    public void review(String accountUuid) {
        if (Objects.equals(NICK_NAME_REVIEW, 1)) {
            nickNameRiskVerifyService.review(accountUuid);
        }
        if (Objects.equals(AVATAR_REVIEW, 1)) {
            accountsPhotoService.review(accountUuid);
        }
        if (Objects.equals(PERSONAL_INFO_REVIEW, 1)) {
            accountsPersonalInfoService.review(accountUuid);
        }
    }

    public AccountsMemberInfo getByUuid(String accountUuid) {
        return accountsMemberInfoDao.findByAccountUuid(accountUuid);
    }

    public List<AccountsMemberInfo> batchGetInfoByUuids(List<String> accountUuids) {
        List<AccountsMemberInfo> result = Lists.newArrayList();
        List<List<String>> partition = Lists.partition(accountUuids, 500);
        for (List<String> list : partition) {
            result.addAll(accountsMemberInfoDao.batchGetInfoByUuids(list));
        }
        return result;
    }

    public List<String> findTokenByAccountUuid(String accountUuid) {
        return accountsMemberInfoDao.findTokenByAccountUuid(accountUuid);
    }

    public List<String> findAccountUuidByToken(String token) {
        return accountsMemberInfoDao.findAccountUuidByToken(token);
    }

    public Integer countByTime(Long startTime, Long endTime) {
        return accountsMemberInfoDao.countByTime(startTime, endTime);
    }

    /**
     * 获取当前表中最大id
     * @return
     */
    public Long getMaxId(){
        return accountsMemberInfoDao.getMaxId();
    }

    /**
     * @param uuid
     * @param time
     * @param token
     * @param latitude
     * @param longitude
     * @param ip
     */
    public void updateAccountAndTokenInfo(String uuid, Long time, String token, String latitude, String longitude,
        String ip) {
        AccountAndTokenInfoDto accountAndTokenInfoDto = new AccountAndTokenInfoDto();
        accountAndTokenInfoDto.setAccountUuid(uuid);
        accountAndTokenInfoDto.setToken(token);
        accountAndTokenInfoDto.setTime(time.toString());
        accountAndTokenInfoDto.setLatitude(latitude);
        accountAndTokenInfoDto.setLongitude(longitude);
        accountAndTokenInfoDto.setIp(ip);

        HashOperations<String, Object, Object> opsForHash = accountStringRedisTemplate.opsForHash();
        String infoKey = RedisKeyConstant.ACCOUNT_TOKEN_INFO.setArg(uuid, token);
        opsForHash.putAll(infoKey, accountAndTokenInfoDto.toMap());
        accountStringRedisTemplate.expire(infoKey, 7, TimeUnit.DAYS);

        String relationKey = RedisKeyConstant.ACCOUNT_TOKEN_RELATION.setArg(uuid);
        ZSetOperations<String, String> opsForZSet = accountStringRedisTemplate.opsForZSet();
        opsForZSet.add(relationKey, token, time);
        accountStringRedisTemplate.expire(relationKey, 7, TimeUnit.DAYS);
    }

    /**
     * @param list
     * @return
     */
    public Map<String, AccountAndTokenInfoDto> listAccountLastTokenInfo(List<Map<String, String>> list) {
        Map<String, AccountAndTokenInfoDto> result = Maps.newHashMap();
        for (Map<String, String> map : list) {
            AccountAndTokenInfoDto accountAndTokenInfoDto = new AccountAndTokenInfoDto();
            String accountUuid = MapUtils.getString(map, "accountUuid", "");
            String token = MapUtils.getString(map, "token", "");
            String lastToken = null;

            String relationKey = RedisKeyConstant.ACCOUNT_TOKEN_RELATION.setArg(accountUuid);
            ZSetOperations<String, String> opsForZSet = accountStringRedisTemplate.opsForZSet();
            Set<String> tokenSet = opsForZSet.reverseRange(relationKey, 0, 1);
            for (Iterator<String> iterator = tokenSet.iterator(); iterator.hasNext();) {
                String next = iterator.next();
                if(!Objects.equals(next, token)) {
                    lastToken = next;
                    break;
                }
            }
            if (lastToken != null) {
                HashOperations<String, Object, Object> opsForHash = accountStringRedisTemplate.opsForHash();
                String infoKey = RedisKeyConstant.ACCOUNT_TOKEN_INFO.setArg(accountUuid, lastToken);
                Map<Object, Object> entries = opsForHash.entries(infoKey);
                accountAndTokenInfoDto.setAccountUuid(MapUtils.getString(entries, "accountUuid", ""));
                accountAndTokenInfoDto.setToken(MapUtils.getString(entries, "token", ""));
                accountAndTokenInfoDto.setTime(MapUtils.getString(entries, "time", ""));
                accountAndTokenInfoDto.setLatitude(MapUtils.getString(entries, "latitude", ""));
                accountAndTokenInfoDto.setLongitude(MapUtils.getString(entries, "longitude", ""));
                accountAndTokenInfoDto.setIp(MapUtils.getString(entries, "ip", ""));
            }

            result.put(accountUuid, accountAndTokenInfoDto);
        }

        return result;
    }

}
