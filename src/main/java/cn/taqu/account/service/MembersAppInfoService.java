/**
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 * @Project Name:tq-account
 * @ClassName:MembersAppInfoService.java
 * @Package:cn.taqu.account.service
 * @Description:
 * @author:laik<PERSON><PERSON>
 * @date:2015年9月21日
 */
package cn.taqu.account.service;

import cn.taqu.account.dao.MembersAppInfoDao;
import cn.taqu.account.model.MembersAppInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName:MembersAppInfoService.java
 * @author:laikunzhen
 * @date:2015年9月21日
 */
@Service
public class MembersAppInfoService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.MembersAppInfo> {

	@Autowired
	private MembersAppInfoDao membersAppInfoDao;

	public MembersAppInfo findByMemberId(Long memberId, boolean master) {
		return membersAppInfoDao.findOneByMemberId(memberId, master);
	}
	
}
