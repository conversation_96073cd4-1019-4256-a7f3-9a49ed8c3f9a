package cn.taqu.account.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsLocationInfoDao;
import cn.taqu.account.dao.TransactionWrapper;
import cn.taqu.account.dto.LocationInfoDTO;
import cn.taqu.account.model.AccountsLocationInfo;
import cn.taqu.account.model.AccountsMemberInfo;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.RedisLockUtil;
import cn.taqu.account.vo.GetAccountLocationInfoVO;
import cn.taqu.account.vo.LocationInfoVO;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户位置信息服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-10-12 15:21
 */
@Slf4j
@Service
public class AccountsLocationInfoService {

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private StringRedisTemplate accountBizStringRedisTemplate;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;
    @Autowired
    private AccountsLocationInfoDao accountsLocationInfoDao;
    /**
     * 添加锁
     */
    private static Integer LOCK_EXPIRE = 10000;

    /**
     * 保存用户位置信息
     * @param accountUuid
     * @param ip
     * @param longitude
     * @param latitude
     * @param cityId
     */
    public LocationInfoVO saveAccountLocationInfo(String accountUuid, String ip, String longitude, String latitude, Long cityId){
        Long nowTime = DateUtil.currentTimeSeconds();
        String longitudeCipher = "";
        String latitudeCipher = "";
        // ip必填
        if(StringUtils.isBlank(ip)){
            log.warn("保存用户位置信息失败，用户ip为空，uuid=[{}],ip=[{}],longitude=[{}],cityId=[{}].", accountUuid, ip, longitude, latitude, cityId);
            return null;
        }
        // 对客户端上报异常数据进行处理
        if(longitude != null && longitude.equals("0")){
            longitude = "";
        }
        if(latitude != null && latitude.equals("0")){
            latitude = "";
        }
        if(cityId != null && cityId == 0L){
            cityId = null;
        }
        Integer dataSources = 0;
        Integer useProvincialCapital = 0;
        // 如果无法获取经纬度或城市信息，则调用soa根据ip查询对应信息
        if(StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude) || null == cityId){
            LocationInfoDTO locationInfo = getLocationInfoByIpV2(ip);
            if(null == locationInfo){
                log.warn("未获取到用户位置信息，尝试通过ip获取失败,ip={}", ip);
                return null;
            }else{
                longitude = locationInfo.getLongitude();
                latitude = locationInfo.getLatitude();
                dataSources = locationInfo.getDataSources();
                useProvincialCapital = locationInfo.getUseProvincialCapital();
                try{
                    cityId = Long.parseLong(locationInfo.getCity() + "");
                }catch (Exception e){
                    log.warn("未获取到用户位置信息，尝试通过ip获取失败, ip=[{}], longitude=[{}], latitude=[{}], cityId=[{}]", ip, longitude, latitude, cityId);
                    return null;
                }
            }
        }

        Boolean addLockFlag = false;
        String addLockKey = RedisKeyConstant.ACCOUNT_LOCAITON_INFO_ADD.setArg(accountUuid);
        // 返回结果
        LocationInfoVO locationInfoVO = new LocationInfoVO();
        locationInfoVO.setIp(ip);
        locationInfoVO.setLongitude(longitude);
        locationInfoVO.setLatitude(latitude);
        locationInfoVO.setCityId(String.valueOf(cityId));
        locationInfoVO.setCreateTime(String.valueOf(nowTime));
        locationInfoVO.setUpdateTime(String.valueOf(nowTime));

        AccountsLocationInfo locationInfo = accountsLocationInfoDao.selectOneByUuid(accountUuid);
        // 2024.07.22 加密存储
        if(StringUtils.isNotBlank(longitude)) {
            longitudeCipher = EncryptUtil.encrypt(longitude);
        }
        if(StringUtils.isNotBlank(latitude)) {
            latitudeCipher = EncryptUtil.encrypt(latitude);
        }
        // accountUuid唯一，这边加一个 10秒 添加锁 防止并发添加，完成后释放锁
        try{
            if(locationInfo == null){
                log.info("location不存在, accountUuid={}", accountUuid);
                addLockFlag = redisLockUtil.lock(addLockKey, LOCK_EXPIRE);
                // 加锁失败，说明并发执行保存，直接跳过
                if(!addLockFlag){
                    return locationInfoVO;
                }
                locationInfo = new AccountsLocationInfo();
                locationInfo.setAccountUuid(accountUuid).setIp(ip).setLongitude(longitude).setLongitudeCipher(longitudeCipher)
                .setLatitude(latitude).setLatitudeCipher(latitudeCipher).setCityId(cityId).setCreateTime(nowTime)
                .setUpdateTime(nowTime).setDataSources(dataSources).setUseProvincialCapital(useProvincialCapital);
                if(!ToolsService.accountsLocationInfoSwitchWrite.isOn(true)) {
                    locationInfo.setLongitude("").setLatitude("");
                }

                // 新增
                log.info("location保存, accountUuid={}", accountUuid);
                final AccountsLocationInfo aci = locationInfo;
                TransactionWrapper.me().wrap(() -> accountsLocationInfoDao.merge(aci));
            }else{
                // 保存缓存用
                locationInfo.setIp(ip);
                locationInfo.setLongitude(longitude);
                locationInfo.setLongitudeCipher(longitudeCipher);
                locationInfo.setLatitude(latitude);
                locationInfo.setLongitudeCipher(longitudeCipher);
                locationInfo.setCityId(cityId);
                locationInfo.setUpdateTime(nowTime);

                locationInfoVO.setCreateTime(String.valueOf(locationInfo.getCreateTime()));

                // 更新
                if(ToolsService.accountsLocationInfoSwitchWrite.isOn(true)) {
                    accountsLocationInfoDao.updateLocationInfo(locationInfo.getId(), ip, longitude, longitudeCipher, latitude, latitudeCipher, cityId, locationInfo.getCreateTime(), nowTime, dataSources, useProvincialCapital);
                }else {
                    accountsLocationInfoDao.updateLocationInfo(locationInfo.getId(), ip, "", longitudeCipher, "", latitudeCipher, cityId, locationInfo.getCreateTime(), nowTime, dataSources, useProvincialCapital);
                }
            }
            // 保存到缓存
            saveLocationInfoCache(accountUuid, locationInfoVO);
        }catch (Exception e){
            log.warn("保存用户位置信息失败.", e);
            return null;
        }finally {
            // 释放add锁
            if(addLockFlag){
                redisLockUtil.unLock(addLockKey);
            }
        }
        return locationInfoVO;
    }

    /**
     * 根据ip获取地图数据
     * @param ip
     * @return
     */
    private LocationInfoDTO getLocationInfoByIpV2(String ip){
        JSONObject datas = null;
        try{
            datas = SoaService.getLocationInfoByUuidV2(ip);
        }catch (Exception e){
            log.warn("根据ip查询用户失败，ip=[{}]", ip, e);
            return null;
        }
        if(datas == null){
            return null;
        }
        LocationInfoDTO locationInfoDTO = new LocationInfoDTO();
        locationInfoDTO.setProvince(datas.getInteger("province")).setCity(datas.getInteger("city"))
                .setProvinceName(datas.getString("province_name")).setCityName(datas.getString("city_name"))
                .setLongitude(datas.getString("longitude")).setLatitude(datas.getString("latitude")).setDistrict("district")
        .setUseProvincialCapital(datas.getInteger("use_provincial_capital")).setDataSources(datas.getInteger("data_sources"));
        return locationInfoDTO;
    }

    /**
     * 根据用户uuid查询ip
     * @param accountUuid
     * @param ip
     * @return
     */
    public GetAccountLocationInfoVO getAccountLocationInfo(String accountUuid, String ip) {
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException(CodeStatus.LOCATION_INFO_NOT_FIND_ERROR);
        }

        LocationInfoVO locationInfoVO = getAccountLocationInfo(accountUuid);
        // 查询不到数据则用ip兜底
        if (locationInfoVO == null) {
            log.info("获取地理位置信息为空, 根据ip进行兜底：{}, {}", accountUuid, ip);
            if (StringUtils.isNotBlank(ip)) {
                locationInfoVO = _this().saveAccountLocationInfo(accountUuid, ip, null, null, null);
            }
            if (locationInfoVO == null) {
                log.warn("ip兜底解析失败");
                throw new ServiceException(CodeStatus.LOCATION_INFO_NOT_FIND_ERROR);
            }
        }

        AccountsMemberInfo accountsMemberInfo = accountsMemberInfoService.getByUuid(accountUuid);
        if(accountsMemberInfo == null){
            throw new ServiceException(CodeStatus.LOCATION_INFO_NOT_FIND_ERROR);
        }

        GetAccountLocationInfoVO getAccountLocationInfoVO = new GetAccountLocationInfoVO();
        BeanUtil.copyProperties(locationInfoVO, getAccountLocationInfoVO);
        getAccountLocationInfoVO.setToken(accountsMemberInfo.getToken());

        return getAccountLocationInfoVO;
    }

    /**
     * 从缓存中获取位置信息，缓存没有查库并回写缓存
     *
     * @param accountUuid
     * @return
     */
    public LocationInfoVO getAccountLocationInfo(String accountUuid) {
        LocationInfoVO locationInfoVO = getAccountLocationInfoInCache(accountUuid);
        if(locationInfoVO == null) {
            AccountsLocationInfo locationInfo = accountsLocationInfoDao.selectOneByUuid(accountUuid);
            if(locationInfo != null){
                // 缓存查不到 再查次数据库
                if(locationInfo != null){
                    locationInfoVO = new LocationInfoVO();
                    locationInfoVO.setIp(locationInfo.getIp());

                    if(ToolsService.accountsLocationInfoSwitchEncryption.isOn(true)) {
                        // 开关开启，使用加密字段
                        locationInfoVO.setLongitude(EncryptUtil.decrypt(locationInfo.getLongitudeCipher()));
                        locationInfoVO.setLatitude(EncryptUtil.decrypt(locationInfo.getLatitudeCipher()));
                    }else {
                        locationInfoVO.setLongitude(locationInfo.getLatitude());
                        locationInfoVO.setLatitude(locationInfo.getLongitude());
                    }

                    locationInfoVO.setCityId(String.valueOf(locationInfo.getCityId()));
                    locationInfoVO.setCreateTime(String.valueOf(locationInfo.getCreateTime()));
                    locationInfoVO.setUpdateTime(String.valueOf(locationInfo.getUpdateTime()));

                    saveLocationInfoCache(accountUuid, locationInfoVO);
                }
            }
        }

        return locationInfoVO;
    }

    /**
     * 从缓存中获取位置信息
     * @param accountUuid
     * @return
     */
    public LocationInfoVO getAccountLocationInfoInCache(String accountUuid) {
        String redisKey = RedisKeyConstant.ACCOUNT_LOCAITON_INFO.setArg(accountUuid);
        // TODO 双写完成之后就切成读新实例
        //String redisKey = RedisKeyConstant.ACCOUNT_LOCATION_INFO.setArg(accountUuid);

        List<Object> objects = accountStringRedisTemplate.opsForHash().multiGet(redisKey, Arrays.asList("ip", "longitude", "latitude", "cityId"));
        if(CollectionUtils.isEmpty(objects) || objects.contains(null)){
            return null;
        }
        LocationInfoVO locationInfoVO = new LocationInfoVO();
        locationInfoVO.setIp(String.valueOf(objects.get(0)));
        locationInfoVO.setLongitude(String.valueOf(objects.get(1)));
        locationInfoVO.setLatitude(String.valueOf(objects.get(2)));
        locationInfoVO.setCityId(String.valueOf(String.valueOf(objects.get(3))));
        return locationInfoVO;
    }

    public Map<String, LocationInfoVO> mGetAccountLocationInfoInCache(List<String> uuids) {
        Map<String, LocationInfoVO> map = Maps.newHashMap();
        Set<String> uuidSet = Sets.newHashSet(uuids);
        for (String uuid : uuidSet) {
            LocationInfoVO locationInfoVO = getAccountLocationInfoInCache(uuid);
            if(locationInfoVO != null) {
                map.put(uuid, locationInfoVO);
            }
        }

        return map;
    }

    /**
     * 保存缓存信息到
     * @param locationInfo
     */
    private void saveLocationInfoCache(String accountUuid, LocationInfoVO locationInfo){
        if(locationInfo == null || StringUtils.isBlank(accountUuid) || StringUtils.isBlank(locationInfo.getIp()) || StringUtils.isBlank(locationInfo.getLongitude())
                || StringUtils.isBlank(locationInfo.getLatitude()) || locationInfo.getCityId() == null){
            return;
        }
        Map<String, String> locationInfoMap = new HashMap<>();
        locationInfoMap.put("ip", locationInfo.getIp());
        locationInfoMap.put("longitude", String.valueOf(locationInfo.getLongitude()));
        locationInfoMap.put("latitude", String.valueOf(locationInfo.getLatitude()));
        locationInfoMap.put("cityId", String.valueOf(locationInfo.getCityId()));
        locationInfoMap.put("createTime", String.valueOf(locationInfo.getCreateTime()));
        locationInfoMap.put("updateTime", String.valueOf(locationInfo.getUpdateTime()));
        String redisKey = RedisKeyConstant.ACCOUNT_LOCAITON_INFO.setArg(accountUuid);
        // TODO 双写结束就去除
        accountStringRedisTemplate.opsForHash().putAll(redisKey, locationInfoMap);
        accountStringRedisTemplate.expire(redisKey, 60L, TimeUnit.DAYS);
        // 双写
        syncNewCache(accountUuid, locationInfoMap);
    }

    public AccountsLocationInfoService _this() {
        return SpringUtil.getBean(AccountsLocationInfoService.class);
    }

    /**
     * 数据迁移到新实例
     */
    public void syncNewCache(String accountUuid, Map<String, String> locationInfoMap) {
        String redisKey = RedisKeyConstant.ACCOUNT_LOCATION_INFO.setArg(accountUuid);
        accountBizStringRedisTemplate.opsForHash().putAll(redisKey, locationInfoMap);
        accountBizStringRedisTemplate.expire(redisKey, 60L, TimeUnit.DAYS);
    }

}
