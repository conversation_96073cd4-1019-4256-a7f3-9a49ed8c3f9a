/*
 * Copyright (c) 2015 taqu.cn
 * All rights reserved.
 */
package cn.taqu.account.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taqu.mp.account.client.MPAccountClient;
import com.taqu.mp.account.common.AccountChannelEnum;
import com.taqu.mp.account.constant.CamelCaseBizConstant;
import com.taqu.mp.account.constant.SnakeCaseBizConstant;
import com.taqu.mp.account.dto.AccountBindDTO;
import com.taqu.mp.account.dto.AccountLoginDTO;
import com.taqu.mp.account.dto.AccountRegisterDTO;

import cn.hutool.core.util.StrUtil;
import cn.taqu.account.client.mp.MPAccountRequest;
import cn.taqu.account.common.ActionTypeEnum;
import cn.taqu.account.common.BindMobileTypeEnum;
import cn.taqu.account.common.ClonedEnum;
import cn.taqu.account.common.GenderEnum;
import cn.taqu.account.common.ModifyGrowScoreEnum;
import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.common.WordTypeEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RegStyle;
import cn.taqu.account.dao.AccountsCertificationDao;
import cn.taqu.account.dao.AccountsInfoDao;
import cn.taqu.account.manager.AccountsManager;
import cn.taqu.account.manager.AccountsThirdPartyManager;
import cn.taqu.account.model.Accounts;
import cn.taqu.account.model.AccountsCertification;
import cn.taqu.account.model.AccountsInfo;
import cn.taqu.account.model.AccountsThirdParty;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.account.utils.ShowDialogUtil;
import cn.taqu.account.vo.AppleLoginVo;
import cn.taqu.account.vo.LoginVo;
import cn.taqu.account.vo.RegVo;
import cn.taqu.account.vo.WeChatLoginVo;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.StringUtil;
import cn.taqu.core.utils.ValidateUtil;

/**
 * 第三方登录Service
 *
 * @author:laikunzhen
 */
@Service
@Transactional
public class AccountsThirdPartyService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsThirdParty> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsThirdPartyService.class);

    /****************微信登陆配置**************/
    //@Value("${weixin.appid}")
//    @Deprecated
//    public static String weixinAppid;
    //@Value("${weixin.secret}")
//    @Deprecated
//    public static String weixinSecret;
    //@Value("${weixin.grant_type}")
//    @Deprecated
//    public static String weixinGrantType;
    //@Value("${api.weixin.base}")
//    @Deprecated
//    public static String baseUri;
    /****************微信公众号登陆配置**************/
    //@Value("${weixin.official.appid}")
//    @Deprecated
//    public static String weixinOfficialAppid;
    //@Value("${weixin.official.secret}")
//    @Deprecated
//    public static String weixinOfficialSecret;
    //@Value("${weixin.official.grant_type}")
//    @Deprecated
//    public static String weixinOfficialGrantType;
    //@Value("${api.weixin.official.base}")
//    @Deprecated
//    public static String officialBaseUri;
    @Autowired
    private AccountsManager accountsManager;
    @Autowired
    private AccountsService accountsService;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private ClickFilterService clickFilterService;
    @Autowired
    private AccountsDestroyLogService accountsDestroyLogService;
    @Autowired
    private ModifyGrowScoreService modifyGrowScoreService;
    @Autowired
    private AccountsInfoDao accountsInfoDao;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    @Autowired
    private MobileService mobileService;
//    @Autowired
//    private FlashClient flashClient;
    @Autowired
    private AccountsCertificationDao accountsCertificationDao;
    @Autowired
    private RiskService riskService;
    @Autowired
    private MPAccountClient mpAccountClient;
    @Autowired
    private AccountsThirdPartyManager accountsThirdPartyManager;

    private static String APPLE_SUB_TOKEN_REDIS_KEY = "appleLogin_${sub}";

    /**
     * 随机昵称形容词和名词redis key
     */
    private static String RANDOM_MALE_ADJ_NICKNAME_REDIS_KEY = "randomMaleAdjNicknameKey";
    private static String RANDOM_MALE_NOUN_NICKNAME_REDIS_KEY = "randomMaleNounNicknameKey";
    private static String RANDOM_FEMALE_ADJ_NICKNAME_REDIS_KEY = "randomFemaleAdjNicknameKey";
    private static String RANDOM_FEMALE_NOUN_NICKNAME_REDIS_KEY = "randomFemaleNounNicknameKey";

    /**
     * 配配随机昵称形容词和名词redis key
     */
    private static String PEIPEI_RANDOM_MALE_ADJ_NICKNAME_REDIS_KEY = "peipeiRandomMaleAdjNicknameKey";
    private static String PEIPEI_RANDOM_MALE_NOUN_NICKNAME_REDIS_KEY = "peipeiRandomMaleNounNicknameKey";
    private static String PEIPEI_RANDOM_FEMALE_ADJ_NICKNAME_REDIS_KEY = "peipeiRandomFemaleAdjNicknameKey";
    private static String PEIPEI_RANDOM_FEMALE_NOUN_NICKNAME_REDIS_KEY = "peipeiRandomFemaleNounNicknameKey";

    private static Integer BIND_MOBILE_SWITCH;

    private static List<Integer> BIND_MOBILE_CLONED;

    private static List<String> BIND_MOBILE_CHANNELS;

    public static void initRegConfig(String raw) {
        try{
            Map<String, Object> map = JsonUtils.stringToObject(raw, new TypeReference<Map<String, Object>>() {
            });
            BIND_MOBILE_SWITCH = MapUtils.getInteger(map, "bindMobileSwitch", 0);

            String clonedArr = MapUtils.getString(map, "bindMobileCloned", "0");
            BIND_MOBILE_CLONED = new ArrayList<>();
            BIND_MOBILE_CLONED.addAll(Arrays.stream(clonedArr.split("\\|")).map(Integer::valueOf).collect(Collectors.toList()));

            String channelArr = MapUtils.getString(map, "bindMobileChannels", "ALL");
            BIND_MOBILE_CHANNELS = new ArrayList<>();
            BIND_MOBILE_CHANNELS.addAll(Arrays.stream(channelArr.split("\\|")).collect(Collectors.toList()));
        }catch (Exception e){
            LOGGER.error("账号ticket配置错误", e);
        }
    }


    public static boolean regNeedBindMobile(Integer cloned, String channel) {
        LOGGER.info("regNeedBindMobile cloned:{}, channel:{}, switch:{}, config cloned:{}, config channels:{}", cloned, channel, BIND_MOBILE_SWITCH, BIND_MOBILE_CLONED, BIND_MOBILE_CHANNELS);
        return (BIND_MOBILE_SWITCH == 1) &&
                (BIND_MOBILE_CLONED.contains(0) || BIND_MOBILE_CLONED.contains(cloned)) &&
                (BIND_MOBILE_CHANNELS.contains("ALL") || BIND_MOBILE_CHANNELS.contains(channel));
    }

    /**
     * 第三方微信登陆
     */
    public Object weChatLogin(String code, int version, String smid, String extraJson) {
        AccountLoginDTO loginDTO = AccountLoginDTO.builder()
                .loginType(AccountChannelEnum.WECHAT.value())
                .code(code)
                .smDeviceId(smid)
                .extraJson(extraJson)
                .build();

        Map<String, Object> login = mpAccountClient.login(loginDTO);

        boolean needRegister = "1".equals(login.get(SnakeCaseBizConstant.NEED_REGISTER));

        if (needRegister) {// 未注册
            WeChatLoginVo weChatLoginVo = new WeChatLoginVo();
            weChatLoginVo.setAvatar(MapUtils.getString(login, CamelCaseBizConstant.AVATAR));
            weChatLoginVo.setNickname(MapUtils.getString(login, CamelCaseBizConstant.NICKNAME));
            weChatLoginVo.setUnion_id(MapUtils.getString(login, SnakeCaseBizConstant.IDP_UID));
            weChatLoginVo.setOpen_id(MapUtils.getString(login, SnakeCaseBizConstant.IDP_OID));
            weChatLoginVo.setSex_type(Integer.valueOf(MapUtils.getString(login, CamelCaseBizConstant.GENDER)));
            weChatLoginVo.setBind_info(0);// 未绑定(固定)

            // 缓存微信信息
            setWeixinUserinfoCache(MapUtils.getString(login, CamelCaseBizConstant.NICKNAME), MapUtils.getString(login, CamelCaseBizConstant.AVATAR), code);

            return weChatLoginVo;
        }

        // 登录成功
        String uuid = MapUtils.getString(login, CamelCaseBizConstant.UUID);
        String ticket = MapUtils.getString(login, CamelCaseBizConstant.TICKET);
        int freezeFlag = 0;
        if (login.containsKey(CamelCaseBizConstant.FREEZE_FLAG) &&
                Boolean.parseBoolean(MapUtils.getString(login, CamelCaseBizConstant.FREEZE_FLAG))) {
            freezeFlag = 1;
        }

        if (accountsDestroyLogService.isAccountHadValidDestroy(uuid)) {
            throw new ServiceException(CodeStatus.WECHAT_DESTROYED);
        }
        LoginVo loginVo = accountsService.loginById(ticket, uuid, version); // login by weChat
        loginVo.setUnionId(MapUtils.getString(login, SnakeCaseBizConstant.IDP_UID));
        loginVo.setFreeze_flag(freezeFlag);
        return loginVo;
    }

    /**
     * 苹果登录
     */
    @Transactional
    public Object appleLogin(String identifyToken, String authoricationCode, Integer version, String bundleId, String smid, String extraJson) {
        AccountLoginDTO loginDTO = AccountLoginDTO.builder()
                .loginType(AccountChannelEnum.APPLE.value())
                .code(authoricationCode)
                .accToken(identifyToken)
                .identify(bundleId)
                .smDeviceId(smid)
                .extraJson(extraJson)
                .build();

        Map<String, Object> login = mpAccountClient.login(loginDTO);

        boolean needRegister = "1".equals(login.get(SnakeCaseBizConstant.NEED_REGISTER));

        if (needRegister) {// 未注册
            String sub = MapUtils.getString(login, SnakeCaseBizConstant.IDP_OID);
            AppleLoginVo appleLoginVo = new AppleLoginVo();
            appleLoginVo.setBind_info(0);
            appleLoginVo.setOpen_id(sub);
            appleLoginVo.setAccess_token(identifyToken);
            String appleLoginKey = APPLE_SUB_TOKEN_REDIS_KEY.replace("${sub}", sub);
            //缓存苹果的access_token，注册的时候进行校验
            accountStringRedisTemplate.opsForValue().set(appleLoginKey, appleLoginVo.getAccess_token(), 10L, TimeUnit.MINUTES);
            return appleLoginVo;
        }

        // 登录成功
        int freezeFlag = 0;
        String uuid = MapUtils.getString(login, CamelCaseBizConstant.UUID);
        String ticket = MapUtils.getString(login, CamelCaseBizConstant.TICKET);
        if (login.containsKey(CamelCaseBizConstant.FREEZE_FLAG) &&
                Boolean.parseBoolean(MapUtils.getString(login, CamelCaseBizConstant.FREEZE_FLAG))) {
            freezeFlag = 1;
        }

        if (accountsDestroyLogService.isAccountHadValidDestroy(uuid)) {
            throw new ServiceException(CodeStatus.APPLE_DESTROYED);
        }
        LoginVo loginVo = accountsService.loginById(ticket, uuid, version);  // login by apple
        loginVo.setFreeze_flag(freezeFlag);

        // 有绑定手机号取手机号，没有则取微信、QQ权重一次递减
        if (StringUtils.isNotBlank(loginVo.getMobile())) {
            loginVo.setOpenId("");
            loginVo.setUnionId("");
        } else {
            List<AccountsThirdParty> thirdPartys = accountsThirdPartyManager.findValidByAccountUuid(uuid);
            for (AccountsThirdParty item : thirdPartys) {
                if ("WeChat".equals(item.getType())) {
                    loginVo.setUnionId(item.getUnion_id());
                    loginVo.setOpenId("");
                    break;
                } else if ("QQ".equals(item.getType())) {
                    loginVo.setOpenId(item.getOpen_id());
                    loginVo.setUnionId("");
                } else {
                    loginVo.setOpenId("");
                    loginVo.setUnionId("");
                }
            }
        }
        return loginVo;
    }

    /**
     * 三方注册再检查
     *
     * @param type    类型
     * @param openId  开放id
     * @param unionId 联盟id
     * @param token   令牌
     */
    private  void thirdRegReRegisterCheck(String type, String openId, String unionId, String token){
        RegStyle regStyle = RegStyle.valueOf(type);
        if (RegStyle.WeChat.equals(regStyle)){
            accountsService.reregistrationCheck(regStyle,unionId);
        }else if (RegStyle.Visitor.equals(regStyle)){
            accountsService.reregistrationCheck(regStyle,token);
        }else {
            accountsService.reregistrationCheck(regStyle,openId);
        }
    };

    public RegVo register(String type, String openId, String unionId,
                          String nickname, String token, Integer platformId,
                          Integer appcode, Integer cloned, Long appVersion,
                          String placeCode, Integer sexType, String avatar,
                          int version, Long birth, String mobile,
                          String shumeiDeviceId, String quickAccessToken,
                          Integer age, Integer defaultNickName,
                          String extraJson) {
        int regType = validedType(type);
        if (!clickFilterService.isValidClick("third_reg", token)) {
            throw new ServiceException(CodeStatus.OPER_TIMES_TOO_OFTEN);
        }
        //注册限制
        accountsService.registerLimit(type, null, openId);

        this.isBindThirdParty(type, openId);

        // 再注册检查
        thirdRegReRegisterCheck(type, openId, unionId, token);

        // 手机号不为空，则绑定手机号
        boolean needBindMobile = regNeedBindMobile(SoaBaseParams.fromThread().getCloned(), SoaBaseParams.fromThread().getChannel());
        String phone = null;
        if (needBindMobile && (StringUtils.isNotBlank(quickAccessToken) || StringUtils.isNotBlank(mobile))) {
            if (StringUtils.isNotBlank(quickAccessToken)) {
                Map<String, Object> mpRes = mpAccountClient.getPhoneNumByFlash(quickAccessToken);
                mobile = MapUtils.getString(mpRes, "phone", null);
            } else {
                // 手机验证码绑定，需要校验
                String isVerifyKey = RedisKeyConstant.IS_REG_VERIFY.setArg(mobile);
                String verifyResult = accountStringRedisTemplate.opsForValue().get(isVerifyKey);
                if (StringUtils.isBlank(verifyResult)) {
                    //注册验证码校验过后在指定时间内还没有完善资料，则要求用户返回重新输入验证码
                    throw new ServiceException(CodeStatus.REG_VERIFY_EXPIRE);
                }
            }
            phone = mobile;
        }

        AccountRegisterDTO registerDTO = AccountRegisterDTO.builder()
                .regType(regType)
                .idpOid(openId)
                .ipdUid(unionId)
                .smDeviceId(shumeiDeviceId)
                .phone(phone)
                .extraJson(extraJson)
                .build();

        // 通过【Account/reg】注册账号
        RegVo regVo = accountsService.reg(phone, nickname, null, token, platformId, appcode, cloned, appVersion, placeCode, sexType, avatar, RegStyle.getByName(type, RegStyle.WeChat), version, birth, defaultNickName, registerDTO, shumeiDeviceId, true);
        String accountUuid = regVo.getUuid();
        this.bindAccountsThirdParty(accountUuid, type, openId, unionId);

        if (StringUtils.isNotBlank(phone)) {
            regVo.setIsDoBindMobile(CommConst.YES_1);
        }

        //使用完后删除
        accountStringRedisTemplate.delete(RedisKeyConstant.WECHAT_ACCESS_TOKEN.setArg(openId));
        return regVo;
    }

    private String getMobileByQuickAccessToken(String quickAccessToken, String mobile, String loginType) {
//        if (StringUtils.isNotBlank(quickAccessToken) && Objects.equals(AccountsThirdParty.VISITOR, loginType)) {
//            mobile = flashClient.getMobileByFlash(quickAccessToken);
//            if (StringUtils.isBlank(mobile) || !ValidateUtil.isMobile(mobile)) {
//                LOGGER.warn("闪验绑定手机号返回手机号:{} 格式错误, accessToken:{}", mobile, quickAccessToken);
//                throw new ServiceException(CodeStatus.FLASH_MOBILE_ERROR);
//            }
//        }
        return mobile;
    }

    /**
     * 校验第三方账号是否绑定过
     *
     * @param type
     * @param openId
     */
    private void isBindThirdParty(String type, String openId) {
        AccountsThirdParty accountsThirdParty;
        // 所有第三方类型统一用openid判断 从此再无unionid (peace&love 愿世间再无bug)
        if (AccountsThirdParty.TYPE_WECHAT.equals(type) || AccountsThirdParty.TYPE_WECHAT_MONEY.equals(type)) {
            accountsThirdParty = accountsThirdPartyManager.findValidByOpenId(openId, AccountsThirdParty.TYPE_WECHAT);
        } else {
            accountsThirdParty = accountsThirdPartyManager.findValidByOpenId(openId, type);
        }

        if (accountsThirdParty != null) {
            if (accountsDestroyLogService.isAccountHadValidDestroy(accountsThirdParty.getAccount_uuid())) {
                switch (type) {

                    case AccountsThirdParty.TYPE_WECHAT:
                    case AccountsThirdParty.TYPE_WECHAT_OFFICIAL:
                        throw new ServiceException(CodeStatus.WECHAT_DESTROYED);
                    case AccountsThirdParty.TYPE_QQ:
                        throw new ServiceException(CodeStatus.QQ_DESTROYED);
                    case AccountsThirdParty.TYPE_WEIBO:
                        throw new ServiceException(CodeStatus.WEIBO_DESTROYED);
                    case AccountsThirdParty.TYPE_APPLE:
                        throw new ServiceException(CodeStatus.APPLE_DESTROYED);
                    case AccountsThirdParty.VISITOR:
                        throw new ServiceException(CodeStatus.VISITOR_DESTROYED);
                }
            }
            if (AccountsThirdParty.TYPE_WECHAT_MONEY.equals(type)) {
                throw new ServiceException(CodeStatus.WECHAT_BEEN_USED);
            }
            throw new ServiceException("is_binded", type + "账号已经被绑定");
        }
    }

    /**
     * 绑定第三方账号
     * (只有在type(类型)为微信的时候union_id必填)
     * actionType 为bind操作时，判断是否绑定用的是findByOpen_id方法
     *
     * @return
     */
    private void bindAccountsThirdParty(String accountUuid, String type, String openId, String unionId) {
        if (StringUtils.isNotBlank(openId) && openId.startsWith("\"") && openId.endsWith("\"")) {
            logger.warn("input invalid openId: {}", openId);
            openId = StrUtil.removeAll(openId, "\"");
        }

        if (StringUtils.isNotBlank(unionId) && unionId.startsWith("\"") && unionId.endsWith("\"")) {
            logger.warn("input invalid unionId: {}", unionId);
            unionId = StrUtil.removeAll(unionId, "\"");
        }

        accountsInfoService.guestTicketExpire(accountUuid);
        AccountsThirdParty accountsThirdParty = new AccountsThirdParty();
        accountsThirdParty.setAccount_uuid(accountUuid);
        accountsThirdParty.setCreate_time(DateUtil.currentTimeSeconds());
        accountsThirdParty.setOpen_id(openId);
        accountsThirdParty.setStatus(AccountsThirdParty.STATUS_BINDING);
        accountsThirdParty.setType(type);
        accountsThirdParty.setUnion_id(unionId);
        accountsThirdParty.setUpdate_time(DateUtil.currentTimeSeconds());
        accountsThirdPartyManager.save(accountsThirdParty);
    }

    /**
     * 第三方qq及微博登陆
     */
    @Transactional
    public LoginVo login(String type, String openId, int version, String mobile, String shumeiDeviceId, String quickAccessToken, String extraJson) {
        LoginVo loginVo;

        AccountLoginDTO loginDTO = AccountLoginDTO.builder()
                .loginType(AccountChannelEnum.QQ.value())
                .code(openId)
                .smDeviceId(shumeiDeviceId)
                .extraJson(extraJson)
                .build();

        Map<String, Object> login = mpAccountClient.login(loginDTO);

        logger.info("login map: {}", login);

        boolean needRegister = "1".equals(login.get(SnakeCaseBizConstant.NEED_REGISTER));

        if (needRegister) {
            logger.info("qq注册！");
            loginVo = new LoginVo();
            loginVo.setBind_info(0);// 0未绑定
            return loginVo;
        }

        int freezeFlag = 0;
        String accountUuid = MapUtils.getString(login, CamelCaseBizConstant.UUID);
        String ticket = MapUtils.getString(login, CamelCaseBizConstant.TICKET);
        if (login.containsKey(CamelCaseBizConstant.FREEZE_FLAG) &&
                Boolean.parseBoolean(MapUtils.getString(login, CamelCaseBizConstant.FREEZE_FLAG))) {
            freezeFlag = 1;
        }

        if (accountsDestroyLogService.isAccountHadValidDestroy(accountUuid)) {
            throw new ServiceException(Objects.equals(type, AccountsThirdParty.TYPE_QQ) ? CodeStatus.QQ_DESTROYED : CodeStatus.WEIBO_DESTROYED);
        }

        // 通过【Account/loginById】登陆
        loginVo = accountsService.loginById(ticket, accountUuid, version); // login by qq
        loginVo.setOpenId(openId);
        loginVo.setFreeze_flag(freezeFlag);
        //TODO 此处只使用 is_check_mobile
        if(1 == loginVo.getIs_check_mobile() && AccountsThirdParty.VISITOR.equals(type)){
            //todo 文案由产品提供
            throw new ServiceException("「为了更好的社交体验，请使用绑定手机号登录」");
        }

        mobile = getMobileByQuickAccessToken(quickAccessToken, mobile,type);

        if(StringUtils.isNotBlank(mobile)){
            Boolean isNeedBind = accountsService.isNeedVisitorBindMobile(type,mobile);
            if(isNeedBind){
                //是游客,并且未绑定手机号,则直接进行绑定
                mobileService.processBindMobile(loginVo.getUuid(), null, mobile, openId, shumeiDeviceId, "no continueLogin", BindMobileTypeEnum.BIND, Boolean.TRUE);
                loginVo.setIsDoBindMobile(CommConst.YES_1);
                loginVo.setMobile(mobile);
            }else{
                LOGGER.info("该手机号已经绑定了其他账号,手机号为:{}",mobile);
            }
        }

        return loginVo;
    }

     /**
     * 绑定哪个第三方以及是否设置了密码
     *
     * @param accountUuid
     */
    public Map<String, Object> isRegister(String accountUuid) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Accounts accounts = accountsManager.getByUuid(accountUuid);
        // 查中台手机号，可以直接用
        String mobile = accounts.getMobile();

        // 通过手机号来判断判断字段
        if (mobile != null && ValidateUtil.isMobile(mobile)) {// 通过手机来判断返回数组中的mobile，如果有值则返回相应手机号码，但是中间四位需要为，例如156****5347，同时返回数组需要包含is_bind_mobile，有mobile则为1，否则为0
            resultMap.put("mobile", StringUtil.mobileSecret(mobile));
            resultMap.put("is_bind_mobile", CommConst.YES_1);
            resultMap.put("is_check_mobile", CommConst.YES_1);
        } else {
            resultMap.put("mobile", "");
            resultMap.put("is_bind_mobile", CommConst.NO_0);
            resultMap.put("is_check_mobile", CommConst.NO_0);
        }

        if (StringUtils.isNotBlank(accounts.getAccount_password())) {
            resultMap.put("is_set_password", CommConst.YES_1);
        } else {
            resultMap.put("is_set_password", CommConst.NO_0);
        }

        String email = accounts.getEmail();
        String emailSuffix = null;
        String emailPrefix = null;

        if (email != null && ValidateUtil.isEmail(email)) {
            emailSuffix = email.substring(email.indexOf("@"), email.length());
            // @前如果超过2位则仅保留2位，如果只有两位则保留一位，如果只有一位则为*
            emailPrefix = email.substring(0, email.indexOf("@"));
            if (emailPrefix.length() > 2) {
                resultMap.put("email", emailPrefix.substring(0, 2) + emailSuffix);
            } else if (emailPrefix.length() == 2) {
                resultMap.put("email", emailPrefix.substring(0, 1) + emailSuffix);
            } else {
                resultMap.put("email", "*" + emailSuffix);
            }
        } else {
            resultMap.put("email", "");
        }

        resultMap.putAll(getAccountsThirdPartyBindStatus(accountUuid));

        return resultMap;
    }

    public Map<String, Object> isRegisterFromMP(String accountUuid) {
        Map<String, Object> resultMap = new HashMap<>();
        Accounts accounts = accountsManager.getByUuid(accountUuid, false);
        Map<String, Object> thirdLoginInfo = mpAccountClient.getAccountBindInfo(accountUuid);
        boolean hasPassword = MapUtils.getBoolean(thirdLoginInfo, "havePassword", false);
        String mobile = MapUtils.getString(thirdLoginInfo, CamelCaseBizConstant.PHONE);
        String thirdPartDOJsons = MapUtils.getString(thirdLoginInfo, "thirdPartDOList", "");
        List<Map<String, Integer>> accountsThirdParties = new ArrayList<>();
        if (StringUtils.isNotBlank(thirdPartDOJsons)) {
            accountsThirdParties = JsonUtils.stringToObject(thirdPartDOJsons, new TypeReference<List<Map<String, Integer>>>() {
            });
        }

        if (mobile != null && ValidateUtil.isMobile(mobile)) {// 通过手机来判断返回数组中的mobile，如果有值则返回相应手机号码，但是中间四位需要为，例如156****5347，同时返回数组需要包含is_bind_mobile，有mobile则为1，否则为0
            resultMap.put("mobile", StringUtil.mobileSecret(mobile));
            resultMap.put("is_bind_mobile", CommConst.YES_1);
            resultMap.put("is_check_mobile", CommConst.YES_1);
        } else {
            resultMap.put("mobile", "");
            resultMap.put("is_bind_mobile", CommConst.NO_0);
            resultMap.put("is_check_mobile", CommConst.NO_0);
        }
        resultMap.put("is_set_password", hasPassword ? CommConst.YES_1 : CommConst.NO_0);

        String email = accounts.getEmail();
        String emailSuffix = null;
        String emailPrefix = null;

        if (email != null && ValidateUtil.isEmail(email)) {
            emailSuffix = email.substring(email.indexOf("@"), email.length());
            // @前如果超过2位则仅保留2位，如果只有两位则保留一位，如果只有一位则为*
            emailPrefix = email.substring(0, email.indexOf("@"));
            if (emailPrefix.length() > 2) {
                resultMap.put("email", emailPrefix.substring(0, 2) + emailSuffix);
            } else if (emailPrefix.length() == 2) {
                resultMap.put("email", emailPrefix.substring(0, 1) + emailSuffix);
            } else {
                resultMap.put("email", "*" + emailSuffix);
            }
        } else {
            resultMap.put("email", "");
        }

        resultMap.put("is_bind_wechat", CommConst.NO_0);
        resultMap.put("is_bind_wechat_official", CommConst.NO_0);
        resultMap.put("is_bind_qq", CommConst.NO_0);
        resultMap.put("is_bind_weibo", CommConst.NO_0);

        if (CollectionUtils.isNotEmpty(accountsThirdParties)) {
            for (Map<String, Integer> map : accountsThirdParties) {
                Integer type = map.get("type");
                Integer status = map.get("status");

                if (!status.equals(CommConst.YES_1)) {
                    continue;
                }

                AccountChannelEnum channelType = AccountChannelEnum.getAccountChannel(type);

                switch (channelType) {
                    case WECHAT:
                        resultMap.put("is_bind_wechat", CommConst.YES_1);// 1已绑定 0未绑定(返回前端使用,暂这样处理,下同)
                        break;
                    case QQ:
                        resultMap.put("is_bind_qq", CommConst.YES_1);
                        break;
                    case WEIBO:
                        resultMap.put("is_bind_weibo", CommConst.YES_1);
                        break;
                }
            }
        }
        return resultMap;
    }

    /**
     * 绑定哪个第三方以及是否设置了密码
     *
     * @param type
     * @param accountUuid
     * @return
     */
    public Map<String, Object> deBind(String type, String accountUuid) {
        int thirdType = this.validedType(type);

        Accounts accounts = accountsManager.getByUuid(accountUuid, false);

        if (StringUtils.isBlank(accounts.getAccount_password()) && StringUtils.isBlank(accounts.getMobile())) {
            throw new ServiceException(CodeStatus.NEED_BIND_MOBILE_AND_PASSWORD);
        }

        List<AccountsInfo> accountsInfos = accountsInfoDao.queryByProp("account_uuid", accountUuid, Collections.singleton("is_check_mobile"));
        AccountsInfo accountsInfo;
        if (accountsInfos != null && accountsInfos.size() > 0) {
            accountsInfo = accountsInfos.get(0);
            if (!accountsInfo.getIs_check_mobile().equals(AccountsInfo.CHECK_MOBILE_BIND)) {
                throw new ServiceException(CodeStatus.NEED_CHECK_MOBILE);
            }
            if (StringUtils.isBlank(accounts.getAccount_password())) {
                throw new ServiceException(CodeStatus.NEED_SET_PASSWORD);
            }
        }

        if (MPAccountRequest.isRequestMP()) {
            mpAccountClient.unbind(accountUuid, thirdType);
        }

        int updateCount = accountsThirdPartyManager.updateForDebind(AccountsThirdParty.STATUS_UNBINDING, DateUtil.currentTimeSeconds(), accountUuid, type, AccountsThirdParty.STATUS_BINDING);
        if (updateCount > 0) {
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("response", CommConst.THIRDPARTY_DEBIND_SUCCESS);// 解绑成功
            return data;
        }

        throw new ServiceException("账号未绑定第三方,解绑失败！");
    }

    public void deBindAll(String accountUuid) {
        accountsThirdPartyManager.updateForDebind(AccountsThirdParty.STATUS_UNBINDING, DateUtil.currentTimeSeconds(), accountUuid, AccountsThirdParty.TYPE_QQ, AccountsThirdParty.STATUS_BINDING);
        accountsThirdPartyManager.updateForDebind(AccountsThirdParty.STATUS_UNBINDING, DateUtil.currentTimeSeconds(), accountUuid, AccountsThirdParty.TYPE_WECHAT, AccountsThirdParty.STATUS_BINDING);
        accountsThirdPartyManager.updateForDebind(AccountsThirdParty.STATUS_UNBINDING, DateUtil.currentTimeSeconds(), accountUuid, AccountsThirdParty.TYPE_WECHAT_OFFICIAL, AccountsThirdParty.STATUS_BINDING);
        accountsThirdPartyManager.updateForDebind(AccountsThirdParty.STATUS_UNBINDING, DateUtil.currentTimeSeconds(), accountUuid, AccountsThirdParty.TYPE_WEIBO, AccountsThirdParty.STATUS_BINDING);
        accountsThirdPartyManager.updateForDebind(AccountsThirdParty.STATUS_UNBINDING, DateUtil.currentTimeSeconds(), accountUuid, AccountsThirdParty.TYPE_APPLE, AccountsThirdParty.STATUS_BINDING);
        accountsThirdPartyManager.updateForDebind(AccountsThirdParty.STATUS_UNBINDING, DateUtil.currentTimeSeconds(), accountUuid, AccountsThirdParty.VISITOR, AccountsThirdParty.STATUS_BINDING);
    }

    /**
     * 绑定第三方
     *
     * @param type
     * @param openId
     * @param accountUuid
     * @throws IOException
     * @throws JsonProcessingException
     * @throws JSONException
     */
    public void bind(String type, String openId, String accountUuid) {
        // 验证type是否有效
        int thirdType = this.validedType(type);
        LOGGER.info("type={}.openId={}.accountUuid={}", type, openId, accountUuid);

        if(!type.equals(AccountsThirdParty.TYPE_WECHAT) && riskService.checkOpenIdRisk(type, openId)){
            ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
        }

        AccountBindDTO bindDTO = AccountBindDTO.builder()
                .uuid(accountUuid)
                .type(thirdType)
                .code(openId)
                .build();

        Map<String, Object> bind = mpAccountClient.bind(bindDTO);
        String unionId = null;

        if (type.equals(AccountsThirdParty.TYPE_WECHAT)) {
            openId = MapUtils.getString(bind, SnakeCaseBizConstant.IDP_OID);
            unionId = MapUtils.getString(bind, SnakeCaseBizConstant.IDP_UID);
            String nickname = MapUtils.getString(bind, CamelCaseBizConstant.NICKNAME);
            String photoUrl = MapUtils.getString(bind, CamelCaseBizConstant.AVATAR);

            if(riskService.checkOpenIdRisk(type, openId)){
                ShowDialogUtil.throwShowDialog(CodeStatus.OPENID_ALREADY_BAN.getReasonPhrase(), "知道了", "", "咨询客服", "m=me&a=service");
            }

            setWeixinUserinfoCache(nickname, photoUrl, openId);
        }

        // 绑定第三方账号
        this.bindAccountsThirdParty(accountUuid, type, openId, unionId);
        //同时推送给社区一条行为队列
        sendModifyGrowScoreMq(type, accountUuid);

        // 2020.03.05 更新用户位置
        accountsService.updateAccountExtraInfo(accountUuid);
    }

    private void sendModifyGrowScoreMq(String type, String accountUuid) {
        switch (type) {
            case AccountsThirdParty.TYPE_WECHAT:
                modifyGrowScoreService.sendModifyGrowScoreMq(accountUuid, ModifyGrowScoreEnum.BIND_WECHAT);
                break;
            case AccountsThirdParty.TYPE_WEIBO:
                modifyGrowScoreService.sendModifyGrowScoreMq(accountUuid, ModifyGrowScoreEnum.BIND_WEIBO);
                break;
            case AccountsThirdParty.TYPE_QQ:
                modifyGrowScoreService.sendModifyGrowScoreMq(accountUuid, ModifyGrowScoreEnum.BIND_QQ);
                break;
        }
    }

    /**
     * 验证登录类型是否有效，只允许微信、qq、微博
     *
     * @param type 类型
     * @Title:validedType
     * @author:huangyuehong
     * @Date:2016年5月20日 下午2:08:54
     */
    private int validedType(String type) {
        int thirdType = 0;
        switch (type) {
            case AccountsThirdParty.TYPE_WECHAT:
            case AccountsThirdParty.TYPE_WECHAT_OFFICIAL:
                thirdType = AccountChannelEnum.WECHAT.value();
                break;
            case AccountsThirdParty.TYPE_WEIBO:
                throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
//                thirdType = AccountChannelEnum.WEIBO.value();
//                break;
            case AccountsThirdParty.TYPE_QQ:
                thirdType = AccountChannelEnum.QQ.value();
                break;
            case AccountsThirdParty.TYPE_APPLE:
                thirdType = AccountChannelEnum.APPLE.value();
                break;
            case AccountsThirdParty.VISITOR:
                throw new ServiceException(CodeStatus.API_OFFLINE_TIPS1);
//                break;
            default:
                throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }
        return thirdType;
    }

    public Map<String, Object> handelThirdLoginResult(LoginVo loginVo, int version) {
        //TODO 此处只查询 is_check_mobile is_bind_mobile
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("nickname", StringUtil.nullToEmptyWithTrim(loginVo.getNickname()));
        resultMap.put("is_bind_mobile", StringUtil.nullNumberToEmptyString(loginVo.getIs_bind_mobile()));
        resultMap.put("is_check_mobile", StringUtil.nullNumberToEmptyString(loginVo.getIs_check_mobile()));
        resultMap.put(AccountsInfoService.getTqcoinName(version), StringUtil.nullNumberToEmptyString(loginVo.getTqcoin()));
        resultMap.put("account_actor", StringUtil.nullNumberToEmptyString(loginVo.getAccount_actor()));
        resultMap.put("account_level", StringUtil.nullNumberToEmptyString(loginVo.getAccount_level()));
        resultMap.put("medal_name", StringUtil.nullToEmptyWithTrim(loginVo.getHonor_name()));
        resultMap.put("ticket_id", StringUtil.nullToEmptyWithTrim(loginVo.getTicket_id()));
        resultMap.put("account_type", StringUtil.nullNumberToEmptyString(loginVo.getAccount_type()));
        resultMap.put("age", StringUtil.nullNumberToEmptyString(loginVo.getAge()));
        resultMap.put("sex_type", StringUtil.nullNumberToEmptyString(loginVo.getSex_type()));
        resultMap.put("avatar", StringUtil.nullToEmptyWithTrim(loginVo.getAvatar()));
        resultMap.put("account_uuid", StringUtil.nullToEmptyWithTrim(loginVo.getUuid()));
        resultMap.put("email", StringUtil.nullToEmptyWithTrim(loginVo.getEmail()));
        resultMap.put("mobile", StringUtil.mobileSecret(loginVo.getMobile()));
        resultMap.put("driver_level", StringUtils.defaultIfBlank(loginVo.getDriver_level(), "C"));
        resultMap.put("kilometer", StringUtil.nullNumberToEmptyString(loginVo.getKilometer()));
        //resultMap.put("group_type", String.valueOf(loginVo.getGroup_type()));
        resultMap.put("bind_info", "1");
        resultMap.put("register_time",StringUtil.nullNumberToEmptyString(loginVo.getRegister_time()));
        resultMap.put("is_do_bind_mobile",loginVo.getIsDoBindMobile());

        // 2021.04.28 新增去认证
        resultMap.put("to_certification", StringUtil.nullNumberToEmptyString(loginVo.getToCertification()));
        resultMap.put("dialog_content",StringUtil.nullToEmptyWithTrim(loginVo.getDialogContent()));

        // 网安喊你绑定你手机
        resultMap.put("wa_say_to_bind_mobile", StringUtil.nullObjectToEmptyString(loginVo.getWaSayToBindMobile()));

        resultMap.put("to_real_person_certification", "0");
        resultMap.put("to_real_person_certification_dialog", "");

        resultMap.put("freeze_flag", loginVo.getFreeze_flag() != null ? loginVo.getFreeze_flag() : 0);
        resultMap.put("risk_status", loginVo.getRisk_status() != null ? loginVo.getRisk_status() : 0);

        // 2022.03.31 新增去真人认证
        if(Objects.equals(SoaBaseParams.fromThread().getAppcode(), CommConst.APPCODE_TAQU)
                && !Objects.equals(SoaBaseParams.fromThread().getCloned(), ClonedEnum.QIALIAO.getCode())){
            resultMap.put("to_real_person_certification", StringUtil.nullObjectToEmptyString(loginVo.getToRealPersonCertification()));
            resultMap.put("to_real_person_certification_dialog", StringUtil.nullObjectToEmptyString(loginVo.getToRealPersonCertificationDialog()));
            if(!VersionSwitchService.SWITCH_20220302.isGeVersion() && Objects.equals(MapUtils.getString(resultMap, "to_real_person_certification", "0"), CommConst.YES_1.toString())){
                throw new ServiceException("提示：请更新至最新版本");
            }
        }
        //若果是版主显示版主头衔图片
        if (loginVo.getAccount_actor() != null && loginVo.getAccount_actor() > 0) {
            resultMap.put("account_medal", StringUtil.nullToEmptyWithTrim(loginVo.getMedal_url()));
        }
        // 版主则获取所管理的版块列表
        if (loginVo.getGroup_type() != null && loginVo.getGroup_type() == 2) {
            List<Long> category_ids = loginVo.getCategory_ids();
            List<String> list = Lists.newArrayList();
            for (int i = 0; i < category_ids.size(); i++) {
                Long id = category_ids.get(i);
                list.add(String.valueOf(id));
            }
            resultMap.put("group_circle", list);
        }

        // 2020.03.27 更新用户位置
        accountsService.updateAccountExtraInfo(StringUtil.nullToEmptyWithTrim(loginVo.getUuid()));

        return resultMap;
    }

    //查找最后一个未绑定的类型
    public AccountsThirdParty findLastestOneByUuid(String accountUuid){
        return accountsThirdPartyManager.findLastestOneByUuid(accountUuid);
    }

    /**
     * 获取随机昵称（客户端接口）
     * @param gender
     * @return
     */
    public String getRandomNickname(Integer gender){
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        Integer cloned = soaBaseParams.getCloned();
        if(ClonedEnum.PEIPEI.getCode().equals(cloned)){
            // 需求文档：https://hb-wiki.yuque.com/staff-er4ql2/atbfuf/nearsz
            // 随机昵称过一次是否重复判断
            String peipeiRandomNickname = getPeipeiRandomNickname(gender);
            if(StringUtils.isNotBlank(peipeiRandomNickname)){
                Boolean existNickName = accountsService.isExistNickName(peipeiRandomNickname);
                if(existNickName) {
                    // 重复再取一次
                    LOGGER.info("随机昵称【{}】重复", peipeiRandomNickname);
                    peipeiRandomNickname = getPeipeiRandomNickname(gender);
                    if(StringUtils.isNotBlank(peipeiRandomNickname)){
                        return peipeiRandomNickname;
                    }
                }else {
                    return peipeiRandomNickname;
                }
            }
        }

        return getTaquRandomNickname(gender);
    }

    /**
     * 获取随机昵称（后台操作）
     */
    public String getRandomNicknameV2(String accountUuid){
        Map<String, Object> infoMap = accountsInfoService.singleGetInfo(accountUuid, new String[]{"sex_type", "login_cloned"});
        Integer gender = MapUtils.getInteger(infoMap, "sex_type", 1);
        Integer cloned = MapUtils.getInteger(infoMap, "login_cloned");
        if(ClonedEnum.PEIPEI.getCode().equals(cloned)){
            String peipeiRandomNickname = getPeipeiRandomNickname(gender);
            if(StringUtils.isNotEmpty(peipeiRandomNickname)){
                return peipeiRandomNickname;
            }
        }

        return getTaquRandomNickname(gender);
    }

    public String getTaquRandomNickname(Integer gender){
        if(GenderEnum.MALE.getValue().equals(gender)){
            String adjNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(RANDOM_MALE_ADJ_NICKNAME_REDIS_KEY)).orElse("幸福的");
            String nounNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(RANDOM_MALE_NOUN_NICKNAME_REDIS_KEY)).orElse("茄子");
            return adjNickname + nounNickname;
        }
        String adjNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(RANDOM_FEMALE_ADJ_NICKNAME_REDIS_KEY)).orElse("快乐的");
        String nounNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(RANDOM_FEMALE_NOUN_NICKNAME_REDIS_KEY)).orElse("苹果");
        return adjNickname + nounNickname;
    }
    /**
     * 获取配配随机昵称
     * @param gender
     * @return
     */
    public String getPeipeiRandomNickname(Integer gender){
        if(GenderEnum.MALE.getValue().equals(gender)){
            String adjNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(PEIPEI_RANDOM_MALE_ADJ_NICKNAME_REDIS_KEY)).orElse("");
            String nounNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(PEIPEI_RANDOM_MALE_NOUN_NICKNAME_REDIS_KEY)).orElse("");
            return adjNickname + nounNickname;
        }
        String adjNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(PEIPEI_RANDOM_FEMALE_ADJ_NICKNAME_REDIS_KEY)).orElse("");
        String nounNickname = Optional.ofNullable(accountStringRedisTemplate.opsForSet().randomMember(PEIPEI_RANDOM_FEMALE_NOUN_NICKNAME_REDIS_KEY)).orElse("");
        return adjNickname + nounNickname;

    }


    /**
     * 添加和删除set里的昵称
     * @param list
     * @param wordTypeEnum
     * @param actionTypeEnum
     */
    public void updateRandomNickname(List<String> list, WordTypeEnum wordTypeEnum, ActionTypeEnum actionTypeEnum){
        String wordRedisKey;
        switch (wordTypeEnum){
            case MALE_ADJ:
                wordRedisKey = RANDOM_MALE_ADJ_NICKNAME_REDIS_KEY;
                break;
            case MALE_NOUN:
                wordRedisKey = RANDOM_MALE_NOUN_NICKNAME_REDIS_KEY;
                break;
            case FEMALE_ADJ:
                wordRedisKey = RANDOM_FEMALE_ADJ_NICKNAME_REDIS_KEY;
                break;
            case FEMALE_NOUN:
                wordRedisKey = RANDOM_FEMALE_NOUN_NICKNAME_REDIS_KEY;
                break;
            default:
                throw new ServiceException("type_error", "类型" + wordTypeEnum + "暂不支持");
        }

        processUpdateRandomNickname(actionTypeEnum,wordRedisKey,list);
    }

    public void updatePeipeiRandomNickname(List<String> list, WordTypeEnum wordTypeEnum, ActionTypeEnum actionTypeEnum){
        String wordRedisKey;

        switch (wordTypeEnum){
            case MALE_ADJ:
                wordRedisKey = PEIPEI_RANDOM_MALE_ADJ_NICKNAME_REDIS_KEY;
                break;
            case MALE_NOUN:
                wordRedisKey = PEIPEI_RANDOM_MALE_NOUN_NICKNAME_REDIS_KEY;
                break;
            case FEMALE_ADJ:
                wordRedisKey = PEIPEI_RANDOM_FEMALE_ADJ_NICKNAME_REDIS_KEY;
                break;
            case FEMALE_NOUN:
                wordRedisKey = PEIPEI_RANDOM_FEMALE_NOUN_NICKNAME_REDIS_KEY;
                break;
            default:
                throw new ServiceException("type_error", "类型" + wordTypeEnum + "暂不支持");
        }

        processUpdateRandomNickname(actionTypeEnum,wordRedisKey,list);
    }

    public void processUpdateRandomNickname(ActionTypeEnum actionTypeEnum,String wordRedisKey,List<String> list){

        if(ActionTypeEnum.ADD.equals(actionTypeEnum)){
            accountStringRedisTemplate.opsForSet().add(wordRedisKey,list.toArray(new String[list.size()]));
        }else if(ActionTypeEnum.DELETE.equals(actionTypeEnum)){
            accountStringRedisTemplate.opsForSet().remove(wordRedisKey,list.toArray(new String[list.size()]));
        }
    }

    public Integer countByOpenIdAndType(String openId,String type){
        return accountsThirdPartyManager.countByOpenIdAndType(openId,type);
    }

    /**
     * 绑定的第三方信息
     * @param accountUuid 用户uuid
     *
     * @return map
     */
    public Map<String, Object> getAccountsThirdPartyBindStatus(String accountUuid) {
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("is_bind_wechat", CommConst.NO_0);
        resultMap.put("is_bind_wechat_official", CommConst.NO_0);
        resultMap.put("is_bind_qq", CommConst.NO_0);
        resultMap.put("is_bind_weibo", CommConst.NO_0);

        List<Map<String, Integer>> accountsThirdParties = thirdLoginInfo(accountUuid);
        if (CollectionUtils.isNotEmpty(accountsThirdParties)) {
            for (Map<String, Integer> map : accountsThirdParties) {
                Integer type = map.get("type");
                Integer status = map.get("status");

                if (!status.equals(CommConst.YES_1)) {
                    continue;
                }

                AccountChannelEnum channelType = AccountChannelEnum.getAccountChannel(type);

                switch (channelType) {
                    case WECHAT:
                        resultMap.put("is_bind_wechat", CommConst.YES_1);// 1已绑定 0未绑定(返回前端使用,暂这样处理,下同)
                        break;
                    case QQ:
                        resultMap.put("is_bind_qq", CommConst.YES_1);
                        break;
                    case WEIBO:
                        resultMap.put("is_bind_weibo", CommConst.YES_1);
                        break;
                }
            }
        }

        return resultMap;
    }

    private List<Map<String, Integer>> thirdLoginInfo(String uuid) {
        Map<String, Object> thirdLoginInfo = mpAccountClient.getAccountBindInfo(uuid);
        String thirdPartDOJsons = MapUtils.getString(thirdLoginInfo, "thirdPartDOList", "");
        if (StringUtils.isNotBlank(thirdPartDOJsons)) {
            return JsonUtils.stringToObject(thirdPartDOJsons, new TypeReference<List<Map<String, Integer>>>() {
            });
        }
        return new ArrayList<>();
    }

    /**
     * @param accountUuid
     * @return
     */
    public Map<String, Object> getAccountWeChatOpenId(String accountUuid) {
        Map<String, Object> userMap = new HashMap<>();
        String openId = "";
        String nickname = "";
        String avatar = "";
        AccountsThirdParty accountsThirdParty = accountsThirdPartyManager.findValidByAccountUuidAndType(accountUuid, AccountsThirdParty.TYPE_WECHAT);
        if(accountsThirdParty != null) {
            openId = accountsThirdParty.getOpen_id();
            String key = RedisKeyConstant.WECHAT_USER_INFO.setArg(openId);
            nickname = StringUtil.nullObjectToEmptyString(accountStringRedisTemplate.opsForHash().get(key, "nickname"));
            avatar = StringUtil.nullObjectToEmptyString(accountStringRedisTemplate.opsForHash().get(key, "head_img_url"));
        }
        userMap.put("openId", openId);
        userMap.put("nickname", nickname);
        userMap.put("head_img_url", avatar);

        logger.info("account: {}, third_is_null: {}, info: {}", accountUuid, accountsThirdParty == null, JSON.toJSONString(userMap));
        return userMap;
    }

    private void setWeixinUserinfoCache(String nickname, String avatar, String openid) {
        logger.info("cache wechat info: {}, {}, {}", nickname, avatar, openid);

//        if (StringUtils.isBlank(nickname)) {
//            return;
//        }
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("nickname", nickname);
        resultMap.put("head_img_url", avatar);
        String key = RedisKeyConstant.WECHAT_USER_INFO.setArg(openid);
        accountStringRedisTemplate.opsForHash().putAll(key, resultMap);
        accountStringRedisTemplate.expire(key, 15, TimeUnit.DAYS);
    }

    /**
     * 获取账号绑定信息
     * @param accountUuid
     * @return
     */
    public Map<String, Object> getBindInfoByUuid(String accountUuid) {
        // 是否uuid有效
        Boolean hasAccountUuid = accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(accountUuid));
        if(!hasAccountUuid){
            Map<String, Object> result = new HashMap<>();
            result.put("account_exist", hasAccountUuid);
            result.put("account_name", "");
            result.put("account_uuid", accountUuid);
            result.put("mobile", "");
            result.put("identity_no", "");
            result.put("real_name", "");
            result.put("reward_account", "");
            result.put("third_party", new ArrayList<>());
        }
        Map<String, Object> infoMap = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, new String[]{"account_name", UuidInfoField.MOBILE, "account_status"}, "1", false, false).get(accountUuid);
        List<AccountsThirdParty> thirdParties = accountsThirdPartyManager.findValidByAccountUuid(accountUuid);
        List<Map<String, String>> thirds = new ArrayList<>();
        for (AccountsThirdParty thirdParty: thirdParties) {
            Map<String, String> one = new HashMap<>();
            one.put("type", thirdParty.getType());
            one.put("open_id", thirdParty.getOpen_id());
            thirds.add(one);
        }
        AccountsCertification accountsCertification = accountsCertificationDao.getByAccountUuid(accountUuid);
        Map<String, Object> result = new HashMap<>();
        result.put("account_name", MapUtils.getString(infoMap, "account_name", ""));
        result.put("account_uuid", accountUuid);
        result.put("mobile", MapUtils.getString(infoMap, UuidInfoField.MOBILE, ""));
        result.put("account_status", MapUtils.getString(infoMap, "account_status", "1"));
        if(accountsCertification != null && accountsCertification.getIsChecked() == 1){
            String identityNo = accountsCertification.getIdentityNo();
            String realName = accountsCertification.getRealName();
            String rewardAccount = accountsCertification.getRewardAccount();

            if(ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
                identityNo = EncryptUtil.decrypt(accountsCertification.getIdentityNoCipher());
                realName = EncryptUtil.decrypt(accountsCertification.getRealNameCipher());
            }
            if(ToolsService.rewardAccountSwitchEncryption.isOn(true)) {
                rewardAccount = EncryptUtil.decrypt(accountsCertification.getRewardAccountCipher());
            }
            result.put("identity_no", identityNo);
            result.put("real_name", realName);
            result.put("reward_account", StringUtils.isBlank(rewardAccount) ? "" : rewardAccount);
        }else{
            result.put("identity_no", "");
            result.put("real_name", "");
            result.put("reward_account", "");
        }
        result.put("third_party", thirds);
        result.put("account_exist", hasAccountUuid);
        return result;
    }

    public Map<String, Map<String, Object>> batchGetAccountWeChatOpenId(String[] accountUuids) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        Arrays.stream(accountUuids)
                .forEach(uuid -> {
                    resultMap.put(uuid, getAccountWeChatOpenId(uuid));
                });
        return resultMap;
    }

    /**
     * @param code
     * @return
     */
    public Map<String, Object> queryWechatUserInfo(String code) {
        Map<String, Object> wechatUserInfo = mpAccountClient.queryWechatUserInfo(code);
        return wechatUserInfo;
    }

}
