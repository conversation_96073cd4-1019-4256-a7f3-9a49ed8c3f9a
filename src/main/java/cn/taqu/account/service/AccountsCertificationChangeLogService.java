package cn.taqu.account.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.manager.AccountsCertificationChangeLogManager;
import cn.taqu.account.manager.AccountsCertificationManager;
import cn.taqu.account.model.AccountsCertificationChangeLog;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 14:24
 */
@Service
@Slf4j
@Transactional
public class AccountsCertificationChangeLogService {

    @Autowired
    private AccountsCertificationChangeLogManager accountsCertificationChangeLogManager;
    @Autowired
    private AccountsInfoService accountsInfoService;
    @Autowired
    private AccountsCertificationService accountsCertificationService;
    @Autowired
    private AccountsCertificationManager accountsCertificationManager;
    @Autowired
    private SoaService soaService;
    
    /**
     * 实名变更记录
     * @return
     */
    public List<Map<String, Object>> getChangeLogList(Integer cloned, String identityNo){
        List<Map<String, Object>> result = new ArrayList<>();
        // 身份证号为空 直接返回空数组
        if(StringUtils.isBlank(identityNo)){
            return result;
        }
        List<String> uuids = accountsCertificationManager.listRelateAccountByIdentityNo(cloned, identityNo, null);
        if(CollectionUtils.isEmpty(uuids)){
            uuids = new ArrayList<>();
        }

        List<String> accountUuidList = accountsCertificationChangeLogManager.listAccountUuid(cloned, identityNo, AccountsCertificationChangeLog.LogTypeEnum.BIND.getValue(), 50);
        if(CollectionUtils.isEmpty(accountUuidList)){
            return result;
        }
        Integer queryLimit = 20;
        if(accountUuidList.size() > queryLimit){
            accountUuidList = accountUuidList.subList(0, queryLimit);
        }

        uuids.addAll(accountUuidList);

        // 对uuid相同去重
        uuids = duplicateRemovalByUuid(uuids);
        
        Map<String, Map<String, Object>> infos = accountsInfoService.getInfoByUuid(uuids.toArray(new String[]{}), new String[]{"account_status", "sex_type", "account_name", "create_time"}, "1", true, false);
        Map<String, Object> mobileMap = soaService.getMobileByUuidsMP(uuids);
        for (String accountUuid: uuids) {
            Map<String, Object> one = new HashMap<>();
            Map<String, Object> infoMap = infos.get(accountUuid);
            one.put("account_uuid", accountUuid);
            one.put("account_name", MapUtils.getString(infoMap, "account_name", ""));
            one.put("account_create_time", MapUtils.getLong(infoMap, "create_time", 0L));
            one.put("account_status", MapUtils.getInteger(infoMap, "account_status", 1));
            one.put("mobile", StringUtil.mobileSecret(MapUtils.getString(mobileMap, accountUuid, "")));
            result.add(one);
        }
        return result;
    }
    
    /**
     * 实名变更记录
     * @return
     */
    public List<Map<String, Object>> getChangeLogListByAccountUuid(Integer cloned, String accountUuid){
        Map<String, String> map = accountsCertificationService.getByUuids(new String[]{accountUuid}).get(accountUuid);
        String isChecked = MapUtils.getString(map, "is_checked", "");
        String identityNo = MapUtils.getString(map, "identity_no", "");
        if(!Objects.equals(isChecked, "1") || StringUtils.isBlank(identityNo)) {
            throw new ServiceException(CodeStatus.ACCOUNT_UNBIND_IDENTITY_NO);
        }
        return getChangeLogList(cloned, identityNo);
    }

    private List<String> duplicateRemovalByUuid(List<String> uuids){
        if(CollectionUtils.isEmpty(uuids)){
            return uuids;
        }
        List<String> newList = new ArrayList<>();
        for (String uuid : uuids) {
            if(!newList.contains(uuid)){
                newList.add(uuid);
            }
        }
        return newList;
    }

    /**
     * 保存记录
     * @param certificationChangeLog
     */
    public void save(AccountsCertificationChangeLog certificationChangeLog) {
        Long now = DateUtil.currentTimeSeconds();
        certificationChangeLog.setCreateTime(now).setUpdateTime(now);
        
        // 加密
        String realName = certificationChangeLog.getRealName();
        String identityNo = certificationChangeLog.getIdentityNo();
        Map<String,String> originalInfoMap = Maps.newHashMap();
        originalInfoMap.put("real_name", realName);
        originalInfoMap.put("identity_no", identityNo);
        Map<String,String> encryptMap = EncryptUtil.batchEncrypt(originalInfoMap);
        Map<String,String> digestMap = EncryptUtil.batchSm3(originalInfoMap);
        certificationChangeLog.setRealNameCipher(encryptMap.get("real_name"));
        certificationChangeLog.setRealNameDigest(digestMap.get("real_name"));
        certificationChangeLog.setIdentityNoCipher(encryptMap.get("identity_no"));
        certificationChangeLog.setIdentityNoDigest(digestMap.get("identity_no"));
        
        // 根据开关判断是否不写入
        if(!ToolsService.accountsCertificationChangeLogSwitchWrite.isOn(true)) {
            log.info("accounts_certification_change_log不写加密字段");
            certificationChangeLog.setRealName("");
            certificationChangeLog.setIdentityNo("");
        }
        
        accountsCertificationChangeLogManager.merge(certificationChangeLog);
    }
}
