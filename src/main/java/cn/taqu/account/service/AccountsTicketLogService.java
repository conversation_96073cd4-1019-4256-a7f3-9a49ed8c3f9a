package cn.taqu.account.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.dao.AccountsTicketLogDao;
import cn.taqu.account.model.AccountsTicketLog;
import cn.taqu.core.utils.DateUtil;

/**
 * 用户ticket变化日志
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class AccountsTicketLogService extends cn.taqu.core.orm.base.BaseServiceImpl<java.lang.Long, cn.taqu.account.model.AccountsTicketLog> {

    @Autowired
    private AccountsTicketLogDao accountsTicketLogDao;

    public void addLog(String accountUuid, String oldTicket, String newTicket, String deviceToken, Integer optType, String clientIp,Integer appcode) {
        AccountsTicketLog log = new AccountsTicketLog();
        log.setAccount_uuid(accountUuid);
        log.setOld_ticket(oldTicket);
        log.setNew_ticket(newTicket);
        log.setDevice_token(deviceToken);
        log.setOpt_type(optType);
        log.setCreate_time(DateUtil.currentTimeSeconds());
        log.setClient_ip(clientIp);
        log.setAppcode(appcode == null ? 1 : appcode);
        accountsTicketLogDao.merge(log);
    }

}
