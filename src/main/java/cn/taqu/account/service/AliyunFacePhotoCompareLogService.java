package cn.taqu.account.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.dao.AliyunFacePhotoCompareLogDao;
import cn.taqu.account.model.AliyunFacePhotoCompareLog;

@Service
@Transactional
public class AliyunFacePhotoCompareLogService {

    @Autowired
    private AliyunFacePhotoCompareLogDao aliyunFacePhotoCompareLogDao;

    public AliyunFacePhotoCompareLog merge(AliyunFacePhotoCompareLog log) {
        log = aliyunFacePhotoCompareLogDao.merge(log);
        return log;
    }

    public void merge(List<AliyunFacePhotoCompareLog> logs) {
        aliyunFacePhotoCompareLogDao.merge(logs);
    }
    
}
