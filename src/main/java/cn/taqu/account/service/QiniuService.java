package cn.taqu.account.service;

import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.JsonObject;
import com.qiniu.common.QiniuException;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;

import cn.taqu.account.bo.QiniuCallbackResponse;
import cn.taqu.account.bo.UploadToken;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.vo.PictureInfoVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.StringUtil;

/**
 * 七牛相关操作
 * <AUTHOR>
 * @date 2021/02/05
 */
@Service
public class QiniuService {
    private static final Logger LOGGER = LoggerFactory.getLogger(QiniuService.class);

	private static String QINIU_ACCESS_KEY = "N9v6m6QoLvNJR0NUieJC-l7KdCI7YKroIzKelwdW";
	private static String QINIU_SECRET_KEY = "R4htDnXTIaWNEfJQQYKDiqwszlVRpKIHbuIptZJe";

	public static void initConfig(String qiniuDefaultJson) {
		Map<String, String> config = null;
		try {
			config = JsonUtils.stringToObject(qiniuDefaultJson, new TypeReference<Map<String, String>>() {});
		} catch (Exception e) {
			LOGGER.error("初始化七牛配置失败。json=【{}】", qiniuDefaultJson, e);
			return;
		}
		if(config == null || !config.containsKey("access_key") || !config.containsKey("secret_key")) {
			LOGGER.error("初始化七牛配置失败。json=【{}】", qiniuDefaultJson);
			return;
		}

		QINIU_ACCESS_KEY = config.get("access_key");
		QINIU_SECRET_KEY = config.get("secret_key");
	}

	/**
	 * 从网络获取图片转存到七牛云，访问格式：https://avatar01.jiaoliuqu.com/${key}，目前使用该域名，后续可能改变
	 * @param bucket
	 * @param folderName
	 * @param tempPhotoUrl
	 * @return
	 */
	public String uploadPic(String bucket, String folderName, String tempPhotoUrl){
		Configuration cfg = new Configuration(Zone.zone0());
		String key = folderName+"/"+System.currentTimeMillis()+ "_"+UUID.randomUUID().toString()+".jpg";
		Auth auth = Auth.create(QINIU_ACCESS_KEY, QINIU_SECRET_KEY);
		BucketManager bucketManager = new BucketManager(auth, cfg);
		try {
			bucketManager.fetch(tempPhotoUrl, bucket, key);
			return key;
		} catch (QiniuException ex1) {
			LOGGER.warn("转存图片到七牛云失败,文件夹名称={}",folderName,ex1);
			try {
				bucketManager.fetch(tempPhotoUrl, bucket, key);
				return key;
			} catch (QiniuException ex2) {
				LOGGER.error("重试转存图片到七牛云失败,文件夹名称={}",folderName,ex2);
			}
		}
		return null;
	}

	/**
	 * 获取私有图片地址
	 * @param photoUrl
	 * @param expire
	 * @return
	 */
	public String getPrivateDownloadUrl(String photoUrl, Long expire){
		photoUrl = AvatarHandleService.getAvatarOfSavePhoto(photoUrl);
		photoUrl = CommConst.AVATAR_SRC_HOST + photoUrl;
		Auth auth = Auth.create(QINIU_ACCESS_KEY, QINIU_SECRET_KEY);
		return auth.privateDownloadUrl(photoUrl, expire);
	}

	/**
     * 上传文件
     * @param fileBytes 上传文件，经base64编码。必填
     * @param bucketType bucket类型，如avatar、mv。必填
     * @param host 域名
     * @param folderName 文件夹名称，可选
     * @return
     */
    public PictureInfoVo uploadFile(byte[] fileBytes, String bucketType, String host, String folderName){
        String fileName = System.currentTimeMillis()+ "_"+UUID.randomUUID().toString()+".jpg";
        return uploadFile(fileBytes, bucketType, host, folderName, fileName);
    }
    
    /**
     * 上传文件
     * @param fileBytes 上传文件，经base64编码。必填
     * @param bucketType bucket类型，如avatar、mv。必填
     * @param host 域名
     * @param folderName 文件夹名称，可选
     * @return
     */
    public PictureInfoVo uploadFile(byte[] fileBytes, String bucketType, String host, String folderName, String fileName){
        if(StringUtils.isNotBlank(folderName)){
            fileName = folderName + "/" + fileName;
        }
        
        UploadToken uploadToken = getPictureUploadToken(bucketType);
        
        QiniuCallbackResponse response = uploadFile(uploadToken, fileName, fileBytes);
        
        PictureInfoVo pictureInfoVo = new PictureInfoVo();
        pictureInfoVo.setUrl(host + fileName);
        pictureInfoVo.setWidth(Objects.equals(response.getW(), CommConst.STRING_NULL) ? 0 : StringUtil.toInteger(response.getW(), 0));
        pictureInfoVo.setHeight(Objects.equals(response.getH(), CommConst.STRING_NULL) ? 0 : StringUtil.toInteger(response.getH(), 0));
        pictureInfoVo.setSize(StringUtils.isBlank(response.getSize()) ? 0 : Long.valueOf(response.getSize()));
        pictureInfoVo.setImg_name(fileName);
        
        if(pictureInfoVo.getWidth() == 0) {
            // TODO 做到监控里
            LOGGER.error("上传图片返回结果缺少width，需要观察日志确认是否上传成功");
        }
        return pictureInfoVo;
    }
 
    public QiniuCallbackResponse uploadFile(UploadToken uploadToken, String fileName, byte[] bs) {
        Zone z = Zone.autoZone();
        Configuration c = new Configuration(z);
        UploadManager uploadManager = new UploadManager(c);
        try {
            Response response = uploadManager.put(bs, fileName, uploadToken.getToken());
            if (!response.isOK()) {
                LOGGER.warn("上传文件到七牛失败, error:{}", response.toString());
                throw new ServiceException("upload_error", String.format("上传文件:%s到七牛失败:%s", fileName, response.toString()));
            }
            LOGGER.info("图片上传七牛成功，response={}", response.bodyString());
            
            QiniuCallbackResponse callbackResponse = JsonUtils.stringToObject2(response.bodyString(), QiniuCallbackResponse.class);
            return callbackResponse;
        } catch (QiniuException e) {
            LOGGER.warn("上传文件到七牛失败, " + e.response, e);
            throw new ServiceException(e);
        }
    }
    
    /**
     * 获取上传图片token
     *
     * @param bucketType 七牛空间名
     */
    public UploadToken getPictureUploadToken(String bucketType) {
        try {
            
            String bucketName = bucketType;
            // TODO 此处有问题，应该根据bucket使用domain
            String domain = CommConst.AVATAR_HOST;
            // TODO 此处有问题，应该根据bucket使用domain
            String srcDomain = CommConst.AVATAR_SRC_HOST;
            if(StringUtils.isBlank(srcDomain)) {
                srcDomain = domain;
            }

            Long expires = 3600L;//【单位：(秒)s】
            // 上传策略
            StringMap putPolicy = new StringMap();
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("bucket", "$(bucket)");
            jsonObject.addProperty("key", "$(key)");
            jsonObject.addProperty("ext", "$(ext)");
            jsonObject.addProperty("name", "${fname}");
            jsonObject.addProperty("size", "$(fsize)");
            jsonObject.addProperty("w", "$(imageInfo.width)");
            jsonObject.addProperty("h", "$(imageInfo.height)");
            jsonObject.addProperty("url", domain + "$(key)");
            jsonObject.addProperty("srcUrl", srcDomain + "$(key)");

            putPolicy.put("returnBody", jsonObject.toString());

            Auth auth = Auth.create(QINIU_ACCESS_KEY, QINIU_SECRET_KEY);
            UploadToken uploadToken = new UploadToken();
            String token = auth.uploadToken(bucketName, null, expires, putPolicy);
            uploadToken.setToken(token);
            uploadToken.setDeadline(DateUtil.currentTimeSeconds() + expires);
            uploadToken.setDomain(domain);
            return uploadToken;
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            LOGGER.warn("获取uploadToken异常", e);
            throw new ServiceException(e.getMessage());
        }
    }
    
    /**
     * 上传活体底图
     * 
     * @param fileBytes
     * @return
     */
    public PictureInfoVo uploadFileLiveFace(byte[] fileBytes){
        String fileName = System.currentTimeMillis()+ "_"+UUID.randomUUID().toString()+".jpg";
        // 需要开关
        if(ToolsService.uploadAvatarBucketLiveFaceSwitch.isOn(true)) {
            uploadFile(fileBytes, CommConst.AVATAR_BUCKET, CommConst.AVATAR_HOST, CommConst.FOLDER_LIVE_FACE, fileName);
        }
        
        PictureInfoVo uploadFilePrivate = uploadFile(fileBytes, CommConst.PRIVATE_AVATAR_BUCKET_TYPE, CommConst.ACCOUNT_PRIVATE_SRC_HOST, CommConst.FOLDER_LIVE_FACE, fileName);
        return uploadFilePrivate;
    }
    
    /**
     * 上传实名底图
     * 
     * @param fileBytes
     * @return
     */
    public PictureInfoVo uploadFileCertification(byte[] fileBytes){
        String fileName = System.currentTimeMillis()+ "_"+UUID.randomUUID().toString()+".jpg";
        // 需要开关
        if(ToolsService.uploadAvatarBucketCertificationSwitch.isOn(true)) {
            uploadFile(fileBytes, CommConst.AVATAR_BUCKET, CommConst.AVATAR_HOST, CommConst.FOLDER_CERTIFICATION, fileName);
        }
        PictureInfoVo uploadFilePrivate = uploadFile(fileBytes, CommConst.PRIVATE_AVATAR_BUCKET_TYPE, CommConst.ACCOUNT_PRIVATE_SRC_HOST, CommConst.FOLDER_CERTIFICATION, fileName);
        return uploadFilePrivate;
    }
}
