package cn.taqu.account.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.taqu.account.common.HomeCoverStatusEnum;
import cn.taqu.account.common.RecordAdminLogActionEnum;
import cn.taqu.account.common.RiskSafeHitTypeEnum;
import cn.taqu.account.constant.CommConst;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsHomeCoverDao;
import cn.taqu.account.dao.AccountsHomeCoverLogDao;
import cn.taqu.account.dto.RecordAdminLogContentDto;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.model.AccountsHomeCover;
import cn.taqu.account.model.AccountsHomeCoverLog;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 09:49
 */
@Deprecated
@Slf4j
@Service
@Transactional
public class AccountsHomeCoverService {

    @Autowired
    private MessageService messageService;
    @Autowired
    private AccountsHomeCoverDao accountsHomeCoverDao;
    @Autowired
    private AccountsHomeCoverLogDao accountsHomeCoverLogDao;

    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;

    private static List<Integer> CHECKED_STATUS = Arrays.asList(HomeCoverStatusEnum.PASS.getValue(),
            HomeCoverStatusEnum.PUNISH.getValue(),
            HomeCoverStatusEnum.DELETE.getValue(),
            HomeCoverStatusEnum.PASS_TO_PUNISH.getValue(),
            HomeCoverStatusEnum.PASS_TO_DELETE.getValue());


    /**
     * 处理审核通过
     * @param homeCover
     * @param operator
     */
    private void handleCheckPass(AccountsHomeCover homeCover, String operator, Integer reCheck) {
        Long now = DateUtil.currentTimeSeconds();
        accountsHomeCoverDao.updateCheckPass(homeCover.getId(), homeCover.getPhotoUrl(),now, operator);
        AccountsHomeCoverLog accountsHomeCoverLog = new AccountsHomeCoverLog();
        accountsHomeCoverLog.setAccountUuid(homeCover.getAccountUuid())
                .setHomeCoverId(homeCover.getId()).setHomeCoverStatus(HomeCoverStatusEnum.PASS.getValue())
                .setPhotoUrl(homeCover.getPhotoUrl()).setPassPhotoUrl(homeCover.getPassPhotoUrl())
                .setRiskLevel(homeCover.getRiskLevel()).setRiskDescription(homeCover.getRiskDescription())
                .setImgCheckOrderNo(homeCover.getImgCheckOrderNo()).setImgQualityOrderNo(homeCover.getImgQualityOrderNo())
                .setCheckTime(now).setOperator(operator).setCreateTime(now).setUpdateTime(now);
        accountsHomeCoverLogDao.merge(accountsHomeCoverLog);
        Map<String, String> caches = new HashMap<>();
        caches.put("home_cover", homeCover.getPhotoUrl());
        caches.put("pass_home_cover", homeCover.getPhotoUrl());
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(homeCover.getAccountUuid()), caches);
        removeCheckZSet(homeCover.getId());
        removeCheckLock(homeCover.getId(), operator);
        try{
            RecordAdminLogContentDto recordAdminLogContent = new RecordAdminLogContentDto();
            recordAdminLogContent.setContent_id(accountsHomeCoverLog.getAccountUuid());
            recordAdminLogContent.setContent("通过用户主页封面,url=" + homeCover.getPhotoUrl());
            String operatorToken = SoaBaseParams.fromThread().getToken();
            SoaService.batchRecordAdminLogByAction(RecordAdminLogActionEnum.HOME_COVER_DELETE.getValue(), Arrays.asList(recordAdminLogContent), operatorToken);
        }catch (Exception e){
            log.warn("用户封面审核忽略推送失败.uuid={}.url={}", homeCover.getAccountUuid(), homeCover.getPhotoUrl(), e);
        }

    }

    /**
     * 处罚
     * @param homeCover
     * @param operator
     */
    private void handleCheckPunish(AccountsHomeCover homeCover, String operator, Integer reCheck) {
        Long now = DateUtil.currentTimeSeconds();
        accountsHomeCoverDao.removeById(homeCover.getId());
        Integer homeCoverStatus = homeCover.getHomeCoverStatus();
        if(reCheck == 1 && HomeCoverStatusEnum.PASS.getValue().equals(homeCoverStatus)){
            // 查询出上一条已通过的日志
            List<AccountsHomeCoverLog> homeCoverLogs = accountsHomeCoverLogDao.listByCoverId(homeCover.getId());
            for (AccountsHomeCoverLog homeCoverLog :homeCoverLogs) {
                homeCoverLog.setHomeCoverStatus(HomeCoverStatusEnum.PASS_TO_PUNISH.getValue());
            }
            if(CollectionUtils.isNotEmpty(homeCoverLogs)){
                accountsHomeCoverLogDao.merge(homeCoverLogs);
            }
        }
        AccountsHomeCoverLog accountsHomeCoverLog = new AccountsHomeCoverLog();
        accountsHomeCoverLog.setAccountUuid(homeCover.getAccountUuid())
                .setHomeCoverId(homeCover.getId()).setHomeCoverStatus(HomeCoverStatusEnum.PUNISH.getValue())
                .setPhotoUrl(homeCover.getPhotoUrl()).setPassPhotoUrl(homeCover.getPassPhotoUrl())
                .setRiskLevel(homeCover.getRiskLevel()).setRiskDescription(homeCover.getRiskDescription())
                .setImgCheckOrderNo(homeCover.getImgCheckOrderNo()).setImgQualityOrderNo(homeCover.getImgQualityOrderNo())
                .setCheckTime(now).setOperator(operator).setCreateTime(now).setUpdateTime(now);
        accountsHomeCoverLogDao.merge(accountsHomeCoverLog);
        Map<String, String> caches = new HashMap<>();
        caches.put("home_cover", "");
        caches.put("pass_home_cover", "");
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(homeCover.getAccountUuid()), caches);
        removeCheckZSet(homeCover.getId());
        removeCheckLock(homeCover.getId(), operator);
        try{
            sendHomeCoverIllSystemMessage(homeCover.getAccountUuid());
            SoaService.updateQiniuFileStatus(AvatarHandleService.getAvatarOfSavePhoto(homeCover.getPhotoUrl()), CommConst.QINIU_FILE_STATUS_DISENABLE);
        }catch (Exception e){
            log.warn("用户封面审核处罚推送失败.uuid={}.url={}", homeCover.getAccountUuid(), homeCover.getPhotoUrl(), e);
        }
    }

    /**
     * 删除
     * @param homeCover
     * @param operator
     */
    private void handleCheckDelete(AccountsHomeCover homeCover, String operator, Integer reCheck) {
        Long now = DateUtil.currentTimeSeconds();
        Integer homeCoverStatus = homeCover.getHomeCoverStatus();
        accountsHomeCoverDao.removeById(homeCover.getId());
        if(reCheck == 1 && HomeCoverStatusEnum.PASS.getValue().equals(homeCoverStatus)){
            // 查询出上一条已通过的日志
            List<AccountsHomeCoverLog> homeCoverLogs = accountsHomeCoverLogDao.listByCoverId(homeCover.getId());
            for (AccountsHomeCoverLog homeCoverLog :homeCoverLogs) {
                homeCoverLog.setHomeCoverStatus(HomeCoverStatusEnum.PASS_TO_DELETE.getValue());
            }
            if(CollectionUtils.isNotEmpty(homeCoverLogs)){
                accountsHomeCoverLogDao.merge(homeCoverLogs);
            }
        }
        AccountsHomeCoverLog accountsHomeCoverLog = new AccountsHomeCoverLog();
        accountsHomeCoverLog.setAccountUuid(homeCover.getAccountUuid())
                .setHomeCoverId(homeCover.getId()).setHomeCoverStatus(HomeCoverStatusEnum.DELETE.getValue())
                .setPhotoUrl(homeCover.getPhotoUrl()).setPassPhotoUrl(homeCover.getPassPhotoUrl())
                .setRiskLevel(homeCover.getRiskLevel()).setRiskDescription(homeCover.getRiskDescription())
                .setImgCheckOrderNo(homeCover.getImgCheckOrderNo()).setImgQualityOrderNo(homeCover.getImgQualityOrderNo())
                .setCheckTime(now).setOperator(operator).setCreateTime(now).setUpdateTime(now);
        accountsHomeCoverLogDao.merge(accountsHomeCoverLog);
        Map<String, String> caches = new HashMap<>();
        caches.put("home_cover", "");
        caches.put("pass_home_cover", "");
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(homeCover.getAccountUuid()), caches);
        removeCheckZSet(homeCover.getId());
        removeCheckLock(homeCover.getId(), operator);
        try{
            sendHomeCoverIllSystemMessage(homeCover.getAccountUuid());
            SoaService.updateQiniuFileStatus(AvatarHandleService.getAvatarOfSavePhoto(homeCover.getPhotoUrl()), CommConst.QINIU_FILE_STATUS_DISENABLE);
            RecordAdminLogContentDto recordAdminLogContent = new RecordAdminLogContentDto();
            recordAdminLogContent.setContent_id(accountsHomeCoverLog.getAccountUuid());
            recordAdminLogContent.setContent("删除用户主页封面,url=" + homeCover.getPhotoUrl());
            String operatorToken = SoaBaseParams.fromThread().getToken();
            SoaService.batchRecordAdminLogByAction(RecordAdminLogActionEnum.HOME_COVER_DELETE.getValue(), Arrays.asList(recordAdminLogContent), operatorToken);
        }catch (Exception e){
            log.warn("用户封面审核删除推送失败.uuid={}.url={}", homeCover.getAccountUuid(), homeCover.getPhotoUrl(), e);
        }
    }

    /**
     * 移除锁
     * @param homeCoverId
     * @param operatorToken
     */
    private void removeCheckLock(Long homeCoverId, String operatorToken) {
        // 加到 图片 => 操作员
        accountStringRedisTemplate.delete(RedisKeyConstant.ACCOUNT_HOME_COVER_CHECK_CURRENT.setArg(homeCoverId + ""));
        // 加到 操作员 => 图片
        accountStringRedisTemplate.delete(RedisKeyConstant.ACCOUNT_HOME_COVER_CHECK_OPERATOR.setArg(operatorToken));
    }

    /**
     * 删除
     * @param id
     */
    private void removeCheckZSet(Long id){
        String redisKey = RedisKeyConstant.ACCOUNT_HOME_COVER_CHECK.getPattern();
        accountStringRedisTemplate.opsForZSet().remove(redisKey, id.toString());
    }


    /**
     * 发送封面违规小秘书
     * @param accountUuid
     */
    public void sendHomeCoverIllSystemMessage(String accountUuid) {
        JSONArray contentJa = new JSONArray();
        JSONObject contentMap = new JSONObject();
        String info="您的封面涉嫌违规己被清除，违规原因可能有：涉黄低俗、留其他联系方式、政治敏感、图片过于模糊、打广告、未成年、二维码等，请重新上传，若再次违规将有可能被封号！" +
                "\n" +
                "\n" +
                "%s";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "重新上传封面");
        contentReplace.put("r", "m=personal&a=homepage&operation=setHomeCover&isMyself=1");
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "您的封面涉嫌违规己被清除");

        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");

        contentJa.add(contentMap);

        messageService.systemNotice(accountUuid, contentJa, "系统消息", "system_link_text", 1);
    }

    /**
     * 获取审核记录
     * @param accontUuid
     * @return
     */
    public List<Map<String, Object>> getHomeCoverCheckLogByUuid(String accontUuid) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<AccountsHomeCoverLog> homeCoverLogListReviewCheck = accountsHomeCoverLogDao.getHomeCoverLogListReviewCheck(accontUuid, CHECKED_STATUS);
        for (AccountsHomeCoverLog coverLog : homeCoverLogListReviewCheck) {
            Map<String, Object> one = new HashMap<>();
            one.put("check_time", coverLog.getCheckTime());
            one.put("photo_url", coverLog.getPhotoUrl());
            one.put("operator", coverLog.getOperator());
            one.put("home_cover_status", coverLog.getHomeCoverStatus());
            result.add(one);
        }
        return result;
    }

    public void audit(RiskSafeCheckResponseDTO dto) {
        log.info("[风控安全审核]用户封面审核回调,accountUuid:{},bizId:{},operator:{},hitType:{},blockReason:{}", dto.getSenderUuid(), dto.getBizId(), dto.getOperator(), dto.getHitTypeCode(), dto.getBlockReason());
        AccountsHomeCover homeCover = accountsHomeCoverDao.getById(Long.valueOf(dto.getBizId()));
        if (homeCover == null) {
            log.info("[风控安全审核]审核封面数据.数据不存在.id={}", dto.getBizId());
            return;
        }
        RiskSafeHitTypeEnum riskSafeHitTypeEnum = dto.getRiskSafeHitType();
        if (Objects.nonNull(riskSafeHitTypeEnum)) {
            switch (riskSafeHitTypeEnum) {
                case PASS:
                    handleCheckPass(homeCover, dto.getOperator(), 0);
                    break;
                case BLOCK:
                    handleCheckPunish(homeCover, dto.getOperator(), 0);
                    break;
                case DELETE:
                    handleCheckDelete(homeCover, dto.getOperator(), 0);
                    break;
            }
        }
    }
}
