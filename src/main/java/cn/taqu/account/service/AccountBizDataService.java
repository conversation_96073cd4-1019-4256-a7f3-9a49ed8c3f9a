package cn.taqu.account.service;


import java.util.Map;
import java.util.Objects;

import cn.taqu.account.dao.NickNameRiskVerifyDao;
import cn.taqu.account.dao.PersonalProfileInfoDao;
import cn.taqu.account.model.NickNameRiskVerify;
import cn.taqu.account.model.PersonalProfileInfo;
import cn.taqu.core.exception.ServiceException;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;

import cn.taqu.account.common.CommonAuditStatus;
import cn.taqu.account.constant.AccountBizDataDtoType;
import cn.taqu.account.dto.AccountBizDataDto;
import cn.taqu.core.utils.JsonUtils;

@Service
public class AccountBizDataService {
	private static final Logger LOGGER = LoggerFactory.getLogger(AccountBizDataService.class);

	@Autowired
	private AccountsRegAttributionService accountsRegAttributionService;
	@Autowired
	private PersonalProfileInfoService personalProfileInfoService;
	@Autowired
	private NickNameRiskVerifyService nickNameRiskVerifyService;
	@Autowired
	private AccountsMemberInfoService accountsMemberInfoService;
	@Autowired
	private AccountsService accountsService;

    @Autowired
    private PersonalProfileInfoDao personalProfileInfoDao;

    @Autowired
    private NickNameRiskVerifyDao nickNameRiskVerifyDao;

    /**
     * @param msgBody
     */
//	@Transactional
    public void processMsgBody(String msgBody) {
        AccountBizDataDto dto = JsonUtils.stringToObject2(msgBody, new TypeReference<AccountBizDataDto>() {});
        String type = dto.getType();
        if(StringUtils.isBlank(type)) {
            LOGGER.error("数据异常，未传type，msgBody={}", msgBody);
            return;
        }

	    switch (type) {
            case AccountBizDataDtoType.REG_ATTRIBUTION:
                if(checkAndRetrun(dto.getData(), "token", "account_uuid")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };
                processAccountsRegAttribution(dto);
                break;
            case AccountBizDataDtoType.PERSONAL_PROFILE_AUTO_AUDIT:
                if(checkAndRetrun(dto.getData(), "auditType", "id", "operator")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };

                try {
                    processPersonalProfileAutoAudit(dto);
                } catch (Exception e) {
                    LOGGER.warn("自动审核个人简介失败。", e);
                }

                break;
            case AccountBizDataDtoType.NICKNAME_AUTO_AUDIT:
                if(checkAndRetrun(dto.getData(), "auditType", "id", "operator")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };

                try {
                    processNicknameAutoAudit(dto);
                } catch (Exception e) {
                    LOGGER.warn("自动审核昵称失败。", e);
                }

                break;
            case AccountBizDataDtoType.NICKNAME_CALLBACK_AUDIT:
                if(checkAndRetrun(dto.getData(), "auditType", "accountUuid", "nickname")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };
                try {
                    processNicknameCallbackAudit(dto);
                } catch (Exception e) {
                    LOGGER.warn("回调人审审核昵称失败。", e);
                }

                break;
            case AccountBizDataDtoType.AUTO_LOGOUT:
                if(checkAndRetrun(dto.getData(), "ticket")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };
                try {
                    processAutoLogout(dto);
                } catch (Exception e) {
                    LOGGER.warn("自动登出失败。", e);
                }

                break;
            case AccountBizDataDtoType.AUTO_LOGOUT_V2:
                if(checkAndRetrun(dto.getData(), "uuid")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };
                try {
                    processAutoLogoutV2(dto);
                } catch (Exception e) {
                    LOGGER.warn("自动登出失败。", e);
                }

                break;
            case AccountBizDataDtoType.FORCE_LOGOUT:
                if(checkAndRetrun(dto.getData(), "accountUuid")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };
                try {
                    processForceLogout(dto);
                } catch (Exception e) {
                    LOGGER.warn("强制登出失败。", e);
                }

                break;
            case AccountBizDataDtoType.REVIEW_INFO:
                if(checkAndRetrun(dto.getData(), "accountUuid")) {
                    LOGGER.error("数据异常，缺少必要参数，msgBody={}", msgBody);
                    return;
                };
                try {
                    processReviewInfo(dto);
                } catch (Exception e) {
                    LOGGER.warn("复审用户信息失败。", e);
                }

                break;
            default:
                LOGGER.warn("数据异常，未知type，msgBody={}", msgBody);
                return;
        }
    }

	/**
	 * 复审用户信息
	 * @param dto
	 */
    private void processReviewInfo(AccountBizDataDto dto) {
        Map<String, Object> data = dto.getData();
        String accountUuid = MapUtils.getString(data, "accountUuid");
        LOGGER.info("复审用户，accountUuid={}", accountUuid);
        accountsMemberInfoService.review(accountUuid);
    }

    /**
     * 强制登出
     * @param dto
     */
    private void processForceLogout(AccountBizDataDto dto) {
        Map<String, Object> data = dto.getData();
        String accountUuid = MapUtils.getString(data, "accountUuid");
        LOGGER.info("强制登出，accountUuid={}", accountUuid);
        accountsService.forceLogout(accountUuid);
    }

    /**
     * 自动登出
     * @param dto
     */
    private void processAutoLogout(AccountBizDataDto dto) {
        Map<String, Object> data = dto.getData();
        String ticket = MapUtils.getString(data, "ticket");
        LOGGER.info("自动登出，ticket={}", ticket);
        accountsService.autoLogout(ticket);
    }

    private void processAutoLogoutV2(AccountBizDataDto dto) {
        Map<String, Object> data = dto.getData();
        String uuid = MapUtils.getString(data, "uuid");
        LOGGER.info("自动登出v2，uuid={}", uuid);
        accountsService.autoLogoutV2(uuid);
    }

    /**
     * 个人简介自动审核
     * @param dto
     */
    private void processPersonalProfileAutoAudit(AccountBizDataDto dto) {
        Map<String, Object> data = dto.getData();
        Integer auditType = MapUtils.getInteger(data, "auditType");
        Long id = MapUtils.getLong(data, "id");
        String operator = MapUtils.getString(data, "operator");
        String reason = MapUtils.getString(data, "reason", "");

        // 特殊处理：人审快于机审的情况，防止人审结果被机审覆盖
        PersonalProfileInfo profileInfo = personalProfileInfoDao.get(id);
        if(null == profileInfo){
            throw new ServiceException("personal_profile_info_not_exist", "审核失败，id为" + id + "的个人简介不存在");
        }
        // 判断当前是否待审核状态，非待审核状态说明人审快于机审，直接不处理
        if (!Objects.equals(profileInfo.getStatus(), CommonAuditStatus.AUDITING.getStatus())) {
            LOGGER.warn("uuid: {} unusual personal profile auto audit status: {}", profileInfo.getAccount_uuid(), profileInfo.getStatus());
            return;
        }

        if(Objects.equals(auditType, CommonAuditStatus.AUDIT_SUCCESS.getStatus())) {
            personalProfileInfoService.auditPass(id, operator);
        }else if(Objects.equals(auditType, CommonAuditStatus.AUDIT_FAIL.getStatus())) {
            personalProfileInfoService.batchAuditReject(Lists.newArrayList(id), operator, reason);
        }else {
            LOGGER.error("个人简介自动审核, auditType传参异常, auditType={}", auditType);
        }
    }

    /**
     * 人审回调审核
     * @param dto
     */
    private void processNicknameCallbackAudit(AccountBizDataDto dto) {
        Map<String, Object> data = dto.getData();
        Integer auditType = MapUtils.getInteger(data, "auditType");
        String accountUuid = MapUtils.getString(data, "accountUuid");
        String nickname = MapUtils.getString(data, "nickname");

        if(Objects.equals(auditType, CommonAuditStatus.AUDIT_SUCCESS.getStatus())) {
            // 通过不处理
        }else if(Objects.equals(auditType, CommonAuditStatus.AUDIT_FAIL.getStatus())) {
            // 不通过要处理
            nickNameRiskVerifyService.nicknameCallbackAuditFail(accountUuid, nickname);
        }else {
            LOGGER.error("昵称自动审核, auditType传参异常, auditType={}", auditType);
        }
    }

    /**
     * 昵称自动审核
     * @param dto
     */
    private void processNicknameAutoAudit(AccountBizDataDto dto) {
        Map<String, Object> data = dto.getData();
        Integer auditType = MapUtils.getInteger(data, "auditType");
        Long id = MapUtils.getLong(data, "id");
        String operator = MapUtils.getString(data, "operator");

        // 特殊处理：人审快于机审的情况，防止人审结果被机审覆盖
        NickNameRiskVerify nameRiskVerify = nickNameRiskVerifyDao.get(id);
        if(null == nameRiskVerify){
            throw new ServiceException("NickNameRiskVerify_not_exist", "审核失败，id为" + id + "的昵称不存在");
        }
        // 判断当前是否待审核状态，非待审核状态说明人审快于机审，直接不处理
        if (!Objects.equals(nameRiskVerify.getStatus(), CommonAuditStatus.AUDITING.getStatus())) {
            LOGGER.warn("uuid: {} unusual nickname auto audit status: {}", nameRiskVerify.getAccountUuid(), nameRiskVerify.getStatus());
            return;
        }

        if(Objects.equals(auditType, CommonAuditStatus.AUDIT_SUCCESS.getStatus())) {
            nickNameRiskVerifyService.passVerify(new Long[] {id}, CommonAuditStatus.AUDIT_SUCCESS.getStatus(), operator);
        }else if(Objects.equals(auditType, CommonAuditStatus.AUDIT_FAIL.getStatus())) {
            nickNameRiskVerifyService.passVerify(new Long[] {id}, CommonAuditStatus.AUDIT_FAIL.getStatus(), operator);
        }else {
            LOGGER.error("昵称自动审核, auditType传参异常, auditType={}", auditType);
        }
    }

    /**
	 * 用户注册归因
	 *
	 * @param dto
	 */
	void processAccountsRegAttribution(AccountBizDataDto dto){
	    Map<String, Object> data = dto.getData();
	    String token = MapUtils.getString(data, "token");
	    String accountUuid = MapUtils.getString(data, "account_uuid");
	    String mediaCode = MapUtils.getString(data, "media_code");
	    String creativeLabel = MapUtils.getString(data, "creative_label");
	    Integer appcode = MapUtils.getInteger(data, "appcode");
	    Integer cloned = MapUtils.getInteger(data, "cloned");
	    Integer platformId = MapUtils.getInteger(data, "platform_id");
	    Integer appVersion = MapUtils.getInteger(data, "app_version");
	    String channel = MapUtils.getString(data, "channel");
	    Long gid = MapUtils.getLong(data, "gid");
	    Long pid = MapUtils.getLong(data, "pid");

	    accountsRegAttributionService.addOrUpdate(accountUuid, token, appVersion, appcode, cloned, channel, platformId, mediaCode, creativeLabel, gid, pid);
	}


	/**
     * 校验参数 如果需要抛弃 则返回true
     *
     * @param map
     * @param keys
     * @return true 抛弃， false 合规
     */
    private boolean checkAndRetrun(Map<String, Object> map, String... keys) {
        if (keys != null && keys.length > 0) {
            if (map == null || map.isEmpty()) {
                LOGGER.warn("data error msgBody empty.");
                return true;
            }

            for (String key : keys) {
                if (map.get(key) == null) {
                    LOGGER.warn("data {} error msgBody={}", key, JsonUtils.objectToString(map));
                    return true;
                }
            }
        }

        return false;
    }

}
