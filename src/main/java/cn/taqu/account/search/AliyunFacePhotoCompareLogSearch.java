package cn.taqu.account.search;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AliyunFacePhotoCompareLogSearch {

    /**
     * 开始认证时间
     */
    private Long start_time;
    /**
     * 结束认证时间
     */
    private Long end_time;
    /**
     * 认证状态，0-为认证，1-已认证
     */
    private Integer verify_status;
    /**
     * 用户uuid
     */
    private String account_uuid;
    /**
     * 页码
     */
    private Integer page_num;
    /**
     * 每页大小
     */
    private Integer page_size;
    /**
     * 性别比对 0-性别不一致 1-性别一致
     */
    private Integer gender_compare_result;

    private String account_name;
    /**
     * 校验是否有任意有效查询条件
     *
     * @return
     */
    public boolean hasParameterAny() {
        if(start_time != null || end_time != null || verify_status != null || StringUtils.isNotBlank(account_uuid)) {
            return true;
        }
        return false;
    }
}
