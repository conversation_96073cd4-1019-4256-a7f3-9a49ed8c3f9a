package cn.taqu.account.search;

import cn.taqu.core.orm.base.BaseSearch;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 标签搜索
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelSearch extends BaseSearch {
    private String content; //标签内容
    private Integer type; //标签类型 1-喜欢类型，2-自我描述，3-兴趣爱好。
    private Integer status; //状态 0启用1禁用
}
