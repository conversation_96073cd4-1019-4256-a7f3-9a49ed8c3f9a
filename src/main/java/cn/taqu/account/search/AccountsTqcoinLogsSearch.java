package cn.taqu.account.search;

import cn.taqu.core.orm.base.BaseSearch;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/28.
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountsTqcoinLogsSearch extends BaseSearch {

    private Long account_id;
    private String account_uuid;
    private Integer type;
    private Long param;
    private String content_uuid;
    private Long start_time;
    private Long end_time;
    private Collection<Long> operator_ids;

  
}
