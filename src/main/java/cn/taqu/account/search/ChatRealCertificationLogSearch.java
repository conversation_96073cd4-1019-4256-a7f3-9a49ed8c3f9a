package cn.taqu.account.search;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ChatRealCertificationLogSearch {

    /**
     * 开始认证时间
     */
    private Long start_time;
    /**
     * 结束认证时间
     */
    private Long end_time;
    /**
     * 认证状态，0-为认证，1-已认证
     */
    private Integer verify_status;
    /**
     * 用户uuid
     */
    private String account_uuid;
}
