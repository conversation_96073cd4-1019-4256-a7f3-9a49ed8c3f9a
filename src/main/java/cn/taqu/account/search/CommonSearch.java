package cn.taqu.account.search;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * 通用的查询类
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CommonSearch {

    /**
     * 开始查询时间
     */
    private Long start_time;
    /**
     * 结束查询时间
     */
    private Long end_time;
    /**
     * 审核状态，1-审核中，2-审核通过，3-审核失败
     */
    private Integer verify_status;
    /**
     * 用户uuid
     */
    private String account_uuid;
    /**
     * 昵称
     */
    private String account_name;
    /**
     * 页码
     */
    private Integer page_num;
    /**
     * 每页大小
     */
    private Integer page_size;
}
