package cn.taqu.account.search;

import cn.taqu.core.orm.PageSearch;
import lombok.Data;

/**
 * 再注册白名单搜索
 *
 * <AUTHOR>
 * @date 2021/07/21
 */
@Data
public class ReRegisterWhiteListSearch extends PageSearch {
    /**
     * 开始查询时间
     */
    private Long startTime;
    /**
     * 结束查询时间
     */
    private Long endTime;

    /**
     * 状态  生效:1 失效:0
     */
    private Integer status;

    /**
     * 类型 手机号:1 openid:2
     */
    private Integer registerType;
}
