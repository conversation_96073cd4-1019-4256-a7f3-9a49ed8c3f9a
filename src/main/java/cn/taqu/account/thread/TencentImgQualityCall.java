package cn.taqu.account.thread;

import cn.taqu.account.constant.AccountPhotoDetectType;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.TencentImgQualityDTO;
import cn.taqu.account.service.AliyunLiveFaceDetectService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.account.service.BuryService;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-20 15:45
 */
@Slf4j
public class TencentImgQualityCall implements Callable<TencentImgQualityDTO> {

    /**
     * 图片地址
     */
    private String photoUrl;

    private String accountUuid;

    public TencentImgQualityCall(String accountUuid, String photoUrl){
        this.accountUuid = accountUuid;
        this.photoUrl = AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl);
    }

    @Override
    public TencentImgQualityDTO call() {
        AliyunLiveFaceDetectService aliyunLiveFaceDetectService = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class);
        StringRedisTemplate accountStringRedisTemplate = (StringRedisTemplate)SpringContextHolder.getBean("accountStringRedisTemplate");
        TencentImgQualityDTO tencentImgQualityDTO = null;

        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO_IMG_QUALITY_RESULT.setArg(Md5Util.encryptSHA1(accountUuid + photoUrl));
        String result = accountStringRedisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isNotBlank(result)){
            tencentImgQualityDTO = JSON.parseObject(result, TencentImgQualityDTO.class);
            log.info("第三方检测跳过.质量检测结果已存在.uuid={}.url={}", accountUuid, photoUrl);
            return tencentImgQualityDTO;
        }

        if(ThirdPartFactory.TENCENNT_IMG_QUALITY_TURN_ON){
            // 默认头像不检测
            if(AvatarHandleService.isDefAvatar(AvatarHandleService.getAvatarOfSavePhoto(photoUrl))){
                tencentImgQualityDTO = new TencentImgQualityDTO();
                tencentImgQualityDTO.setAestheticScore(100);
                tencentImgQualityDTO.setClarityScore(100);
            }else{
                // 缓存
                long s1 = System.currentTimeMillis();
                tencentImgQualityDTO = aliyunLiveFaceDetectService.getTencentImgQualityResult(accountUuid, photoUrl);
                if(tencentImgQualityDTO == null){
                    return null;
                }
                long s2 = System.currentTimeMillis();
                tencentImgQualityDTO.setPhotoUrl(photoUrl);
                BuryService.pushToPhotoDetectRecord(AccountPhotoDetectType.IMG_QUALITY, accountUuid, tencentImgQualityDTO);

                long s3 = System.currentTimeMillis();
                log.info("第三方检测.图片质量.uuid={}.耗时={}ms.保存耗时={}ms.url={}", accountUuid, s2 - s1, s3 - s2, photoUrl);
            }

        }else{
            tencentImgQualityDTO = new TencentImgQualityDTO();
            tencentImgQualityDTO.setAestheticScore(100);
            tencentImgQualityDTO.setClarityScore(100);
        }
        return tencentImgQualityDTO;
    }
}
