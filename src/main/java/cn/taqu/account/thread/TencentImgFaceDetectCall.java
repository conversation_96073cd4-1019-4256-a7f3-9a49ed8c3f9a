package cn.taqu.account.thread;

import cn.taqu.account.service.AliyunLiveFaceDetectService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.core.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-20 15:47
 */
@Slf4j
public class TencentImgFaceDetectCall implements Callable<Boolean> {

    /**
     * 图片地址
     */
    private String photoUrl;

    private String accountUuid;

    private Boolean needFaceDetect;

    public TencentImgFaceDetectCall(String accountUuid, String photoUrl, Boolean needFaceDetect) {
        this.accountUuid = accountUuid;
        this.photoUrl = AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl);
        this.needFaceDetect = needFaceDetect;
    }

    @Override
    public Boolean call() {
        if (!needFaceDetect) {
            log.info("accountUuid:{} don't need face detect", accountUuid);
            return Boolean.TRUE;
        }

        AliyunLiveFaceDetectService aliyunLiveFaceDetectService = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class);
        return aliyunLiveFaceDetectService.picIsFace(accountUuid, photoUrl);
    }
}
