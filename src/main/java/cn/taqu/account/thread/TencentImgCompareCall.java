package cn.taqu.account.thread;

import cn.taqu.account.constant.AccountPhotoDetectType;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.constant.RiskCertificationTypeEnum;
import cn.taqu.account.dto.CompareFaceDto;
import cn.taqu.account.service.AliyunLiveFaceDetectService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.account.service.BuryService;
import cn.taqu.account.service.ToolsService;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-20 15:47
 */
@Slf4j
public class TencentImgCompareCall implements Callable<CompareFaceDto> {

    private String accountUuid;
    /**
     * 图片地址
     */
    private String pic1;

    /**
     * 图片地址
     */
    private String basePhotoUrl;

    /**
     * 比对类型
     * 默认类型为{@see RiskCertificationTypeEnum.UNKNOWN}
     */
    private final RiskCertificationTypeEnum certificationType;

    /**
     *
     * @param accountUuid
     * @param pic1
     * @param basePhotoUrl
     */
    public TencentImgCompareCall(String accountUuid, String pic1, String basePhotoUrl, RiskCertificationTypeEnum certificationType) {
        this.accountUuid = accountUuid;
        this.pic1 = AvatarHandleService.getAvatarSrcPhotoUrl(pic1);
        this.basePhotoUrl = ToolsService.addAccountPrivateUrlPreDomain(basePhotoUrl);
        this.certificationType = certificationType;
    }


    @Override
    public CompareFaceDto call() {
        AliyunLiveFaceDetectService aliyunLiveFaceDetectService = SpringContextHolder.getBean(AliyunLiveFaceDetectService.class);
        StringRedisTemplate accountStringRedisTemplate = (StringRedisTemplate)SpringContextHolder.getBean("accountStringRedisTemplate");
        CompareFaceDto compareFaceDto = null;
        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO_IMG_COMPARE_RESULT.setArg(Md5Util.encryptSHA1(accountUuid + basePhotoUrl + pic1));
        String result = accountStringRedisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isNotBlank(result)){
            compareFaceDto = JSON.parseObject(result, CompareFaceDto.class);
            log.info("第三方检测跳过.比对检测结果已存在.uuid={}.base_url={}.verify_url={}", accountUuid, basePhotoUrl, pic1);
            return compareFaceDto;
        }

        long s1 = System.currentTimeMillis();
        
        // 2024.07.04 对比前底图获取是有图片地址去比较
        String privateBasePic = ToolsService.getPrivateBasePic(basePhotoUrl);
        compareFaceDto = aliyunLiveFaceDetectService.comparePic(pic1, privateBasePic, accountUuid, certificationType);
        long s2 = System.currentTimeMillis();
        if(compareFaceDto != null){
            compareFaceDto.setVerifyPhotoUrl(pic1);
            compareFaceDto.setBasePhotoUrl(basePhotoUrl);
            BuryService.pushToPhotoDetectRecord(AccountPhotoDetectType.IMG_COMPARE, accountUuid, compareFaceDto);

//            AccountsPhotoImgCompareLog photoImgCompareLog = new AccountsPhotoImgCompareLog();
//            photoImgCompareLog.setAccountUuid(accountUuid).setBasePhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(basePhotoUrl)).setVerifyPhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(pic1))
//                    .setFaceModelVersion(compareFaceDto.getFaceModelVersion()).setSocre(compareFaceDto.getScore())
//                    .setThirdOrderNo(compareFaceDto.getRequestId()).setResponseStr(JSON.toJSONString(compareFaceDto))
//                    .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
//            SpringContextHolder.getBean(AccountsPhotoImgCompareLogService.class).merge(photoImgCompareLog);
//            accountStringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(compareFaceDto),7, TimeUnit.DAYS);
        }
        long s3 = System.currentTimeMillis();
        log.info("第三方检测.图片比对.uuid={}.耗时={}ms.保存耗时={}ms.url={}.baseUrl={}", accountUuid, s2 - s1, s3 - s2, pic1, basePhotoUrl);

        return compareFaceDto;
    }
}
