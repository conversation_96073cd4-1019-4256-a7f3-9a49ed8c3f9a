package cn.taqu.account.thread;

import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsIntroductionLogDao;
import cn.taqu.account.dto.IntroductionDetectDTO;
import cn.taqu.account.dto.IntroductionPhotoDTO;
import cn.taqu.account.service.AccountsIntroductionService;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-23 15:32
 */
@Slf4j
public class SetIntroductionCall implements Callable<Boolean> {

    private String accountUuid;

    private String content;

    private String bucket;

    private List<IntroductionPhotoDTO> introductionPhotoDTOs;

    private String smid;

    private String tracerId;

    public SetIntroductionCall(String accountUuid, String content, String bucket, List<IntroductionPhotoDTO> introductionPhotoDTOs, String smid, String tracerId) {
        this.accountUuid = accountUuid;
        this.content = content;
        this.bucket = bucket;
        this.introductionPhotoDTOs = introductionPhotoDTOs;
        this.smid = smid;
        this.tracerId = tracerId;
    }


    @Override
    public Boolean call() throws Exception {
        if (StringUtils.isNotBlank(tracerId)) {
            SoaBaseParams.fromThread().setDistinctRequestId(tracerId);
        }
        SoaBaseParams.fromThread().setDistinctRequestId(tracerId);
        AccountsIntroductionService accountsIntroductionService = SpringContextHolder.getBean(AccountsIntroductionService.class);
        AccountsIntroductionLogDao accountsIntroductionLogDao = SpringContextHolder.getBean(AccountsIntroductionLogDao.class);
        IntroductionDetectDTO introductionDetectDTO = accountsIntroductionService.detectAccountsIntroduction(accountUuid, content, bucket, introductionPhotoDTOs, smid);
        StringRedisTemplate accountStringRedisTemplate = (StringRedisTemplate) SpringContextHolder.getBean("accountStringRedisTemplate");
        Boolean hasKey = accountStringRedisTemplate.hasKey(RedisKeyConstant.ACCOUNT_SUBMIT_INTRODUCTION_TIMEOUT.setArg(accountUuid));
        if (introductionDetectDTO != null && introductionDetectDTO.getDetectPass() && hasKey){
            log.info("SetIntroductionCall线程继续{}", System.currentTimeMillis());
            // 保存 + 缓存
            accountsIntroductionService.saveMyIntroductonAndAddTimes(accountUuid, content, bucket, introductionPhotoDTOs, introductionDetectDTO);
            accountStringRedisTemplate.delete(RedisKeyConstant.ACCOUNT_SUBMIT_INTRODUCTION_TIMEOUT.setArg(accountUuid));
            return true;
        } else {
            log.info("SetIntroductionCall线程终止{}", System.currentTimeMillis());
            if (Objects.nonNull(introductionDetectDTO) && Objects.nonNull(introductionDetectDTO.getIntroductionLogId())) {
                accountsIntroductionLogDao.removeById(introductionDetectDTO.getIntroductionLogId());
            }
            return false;
        }
    }
}
