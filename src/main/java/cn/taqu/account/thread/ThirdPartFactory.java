package cn.taqu.account.thread;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-20 15:18
 */
@Slf4j
@Component
public class ThirdPartFactory {

    private static Integer DEFAULT_THREAD_SIZE = 10;
    private static Integer MAX_THREAD_SIZE = 50;

    public static ExecutorService setIntroductionPool = new ThreadPoolExecutor(DEFAULT_THREAD_SIZE, MAX_THREAD_SIZE, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());;

    public static ExecutorService shumeiImgCheckPool = new ThreadPoolExecutor(DEFAULT_THREAD_SIZE, MAX_THREAD_SIZE, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());;

    public static ExecutorService tencentImgQualityPool = new ThreadPoolExecutor(DEFAULT_THREAD_SIZE, MAX_THREAD_SIZE, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());;

    public static ExecutorService tencentImgComparePool = new ThreadPoolExecutor(DEFAULT_THREAD_SIZE, MAX_THREAD_SIZE, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());;

    public static Boolean TENCENNT_IMG_QUALITY_TURN_ON = true;

    /**
     * 初始化配置
     * @param jsonStr
     */
    public static void initConfig(String jsonStr){
        try {
            JSONObject json = JSON.parseObject(jsonStr);
            TENCENNT_IMG_QUALITY_TURN_ON = MapUtils.getBoolean(json, "tencentImgQualityTurnOn", true);
        }catch (Exception e){
            log.error("第三方配置读取失败,jsonStr={}", jsonStr, e);
        }
    }

}
