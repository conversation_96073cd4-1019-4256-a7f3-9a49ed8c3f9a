package cn.taqu.account.thread;

import cn.taqu.account.constant.AccountPhotoDetectType;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dto.ShumeiImgCheckResponseDTO;
import cn.taqu.account.service.AccountsPhotoService;
import cn.taqu.account.service.AvatarHandleService;
import cn.taqu.account.service.BuryService;
import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.Md5Util;
import cn.taqu.core.utils.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-07 17:33
 */
@Slf4j
public class ShumeImgCheckWithSmidCall implements Callable<ShumeiImgCheckResponseDTO> {

    private String accountUuid;

    private String photoUrl;

    private String type;

    private String smid;

    private Long photoId;

    private Integer appcode;

    private Integer cloned;

    private String tracerId;

    public ShumeImgCheckWithSmidCall(String accountUuid, String photoUrl, String type, String smid, Integer appcode, Integer cloned, Long photoId, String tracerId) {
        this.accountUuid = accountUuid;
        this.photoUrl = AvatarHandleService.getAvatarSrcPhotoUrl(photoUrl);
        this.type = type;
        this.smid = smid;
        this.photoId = photoId;
        this.appcode = appcode;
        this.cloned = cloned;
        this.tracerId = tracerId;
    }

    @Override
    public ShumeiImgCheckResponseDTO call() {
        if (StringUtils.isNotBlank(tracerId)) {
            SoaBaseParams.fromThread().setDistinctRequestId(tracerId);
        }
        log.info("检测中台图片数美检测");
        StringRedisTemplate accountStringRedisTemplate = (StringRedisTemplate) SpringContextHolder.getBean("accountStringRedisTemplate");
        // 查询缓存
        String redisKey = RedisKeyConstant.ACCOUNT_PHOTO_IMG_CHECK_RESULT.setArg(Md5Util.encryptSHA1(accountUuid + photoUrl));
        String result = accountStringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(result)) {
            ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = JSON.parseObject(result, ShumeiImgCheckResponseDTO.class);
            log.info("第三方检测跳过.违规检测结果已存在.uuid={}.url={}", accountUuid, photoUrl);
            return shumeiImgCheckResponseDTO;
        }

        long s1 = System.currentTimeMillis();
        ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO;
        shumeiImgCheckResponseDTO = SpringContextHolder.getBean(AccountsPhotoService.class).shumeiImgFromRiskDetect(accountUuid, photoUrl, appcode, cloned, smid, photoId, type);

        long s2 = System.currentTimeMillis();
        if (shumeiImgCheckResponseDTO != null && StringUtils.isNotBlank(shumeiImgCheckResponseDTO.getRiskLevel())) {
            shumeiImgCheckResponseDTO.setPhotoUrl(photoUrl);
            BuryService.pushToPhotoDetectRecord(AccountPhotoDetectType.IMG_CHECK, accountUuid, shumeiImgCheckResponseDTO);
//            AccountsPhotoImgCheckLog accountsPhotoImgCheckLog = new AccountsPhotoImgCheckLog();
//            accountsPhotoImgCheckLog.setAccountUuid(accountUuid).setPhotoUrl(AvatarHandleService.getAvatarOfSavePhoto(photoUrl))
//                    .setRiskLevel(shumeiImgCheckResponseDTO.getRiskLevel()).setRiskDescription(shumeiImgCheckResponseDTO.getRiskDescription())
//                    .setThirdOrderNo(shumeiImgCheckResponseDTO.getRequestId()).setResponseStr(shumeiImgCheckResponseDTO.getResponseStr())
//                    .setCreateTime(DateUtil.currentTimeSeconds()).setUpdateTime(DateUtil.currentTimeSeconds());
//            SpringContextHolder.getBean(AccountsPhotoImgCheckLogService.class).merge(accountsPhotoImgCheckLog);
//            // 保存到缓存7天
//            accountStringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(shumeiImgCheckResponseDTO),7, TimeUnit.DAYS);
        }
        long s3 = System.currentTimeMillis();
        log.info("第三方检测.违规检测.uuid={}.耗时={}ms.保存耗时={}ms.url={}", accountUuid, s2 - s1, s3 - s2, photoUrl);
        return shumeiImgCheckResponseDTO;
    }
}
