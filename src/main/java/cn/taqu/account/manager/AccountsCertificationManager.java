package cn.taqu.account.manager;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taqu.mp.account.dto.UserRegInfo;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.dao.AccountsCertificationDao;
import cn.taqu.account.model.AccountsCertification;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.service.ToolsService;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Repository
public class AccountsCertificationManager {

    @Autowired
    private AccountsCertificationDao accountsCertificationDao;

    @Autowired
    private SoaService soaService;


    public Integer getCheckedIdentityNoCount(Integer cloned, String identityNo) {
        checkCloned(cloned);

        List<AccountsCertification> certifications = getAccountCertificationsByIdentifyNo(identityNo, 1);
        if (certifications == null || certifications.isEmpty()) {
            return 0;
        }
        List<String> uuids = certifications.stream().map(AccountsCertification::getAccountUuid).distinct().collect(Collectors.toList());
        Map<String, Integer> clonedMap = getRegClonedByUuids(uuids);

        ClonedGroupEnum clonedGroup = ClonedGroupEnum.getClonedGroup(cloned);
        return (int) clonedMap.values().stream().filter(clonedGroup::contains).count();
    }

    public String getLastUuidByIdentityNo(Integer cloned, String identityNo, String excludeUuid) {
        checkCloned(cloned);

        List<AccountsCertification> certifications = getAccountCertificationsByIdentifyNo(identityNo, null);
        if (certifications == null || certifications.isEmpty()) {
            return null;
        }

        return certifications.stream()
                .sorted((c1, c2) -> c2.getId().compareTo(c1.getId()))
                .map(AccountsCertification::getAccountUuid)
                .distinct()
                .filter(uuid -> !uuid.equals(excludeUuid))
                .filter(uuid -> {
                    Integer regCloned = getRegClonedByUuid(uuid);
                    return ClonedGroupEnum.getClonedGroup(regCloned).contains(cloned);
                })
                .findFirst()
                .orElse(null);
    }

    public List<String> listRelateAccountByIdentityNo(Integer cloned, String identityNo, Integer isChecked) {
        checkCloned(cloned);

        List<AccountsCertification> certifications = getAccountCertificationsByIdentifyNo(identityNo, isChecked);
        if (certifications == null || certifications.isEmpty()) {
            return null;
        }

        return certifications.stream()
                .sorted((c1, c2) -> c2.getId().compareTo(c1.getId()))
                .map(AccountsCertification::getAccountUuid)
                .distinct()
                .filter(uuid -> {
                    Integer regCloned = getRegClonedByUuid(uuid);
                    return ClonedGroupEnum.getClonedGroup(regCloned).contains(cloned);
                })
                .collect(Collectors.toList());
    }

    private List<AccountsCertification> getAccountCertificationsByIdentifyNo(String identityNo, Integer isChecked) {
        log.info("identityNo=【{}】",identityNo);
        if(ToolsService.accountsCertificationSwitchEncryption.isOn(true)) {
            // 2024.09.02 处理X结尾身份。数据库以后以大写X为主
            // 小写身份证
            String lowerIdentityNo = identityNo.toLowerCase();
            // 大写身份证
            String upperIdentityNo = identityNo.toUpperCase();
            
            return accountsCertificationDao.getAccountCertificationsByIdentityNoDigest(EncryptUtil.sm3(lowerIdentityNo), EncryptUtil.sm3(upperIdentityNo), isChecked);
        }else {
            return accountsCertificationDao.getAccountCertificationsByIdentityNo(identityNo, isChecked);
        }
    }

    private Map<String, Integer> getRegClonedByUuids(List<String> uuids) {
        Set<String> uuidSet = Sets.newHashSet(uuids);
        return uuidSet.stream().collect(Collectors.toMap(uuid -> uuid, this::getRegClonedByUuid));
    }

    private static void checkCloned(Integer cloned) {
        if (cloned == null) {
            log.warn("req cloned is null");
            throw new ServiceException(CodeStatus.CLONE_MISS);
        }
    }

    public Integer getRegClonedByUuid(String uuid) {
        UserRegInfo userRegInfo = soaService.getUserRegInfo(uuid);
        return userRegInfo == null || userRegInfo.getCloned() == null ? 1 : userRegInfo.getCloned();
    }

    public List<AccountsCertification> listAccountCertificationsByIdentifyNo(String identityNo,Integer cloned){
        checkCloned(cloned);
        List<AccountsCertification> accountsCertificationList = getAccountCertificationsByIdentifyNo(identityNo, 1);
        if(CollectionUtils.isEmpty(accountsCertificationList)){
            return null;
        }
        List<String> uuids = accountsCertificationList.stream().map(AccountsCertification::getAccountUuid).distinct().collect(Collectors.toList());
        Map<String, Integer> clonedMap = getRegClonedByUuids(uuids);
        List<AccountsCertification> resultList= Lists.newArrayList();
        for(AccountsCertification temp : accountsCertificationList){
//            Integer identityNoStatus=temp.getIdentityNoStatus();
            Integer isCheck=temp.getIsChecked();
//            if(identityNoStatus==null || identityNoStatus!=1){
//                continue;
//            }
            if(isCheck==null || isCheck!=1){
                continue;
            }
            String uuidTemp=temp.getAccountUuid();
            Integer clonedTemp=clonedMap.get(uuidTemp);
            ClonedGroupEnum clonedGroup=ClonedGroupEnum.getClonedGroup(clonedTemp);
            if(clonedGroup.contains(cloned)){
                resultList.add(temp);
            }
        }
        return resultList.stream().sorted((c1, c2) -> c2.getId().compareTo(c1.getId())).collect(Collectors.toList());
    }

    /**
     * @param accountCloned
     * @param rewardAccountDigest
     * @param value
     * @return
     */
    public Set<String> listAccountUuidByRewardAccountDigest(Integer cloned, String rewardAccountDigest,
        int rewardAccountStatus) {
        checkCloned(cloned);

        List<String> accountUuidList = accountsCertificationDao.listAccountUuidByRewardAccountDigest(rewardAccountDigest, rewardAccountStatus);
        
        if (CollectionUtils.isEmpty(accountUuidList)) {
            return Sets.newHashSet();
        }
        
        Set<String> accountUuidSet = Sets.newHashSet(accountUuidList);
        
        Set<String> reSet = Sets.newHashSet();
        for (String accountUuid : accountUuidSet) {
            Integer regCloned = getRegClonedByUuid(accountUuid);
            if(ClonedGroupEnum.getClonedGroup(regCloned).contains(cloned)) {
                reSet.add(accountUuid);
            }
        }
        return reSet;
    }

}
