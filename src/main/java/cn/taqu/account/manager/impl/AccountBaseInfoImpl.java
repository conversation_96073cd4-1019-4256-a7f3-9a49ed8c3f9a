package cn.taqu.account.manager.impl;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.dao.AccountsCertificationDao;
import cn.taqu.account.dto.InfoFiledCacheDTO;
import cn.taqu.account.manager.AccountBaseInfoManager;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.core.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/15 1:51 下午
 */
@Slf4j
@Service
public class AccountBaseInfoImpl implements AccountBaseInfoManager {

    @Resource
    private AccountsInfoService accountsInfoService;
    @Resource
    private AccountsCertificationDao accountsCertificationDao;

    @Override
    public InfoFiledCacheDTO getInfoByUuid(String accountUuid, String[] fields) {
        Map<String, Map<String, Object>> map = accountsInfoService.getInfoByUuid(new String[]{accountUuid}, fields, "1", false, false);
        if (map != null && !map.isEmpty()) {
            Map<String, Object> tempMap = map.get(accountUuid);
            return JsonUtils.mapper().convertValue(tempMap, InfoFiledCacheDTO.class);
        }

        return null;
    }

    @Override
    public List<InfoFiledCacheDTO> listInfoByUuid(List<String> accountUuids, String[] fields) {
        Map<String, Map<String, Object>> map = accountsInfoService.getInfoByUuid(accountUuids.toArray(new String[]{}), fields, "1", false, false);
        if (map != null && !map.isEmpty()) {
            Collection<Map<String, Object>> values = map.values();
            return values.stream().map(value -> JsonUtils.mapper().convertValue(value, InfoFiledCacheDTO.class)).collect(Collectors.toList());
        }

        return null;
    }

    @Override
    public Integer getGender(String accountUuid) {
        ArrayList<String> accountUuidList = Lists.newArrayList(accountUuid);
        Map<String, Map<String, Object>> accountsInfoMap = accountsInfoService.getInfoByUuid(accountUuidList.toArray(new String[]{}), new String[]{"sex_type"}, "1", false, false);
        Map<String, Object> userInfo = accountsInfoMap.get(accountUuid);
        return MapUtils.getInteger(userInfo, "sex_type", 0);
    }

    @Override
    public Integer getRealAge(String uuid) {
        InfoFiledCacheDTO info = getInfoByUuid(uuid, new String[]{UuidInfoField.IDENTITY_NO_BIRTH});
        Long identityNoBirth = Optional.ofNullable(info).map(InfoFiledCacheDTO::getIdentityNoBirth).orElse(null);
        if (identityNoBirth == null || identityNoBirth == 0L) {
            log.info("用户身份证缓存不存在：{}", uuid);
            return 0;
        }

        Integer age = parseAge(identityNoBirth);
        if (age == 0) {
            log.warn("用户身份证异常：{}", uuid);
        }
        return age;
    }

    @Override
    public List<InfoFiledCacheDTO> listAccountStatus(List<String> uuidList) {
        return this.listInfoByUuid(uuidList, new String[]{UuidInfoField.UUID, UuidInfoField.ACCOUNT_STATUS, UuidInfoField.SEX_TYPE});
    }

    @Override
    public List<InfoFiledCacheDTO> listAvatar(List<String> uuidList) {
        return this.listInfoByUuid(uuidList, new String[]{UuidInfoField.UUID, UuidInfoField.AVATAR});
    }

    /**
     * 解析身份证年龄（周岁）
     *
     * @param birthSecond
     * @return
     */
    public Integer parseAge(Long birthSecond) {

        // 将字符串转换为LocalDate
        LocalDate birthDate = Instant.ofEpochSecond(birthSecond).atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算年龄
        Period period = Period.between(birthDate, LocalDate.now());

        // 检查计算结果是否为负数
        if (period.getYears() < 0) {
            log.warn("当前日期早于出生日期，请检查");
            return 0;
        }

        // 返回周岁年龄
        return period.getYears();
    }

    @Override
    public boolean isRealAge(String uuid) {
        Map<String, Object> accMap = accountsInfoService.singleGetInfo(uuid, new String[] {UuidInfoField.IDENTITY_NO_BIRTH, UuidInfoField.BIRTH, UuidInfoField.ZHIMA_CERTIFICATION});
        String idBirth = MapUtils.getString(accMap, UuidInfoField.IDENTITY_NO_BIRTH, "");
        String birth = MapUtils.getString(accMap, UuidInfoField.BIRTH, "");
        String certification = MapUtils.getString(accMap, UuidInfoField.ZHIMA_CERTIFICATION, "0");
        if ("0".equals(certification)) {
            return true;
        }
        if (StringUtils.isBlank(idBirth)) {
            // 未实名 则为真实年龄
            return true;
        }
        if (StringUtils.isBlank(birth)) {
            return true;
        }

        // 将字符串转换为LocalDate
        LocalDate b1 = Instant.ofEpochSecond(Long.parseLong(idBirth)).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate b2 = Instant.ofEpochSecond(Long.parseLong(birth)).atZone(ZoneId.systemDefault()).toLocalDate();

        return b1.getYear() == b2.getYear();
    }

}
