package cn.taqu.account.manager;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.dto.InfoFiledCacheDTO;

import java.util.List;

/**
 * 基础信息查询接口
 * （目前经常出现要查个东西不知道接口在哪里, 这里做下常用的汇总）
 *
 * <AUTHOR>
 * @date 2024/10/15 1:48 下午
 */
public interface AccountBaseInfoManager {

    /**
     * 基础信息获取【最常用接口】
     *
     * @param accountUuid 用户uuid
     * @param fields      字段{@link UuidInfoField}
     * @return 返回实体类还在完善补充中，需要调用者共同一起完善
     */
    InfoFiledCacheDTO getInfoByUuid(String accountUuid, String[] fields);

    /**
     * 基础信息批量获取【最常用接口】
     *
     * @param accountUuids 用户uuid
     * @param fields       字段{@link UuidInfoField}
     * @return 返回实体类还在完善补充中，需要调用者共同一起完善
     */
    List<InfoFiledCacheDTO> listInfoByUuid(List<String> accountUuids, String[] fields);

    /**
     * 获取用户性别（单个）
     *
     * @param accountUuid
     * @return
     */
    Integer getGender(String accountUuid);

    /**
     * 获取真实年龄
     *
     * @param uuid
     * @return
     */
    Integer getRealAge(String uuid);

    /**
     * 获取用户状态
     *
     * @param uuidList
     * @return
     */
    List<InfoFiledCacheDTO> listAccountStatus(List<String> uuidList);

    /**
     * 获取用户头像
     *
     * @param uuidList
     * @return
     */
    List<InfoFiledCacheDTO> listAvatar(List<String> uuidList);


    boolean isRealAge(String uuid);

}
