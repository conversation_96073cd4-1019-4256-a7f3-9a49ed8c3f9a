package cn.taqu.account.manager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.taqu.mp.account.dto.UserRegInfo;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.dao.AccountsCertificationChangeLogDao;
import cn.taqu.account.model.AccountsCertificationChangeLog;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.service.ToolsService;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * 
 * <AUTHOR>
 * 2023年12月11日下午7:26:48
 */
@Slf4j
@Repository
public class AccountsCertificationChangeLogManager {

    @Autowired
    private SoaService soaService;

    @Autowired
    private AccountsCertificationChangeLogDao accountsCertificationChangeLogDao;

    /**
     * 获取最后20条
     * @param identityNo
     * @param logType
     * @return
     */
    public List<String> listAccountUuid(Integer cloned, String identityNo, Integer logType, Integer limit){
        checkCloned(cloned);
        ClonedGroupEnum clonedGroup = ClonedGroupEnum.getClonedGroup(cloned);
        
        List<String> uuidList = null;
        // 是否使用加密摘要查询
        if(ToolsService.accountsCertificationChangeLogSwitchEncryption.isOn(true)) {
            uuidList = accountsCertificationChangeLogDao.listAccountUuidByIdentityNoDigest(EncryptUtil.sm3(identityNo), logType, limit);
        }else {
            uuidList = accountsCertificationChangeLogDao.listAccountUuidByIdentityNo(identityNo, logType, limit);
        }
        
        if (uuidList == null || uuidList.isEmpty()) {
            return new ArrayList<>();
        }
        
        Map<String, UserRegInfo> userRegInfo = soaService.getUserRegInfo(uuidList);

        return uuidList.stream()
                .filter(accountUuid -> {
                    UserRegInfo regInfo = userRegInfo.get(accountUuid);
                    return regInfo != null && clonedGroup.contains(regInfo.getCloned());
                })
                .collect(Collectors.toList());
    }

    private static void checkCloned(Integer cloned) {
        if (cloned == null) {
            log.warn("req cloned is null");
            throw new ServiceException(CodeStatus.CLONE_MISS);
        }
    }
    
    /**
     * @param accountsCertificationChangeLog
     * @return
     */
    public AccountsCertificationChangeLog merge(AccountsCertificationChangeLog accountsCertificationChangeLog) {
        return accountsCertificationChangeLogDao.merge(accountsCertificationChangeLog);
    }
}
