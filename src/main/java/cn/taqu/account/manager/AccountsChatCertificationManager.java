package cn.taqu.account.manager;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taqu.mp.account.dto.UserRegInfo;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.dao.AccountsChatCertificationDao;
import cn.taqu.account.model.AccountsChatCertification;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.service.ToolsService;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Repository
public class AccountsChatCertificationManager {

    @Autowired
    private AccountsChatCertificationDao accountsChatCertificationDao;

    @Autowired
    private SoaService soaService;

    public Integer getCheckedIdentityNoCount(Integer cloned, String identityNo) {
        checkCloned(cloned);

        List<AccountsChatCertification> certifications = getAccountCertificationsByIdentifyNo(identityNo, 1);
        if (certifications == null || certifications.isEmpty()) {
            return 0;
        }
        List<String> uuids = certifications.stream().map(AccountsChatCertification::getAccountUuid).distinct().collect(Collectors.toList());
        Map<String, Integer> clonedMap = getRegClonedByUuids(uuids);

        ClonedGroupEnum clonedGroup = ClonedGroupEnum.getClonedGroup(cloned);
        return (int) clonedMap.values().stream().filter(clonedGroup::contains).count();
    }

    public Integer countIdentityInUse(Integer cloned, String identityNo, String accountUuid) {
        checkCloned(cloned);

        List<AccountsChatCertification> certifications = getAccountCertificationsByIdentifyNo(identityNo, 1);
        if (certifications == null || certifications.isEmpty()) {
            return 0;
        }

        return Math.toIntExact(
                certifications.stream()
                        .map(AccountsChatCertification::getAccountUuid)
                        .distinct()
                        .filter(uuid -> !uuid.equals(accountUuid))
                        .filter(uuid -> {
                            Integer regCloned = getRegClonedByUuid(uuid);
                            return ClonedGroupEnum.getClonedGroup(regCloned).contains(cloned);
                        })
                        .count()
        );
    }


    public List<String> listRelateAccountByIdentityNo(Integer cloned, String identityNo, Integer isChecked) {
        checkCloned(cloned);

        List<AccountsChatCertification> certifications = getAccountCertificationsByIdentifyNo(identityNo, isChecked);
        if (certifications == null || certifications.isEmpty()) {
            return null;
        }

        return certifications.stream()
                .sorted((c1, c2) -> c2.getId().compareTo(c1.getId()))
                .map(AccountsChatCertification::getAccountUuid)
                .distinct()
                .filter(uuid -> {
                    Integer regCloned = getRegClonedByUuid(uuid);
                    return ClonedGroupEnum.getClonedGroup(regCloned).contains(cloned);
                })
                .collect(Collectors.toList());
    }

    private List<AccountsChatCertification> getAccountCertificationsByIdentifyNo(String identityNo, Integer isChecked) {
        if(ToolsService.accountsChatCertificationSwitchEncryption.isOn(true)) {
            
            // 2024.09.02 处理X结尾身份。数据库以后以大写X为主
            // 小写身份证
            String lowerIdentityNo = identityNo.toLowerCase();
            // 大写身份证
            String upperIdentityNo = identityNo.toUpperCase();
            
            return accountsChatCertificationDao.getAccountChatCertificationsByIdentityNoDigest(EncryptUtil.sm3(lowerIdentityNo), EncryptUtil.sm3(upperIdentityNo), isChecked);
        }else {
            return accountsChatCertificationDao.getAccountChatCertificationsByIdentityNo(identityNo, isChecked);
        }
    }

    private Map<String, Integer> getRegClonedByUuids(List<String> uuids) {
        Set<String> uuidSet = Sets.newHashSet(uuids);
        return uuidSet.stream().collect(Collectors.toMap(uuid -> uuid, this::getRegClonedByUuid));
    }

    private static void checkCloned(Integer cloned) {
        if (cloned == null) {
            log.warn("req cloned is null");
            throw new ServiceException(CodeStatus.CLONE_MISS);
        }
    }

    public Integer getRegClonedByUuid(String uuid) {
        UserRegInfo userRegInfo = soaService.getUserRegInfo(uuid);
        return userRegInfo == null || userRegInfo.getCloned() == null ? 1 : userRegInfo.getCloned();
    }

    public List<AccountsChatCertification> listAccountCertificationsByIdentifyNo(String identityNo, Integer cloned){
        checkCloned(cloned);
        List<AccountsChatCertification> accountsChatCertificationList = getAccountCertificationsByIdentifyNo(identityNo, 1);
        if(CollectionUtils.isEmpty(accountsChatCertificationList)){
            return null;
        }
        List<String> uuids = accountsChatCertificationList.stream().map(AccountsChatCertification::getAccountUuid).distinct().collect(Collectors.toList());
        Map<String, Integer> clonedMap = getRegClonedByUuids(uuids);
        List<AccountsChatCertification> resultList= Lists.newArrayList();
        for(AccountsChatCertification temp : accountsChatCertificationList){
//            Integer identityNoStatus=temp.getIdentityNoStatus();
            Integer isCheck=temp.getIsChecked();
//            if(identityNoStatus==null || identityNoStatus!=1){
//                continue;
//            }
            if(isCheck==null || isCheck!=1){
                continue;
            }
            String uuidTemp=temp.getAccountUuid();
            Integer clonedTemp=clonedMap.get(uuidTemp);
            ClonedGroupEnum clonedGroup=ClonedGroupEnum.getClonedGroup(clonedTemp);
            if(clonedGroup.contains(cloned)){
                resultList.add(temp);
            }
        }
        return resultList.stream().sorted((c1, c2) -> c2.getId().compareTo(c1.getId())).collect(Collectors.toList());
    }

}
