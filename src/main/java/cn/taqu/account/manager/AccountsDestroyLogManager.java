package cn.taqu.account.manager;

import cn.taqu.account.common.ClonedGroupEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.dao.AccountsDestroyLogDao;
import cn.taqu.account.model.AccountsDestroyLog;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.service.ToolsService;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.exception.ServiceException;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taqu.mp.account.dto.UserRegInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Repository
public class AccountsDestroyLogManager {

    @Autowired
    private SoaService soaService;

    @Autowired
    private AccountsDestroyLogDao accountsDestroyLogDao;

    public Integer countByMobile(Integer cloned, String mobile) {
        return Math.toIntExact(getDestroyLogsBySpecificCloned(cloned, Collections.singletonList(mobile), null).size());
    }

    public String getUuidByMobile(Integer cloned, String mobile) {
        return getDestroyLogsBySpecificCloned(cloned, Collections.singletonList(mobile), null)
                .stream()
                .sorted((o1, o2) -> o2.getId().compareTo(o1.getId()))
                .map(AccountsDestroyLog::getAccount_uuid)
                .findFirst()
                .orElse(null);
    }

    public AccountsDestroyLog getByMobile(Integer cloned, String mobile) {
        return getDestroyLogsBySpecificCloned(cloned, Collections.singletonList(mobile), null)
                .stream()
                .filter(log -> log.getStatus() == 1)
                .min((o1, o2) -> o2.getId().compareTo(o1.getId()))
                .orElse(null);
    }

    public List<String> listMobileHasDestroyLog(Integer cloned, List<String> mobileList, Integer status) {
        return getDestroyLogsBySpecificCloned(cloned, mobileList, status)
                .stream()
                .map(AccountsDestroyLog::getMobile)
                .distinct()
                .collect(Collectors.toList());
    }

    public Long getLastCreateTimeByMobile(Integer cloned, List<String> mobileList, Integer status) {
        return getDestroyLogsBySpecificCloned(cloned, mobileList, status)
                .stream()
                .map(AccountsDestroyLog::getCreate_time)
                .max(Long::compareTo)
                .orElse(null);
    }

    private List<AccountsDestroyLog> getDestroyLogsBySpecificCloned(Integer cloned, List<String> mobileList, Integer status) {
        checkCloned(cloned);
        ClonedGroupEnum clonedGroup = ClonedGroupEnum.getClonedGroup(cloned);

        List<AccountsDestroyLog> destroyLogs = null;
        if(ToolsService.accountsDestroyLogSwitchEncryption.isOn(true)) {
            Map<String,String> originalInfoMap = Maps.newHashMap();
            for (String mobile : mobileList) {
                originalInfoMap.put(mobile, mobile);
            }
            Map<String, String> batchSm3Map = EncryptUtil.batchSm3(originalInfoMap);
            Collection<String> mobileDigestList = batchSm3Map.values();
            destroyLogs = accountsDestroyLogDao.getListByMobileDigest(Lists.newArrayList(mobileDigestList), status);
        }else {
            destroyLogs = accountsDestroyLogDao.getListByMobile(mobileList, status);
        }

        if (destroyLogs == null || destroyLogs.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> uuids = destroyLogs.stream().map(AccountsDestroyLog::getAccount_uuid).collect(Collectors.toList());
        Map<String, UserRegInfo> userRegInfo = soaService.getUserRegInfo(uuids);

        return destroyLogs.stream()
                .filter(log -> {
                    UserRegInfo regInfo = userRegInfo.get(log.getAccount_uuid());
                    return regInfo != null && clonedGroup.contains(regInfo.getCloned());
                })
                .collect(Collectors.toList());
    }

    private static void checkCloned(Integer cloned) {
        if (cloned == null) {
            log.warn("req cloned is null");
            throw new ServiceException(CodeStatus.CLONE_MISS);
        }
    }
}
