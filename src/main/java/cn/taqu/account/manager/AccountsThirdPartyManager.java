package cn.taqu.account.manager;

import cn.taqu.account.dao.AccountsThirdPartyDao;
import cn.taqu.account.model.AccountsThirdParty;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import com.taqu.mp.account.client.MPAccountClient;
import com.taqu.mp.account.common.AccountChannelEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 第三方
 * <AUTHOR>
 * 2023年8月3日上午10:10:35
 */
@Repository
public class AccountsThirdPartyManager {

    @Autowired
    private AccountsThirdPartyDao accountsThirdPartyDao;

    @Autowired
    private MPAccountClient mpAccountClient;


    public AccountsThirdParty findValidByOpenId(String openId, String type) {
        List<Map<String, Object>> thirdParties = mpAccountClient.queryThirdPart(openId, 2);

        if (CollectionUtils.isEmpty(thirdParties)) {
            return null;
        }

        int thirdType = checkOpenIdType(type);

        return thirdParties.stream()
                .sorted((map1, map2) -> MapUtils.getLong(map2, "createTime").compareTo(MapUtils.getLong(map1, "createTime")))
                .filter(map -> Objects.equals(thirdType, MapUtils.getInteger(map, "type")))
                .map(map -> {
                    AccountsThirdParty thirdParty = new AccountsThirdParty();
                    thirdParty.setAccount_uuid(MapUtils.getString(map, "uuid"));
                    thirdParty.setOpen_id(MapUtils.getString(map, "idpOid"));
                    thirdParty.setType(type);
                    thirdParty.setUnion_id(MapUtils.getString(map, "idpUid"));
                    return thirdParty;
                })
                .findFirst()
                .orElse(null);
    }

    public Integer countByOpenIdAndType(String openId, String type) {
        return accountsThirdPartyDao.countByOpenIdAndType(openId, type);
    }

    public List<AccountsThirdParty> findValidByAccountUuid(String uuid) {
        List<Map<String, Object>> thirdParties = mpAccountClient.queryThirdPart(uuid, 1);

        if (CollectionUtils.isEmpty(thirdParties)) {
            return new ArrayList<>();
        }

        return thirdParties.stream()
                .map(map -> {
                    AccountsThirdParty thirdParty = new AccountsThirdParty();
                    thirdParty.setOpen_id(MapUtils.getString(map, "idpOid"));
                    thirdParty.setAccount_uuid(MapUtils.getString(map, "uuid"));
                    thirdParty.setType(transType(MapUtils.getInteger(map, "type")));
                    thirdParty.setUnion_id(MapUtils.getString(map, "idpUid"));
                    return thirdParty;
                })
                .collect(Collectors.toList());
    }

    public AccountsThirdParty findValidByAccountUuidAndType(String uuid, String type) {
        List<Map<String, Object>> thirdParties = mpAccountClient.queryThirdPart(uuid, 1);

        if (CollectionUtils.isEmpty(thirdParties)) {
            return null;
        }

        int thirdType = checkOpenIdType(type);

        return thirdParties.stream()
                .sorted((map1, map2) -> MapUtils.getLong(map2, "createTime").compareTo(MapUtils.getLong(map1, "createTime")))
                .filter(map -> Objects.equals(thirdType, MapUtils.getInteger(map, "type")))
                .map(map -> {
                    AccountsThirdParty thirdParty = new AccountsThirdParty();
                    thirdParty.setAccount_uuid(MapUtils.getString(map, "uuid"));
                    thirdParty.setOpen_id(MapUtils.getString(map, "idpOid"));
                    thirdParty.setType(type);
                    thirdParty.setUnion_id(MapUtils.getString(map, "idpUid"));
                    return thirdParty;
                })
                .findFirst()
                .orElse(null);
    }

    public List<String> getValidTypeByAccountUuid(String uuid) {
        List<Map<String, Object>> thirdParties = mpAccountClient.queryThirdPart(uuid, 1);

        if (CollectionUtils.isEmpty(thirdParties)) {
            return new ArrayList<>();
        }

        return thirdParties.stream()
                .map(map -> transType(MapUtils.getInteger(map, "type")))
                .collect(Collectors.toList());
    }

    public AccountsThirdParty findLastestOneByUuid(String uuid) {
        List<Map<String, Object>> thirdParties = mpAccountClient.queryThirdPart(uuid, 1);

        if (CollectionUtils.isEmpty(thirdParties)) {
            return null;
        }

        return thirdParties.stream()
                .sorted((map1, map2) -> MapUtils.getLong(map2, "createTime").compareTo(MapUtils.getLong(map1, "createTime")))
                .map(map -> {
                    AccountsThirdParty thirdParty = new AccountsThirdParty();
                    thirdParty.setAccount_uuid(MapUtils.getString(map, "uuid"));
                    thirdParty.setOpen_id(MapUtils.getString(map, "idpOid"));
                    thirdParty.setType(transType(MapUtils.getInteger(map, "type")));
                    thirdParty.setUnion_id(MapUtils.getString(map, "idpUid"));
                    return thirdParty;
                })
                .findFirst()
                .orElse(null);
    }

    @Transactional
    public int updateForDebind(Integer debindStatus, Long updateTime, String account_uuid, String type, Integer bindStatus) {
        return accountsThirdPartyDao.updateForDebind(debindStatus, updateTime, account_uuid, type, bindStatus);
    }

    @Transactional
    public void save(AccountsThirdParty accountsThirdParty) {
        accountsThirdPartyDao.merge(accountsThirdParty);
    }

    private int checkOpenIdType(String type) {
        int thirdType;
        switch (type) {
            case AccountsThirdParty.TYPE_WECHAT:
            case AccountsThirdParty.TYPE_WECHAT_OFFICIAL:
                thirdType = AccountChannelEnum.WECHAT.value();
                break;
            case AccountsThirdParty.TYPE_WEIBO:
                thirdType = AccountChannelEnum.WEIBO.value();
                break;
            case AccountsThirdParty.TYPE_QQ:
                thirdType = AccountChannelEnum.QQ.value();
                break;
            case AccountsThirdParty.TYPE_APPLE:
                thirdType = AccountChannelEnum.APPLE.value();
                break;
            case AccountsThirdParty.VISITOR:
                thirdType = AccountChannelEnum.VISITOR.value();
                break;
            default:
                throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }
        return thirdType;
    }

    private String transType(Integer type) {
        String thirdType;
        AccountChannelEnum channel = AccountChannelEnum.getAccountChannel(type);

        if (channel == AccountChannelEnum.WECHAT) {
            thirdType = AccountsThirdParty.TYPE_WECHAT;
        } else if (channel == AccountChannelEnum.QQ) {
            thirdType = AccountsThirdParty.TYPE_QQ;
        } else if (channel == AccountChannelEnum.WEIBO) {
            thirdType = AccountsThirdParty.TYPE_WEIBO;
        } else if (channel == AccountChannelEnum.APPLE) {
            thirdType = AccountsThirdParty.TYPE_APPLE;
        } else if (channel == AccountChannelEnum.VISITOR) {
            thirdType = AccountsThirdParty.VISITOR;
        } else {
            throw new ServiceException(SysCodeStatus.REQUEST_PARA_ERROR);
        }

        return thirdType;
    }
}
