package cn.taqu.account.manager;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Maps;

import cn.taqu.account.common.UuidInfoField;
import cn.taqu.account.constant.RedisKeyConstant;
import cn.taqu.account.dao.AccountsDao;
import cn.taqu.account.model.Accounts;
import cn.taqu.account.service.SoaService;
import cn.taqu.account.service.ToolsService;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.orm.Sql;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户
 * <AUTHOR>
 * 2023年8月3日上午10:07:54
 */
@Slf4j
@Repository
public class AccountsManager {

    @Autowired
    private AccountsDao accountsDao;
    @Autowired
    private SoaService soaService;
    @Autowired
    @Qualifier("accountStringRedisTemplate")
    private StringRedisTemplate accountStringRedisTemplate;
    
    /**
     * 改过
     * @param uuid
     * @param nickname
     * @param mobile
     * @return
     */
    public List<Accounts> listByUuidOrNicknameOrMobile(String uuid, String nickname, String mobile){
        // 通过手机号查uuid  以手机号为准
        String uuidMp = getUuidByMobile(mobile);
        if(StringUtils.isNotBlank(uuidMp)) {
            uuid = uuidMp;
        }
        return accountsDao.listByUuidOrNickname(uuid, nickname);
    }

    /**
     * 改过
     * 通过uuid，获取手机号，未查询到返回空字符串
     * @param uuid
     * @return
     */
    public Long getCreateTimeByUuid(String uuid) {
        return accountsDao.getCreateTimeByUuid(uuid);
    }
    
    /**
     * 改过
     * 通过uuid，获取手机号，未查询到返回空字符串
     * @param uuid
     * @return
     */
    public String getMobileByUuid(String uuid){
        return soaService.getMobileByUuidMP(uuid);
    }
    
    /**
     * 改过
     * 通过手机号，获取uuid，未查询到返回空字符串
     * @param mobile
     * @return
     */
    public String getUuidByMobile(String mobile){
        return soaService.getUuidByMobileMP(mobile);
    }

    public String getAccountNamebyUuid(String uuid){
        return accountsDao.getAccountNamebyUuid(uuid);
    }

    public int bindMobileByUuid(String mobile, String mobileCipher,String mobileDigest, String uuid){
        // 关闭写入开关，手机号置为空
        if(!ToolsService.accountsSwitchWrite.isOn(true)) {
            mobile = "";
        }
        return accountsDao.bindMobileByUuid(mobile, mobileCipher, mobileDigest, uuid);
    }

    public int setMobileNullByUuid(String mobile, String uuid){
        if(ToolsService.accountsSwitchEncryption.isOn(true)){
            return accountsDao.setMobileNullByUuidDigest(EncryptUtil.sm3(mobile), uuid);
        }else {
            return accountsDao.setMobileNullByUuid(mobile, uuid);
        }
    }

    public int updatePasswordForFirst(String password, String accountUuid){
        return accountsDao.updatePasswordForFirst(password, accountUuid);
    }

    public int updatePasswordForReset(String password, String accountUuid){
        return accountsDao.updatePasswordForReset(password, accountUuid);
    }

    public int updateMemberByUuid(Long member_id, String account_uuid){
        return accountsDao.updateMemberByUuid(member_id, account_uuid);
    }

    public long getCountByMemberId(Long member_id,Long todayBeginSecond,Long todayEndSecond){
        return accountsDao.getCountByMemberId(member_id, todayBeginSecond, todayEndSecond);
    }

    public Integer getSexTypeByAccountUuid(String account_uuid){
        return accountsDao.getSexTypeByAccountUuid(account_uuid);
    }

    public Integer getSexTypeFromMasterDBByAccountUuid(String account_uuid){
        return accountsDao.getSexTypeFromMasterDBByAccountUuid(account_uuid);
    }

    public Long getAccountIdByUuid(String uuid){
        return accountsDao.getAccountIdByUuid(uuid);
    }

    public int setNicknameByUuid(String nickname, String account_uuid){
        return accountsDao.setNicknameByUuid(nickname, account_uuid);
    }

//    /**
//     * 改过
//     * 
//     * 分组查，若性能不好，再优化
//     * 
//     * @param accountUuids
//     * @return
//     */
//    public Map<String, Object>  getMobileByRegAccountUuids(List<String> accountUuids){
//        Map<String, Object> map = Maps.newHashMap();
//        
//        List<List<String>> partition = Lists.partition(accountUuids, 10);
//        for (List<String> list : partition) {
//            map.putAll(soaService.getMobileByUuidsMP(list));
//        }
//        
//        return map;
//    }
    
    /**
     * 改过
     * 根据uuid查询用户，用户存在，则查中台补全手机号信息
     * @param accountUuid
     * @return
     */
    public Accounts getByUuid(String accountUuid){
        return getByUuid(accountUuid, true);
    }
    
    /**
     * 新增
     * 根据uuid查询用户，用户存在，则查中台补全手机号信息
     * @param accountUuid
     * @param needMobile 是否查询手机号，只有不需要手机号的业务才能传 false
     * @return
     */
    public Accounts getByUuid(String accountUuid, boolean needMobile){
        Accounts accounts = accountsDao.getByUuid(accountUuid);
        if(accounts != null) {
            if(needMobile) {
                String phone = soaService.getMobileByUuidMP(accountUuid);
//                String mobile = accounts.getMobile();
//                String mobileCipher = accounts.getMobile_cipher();
//                if(ToolsService.accountsSwitchEncryption.isOn(true)) {
//                    mobile = EncryptUtil.decrypt(mobileCipher);
//                }
//                
//                // 以中台手机号为准
//                if(!Objects.equals(mobile, phone)){
//                    // 存在脏数据
//                    log.warn("用户系统手机号异常，重置为中台手机号");
////                    bindMobileByUuid(phone, EncryptUtil.encrypt(phone), EncryptUtil.sm3(phone) , accountUuid);
////                    setMobileCache(accountUuid, phone);
//                }
                
                accounts.setMobile(phone);
            }
        }
        
        return accounts;
    }

    public List<Object[]> getUuidAndAccountNameInUuids(Collection<String> uuids){
        return accountsDao.getUuidAndAccountNameInUuids(uuids);
    }

    public List<Object[]> getUuidAndAccountNameAndRegtimeInUuids(Collection<String> uuids){
        return accountsDao.getUuidAndAccountNameAndRegtimeInUuids(uuids);
    }

    public List<String> listUuidByAccountName(String accountName){
        return accountsDao.listUuidByAccountName(accountName);
    }

    /**
     * 根据手机号查询uuid和昵称，先通过中台查uuid，再查本地库
     * 改过
     * @param mobile
     * @return
     */
    public Accounts getUuidAndNameByMobile(String mobile){
        Accounts accounts = null;
        String accountUuid = getUuidByMobile(mobile);
        if(StringUtils.isNotBlank(accountUuid)) {
            accounts = new Accounts();
            accounts.setUuid(accountUuid);
            String accountName = accountsDao.getAccountNamebyUuid(accountUuid);
            accounts.setAccount_name(accountName);
        }
        
        return accounts;
    }
    
    @Deprecated
    public String getUuidByEmail(String email){
        return accountsDao.getUuidByEmail(email);
    }

    public Object[] getByAccountKey(String accountKey){
        return accountsDao.getByAccountKey(accountKey);
    }

    public int updateAccountKeyByUuid(String accountKey, String account_uuid){
        return accountsDao.updateAccountKeyByUuid(accountKey, account_uuid);
    }

    public int clearAccountKey(String accountKey){
        return accountsDao.clearAccountKey(accountKey);
    }

    /**
     * 改过
     * 
     * @param mobileList
     * @return
     */
    public Map<String, String> batchGetUuidByMobile(List<String> mobileList){
        Map<String, String> result = Maps.newHashMap();
        
        Map<String, Object> map = soaService.getUuidsByMobileMP(mobileList);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if (value != null && value instanceof Map) {
                Map infoMap =  (Map) value;
                result.put(entry.getKey(), MapUtils.getString(infoMap, "uuid"));
            }
        }
        
        return result;
    }

    public int updateByUuid(Map<String, Object> fieldValue, String accountUuid){
        // 关闭写入开关，手机号置为空
        if(!ToolsService.accountsSwitchWrite.isOn(true)) {
            fieldValue.put("mobile", "");
        }
        return accountsDao.updateByUuid(fieldValue, accountUuid);
    }

    public int updateSexType(Integer  param, String uuid){
        return accountsDao.updateSexType(param, uuid);
    }

    public int updateDestroyStatus(String accountName, String avatar, String uuid){
        return accountsDao.updateDestroyStatus(accountName, avatar, uuid);
    }

    public int updateMobilePlace(String uuid, String mobilePlace){
        return accountsDao.updateMobilePlace(uuid, mobilePlace);
    }

    public Accounts getUuidAndPwdByName(String accountName){
        return accountsDao.getUuidAndPwdByName(accountName);
    }

//    /**
//     * 改过
//     * 手机号先查uuid，再查本地库，再将手机号写入对象
//     * 
//     * @param mobile
//     * @return
//     */
//    public Accounts getUuidAndPwdByMobile(String mobile){
//        Accounts accounts = null;
//        String accountUuid = getUuidByMobile(mobile);
//        if(StringUtils.isBlank(accountUuid)) {
//            return accounts;
//        }
//        accounts = getUuidAndPwdByUuid(accountUuid);
//        if(accounts != null) {
//            accounts.setMobile(mobile);
//        }
//        return accounts;
//    }
    
    public Accounts getUuidAndPwdByUuid(String accountUuid){
        return accountsDao.getUuidAndPwdByUuid(accountUuid);
    }

//    /**
//     * 改过
//     * 根据uuid查询用户，用户存在，则查中台补全手机号信息
//     * @param accountUuid
//     * @return
//     */
//    public Accounts getRegStyleByUuid(String accountUuid){
//        Accounts accounts = accountsDao.getRegStyleByUuid(accountUuid);
//        if(accounts != null) {
//            String mobile = getMobileByUuid(accountUuid);
//            accounts.setMobile(mobile);
//        }
//        return accounts;
//    }

    /**
     * 获取用户状态
     *
     * @param accountUuid
     * @return
     */
    public Integer getAccountStatusByUuid(String accountUuid){
        return accountsDao.getAccountStatusByUuid(accountUuid);
    }

    /**
     * @param accountUuid
     */
    public Integer renewAccount(String accountUuid){
        return accountsDao.renewAccount(accountUuid);
    }

    /**
     * @param accountsUuidList
     * @return
     */
    public int setMobileNullInUuid(List<String> accountsUuidList){
        return accountsDao.setMobileNullInUuid(accountsUuidList);
    }

    /**
     * @param accounts
     * @return
     */
    public Accounts merge(Accounts accounts) {
        // 开关关闭，不写手机号
        if(!ToolsService.accountsSwitchWrite.isOn(true)) {
            accounts.setMobile("");
        }
        return accountsDao.merge(accounts);
    }

    /**
     * @param sql
     * @param i
     * @param j
     * @param b
     * @return
     */
    public List<Accounts> queryForPage(Sql sql, int i, int j, boolean b) {
        return accountsDao.queryForPage(sql, i, j, b).getData();
    }

    /**
     * @param sql
     * @return
     */
    public List<Map<String, Object>> queryForList(Sql sql) {
        return accountsDao.queryForList(sql);
    }

    /**
     * 设置手机号缓存
     * @param uuid
     * @param mobile
     */
    public void setMobileCache(String uuid, String mobile) {
        Map<String, String> hashValues = new HashMap<>();
        hashValues.put(UuidInfoField.MOBILE, mobile);
        // 2024.05.27 新增加，原来没写缓存，说明数据不准确
//        hashValues.put(UuidInfoField.MOBILE_CIPHER, mobileCipher);
        // 2024.05.27 不记录缓存
//        hashValues.put(UuidInfoField.IS_CHECK_MOBILE, "1");
//        hashValues.put(UuidInfoField.IS_BIND_MOBILE, "1");
        accountStringRedisTemplate.opsForHash().putAll(RedisKeyConstant.ACCOUNT_INFOS_UUID.setArg(uuid), hashValues);
    }
}
