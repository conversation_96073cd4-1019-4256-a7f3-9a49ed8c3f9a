package cn.taqu.account;

import cn.taqu.core.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 容器关闭
 *  1. 监听kill 命令
 *  2. 设置定时任务执行开关 置为关闭
 *  3. 等待n秒，让正在执行的任务执行完毕
 *  4. 获取定时任务执行类，执行销毁。
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-07 18:03
 */
@Component
@Slf4j
public class ApplicationCloseListener implements ApplicationListener<ContextClosedEvent> {

    /**
     * 延迟关闭定时任务时间
     */
    private static Long DELAY_CLOSE_SCHEDULE_SECONDS  = 5L;

    /**
     * 应用是否正在关闭
     */
    public static volatile AtomicBoolean APPLICATION_STOPPING = new AtomicBoolean(false);

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        if(APPLICATION_STOPPING.get()){
            return;
        }
        // 注意这边只能监听kill pid，不能监听kill -9(直接杀进程了肯定监听不到了)
        log.info("监听到应用关闭命令,倒计时[{}]秒销毁定时任务", DELAY_CLOSE_SCHEDULE_SECONDS);
        APPLICATION_STOPPING.set(true);

        try {
            if(DELAY_CLOSE_SCHEDULE_SECONDS > 0){
                Thread.sleep(DELAY_CLOSE_SCHEDULE_SECONDS * 1000);
            }
            handleCloseSchedule();
            log.info("执行关闭定时任务成功");
        } catch (InterruptedException e) {
            log.warn("执行关闭定时任务失败");
        }
    }

    /**
     * 关闭时 处理定时任务
     *
     */
    private void handleCloseSchedule(){
        ScheduledAnnotationBeanPostProcessor sBeanProcessoor = SpringContextHolder.getBean(ScheduledAnnotationBeanPostProcessor.class);
        if(null != sBeanProcessoor){
            // 服务启动时，会将注解了定时任务的类put到 ScheduledAnnotationBeanPostProcessor 的 scheduledTasks 中
            // destroy 方法可以将定时任务清除
            sBeanProcessoor.destroy();
        }
    }
}
