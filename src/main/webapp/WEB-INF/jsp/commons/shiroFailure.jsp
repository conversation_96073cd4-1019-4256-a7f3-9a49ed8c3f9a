<%@page import="cn.taqu.core.utils.Encodes"%>
<%@ page language="java" pageEncoding="UTF-8"
	contentType="text/html; charset=UTF-8" isErrorPage="true"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE>
<html>
<head>
<link rel="shortcut icon" href="${pageContext.request.contextPath}/static/style/images/favicon.ico" />
<% 
	String sn = request.getParameter("sn");
	if(sn==null || sn.trim().isEmpty()) {
		sn = "厦门海豹后台系统";
	} else {
		sn = new String(sn.getBytes("iso8859-1"), "UTF-8");
	}
%>
<title><%=sn %></title>
<style>
* {
	margin: 0;
	padding: 0;
}

body {
	font: 12px/1.5 tahoma, arial, "宋体";
	color: #666;
	background: #FFF
}

.error-404 {
	background-color: #EDEDF0
}

.module-error {
	margin-top: 182px
}

.module-error .error-main {
	margin: 0 auto;
	width: 420px
}

.module-error .label {
	float: left;
	width: 160px;
	height: 151px;
}

.error {
	background: url('../static/style/images/tipinfo.png') 0 0 no-repeat;
}

.success {
	background: url('/admin/Tpl/ThemeFiles/images/success.png') 0 0
		no-repeat;
}

.module-error .info {
	margin-left: 182px;
	line-height: 1.8
}

.module-error .title {
	color: #666;
	font-size: 14px
}

.module-error .error1 {
	color: red;
}

.module-error .reason {
	margin: 8px 0 18px 0;
	color: #666
}

.warn {
	padding:3px 5px;
	border: 0px solid #F00;
	color: #AD8200;
}

.btn {
	padding:2px 4px;
	font-family:微软雅黑;
	font-size:10px;
}
</style>
<script type="text/javascript">
if (window != top){
	top.location.href = location.href
} 
function closeWin() {
	window.close();
	setTimeout("closeFail()", 280);
}

function closeFail() {
	alert("因为浏览器不支持，无法关闭当前页，请直接使用浏览器的关闭功能");
}
</script>
</head>
<body class="error-404">
	<div id="doc_main">
		<div class="bd clearfix">
			<div class="module-error">
				<div class="error-main clearfix">
					<div class="label error"></div>
					<div class="info">
						<h3 class="title error1">登录失败！</h3>
						<div class="reason">
							<p>失败原因：</p>
							<%
								String msg = request.getParameter("msg");
								if(msg!=null) {
									msg = new String(Encodes.decodeBase64(msg));
								}
							%>
							<p><%=msg==null?"权限验证失败":msg %></p>
						</div>
						<div class="oper">
							您可以<a href="#" class="btn" onclick="closeWin();">关闭页面</a>或<a href="#" class="btn" onclick="window.location.href='${pageContext.request.contextPath}/logout'">重新登录</a>
							<div class="warn">
								重新登录成功后，所有已登录系统也将自动使用新登录用户重新登录!
							</div>
						</div>
						<%-- 
						<div class="oper">
							<p>
								页面将在 <span class="wait">3</span> 秒后自动跳转，如果不想等待请点击 <a
									href="http://hbms.test.taqu.cn:2000/admin.php/Push/push_list_new">这里</a>
								跳转
							</p>
						</div>
						--%>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>