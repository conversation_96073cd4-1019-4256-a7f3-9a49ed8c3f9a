<%@ page import="cn.taqu.core.common.client.RpcClientFactory" %>
<%@ page import="cn.taqu.core.common.client.RpcServer" %>
<%@ page import="cn.taqu.core.common.client.SoaResponse" %>
<%@ page import="cn.taqu.core.utils.JsonUtils" %>
<%@ page import="com.fasterxml.jackson.core.type.TypeReference" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.io.PrintWriter" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>RPC TEST</title>
</head>
<body>
<%
    if ("rpc".equals(request.getParameter("action"))) {
        Map<String, Object> params = null;
        String form = request.getParameter("form");
        if (form != null && !"".equals(form.trim())) {
            params = JsonUtils.stringToObject("{" + form + "}", new TypeReference<Map<String, Object>>() {});
        }
        SoaResponse res = RpcClientFactory.create(RpcServer.JAVA.ACCOUNT).call(request.getParameter("service").trim(), request.getParameter("method").trim(), params);
        PrintWriter pw = response.getWriter();
        pw.write(res.toJsonString());
        pw.flush();
        pw.close();
    } else {
%>
<form action="grpc.jsp?action=rpc" method="post" target="_blank">
    service:<input type="text" style="width: 300px" name="service"><br/><br/>
    method:<input type="text" style="width: 300px" name="method"><br/><br/>
    form:{<input type="text" style="width: 1000px" name="form">}<br/><br/>
    <input type="submit" value="request"/>
</form>
<% } %>
</body>
</html>