<%@ page import="org.apache.commons.codec.binary.Base64"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>SOA TEST</title>
</head>
<body>
<%
	if("soa".equals(request.getParameter("action"))) {
		String url = "api?service="+request.getParameter("service").trim();
			url += "&method="+request.getParameter("method").trim();
			url += "&form="+Base64.encodeBase64String(("["+request.getParameter("form")+"]").trim().getBytes());
			url += "&token=326df6aad434061a2468213d7c8d6349&platform_name=android&platform_id=1&appcode=1&app_version=5001&channel=taqu&access=WIFI&timestamp=1477874740&alias=&gender=1&ip=127.0.0.1&longitude=&latitude=";
			url += "&distinctRequestId=**************************************";
		response.sendRedirect(url);
	} else {
%>
<form action="index.jsp?action=soa" method="post" target="_blank">
	service:<input type="text" style="width: 300px" name="service"><br/><br/>
	method:<input type="text" style="width: 300px" name="method"><br/><br/>
	form:[<input type="text" style="width: 1000px" name="form">]<br/><br/>
	<input type="submit" value="request"/>
</form>
<% } %>
</body>
</html>