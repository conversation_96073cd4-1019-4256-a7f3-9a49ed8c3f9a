spring.aop.proxy-target-class=true
security.basic.enabled=false
management.security.enabled=false
spring.jackson.default-property-inclusion=non_null
logging.level.root=info

#the service code of current web app
service.code = jaccount
service.index = j2
service.name = \u8D26\u53F7\u7CFB\u7EDF

#spring.profiles.active	dev/test/prod
spring.profiles.active=dev

#\u7CFB\u7EDF\u8BC6\u522B\u7801
shiro.serviceCode = ${service.code}

server.context-path=/tq-account

spring.mvc.view.prefix=/WEB-INF/jsp/
spring.mvc.view.suffix=.jsp

# \u8FDE\u63A5\u6C60\u914D\u7F6E
# \u521D\u59CB\u5316\u5927\u5C0F\uFF0C\u6700\u5C0F\uFF0C\u6700\u5927
spring.datasource.initialSize=100
spring.datasource.minIdle=20
spring.datasource.maxActive=300
spring.datasource.query-timeout=3

# \u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.maxWait=3000

# \u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.timeBetweenEvictionRunsMillis=90000

# \u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.minEvictableIdleTimeMillis=1200000
spring.datasource.validationQuery=/* ping */ SELECT 1
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false

# \u6253\u5F00PSCache\uFF0C\u5E76\u4E14\u6307\u5B9A\u6BCF\u4E2A\u8FDE\u63A5\u4E0APSCache\u7684\u5927\u5C0F
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20

# \u914D\u7F6E\u76D1\u63A7\u7EDF\u8BA1\u62E6\u622A\u7684filters\uFF0C\u53BB\u6389\u540E\u76D1\u63A7\u754C\u9762sql\u65E0\u6CD5\u7EDF\u8BA1\uFF0C'wall'\u7528\u4E8E\u9632\u706B\u5899
spring.datasource.filters=stat,slf4j


#dict table
pro.dict.tablename=dict
pro.dict.field.key=name
pro.dict.field.display=display
pro.dict.field.value=value

#version
pro.debug=true
pro.version=1.0.0

#max live time from verify code on redis
verifycode.validTime=1800

#max times for every device every day can register
member.reg.limit.daily = 10

#max times for every mobile no every day can send sms
verify.code.mobile.limit.daily = 10

#max times for every device every day can send sms
verify.code.device.limit.daily = 10

#nickname can't not contain these chars when register
reg.nick.special.char = @

#qucoin webview config
qucoin.webview.1.img=http://miscimg01.touchcdn.com/homepage/da059e252bfffc8eac54372f440b7ef6.jpg
qucoin.webview.1.title=\u8DA3\u5E01\u5546\u57CE
qucoin.webview.1.description=\u9686\u91CD\u4E0A\u7EBF
qucoin.webview.1.relaction=m=wap&a=url&ul=http://***********:3002/v1/Info/duibaLogin
qucoin.webview.1.subtitle_color=

qucoin.webview.2.img=http://miscimg01.touchcdn.com/homepage/684015ac0e41c5af57fea88d29a6b901.jpg
qucoin.webview.2.title=\u5BCC\u8C6A\u6392\u884C\u699C
qucoin.webview.2.description=\u5BCC\u8C6A\u6392\u884C\u699C
qucoin.webview.2.relaction=m=wap&a=url&ul=http://forum.test.taqu.cn:1001/forumx.php/QuCoin/rankList
qucoin.webview.2.subtitle_color=

login.send.verifycode.times.limit = 5
reset.send.verifycode.times.limit = 10
register.send.verifycode.times.limit = 10
modify.send.verifycode.times.limit = 10
bind.send.verifycode.times.limit = 10
certification.send.verifycode.times.limit = 10

default.illegal.avatar = avatar/illegal_avatar.png

#php\u914D\u7F6E

me.weixin.relaction = http%3a%2f%2fmall.test.taqu.cn%3a2002%2fv3%2fcommon%2fweixin

avatar.url = https://avatar01.jiaoliuqu.com/
#IOS\u5BA1\u6838\u7528\u7684\u6D77\u5916\u7CFB\u7EDF\uFF0C0\u8868\u793A\u975E\u6D77\u5916\u7CFB\u7EDF\uFF0C1\u8868\u793A\u662F\u6D77\u5916\u7CFB\u7EDF
me.is.overseas.system = 0

server.port=8080

#\u6700\u5927\u7EBF\u7A0B
server.tomcat.max-threads=1200
#\u5907\u7528\u7EBF\u7A0B\u94FE\u63A5
server.tomcat.min-spare-threads=200
#\u6700\u5927\u8FDE\u63A5\u6570
server.tomcat.max-connections=20000
#\u6700\u5927\u7B49\u5F85\u961F\u5217\uFF0C\u5982\u679C\u5E76\u53D1\u8D85\u8FC721000\u5219\u76F4\u63A5\u62D2\u7EDD
server.tomcat.accept-count=8000


management.port=8000
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoints.shutdown.exposure.include=*
management.metrics.distribution.percentiles-histogram[http.server.requests]=true
management.metrics.tags.application=j2
#management.endpoints.web.base-path=/admin

#tomcat monitor
server.tomcat.mbeanregistry.enabled=true

#sentinel
project.name=tq-members
project.biz.name=mp
nacos.address=mse-837d4fa4-nacos-ans.mse.aliyuncs.com:8848
nacos.namespace=sentinel
nacos.groupid=sentinel_rules

warmup.endpoint.enabled=false

warmup.client.maxAllowedWarmupSeconds=300
warmup.client.landingSeconds=30
