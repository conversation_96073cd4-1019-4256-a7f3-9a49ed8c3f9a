[{"path": "/api?service=info&method=getInfoByUuid", "contentType": "application/x-www-form-urlencoded", "total": 5000, "initRate": 4, "rate": 30, "payload": "form=W1siZGdlMzZmMHA2bWJzIiwiZGdnMGlub2ozdDRzIl0sIFsic2V4X3R5cGUiLCJjcmVhdGVfdGltZSIsImxvZ2luX2Nsb25lZCIsInBsYXRmb3JtX2lkIiwiYWNjb3VudF9uYW1lIiwibW9iaWxlIiwiYWNjb3VudF9uYW1lX3N0YXR1cyIsICJhdmF0YXIiLCAiYWdlIiwgImdlbmRlcl9jZXJ0aWZpY2F0aW9uIiwgImVkdWNhdGlvbiIsImluY29tZSIsImxvbmdpdHVkZSIsImxhdGl0dWRlIiwiY2l0eV9pZCIsImFjY291bnRfbmFtZSIsImFjdGl2ZV90aW1lIiwgInJlYWxfYXZhdGFyX2NlcnRpZmljYXRpb24iXV0="}, {"path": "/api?service=info&method=getInfoByUuid", "contentType": "application/x-www-form-urlencoded", "total": 5000, "initRate": 4, "rate": 30, "payload": "form=W1siYmdqYmJkZWVkZWNhZmZjYiIsImJnamZiYWJoamhjZ2djZmoiXSxbInV1aWQiLCJhY2NvdW50X25hbWUiLCJzZXhfdHlwZSIsImF2YXRhciIsImFnZSIsImxvZ2luX2FwcGNvZGUiLCJsb2dpbl9jbG9uZWQiLCJjcmVhdGVfdGltZSIsInBsYXRmb3JtX2lkIiwiYWNjb3VudF9zdGF0dXMiXV0="}, {"path": "/api?service=info&method=getInfoByUuid", "contentType": "application/x-www-form-urlencoded", "total": 5000, "initRate": 4, "rate": 30, "payload": "form=W1siYmdqYmJkZWhjaWphYmpkYSIsImFiY2QiXSxbInV1aWQiLCJhY2NvdW50X25hbWUiLCJzZXhfdHlwZSIsImF2YXRhciIsImFnZSIsImxvZ2luX2FwcGNvZGUiLCJsb2dpbl9jbG9uZWQiLCJjcmVhdGVfdGltZSIsInBsYXRmb3JtX2lkIiwiYWNjb3VudF9zdGF0dXMiXV0="}, {"path": "/api?service=members&method=getTokenByUuids", "contentType": "application/x-www-form-urlencoded", "total": 2500, "initRate": 2, "rate": 20, "payload": "form=W1siZGdlMzZmMHA2bWJzIiwiZGdnMGlub2ozdDRzIl1d"}, {"path": "/api?service=members&method=getTokenByUuids", "contentType": "application/x-www-form-urlencoded", "total": 2500, "initRate": 2, "rate": 20, "payload": "form=WyJiZ2piYmRlaGNpamFiamRhIl0="}, {"path": "/api?service=accountsLocationInfo&method=getLocationInfoByUuid", "contentType": "application/x-www-form-urlencoded", "total": 1500, "initRate": 1, "rate": 15, "payload": "form=WyJkZ2UzNmYwcDZtYnMiXQ=="}, {"path": "/api?service=info&method=getAccountInfoOfMyMenu", "contentType": "application/x-www-form-urlencoded", "total": 1500, "initRate": 1, "rate": 11, "payload": "form=WyJiZ2lkZmRoamlhaWFoaWlpIl0="}, {"path": "/api?service=region&method=mgetProvinceCityInfoByUuids", "contentType": "application/x-www-form-urlencoded", "total": 1500, "initRate": 2, "rate": 15, "payload": "form=W1siZGdlMzZmMHA2bWJzIiwiZGdnMGlub2ozdDRzIiwiYmdpZGZkaGppYWlhaGlpaSJdXQ=="}, {"path": "/api?service=accountsGrayList&method=isInGrayList", "contentType": "application/x-www-form-urlencoded", "total": 1500, "initRate": 1, "rate": 15, "payload": "form=WyIiLCJiZ2lkZmRoamlhaWFoaWlpIl0="}, {"path": "/api?service=info&method=getAllure", "contentType": "application/x-www-form-urlencoded", "total": 1500, "initRate": 1, "rate": 15, "payload": "form=WyJiZ2lkZmRoamlhaWFoaWlpIl0="}, {"path": "/api?service=accountActionProfile&method=getAccountAction", "contentType": "application/x-www-form-urlencoded", "total": 1500, "initRate": 1, "rate": 15, "payload": "form=WyJiZ2lkZmRoamlhaWFoaWlpIiwiTE9HSU4iXQ=="}]