<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 日志文件目录 -->
    <property name="log.dir" value="./data/logs/sjava"/>

    <springProfile name="prod">
    </springProfile>

    <springProfile name="dev, test">
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                <layout class="cn.taqu.core.log.LogbackLayout"/>
            </encoder>
        </appender>
    </springProfile>


    <!-- trace日志 -->
    <appender name="traceFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/trace.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.core.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>TRACE</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- debug日志 -->
    <appender name="debugFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/debug.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.core.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- info日志 -->
    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.core.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- warn日志 -->
    <appender name="warnFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/warn.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.core.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- error日志 -->
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/error.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.core.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name="org.apache.shiro" level="WARN"/>
    <logger name="jdbc.sqltiming" level="ERROR"/>
    <logger name="jdbc.sqlonly" level="WARN"/>
    <logger name="jdbc.resultset" level="ERROR"/>
    <logger name="jdbc.audit" level="ERROR"/>
    <logger name="jdbc.connection" level="ERROR"/>

    <springProfile name="prod">
        <logger name="cn.taqu.account" level="INFO"/>
        <root level="INFO">
            <appender-ref ref="errorFile"/>
            <appender-ref ref="warnFile"/>
            <appender-ref ref="infoFile"/>
            <appender-ref ref="debugFile"/>
            <appender-ref ref="traceFile"/>
        </root>
    </springProfile>

    <springProfile name="dev, test">
        <logger name="cn.taqu" level="INFO"/>
        <root level="INFO">
            <appender-ref ref="errorFile"/>
            <appender-ref ref="warnFile"/>
            <appender-ref ref="infoFile"/>
            <appender-ref ref="debugFile"/>
            <appender-ref ref="traceFile"/>
            <appender-ref ref="console"/>
        </root>
    </springProfile>

</configuration>