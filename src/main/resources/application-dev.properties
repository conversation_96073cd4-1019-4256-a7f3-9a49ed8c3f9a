#etcd.url = http://10.73.152.217:2379
#etcd.url = http://10.73.152.217:2379
etcd.url = http://192.168.100.35:2479

etcd.watchDir = account

server.port=8081
grpc.port=6565

#server\u8DEF\u5F84
shiro.casServerUrl = http://10.10.88.244:8080/tq-ssoserver

# primary datasource
#datasource.primary.url=********************************************************************************
#datasource.primary.username=apiuser
#datasource.primary.password=5eZG9TBeNRCyxG4C78Od
#datasource.primary.driver-class-name=com.mysql.jdbc.Driver
datasource.primary.url=********************************************************************************
datasource.primary.username=javauser
datasource.primary.password=java123456
#datasource.primary.driver-class-name=com.mysql.jdbc.Driver

#wei xin client
weixin.appid=wx50de2cc0b80052df
weixin.secret=5a2408061fa291644ea135e94f7338ab
weixin.grant_type=authorization_code
api.weixin.base = https://api.weixin.qq.com

weixin.official.appid = wxec460736049b5952
weixin.official.secret = f202af00aed65649e92df89552f6c99c
weixin.official.grant_type = authorization_code
api.weixin.official.base = https://api.weixin.qq.com

#redis connection configuration
#redis.account.host=************
#redis.account.port=6380

redis.ticket.host=************
redis.ticket.port=6788

redis.account.host=************
redis.account.port=6789

redis.uuid.host=************
redis.uuid.port=10000

redis.member.host=************
redis.member.port=6384

redis.experience.host=************
redis.experience.port=6788

redis.privilege.host=************
redis.privilege.port=6788

redis.level.host=************
redis.level.port=6788

redis.medal.host=************
redis.medal.port=6788

redis.identity.host=************
redis.identity.port=6788

redis.certification.host=************
redis.certification.port=6788

redis.sn.host=************
redis.sn.port=6788

redis.region.host=************
redis.region.port=6788

redis.recommend.host=************
redis.recommend.port=6788

redis.moderator.host=************
redis.moderator.port=6788

#default avatar baseurl
default.avatar.baseurl = https://avatar01.jiaoliuqu.com/avatar/default/

#mq url for soa call
soa.mq.url = http://************:5001/v2/Soa/jService

#mq url for soa call
soa.forumMq.url = http://***********:5001/v2/Soa/jService

#shiro filterChainDefinitions
shiro.filterChainDefinitions = /api=hosts[127.0.0.1,10.*.*.*]

vip.url = https%3A//h5.test.taqu.cn/html/app/member-center/index.html
vip.intro.url = https%3A//h5.test.taqu.cn/html/app/member-center/member.html

# \u6821\u9A8C\u7528\u6237\u767B\u5F55\u9650\u5236\uFF08token\uFF09\uFF08\u6B21\u6570\uFF09
verify.login.token.limit.times = 10
# \u6821\u9A8C\u7528\u6237\u767B\u5F55\u9650\u5236\uFF08token\uFF09\uFF08\u65F6\u95F4\u8303\u56F4 \u5355\u4F4D\uFF1A\u5206\u949F\uFF09
verify.login.token.limit.range = 5
# \u6821\u9A8C\u7528\u6237\u767B\u5F55\u9650\u5236\uFF08ip\uFF09\uFF08\u6B21\u6570\uFF09
verify.login.ip.limit.times = 10
# \u6821\u9A8C\u7528\u6237\u767B\u5F55\u9650\u5236\uFF08ip\uFF09\uFF08\u65F6\u95F4\u8303\u56F4 \u5355\u4F4D\uFF1A\u79D2\uFF09
verify.login.ip.limit.range = 5
# \u6821\u9A8C\u7528\u6237\u767B\u5F55\u9650\u5236\uFF08account\uFF09\uFF08\u6B21\u6570\uFF09
verify.login.account.limit.times = 10
# \u6821\u9A8C\u7528\u6237\u767B\u5F55\u9650\u5236\uFF08account\uFF09\uFF08\u65F6\u95F4\u8303\u56F4 \u5355\u4F4D\uFF1A\u79D2\uFF09
verify.login.account.limit.range = 5it 

#max times for every device every day can register
member.reg.limit.daily = 10000

#max times for every mobile no every day can send sms
verify.code.mobile.limit.daily = 1000

#max times for every device every day can send sms
verify.code.device.limit.daily = 1000

login.send.verifycode.times.limit = 1000
reset.send.verifycode.times.limit = 1000
register.send.verifycode.times.limit = 1000
modify.send.verifycode.times.limit = 1000
bind.send.verifycode.times.limit = 1000
certification.send.verifycode.times.limit = 1000

#sentinel
nacos.address=mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848

# reduce size for app startup
spring.datasource.initialSize=16
