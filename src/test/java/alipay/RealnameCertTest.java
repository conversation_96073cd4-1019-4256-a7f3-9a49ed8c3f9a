package alipay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserCertdocCertverifyConsultRequest;
import com.alipay.api.request.AlipayUserCertdocCertverifyPreconsultRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserCertdocCertverifyConsultResponse;
import com.alipay.api.response.AlipayUserCertdocCertverifyPreconsultResponse;
import org.junit.Before;
import org.junit.Test;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-02 10:59
 */
public class RealnameCertTest {

    AlipayClient alipayClient = null;

    @Before
    public void ss(){String appId = "2021002117693305";
        String priKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCB7RLBwjpHB8L5m15vDdC4PHFjiPE+V8fhZrtbZgdYWEwISazYKShCFwaMQ1xsvEhCuh5RuGuImVDxMGpz8ppqG8rb2EekEEmFNi0aq3o+FpPINfYsmBWnqBLtYY7+DDiJxflmgxvqfYyTDbFEvfUG/tZs33tExZSPKP/x5PzJC6ruG/pAf6g+/qufdrKEQw1CZb0oPCy5uLCTU/DZLEui7uXTy1FKi3Na2wVWY0K8iKtVGiztKpGt9MHUQnnI6MDhpnOA/w5dD+zfobOqDXrrh33XBsL2esw5JR+oKy2ZA7P3uee3yrv1GC5t3zCsb8zoFCc2pl4xnXkQn0Xz1gfhAgMBAAECggEAHlBtLu4dYNNLx6dbmjez+jiIEDw0ZWJfzZSFpGai0hcP7mMaQ/keXoU2zuUsnGY1yfPY/SI1XNLA0vWD8Peq1ZUUeMT2ui3eqYkhbvaReAkWhAErJe56JvsNJ3u6vEWnr2WUsjlm+wg+E1V/hdV4ZSBycUaXYU1kQx7C0PKnlFSfEHdlTcFB6+m55jhn7XQrnBVLiy8xVTjLc/s+LFdVOgjoWrtZU78ftnxhPQIDq4YnFNy6oiy+6/sq8Z/k7iIxQ4YDRnv1w5zNWfV2psdLWyfpE4Y2dsFE5diZe+KwX+QQCmhi7bGcAX/YYmEqMIgwN8YexZ28LfGl9+lQhupPHQKBgQDx5ewpPwcAPf+E0uxjGx+wCBjZ44cIy7jiGs7RY3whVWt6S4A7AAxDIJlVF5WkMDDAzI9eJ1YegLiOcnS1QRm9a19SxVo/fQ1fYP9mNDvJgWrAyQfUzMQdqHeTG9CFmq9h1i6YYmvYtR54FeRm4AnNFTM4TBkiPc6QiQBZkh1DowKBgQCJgBWaPZT9Kr11p3Owf8nLSNnCmNK7OBR0ZzsSLHNh+ugdrAaC6SKhsZudGlXNmcGyql0thEMpjO4idaD9mmPb30uEgZiD8hCiG6AqiFpwOxi7uYuDFRvb3eIUWagmZtMojZbOUbqJqY1cXnuoXrdRm8I8fFkjC7w50aYMTfxeqwKBgQCuAHveN44rqG0y+B4+M/aZQ1nM8hPiSdjWG/6FNBDIUY+cSs8UobYwWzSwtuRXVKrdJMhEK/uQuXsITGCTOR7w2FmbSobegG+sGjPdKtmO218VCgZuFlVEGDjbQvi+AkUCFXUy/CkjmzUtoGHJdoLWWNedOXAi5169LS9pPud6DQKBgFqJlS/flsfwqZnb7ehs7GHHkWN6H2AZeBaBlrrLvBnrDYXkLNFsU1pmyVFz8sWTlaBgX0RGT7b/ThownBYA3/n6GQ6iCtvuuxcS267F4878irepX/yrHJH0kxXVR0wp7qZIPOGTnSFiHGuS5xehblH1HruXIVZbNtBXhF+bxi5LAoGAW7IPmuNCzVkk8dRMy1s42/X9a6NasXRni7uBy7oKb2KUFEUKsd2/dzkGLEZL2lYI9FAlcOI1LmLnPlbcH4mb7qtwARbu8nCA5wm+n3pG7MpOGago+9LgF0vjFC4Ex+eQrgQcZfMREWltDwI5FgkLTL7h0OmUm72I+cl82CBhcQ4=";
        String pubKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnAL6nHw1hipd1DzhemtWkO7uGYd+MKy41lsMJYueoUzC0+Y/MnrgipDDvnU+ydmuYYP/gfa7G8gE1M2befsLLngO4XDpZHDo938tl6bRq+S/PEKIUFi0xIHvdjmI1Vp7s0fOzIkXB8VRUucR9EVYse6uzgha/rmjD1Nv0A71758O6hSbxAI4UqIsdB2eWbjeS7deYBTVsp6KYVM8/YSx634aJEwF3cyXJ4xabpQ2HRi1dQ9QpTqAh3fOj2dSGQMQH49fTrB3/bmjuYKwfQpiR+s673PH0wNHQPGayxUHlxdCOIhbXH5tm/v8p64nGVZbreTXyAqOhMOjVKUqgV9roQIDAQAB";

        alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",appId, priKey,"json","GBK",pubKey,"RSA2");
    }

    @Test
    public void realnameCertTest() throws AlipayApiException {
        AlipayUserCertdocCertverifyPreconsultRequest request = new AlipayUserCertdocCertverifyPreconsultRequest();
//        request.setBizContent("{" +
//                "\"user_name\":\"李杰\"," +
//                "\"cert_type\":\"IDENTITY_CARD\"," +
//                "\"cert_no\":\"350212199512150051\"," +
//                "\"mobile\":\"\"," +
//                "\"logon_id\":\"15160096020\"," +
//                "\"ext_info\":\"{}\"" +
//                "  }");
        request.setBizContent("{" +
                "\"user_name\":\"洪少杰\"," +
                "\"cert_type\":\"IDENTITY_CARD\"," +
                "\"cert_no\":\"350681199006090097\"," +
                "\"mobile\":\"\"," +
                "\"logon_id\":\"<EMAIL>\"," +
                "\"ext_info\":\"{}\"" +
                "  }");
        AlipayUserCertdocCertverifyPreconsultResponse response = alipayClient.execute(request);
        if(response.isSuccess()){
            System.out.println(response.getSubMsg());
            System.out.println(response.getBody());
            JSONObject result = JSON.parseObject(response.getBody());
            JSONObject response1 = result.getJSONObject("alipay_user_certdoc_certverify_preconsult_response");
            String code = response1.getString("code");
            if(Objects.equals(code, "10000")){
                String verifyId = response1.getString("verify_id");
            }

            System.out.println("调用成功");
        } else {
            System.out.println(response.getCode());
            System.out.println(response.getSubMsg());
            System.out.println("调用失败");
        }
    }

    @Test
    public void realnameCertTest2() throws AlipayApiException {
        String accessToken = "idvrifyBba0d4214e66047019db645f6a2708E53";
        AlipayUserCertdocCertverifyConsultRequest request = new AlipayUserCertdocCertverifyConsultRequest();
        request.setBizContent("{" +
                "  \"verify_id\":\"2cc390aadc078c68821089f7a5d9aabb\"" +
                "}");
        AlipayUserCertdocCertverifyConsultResponse response = alipayClient.execute(request,accessToken);
        if(response.isSuccess()){
            System.out.println("调用成功");
            System.out.println(response.getPassed());
            System.out.println(response.getFailParams());
            System.out.println(response.getFailReason());
        } else {
            System.err.println("调用失败");
            System.err.println(response.getMsg());
            System.err.println(response.getSubMsg());
            System.out.println(response.getBody());
        }
    }

    @Test
    public void getAccessToken() throws AlipayApiException {
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setGrantType("authorization_code");
        request.setCode("1e10bae356904fcd9d4277ad1834KC53");
        AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
        if(response.isSuccess()){
            System.out.println("调用成功");
            System.out.println(JSON.toJSONString(response));
        } else {
            System.err.println("调用失败");
            System.err.println(response.getMsg());
            System.err.println(response.getSubMsg());
        }
    }

}
