package alipay;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Map;

import org.apache.commons.collections.MapUtils;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayUserCertifyOpenCertifyRequest;
import com.alipay.api.request.AlipayUserCertifyOpenInitializeRequest;
import com.alipay.api.request.AlipayUserCertifyOpenQueryRequest;
import com.alipay.api.response.AlipayUserCertifyOpenCertifyResponse;
import com.alipay.api.response.AlipayUserCertifyOpenInitializeResponse;
import com.alipay.api.response.AlipayUserCertifyOpenQueryResponse;
import com.google.common.collect.Maps;

import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.Identities;
import cn.taqu.core.utils.JsonUtils;

public class AlipayTest {

	private static AlipayClient _alipayClient = null;
	
	static {
		String Json = "{\n" + 
				"    \"url\": \"https://openapi.alipay.com/gateway.do\",\n" + 
				"    \"app.id\": \"2017101909394524\",\n" + 
				"    \"app.private.key\": \"MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCztd7D1TT4wZXlA/LqtRqBAkL78Ix5eXadVrtXhaGoaAxNA3Ndzijr9XQ8+XBxjhzZWTg49LpbFtWY5SIIoG8G0T/daL8tkC2nrGD5g5Ydc5WQ+cEy2/BchQqb0mmuYo5d27LzmQ+0AVVZW9rej+1z2Kn/G9jLFwNIiih/C78CU7Te7lMqQe5ek5qywdpINmIuMPPELtF79FxSGjZHVYUxw5yICSzIQmR2vAxT1OWrRGPj6g2O82xnYO2lsvV4vMHNcG27pzUoHwPtyCGR4Ki/jvzkXYTSns3b/ZVnH99XEL69IYkZFjjSjclZSC7GhwTlbRwDGtvYmoHxIMSbbyWjAgMBAAECggEAIyKCnAz6H0xRzwWXn5WqyeA0Umd0P2ek7hv/4puowHEfPotXKdbdn0xM3oLyHfFn6IKOAGywZpCvUAY0QjWDn3SXpOUOw4y1sFt+VioI1lc7va1mvVfMxXnpWg+oQ8TXCriiYwfgzxOXqTHRN8Qx631Hty2tk/860Bra0gPs4zpAcW0xYEBtoZ3wN15ozBgNZXb8Cd8zIIR9BNmVsSURaUH9difQz/UFU6zkshzo3/x/lAVXefYzmUU4TCjPzN5k9u5qQ6imFG+6DY6GAGvxW0vZn0Hpp1rdJ32zrFwFL7C03QzDdrgBYYneasUJL3SKWu2QOGxIEGUq8Zckx94+OQKBgQDc5BxA1uTFer7qyFsahlk5gi8d4zEYJQST5GZgWKcMsKiyhr08H0LqOYopjplQYTIUcEZAQMBvQlmKyLK8xpfiZcg0mtmi22r/nL8hk8qNp+5LBQbxQfTOHEWaQxEKPzGFJ65qS4Ly7FSUgsXQdqz6+CjqepeFeCklgVwWYTVtBwKBgQDQRia/4WdPzz2GJuMt6lNoslAqdLJCucixIBv1hpJytCUwc1ryCxTo+P/t2+rnxIUK5BZV8Mzr2k7xMO71tdYdsfQ99jVAXHN4/gxD9u9KKOLUS5j4hGf50+vI/ZVI8P5cu6NrxnQAt/xE6Klo3xJdiFSdJOyUrg8xqjUJ/nc3hQKBgBbVwhH15SqBGPF+iuSAFQM78RD+5plfYkzDpM26/HH0ZUwnX7KybKF4F09afYDwu1Ofk7OavgoCau1M+CVJxnPSH9NIWs1aL4KDOsMG2z6vqJO6zk/siJkyFQPwgPd1NvJT+tTkP4JLaib8KeT2D8XWF6y9xaI3nNFSwSlTHUnLAoGAUKjqduNvpFwJYrwKvYyDkweDvn2FUAwtalOaIGNmsh+MS5BRR239QiC01USXW/0i3U3qTIw+gJUCf6xIz0YFz2hMw/ZnNy7W1aPl0ih/EcR13GwOqSh2MC3foe57c01J5VYR+zpXULPwcit8+I1zyl9zDYzwRsCGR+C4O8+eEhUCgYEAv/e5vzNIrx7eXIKWF5Qy5i3tUEOXUc9Kb6F3S8B8xbzVYwx5v7WUxqKGJgFlHFjeWpIo17SAB2NAVfDZ0gbjk9OvlDMipdc0Nn8/4f5A6zQWLQEYn3hzlQlznnfwCfXarg5VLxuEdC0jTyHSQQvAyb69xFBn2gNu7ygDLtumBns=\",\n" + 
				"    \"charset\": \"utf-8\",\n" + 
				"    \"alipay.public.key\": \"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnqHTpI3mgcbKfsbYmfI/5cxO36nibjq+N3JTrGLkiIbnVD/U09gINGFDia66WfFrUQBimWBnOgNwsjs6KAq2ajMwJsdJK/CedaY1edukrk7X7gPauoerTtnwBFNIOPuC2R8EUIw+F6ev10Hzkv4nFYBvG9fwdNsc5smkfmIAwacdOR1h+y+YTXyRIHXTyrJoVEXtZ2n9iIb2bFbIUEd4a2zf0KgMSO2mgJTVFyHNBF0L72TXQP/dOMN6Tyy4m7PbYz8lpe9Ptm9VacU7wJG3H5Qe1gqq033OV1TmASAuClyb5UiA/3SYZmTGqRiXPXUTa6XFZ0JT+PjhqzJIXLe+XwIDAQAB\",\n" + 
				"    \"sign.type\": \"RSA2\"\n" + 
				"}";
		
		Map<Object, Object> configMap = JsonUtils.stringToObject(Json, Map.class);
    	String url = MapUtils.getString(configMap, "url");
//    	String appId = "2019070965784249";
    	String appId = MapUtils.getString(configMap, "app.id");
    	String appPrivateKey = MapUtils.getString(configMap, "app.private.key");
//    	String appPrivateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCgz7uHRJpLg3Q4kKMLvFzn6rOGTnwxMFhvr3swq0+LwVQp4c8t/sFBk1i9wp2bW4atoLqWS7vqE0e7lyv7GNjYe8dxBZ5o1W9c3hLuR+MgXcnqNcct09Tpz+OZ5NFfNLfPKYullP3alqTmTo2TSnz9CfAtWhbjLVwChQUh9SEk5nv9XloKaDUPOd5ifPKy18ttFM6G2jSfG28CYbuyq6cxnOE66F+qP5UwyuWL5RTDgyRdgAuuWwR4rxTFPtB6F3Qarvy/XeDS9sWRgxWWP41poJKE3tiZdflUJBRwlRykLOJfoYFw9bywqCcJ8HiMnu+vKHigvkLYK0XH/H3UUAJ3AgMBAAECggEAVzu4z40AYhxCDHkxaSlaAJiJoD6tEgXWw2ce3WzgrsBGBp4uxBWhAhCLIA0Vb/Q7X66D6QnWOwkQbYt42TJGV1D2BLJ2lwnI6kixXj7yz1jxMjLQBGUX52nVXmMIzKuNVjyEO2NSPaRgeWp9VDwhBya/+eoX5k5xzhAYk64kt53MkRwgbgSLr0Oad7mQggymkQyxsUPbSAJgaD+C1B7/0tCbUikjuPpKMPwbZE+rg1HQyrIa/IB0TRn3HrEwsvmQptyLuQ7xcx59vp5J5HS66zsfKlDuI5z7KEV+mKbpsYATw7V+72R7LTH1qtdRqwig8U3s4VrkUFrOpNjbo3TDsQKBgQDxMMfXcAI8hJik6WupGa9tGqQYo5oiyN6Gel45gATGTl7dB33fL6YL6JSNErC3K2dYf9rtP3pUf9r6TpXPh/NIrA/5zpHUhKUqFtXal612s25Qcd1hZKuAaRjSvmg6ykjmxHb4JziB7sInGS3eFhvIVGEjSAg3Ws1/pQ9F0rgWjwKBgQCqr32/3+48buWe7KKJHtHwQ+oE46n6FtUpYNk1tlnM2U1ICQOALs2BDKiUDQZPTOgvwNloNZsG7vuihSzMox3QXb2h/1hnoKlov3eD3MWx7Yj9iF90vVV84HfkBKTRfNeF+S/Nfvj8YqaeRMJPyW3GPHplu9rk9oc1RVCLeEiJmQKBgQDFSgmuCElMB4p1GxqdsAz3ShuArBKg8X5UsjPxBO7i56hhVId50RgaFZ2t5+P1cOOQWMVT2+DMzejpZu7UW3h9sYE/7eEL3ZTc4OD9daTGu+84jmtwB1DiMRXFDUtlC6KmYX6PbkWiwxK9uLL0P1FqhNsQaROPdA80omfjzYIXsQKBgD5ZhNIGdvxUdkSrpGP6gi4GYQIQOBNHK+7FlhLs3bja2EQsJr84WZF+kkYiy99D9L0c8U4D5vGNcxZXF5pZK5bl51O59w1Dvx6ocnwUtZLddmFG47e9QfEv/hupJpvvwrsJ39BtRX7Mepjt/R0wM41qojtvd/KTq6hl/Zo1AiphAoGAIaxAeMKpjEV5YyU9G2Z48/LPbUGQBfp0qjb4Hl3OffVxxWg0uzRswGufPNFdBCxK8gBKRt3Y1LZDO9/QRd74AVcsz+YiN3azgNe3d+bC9lKXeaXHktE527yE+3hkY5LuME7uofFe+iI0LPvaVheYGMwRW4vk5QPgm/pbvsj8HI8=";
    	String charset = MapUtils.getString(configMap, "charset");
//    	String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAllbCkQi8kMdZmc2y9uZMEChH8Nx6JPgO/rumBuUiOk3/UrI2juQCl7USQqCrUBuaysDjLjcB9q15HYrQjUuUlJDqYQlZ9f5eHG6JsIKIAc35WzBMb/0mDFMdvERXzQJpDBkctVVHtS1HF5Me8+v710ZQo1mj3sGZfWjxE7gF1KwITpXFdrnkFG0ZTPfR0FGgRmypAoKdENpW4ON6jwzXSV09OqXeCu+hXQrr7wLa8dDKSnUDK8nle/b7DZ9YOxWS5d2A0qGED7CB4Lv2+W11l/kJ8ahwTBYOUFUB8TSLItLDgM1OFT+jIL9Q99tlkDD+scyxY5svRpq7QwJ4nMewiQIDAQAB";
    	String alipayPublicKey = MapUtils.getString(configMap, "alipay.public.key");
    	String signType = MapUtils.getString(configMap, "sign.type");
    	_alipayClient = new DefaultAlipayClient(url, appId, appPrivateKey, "json", charset, alipayPublicKey, signType);
	}
	
	
	public static void main(String[] args) throws AlipayApiException, NoSuchAlgorithmException, InvalidKeySpecException {
		AlipayTest alipayTest = new AlipayTest();
		alipayTest.initialize();
	}
	
	public void initialize() throws AlipayApiException, NoSuchAlgorithmException, InvalidKeySpecException {
		AlipayUserCertifyOpenInitializeRequest request = new AlipayUserCertifyOpenInitializeRequest();

		//构造身份信息json对象
		Map<String, Object> identityObj = Maps.newHashMap();
		//身份类型，必填，详细取值范围请参考接口文档说明
		identityObj.put("identity_type", "CERT_INFO");
		//证件类型，必填，详细取值范围请参考接口文档说明
		identityObj.put("cert_type", "IDENTITY_CARD");
		//真实姓名，必填
		identityObj.put("cert_name", "邱宇骅");
		//证件号码，必填
		identityObj.put("cert_no", "350721199012101337");

		//构造商户配置json对象
		Map<String, Object> merchantConfigObj = Maps.newHashMap();
		// 设置回调地址,必填. 如果需要直接在支付宝APP里面打开回调地址使用alipay协议，参考下面的案例：appId用固定值 20000067，url替换为urlEncode后的业务回跳地址
		// alipays://platformapi/startapp?appId=20000067&url=https%3A%2F%2Fapp.cqkqinfo.com%2Fcertify%2FzmxyBackNew.do
		merchantConfigObj.put("return_url", "ushengsheng.xjb.certification://");

		//构造身份认证初始化服务业务参数数据
		Map<String, Object> bizContentObj = Maps.newHashMap();
		//商户请求的唯一标识，推荐为uuid，必填
		bizContentObj.put("outer_order_no", this.generateSn());
		bizContentObj.put("biz_code", "FACE");
		bizContentObj.put("identity_param", identityObj);
		bizContentObj.put("merchant_config", merchantConfigObj);
		request.setBizContent(JsonUtils.objectToString(bizContentObj));

		String certifyId = null;
		
		//发起请求
		AlipayUserCertifyOpenInitializeResponse response = _alipayClient.execute(request);
		if (response.isSuccess()) {
		    System.out.println("调用成功");
		    //接口调用成功，从返回对象中获取certify_id
		    certifyId = response.getCertifyId();
		    System.out.println(JsonUtils.objectToString(response));
		    //执行后续流程...
		} else {
		    System.out.println("调用失败");
		}
		
		//参考代码如下：
		//获取alipay client
		AlipayUserCertifyOpenCertifyRequest request2 = new AlipayUserCertifyOpenCertifyRequest();

		//设置certifyId
		Map<String, Object> bizContentObj2 = Maps.newHashMap();
		bizContentObj2.put("certify_id", certifyId);
		request.setBizContent(JsonUtils.objectToString(bizContentObj2));

		//生成请求链接，这里一定要使用GET模式
		AlipayUserCertifyOpenCertifyResponse response2 = _alipayClient.pageExecute(request2, "GET");
		if(response2.isSuccess()){
 			System.out.println("开始认证服务调用成功");
			String certifyUrl = response2.getBody();
			System.out.println(certifyUrl);
			//执行后续流程...
		} else {
			System.out.println("调用失败");
		}
		
		
		AlipayUserCertifyOpenQueryRequest request3 = new AlipayUserCertifyOpenQueryRequest();

		//设置certifyId
		Map<String, Object> bizContentObj3 = Maps.newHashMap();
		//certifyId是初始化接口返回
		bizContentObj3.put("certify_id",certifyId );
		
		request3.setBizContent(JsonUtils.objectToString(bizContentObj3));

		
		AlipayUserCertifyOpenQueryResponse response3 = _alipayClient.execute(request3);
		if(response3.isSuccess()){
		  System.out.println("开始认证服务调用成功");
		  String body = response3.getBody();
		  response3.getPassed();
		  System.out.println(JsonUtils.objectToString(response3));
		  //执行后续流程...
		} else {
		  System.out.println("调用失败");
		}
		
	}
	
    private String generateSn() {
        StringBuilder sn = new StringBuilder("TAQU");
        sn.append(DateUtil.getCurrentTime("yyyyMMdd")).append(Identities.randomLong());
        return sn.toString();
    }

}
