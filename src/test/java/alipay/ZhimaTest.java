package alipay;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import cn.taqu.account.service.AccountsInfoService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.ZhimaCustomerCertificationCertifyRequest;
import com.alipay.api.request.ZhimaCustomerCertificationInitializeRequest;
import com.alipay.api.request.ZhimaCustomerCertificationQueryRequest;
import com.alipay.api.response.ZhimaCustomerCertificationCertifyResponse;
import com.alipay.api.response.ZhimaCustomerCertificationInitializeResponse;
import com.alipay.api.response.ZhimaCustomerCertificationQueryResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;

import cn.taqu.account.constant.CodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.Identities;
import cn.taqu.core.utils.JsonUtils;

public class ZhimaTest {

private static AlipayClient _alipayClient = null;
	
	static {
		String Json = "{\n" + 
				"    \"url\": \"https://openapi.alipay.com/gateway.do\",\n" + 
				"    \"app.id\": \"****************\",\n" + 
				"    \"app.private.key\": \"MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC8kz1bottRQcsMCrUX19pgKKEQDyzvdfoJ4VLxxcxuHSyjLJRlvpGIHaAktVGc3LmUE9xUQyI6aZIBClBOkc9UaNVbI41zJqYH5rr/A58djG+mRqI7yxtaciND2FLwJV07h76uLcDFbiBjtAkpkr/IpPGsAYh61fTTU5jxbbROrqPYinG2YY92aP/+JXUxTWQ0opRxqJOnKGtPRbaLUyYN0cecPje9+7qnY0RjnqKLkS+wn3oAil8QGStDCLc8u7d8ElpL73U7NNSFkca19w0uGcJCgg0CgLI5tPCTQCcbcKWI0EOlTMez7RZxM7Wb9jF9bgVSymJ0DN9fjpnqrN41AgMBAAECggEBALKl7wAMRigx43GSB0QCW0Cz3PpA1jo5k1qe25xu3iAHhY1Eo4XMR0Ujg96i5CFuPmWZdydgvmGGOCrCIZh9rGR0OIRyJwRLPgnIPalwPeKZdACbdACfh1dlQCF7I/+b9jp6fFu4vqKLlSW/ntqCyywDj2x7u1dbsOgvj3pRivrp+Y6Tx0MUH0DlAFNXfGm9ANuz/VNbx7+XNczAEq8URZZ9YewbOLrR32fROf3KM6lZdmL2m8WPRBMydDcXJYq+ZeT6HussQgj9OH5BGfg4FdiaHn2+Wuf0jzGYr+3gP2G2/C8QBA8KmU7FNVkKtuGjqkDRN9k4TK0wepKdmUAUojUCgYEA4rKXclehhiR4VJGwUKMFkWxqF1jZ3eMOcmjS3iL8g5pPmw/NyWJTxzW8a8EyjRiumGpmZO+TUP44XFbwZrgXGmMl+SCRX2QVRV+KZkUObZYHBaiwWg05Ef+q9n6fTlI8Tk8XLaaN9kwSKM6eeyeYOWgXdfcbIx7lqe8eeenaUlsCgYEA1PMtpKSOHFtm4/Ozas13tERloC8uBLdm6y6sAyo+FP74HAqNXVU97uBtq4J0MT3F6BWE6wqcaTh1CpqfTHtfzFTrr/JqoJtvIbhB3kCZupxftpXqQSJ26z2D7p2aETAgnqG00V8l9rP7BeuDwA31ucEebn564v+uhsQHIZeMVq8CgYEAtLn6AL8Z105F2+3VqUN90eSjY3+DGODGCLHGwi8ULnqb0hu0TEMmVR9i2mjIOzIdJwdUIE3anOw+1Ga++rTyPigjAsxS+MBaMNctB8Ev8m9zo7BK/0+hWtb/XcmuiQsIZsmoyc0JSfx1UbD/KWF0evegFHxuVv5vy50FJ94mHhcCgYA9DEn1fUdQUdO5Xc/9ulGq2EDaHyxuJbXXjlW5wo5xrnaqc0v7ichrhuNp28g7pZ3q3uBwEWats4W6OIGah3P7PA0lL7XpvSWq4QvhD1wC53l4+plheuPkpIoCCiR08Pso/uq7ay+S/h3koJPwpw/e5MI4owHPucTq1Z/FvZB5+QKBgCFF21accEbBSq7qmxosti/lwXWXvMDtxzhaYTZc/CthVUzKJaI1wh7aW71cAbW1C4jnFoKsz2R5jJwl+Me2YLOr97agmSFj10joxyC4wcAoeuQ0EDVfhdZ46N4OTb652TzpIIWgstPzndVINb8svXsffyX049To5OFqaSIhqdtS\",\n" + 
				"    \"charset\": \"utf-8\",\n" + 
				"    \"alipay.public.key\": \"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwAcZoWAUuvXvnQyh/a6suTg0eDptdsIGs3CJDHtpuj4BXifGbk4aKtB6D5H27aFRGyzTVEVZrvUR8nnyT2AzF0wo7VK7N2Z3iY+pavTEvFWK8tf8zHyJ8WCgzn77C8ZLKLHUNA4fmGsohD/Wq1Km/ira1Hzy6EHEFTcT5yV8IfyjKP8o7R58xYfUDm8OXPVtkbKtSkgHOzoTzWJ9X0qhW8IfdnoHD/mWx2VBD+Ha+lJhA//6eDKCMTXTztLvep0TWn+W5Q+/9gGSh5w57Cg97NvERf/9qcZp8xXCLgn7bvEcq3nj5ZrfeY3+WbJ8gR+aoXUwztm3dupOQwk1z30k+QIDAQAB\",\n" + 
				"    \"sign.type\": \"RSA2\"\n" + 
				"}";
		
		Map<Object, Object> configMap = JsonUtils.stringToObject(Json, Map.class);
    	String url = MapUtils.getString(configMap, "url");
    	String appId = MapUtils.getString(configMap, "app.id");
    	String appPrivateKey = MapUtils.getString(configMap, "app.private.key");
    	String charset = MapUtils.getString(configMap, "charset");
    	String alipayPublicKey = MapUtils.getString(configMap, "alipay.public.key");
    	String signType = MapUtils.getString(configMap, "sign.type");
    	_alipayClient = new DefaultAlipayClient(url, appId, appPrivateKey, "json", charset, alipayPublicKey, signType);
	}
	
	
	public static void main(String[] args) {
//		ZhimaTest zhimaTest = new ZhimaTest();
//
//		String a = zhimaTest.a();
//		zhimaTest.b(a);
//		zhimaTest.c(a);
		identityNoTest();
		Object obj=null;
		String code = "***************";
		String ss=getIdentityNoStr(code);
//		String str=JSON.toJSONString(obj);
		System.out.println(ss);
		
	}

	public static String getIdentityNoStr(String identityNo){
		String start=identityNo.substring(0,1);
		int length=identityNo.length();
		String end=identityNo.substring(length-1,length);
		StringBuffer sb=new StringBuffer(start);
		if (identityNo.length() == 15){
			sb.append(AccountsInfoService.IDENTITY_NO_15);
		}else if (identityNo.length() == 18){
			sb.append(AccountsInfoService.IDENTITY_NO_18);
		}else {
			return "";
		}
		sb.append(end);
		String identityNoStr=sb.toString();
		if(identityNoStr.length()==15 || identityNoStr.length()==18){
			return identityNoStr;
		}
		return "";
	}

	public static void identityNoTest(){
		String code = "35060019880218151X";
		System.out.println("找到身份证号：" + code);
		// 替换身份证号中间的数字为星号
		String codeNew = "";
		if (code.length() == 15){
			codeNew = code.replaceAll("(\\d{1})\\d{13}(\\d{1})", "$1*************$2");
		}else if (code.length() == 18){
			codeNew = code.replaceAll("(\\d{1})\\d{16}(\\d{1})", "$1****************$2");
		}else {

		}


//		String content = content.replace(code,codeNew);
		System.out.println(codeNew);

	}
	
	public String a() {
		String bizNo = null;
        ZhimaCustomerCertificationInitializeRequest request = new ZhimaCustomerCertificationInitializeRequest();
        Map<String, String> params = Maps.newHashMap();
        params.put("transaction_id", this.generateSn());
        params.put("product_code", "w1010100000000002978");
        params.put("biz_code", "FACE");
        Map<String, String> identityParam = Maps.newHashMap();
        identityParam.put("identity_type", "CERT_INFO");
        identityParam.put("cert_type", "IDENTITY_CARD");
        identityParam.put("cert_name", "邱宇骅");
        identityParam.put("cert_no", "350721199012101337");
        params.put("identity_param", JsonUtils.objectToString(identityParam));
        params.put("ext_biz_param", "{}");
        request.setBizContent(JsonUtils.objectToString(params));
        try {
            ZhimaCustomerCertificationInitializeResponse response = _alipayClient.execute(request);
            if (!response.isSuccess()) {
                throw new ServiceException(CodeStatus.CERTIFY_FAIL);
            }
            bizNo = response.getBizNo();
            System.out.println(bizNo);
        } catch (AlipayApiException e) {
            throw new ServiceException(CodeStatus.CERTIFY_FAIL);
        }
        return bizNo;
	}
	
	public void b(String bizNo) {
		 String returnUrl;
	        ZhimaCustomerCertificationCertifyRequest certifyRequest = new ZhimaCustomerCertificationCertifyRequest();
	        Map<String, String> params = Maps.newHashMap();
	        params.put("biz_no", bizNo);
	        certifyRequest.setBizContent(JsonUtils.objectToString(params));
	        certifyRequest.setReturnUrl("ushengsheng.xjb.certification://");

	        try {
	            ZhimaCustomerCertificationCertifyResponse response = _alipayClient.pageExecute(certifyRequest, "GET");
	            if (!response.isSuccess()) {
	                throw new ServiceException(CodeStatus.CERTIFY_FAIL);
	            }
	            returnUrl = response.getBody();
	            System.out.println(returnUrl);
	        } catch (AlipayApiException e) {
	            throw new ServiceException(CodeStatus.CERTIFY_FAIL);
	        }
	}
	public void c(String bizNo) {

        //调用芝麻认证查询接口
        ZhimaCustomerCertificationQueryRequest request = new ZhimaCustomerCertificationQueryRequest();
        Map<String, String> params = Maps.newHashMap();
        params.put("biz_no", bizNo);
        request.setBizContent(JsonUtils.objectToString(params));
        ZhimaCustomerCertificationQueryResponse response;
		try {
			response = _alipayClient.execute(request);
			System.out.println(JsonUtils.objectToString(response));
			
			System.out.println(response.getPassed());
		} catch (AlipayApiException e) {
			System.out.println("异常");
			e.printStackTrace();
		}
		
	}
	
	private static String generateSn() {
        StringBuilder sn = new StringBuilder("TAQU");
        sn.append(DateUtil.getCurrentTime("yyyyMMdd")).append(Identities.randomLong());
        return sn.toString();
    }
}
