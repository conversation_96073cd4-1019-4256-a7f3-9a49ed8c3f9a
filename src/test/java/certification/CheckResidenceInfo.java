package certification;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.taqu.account.client.FujianGovApiClient.FujianGovApiResponse;
import cn.taqu.core.utils.JsonUtils;

/**
 * https://data.fujian.gov.cn/#/oportal/service/details?serviceId=dc6f072c1397614926cda470ec2b89de
 * 港澳台居住证核查接口-港澳台居住证信息核查
 * 
 * <AUTHOR>
 * 2024年5月9日上午9:23:56
 */
public class CheckResidenceInfo {

    static String serviceUrl = "";
    static List formParams = new ArrayList<>();
    
    public static void main(String[] args) throws IOException {
        
        test1();
//        test2();
        
        int timeout = 3000; // 服务调用延时
        String clientId = "382c7476baf84037a459dedb0622711c"; // 替换为用户的client_id
        String timestamp = String.valueOf(Instant.now().toEpochMilli());
        String nonce = UUID.randomUUID().toString();
        String secret = "381eec9aaa35481fb6eae3b36a13f95c"; // 替换为用户的client_secret
        String stringToSign = clientId + timestamp + nonce;
        String sign = null;
        
        String AppKey = "0f7d9826edaa46d8b3983af8b9e423ea";// 不建议使用，有次数限制
//        Client ID:382c7476baf84037a459dedb0622711c
//        Client Secret:381eec9aaa35481fb6eae3b36a13f95c
//        AppKey:0f7d9826edaa46d8b3983af8b9e423ea
        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            byte[] keyBytes = secret.getBytes("UTF-8");
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
            sign = Base64.encodeBase64String(hmacSha256.doFinal(stringToSign.getBytes("UTF-8")));
        } catch (NoSuchAlgorithmException e) {

        } catch (UnsupportedEncodingException e) {

        } catch (InvalidKeyException e) {

        }

        HttpClientBuilder httpClientBuilder = HttpClients.custom().useSystemProperties();
        CloseableHttpClient httpClient = httpClientBuilder.build();

        // 根据服务的具体请求方法构造合适的请求方法对象，此处以POST方法为例说明
        HttpPost requestMethod = new HttpPost(serviceUrl);
        RequestConfig config = RequestConfig.custom().setConnectTimeout(timeout).build();
        requestMethod.setConfig(config);
        requestMethod.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        requestMethod.addHeader("X-Client-Id", clientId);
        requestMethod.addHeader("X-Timestamp", timestamp);
        requestMethod.addHeader("X-Nonce", nonce);
        requestMethod.addHeader("X-Signature", sign);
        // 创建请求体中的表单参数（如果需要）
//        List formParams = new ArrayList<>();
        
        
        // 设置请求体的表单参数（如果需要）
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formParams, "UTF-8");
        requestMethod.setEntity(entity);
        CloseableHttpResponse response = httpClient.execute(requestMethod);
        // 处理请求结果
        HttpEntity responseEntity = response.getEntity();
        String responseBody = EntityUtils.toString(responseEntity, "UTF-8");
        System.out.println(responseBody);
        
        FujianGovApiResponse fujianGovApiResponse = JsonUtils.stringToObject(responseBody, new TypeReference<FujianGovApiResponse>() {});
        System.err.println(JsonUtils.objectToString(fujianGovApiResponse));
    }
    
    public static void test1() {
      serviceUrl = "https://data.fujian.gov.cn/gateway/api/1/customizationInterface/checkResidenceInfo"; // 具体服务的调用地址, 若是GET请求, 请求参数放在URL后面
      formParams.add(new BasicNameValuePair("sirc_query_cause", "校验数据"));
      formParams.add(new BasicNameValuePair("sirc_approve_name", "张毅"));
      formParams.add(new BasicNameValuePair("sirc_approve_id", "350521198207220015"));
      formParams.add(new BasicNameValuePair("sirc_ip", "*************"));
      formParams.add(new BasicNameValuePair("sirc_mac_address", "00:e0:4d:6d:af:4c"));
      formParams.add(new BasicNameValuePair("sirc_approve_phone", "13860151783"));
      formParams.add(new BasicNameValuePair("XM", "吴又昌"));
      formParams.add(new BasicNameValuePair("GMSFHM", "810000194209210017"));
    }
        
    
    public static void test2() {
        serviceUrl = "https://data.fujian.gov.cn/gateway/api/1/zjhyqyxx/get_datalist_1.0"; // 具体服务的调用地址, 若是GET请求, 请求参数放在URL后面
        formParams.add(new BasicNameValuePair("START", "1"));
        formParams.add(new BasicNameValuePair("SIZE", "10"));
    }
}
