package cn.taqu.account;


import cn.taqu.account.constant.RegStyle;
import cn.taqu.account.service.AccountsDestroyLogService;
import cn.taqu.account.service.AccountsService;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.protocol.SoaBaseParams;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * ab测试demo
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AbTest {
    @Autowired
    AccountsDestroyLogService accountsDestroyLogService;
    @Autowired
    AccountsService accountsService;
    @Test
    public void abTest(){

        String taskCode = "justin_test";

        SoaClient soaClient = new SoaClient("http://10.10.60.228:8096/tq-gateway/api");
        Object[] form = {
                taskCode
        };
        SoaBaseParams soaBaseParams = new SoaBaseParams();
        soaBaseParams.setAppcode("1");
        soaBaseParams.setToken("3b5b32d1df7844c5d2a4148ae86dc18c");
        soaBaseParams.setPlatform_id("1");
        soaBaseParams.setChannel("dev");
        soaBaseParams.setApp_version("999");
        soaBaseParams.setGender("1");
        //ab测试的时候城市筛选条件，这边需要传城市的code
        soaBaseParams.setCity("2793");
        SoaResponse response = soaClient.callService("abRule", "getTaskMark", form,soaBaseParams,"","1");
        if(response.fail()){}
        /*String taskMark = abTestRuleService.getTaskMark(soaBaseParams.getToken(), taskCode, soaBaseParams.getAppcode(),
                soaBaseParams.getPlatform_id(), soaBaseParams.getChannel(), soaBaseParams.getApp_version(), soaBaseParams.getGender(), soaBaseParams.getCity());*/
    }


    /**
     * 测试推送消息到推送系统
     */
    @Test
    public void testPush(){
        Map<String, Object> pushData = new HashMap<>();
        pushData.put("motion", "welcomeGift");   //给客户端显示的网页，客户端提供
        pushData.put("action", "welcomeGift");   //给客户端显示的页面，客户端提供
        pushData.put("message", "测试推送给justin"); //推送给客户端的消息内容
        pushData.put("title", "title test");   //没有title，可不填
        pushData.put("pushTagCode", "visitHomepageOverThree");  //标识该推送的标识符，可后端定义
        pushData.put("isHighPriority", false);   //是否高优先级
        pushData.put("uuid", "bi5uof99w5qt");    //推送给用户的uuid
        pushData.put("offlineExpireTime", -1L);   //
        pushData.put("extraArgs", "");   //

        Map<String, Object> msgInfo = new HashMap<>();
        msgInfo.put("service", "asyncpush");
        msgInfo.put("method", "pushPopupMessageToSingleByUuid");
        msgInfo.put("distinctRequestId", SoaBaseParams.fromThread().getDistinctRequestId());
        msgInfo.put("asyncforms", pushData);
        MqClient mqClient = new MqClient("http://g2.test.k8s.taqu.cn/v2/Soa/jService");
        MqResponse mqResponse = mqClient.push("push_async_invoke_queue", msgInfo, null);
        System.out.println(mqResponse);
    }

    @Test
    public void matchTest(){
        // 校验昵称正则表达式
        final Pattern NICKNAME_PATTERN = Pattern.compile("^[a-z0-9]{32}$");
        // 校验uuid正则表达式
        final Pattern UUID_PATTERN = Pattern.compile("^[A-Z0-9\\-]{36}$");

        Boolean isAndroidMatch = NICKNAME_PATTERN.matcher("84299a4ab5bf555fa1cbf9033472f71d").matches();

        Boolean isIosMatch = UUID_PATTERN.matcher("5F629A5F-4F57-41D7-8D88-70583C2FC1F4").matches();

        System.out.println(isAndroidMatch);

        System.out.println(isIosMatch);
    }

    @Test
    public void test(){
         Pattern ANDROID_TOKEN_PATTERN = Pattern.compile("^[a-z0-9]{32}|[A-Z]{1}[a-z0-9]{32}$");
        System.out.println( ANDROID_TOKEN_PATTERN.matcher("A2a2f65cbc5996796b02b1a66417f537").matches());
        System.out.println(ANDROID_TOKEN_PATTERN.matcher("db490af99c89768cbf3b86a76958118").matches());
    }

    @Test
    public  void testReregistrationCheck(){
        accountsService.reregistrationCheck(RegStyle.WeiBo,"**********");
    }

    @Test
    public void testdeploylog(){
        accountsDestroyLogService.addLog("a93dk81w1x5","测试","*********","测试",false);
    }

    @Test
    public void testdeploylogByScript(){
        accountsDestroyLogService.addLog("a93dk81w1x5","测试","*********","测试",true);
    }
}
