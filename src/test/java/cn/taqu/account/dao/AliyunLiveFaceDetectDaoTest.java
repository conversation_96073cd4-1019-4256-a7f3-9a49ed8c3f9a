package cn.taqu.account.dao;


import cn.taqu.account.model.AccountsDestroyLog;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.model.AccountsPhoto;
import cn.taqu.account.model.AliyunFacePhotoCompareLog;
import cn.taqu.account.model.AliyunLiveFaceDetect;
import cn.taqu.core.utils.DateUtil;


@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AliyunLiveFaceDetectDaoTest {

    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;

    @Autowired
    private AliyunFacePhotoCompareLogDao aliyunFacePhotoCompareLogDao;

    @Autowired
    private AccountsPhotoDao accountsPhotoDao;

    @Autowired
    private ReRegisterWhitelistDao reRegisterWhitelistDao;

    @Autowired
    private AccountsDestroyLogDao accountsDestroyLogDao;

    @Test
    public void find(){
        AliyunLiveFaceDetect detect = aliyunLiveFaceDetectDao.getInfoByAccountUuid("jadi090adf");
        System.out.println(detect.getBase_photo_url());
    }

    @Test
    public void testLogMerge(){
        AliyunFacePhotoCompareLog log = new AliyunFacePhotoCompareLog();
        log.setAccount_uuid("38898sds");
        log.setCreate_time(DateUtil.currentTimeSeconds());
      //  log.setUpdate_time(DateUtil.currentTimeSeconds());
        aliyunFacePhotoCompareLogDao.merge(log);
    }

    @Test
    public void testGetById(){
        AccountsPhoto photo = accountsPhotoDao.get(42L);
        System.out.println(photo);
    }

}
