package cn.taqu.account;

import cn.taqu.core.utils.Encodes;
import cn.taqu.core.utils.NumberUtil;
import cn.taqu.core.utils.StringUtil;
import org.apache.commons.io.Charsets;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.HashMap;
import java.util.Map;

public class Utils {
    /**
     * 获取带有头信息的参数原型
     * @return
     */
    public static Map<String,Object> getCommonHeaderParam () {
        Map<String, Object> params = new HashMap<>();
        params.put("appcode", 2);
        params.put("cloned", 2);
        params.put("platform_id", 2);
        params.put("channel", "1");
        params.put("app_version", 1);
        params.put("token", "**********");
        params.put("env", 0);
        params.put("ip", "**********");
        return params;
    }

    public static String utf8ToBase64 (String raw) {
        return Encodes.encodeBase64(Encodes.decodeCharset(raw, Charsets.UTF_8.name()));
    }

    public static void main(String[] args) {
        String sourceFile = "/Users/<USER>/Desktop/EDB配货单明细_201812.csv";
        String otherFile = "/Users/<USER>/Desktop/invoiceDetail(20181201-20181231)J0oHdBpzJ6.csv";

        Map<String, Double[]> sourceInvoiceAmount = calInvoiceAmount(sourceFile);
        Map<String, Double[]> otherInvoiceAmount = calInvoiceAmount(otherFile);
        System.out.printf("\"%s\"与\"%s\"文件差异比较\n", sourceFile, otherFile);
        showDiff(sourceInvoiceAmount, otherInvoiceAmount);
        System.out.printf("\"%s\"与\"%s\"文件差异比较\n", otherFile, sourceFile);
        showDiff(otherInvoiceAmount, sourceInvoiceAmount);
    }

    private static Map<String, Double[]> calInvoiceAmount(String file) {
        Map<String, Double[]> invoiceAmountMap = new HashMap<>();
        int i=0;
        try(FileReader fileReader = new FileReader(new File(file));
            BufferedReader reader = new BufferedReader(fileReader)) {
            String line;
            while ((line = reader.readLine()) != null) {
                if(i++ > 0 && StringUtils.isNotBlank(line)) {
                    String[] dataArray = line.split(",");
                    if(dataArray.length<14) {
                        System.err.println("第" + i +  "行" + line + " 格式错误");
                        continue;
                    }
                    String invoiceSn = StringUtils.removeAll(dataArray[1], "\"");
                    if(StringUtils.isBlank(invoiceSn)) {
                        System.err.println("第" + i + "行配货单号为空, 行内容为:" + line);
                        continue;
                    }

                    String goodsAmount = StringUtils.removeAll(dataArray[10], "\"");
                    String orderAmount = StringUtils.removeAll(dataArray[11], "\"");
                    String shippingFee = StringUtils.removeAll(dataArray[12], "\"");
                    String promoAmount = StringUtils.removeAll(dataArray[13], "\"");
                    invoiceAmountMap.compute(invoiceSn, (k, v) -> {
                        if(v == null) {
                            v = new Double[]{0D, 0D, 0D, 0D};
                        }
                        v[0] = NumberUtil.addDouble(v[0], StringUtil.toDouble(goodsAmount, 0D));
                        v[1] = NumberUtil.addDouble(v[1], StringUtil.toDouble(orderAmount, 0D));
                        v[2] = NumberUtil.addDouble(v[2], StringUtil.toDouble(shippingFee, 0D));
                        v[3] = NumberUtil.addDouble(v[3], StringUtil.toDouble(promoAmount, 0D));
                        return v;
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return invoiceAmountMap;
    }

    private static void showDiff(Map<String, Double[]> invoiceAmount1, Map<String, Double[]> invoiceAmount2) {
        invoiceAmount1.forEach((invoiceSn, amount) -> {
            Double[] amount2 = invoiceAmount2.get(invoiceSn);

            if(amount2 == null) {
                System.out.println(String.format("多的配货单:%s, 商品总额:%.2f, 订单总额:%.2f, 邮费:%.2f, 优惠金额:%.2f", invoiceSn, amount[0], amount[1], amount[2], amount[3]));
                return;
            }

            if(!amount[0].equals(amount2[0]) || !amount[1].equals(amount2[1])
                    || !amount[2].equals(amount2[2]) || !amount[3].equals(amount2[3])) {
                System.out.println(String.format("配货单[%s]金额不匹配, 商品总额:%.2f->%.2f, 订单总额:%.2f->%.2f, 邮费:%.2f->%.2f, 优惠金额:%.2f->%.2f", invoiceSn, amount[0], amount2[0], amount[1], amount2[1], amount[2], amount2[2], amount[3], amount2[3]));
            }
        });
    }
}
