package cn.taqu.account;

import cn.taqu.account.utils.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-19 09:30
 */
@Slf4j
public class TimeFormatUtilTest {

    @Test
    public void test(){
        // 这个方法有问题，极矮
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.WEEK_OF_YEAR,1);
        calendar.set(Calendar.DAY_OF_WEEK, 5);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MINUTE, 59);
        System.out.println(calendar.getTimeInMillis() / 1000);

        Long nextWeekDay = TimeFormatUtil.getNextWeekDay(7);
        System.out.println(nextWeekDay);
    }



}
