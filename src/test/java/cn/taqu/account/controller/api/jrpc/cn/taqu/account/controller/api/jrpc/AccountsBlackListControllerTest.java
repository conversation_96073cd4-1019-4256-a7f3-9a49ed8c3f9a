package cn.taqu.account.controller.api.jrpc.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.account.dto.AccountBlackListDto;
import cn.taqu.core.common.client.HttpClient;
import cn.taqu.core.utils.Encodes;
import com.google.gson.Gson;
import org.apache.commons.io.Charsets;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class AccountsBlackListControllerTest {
    @Test
    public void testBatchAdd() {
        HttpClient client = new HttpClient("http://localhost:8089/tq-account/api?service=blacklist&method=batchAdd");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        List<AccountBlackListDto> dtoList = new ArrayList<>();
        AccountBlackListDto dto1 = new AccountBlackListDto();
        dto1.setAccountUuid("a91wl1r36tp");
        dto1.setRemark("test2");
        dto1.setOperateName("zenghw");

        AccountBlackListDto dto2 = new AccountBlackListDto();
        dto2.setAccountUuid("a9ehmqqgynn");
        dto2.setRemark("test2");
        dto2.setOperateName("zenghw");

        dtoList.add(dto1);
        dtoList.add(dto2);

        List<Object> objects = Arrays.asList(dtoList);

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }


    public static String utf8ToBase64 (String raw) {
        return Encodes.encodeBase64(Encodes.decodeCharset(raw, Charsets.UTF_8.name()));
    }

}
