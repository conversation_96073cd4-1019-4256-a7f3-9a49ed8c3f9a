package cn.taqu.account.controller.api.jrpc.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import cn.taqu.core.utils.Encodes;
import com.google.gson.Gson;
import org.apache.commons.io.Charsets;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class MobileControllerTest {
    @Test
    public void testChangeMobile() {
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=mobile&method=changeMobile");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        List<Object> objects = Arrays.asList("11", "ec6df646ae1e659003d01dd34b3dd6f3", "***********");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    public static String utf8ToBase64 (String raw) {
        return Encodes.encodeBase64(Encodes.decodeCharset(raw, Charsets.UTF_8.name()));
    }
}
