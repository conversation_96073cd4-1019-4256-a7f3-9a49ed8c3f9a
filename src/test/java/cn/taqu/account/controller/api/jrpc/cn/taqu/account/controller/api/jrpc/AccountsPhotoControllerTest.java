package cn.taqu.account.controller.api.jrpc.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Application;
import cn.taqu.account.controller.api.jrpc.AccountsPhotoController;
import cn.taqu.account.vo.AccountVo;
import cn.taqu.core.common.client.HttpClient;
import cn.taqu.core.protocol.http.RequestParams;
import cn.taqu.core.utils.Encodes;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.Charsets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AccountsPhotoControllerTest {

    @Autowired
    private AccountsPhotoController accountsPhotoController;

    @Test
    public void findCheckFailAvatarByUuid(){
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=photo&method=findCheckFailAvatarByUuid");
        Map<String, Object> params = new HashMap<>();
        List<Object> objects = Arrays.asList("r2mstms8qmu");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

    public static String utf8ToBase64 (String raw) {
        return Encodes.encodeBase64(Encodes.decodeCharset(raw, Charsets.UTF_8.name()));
    }

    @Test
    public void encode(){
        List<Object> objects = Arrays.asList("r2mstms8qmu","1");
        String result=utf8ToBase64(new Gson().toJson(objects));
        System.out.println(result);
    }
}
