package cn.taqu.account.controller.api.jrpc.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.account.vo.AccountVo;
import cn.taqu.core.common.client.HttpClient;
import cn.taqu.core.utils.Encodes;
import com.google.gson.Gson;
import org.apache.commons.io.Charsets;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AccountControllerTest {
    @Test
    public void testAddAccountForBackstage() {
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=account&method=addAccountForBackstage");
        Map<String, Object> params = new HashMap<>();

        AccountVo accountVo = new AccountVo();
        accountVo.setNickname("test79");
        accountVo.setMobile("***********");
        accountVo.setAccount_password("123456");
        List<Object> objects = Arrays.asList(accountVo, "123456");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    public static String utf8ToBase64 (String raw) {
        return Encodes.encodeBase64(Encodes.decodeCharset(raw, Charsets.UTF_8.name()));
    }

    @Test
    public void testReg() {
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=account&method=reg");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        List<Object> objects = Arrays.asList("***********", "zenghw7", "123456", "0");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }


    @Test
    public void testLogin() {
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=account&method=login");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        List<Object> objects = Arrays.asList("***********", "123456");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void testChar() {
        char c = 'x';
        while (c > 108) {
            c = (char) ((int) c - 12);
        }
        System.out.println(c);
    }
}
