package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import static cn.taqu.account.Utils.utf8ToBase64;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.gson.Gson;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountsWhiteListControllerTest {
    @Test
    public void testAdd() {
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=whitelist&method=add");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        List<Object> objects = Arrays.asList("zzzzzzzzzzz", "remark", "zeng");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void testIsWhite(){
        Object[] form = {
                "nagtfdwgbax",
                2
        };
        SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM);
        SoaResponse soaResponse = soaClient.call("whitelist","isWhite",form);
        if (soaResponse.fail()) {
            throw new ServiceException("判断uuid是否是指定类型的白名单用户失败");
        }
    }

    @Test
    public void testAddOne(){
        Object[] form = {
                "bjefp7ojtbbj",
                "测试添加",
                "lin",
                1
        };
        SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM);
        SoaResponse response = soaClient.call("whitelist", "add", form);
        if (response.fail()) {
            throw new ServiceException("添加用户白名单失败");
        }
    }

    @Test
    public void remove(){
        Object[] form = {
                "a9fiw2bgw8a",
                1
        };

        SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM);
        SoaResponse soaResponse = soaClient.call("whitelist","remove",form);
        if (soaResponse.fail()) {
            throw new ServiceException("移除用户白名单失败");
        }
    }

    @Test
    public void removeById(){
        Object[] form = {
                90
        };
        SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.ANTISPAM);
        SoaResponse soaResponse = soaClient.call("whitelist","removeById",form);
        if (soaResponse.fail()) {
            throw new ServiceException("根据白名单id移除用户白名单失败");
        }
    }

}
