package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import com.google.gson.Gson;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 16:48
 */
public class AccountCertificationTest {


    private static String TEST_HOST = "http://j2.test2.k8s.taqu.cn/tq-account/api";
    private static String DEV_HOST = "http://localhost:8080/tq-account/api";
    private static String SERVICE = "certification";
    private static String SERVICE_1 = "backendOperation";
    private static String SERVICE_2 = "accountsCert";

    @Test
    public void getChangeLogListByIdentityNo(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE + "&method=getChangeLogListByIdentityNo");
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE + "&method=getChangeLogListByIdentityNo");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("350212199512150051");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getByUuids(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE + "&method=getByUuids", 60 * 1000, 60 * 1000, 60 * 1000);
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE + "&method=getByUuids", 60 * 1000, 60 * 1000, 60 * 1000);

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(Arrays.asList("ce8v17iaj0n3"));
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void cancelCert(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE_1 + "&method=cancelCert");
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE_1 + "&method=cancelCert");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("ce8v17iaj0n3", Arrays.asList(3));
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void searchCertInfoForBackstage(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE_1 + "&method=searchCertInfoForBackstage");
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE_1 + "&method=searchCertInfoForBackstage");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("djbd30v6s8gi", "", "", "", "");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }


    @Test
    public void getCancelTips(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE_2 + "&method=getCancelTips");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE_2 + "&method=getCancelTipsgetCancelTips");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("djbd30v6s8gi");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void cancelMyCert(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE_2 + "&method=cancelMyCert");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE_2 + "&method=cancelMyCert");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("djbd30v6s8gi", "2");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getInfoByCard(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + "info" + "&method=getInfoByCard");
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + "info" + "&method=getInfoByCard");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("djbd30v6s8gi", "176180380");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

}
