package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.*;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-16 09:16
 */
@Slf4j
public class AccountIntroductionTest {

    private static String TEST_HOST = "http://j2.test2.k8s.taqu.cn/tq-account/api";
    private static String DEV_HOST = "http://localhost:8080/tq-account/api";

    /**
     * 保存个性签名
     */
    @Test
    public void setIntroduction(){
//        for (int i = 0; i < 10; i++) {

            HttpClient client = new HttpClient(DEV_HOST + "?service=introduction&method=setIntroduction");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=introduction&method=setIntroduction");

            Map<String, Object> params = Utils.getCommonHeaderParam();
            // 3bb733ea9a46c62d69cb3cb20c7b02c8
            // 账号uuid 活体图片 性别
            List<Object> objects = Arrays.asList("djbd30v6s8gi", "yuiittvbhhhhkjjjhhhvnjhghgggf cvbhjhhhhhhhyyyyyuuyhgyycvhhyuihhggggg九龙路咯摩托封路了咯莫呕吐了h[grimacing][grinning][grin][wink][blush][blush] ", "", "{\"img_list\":[{\"img_name\":\"https://avatar01.jiaoliuqu.com/cn.taqu.test_iOS_avatar_115_1644377884786_16691_0.jpg\"}]}");
//        List<Object> objects = Arrays.asList("djbd30v6s8gi", "yuiittvbhhhhkjjjhhhvnjhghgggf cvbhjhhhhhhhyyyyyuuyhgyycvhhyuihhggggg九龙路咯摩托封路了咯莫呕吐了h[grimacing][grinning][grin][wink][blush][blush] ", "", "{\"img_list\":[{\"img_name\":\"https://avatar01.jiaoliuqu.com/cn.taqu.test_iOS_avatar_115_1644377884786_16691_0.jpg\",\"width\":\"100\",\"height\":\"100\"}]}");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

            String result = client.post("", params);
            System.out.println(result);
//        }
    }

    @Test
    public void getIntroductionFromGoodSamples(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=introduction&method=getIntroductionFromGoodSamples");
        HttpClient client = new HttpClient(TEST_HOST + "?service=introduction&method=getIntroductionFromGoodSamples");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 3bb733ea9a46c62d69cb3cb20c7b02c8
        // 账号uuid 活体图片 性别
        List<Object> objects = Arrays.asList("bb", "");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getIntroductionPage(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=introduction&method=getIntroductionPage");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=introduction&method=getIntroductionPage");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 3bb733ea9a46c62d69cb3cb20c7b02c8
        // 账号uuid 活体图片 性别
        List<Object> objects = Arrays.asList("1", "30", "0", "0", "9999999999", "", "", "");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    /**
     * 批量通过
     */
    @Test
    public void batchAuditPass(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=introduction&method=batchAuditPass");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=introduction&method=batchAuditPass");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 3bb733ea9a46c62d69cb3cb20c7b02c8
        // 账号uuid 活体图片 性别
        List<Long> ids = new ArrayList<>();
        ids.add(14L);
        List<Object> objects = Arrays.asList(ids);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    /**
     * 批量拒绝
     */
    @Test
    public void batchAuditReject(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=introduction&method=batchAuditReject");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=introduction&method=batchAuditReject");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 3bb733ea9a46c62d69cb3cb20c7b02c8
        // 账号uuid 活体图片 性别
        List<Long> ids = new ArrayList<>();
        ids.add(7L);
        List<Object> objects = Arrays.asList(ids);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getIntroductionFromGoodSamplesUrl(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=introduction&method=getIntroductionFromGoodSamplesUrl");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=introduction&method=getIntroductionFromGoodSamplesUrl");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 3bb733ea9a46c62d69cb3cb20c7b02c8
        // 账号uuid 活体图片 性别
        List<Long> ids = new ArrayList<>();
        ids.add(7L);
        List<Object> objects = Arrays.asList(ids);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
}


//    /**
//     * 测试 图片检测
//     * @param accountUuid
//     * @param avatarUrl
//     * @return
//     */
//    public Map<String, String> testDetectPhoto(String accountUuid, String avatarUrl) {
//
//        avatarUrl = avatarHandleService.getAvatarByVersion(avatarUrl, null);
//        avatarUrl = replacePhotoDomainCompare(avatarUrl);
//
//        // 查询活体图片
//        AliyunLiveFaceDetect liveFaceDetect = SpringContextHolder.getBean(AliyunLiveFaceDetectDao.class).getInfoByAccountUuid(accountUuid);
//        String faceBasePhoto = "";
//        if(liveFaceDetect != null){
//            faceBasePhoto = liveFaceDetect.getBase_photo_url();
//            faceBasePhoto = avatarHandleService.getAvatarByVersion(faceBasePhoto, null);
//            faceBasePhoto = replacePhotoDomainCompare(faceBasePhoto);
//        }else{
//            throw new ServiceException(CodeStatus.UPLOAD_AVATAR_FAIL_LIVE_FACE);
//        }
//        Future<ShumeiImgCheckResponseDTO> shumeiFuture = ThirdPartFactory.shumeiImgCheckPool.submit(new ShumeImgCheckCall(accountUuid, avatarUrl));
//        Future<TencentImgQualityDTO> imgQualityFuture = ThirdPartFactory.tencentImgQualityPool.submit(new TencentImgQualityCall(accountUuid, avatarUrl));
//        Future<CompareFaceDto> imgCompareFuture = ThirdPartFactory.tencentImgComparePool.submit(new TencentImgCompareCall(accountUuid, avatarUrl, faceBasePhoto));
//
//        ShumeiImgCheckResponseDTO shumeiImgCheckResponseDTO = null;
//        TencentImgQualityDTO tencentImgQualityDTO = null;
//        CompareFaceDto compareFaceDto = null;
//        try {
//            shumeiImgCheckResponseDTO = shumeiFuture.get(5000, TimeUnit.MILLISECONDS);
//            tencentImgQualityDTO = imgQualityFuture.get(5000, TimeUnit.MILLISECONDS);
//            compareFaceDto = imgCompareFuture.get(5000, TimeUnit.MILLISECONDS);
//        }catch (Exception e){
//            logger.error("图片检测失败", e);
//        }
//
//        LOGGER.info("图片违规检测.结果={}", shumeiImgCheckResponseDTO == null ? "" : JSON.toJSONString(shumeiImgCheckResponseDTO));
//        LOGGER.info("图片质量检测.结果={}", tencentImgQualityDTO == null ? "" :JSON.toJSONString(tencentImgQualityDTO));
//        LOGGER.info("图片比对检测.结果={}", compareFaceDto == null ? "" :JSON.toJSONString(compareFaceDto));
//
//        if(shumeiImgCheckResponseDTO == null || Objects.equals(shumeiImgCheckResponseDTO.getRiskLevel(), ShumeiImgCheckResponseRiskLevelEnum.REJECT.name())){
//            logger.info("图片违规");
//        }
//        if(tencentImgQualityDTO == null){
//            throw new ServiceException(CodeStatus.PHOTO_DETECT_QUALITY_BUSY);
//        }
//        if(!tencentImgQualityDTO.isImgQualityPass()){
//            logger.info("图片模糊");
//        }
//
//        if(compareFaceDto == null || !compareFaceDto.isRequestSuccess()){
//            throw new ServiceException(CodeStatus.PHOTO_DETECT_COMPARE_BUSY);
//        }
//        if(!compareFaceDto.isSimilarity()){
//            logger.info("图片非本人");
//        }
//
//        return new HashMap<>();
//    }
