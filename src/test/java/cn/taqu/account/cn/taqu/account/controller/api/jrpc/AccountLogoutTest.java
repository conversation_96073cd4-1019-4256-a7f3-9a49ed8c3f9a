package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.account.common.LogoutTypeEnum;
import cn.taqu.account.service.AccountActionService;
import cn.taqu.account.service.AccountsInfoService;
import cn.taqu.account.vo.GetByTicketVo;
import cn.taqu.core.common.client.HttpClient;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-12 15:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AccountLogoutTest {

    @Autowired
    private AccountActionService accountActionService;
    @Autowired
    private AccountsInfoService accountsInfoService;

    /**
     * 主动注销
     */
    @Test
    public void activeLogout(){
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=account&method=logout");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        List<Object> objects = Arrays.asList("zzzzzzzzzzz");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

//
//    @Test
//    public void getByTicket(){
//        GetByTicketVo vo = accountsInfoService.getByTicket("ak1vti7jt547");
//        System.out.println(JSON.toJSONString(vo));
//    }

}
