package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-08 16:08
 */
@Slf4j
public class AccountNickNameTest {

    private static String TEST_HOST = "http://j2.test2.k8s.taqu.cn/tq-account/api";
    private static String DEV_HOST = "http://localhost:8080/tq-account/api";


    @Test
    public void setNicknameV2(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=account&method=setNicknameV2");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=account&method=setNicknameV2");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 账号uuid 活体图片 性别
        List<Object> objects = Arrays.asList("djbd30v6s8gi", "abcaaaaa");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
}
