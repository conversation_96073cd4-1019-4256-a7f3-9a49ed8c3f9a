package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import com.google.gson.Gson;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-30 14:05
 */
public class AccountPhotoTest {

    // reportAccountInfo


    private static String TEST_HOST = "http://j2.test2.k8s.taqu.cn/tq-account/api";
    private static String DEV_HOST = "http://localhost:8080/tq-account/api";

    /**
     * 保存个性签名
     */
    @Test
    public void reportAccountInfo(){

        HttpClient client = new HttpClient(DEV_HOST + "?service=info&method=reportAccountInfo");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=info&method=reportAccountInfo");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("djbd30v6s8gi");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
    // pullNoCheckFromDB
    @Test
    public void pullNoCheckFromDB(){

//        HttpClient client = new HttpClient(DEV_HOST + "?service=accountPhotoFastCheckTest&method=pullNoCheckFromDB");
        HttpClient client = new HttpClient(TEST_HOST + "?service=accountPhotoFastCheckTest&method=pullNoCheckFromDB");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
}
