package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import com.google.gson.Gson;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-16 10:18
 */
public class AccountsCertWhiteListTest {

    private static String TEST_HOST = "http://j2.test.k8s.taqu.cn/tq-account/api";
    private static String DEV_HOST = "http://localhost:8080/tq-account/api";
    private static String SERVICE = "certWhiteList";
    private static String SERVICE_1 = "accountsCert";
    private static String SERVICE_2 = "identityNoWhiteList";

    @Test
    public void getListDataForPage(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE + "&method=getListDataForPage", 20 * 1000, 20 * 1000, 20 * 1000);
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE + "&method=getListDataForPage", 20 * 1000, 20 * 1000, 20 * 1000);

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(1, 20, 0, 9999999999999L, "", null);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getListDataForPage1(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE_2 + "&method=getListDataForPage", 20 * 1000, 20 * 1000, 20 * 1000);
//        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE_2 + "&method=getListDataForPage", 20 * 1000, 20 * 1000, 20 * 1000);

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(1, 20, 0, 9999999999999L, "");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void addCertWhiteList(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE + "&method=addCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE + "&method=addCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("bijecurub4mo", 1, "测试");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void addCertWhiteList2(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE_2 + "&method=addCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);
//        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE_2 + "&method=addCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("bijecurub4mo", 1, "测试");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
    // batchAddCertWhiteList
    @Test
    public void batchAddCertWhiteList(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE + "&method=batchAddCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE + "&method=batchAddCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(Arrays.asList("p224t03oxss,1,测试用户","ck58usw8pl62,2,测试用户2","bijecurub4mo,3,测试用户333,注意后面的类型哦，最好copy，不要手写"));
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void delCertWhiteList(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE + "&method=delCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE + "&method=delCertWhiteList", 20 * 1000, 20 * 1000, 20 * 1000);


        Map<String, Object> params = Utils.getCommonHeaderParam();
        /**
         *
         String accountUuid = params.getFormStringOption(0);
         Integer whiteListFrom = params.getFormInteger(1);
         String remark = params.getFormStringOption(2);
         */
        List<Object> objects = Arrays.asList(Arrays.asList(3,4,5,6,7,8,9));
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void needFinishBeforeCert(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE_1 + "&method=needFinishBeforeCert", 20 * 1000, 20 * 1000, 20 * 1000);
//        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE_1 + "&method=needFinishBeforeCert", 20 * 1000, 20 * 1000, 20 * 1000);


        Map<String, Object> params = Utils.getCommonHeaderParam();
        /**
         *
         String accountUuid = params.getFormStringOption(0);
         Integer whiteListFrom = params.getFormInteger(1);
         String remark = params.getFormStringOption(2);
         */
        List<Object> objects = Arrays.asList("djbd30v6s8gi", 1);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }


    @Test
    public void getCertWhiteListInfo(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=" + SERVICE + "&method=getCertWhiteListInfo", 20 * 1000, 20 * 1000, 20 * 1000);
        HttpClient client = new HttpClient(TEST_HOST + "?service=" + SERVICE + "&method=getCertWhiteListInfo", 20 * 1000, 20 * 1000, 20 * 1000);


        Map<String, Object> params = Utils.getCommonHeaderParam();
        /**
         *
         String accountUuid = params.getFormStringOption(0);
         Integer whiteListFrom = params.getFormInteger(1);
         String remark = params.getFormStringOption(2);
         */
        List<Object> objects = Arrays.asList("11");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }


    @Test
    public void getAddress(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=ipTest&method=getAddress", 20 * 1000, 20 * 1000, 20 * 1000);
        HttpClient client = new HttpClient(TEST_HOST + "?service=ipTest&method=getAddress", 20 * 1000, 20 * 1000, 20 * 1000);


        Map<String, Object> params = Utils.getCommonHeaderParam();
        /**
         *
         String accountUuid = params.getFormStringOption(0);
         Integer whiteListFrom = params.getFormInteger(1);
         String remark = params.getFormStringOption(2);
         */
        List<Object> objects = Arrays.asList("1.1.500.1");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getAddressByIp(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=mpgeo&method=getAddressByIp", 20 * 1000, 20 * 1000, 20 * 1000);
        HttpClient client = new HttpClient("http://j79.test.k8s.taqu.cn/api?service=mpgeo&method=getAddressByIp", 20 * 1000, 20 * 1000, 20 * 1000);


        Map<String, Object> params = Utils.getCommonHeaderParam();
        /**
         *
         String accountUuid = params.getFormStringOption(0);
         Integer whiteListFrom = params.getFormInteger(1);
         String remark = params.getFormStringOption(2);
         */
        List<Object> objects = Arrays.asList("1.1.500.1");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
}
