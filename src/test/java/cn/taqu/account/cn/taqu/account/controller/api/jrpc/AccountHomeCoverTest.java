package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 14:19
 */
@Slf4j
public class AccountHomeCoverTest {
    private static String TEST_HOST = "http://j2.test2.k8s.taqu.cn/tq-account/api";
    private static String DEV_HOST = "http://localhost:8080/tq-account/api";

    // getNextHomeCover
    @Test
    public void getNextHomeCover(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=getNextHomeCover");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=getNextHomeCover");
        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList();
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getNextHomeCoverBb(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=getNextHomeCover");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=getNextHomeCover");
        Map<String, Object> params = Utils.getCommonHeaderParam();
        params.put("token", "bbbbbb");
        List<Object> objects = Arrays.asList();
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getNoCheckHomeCoverPage(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=getNoCheckHomeCoverPage");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=getNoCheckHomeCoverPage");
        // Integer pageNumber = params.getFormInteger(0);//页数
        //        Integer pageSize = params.getFormInteger(1);//每页数量
        //        Long startTime = params.getFormLongOption(2);//更新时间-开始秒时间戳
        //        Long endTime = params.getFormLongOption(3);//更新时间-结束秒时间戳
        //        String accontUuid = params.getFormStringDefault(4, null);
        //        String operatorToken = params.getFormStringDefault(5, null);
        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(1, 30, 0, 99999999999L, "", "");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getHomeCoverLogPage(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=getHomeCoverLogPage");
        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=getHomeCoverLogPage");
        // Integer pageNumber = params.getFormInteger(0);//页数
        //        Integer pageSize = params.getFormInteger(1);//每页数量
        //        Long startTime = params.getFormLongOption(2);//更新时间-开始秒时间戳
        //        Long endTime = params.getFormLongOption(3);//更新时间-结束秒时间戳
        //        String accontUuid = params.getFormStringDefault(4, null);
        //        String operatorToken = params.getFormStringDefault(5, null);
        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(1, 30, 0, 99999999999L,1, "", "");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void auditHomeCoverPunish(){

        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=auditHomeCover");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=auditHomeCover");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(Arrays.asList(5), 2);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void auditHomeCover(){

        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=auditHomeCover");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=auditHomeCover");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(Arrays.asList(7), 1);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void setAccountsHomeCover(){

        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=setAccountsHomeCover");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=setAccountsHomeCover");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("djbd30v6s8gi", "https://avatar01.jiaoliuqu.com/cn.taqu.test_iOS_avatar_114_1645155269413_52716_0.jpg");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getHomeCoverCheckLogByUuid(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=accountsHomeCover&method=getHomeCoverCheckLogByUuid");
        HttpClient client = new HttpClient(TEST_HOST + "?service=accountsHomeCover&method=getHomeCoverCheckLogByUuid");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("djbd30v6s8gi");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
}
