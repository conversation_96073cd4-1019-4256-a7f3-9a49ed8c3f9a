package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import com.google.gson.Gson;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.taqu.account.Utils.utf8ToBase64;

/**
 * <AUTHOR>
 * @date 2021/7/8
 */
public class AccountsInfoControllerTest {

    @Test
    public void updateAccountsBirthTest(){
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=accountsInfo&method=updateAccountsBirthByUuid");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        // uuid birth
        List<Object> objects = Arrays.asList("d4gqt7xfgq8", "**********");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void updateAccountspersonalProfileByUuidTest(){
        HttpClient client = new HttpClient("http://localhost:8080/tq-account/api?service=accountsInfo&method=updateAccountspersonalProfileByUuid");
        Map<String, Object> params = Utils.getCommonHeaderParam();

        // uuid birth
        List<Object> objects = Arrays.asList("d4gqt7xfgq8", "abcd");

        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
}
