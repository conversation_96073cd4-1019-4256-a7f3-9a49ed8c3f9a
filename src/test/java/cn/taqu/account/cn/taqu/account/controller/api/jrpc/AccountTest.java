package cn.taqu.account.cn.taqu.account.controller.api.jrpc;

import static cn.taqu.account.Utils.utf8ToBase64;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;

import cn.taqu.account.Utils;
import cn.taqu.core.common.client.HttpClient;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-03 09:23
 */

@Slf4j
public class AccountTest {

    private static String TEST_HOST = "http://j2.test2.k8s.taqu.cn/tq-account/api";
    private static String DEV_HOST = "http://localhost:8080/tq-account/api";


    @Test
    public void compareAndCheckFaceDetect(){
        HttpClient client = new HttpClient(DEV_HOST + "?service=faceDetect&method=compareAndCheckFaceDetect");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=faceDetect&method=compareAndCheckFaceDetect");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 账号uuid 活体图片 性别
        List<Object> objects = Arrays.asList("djbd30v6s8gi");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void isAccountNeedRedo(){
//        HttpClient client = new HttpClient(DEV_HOST + "?service=info&method=isAccountNeedRedo");
        HttpClient client = new HttpClient(TEST_HOST + "?service=info&method=isAccountNeedRedo");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 账号uuid 活体图片 性别
        List<Object> objects = Arrays.asList("djbd30v6s8gi");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void saveGender() {
        HttpClient client = new HttpClient(DEV_HOST + "?service=faceDetect&method=saveGender");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=faceDetect&method=saveGender");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        // 账号uuid 活体图片 性别
        List<Object> objects = Arrays.asList("", "", "");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getCheckLogPage() {
        HttpClient client = new HttpClient(DEV_HOST + "?service=photoFaceLevel&method=getCheckLogPage");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=photoFaceLevel&method=getCheckLogPage");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("1","30", "1", "**********");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }


    @Test
    public void getAccountsPhotoCondition() {
//        HttpClient client = new HttpClient(DEV_HOST + "?service=photo&method=getAccountsPhotoCondition");
        HttpClient client = new HttpClient(TEST_HOST + "?service=photo&method=getAccountsPhotoCondition");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("1","30","1", "**********", "**********", "","","");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }


    @Test
    public void getNextAccountsPhoto() {
        HttpClient client = new HttpClient(DEV_HOST + "?service=photo&method=getNextAccountsPhoto");
//        HttpClient client = new HttpClient(TEST_HOST + "?service=photo&method=getNextAccountsPhoto");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void getDestroyInfo() {
        HttpClient client = new HttpClient(TEST_HOST + "?service=account&method=getDestoryInfo");
//        HttpClient client = new HttpClient(DEV_HOST + "?service=account&method=getDestroyInfo");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("0f5d3c5cf4c7bf736601e62b1c8cd464");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }
    // ["e3c8c5a2f2f3eee00c7d633b5d439dbb","测试注销",1, "mobile","verify","","",""]
    @Test
    public void destroyV2() {
        HttpClient client = new HttpClient(TEST_HOST + "?service=account&method=destroyV2");
//        HttpClient client = new HttpClient(DEV_HOST + "?service=account&method=getDestroyInfo");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("642e4ba7c95c0d8e5c94134bd0ebbf70","测试注销","***********","968894");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void sendDestroySmsCode() {
//        HttpClient client = new HttpClient(TEST_HOST + "?service=account&method=sendDestroySmsCode");
        HttpClient client = new HttpClient(DEV_HOST + "?service=account&method=sendDestroySmsCode");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("0f5d3c5cf4c7bf736601e62b1c8cd464");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));

        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void photoIgnoreV4(){
        HttpClient client = new HttpClient(TEST_HOST + "?service=photo&method=photoIgnoreV4");

        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("");
//        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        params.put("form", "WyJ7XCIyNDIxOFwiOlwiaHR0cHM6Ly9hdmF0YXIwMS5qaWFvbGl1cXUuY29tL3RhcXVfaW9zXzBfMzQ0MjA0ODY1MF8xNjI4MjM4NzM4NDYzLmpwZ1wifSJd");
        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void cancelCertification(){
        HttpClient client = new HttpClient(TEST_HOST + "?service=certification&method=cancelCertification");
        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("dhq5kmp54tkw");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void cancelFaceVerify(){
        HttpClient client = new HttpClient(TEST_HOST + "?service=aliyunFaceDetect&method=cancelFaceVerify");
        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList(1911);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

    @Test
    public void test(){
//        Map<String, Object> result = Maps.newHashMap();
//        Object[] form = {accountUuid, StringUtils.join(fieldNames, ",")};
//
//        SoaResponse response = SoaClientFactory.create(SoaServer.JAVA.SEARCH).call(service, method, form);
//        if (response.fail()) {
//            log.error("调用j6根据id查询画像标签对应的map失败，uuid:{}，code:{}, 失败原因:{}", accountUuid, response.getCode(), response.getMsg());
//            return result;
//        } else {
//            result = JsonUtils.stringToObject(response.getData(), new TypeReference<Map<String, Object>>() {});
//        }
//        return result;

        String service = "userPortrait";
        String method = "getUserCapacityByUuid";
        String baseUrl = "http://j6.test2.k8s.taqu.cn/tq-search/api";
        String url = baseUrl + "?service=" + service + "&method=" + method;
        System.out.println(url);
        HttpClient client = new HttpClient(url);
        List<String> fields = Arrays.asList("inpour_amount_total", "interact_cash");
        Map<String, Object> params = Utils.getCommonHeaderParam();
//        List<Object> objects = Arrays.asList("dhrjqiy3e5r0", fields);
        List<Object> objects = Arrays.asList("dhvatiqo15yr", fields);
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        JSONObject jsonObject = JSON.parseObject(result);
        BigDecimal interactCash = jsonObject.getJSONObject("data").getBigDecimal("interact_cash");
        BigDecimal inpourAmountTotal = jsonObject.getJSONObject("data").getBigDecimal("inpour_amount_total");
        if(null == interactCash){
            interactCash = BigDecimal.ZERO;
        }

        if(null == inpourAmountTotal){
            inpourAmountTotal = BigDecimal.ZERO;
        }
        System.out.println(result);
    }

    @Test
    public void requestVerificationCodeV2(){
        HttpClient client = new HttpClient("http://j39.test.k8s.taqu.cn/tq-push-v2/api" + "?service=verificationCode&method=requestVerificationCodeV2");
        Map<String, Object> params = Utils.getCommonHeaderParam();
        List<Object> objects = Arrays.asList("destroy_account","SINGLE_VCODE","di2zzgfq5ebl");
        params.put("form", utf8ToBase64(new Gson().toJson(objects)));
        String result = client.post("", params);
        System.out.println(result);
    }

}
