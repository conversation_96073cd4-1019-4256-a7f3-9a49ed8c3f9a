package cn.taqu.account;


import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;

import org.apache.http.HttpHeaders;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.FileReader;
import java.security.PrivateKey;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 苹果登录demo
 */
public class SignInWithApple2 {

    private static String APPLE_AUTH_URL = "https://appleid.apple.com/auth/token";

//    private static String KEY_ID = "279SCN3AMY";
//    private static String TEAM_ID = "SYXJZ7568R";
//    private static String CLIENT_ID = "cn.ishengsheng.xingjiabi";
    private static String KEY_ID = "L54TMSP6UK";
    private static String TEAM_ID = "69YD8JX86J";
    private static String CLIENT_ID = "cn.ishengsheng.xingjiabi";

    private static PrivateKey pKey;

    private static PrivateKey getPrivateKey() throws Exception {
//read your key
        String path = new ClassPathResource("AuthKey_L54TMSP6UK.p8").getFile().getAbsolutePath();

        final PEMParser pemParser = new PEMParser(new FileReader(path));
        final JcaPEMKeyConverter converter = new JcaPEMKeyConverter();
        final PrivateKeyInfo object = (PrivateKeyInfo) pemParser.readObject();
        final PrivateKey pKey = converter.getPrivateKey(object);

        return pKey;
    }

    private static String generateJWT() throws Exception {
        if (pKey == null) {
            pKey = getPrivateKey();
        }

        String token = Jwts.builder()
                .setHeaderParam(JwsHeader.ALGORITHM,"ES256")
                .setHeaderParam(JwsHeader.KEY_ID, KEY_ID)
                .setIssuer(TEAM_ID)
                .setAudience("https://appleid.apple.com")
                .setSubject(CLIENT_ID)
                .setExpiration(new Date(System.currentTimeMillis() + (1000 * 60 * 60 * 24 * 2)))
                .setIssuedAt(new Date(System.currentTimeMillis() - (1000 * 60 * 60 * 24 * 2)))
                .signWith(SignatureAlgorithm.ES256,pKey)
                .compact();

        return token;
    }

    @Test
    public void testLogin() throws Exception{
        appleAuth("c7ec9471f3c384eb4bc16b9c653531f12.0.nrsty.OC-6IDuAF1cpxKrXtzOMRQ");
    }
    /*
     * Returns unique user id from apple
     * */
    public static String appleAuth(String authorizationCode) throws Exception {


        String token = generateJWT();

        HttpResponse<String> response = Unirest.post(APPLE_AUTH_URL)
                .header(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header(HttpHeaders.USER_AGENT,"app")
                .field("client_id", CLIENT_ID)
                .field("client_secret", token)
                .field("grant_type", "authorization_code")
                .field("code", authorizationCode)
                .asString();

        TokenResponse tokenResponse = new Gson().fromJson(response.getBody(),TokenResponse.class);
        String idToken = tokenResponse.getId_token();
        String payload = idToken.split("\\.")[1];//0 is header we ignore it for now
        String decoded = new String(Decoders.BASE64.decode(payload));

        IdTokenPayload idTokenPayload = new Gson().fromJson(decoded,IdTokenPayload.class);

        return idTokenPayload.getSub();
    }


    @Test
    public void test(){
        String photos = "{\"bucket\":\"avatar01\",\"img_list\":[{\"img_name\":\"123.jpg\",\"similarity_score\":\"95.0\",\"face_score\":\"80.0\"},{\"img_name\":\"345.jpg\",\"similarity_score\":\"94.0\",\"face_score\":\"80.0\"}]}";
       // Map<String , Object> photoData = JsonUtils.stringToObject2(photos, new TypeReference<Map<String, Object>>() {});
        JSONObject data = JSONObject.parseObject(photos);
        JSONArray jsonArray = data.getJSONArray("img_list");
        //JSONArray jsonArray =  JSON.parseArray(photoData.get("img_list").toString());
        List<Map<String,Object>> imgList = JsonUtils.stringToObject2(jsonArray.toJSONString(), new TypeReference<List<Map<String,Object>>>() {});
        System.out.println(imgList);
    }


}
