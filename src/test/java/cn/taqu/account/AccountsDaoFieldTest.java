package cn.taqu.account;

import cn.taqu.account.model.AccountsHomeCover;
import cn.taqu.account.model.AccountsHomeCoverLog;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.persistence.Column;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-03 09:23
 */
@Slf4j
public class AccountsDaoFieldTest {

    @Test
    public void gen(){
        List<String> names = new ArrayList<>();
        Field[] fields = AccountsHomeCoverLog.class.getDeclaredFields();
        for (Field field: fields) {
            Column annotation = field.getAnnotation(Column.class);
            String name = annotation.name();
            names.add(name);
        }
        System.out.println(JSON.toJSONString(names));
    }


}
