package cn.taqu.account.dev.aliyun;

import cn.taqu.account.service.MobilePlaceService;
import org.junit.Test;

import java.util.Map;

public class MobilePlaceTest {

    @Test
    public void getMobilePlace() {
        String config = "{ \"url\": \"https://api04.aliyun.venuscn.com\", \"appcode\": \"xxxxxxxxxxx\" }";
        MobilePlaceService.paserConfig(config);
        MobilePlaceService mobilePlaceService = new MobilePlaceService();
        Map<String, String> placeMap = mobilePlaceService.getPlace("***********");
        System.out.println(placeMap);
    }
}
