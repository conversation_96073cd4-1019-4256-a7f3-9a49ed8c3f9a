package cn.taqu.account.client;

import java.util.Arrays;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Hex;
import org.junit.Test;

import cn.taqu.core.protocol.SoaBaseParams;
import cn.taqu.core.utils.Encodes;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.Md5Util;

/**
 * <AUTHOR>  2020/2/5 9:12 上午
 */
public class FlashClientTest {

    @Test
    public void aesDecrypt() throws Exception {
        String string = "BAF3E95EA8F95337E5327714DE823FB2";
        String aesDecrypt = EncryptUtil.aesDecrypt(string, Md5Util.encode("KmDriWym"));
        System.out.println(aesDecrypt);

    }
    
    private static class EncryptUtil {
        private EncryptUtil() {
        }

        private static byte[] iv = { 1, 2, 3, 4, 5, 6, 7, 8 };

        public static String encryptDES(String encryptString, String encryptKey)
                throws Exception {
            IvParameterSpec zeroIv = new IvParameterSpec(iv);
            SecretKeySpec key = new SecretKeySpec(encryptKey.getBytes(), "DES");
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key, zeroIv);
            byte[] encryptedData = cipher.doFinal(encryptString.getBytes());
            return Encodes.encodeBase64(encryptedData);
        }

        public static String decryptDES(String decryptString, String decryptKey)
                throws Exception {
            byte[] byteMi = Encodes.decodeBase64(decryptString);
            IvParameterSpec zeroIv = new IvParameterSpec(iv);
            SecretKeySpec key = new SecretKeySpec(decryptKey.getBytes(), "DES");
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, key, zeroIv);
            byte decryptedData[] = cipher.doFinal(byteMi);
            return new String(decryptedData);
        }

        private static String mapHmacSHA256Encrypt(Map<String, String> requestMap, String appKey) throws Exception {
            return hmacSHA256Encrypt(requestMap2Str(requestMap), appKey);
        }

        private static String hmacSHA256Encrypt(String encryptText, String encryptKey) throws Exception {
            //根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
            SecretKeySpec signinKey = new SecretKeySpec(encryptKey.getBytes("UTF-8"), "HmacSHA256");
            //生成一个指定 Mac 算法 的 Mac 对象
            Mac mac = Mac.getInstance("HmacSHA256");
            //用给定密钥初始化 Mac 对象
            mac.init(signinKey);
            //完成 Mac 操作
            byte[] rawHmac = mac.doFinal(encryptText.getBytes("UTF-8"));
            return new String(Hex.encodeHex(rawHmac, false));
        }

        private static String requestMap2Str(Map<String, String> requestMap) {
            String[] keys = requestMap.keySet().toArray(new String[0]);
            Arrays.sort(keys);
            StringBuilder stringBuilder = new StringBuilder();
            for (String str : keys) {
                if (!"sign".equals(str)) {
                    stringBuilder.append(str).append(requestMap.get(str));
                }
            }
            return stringBuilder.toString();
        }

        public static String aesDecrypt(String sSrc, String key) throws Exception {
            String sKey = key.substring(0, 16);
            String siv = key.substring(16);
            String charset = "UTF-8";

            if (sSrc == null || sSrc.length() == 0) {
                return null;
            }
            if (sKey == null) {
                throw new Exception("decrypt key is null");
            }
            if (sKey.length() != 16) {
                throw new Exception("decrypt key length error");
            }
            byte[] decrypt = Hex.decodeHex(sSrc.toCharArray());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec skeySpec = new SecretKeySpec(sKey.getBytes(charset), "AES");
            IvParameterSpec iv = new IvParameterSpec(siv.getBytes(charset));//new IvParameterSpec(getIV());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);//使用解密模式初始化 密
            return new String(cipher.doFinal(decrypt), charset);
        }
    }
    
}
