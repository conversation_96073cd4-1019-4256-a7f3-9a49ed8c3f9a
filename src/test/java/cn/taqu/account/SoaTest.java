package cn.taqu.account;


import cn.taqu.account.service.SoaService;
import cn.taqu.account.utils.EncryptUtil;
import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SoaTest {

    /**
     * 批量获取发帖人的信息
     */
    @Test
    public void testGetPostInfo(){
        List<String> postUuids = Lists.newArrayList("cbi1h4we3c0mp");
        String[] fields = {"longitude","latitude","account_uuid"};
        Object[] form = {
                postUuids.toArray(),fields
        };
        System.out.println(JsonUtils.objectToString(form));
        SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.FORUM_V5);
        SoaResponse response = soaClient.call("PostDetail", "mGetPostInfoByField", form);
        Map<String,Object> map = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String,Object>>() {});
        if (response.fail()) {

        }
    }

    /**
     * 查询用户是否支付
     */
    @Test
    public void testGetIfPay(){
        Object[] form = {
                "gu7aq26r14p"
        };
        System.out.println(JsonUtils.objectToString(form));
        SoaClient soaClient = SoaClientFactory.create(SoaServer.PHP.LIVE_V1);
        SoaResponse response = soaClient.call("Account", "getInfo", form);
        Map<String,Object> map = JsonUtils.stringToObject2(response.getData(), new TypeReference<Map<String,Object>>() {});
        if (response.fail()) {

        }
    }

    @Test
    public void testBatchEncryptDecript(){
        Map<String,String> map = Maps.newHashMap();
        map.put("phone",null);
        map.put("name","justin");
        Map<String,String> result1 = EncryptUtil.batchEncrypt(map);
        Map<String,String> result2 = EncryptUtil.batchDecrypt(result1);
        System.out.println(result2);
    }

    @Test
    public void testSm3(){
        //196a3aa46160f05e2930af2c0d4e0364be8803a40b82983021f018c1b935b5d9
        String sm3Content = EncryptUtil.sm3("***********");
        System.out.println(sm3Content);
        // b8e3e248c6b2105a1741194945a3c7e2ae4184097b9345ef9ad746713810d7d0
        
    }

    @Test
    public void testBatchSm3(){
        Map<String,String> map = Maps.newHashMap();
        map.put("phone","");
        map.put("name","justin");
        Map<String,String> result = EncryptUtil.batchSm3(map);
        System.out.println(JSON.toJSON(result));
    }
//    @Test
//    public  void getCharmScoreByUuid(){
//        SoaService.getCharmScoreByUuid("a9t13qryf4u");
//    }

}
