package cn.taqu.account;

import com.google.common.collect.Lists;

import cn.taqu.account.vo.AccountLucVo;
import cn.taqu.core.utils.JsonUtils;

/**
 * 
 * <AUTHOR>
 * 2023年12月5日上午9:23:19
 */
public class AddLucTest {

    
    public static void main(String[] args) {
        AccountLucVo accountLucVo = new AccountLucVo();
        accountLucVo.setAffectivestatus(1);
//        accountLucVo.setAge(null);
        accountLucVo.setAvatar_list(Lists.newArrayList("taqu_android_avatar_101_1645167187197_1_0_73318.JPEG"));
        accountLucVo.setBirth(660360398L);
//        accountLucVo.setConstellation(null);
        accountLucVo.setEducation_level("小学");
        accountLucVo.setHeight(181);
        accountLucVo.setHometown("227,303");
        accountLucVo.setIncome(6);
        accountLucVo.setMobile("***********");
        accountLucVo.setNickname("soa创建测试luc");
        accountLucVo.setProfession("生产管理");
        accountLucVo.setSex_type(1);
        accountLucVo.setTrade("生产制造");
        accountLucVo.setWeight(71);
        
        System.out.println(JsonUtils.objectToString(accountLucVo));
    }
}
