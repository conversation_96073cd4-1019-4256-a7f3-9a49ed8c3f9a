package cn.taqu.account;


import cn.taqu.account.constant.CommConst;
import cn.taqu.account.dto.PostUpdateUuidDto;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.utils.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

//@RunWith(SpringRunner.class)
////@SpringBootTest
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = Application.class)
public class TqmqTest {


    @Test
    public void push1() {
        PostUpdateUuidDto dto = new PostUpdateUuidDto();
        dto.setSendUuid("a9y5cdtmdue");
        dto.setRecUuid(Lists.newArrayList("bvubjhtowfv", "a9qqqgqpqfc"));
        MqClient mqClient = new MqClient("http://g2.test.k8s.taqu.cn/v2/Soa/jService");
        MqResponse mqResponse = mqClient.push("follow_post_update_queue", dto, null);
        System.out.println(mqResponse);
    }

    @Test
    public void push2() {
        MqClient mq = new MqClient("http://g2.test.k8s.taqu.cn/v2/Soa/jService");
        Map<String, String> param = new HashMap<>();
        param.put("post_uuid", "cbi5d9olap508");
        mq.push("post_pass_queue", param, null);
    }

    /**
     * accounts_active_log表数据定时清理
     */
    @Test
    public void push3() {
        MqClient mq = new MqClient("http://10.10.60.186:8090/v2/Soa/jService");
        Map<String, String> param = new HashMap<>();
        param.put("uuid", "ubdj6aqt87a");
        param.put("time", DateUtil.currentTimeSeconds().toString());
        param.put("city_id", "");
        MqResponse mqResponse = mq.push("taqu_account_active", param, null);
        System.out.println(mqResponse);
    }

    public static void main(String[] args) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("service", "asyncrobot");
        map.put("method", "pushMarkdownMsg");
        Map<String, Object> map2 = Maps.newHashMap();
        map2.put("webhook_key", "https://oapi.dingtalk.com/robot/send?access_token=933b61ce40cff540d966b4e9b4ed297d55ce11fd343aacddce650d422bd26dc4");
        map2.put("atDingtalkIds", "");
        map2.put("atMobiles", new String[]{});
        map2.put("text", "白名单添加文案换行测试 \n\n 添加时间:" + DateUtil.getCurrentTime());
        map2.put("title", "白名单添加文案换行测试");
        map.put("asyncforms", map2);
        MqClient mqClient = new MqClient("http://g2.test.k8s.taqu.cn/v2/Soa/jService");
        MqResponse mqResponse = mqClient.push(CommConst.PUSH_ASYNC_INVOKE_QUEUE, map, null);
        if (mqResponse.fail()){
            System.out.println(mqResponse.getMsg());
        }
        System.out.println(mqResponse.toString());
    }
}
