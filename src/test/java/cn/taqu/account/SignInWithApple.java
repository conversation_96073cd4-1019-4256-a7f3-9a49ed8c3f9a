package cn.taqu.account;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwtParser;
import io.jsonwebtoken.Jwts;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;

import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;

public class SignInWithApple {

    private static Logger logger = LoggerFactory.getLogger(SignInWithApple.class);


    @Test
    public void test(){
        verify("eyJraWQiOiJlWGF1bm1MIiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RGj8ofeSJ738qUw1rMWTu9lcuKDP0umi5A7dJCArjRrW87920dws86v6ZTIQfXj7XKIGSmS38mPGxidqXVqP48RJ7Y_VsuJgowajLsI7xo7XgYRLYTwpx5TLYVeR0uOH5wkpIjBXyUMxvqsSmc7ODKxYzXBnRftzYFNZ4WQ_fJvF_K9q6aVmFYPg_lztE9G00b0A95BfGEACBX96M8tQI6Roc1DwJkkklJwWj2yxqnspEIgd5ALw1Xhjwr5gDBoSZz4-XD_wtCO1FBjafZgOKfr4cbpEb1bBq9u1k4SoUj0O4p8P_8ukVd5ifGHE_WHkXtVLmMUBDQoEZNOBOaC7HA ");
    }

    /**
     * 解密个人信息
     *
     * @param identityToken APP获取的identityToken
     *
     * @return 解密参数：失败返回null  sub就是用户id,用户昵称需要前端传过来
     */
    public static JSONObject verify(String identityToken) {
        try {
            String[] identityTokens = identityToken.split("\\.");
            String str = new String(Base64Utils.decodeFromString(identityTokens[1]), "UTF-8");
            JSONObject data = JSONObject.parseObject(str);
            String aud = (String) data.get("aud");
            String sub = (String) data.get("sub");
            if (verify(identityToken, aud, sub)) {
                return data;
            }
        } catch (Exception e) {
            logger.info("verify(*) error ",e);
        }
        return null;
    }

    /**
     * 验证
     *
     * @param identityToken APP获取的identityToken
     * @param aud           您在您的Apple Developer帐户中的client_id
     * @param sub           用户的唯一标识符对应APP获取到的：user
     * @return true/false
     */
    private static boolean verify(String identityToken, String aud, String sub) {
        try {
            PublicKey publicKey = getPublicKey();
            if (publicKey==null){
                return false;
            }
            JwtParser jwtParser = Jwts.parser().setSigningKey(publicKey);
            jwtParser.requireIssuer("https://appleid.apple.com");
            jwtParser.requireAudience(aud);
            jwtParser.requireSubject(sub);
            Jws<Claims> claim = jwtParser.parseClaimsJws(identityToken);
            if (claim != null && claim.getBody().containsKey("auth_time")) {
                return true;
            }
        } catch (Exception e) {
            logger.info("verify(*,*,*) error ", e);
        }
        return false;
    }

    private static PublicKey getPublicKey() {
        try {

            String str = httpGet("https://appleid.apple.com/auth/keys");
            JSONObject data = JSONObject.parseObject(str);
            JSONArray jsonArray = data.getJSONArray("keys");
            String n = jsonArray.getJSONObject(0).getString("n");
            String e = jsonArray.getJSONObject(0).getString("e");
            BigInteger modulus = new BigInteger(1, Base64.decodeBase64(n));
            BigInteger publicExponent = new BigInteger(1, Base64.decodeBase64(e));

            RSAPublicKeySpec spec = new RSAPublicKeySpec(modulus, publicExponent);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            return kf.generatePublic(spec);
        } catch (Exception e) {
            logger.info("getPublicKey error ", e);
        }
        return null;
    }

    private static String httpGet(String url){
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpPost = new HttpGet(url);
        //httpPost.addHeader("Content-Type", "application/json");
        try {
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String responseContent = EntityUtils.toString(entity, "UTF-8");
            response.close();
            httpClient.close();
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

}
