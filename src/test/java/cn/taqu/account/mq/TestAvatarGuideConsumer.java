package cn.taqu.account.mq;

import cn.taqu.account.constant.AvatarGuidePopupEnum;
import cn.taqu.account.dto.AvatarGuideDTO;
import cn.taqu.account.soa.AIGCSoaService;
import cn.taqu.core.utils.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2025/6/27 17:04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestAvatarGuideConsumer {

    @Autowired
    private AvatarGuideConsumer avatarGuideConsumer;
    @Autowired
    private AIGCSoaService aigcSoaService;

    @Test
    public void consumer() {
        AvatarGuideDTO guide = new AvatarGuideDTO();
        guide.setUuid("bgihddchehhdbgee");
        guide.setUrl("https://avatar01.jiaoliuqu.com/avatar/newavatarfemale.jpg");
        guide.setScene(AvatarGuidePopupEnum.BEAUTY.getType());
        guide.setResult("success");
        guide.setError("");
        avatarGuideConsumer.process(JsonUtils.objectToString(guide));
    }

    @Test
    public void avatarQuality() {
        aigcSoaService.avatarQuality("bgihddchehhdbgee", "https://avatar01.jiaoliuqu.com/avatar/newavatarfemale.jpg");
    }
}
