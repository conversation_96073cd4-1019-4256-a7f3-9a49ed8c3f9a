package cn.taqu.account.cron;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 单元测试：AccountCurrentActiveTask
 * <AUTHOR>
 * @date 2021/7/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountCurrentActiveTaskTest {

    @Autowired
    private AccountCurrentActiveTask accountCurrentActiveTask;

    @Test
    public void currentActiveInPass15Days(){
        accountCurrentActiveTask.currentActiveInPass15Days();
    }

    @Test
    public void push(){
        List<String> uuids = new ArrayList<>();
        uuids.add("dg5gg72k07b7");
//        accountCurrentActiveTask.activeAccountPush(uuids);
    }
}
