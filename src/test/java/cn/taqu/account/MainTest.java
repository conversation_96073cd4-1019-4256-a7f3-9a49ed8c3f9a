package cn.taqu.account;

import java.util.ArrayList;
import java.util.LinkedList;

import com.google.common.collect.Lists;

import cn.taqu.account.vo.life.AccountsLifeVo;
import cn.taqu.core.utils.JsonUtils;

/**
 * 
 * <AUTHOR>
 * 2024年11月26日下午2:37:11
 */
public class MainTest {

    public static void main(String[] args) {
        getSetAccountsLifeVo();
    }
    
    
    private static void getSetAccountsLifeVo() {
        
//        SetAccountsLifeVo setAccountsLifeVo = new SetAccountsLifeVo();
//        ArrayList<AccountsLifeContainerVo> containerList = Lists.newArrayList();
        ArrayList<Integer> seqList = Lists.newArrayList(3,2,1,4,5);
        LinkedList<AccountsLifeVo> imgList = Lists.newLinkedList();
        
//        setAccountsLifeVo.setContainerList(containerList);
//        setAccountsLifeVo.setImgList(imgList);
//        setAccountsLifeVo.setSeqList(seqList);
        
        for (int i = 0; i < 5; i++) {
//            AccountsLifeContainerVo accountsLifeContainerVo = new AccountsLifeContainerVo();
//            accountsLifeContainerVo.setSeq(i+1);
//            accountsLifeContainerVo.setText("描述"+i);
//            containerList.add(accountsLifeContainerVo);
            
            AccountsLifeVo accountsLifeVo = new AccountsLifeVo();
            accountsLifeVo.setPhotoUrl("图片地址"+i);
            accountsLifeVo.setRemark("图片描述"+i);
            accountsLifeVo.setSeq(i+1);
            accountsLifeVo.setUpdateStatus(i%2);
            accountsLifeVo.setContainerText("容器描述"+i);
            if(i%2 == 0) {
                imgList.addFirst(accountsLifeVo);
            }else {
                imgList.addLast(accountsLifeVo);
            }
        }
        
        System.out.println(JsonUtils.objectToString(imgList));
    }
}


