package cn.taqu.account.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountsGrayListTest {

    @Autowired
    private AccountsGrayListService accountsGrayListService;

    @Test
    public void testIsInGrayList(){
        boolean result = accountsGrayListService.isInGrayList("","h97wv4naznp");
        System.out.println(result);
    }
}
