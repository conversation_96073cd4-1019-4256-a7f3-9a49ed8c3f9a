package cn.taqu.account.service;


import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RegionServiceTest {

    @Autowired
    private RegionService regionService;

    @Test
    public void testGetInfoByCityName(){
        Map<String,Object> cityMap = regionService.getInfoByCityName("厦门市");
        System.out.println(cityMap);
    }

    @Test
    public void correctBaseaddr() throws Exception{
        try{
            String succMsg = "处理成功";
            String s1 = regionService.correctBaseaddr("42,43");
            String correct1 = "42,43";
            String s2 = regionService.correctBaseaddr("43,46");
            String correct2 = "42,43";
            String s3 = regionService.correctBaseaddr("42,52");
            String correct3 = "42,43";
            String s4 = regionService.correctBaseaddr("43,52");
            String correct4 = "42,43";
            String s5 = regionService.correctBaseaddr("43,43");
            String correct5 = "42,43";

            System.out.println(s1.equals(correct1)? succMsg: s1 + "!=" + correct1);
            System.out.println(s2.equals(correct2)? succMsg: s2 + "!=" + correct2);
            System.out.println(s3.equals(correct3)? succMsg: s3 + "!=" + correct3);
            System.out.println(s4.equals(correct4)? succMsg: s4 + "!=" + correct4);
            System.out.println(s5.equals(correct5)? succMsg: s5 + "!=" + correct5);
        }catch (Exception e){
            System.err.println(e.getMessage());
        }

        try {
            String s6 = regionService.correctBaseaddr("43");
            System.out.println(s6);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
