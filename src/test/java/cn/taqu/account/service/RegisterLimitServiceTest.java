package cn.taqu.account.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * 注册限制 测试
 * <AUTHOR>
 * @date 2021/7/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RegisterLimitServiceTest {

    @Autowired
    private RegisterLimitService registerLimitService;

    @Test
    public void getRegisterLimitInfo(){
        Integer registerLimitTime = registerLimitService.getRegisterLimitTime();
        System.out.println(JSON.toJSONString(registerLimitTime));
        System.out.println(RegisterLimitService.REGISTER_NOT_LIMIT_TIME.equals(registerLimitTime));
    }

}
