package cn.taqu.account.service;


import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountsInfoServiceTest {

    @Autowired
    private AccountsInfoService accountsInfoService;

    @Autowired
    PrivacyService privacyService;

    @Test
    public void getSoulFitInfo(){
        Map<String,Object> result =accountsInfoService.getSoulFitInfo("a9b8q7rxbfh","a9mlfboci9k");
        System.out.println(result);
    }

    @Test
    public void testMgetAccountInfoByUserNew(){
        Map<String, Map<String, Object>> map = accountsInfoService.mgetAccountInfoByUserNew(null,new String[]{"aheo69a21779"},0,0);
        System.out.println(map);
    }

    @Test
    public void getUserPrivacyStatus(){
        privacyService.getAccountPrivacyStatus("fewyc2zf0ck");
//        SoaService.getAccountPrivacyStatusFromPrivacy("fewyc2zf0ck");
    }

    @Test
    public void testGeInfoByUuid(){
        Map<String, Map<String, Object>> map = accountsInfoService.getInfoByUuid(new String[]{"ndptnwqq8w6","cedpdni2ef9m"},new String[]{"mobile","account_name"},"0",false,false);
        System.out.println(map);
    }

    @Test
    public void testGetUserPushConfig(){
        SoaService.getUserPushConfig(Lists.newArrayList("cglwu601109d"));
    }

    @Test
    public void testGetFamilyAccount(){
        String accountUuid = "cd7lnvg4m8np";
        String accountFamily = SoaService.getAccountFamily(accountUuid);
        System.out.println("账号uuid"+accountUuid+"对应的家族uuid是:"+accountFamily);
    }
}
