package cn.taqu.account.service;


import cn.taqu.account.dao.AliyunLiveFaceDetectDao;
import cn.taqu.account.model.AliyunFacePhotoCompareLog;
import cn.taqu.account.model.AliyunLiveFaceDetect;
import cn.taqu.account.utils.TimeFormatUtil;
import cn.taqu.core.utils.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AliyunLiveFaceDetectServiceTest {

    @Autowired
    private AliyunLiveFaceDetectService aliyunLiveFaceDetectService;

    @Autowired
    private AliyunFacePhotoCompareLogService aliyunFacePhotoCompareLogService;

    @Autowired
    private AliyunLiveFaceDetectDao aliyunLiveFaceDetectDao;

    @Autowired
    private AccountsInfoService accountsInfoService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private AccountsPhotoService accountsPhotoService;

    @Autowired
    private StringRedisTemplate accountStringRedisTemplate;

//    @Test
//    public void testGetVerifyToken(){
//        Map<String,Object> map = aliyunLiveFaceDetectService.getVerifyToken("affq0ghy62ef","https:/avatar01.jiaoliuqu.com/taqu_ios_0_4289772428_1583114665613.jpg");
//        System.out.println(map);
//    }

//    @Test
//    public void testGetVerifyResponse(){
//        aliyunLiveFaceDetectService.getLiveFaceResponse("justin123",null);
//    }

    @Test
    public void testMerge(){
        AliyunFacePhotoCompareLog log = new AliyunFacePhotoCompareLog();
        log.setSimilarity_score(99.0f);
        log.setVerify_photo_url("123.jpg");
        log.setBase_photo_url("234.jpg");
        log.setAccount_uuid("1212");
        log.setStatus(1);
        log.setAccounts_photo_id(1L);
        log.setCancel_update_time(DateUtil.currentTimeSeconds());
        log.setError_msg("没有头像");
        aliyunFacePhotoCompareLogService.merge(log);
    }

    @Test
    public void testDaoMerge(){
        AliyunLiveFaceDetect detect = new AliyunLiveFaceDetect();
        detect.setBase_photo_url("321.jpa");
        detect.setAccount_uuid("4ads4f");
        detect = aliyunLiveFaceDetectDao.merge(detect);
        System.out.println(detect);
    }

    @Test
    public void testGetVerifyPhotoByAccountUuid(){
        Object photos = aliyunLiveFaceDetectService.getPhotoInfoByAccountUuid("ilaa5qqhsba");
        System.out.println(JSON.toJSON(photos));
    }

    @Test
    public void getImgList(){
        Map<String, List<Map<String, String>>> accountCoversMap = accountsInfoService.mgetAccountsCover(new String []{"ilaa5qqhsba"}, new boolean[]{false});
        System.out.println();
    }

    @Test
    public void sendSystemMsg(){
        String tips = "恭喜你，你的语音签名审核通过了，快去看看～";
        Map<String, Object> content = Maps.newHashMap();
        content.put("content", tips);
        content.put("relaction", "m=personal&a=homepage&isMyself=1");
        content.put("is_local_push", "1");
        messageService.systemNotice("bdcxsghfl6l6", content, "", "text", 1);
    }

    @Test
    public void sendSystemMsg2(){
        JSONArray content = new JSONArray();

        String info = "检测到您的相册中存在重复照片，系统已将其隐藏，仅您自己可见。%s";
        JSONArray contentReplaces = new JSONArray();
        JSONObject contentReplace = new JSONObject();
        contentReplace.put("w", "点击重新上传");
        contentReplace.put("r", "m=mine&a=album");
        contentReplace.put("c", "#0000FF");
        contentReplaces.add(contentReplace);

        JSONObject contentLevel2 = new JSONObject();
        contentLevel2.put("content_replace", contentReplaces);
        contentLevel2.put("content", info);
        contentLevel2.put("describe", "");

        JSONObject contentMap = new JSONObject();
        contentMap.put("content", contentLevel2);
        contentMap.put("relaction", "");
        contentMap.put("is_local_push", "1");
        content.add(contentMap);
        messageService.systemNotice("die8ltmzr5fw", content, "系统消息", "system_link_text", 1);

    }

    @Test
    public void sendMsg(){
        Map<String, Object> map = Maps.newHashMap();
        map.put("title", "萌新生存攻略，一分钟就能玩转社区");
        map.put("relaction", "m=forum&a=detail&id=bi5uof99w5qt&cid=20&gender_type=0");
        map.put("pic_url", "https://forumimg01.jiaoliuqu.com/index/f63da850959f514e69bdca871d23f741.jpg");
        map.put("content", "");
        messageService.systemNotice("bi5uof99w5qt", Arrays.asList(map), "", "text", 1);
    }

    @Test
    public void testGetPhot(){
        Long count = accountsPhotoService.getCountByVerifyStatus("t0tyuyy2t8s",1);
        System.out.println(count);
    }

    @Test
    public void testRedis(){
        String aliyunFaceVerifyLimitKey = "aliyunFaceDetectLockKey_justin";
        Integer limit = 0;
        String limitStr = accountStringRedisTemplate.opsForValue().get(aliyunFaceVerifyLimitKey);
        if (StringUtils.isNotBlank(limitStr)) {
            limit = Integer.parseInt(limitStr);
        }
        Long t = TimeFormatUtil.getTodayRemainSec();
        accountStringRedisTemplate.opsForValue().set(aliyunFaceVerifyLimitKey, String.valueOf(limit + 1), TimeFormatUtil.getTodayRemainSec(), TimeUnit.SECONDS);
    }

    @Test
    public void testUpateAccountsPhoto(){
        accountsPhotoService.updateVerifyStatusById(35L,1,"");
    }
}
