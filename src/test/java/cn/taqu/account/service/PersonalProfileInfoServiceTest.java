package cn.taqu.account.service;


import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import cn.taqu.account.model.PersonalProfileInfo;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class PersonalProfileInfoServiceTest {

    @Autowired
    private PersonalProfileInfoService personalProfileInfoService;

//    @Test
//    public void testGetPage(){
//        CommonSearch search = new CommonSearch();
//        search.setPage_num(1);
//        search.setPage_size(5);
//        search.setAccount_name("");
//        CommonPage page = personalProfileInfoService.getPersonalProfileInfoByPage(search);
//        System.out.println(page);
//    }

    @Test
    public void testMerge(){
        PersonalProfileInfo personalProfileInfo = new PersonalProfileInfo();
        personalProfileInfo.setAccount_uuid("pijuez4w54k");
        personalProfileInfo.setStatus(1);
        personalProfileInfoService.merge(personalProfileInfo);
    }
}
