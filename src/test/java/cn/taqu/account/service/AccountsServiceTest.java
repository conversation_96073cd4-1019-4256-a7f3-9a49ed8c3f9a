package cn.taqu.account.service;


import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import cn.taqu.account.common.ActionModeEnum;
import cn.taqu.account.dto.RiskSafeCheckResponseDTO;
import cn.taqu.account.mq.RiskSafeCallbackConsumer;
import cn.taqu.core.utils.JsonUtils;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountsServiceTest {

    @Autowired
    private AccountsService accountsService;

    @Autowired
    private AccountsPhotoService accountsPhotoService;
    @Autowired
    private RiskSafeCallbackConsumer riskSafeCallbackConsumer;

    @Test
    public void testAudit() {
        String message = "{\"appCode\":1,\"cloned\":1,\"operator\":\"admin\",\"resSource\":2,\"safeId\":\"1_1_1_1_7\",\"bizId\":\"236528\",\"senderUuid\":\"bghhiajhjabjhcbf\",\"requestId\":\"eba78956-52cf-4383-ad10-91fc27b547c4\",\"hitTypeCode\":\"block\",\"extra\":{\"reCheck\":0,\"bizSource\":\"\",\"isOldData\":0,\"reCheckBlock\":0}}";
        RiskSafeCheckResponseDTO responseDTO = JsonUtils.stringToObject(message, RiskSafeCheckResponseDTO.class);
        riskSafeCallbackConsumer.accountsPhotoAudit(responseDTO, 1);
    }

    @Test
    public void testRegisterLimit(){
        accountsService.registerLimit(ActionModeEnum.TAQU.value(),"************","opxK3s9t1PYz9UoQIpQzFndvW8KA");
    }

    @Test
    public void testUuidLoginLimt(){
        boolean result = accountsService.uuidLoginLimt("chonb081va6",1,1);
        System.out.println(result);
    }

}
