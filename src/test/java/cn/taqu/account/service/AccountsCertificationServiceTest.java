package cn.taqu.account.service;


import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountsCertificationServiceTest {

    @Autowired
    private AccountsCertificationService accountsCertificationService;

    @Test
    public void testAlipayInitCert(){
//        accountsCertificationService.alipayInitCert("370382198311228328","370382198311228328",1,1,2,50L,"certification");
    }

    @Test
    public void testAlipayQueryCert() throws Exception{
//        accountsCertificationService.alipayQueryCert("8b16cad650f7b4e6965a3af1463b874f");
    }
}
