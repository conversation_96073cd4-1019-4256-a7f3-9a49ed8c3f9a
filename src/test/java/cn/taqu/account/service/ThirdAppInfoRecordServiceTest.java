package cn.taqu.account.service;


import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ThirdAppInfoRecordServiceTest {

    @Autowired
    private ThirdAppInfoRecordService appInfoRecordService;

    @Test
    public void testAddOrUpdate(){
        appInfoRecordService.addOrUpdate("asdfassdfasdfadfaasdfasdfasdfasd","测试一下看看是什么样子的的的的的的测试一下看看是什么样子的的的的的的测试一下看看是什么样子的的的的的的测试一下看看是什么样子的的的的的的测试一下看看是什么样子的的的的的的测试一下看看是什么样子的的的的的的测试一下看看是什么样子的的的的的的");
    }
}
