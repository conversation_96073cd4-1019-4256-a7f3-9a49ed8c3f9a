package cn.taqu.account.service;

import java.io.File;
import java.io.FileReader;
import java.io.InputStream;
import java.security.PrivateKey;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;

import cn.taqu.account.bo.AppleIdTokenPayload;
import cn.taqu.account.bo.AppleTokenResponse;
import cn.taqu.account.common.ClonedEnum;
import cn.taqu.account.constant.CodeStatus;
import cn.taqu.account.model.AccountsThirdParty;
import cn.taqu.account.vo.AppleLoginVo;
import cn.taqu.account.vo.LoginVo;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.protocol.SoaBaseParams;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountsThirdPartyServiceTest {

    @Autowired
    private AccountsThirdPartyService accountsThirdPartyService;

//    {
//        "appleAudience":"http://************",
//        "appleAuthUrl":"http://************/auth/token",
//        "appleKeyId":"L54TMSP6UK",
//        "appleTeamId":"69YD8JX86J",
//        "appleClientId":"cn.ishengsheng.xingjiabi",
//        "appleAuthUrl_bak1":"http://************/auth/token",
//        "appleAuthUrl_bak2":"https://appleid.apple.com/auth/token"
//    }

//  "appleClientId":"cn.ishengsheng.xingjiabi",
//  "appleAuthUrl":"https://appleid.apple.com/auth/token"

    static String authorizationCode ="c60f89132fa3a49a48f667a77e2bd4991.0.ruxz.pi8-2XeJXm2_yZBVl5aW7g";

    /**
     * 配配app的苹果登录基本信息（新配配）
     */
    private static PrivateKey newPeipeiApplePrivateKey = getApplePrivateKey("AuthKey_98NCTXJJFY.p8");
    private static String NEW_PEIPEI_APPLE_KEY_ID = "98NCTXJJFY";
    private static String NEW_PEIPEI_APPLE_TEAM_ID = "AYM9AWPHQ6";
    private static String NEW_PEIPEI_CLIENT_ID = "com.taoseji.pc";

    public static void main(String[] args) {
        AppleIdTokenPayload appleIdTokenPayload = null;
        try {

            String appleClientId  = NEW_PEIPEI_CLIENT_ID;

            String token = generateAppleJWT();

            HttpResponse<String> response = Unirest.post("https://appleid.apple.com/auth/token")
                    .header(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded")
                    .header(HttpHeaders.USER_AGENT,"app")
                    .field("client_id", appleClientId)
                    .field("client_secret", token)
                    .field("grant_type", "authorization_code")
                    .field("code", authorizationCode)
                    .asString();

            AppleTokenResponse tokenResponse = new Gson().fromJson(response.getBody(),AppleTokenResponse.class);
            String idToken = tokenResponse.getId_token();
            if(StringUtils.isEmpty(idToken) || idToken.split("\\.").length <= 1){
                System.out.println( JSON.toJSON(response));
                throw new ServiceException(CodeStatus.APPLE_UNAUTHORIZED);
            }

            String payload = idToken.split("\\.")[1];//0 is header we ignore it for now
            String decoded = new String(Decoders.BASE64.decode(payload));

            AppleIdTokenPayload idTokenPayload = new Gson().fromJson(decoded,AppleIdTokenPayload.class);
            idTokenPayload.setAccess_token(tokenResponse.getAccess_token());

        }catch (ServiceException e) {
            throw e;
        }catch (Exception e){
            throw new ServiceException(CodeStatus.APPLE_UNAUTHORIZED);
        }

        String sub = appleIdTokenPayload.getSub();

    }



    private static PrivateKey getApplePrivateKey(String keyName){
        try {
         // String path = new ClassPathResource("AuthKey_279SCN3AMY.p8").getFile().getAbsolutePath();
            ClassPathResource resource = new ClassPathResource(keyName);
            InputStream inputStream = resource.getInputStream();
            File somethingFile = File.createTempFile("appleAuth", ".txt");
            FileUtils.copyInputStreamToFile(inputStream,somethingFile);
            final PEMParser pemParser = new PEMParser(new FileReader(somethingFile));
            final JcaPEMKeyConverter converter = new JcaPEMKeyConverter();
            final PrivateKeyInfo object = (PrivateKeyInfo) pemParser.readObject();
            final PrivateKey pKey = converter.getPrivateKey(object);
            return pKey;
        } catch (Exception e) {
           System.out.println("初始化苹果Key异常");
        }
        return null;
    }


    private static String generateAppleJWT() {
        String appleKeyId;
        String appleTeamId;
        String appleClientId;
        PrivateKey privateKey;

        // 新配配处理
        appleKeyId = NEW_PEIPEI_APPLE_KEY_ID;
        appleTeamId = NEW_PEIPEI_APPLE_TEAM_ID;
        appleClientId = NEW_PEIPEI_CLIENT_ID ;
        privateKey = newPeipeiApplePrivateKey;

        String token = Jwts.builder()
                .setHeaderParam(JwsHeader.ALGORITHM,"ES256")
                .setHeaderParam(JwsHeader.KEY_ID, appleKeyId)
                .setIssuer(appleTeamId)
                .setAudience("https://appleid.apple.com")
                .setSubject(appleClientId)
                .setExpiration(new Date(System.currentTimeMillis() + (1000 * 60 * 60 * 24 * 2)))
                .setIssuedAt(new Date(System.currentTimeMillis() - (1000 * 60 * 60 * 24 * 2)))
                .signWith(SignatureAlgorithm.ES256,privateKey)
                .compact();

        return token;
    }
}
