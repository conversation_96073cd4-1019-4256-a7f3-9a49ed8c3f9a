package cn.taqu.account.service;


import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import cn.taqu.core.utils.JsonUtils;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MobilePlaceServiceServiceTest {

    @Autowired
    private MobilePlaceService mobilePlaceService;

    @Test
    public void getPlace(){
        Map<String, String> map1 = mobilePlaceService.getPlace("***********");
        System.out.println(JsonUtils.objectToString(map1));
    }
}
