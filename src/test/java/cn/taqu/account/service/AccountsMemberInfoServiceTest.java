package cn.taqu.account.service;


import cn.taqu.account.model.AccountsMemberInfo;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountsMemberInfoServiceTest {

    @Autowired
    private AccountsMemberInfoService accountsMemberInfoService;

}
