package cn.taqu.account.service;


import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AliyunFacePhotoCompareLogServiceTest {

    @Autowired
    private AliyunFacePhotoCompareLogService aliyunFacePhotoCompareLogService;

    @Test
    public void testgetAliyunFaceLogByPage(){
        /*AliyunFacePhotoCompareLogSearch search = new AliyunFacePhotoCompareLogSearch();
        search.setPage_num(1);
        search.setPage_size(10);
        search.setAccount_uuid("123456");
        search.setAccount_name("**35");
        search.setStart_time(**********L);*/
//        String paramStr ="{\"start_time\":**********,\"account_uuid\":\"\",\"account_name\":\"\",\"end_time\":\"\",\"page_num\":1,\"verify_status\":\"\",\"page_size\":10}";
//        AliyunFacePhotoCompareLogSearch search = JsonUtils.stringToObject(paramStr,AliyunFacePhotoCompareLogSearch.class);
//        CommonPage page = aliyunFacePhotoCompareLogService.getAliyunFaceLogByPage(search);
//        System.out.println(page);
    }


}
