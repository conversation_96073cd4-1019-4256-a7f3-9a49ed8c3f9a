package cn.taqu.account.qiniu;

import org.junit.Test;

import com.qiniu.common.QiniuException;
import com.qiniu.http.Client;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.model.FetchRet;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;



public class QiniuTest {
	
	String qiniuUrl = "http://ai.qiniuapi.com/v3/image/censor";
//    String qiniu_access_key = "N9v6m6QoLvNJR0NUieJC-l7KdCI7YKroIzKelwdW";
//    String qiniu_secret_key = "R4htDnXTIaWNEfJQQYKDiqwszlVRpKIHbuIptZJe";
    String qiniu_access_key = "JtExk-61vy9WR7r9MwcFsbJI6kl2mVj808Ta7MZI";
    String qiniu_secret_key = "fgoBLAG6ebLyB4zwoePOKu0-cMUNJ6rIErFWLLC_";
    private static Client QINIU_CLIENT = new Client();
    
//	public static void main(String[] args) {
//		QiniuTest qiniuTest = new QiniuTest();
//		
//		qiniuTest.a();
//	}
    @Test
    public void fetch() {
    	
    	//构造一个带指定 Region 对象的配置类
    	Configuration cfg = new Configuration();
    	//...其他参数参考类注释
    	String bucket = "qiuyuhua1";
    	String key = "test1.jpg";
    	String remoteSrcUrl = "http://devtools.qiniu.com/qiniu.png";
		Auth auth =  Auth.create(qiniu_access_key, qiniu_secret_key);
    	BucketManager bucketManager = new BucketManager(auth, cfg);
    	//抓取网络资源到空间
    	try {
    	    FetchRet fetchRet = bucketManager.fetch(remoteSrcUrl, bucket, key);
    	    System.out.println(fetchRet.hash);
    	    System.out.println(fetchRet.key);
    	    System.out.println(fetchRet.mimeType);
    	    System.out.println(fetchRet.fsize);
    	} catch (QiniuException ex) {
    	    System.err.println(ex.response.toString());
    	}
    }
	
	public void a() {
		Auth auth =  Auth.create(qiniu_access_key, qiniu_secret_key);
	    
//		StringMap data = new StringMap();
//        data.put("uri", imageUrl);
//        StringMap map = new StringMap();
//        map.put("data", data.map());
//        String postBody = JsonUtils.objectToString(map.map());
		String pic = "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1563108301265&di=f51f749fa70957dc7857c42d6cca9b69&imgtype=0&src=http%3A%2F%2Fi8.hexun.com%2F2018-10-18%2F194919525.jpg";
        String aString = "{\n" + 
        		"            \"data\": {\n" + 
        		"                \"uri\": \""+pic+"\"\n" + 
        		"            },\n" + 
        		"            \"params\": {\n" + 
        		"                \"scenes\": [\n" + 
        		"                    \"pulp\",\n" + 
        		"                    \"terror\",\n" + 
        		"                    \"politician\",\n" + 
        		"                    \"ads\"\n" + 
        		"                ]\n" + 
        		"            }\n" + 
        		"        }";
        
		
		String accessToken = String.format("Qiniu %s", auth.signRequestV2(qiniuUrl, "POST", aString.getBytes(), Client.JsonMime));
        StringMap headers = new StringMap();
        headers.put("Authorization", accessToken);

        // 发起鉴黄请求
        try {
            Response resp = QINIU_CLIENT.post(qiniuUrl, aString.getBytes(), headers, Client.JsonMime);

            System.out.println(resp);
            System.out.println(resp.bodyString());
            
        } catch (QiniuException qe) {
           System.out.println(11);
        } catch (Exception e) {
        	System.out.println(22);
        }
	}
}
