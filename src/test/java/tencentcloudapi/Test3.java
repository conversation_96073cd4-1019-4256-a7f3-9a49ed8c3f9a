package tencentcloudapi;

import java.util.List;

import com.google.common.collect.Lists;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tiia.v20190529.TiiaClient;
import com.tencentcloudapi.tiia.v20190529.models.AssessQualityRequest;
import com.tencentcloudapi.tiia.v20190529.models.AssessQualityResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-08 17:50
 */
public class Test3 {


    // secretid
    private static String TENGXUNYUN_SECRETID = "AKIDAD2cy5yc2DAsKWTAblfKkvfEYdT3OTIr";
    // secretkey
    private static String TENGXUNYUN_SECRETKEY = "MSIrBG4cuwkJdiZAWdV8WfYCbmiOp1yi";
    public static TiiaClient tiiaclient;
    
    static List<String> photos = Lists.newArrayList();
    static {
        photos.add("https://avatar01.jiaoliuqu.com/cn.ishengsheng.xingjiabi_iOS_avatar_115_1645429728348_8448_0.jpg");
        photos.add("https://avatar01.jiaoliuqu.com/cn.ishengsheng.xingjiabi_iOS_avatar_114_1645588759024_9925_0.jpg");
        photos.add("https://avatar01.jiaoliuqu.com/cn.ishengsheng.xingjiabi_iOS_avatar_114_1645588756725_48888_0.jpg");
        photos.add("https://avatar01.jiaoliuqu.com/taqu_android_avatar_101_1616220710334_1_0_11532.JPEG");
        photos.add("https://avatar01.jiaoliuqu.com/cn.ishengsheng.xingjiabi_iOS_avatar_101_1645588746636_54236_0.jpg");
        photos.add("https://avatar01.jiaoliuqu.com/taqu_android_avatar_101_1645588760501_1_0_25131.png");
    }

    public static void main(String[] args) throws Exception {
        Credential cred = new Credential(TENGXUNYUN_SECRETID, TENGXUNYUN_SECRETKEY);

        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("tiia.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        tiiaclient = new TiiaClient(cred, "ap-guangzhou", clientProfile);
        
        for (int i = 0; i < 5; i++) {
            Long start = System.currentTimeMillis();
            
            // 实例化一个请求对象,每个接口都会对应一个request对象
            AssessQualityRequest req = new AssessQualityRequest();
//            req.setImageUrl("https://avatar01.jiaoliuqu.com/taqu_ios_1638956259818_683566752_0.jpg");
            req.setImageUrl(photos.get(i));
            // 返回的resp是一个AssessQualityResponse的实例，与请求对象对应
            AssessQualityResponse resp = null;
            resp = tiiaclient.AssessQuality(req);
            // 输出json格式的字符串回包
            System.out.println(AssessQualityResponse.toJsonString(resp));
            
            Long end = System.currentTimeMillis();
            
            System.out.println(end - start);
        }
        
        

    }

}
