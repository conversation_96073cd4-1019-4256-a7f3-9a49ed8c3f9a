package tencentcloudapi;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import org.apache.commons.collections.MapUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.GetFaceIdTokenRequest;
import com.tencentcloudapi.faceid.v20180301.models.GetFaceIdTokenResponse;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import com.tencentcloudapi.iai.v20200303.models.CompareFaceRequest;
import com.tencentcloudapi.iai.v20200303.models.CompareFaceResponse;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceAttributesRequest;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceAttributesResponse;

import cn.taqu.account.client.TengxunyunApiClient;
import cn.taqu.account.client.api.tengxunyun.TengxunyunApi;
import cn.taqu.core.utils.JsonUtils;

/**
 * 腾讯云测试
 * 
 * <AUTHOR>
 * @date 2021/05/27
 */
public class Test1 {

    // 业务流程唯一标识，即 wbappid，可参考 获取 WBappid 指引在人脸核身控制台内申请
//    private static String appid = "IDAuMtBF";
    // wbappid 对应的密钥，申请 wbappid 时得到，可参考 获取 WBappid 指引在人脸核身控制台内申请
//    private static String secret = "aDTjZkWFXfr013jyn0jicjoeXetRwxcRR9euYq1Hjy7dz0UQi0DRk6Sc2ZPQK5Dk";
    // 业务流程唯一标识，即 wbappid，可参考 获取 WBappid 指引在人脸核身控制台内申请
//    private static String appid2 = "IDAqbyri";
    // wbappid 对应的密钥，申请 wbappid 时得到，可参考 获取 WBappid 指引在人脸核身控制台内申请
//    private static String secret2 = "PjMvJ1dz1dwh0gtkm1LJmNop029vnGQh6PsaJFvsXyEqVKU8paS8TqR470scl9Ut";
    // 业务流程唯一标识，即 wbappid，可参考 获取 WBappid 指引在人脸核身控制台内申请
    private static String appid = "TIDAiTLj";
    // wbappid 对应的密钥，申请 wbappid 时得到，可参考 获取 WBappid 指引在人脸核身控制台内申请
    private static String secret = "rXmpymTi304dvepYofe2H6Fv1Nph7jnVjqyTJZdZezxUMr4YKCGHLXPEzQ4ttULU";
    // 授权类型，默认值为：client_credential（必须小写）
    private static String grantType = "client_credential";
    // 版本号，默认值为：1.0.0
    private static String version = "1.0.0";

    private static TengxunyunApi tengxunyunApi;

    // 临时token
    private static String accessToken;
    //
    private static String apiTicketNonce;
    //
    private static String apiTicketSign;
    //
    private static String faceid;
    //
    private static String nonce;
    //
    private static String sign;
    //
    private static String nonce2;
    //
    private static String sign2;
    //
    private static String serverSync;
    //
    private static String serverGetLiveResult;
    //
    private static String userid = "oxei9n6bdn9";
    //
    private static String orderNo = "c24ec5962a6c4213adbef7e27cb381af";

    public static void main(String[] args) {
        Test1 test1 = new Test1();
        init();
        
        test1.getBase(test1);
        // userid,noce,sign,orderno
        System.out.println("userid = " + userid);
        System.out.println("nonce = " + nonce);
        System.out.println("sign = " + sign);
        System.out.println("orderno = " + orderNo);
        System.out.println("faceid = " + faceid);
        
//        test1.getCheck1(test1);
//        test1.getCheck2(test1);
//        test1.getCheck3(test1);
//        
//        test1.clientProfile();
//        test1.aaaa();
        
        
    }
    
    private void getBase(Test1 test1) {
        
        test1.getAccessToken();
        System.out.println("****************** accessToken=" + accessToken);
        test1.getApiTicketNonce();
        System.out.println("****************** apiTicketNonce=" + apiTicketNonce);
        
        nonce = generateBizId();
        orderNo = generateBizId();
        List<String> values = Lists.newArrayList();
        values.add("1.0.0");
        values.add(appid);
//        values.add(apiTicketNonce);
        values.add(nonce);
        values.add(userid);
        sign = sign(values, apiTicketNonce);
        
        test1.getfaceid();
//        test1.getfaceid2();
        System.out.println("****************** faceid=" + faceid);
        
    }
    
    private void getCheck1(Test1 test1) {
        test1.getAccessToken();
        System.out.println("****************** accessToken=" + accessToken);
        test1.getApiTicketSign();
        System.out.println("****************** apiTicketSign=" + apiTicketSign);
        test1.serverSync();
        System.out.println("****************** serverSync=" + serverSync);
        
    }
    
    private void getCheck2(Test1 test1) {
        test1.getAccessToken();
        System.out.println("****************** accessToken=" + accessToken);
        test1.getApiTicketSign();
        System.out.println("****************** apiTicketSign=" + apiTicketSign);
        test1.serverGetLiveResult();
        System.out.println("****************** serverGetLiveResult=" + serverGetLiveResult);
        
    }
    
    private void getCheck3(Test1 test1) {
        test1.getAccessToken();
        System.out.println("****************** accessToken=" + accessToken);
        test1.getApiTicketSign();
        System.out.println("****************** apiTicketSign=" + apiTicketSign);
        test1.serverGetLiveResult();
        System.out.println("****************** serverGetLiveResult=" + serverGetLiveResult);
        
    }
    

    public static void init() {
        TengxunyunApiClient tengxunyunApiClient = new TengxunyunApiClient("https://idasc.webank.com");
        tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
    }

    public void m1() {
        try {

            // Credential cred = new Credential("SecretId", "SecretKey");
            Credential cred =
                new Credential("AKIDUlmW0D9oEMDn69vRgfNoytXu0pOghRv8", "yf6G9k48vRgfNoysVXIm0xc08zZsvhCQ");

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("faceid.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            FaceidClient client = new FaceidClient(cred, "", clientProfile);

            GetFaceIdTokenRequest req = new GetFaceIdTokenRequest();

            GetFaceIdTokenResponse resp = client.GetFaceIdToken(req);

            System.out.println(GetFaceIdTokenResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
    }

    public void getAccessToken() {
        String oauth2AccessToken = tengxunyunApi.oauth2AccessToken(appid, secret);
        System.out.println(oauth2AccessToken);
        Map<String, Object> map =
            JsonUtils.stringToObject(oauth2AccessToken, new TypeReference<Map<String, Object>>() {});
        accessToken = MapUtils.getString(map, "access_token");
    }

    public void getApiTicketNonce() {
        String oauth2ApiTicket = tengxunyunApi.oauth2ApiTicketNonce(appid, accessToken, userid);
        System.out.println(oauth2ApiTicket);
        Map<String, Object> map =
            JsonUtils.stringToObject(oauth2ApiTicket, new TypeReference<Map<String, Object>>() {});
        List<Map<String, Object>> object = (List<Map<String, Object>>)MapUtils.getObject(map, "tickets");
        apiTicketNonce = (String)object.get(0).get("value");
    }
    
    public void getApiTicketSign() {
        String oauth2ApiTicket = tengxunyunApi.oauth2ApiTicketSign(appid, accessToken);
        System.out.println(oauth2ApiTicket);
        Map<String, Object> map =
            JsonUtils.stringToObject(oauth2ApiTicket, new TypeReference<Map<String, Object>>() {});
        List<Map<String, Object>> object = (List<Map<String, Object>>)MapUtils.getObject(map, "tickets");
        apiTicketSign = (String)object.get(0).get("value");
    }
    
    public void serverSync() {
        nonce2 = generateBizId();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderNo);
        values.add("1.0.0");
//        values.add(apiTicketSign);
        values.add(nonce2);
        sign2 = sign(values, apiTicketSign);
        
        serverSync = tengxunyunApi.serverSync(appid, "1.0.0", nonce2, orderNo, sign2, "2");
        Map<String, Object> map =
            JsonUtils.stringToObject(serverSync, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> object = (Map<String, Object>)MapUtils.getObject(map, "result");
        String bsae64 = (String)object.get("photo");
        System.out.println(bsae64);
    }
    
    public void serverGetLiveResult() {
        nonce2 = generateBizId();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderNo);
        values.add("1.0.0");
//        values.add(apiTicketSign);
        values.add(nonce2);
        sign2 = sign(values, apiTicketSign);
        
        serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce2, orderNo, sign2);
        Map<String, Object> map =
            JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> object = (Map<String, Object>)MapUtils.getObject(map, "result");
        String bsae64 = (String)object.get("photo");
        System.out.println(bsae64);
    }
    
    public void getfaceid() {
        
        
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("webankAppId", appid);
        params.put("orderNo", orderNo);
//        params.put("name", "许根水");
//        params.put("idNo", "362331199102083018");
//        params.put("name", "刘•");
        params.put("name", "邱宇骅2");
        params.put("idNo", "350721199012101337");
        params.put("userId", userid);
        params.put("sourcePhotoStr", "");
        params.put("sourcePhotoType", 2);
        params.put("version", version);
        params.put("sign", sign);
        
//        faceid = tengxunyunApi.serverGetfaceid(appid, generateBizId(), "邱宇骅", "350721199012101337", userid, 2, "1.0.0", sign );
        String faceidJson = doPostByJson("https://idasc.webank.com/api/server/getfaceid?orderNo="+orderNo, null, JsonUtils.objectToString(params));
        System.out.println(faceidJson);
        Map<String, Object> map =
            JsonUtils.stringToObject(faceidJson, new TypeReference<Map<String, Object>>() {});
        String code = MapUtils.getString(map, "code");
        if (Objects.equals(code, "0")) {
            Map<String, Object> object = (Map<String, Object>)MapUtils.getObject(map, "result");
            faceid = (String)object.get("faceId");
        }else {
            System.out.println("认证失败，code=" + code + ",msg=" + MapUtils.getString(map, "msg"));
        }
        
        
        
        
        //        String accessToken = client.getAccessToken();//http 请求
//        String sign = SignUtils.sign(data, ticket);

    }
    
//    String base64Img = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAQcBBwDASIAAhEBAxEB/8QAHQAAAQUBAQEBAAAAAAAAAAAAAwABAgQFBgcICf/EAEcQAAEDAwMCBAUCBAQEBQMCBwEAAhEDBCEFEjFBUQYTImEHMnGBkRShI0KxwRVS0fAIM2LhFiRDcvEmNDZTc4KSFzVjRKL/xAAbAQADAQEBAQEAAAAAAAAAAAAAAQIDBAUGB//EACoRAAICAgICAgMBAAMBAQEBAAABAhEDIRIxBEETUQUiYTIUQnEGgRUj/9oADAMBAAIRAxEAPwD3zaFCo3jCLCi8cZXltM7gBaZJUHcI6G9sDlFDoruaSeFAtwZHRWVBzeSimIrFgaCSgPb6vZXdqDVb6uEtl6KVWmIkZQHMxHHZaBaOyg+kHdUUwMpzIJEKJo44Wk6lE4JTeSfqmkwsy/InoUhRj+UrU8o9j+EvKPY/hGw5IzhSk4CkKJ7K/wCUe0JxSgdPwimO7KQpd0RlOCcdFabSyJhE2AcJUFlXy0XyQjBiI1kkCEqYNoBTpDKI2jDiUZlM5gQpBhwih2gYpiUvKCslke6W32T4sVorikAU+xqOG54UvLb3RxYWit5bUvLarPlt7peW3ulxC0Vw0DhLKs+UEvKajoZXEynAnhH8seydjQJ4KBWBDTxCkKYBRunCcNjJTGA2DsfwpCmD1BR4altCKJAeWAMkJeXjojhvdMRnCNgBdTESo+UeyMklYwPlY4Cby0clRTQJA/L7peWO6IeUkFUQFMKTWBOmMzgoE+iYEJoTnhRlJCqySSSbd7H8JAOkkDIwkgBJJJIASg8YBHCmovdGAmgYNM7hTIkzKi4Q2QVQqYNKE890i4SlSKIbSltKlKUo0Fkdp6p9g7KRIzCbd3EIAjtjhMeFPd2EqBQNCSSSQMbmQDlSAPVMMQptkpqhMcNMAwiU2ncZHRRa4yAitEH7J2Im0KQ4TKQQJk6U7kWEBpLTIUvMPsjQkx6k7QOSUzh6Wjqk8+ueyi4lxkoAZJJJAhJJJIASSSSAHBg4RN4903l+6Xl+6nQ9ksmCDhOk0Q0J4RTGNKUqW1Lb7o2FjQUoKmkin9isr1B6SqzuVbc3Byqrm5OVMi0CIlxUSI5Uokkgp4PdQ0xpkB8pRKQ6ptmecKbPSO6ai0J7ZNvKNTaQ3KHTEuVhqtdiIkYP0UHQOiI7goLspoTZF2Smg9lKEoTpBZFEY0kKMI1MQ1DYdjt5RGjuotGQpoQv4JJJJMQkkk45ygBNB6J9hUmiE6dCsSSSfhUIZJJJAClKUkktgJJJJMBJJJIASccpk45QC7GLwD3+iEeVKpyPooqKLYxIhRPKTsBMol2NCSUZKbd7hFDoKY2wEMhWHsAGAZQ3Ajla0YtgHDOAoVGnbwrDhmVE5GUUOyrtKRbhGc0yoEzHslYrAbSoPbLuYVh/RCfyl1sadgjTwcqLqXZGSJlFjorbfdLb7o5cAYTOc2I5TAFs9wls9wnSToVjeWe4T+V7pcJ9xRQ0/sbyz3SbTzyE4MKaVDWxgABCmAop5SKZJJMDKdBIcADhOPdDa8EgQiJlD4TJp7ESoucf5mmEgJqG9vdR3D/q/Kgk9isJju38KUhB3NTF56cJPYw8pYCr+Y5PuJ+ZJjDAzGcIsjuqzHjAyiBwJgFPokLI7pSO6CXhLePdFsKCOPYqMqJcOFA1Gg9T9EuxoLKYu7EKvJ7pt3dABS6CROPqm8wIUpEiE7GlQXzR2/dLzR2/dASTGH8wJF89UFqklYmF81N5g7IaYZSsEF8wdk7XCcmUJO0GRhNMQcPBS3NA5QthS2FOkBNz5EcKEpbD7JxTJE4+6OgoU+6ZxwM/un8s+2EmsnJQFEd3ufyol2Of3UzS/wBymNLp/dJ7GCLgUlI0o5/qn8sxiIQF0RkqJPuiFmOiWyeiAsEXCDlDLieSjGl1/ukKYImPyU7F2CbujEx7Jpd7o+2MIdR4ZO5wA7yAiwoiZHUppPcqvc39rQbvq16bGDkl0f1WLc+NNDoVHMN9SO08jIQn9DSfs6VhO+DKK37rCt/EmjVm7jqFDHQmD+eFOp4j0emJbfUXHsKkn+qKHRv0gcyDhFasG18RWFQjbc04J/zBbdtcMqDc0tcCP5TJSX9EFE9lMcJw5rv+6lsPsmJogkjQe4/CZzoMEIWxUCzOcpwCTARGAbYPJU4TphQEsIEymAJ4CPCSKYUCawn2UgwDBEqaSKHQ20DsltB6BIjKk044RxRPIaE4b3UoTgEpNIdkYCUeyntKYCTBTFYySlBCaE+IWMkpbT2S2lHEVg3DlVHj1HCvFpgquQZcO6mRaKpGUkR1N0oaSKEkknBEIAJS+ZHag0yAY6lG4CVaF6E75T9EFHSQnQmgCcZICMknyFQ20dk4ENwCnTgwqSoY4ABmMqbADyo8IrQIx1TWyGxoHZPA7JynaJV1aJshA7J4HZS2lINIKFELGg9ktpU04KdCsRbOP3TbI5yppKaLI7R2UXARgIh4UE1ElyoGnUnchMRykwsikkkgYkkkiYQAlBxxzn2SLhHVQJhS9jSHUSRHKUj3QycuKhsocnHq490NzoPzFKo4EBBcYKB9knHnP7qKiTlJD2OjWQ6xyESfYodXkcrUxYM8IZJjCIoRClggbnEFDPBRKgkiAoRj2SEwZMpiQER8RiENPsogknIzwm4OQkUCf8xUUYxOYQVSZLQkkiYTbgixDqQJPCG4z1UUmykg/q7Jersg7j/mP5S3H/Mfyixh0kJrhHP7p9w7/uk2WEBTyhzHU/lOHlKwoOAQZTlwGHIHmFLcTykwqg/mDuol4PJ/ZCUTEIth2ELs44TbioflKT7pbChyYS3Js+6ZMdEt3fhIHsmAnnhOABwkxEpTSUoUxT7kJCGDkpUhSxypimOyewoDBKYiDlWNsJFgPKQ9FdRLeys+WEiwDpyhAVtrkgD1RjTBPMJ/K+qEqADCbaenCP5XunFM4ynsNgA0k91IMMcI4YBwSpAQIQBXFN08KXlFHg9inARQAmsEZmVMNUw3uE4CdCsjEc4TQiASptGBgfhUkKwAZCltPQIwA7fslgYj9k6FyYCCmcMCFZgR0j6KDxxA/ZKirABveZUSwDMlH2nsonj/ALIoLsGG9eUxZnBTkk8fsnznCOIXZDYO5UTTJ6IhJaJcWho5kLOvNWt7Vji6oMCfZDodWWqhDGEuInKxNT1anY0X1a1VrQ3OXRjlcl4k8eUKDSWXFFgBPpEkmF5J4n8ZXGsmpSbULaJMHbgkQRz2ykk2Nfr2dtr/AMZLW0ruZZNFwQYkEgf1XJVfinrd/cuNEMa04aA0mPyuBqWdB7djmF854nHvCs2dhVa0C3qPouBwehW0carZlzbejT1nUte1io43d1U5+Wf6ALn6lKu5xpCpVDhyC6AVuhl6K7dtbzBwTGVKtpjrkhjnu3j1AgGZ7EhNRSZMrOWZeahbVXN/U1areziSPwtPT791ao0VJJxjg/ZXWaVcM3+dS3iMEHj7IP6SjQe0sqFlTkh3fsqpMi5I6/S3lzGlrjBGATwuw8PeIr+xqbG13Op8QTOPZedadXhn8Q7Q6QCDwe8qzR1qrQLqFYCZ9Lu6hw2axn9n0VoniNt+1jS0iptzn91uafcOuHPLnYmGge3MrwbQ/EZs69B+8lvyuG7ovVdG1y2ZZ06oqAtPA3Zz/dZSjTL7Oyo1RDi4gEdyhv1GgwnfUaIk8rh73xG1vngvaajn+hgPT6LnNU8UVLRzd435J2jslsej1b/FrWPS8OP/AE5Tt1WkSZBA7wvEnfEG5tq9R9GlQ2k7Qwgn7ieEah8TXmo1t5QDGddoICTbBJPo9wo3tCrhrxPUSrAOJER9V5HZ+NtCuXiatSnUJkkAz+Qul03xbasrNDbincWzhh2/IPuElPexyi/R3DBIUoQ7K4o3dIPoPaWkcAqztjBHC10zK37INbPKltCkBJgKTW47quNEtgw36pwPqiR7KQZnI/ZOkFtgVPb2RNv/AEj8JwI6fsloAIaZyn2hEIceRKUeyehA9oS2hFaB/wDITugdB+EaDZWcJwq7mSTEhW6gjgfhBf3491ElotFWqC0z1QXtgwD+VbcATkAqu8YMj9lDRdgUk5HCaEiglPJCODIPsqzZkdEUSAJQuiX0PvPdPvPsoJJ0ST3n2S3n2UEkUAam4umVJCYYmTCIHSJAKpFIkSYR6ZwgH2RafCaJkiZP4Ts5Umxt4lSAEcLRaRKSFCUJQEoCVhxQoTbfcp4CcIsKQkkkkhiTbfcpyoOOJBKaFQzxBCjKck//ACmJweOFLCkMoOJDk0lRJypuxpE959kxJPKjISkJDodMcpEjuhl0dVLsZI4gKD/lKjUdnB/dCc4xyVIxFx6qJ5+yiXdzCg8kmQSR7FA6JJShS7umkotjs3VB43cdFImQYUHEnqt7oxqyDhDiouTu5JUHHCTFTGTP+Upi4zhRc4hpJOFA6Ap28FRBBTFxDuVbY12Sc4bTByoOdu6QolRlSV2InKSUE5ykRIgpjBVfm+ygjFgPt9FA0zOCISFRBIqflHuEvKPcIoKBHHumRfKMj/VE8v8A3CWxlaCRwVMMIMo4ZhS2H3/CTTYWgIBUmsO0IzaZKKxsNAS40O7K7G+rjCJ5Y90cMT7QmkFoB5Q90vKCMWjoltRTFYDyx7peW1GLRKbaE6HoFsalsb2RdvuUtvuUuI7BtYJUi32lTAzypBsIoVkAwRwp7QpTiOiZNKiRoCUIjQCE5aAJTFYIyU0Imfb8BNCLHZCE8J04BPCAsjCQBlSIITICxQE7WTBjCZFYYYEIELyx7peWPdPJSBhVQrRDyz7JeWfZElKUqC0D2Edkth9kQNJyOE5YnxBtUDaIUw0kSpCmSFNrCMdE+LJsHsPcJwwdcouwpxTMZ/qimO0VyO3CW0np+VZ2QOExaimK0VnAgxChsnBiFb2CcjKr3NSnb031Kjg1jQSS48IGtgXBreoAHXsuR8TeNNO0Zj5rUzWb3yAuC+JHxX/TvdZaLDnElpqTPbj914Ve3l7ql0+pWq1ariZyYAylxs0ikj1TxF8Wrh9YttKvmkE5AO0ffquC1rxzrWqvcDVLGuEQ2QsP9K1h3VKjZ7J31KTSBTAcQPoFSgvZLl6EDdVnb61WQe+SrDRtb6QTnMjlAD6+0n0Um9BMklOHOIhxd3mR/ZWkTthi6oPTsAHeQol10agFKqxv2CkykHuA9Z+mSr9Kzpub6axaT/mZB/KpUuxUwVA6oKe03TCCcDCuUry+pDJMdwE4sXMZLQXnuCqlao62IO2oHE5gShNPoXXYS51+7a+K9MGP5wJ57hVa+tMuGNpVralUYDyGwQfshV7wVjJc0uiJiJ+qy69X+Nubl2flMJ1ZEmi227IqljCWsJ+UnHt90WrdebuY/wCcCQSeyxatZxkglz2jO7BTm582j5jfmaMopkNmza6k8UxumWmAf7rpbPxU6ha0WOqZbkCT0Xn5ql7gGA7unSB7o9JrnENBBcP2ScUylJo9CsfE7q1/VfUdtDv5gePb8Sr9fVW18hgeQMTlefW7QCHNqSeTHE+y16NQim0U6lQCM4hS4G0ZX2dFVqXFdwp+VSpNaZO2JjtKt0GUa9sad5TZuBO0zE/WPsuatq75EVA08SeVr0L+m1oFS4YCBMlZtWWpUWm0alB3mUqTHFuAwgkQlcXpdTJ8gUHR/wCkcSq79XpVs/qGiJHpJVF9/TbOypJ5EqXA0jJHY+EviDqmg1KYuajatrIDmkyQO/4X0D4Z8TaZr9mytZXNOoSJLActxwvju4um1ZNXafoi6Lq17o9yLrR7qpScDMB2D9uqFcSZKMuj7bAByI/KcEiZXzz4X+OVW1eyh4isy/gedTJOPdew+H/HGha/Ra+wvqZJ5DuQq532ZyizqACCFJBo1W1hLHtcP+kogB6YTZDbsieUkkkGYkkkkAO7lI8BPy5ICQgpdkH/AClAeJaUd3ylV6hIAhN/ZTAhDrfL90RAJLsE4UMtdIg9RTuMlRJysn2UkOi7tw4iEEk9FNpAGRlNaBokkoyY5wVGcobFQSAlAUNxUm8gJp2FMkBJwit+UBRp8OTjgKkSwrWmQJRgMKDPmRmNBBlUkwJM+UJOO0JwICapwq9gNvHZLeOyGkh6FYTcOyW4dkNJFhbCggmE8BBkg+yfzSlYwsyChT7BLcSOeVF2BhHKgEXR0UHGSmJlMXLNsaQiYUXHKT3RyobmkqW0UkOSluQvM9kxqE8BLQ9BTyoudDZUPM7jKgXGOUWA7nSRKG5wAKThMIbmHJCXQESRhRLiDgqL56oZOeVPZS6D7h3S3Duq/mNS8xqLHZ0UHsonghSLpEQorduzAEcHKi/hEePWoESE+0IEoOaSSQilvZLbiVKGVywxkYTimO5Rw0zlItzhALRWLRmDPtCiWHqIVlzRB4QU0rBshsS2BEd8x+qZOgtkNgS2KaUYlFBbIbEi0Dkqak1k8n9kUFsCOQiFwHRT8r3/AGS8v3R0O2RBBE7cJA5k8KewdCk1gBnlLQWxxBEhPHYJfRJIBJKQEmFLYEJWKxDLPdR2nspgQO6YE9QqoLZAgjlNAU3AkpBsgZS4hZDalAU9iWxFDsgGzwE4aeymGnlPDuyfEVkNpHRINPZF2nmEg0lFILItbHTKSn5ZjKkKZx/oq4omwUDslA7I3lnsEvLPYI4hYGB2CQHZG8s9gkKZHQIoLBJoHYKz5ft+ybYeyKKTXsrwOwUgcAI/l+37JCmMFCQm/oBB7KTZA4VgMB7fhI0x0MfZFCsDBOITinEoopwZlPt9z+E1FByBQeyJtAyCn2+5/CfYqoGyMHsnDT2UkkibEnj2Ug3rKdOibI7T2USDI98R7qWYEcrG8V+ItP8ADmkVb7Uawp02iQJy72hItIsaxqlnpFhWvNRrtoW1MS4u6/b/ALFfMPxM+KF14nrus9I30NNa6WkmC/tM5WD8QPHuo+Nb8urvdb6Yx00aDDgDue56riqtyC7y6DS1jeXnr9kqs0Wi9WrMaN1U73zwOv1QH3z6jyMtaRwz6LPqPE8mf6pUwXjdVOyiOxySqjG3QnMm4l7sbijMZ13Fg/KrVbsUiQ30s7FU31a9cksLgztwr4oycqNZ9a1pmalR7zPX/RFp6zb0vkpB0cYWGKOdw4HJJVmhQY7O6ftIRVC5v0bdPxAx7vSxtOforlv4jbbQKrHFpPzAzH2WNQt3Pw2lLe0Sroa2iWsqW9XGTLQRCfFNC+SR0dHxNp9ZjWkMBPJe2P3VK9p2N28Frw0mYc2osHUnWz+KBAjPT8LGeG0SDa1tp7O6JcUivkvs1NSs7mkN7HNqNIwW8n6hZLarjIePU39lJmoV2ZqOdI4HZGZWp3B3OAD+8c/ZPohu2AbWNYw8w4cT1U6VMscSBM5KP+hFV5e0gNjoOfZGYx1FpG3aP8x6oEBbbGp7SrVGwc4NDI2jueT3KgKppvBqzsOO0qQ1BlKQ1sg+8YQVGi9Rsq4Ba6qz6kgBbFtp26mA2tTc/tuwubN/Sc0FxYPYlWre+pMZ5u8RxDXIa9lqSR0Q0uswB1OlTcD8oD5KrVa1zbktrW24EwSW8/7+6qUdboNMu9BPuf6K1+vpVmB1K6pgEfzemfzylSBNEHahTaWh9I0fwR+CMINzqD/5ajS3vEhAva4e8Avo1fq4f6rOdXptL2k7OwaZE/UIpD0Nc3TiX7HAZmFXZdPxkT1KZ2x4y7c48yCqdVjQZaSOvKKRDk10atO+JhtQ729Pqt7QdfutIuG1bNx2yC+mTEhcMXuZkOMq9YXrXy1zvV7rKcL2aYsv2fWnw18YWniS3FOg8Wt80D0T82F6bZXVUEMumhlQdZ5HdfFHhrVLrSb1t5aP2Pbk9cfsvqn4deKbbxbozC9zRe0xDoMn8e8LNOnRtkVqzvD09+E7TCo2tU7jRqYI4Mq8DIWiVnN0N9U8nulITSFXEQ45Tg8yU2E2ClxBOnYN/MoTiIzlHcPSTKrv+YoaNAL3DecqsSOJyj1mbSTKA9ud3ZZMtEXxEKKdxkplJoiLiBynDsGMpxBMJbe2EhCLoCF5h3E9EUtwc9EFzIjKTAfzSiMqAuAQE7TBB7IsC4HBFa8BoyqTagnIhWA4QOFaZLSLNM+pWAcDKqMcCZOCrLXDbzCuyWgu4bYyoye6hu9kt3snbESPshqYd3QS6CcdUmxokSO6bcFBz5j/AFTbwOY/Kl9jWwgIBkJy+eULzG9029vdDdDpBJCeQhb290xf2ghRbYUgu4dCoP6n2KHvA5SL5Bxyk2CIkyoO5TuMKE9VDdDSHlKUySXJjoRKbkxKdHtqUtzn7KouxkRTJCRomMK42njj9lLypH/ZbLGTZmupGDIVWrSK2X0jHCq1KWD/AKIcKKswqoLXTGEt5V2vR5CyazS2oRKylEZ2m0dkzgMZhTOQhuaT7rejntg3AbioQUQtISLTHCKFZAAg4TQp7Smj2RQ7IpiAeUTa5NBnlFBYJwmZ4Q3U5iMKyRhRhHELK/lmUvKKsQlCK/gFfyikKblYhKEUBX8pyXluHVWdpTFkjKOLC0CEQJ5UtiIKUEQFLa72RxBNgNnskGZyFYDR15+iQaSYRX8CwUcqW2eEXYU4aQmooNgAyFIN7om09wn2k8lVxFbBbU5YQcowaBHdSg9kuIFeB2TbD9lZg9lHaOyKAEKZKfykdjY6KQBhFBYAUzOU5aG8iUaOZ4TtZIkJ0KwAYeRwiBoCKGgKQABwMp0KwIZPCfZ7IspbT2KdBYLyylsKNBByE8IE5NAQ09YT7ETPZOJyjiK2D2FIMRYKUFILYPYl5YRNp7JiCAfZOhWwZZHCbappkUO2RLJ6pbfupJIodkdvuISLSBKkkTARQWNtCUDsluCW4I0GxT7JpCYux0jrlZPiTX7Hw/ptS81C4bRpsBOSJcew9ylaGotgfGHiax8LaRWv9RqQxoOxg5eYkRC+SvG/jHUfF+qVLq+eWW7HEUbcfK1s9u/1Vr4i+Nrnxfrrq9UubY0/Tb24OAJ+Y/aFxtzWa1ucAlT30bJVtgK7/MJEBrG+0Ss+rXLqnlW4khTuKjq9by6Zwcz/AJQlRFNrXMoQKQ+eseXn2WkVRlKVselSIBNQy4D8ID676hLKUSTl0DCRqmo2Gj+GDg9T9UN1RtJvpaNp5CpfwiybKNKmSaji93uZUm1GvJDSZ6x2VRrgJ3SG/wCUHk+5Q3V3OMUwGD2RRDejVtzRY0io6ATgHv7q7a3dI/JSBaMbnSBKxaFAPy/c8/VbFvah1Iee4NYOBPPsqihW/Qa4ruqGWuI6DYY/ohut7qswuBc2mOrnmVsafWsaQ9YyBiBJ/wCyv061sAagt/U7H8WUxpWci+0ftO6pJ9pKzq1o93DgT3iF297cUgIApskRDWF0Ln7qtQe8tcxxA7U4UA0YAt6pMHJPurLLaphow4HJ7qxULRGwGOndW7IOqOgRAgmU/QJOyzYUR5IwZblXaNOndAAkbweqm+g1jG1HjcCIAHUqi+lUDi6m17B0kqUaUHqaW4/ykk55QX6HUe4BtF09iOfplM19UmfPk8D1HHsrlqbkukbnxy5ryCEwqyq3QnMJ30KbfZ45+isGwqUqJbRtAR1FOD91s2b61JpNSpctYBMPaHg/6K0aFlXpCpWa9jzkGk6JTsVI5FzKjfS9gE9Krdp/MYVukyq4AhjSIkjn+i2q7atKmf0tw94HJcQ6c9ZWdVql1QNuaNMYMlo2uP8ARIOLRkXrqVaoGupUmke4H9RKz6tCgZdTe5jh0AxK2LnyHjaHlp4Aqeon7rGuqGwHy+e4zj7oJoAx75w4nPMwiveTTO5o/us976zH4cQfcRKnRv8Aa7bcNbMRuH9UAmvYUNB5iOxVStRLX728dIV806dVm5jpnqCqzmvpEtcCWnqMpLWge+i1p2oOZUDXuj68cL0HwF4mr6BrFC/tHgNB9bG8OaeZC8zcWYJaARwVfsL19Cq0OB2z3WWSLe4m+Kdakfd9jqFDWtLoahYVGlr27voeP7R9luW9UVKQcOoEL5i+Efjj/C7k2Ny4/oq+I/yH2/31X0Rod7Tud4pkGRu5Uwl6ZpOGrRtS1KWqHUjslOYWtswokfqmA90yYmEhoc5ESq9Q+rlTLgECpzPRS2OmQrIBIhEqEEYQN45Kyk/o1SE5MoOeJmE+8KaLTJBSDh1Q94S3hOg0TcfScoDnGOVIuG2EF/JUtAiU+6UoUg8JyQOVPsKJl3UorHy0SUCU4dhO69gXmOxnjsjsqCD/AKqlSIhGaZVJktFoVJ4Tl0ZKC10AAzhLeBxI+yqxE3P6ghQ8w9VB7g4zlQLgnYUTfUwAMITqgkyoOIEIbzmQs23ZVaDeYO/7pbx/soE+yUpbAP5n+5TioOirgiFJsAqdjqwrqg6lPJ6HCES1KWqW9hxCzPJTAoYIKdKx0ETQFEGFKU0KiTRJWnQp+jCzrf8A5kLaosHTldGJLsiYhTACWwKxsmE2xdFGXIA5kqrUpnK0C3sgVWwPUEUUpGNXp8rKrUgahkFdFXYCDzwsypS9ZSljs0Ts2dpSj3CdwESFFFGNkH/NCZSPJOSmhLiUhki7kQEzpJxJTbXe/wCEUJseUycNJMGQnDI906FTYMnkQm2lFc0dv2ThoRQm6A7SkAZRdreyW0Doig5Ay3spBmATIRGjsEtp/wAv7IoOQPHdPA7omwnEQnFLH/ZAWRAOE8KYaeIwMKWyeidj5AoShG8r3KXlxmPsiw5g0oPZFaznATgIWxcgIa6c4CkG/dFgJQBwEUKwWw9glsPYIv5S/KdBYLb35T7QpwCeEoHZSFjbfdOBiE6SdE2JJN0CUlHuh0TJSHKgCpKl+uhUSLITtJAhRk9ylnui0FMdxTbimgpjPYosOJLcUtx9kOSnEnqjYUT3H2S3H2UJ+n5S+/7pcgonuKYklQn3/dKff90WFE03VMPr+6Zxzyk2OiROJUS6eiaT3TJWOhE8pyZHRMTByUxcOhCAHBlMXBDe7aJ5WfqOoUrCzq3NzUDadNpcZwAPqk37KUbA+JfEVj4f0uteajUaymxstBPzEdAF8lfELxtf+MNSdcXMttAf4FAGABnJj6q/8WfG1TxRrHlUS79HQfDQCSHHI447riduwF9Uy4mA3/spX7GnWiHmPcNjYP8AM5wEY6hULyruft78Kxd3LaTC0NEzAAWZudUqFrPVUOXGcN/0WsYtGc57oLTpuqsqU2EBvLncAAdEN8VKe0gtpMIDADEpOeNgpMO1gPqPVyg+oN0xDRgCIH2TRDYRzmsYQYaCMKm/q4mYOOyJXdAEjJ7dEBoJJLnHAwCVSVbIbGDy84GPflHoUQTk/cpUrYNG97y0f74RDUbsLWtIb0J5TJLDKjafpp5cMfdGp1i54NSqZHDAEC3oboIdJPSOfZbNpYAiTAMZ6QEAKg8kguGwDMla9ndtFM0wHPJGDBP9JKBTFrQaGua1zxxuOPrjKtufVq242uLKY4FMbCf7obKWihdmpVaSKFVs9vSD+Vkmk1znRVMjoTMfeVf1BzWtAqsqVInDnkj8LPbWPDWtYPoknQEqdIbgAXPjs2P6rU0+03vaDLgcGBJCBRcSMtwexWlZiow/wzkiBEIbLivZqVrLy6MbWkRieR3wsN9NlR2643CkSdrQ3qP9laNxY120G1KlSsXTxEx9UCpb1GEOLtzXAndEH8FQuyvRjV6QdUijTcAOCRGO6NavqUIAfS9Rgh+f36K/RfREtLHGTBMKVanRMeSAQPmIHCshlq3r1mFpc99MAcB8tP24hK71Cqwhr2Uy3ptELKdWZSds9BHWDn91Tr1GPPprkEd+EULlRK5vmNcCwuZBnBKgdUe7NTa8xAJHCz7jdtII3A/dUSSCQcAdzKGqFy+zRrVjVBe4jnEdFV893IOVUL9pwYHZMagdgbUgcrLVSqxw/iiex6qvWtGVAS1CfnEyB98qTavp2tIBAkGYlBJXpitaEg+qmcfRX7e4FT0Pgg5Qw/zGFxEkDLSeQq5gPDqQIM/L0Te+xpV0Grsc1xPToU7DvI3Hp+EqdVrwQ53pOCCePohvY+k4R6mzIPKmik3eja0jUX21dgJEtMt7r6O+C3i8XVx+jvKsugBhJjEdV8wVWDy212EjaMkHIW74V1ytp99Qq03EOY4GQSCYWM4e0dOPJrjI+9g7AIIzn3CbcM5XI/DvxTb+JdFp1m1B5zWjeJEj6rrHgxMZ6pp2hSjTHJHdLeA09UN0fdR4Bnqj+k9uiFVxLucFBqVI5UqnP9FXrH08rOTrRaRF9SMgoZdI91E+6gostInKUqCSAonKcZQ590t8cFF0OgpwUKpx9UvM6d1F5xzKlyGluyEmZ6pt8c8Ibqkd47quaskiZ9lPJDpl3zJ4gpw8xwqPmnphFZW9IBUNspo0qRx3VikfplUKNTtCt06gOZAWsWRIslxBjCepwEGczOUie5VWSOSZUHGJKcn3QyRkEj8pWCIPeXASoyVJ8SIj7KEjukFCJgKMn2TSkk2VQpUzUUYxymSAIHE9AnknlBmO4+6W49/3UPsYYPThxPZVxVzgCUvNKQ6LO4qTXc9FWFX2Um1O4j6poKLtqZrCOF0dBoIlcvaVP4wwuntj6R/quzBtHPmDbfdKBB+ilI7pj1XVRzkEN4nlEdwonlLopFSuyMhZ76QLiVsOAMgqq6kNx4QaRdCUXgkiFJJZEENp7BNsnmIREiQEWO7IbI4CW09gpyEpCB2yG09gltPYKchKQgLYMD6Jyw+ymBHCSLE3YGCnDZMIm0J2tEjlOyKZFrS2eFMAmOyltCXGEUOx0hPVJJMQ0fT8JR9PwpJ4HuigsikpQPdKB7oFZFJSgdilA7FOhkUlKB2KUDsUUBFJSgdilA7FFARSUo9ilA90qFZCPp+Eo+n4RNo7O/Ci4QYQMjH0ShSCRTr2FjQnlJMkAkk0pt30Q9DoklI7FRL54US+MEpWFEksKM+4TOeGxKfIY55SQy8k4S3OU0ARJD3xyluJ44RQE3EAKO8e6iXT8xx7KJc2eU6AKHDpP3S3eyGHDul5je6BrZI5M8JnfKU26eEE1ARkn7JMqh6pa0FxMQP9kr50+N/jp15UOk2FeGMMVnUzyO0916L8X/GTPDeiOt6bmm+uBta0HLfcr5RuKrq9y41Km8ucXOccmTys3b0i1GkEa+WeZEA9eT9vdCrXLaIJcPW4YHP+zypOqspsLqmGMkCeCVlguvLjdGAZC2ilRMpLoiwVLmrL8B3H3Vl7RRa6mwif5j3P+itBjLegHO+eJ+n0We5+6qXOiAOFRjWwbGS3IzKHUdtMxI6DqiNHV/EyEGqSXGOoTSsUgTgah2/zHk9lZt7anGSTHJUKDIaCRAmD3KnXf6gCPT2TM0/ZKv0jhKlTBPrgj6qBcahyIaFF9wB6WZJwgDToXAYA2kGt9+pV1tR9Wl63gweG9Vg2jH1HQDAmF02lUQGemOPmdnKBli1snimHspBr/wDqM47o7reqyJqVC3ptEwo3GpWtk0l9Q1KnboPZYGpeJK1Y7abHOjAn/sih2kaNy1jA4vr0gCciZd9wBhVmOoE+jc49SRCw2Pu653VIYw8z/ortJ2wEUwXEDCT0Fmm24LCAAwH2ytnS6heRUd0jpBXPUaRp02ufBcV0mmUmmg5ziQB/VS+zVdF013Oa4VHzOQB27KjUqtFQs9IYP8wgjvyoCoHPAOInI5+yHcMY4+rc4EfMRJH0QP0EubMN9dJxeCMhoj/YVSpQqvBDYLAJkDKsU6lSjAcC+mcA8RhQeGja9tXaSRLYwfZUiX0Zdai9omQZPIKzbhjw6Xxt+uf6Lcuy1oMCMdMiVnVS14k5+oVGLbsyt8GfMP2Q61Umev8AVSu6ZYZiW9CFXGGHqkIiH7pHH1UC0gwM/RMQdxIIz0UWVS0+qQe6KCybC/cZ445TlrQMEzH7qIioMZ6JbS0R2wikFjh5A5g/1Th43yREKBbJ91J07S7GBwEmqKIvaRUDmGZVtlTzqWwxu6SqjeAZhTbO7cEnsak+ixQq5qUqkjdiEGnUcxxaJ3tP5IRKgFdgeyfOb26qNw0VWeayWkcoG77PU/gt44raFrtBrn/+WqEMrsPUH+6+v7S5p1qLalN4dTeA5hGRngL86bG5dRrio3Duy+t/+H/xlT1rRXWFd/8A5ihG3ceRCynFxZrCamj2N/MqLstwohw7p92McKWy1pgXkThVK+TjorLlTru54Wb2aRQJz4KjvCFVeRxEoO9vcrFumaJIt7wm3hVt7fdLe3uUrRVIsOdjgpILH7nYRJVLZF12OSByoVXQ3CYuOUOq47UMa7BOeSIlAc7kcHupOcQCUJ/yLNmqXod1QtxIP0TsqE4mPqq05jsnBIMjlKx19mlSqx1VunVBgLIpkjqrlMkZ7Kk2kZNI1W1fTCRqH/MVVaZAKeT3V2yXEsF57lR3exQ9xA4UkWFDl0ppSSRbChJwJSAkwpsAyhKwIQfZMRHKMWhRADhn9kVXYAH+oz7KBICI8bchBqGGz7JOKLWyL3ESQoiqT2H1Q3VAAgVHhvHKloqi55uVLzPdZ/mpeaFIUjYs6hNZueTC663eNgXB2NWKzc+67Czrgtwuvx2znyx2ae5PuHugtqAiVORE9F1J0c/EkSCFFMXBNvCe2CQxPUoD3Dccp6tSAfdVHVBuMlJlpFokRhRSSWZAkkkkgEkmkJ+coASSQxhJACSUtqW1NILGCcDIwn2pAK/QrHSSTgSJSEMpNHdJoypKkhNjbQnSSVCJCOqUj2/Cikgdjkg8JkkkCEkkTAUd3slYUOSITJkpTUkOh0k0qO5Lkh0SlJDc+OiiX9lmPiFTOMIO53dJzu5RY+IUuwSSob5wob8cqBe0JDoMakHCGXEnAwUJ1QTAx91A1ff90WgRY3R82FFxG4FV31sBD8+On7qXLY+y2anum836Kk6uIOUN9biMpckPiy+6rzBUfNPdUBW/6k3njv8AujkNI0PMnsn83pws/wDUD2T+dOZRyQ6L+4d08hZ4rEFTFUnP90ciOJdkd0seyq+cka2Ocp8goNUqRgdOypane0tOsq11cvDWsbuJ6IjagcC50ADnK8M+Ovi97mjTLOtFOYeWnJUSfouCPL/iH4iqeIfEl1e1idkxTE/yjr91yJe1pLjwDgBTrvbJcXEnqSqVd7nNLW8HErSCCcqVAL65dVJpCS2VrafTFrairUZJd8oPQrP02331/W3+G05PutO4qFzXbo2D5R2C2dLowirdso3tZ7g5zok5AHT2VeiDs2mSTz7JtxrvLjhowB390bcRgcygG12J0gkEYKTaTZdVJwP2VgyQ3IIj1OPQBV638Zxaz5B6oGCB3KZnJjNIqkHoClVcwOO4jCYlwo+n00x17qi9+7IOOiCR69dpJY0GI5CnaW4eAQ5waTklRtbfcd9TA5+qs1qpOKYAEQgKdWXbTy6RII9PSeoVt2pMYIDtzuAxvCx6VGpUIBdE9I5WjZ2jGtNZo2sH8xyXH2TDZB9Jt1U2y5tTktGcfVHbY7AGtaCTgGP9ytOjpd1XhtlTNKkQC5xGXfVXa1Fllahr3tc/uTwfYJNoaTMavb07dg82CRke8oTWUwA7cc9Apvm5qbolvG5xhPVtyx7XuMg8RwpLUQ7SwNptIHH4W7b0y62awPLQ7kjqsfyTLWjjaCStq3IFGm0nopbVmtFE0vKujufuwYTUqrRU2k7Xfyg91HV2Cm4uBME8jCz6bjWBbUM7ctdxH1/KqPQNm6GDLsh/JjM+6oanSAZ/FBA6OaOvuhWF0+hUHm+toPWVp1b2hVZubtHTacj6SmiGzkqlarTc5jiXgZn2VWpdhxInPaFqai2iXlzZY4nHUH7LIr0JnYGT3/7JmTWxqlY7S14EHpCrPDeQSCeMSCk/zKQg+pvuJCHva4GAQfrgJipjktfh42no4f3Qn0jHO73CeYwcp2ueGnychIAJJpmBxyptqgxPVOCyqIn1jkIJaQ7aRB4VCLEGJHCTXbXZQWPLDtdxwrbGtc2cTPZKRSBOblze4/CGx7gYMzOUeCAAfmBQ6+YMfhSn6GIO2vFQHA59lcYAW7m/I4LPBAd3b190ejV8pwYcsJRJX0NNNgqjfKqQcDp7rqvh74or+FfEVre0nHyN8Vmjq3r+yw7uhvpbmgERIKz2Oh0uwEq5Kh/4dn6A6HrVDV9KpXtq5r6dRogjMiBn6q+KoaBnkL5Y+CXxBdpFxT0fUHgWlZ48t3+Unp9OV9J/qGuILDLHCWmeZXHK49nXGpKy+6sM5VSrWEnKq1arhKpVazjyVi8hqotMtVawPJVR1cAc5VWrWPQqq6ruHp5+qylkNYpml+oHUwnFcHgrK3HrlEZULVHyFcTYp1e/KsCt6RCxaVc7slH84kDMLSM9kOGjSdXEESJQalXHKp+aSI590N1UyRHCfKxKNFh1QbY6oLiQCUIuM8qO505Moei7CtdPIhO09SoN9U9IUxgBIZbox1RmnmFXpOkSjU3ymTItUXCInoigqtTwZ7o7TiITI9BFMEKABKfbnlWnYiaSScCRKBEmg7uMIoYD7dkNvKt02zAV49k2qAil7pFggwIVwUcdU/k4K2Uf4IyqrMFVKwIx0WxVpRyFQuaMThJwKTRkVnKlWqxMlX7lhb0WNeOLcrNxZpFr2F88wk25HUhY77jMSE36g/5is6ZZu07ra4OC7DTLzdTbwvMhckfzLodF1M/KXR91ri0zPIvo9Ep1wQJRRVwM4hc7a3sj5loMuQWzPSV1JmDiaLqojCbzfqqQuGxKb9QO4TBJlitVHCzqldoecqN1dBs91i3F60VTJH5QB2m0HEJvLKNBSgqaOfkC2gYhKPoiwU+2EKNhyBNaJ4hOGQfZT2nsUgDIwihNkdonhPA7Iu09ktp7KuIbBJ0QUsTKXlx7oSAEpw1T2nspbfZOqJbBQFJobCnt9kg3PCYWR9PdL090VJKyge1vZLa3snSQVQ21vZQREzhITQmiCSRMcqJdIQ2RQxMplKOqiSAcqHoqhSmJTF2fZQc9pA/0RY0ggJJQnuhRQy4AmOUmXXsnun7JElAc8lDNQg/9kDQfzfqh1KkkcoDnkNKGah6JNjoO6qATkqHmg8kqu6oepUC8wobGolh9XOEN1UHqUAuMwEJ1QyRCnlZXH7DPqweqgax90Fzi7lQJhLsKCGoT1Tb3f5iq+0dkto7JDoMX5gnKeUDaFNjYOAhBQSVIcKKmGkjhAhw9w6olNxIP1TMpy4zwjNYQMIERBcTBiE6ciBJ4UakB2wkCm31FP+gY3inU6emaRcVXOIIbOOi+R/EGqP1bVK108n1uJH04Xr/xz8QvYRp1B8Oq5eG4Ib2K8HvKzaLSGu3OIg+yIq3ZTaitFe8qj/ljDpyfZO2kXbWNMCJH0Va3a6vcyB/YLVpD1QwQD83aAupUlRzf6ZYtQ2nSFIgOZ/M7iSqd+5wona75ueOFoHaKUNERk/RZlZor1WgH0jJP06ICqRUpOFOgCRgce3ZWKFMmm2q7JdwmqxV+Yw0mY9kaW1AHgbWNEBUZ2CuN/leWzDSZJ90OkGhu1xgcuI5Km+o552uMZ6prl8Wwp/MTjCCWrKl1cGs8UqPyjiFFlLhzx6W9OEShbtpjI9fX2Ra3qaBH0CAoDUcKhGYaMBGtKIe6QZyoUaQgue0E9ir1J7mNgAZz7BA0vQdlFxewASOo4mF0VnToUqG642kCCcz9lzNO5cH5HWPblX6JL/4pLnuBgdgiwr0beoa1Wq0nULOk6lT/AM09FhVDuHqDqj/2H1Raly5zNjB6s4jqpMtSG7qh3O5iVEnRrGLZSNIubNapAB+Uf9kb5X0wDP7qdai57draeVZp2gdVaBl2Afqos1WJoak+PV/KOVpNcP0zXdCcKbLEi1eQMwiPot/TUmjoEm7KUGU7yk6vRh2WxhZpoPps9Ilalar5dQsblhBBHdUa1V9Eh1I4J4VxkZOPohS2vbEAu7IF28taANpjmMKF1VBfvHPVV3PZVne4haIzkqKtavucQRAH3VdzmjI/1RLijkw/0jhUKjn0yZyO6DNvZMO/6p9kKqxoyBEpB4OeVGo4gDZ9/dMVgXB0mDhMDBlpM+yI8tcMiHf0Q3h1MZOPZFboTE4kHdIDvYIlFzavpOHjg90FrmkkN55SHM8dU3EVhKlPJB6df7JUnlpgqTK2+A7kYnupEBwP4SasaZNxa5onDx+6G+XNIn6pUn52gwfdELt0tcPUprZS2VQIx2RGyWe6i/DyCMpmvM54VErTNDTq4wyoZBwJS1C0DYe0gTlUnj1bmrStawuaHlPORws5JraN01JUzR8I2lG8q1bSs5zLms0i2qD+V4yPtgj7r3/4SeMf8QsP8K1KWahaRTcDndGF80UqlW0u2OY4tc18tIwvStS83SLnS/EVhUMV2tFyGk5Pf74/Cxyq0b4no+l31AQDHOYVOu6QYWT4W1unq+ltqhw3QAQO8LQfyV582kdkVZXqKufZGfzJQXCXgjhZf00S1QvV3P5RGk7RlDCecQp7K4hwByFZAB5VRmcqxuEYVLZAZRcBMxlIZA+id3AWkfoATvmTIkIaqRMkOCRwVJriRyhnopNp7uiEgRZpuMcogMOQ6Vu7AcVap0ABBd+yri2TII2pAEnhFbV+qAKcdU7g5rcD2ToksCsQMEozasgd1nNqYM/0RGvMgoTF2aLTJKkCQqXnE9f2RWOnBOUJ26Ci4wkuGVp2zJGAsmi4GPqt21+T7BdGKO7M2FFNsAEZTPa0NMBSJlQeJGF0JCoC9gLchVLiiC0mO8K6/Bz2QarenROgRhXVAbSYyub1RgAOMrta1IPY76Ll9ctz5ZAElJxK5HD16kViAYhQ87uUO/G24IdiOirAnpKjgmaqVl3zhEGUa3vHUagcHECc4WZud7ptx7ojBLoHKzvdM1Vrw1p7Lbo3vpEEryyhc1KLpYVrWeuPZ/zCZVJ0RVnoovfRym/WjufyuLpa21w9Tz3UjrTAcVJVWLjR015fAAkzhcbqGsEXTwHH9lV1TWS9u2m4HoueeXOcXHqiw4n0/t90tvupfYqTQMyFJx0ge33ThvuiQOyaB2TviS19A9vupNZwZU9oTwnsEhoShPCUI2MSSZ2QVCfdKxNhEkPcf8wSn3/dOxhFEugxCjPv+6bcErGS3O9ktzvZReY4IUd8Iti0EnOEpQp6ym3H/MUWx2EJMwobz2CgXkHqoGoITtiSYV7i6MKM90J1TsoOeScJDD7vZQe8T0QXVBHCG+pJ4RZSRYLjBCg4hvKCauMYKG6qSMnHulY6LHmAYQHPElCfUAbIP7qu6r7wfqpuiqLTnj2Q3OBccqq6sByVA1myTIS5jSZZc8FpjlCLgFXdcDbgifqhGsDy4flS5jotucIKEajSIVY1m5yE3mN7j8qbGiw6pnCGTmUI1R7JeaEx0EUHclQdV4hMH7ucfdHQ0iaSju9x+U4907GSDZEyiNElRbwFJvzCVPRJPaUZokNH0QwR3RW8BCCkSAgotMY5Q0WmRt5CEJdj7RyTjkrI1a/pWtpXrV4FKmC9x9gJ/crSuXwC0Z7wvH/jj4nbp2iVbC3/AOZWhrs8D/cpv6Gkuzwnxxr1fW9durmo7bvedsdBOB9hhcpeVCXwwSBj7o92/aS6JJPVPY20u8ypk8gdFrBVsxf7Ms2tE0bVu75nZKtt/hUC58bAIjqfZAdVDnFzjLBjcOsdvons91eqXPPpYSc8QFcXYcUtBy99G1Lnn11DAEdD0QnhrabWcOOSI5VwsFwQ8gCjSmSeJWRfVKjyagIBfhgI6LRJMiZJr3OfhoiIhHuHtptaxoJ2dPdC0sFzTVqgBrcGe6jUq+bVfBhsxzyUGRFlPzA51T6x2TGn5bhOY4VgllOm7cYjP1VZv8aru3DZ/IIQAnOjJUC4ud6cR3U7gGmARBQ6O+sTuENQAWltblx3H9kn1sEcD91Gq8MDWMAJ9kre3l4dWJJnA6KeVFJBrWg+ow1HehgOJ/m+i1rKhVqkNb6Wqem6dWvKoj5RgY4Xa6borKFFpd6qpxtWEstHXi8e9s52jYbB6Whz+6ttsQ35hLl0NW1ZSwGgFW9N0h1y4ucCGnHELnnms7I4a0c1aaa6qC4NPMYWg3SfLpF725mQu3sNFY0bGMjrJR36SHUKxMSB6ccFZvMzb4VRwtG3NNu1xGTCDcW0biwQeSF1V9YAWxqCnkZWTUtnbXu4LhuB/stYZUQ8Jx2oMgEgQVl137HFrgDicrodSpQCAPf/ALLnK9Jz608kjquiEr6OXJCjOuasGYG2FSNWTMwFbuWlpLKgwTgqg6kGuIOQTAnstUzimhq1WBzKrGoXc5ClcUnNiCS09OyCOIVroxktkH+kyG/ZSZVYT6gUvMBOUFwD3TEJxIb0WCGnLZM91Bu0yATB6HohAupnGQpbhUy4w5MV2OaOfSR7qBad2VMNjh+PqmIcQYcQB+6YEXDbMg+x7KTau4AxMJbiBB49+FHA4CTWhLQR4FRktncE9N3mNgiHjqhhxadzMR07o3JNZg7AgJFpkSJZDuep7quTGVeaBWaXN56juq1elAMYQgZNjg+ng5hQo1HU3ggwZUKHoJEolRokEDEQUe6GnWzTqkXFNrm8gQSF6d4JY3xJ4Ov9Lc4fqaTN9OcyAP6yF5Pp9Xy3Fjj6XGPZdl8PdU/wbxPQc8xSedhB98SuXLHidWKSk6Ol+GHiGro+pGz1Bzw1rvLc09Ok/Ze7MqtfTa4OkOE/nheEfEDTauieIKes0mB1pUfsqAcAH/VeseEtRbqOkUXNBkNAH0hceaOuSO7E30zaqISK8iFWn3K5LZsnRMJ0P7lTHCSfodhqPAViPcqtT+UQVZaZblXHsgKMNH0SPBSBwE4Vx7QiLeVDb7osZEKJbLTAgrQOxmM9XcBWqTBKHRbBz1hW2AQCmkiW9jtycCFOCpUqc9VPy56rRIlsgOEiEXZA4koZVtWieivUAbkIf3wj1gNvRUary04OOyzaSH2WabodyjNf6pBWb5v+5U6VVJJFUbVq6XgLpqPopNjMrjrCrNRueq6ujU3MGZwunEYS7LHmeycvHQSUKZ647SonBW6pi2SqZd9kCs48fZPUqbSI7INV4MFUMG8xIhYuq09zDhalR5PcLNvCS0zMdkgPNtcaBeugxhZq1/Ebmt1B4gBZBc09vykUhJJJpHdDVlDpJpHcJ1mBFzSeCYUQ0+/5RJCi6ZxKpDTIOp5yTKjsHdTSTHR9QJJQO7koHdyo8zkhoCUKW0f9SW0f9SNi5IiOE6cAdikRGEFIZJMJSJgoGtjFwghRLoSMiSoSTlKmDdEt490OVKENFP2LslKUqKSQUJJJMTCG6GRLiol5906i/okUhtxKG54IPKmgFroOEFVQ5fHyqDquSkQ6chRc32Q0BF9T08ofm/VMWGOqhtzBUtMaGNQyTKHUrenryk6nkkT1KEWOIypaopMc1QcGQq1SrkAlTcx08IDqMmZdhS+ykQqVziEF1UlxJJT1GEE8oRDplJ9FDGtI4KgapKRYAOVBwjqp2CTJeYUt5Q5KX8yQbQYPPVTBJA/KiGY5KmBwmrKHBJKkmDYU2tkcq1okYGDKIw7pgcKXlN7lTp0mieUMEOwQ1SGCpbA0cogpGUiWDBkwiteBDczwpMp7cmZRA2Y+qBJoaUgUTynJeX/mkFFAUr2uyja1Hlwa7J/C+TfizrZ1XXavrLmtxHv7r6M+JepN0zw1eVy4BxlrPqvju+rPvLx9QkEudM/dOK3YN+gdKg6tVG8nyxz7qzVrCC1oDZ6joEOtVNJmxhEjqqtV2yludgxhbLejNtR6J1Kpe9tOmAB2/utK2aQ0Uqf8xG4/2WXprJLqz8jp7rTq3DbK33/+o7p9VdCXVssXty1lu21oHc9xhw9u6ya7XPrtEmJ2g/T/ALyhU6j/AOJcFxL3+lgPuj0APPEGWU8mfp/XlUlTMW7LN1FC2bTbl5G7HZCo0mASY9OTPdSqHz6znuEE4H0HRRuH+UwkcuTEDcf1FYMBhgy49ApNc2oQWtgARAQXnbSFNpl7yC49gpUWED0nKBLY5Dq73f5QYyi1CKTYbkniE5qNo0vLaJPJPVQt27iXEGeiTddmkU3oenS6gS7qVsaVp7riuxgEyeihp9m6q9uDk9F6X4W0ujY25uao3VCIawYz3+i5cuT0d2DB7YTSNHbaWzCGesj+yuVy22aQBuqHg9vZW31C5wyd3QNK0dH0c3LhUqj0z95XI22ejGKiqRn6RpHm7aleSHGSF09rZsAFNjIZPVaTbIUoDGiBCv0qAaBjJCXEfJLop0rRlNvGYwl+mZsIg55WgaJxg4TupyIKVAnZzVxZtfb1GRknK5u6sS2mdsSzA9wu7q0ocXd+ix7y3l52tEGUdDTs831O08xu8M2tEz3lcvc23kVwS0HB+mV6jqOnQHBreZP/AMLkL6yHkVKbhkHBWuORnlx2ee6hQJJIOFQ8sfKYkDELpLyzcGmASAMlYz6MVJaJHVdUZWjzJY9lB9IFpGfuqFzb7BIK6G4t/Mbup4McKs23FWm5j2ncOPqtFOjKWNPTOe2ncW8Ed+qi4kGHLUNsXucypgt5KqXVo6m0ubkBaRkm9HPLH7KhcenCjgxMA+yf6z9EhtPvhaUYyHDQ3+YFI7gJIgHAlC64U21CBDstQJMmDgNcJaTyk5u0gDIPVOAxwwSnNNwbJJxwgqyI3NxA+6k13lvBBwoOk9TPYpQCCEqsP6WTDCHM+XqEq2QUFjvKI/maeQjgbmgsIIPKno0u1RQdLXyrFIh7IULhkAk8hCpPLHT+VXaM1rRZc2IjvK1bGv5jGndFZhB+uQf9FkufIDhwQiUKnlVRUB6ws5RtUawlxZ9CWDqXi/wCKNcB1xsLD33AY/dZvwe1C4oXN1pN693nUHQAf98LG+EutNoXb7aqQ2nWG5pPR3ULa8UW58PeOrHWLXFrd+moQcB0rz5LuLPTTtKSPV6hGQgnBgojSKtNtQGd7QfyEM5MlcMlWjpTuIp4HdTAgIfv2RAZEpJDQSmCPdWAq7T6QrK0ivYu0Eb0+yIht6fUIo5VxIYySeE8e6q/QrC0WzBVum3ORKBbCZWpZ0d5grWEbM2xqFJGNIe6u0raBgFGbR7yupQM5OzM8r0/ZVatODhbVSiBxOVUrUewym4OhpoxKrSFm3Z2kzyeFu3NMgErG1GnjqspwaHEz/NgwcJecRwRCza1QhxngGFDzVFGt2dHY1w2o3vK6yzuAWNz9F5vQuSCMrp9NvQWtE4C0xujOSOsLyTOITmoSFQpXALR9FPzm/7C3RFFh7s559kGq/tMqPmg8/0Q31AfsqQUNVeQ3osy7qGJn3R7itj2lY+p3IpUXOJEwUCqkcP4hqebqDz34WWj3dXzq7nd0BIaFnocJJJIGJKTx0SSQAp/KcOMJkuiACb+4S3hRhKFNIdM+oo9ilHsVNJa8TzeJDaU0IigjiJqiQ4SbwkOEm8JS7KXQzhHAUYRFB3KkpMG4GOExgDj9kRRcJhVY2wZ5MBNtPb9kSIJTOEhMKIQFEjKntPcJEQk1YqaB/ZKPb9lMplDVBZAj2UHtkdkQZKRQOwOz3CgWkZkwrEBQJlsIZakCICE6mS4kYCM4QUyCiu+mQEMsE5j7qxUd6eEI5hIOiu5nMIZE9M9oVhwAnIlA3Q8lT2Oyu8dIQS2AZOeyPUwZVeoZgrNoteipV4KBxz+6sVDElAed2UmrKAn5QoGOqkeAoP5CTKXQseyk0NJ4CGlTMOUoL0Gzv8AZFaMAobTuapB4/srRJMflGpxtQmOg8Ix4KbYIKApsGSosg5BUh8wSZIUCRMIyFSOAitIPCdWJscycwpsacGFEGOEZpkDCKoKsmo1IG4nhomVIKtqNXZR2t+Z0fZFKtht9Hh3/ERrRt7G106m7c581Hdh0Xz3sNKmJ5OZXpPx11Jt54urW7CHMt2hhIOdy8wqVSDsGT37KoJsmboZ5a3AG5x7ZVGq4160AmJwP+yleXAb8vz9UWxp+oTyF0JcVZzt8nSLtBjaNIbugmOyoXVc3NYyTsGAO6Le13Gabcd1SptiTOQIE/1TSvYpSrRdNRrWMJiG8N6qwxoo6dvef4tUznsqVFvmV6TCfS2S491arPFauA2TTYMBMmwlInyxJIPUoFSqalRxLgWAKdy8Npin1Iz7KjVftaGM69UxXZN1SasNMF2MdlapP8pm6c9O5VOkwZe49MI9MGofUcdApb9jit0WKLTU9RmVqWFAOIcQPwq9nRJLQOq6fSLA1Hhu3C5ss/o78GM0vD9gRTFUkgAz9V2NB3oEAgkQAegVTTqO1jabWj09O66rRNLc9za1duejR2XE227PTgqWx9D0o1Hh1RskwRhdfb2raLGtaAI7KVnaNY0ECCtGhbgmTHCtRsUpOwNOhubKM2n6hPRWfLIwBhSFIAp8SOTK5pTwovaA0cT3Vl/pbgcqu/iEnFIpMo1afKpVqInAC0H5yq7xkqWi12Y11bSZjHVcxq2l7nPcxo/C7as0RwqlWg0g45EKEWeRX9g6lVqB7AWO9lzV5ppZW30eCvYtS0plQHEkrjtX0qpTL9giMcFaxyPownjvo4OpRLX5aWkdioVqJDmva07oyulq27S31t9XeFXqae9wmkfTHC15o53jOUumEVA/byIOOfdQqUP4O5okO5HZa1zbFu5tRh5y0c/VVKDgHeW4jaePZaRlrRlKCXZzt1ZSSRg9Rwsx9NzXTBxhdfeW8OJ247rFvKO15gLfHN+zhy4jJmOQlDXDCOWAOgkAINSnElh5W92c1URO5mR9EUVjtgmR7oTX42vklSLOdsEcp0LoMDTeZyHd0+wHgoDB357FHplJod/Y4bAA5+yiyaTsSGE/hGaN3BE9lEiRBHKktP2QrARgyDweZVN0AnhWhIGxxA7KpVBa8gqooUlbD0DiInpB6J3AtM/dDpujCLJezg4UvTGujX8PX77K6pvbMggj6r2/VSzxT8PnPtxN1QIe09Q4cj9l89UX7CCSMZXsvwf1hjrh9hWeBTrtcWtPU9lyZ4U+SO3x8iceLPQPh1rP+LeFrcvdNekNlTPBHT9l0dQjovOvBYdo3jHVtLf6aFV3mMH1z/dehOBg4K83Kly0ejj/AM7GByiCYwUOJ9lOQB7hZsp/wNS4IKtUj6TPfqqlIyZ9lZZ6jAwrRAdFHsg8AeyMzp9FcRMkOE4SSCa7ILlplwAxwujsKABGJ+ywtMpl1UGOF19tRim2IBjld+KKo58ktjinJ6QpeWOwVgMB6p/LC6EtEckUnU8kdECpSHIA/C0SwGQhupwCIToEzCuqODAzHZc9qVPDhHddjXpTlYWp0AZxzKmUbNIyPO9QZtqHnqqW4DkifqtzV6G1/EdVjPpEO4lZuKNE7GbUP2Whp98abwDjPUrO2kCYQ37pBAMpcR36O7sr7cBB591otufTyF5/Z3tSiBJMBbFHVJA9RVp0iWqOp889SAoVK3Yz9Fhi/DhO6OnKg6/APz47ynYjUrVYBk8Lk/EepDb5bXCTgweFLVdX203NpmXHC5OrVdVqEuz3SsV3olI5xnqmkeyGJ6nCZxI4wi0Ogp9kkHc7uo+Ye5RYrLExykgCp3yl5hnGAixhgf8AZToAqd8ozXAjlFgES+yiHgnqnkdwkaH1KkkktjzBJjwnSKAGHCdNwMp1EuwEmLRKW4KByUgGgJQEziII6qG7vlVQxP8AmKd5gKCRQ+gsW490pPdMokqA2yR4UZ7pkkBQkzjASJEHKghspIfcULecoiCkhkueUNz9pITVHDCETJJTKQ5cSIJQXVNphSflqE6AcjKQDPd1n3QqjoElSfkO90J5gZUyKQN9SWnuq9WrkSiPiccKu6JKzLiBqP3E9kCqS0YwiVCC7CgUPSKAEk8lQcVNQPRSwQyQMcJJJFBKZMH6qQf6pKCpiICaE2HbUEogqYwcKvTIBMooIIkcKuxB6VXa72RxVE8lUxEwOUalLZQxUWw+BCk18GYQQZATt5QTRaY/cEQVCIjoqaO35R9ErGgwrOPVcx458RW+gac+4unAHYS1swXHpC6Jz206TnvIa1g3OyBjk5K+T/jR42d4i18ss3EWtEFjIPJky77ppWF1s4nxJqVTVNVu7wuO+tULzPuf9Fh1auxhH8x4KK8F3ckcqlUeAcZeunHGjkySsalTmq3dku4/7rWAbRouJH0yqdjTI9R55MpahXLnbevBCqW3Qo/qrK7nl7y7rKdzw1m48p6bJjvElRcPNqY4YqSJl0WraKdHcOefqVZLyy2DTAJ9UREFVXEB1NjemT7IlcwyOpTJKr3GtVcSeFBh3VJ6dUqlTy6exvA/qos9DY6uRWiX2HLt52t6K/ZUySJVOjTyG9eq3NNty4gQspypHVhjyZp6XbEgOPtC7DRaRH1PKxrC3ENaOcBegeG9LMMqVGk9AF583Z7GGFGt4e0oCn5jmjc4zHK7TTbbYCSANoQNOtRTa3ABIytmkwYjspSNZdBKTSSPdWWNIMHhQpiMx9EWnly0itGMnYZrRHCT2gDqnnOOyT/lKqiQFQYnsq1RGd8xQqmeMqJFLsrvaOBwqdTlXSCMoFVsgwJWVmxSeA7BQCIMK25sDsSEBzTBmYUFWU6rJMx7KhdWTK/8ok9VrbB1UX0wQcdErCzgtY0Ta8lgwVjCxczmZC9LrUBUYWuwFiXenAVCAPoVSkKkzhr3Tv1DN/y1GiDiMLmtU0jl1ORUGflwV6hUsTnaMfVULrT2PBY8QIVwzVpmc8V7R5TTq1Buo3LMT8xEQql1Qa9zwRk/L7Ltdb0IupuNH0vBy13Vcjc21Sm7bUBY8cEjB9l0wmmcOXG0c5Xoua87owcqlVp9QIW9dUi+dwz/AFWa+lEg/RdcGcGSHtGedsQRJ7phLCcmEapTAJJQ3BgMHdPstkzmqhx6xuaMqY3U3bX9UIOaD6JBHBRhU8wAVDEoYBJjNPDinY+RBEPH3TbC1pLc9oKi9rXAgsOUihqzQRJ56KpUcXAHoMH6qw+Wc8dFWquDjITQEmCQSrNuQWlsc5VSnCPQIFSThKSBOmJ7RJ9lveE9SdYX9GsxxDqbg4R26rGrYeXO4iQUqDzTe17ehWco8o0a43xlZ9Ba4RQ8T+HNYZijdt8txHciQvUhaFzGuAxErwbStSfq3gu0p7y6tp9dlTODEgQvpTSqAr6dRqNzuYF5s8Vuj0lMwzZHt+6ibIiRC6Z1jxAKj+i9j+Fn8Bfy+jnqdoQfZEFu5p91uGzBGE36KMQSn8VbH8hjhpHQqbcYlaZtYBxmEP8ASxyE+DD5EylPupU8lHdbdQmp0TMRyhRdg5I2tGoy8FdTbjAhY2j0i2mMcwt2j29l3Y1SOSYWAlASTytTIaAovbPCkmcmhorVqeOFjX9EbiV0JBhZt2wZTouLPPdct5OBwSsd1sZ+Vdjq9MOqCM91kVbeRPVQ0rN4HPOtyW5CE614W++1n6KBs29EtF2jnnW5kwELyXDM5XROs+eVX/SEctRolmKRVGATCE/znE+oraqW44jKEbbBxlFIRgVaRiXZygPoAmSPwVvOtcnBUH2gI4TpBRgOpQcBRdS7hbhsyP5R+EKpbDHp+qQGG6mZMcIZYQtl1oC6coT7UwRCAMlwM4KgQeswtR1sAcghQND2KKGjO47o7eMhWP056BLyfqikOkAaMqcI3knsUvIPupBn1IkkktjzhJJJIASR4KSbqVPFgQSU9oUSIKQ7BpnCVOB2Ci5o6JjbBnBhIqUAnlM8ABEuhURIlRIypJKBWQSKSSZRA8lMp7QouEKaKsi7hDd8pRUJ08FCHdAqgmIQzzCO4TCG9okxymxpgnmAgPMkfRWHNMZQn0+yBgXGAUGoYyrJYYKC6lIUSQ06KpwqtQkOOJkq++lAxygup4yFLQ0zNfyScIZd6QY56LQfQa7kKtUt/VAw0cBTRfIpkwFAmVcfQG04/qhm3nopa2FoqF8GIS8z2Vn9KD0/dI2uMD90BZXadzVMHjCKLYj2UhbnCOxpoC3hFY+BEIn6cty6VIUAeASqCxMMOBU5TtpncBwp+SfZAch2GWhFYIKi2mQBlEAhFisKMhOMR7KAkNJnASALqZMkSJH4SQ7PP/jX4rboHhd9tScW3d4Axn/S3q6PaB+V8sXbW4qmN7gSYEYleh/G3WKmt/ECtbUdz6VqBQpsAnPUY5XJeI9LZptgzzHEXLoaWxEEiT/QflaQRlP2clXquJ2jDfb+/dBp0y98njupEAmR1RR/DpEn7LpWtHL2Ha4Asp/y9SFUA8yqXE4GcpeZj6pMEMLT1Qgck+hH/NO3dwokhjBB5Mn3UZ3fMemFGA522MRlWQ2WLcE7nzk+3RKvU7ukhJr9rIbjogVTJjqRKTQuRGdzsZHWUW3bvfJMNGe6CG4A6wrdFuykR1KH0C26LdjTL3l0c/0XWaRbS5pJx9PZYml0fS2Oy7LTLYltNjRBnAXHlkep4uM3PDOlm5vGy2WD1cL1LSrPbtgQfpjsud8IWBYzAjgf1Xf2VsKYAAyFyJWelfFUWLW32gGZVymyOUzDA9OET+qtIylJsIGiQZwFJpA+WPsmn0ieqTQBkFVRIVpkZTu+UoQJjBSLj3TF/QT/AJvqhhsjlEqD90yh7LirKzj6YhCcMH3RSFAjosqo0TKj2yfooubIiArBYJOEi2fZFDplN9P2H4UHMxwrrmwc5Q4ClqxFF1ORxH2VevbipSLSPvC1CAAhPpg9MJcWVHs5x1Db8wQats2oww0AhbtxQB7SqookTyodlpo5i7sA4QW5+i5fVtEpVQQ9jTM916TXo845WbdWO9pkAq4yaJlBSPENU0arbOd5bS5nbsudu6Ownc0h2eRwV7fqOlEte/aT0OFw+v6Jglree3K6sWZ9HBm8XWjzeq3cc4VZ7SHQROFtX9kaT4LSPqs97MeoSV3xnaPKyY2nRTNMEYgEpNa9p+WeyPsBS2gASCfp0VqbMeImioACJB+v9lNj4EOyhlzdwEwOoKJDY5ynY+LA142mDJVI8q3cU3RuJ+yqHlNCRKmYKIXEOBQZRgAQJTB92Wag3UGuntKrtJDi2fp7KxQJdScJxCqOMVFnH6Lbro67wpqjrQVqRyKrdn79vsvtfwTTNTw3YOf8xot+6+C7Gr5VdlQcTK+8/hZeUtV8E6VeUvlfRH2ysJxVnRjm2jcdbg8BN+mb7rT2FMaUySMqVFM0tmYbYRgKP6cDkLT2bcwoOYHmSEvjQ1JmYbYST9+FB1ARG39lpupQD90M045CXCkPlZkVbUAY/oosthuH+i1alMOOOEEUyCDHBUKI+RfsKe1g+3RaNMZn2VC2dgSeFfpHPK6IIhuwiSSSsgSYiBynSKBjdFTviA04zHKuws7USQDHZOxx2zmr4bqrh2We6nPRaVyJfI5PKAWt7KGzpivbKTqQAOBCGWAcAH7K44SCO6H5SlbAq7PZMaIIiFb8r3TilnKpaAofpm5wPwhuoAE+lanljshmkZKYGUbVpM/2Q32g3Y7LY8n6Ib6IDuEAY36SMkY+iE+0EYErbdSG0gcofkx/KE0Bgus8lCqWf8239lvvojd8qY0d2ISHRzrrSf5Y+yC+zMxHPsuldbZ4UTbjq0T390DSo5c2hBjbKb9Iex/C6T9ID0S/RjskxnOfpesfZN+lPYrpP0YJwBKf9F7IsR7NASgJSO6WfZaWebQ6R4SSSbGNASgJ0krYCSSSUgM7hQU3cKCBoYuA5QwZ6KZbOUz2iJCpBbIEOPIUTjlFlRI5KXEbRCEiCnST4okhJSkpyOSmSqhkHCSmLcKaRGEqRVgi3shuBkwEfaJnqoObBOUug5UV9vQ8KJaAchWdg7lNsB6pD2VCznCgaIIwFdcwQe8KBYe37IodlF9La6EI0jJWkaU8j9lE0+cIoakZr6JcEP8ATE8xK1PK9v2S8r2/ZJxQWZHkA4H9Extx2/Za5oA9ComjB4JSUUOzL8gd0vIHdankjsFF1KBhFD5GYbcdITCgCYhaJp9wouYYOEqQmyi63A7QmNHtwrkHshubmCpopMqbAkWwjOZAlQq4j7oaFbBzCSg8wJQnVHB2IUllhzgGweZTVKop0XucQGsaScxiFUqVHHGFieMb82vhy+qjBbSd/QpX6HXs+evClrS1v4i3lxUbvabh72SY9RcYP25+ywvinW3eJa1ux007dxYAPZdX8JQad1WvC5pI3mIyDyP7rzXxTdm41y7ruMl9RxM8TK2x7ZnPUDIb6nkdBx9U1d5LonhSBHIVeo6CT1PErprZx37CMaSJClUMNgclPSJ8mSPV0CgI3STxKdCI7C1s9Em+kbukwPqnEwSeHKLnACOyYmSkhvshN9T8dAne+AIhRAiR7cppEtk2iaoAzK0WDzarQOARyqNuyHAjJWzY0shZZHxWjXCuTNzSaY9K7nQLZ1au1wHA6rltHoFxbAXpPhq0gMEZj8rz8sme548Ko7jw9aCjbt/zTK6WgyQCVl6ZS/hNA6crYpiBChWjab9hFIS3hOGwcqUe4V3RjYhwnBgqMFPBTtgFaZCjvHuoAkcJigB3mRhNyElFynscXRCqCRMYQHCegVrn7IdSJwApaRpFuisQROEtpjp+UYxkoPmN3nOIRQ7FtKYjoU1Su1pgRxOUM3DTyQpaSGnZN9MEBDLGgwU5rNHb8qBqMJncPypfY7oC+mCZQHMAlWyWxjn6oLmAg5z0UONDKj2AiEB9GBnhXHekZhReNwhJ9DtmZUtmuBB4Kxb7SKVQH095wuqcwdQh1KIcOEk2P0eSeJvC7KlNz6Q9X/YrzHUdOqWr3NcOMZX0te2Qcw+mSvNfGejFlNz20888LqwZ6dM4vI8dNckeQPaG/NP2UQ+nJmfutW+tSA6BlY9ajBiIIXoxlZ484NMM11M8PA+wTuYC2WOb+FSLJCI2WjJcfYcq0v6Z3qgVaQf4laT2iVWO33VmqwvPoZH1T21sXVmNeRLjEK7SEleiu2mXH0p3nMLoX2dKhRgNO6JlYFVoNRx4UxnyZWTG49hbUkAtPBUbhsj3UqJh7fcKVduccQk9MSWiNo4Yk8L66/4Udf8A1vhm+0qo8B9pVFSm3/ocvj5rtr5C9f8A+HHxCNE+JdiyqdttfB1q/Pyk/KfyBH1UZI+0XilWj7XcZiEhJUy0ACY+3buowoXRupMi4GOFEAlTUW8ooE7YI8lQcEdzBkye6EkyloA+SIHKAfSVYf6XQMoD8uyopIa/pYou/srtNwLQAsyk+DBVpjozJVphRfDwU8qq14kZUw4HqrTFxLEhKQgb8RITbh3TsXEJUdAgLJ1F8yO6t16uD7LJuahJPBmVLZcUUjyoOYCZUySXHCUJG3RXLBxCjszCseWnZTzJS6JtkGUoaApeXCOKZImCFJlMkwQVSY+TKuwJnUwQfor3l+37JjSnomHJmb5JUSyCtM0vqomgCZygLZl+UovpjqFpeQZyMJG3lAcmZZpSZTGjhaRtgDGfwoOtyG/9kByM11KTgKPknsVo+Q5MaLu2ED5Gd5fsl5a0RR9oS8ke6GrGmzO8pvI5S8taPk/7hN5R7JUFs7sEAjP7p90cZQNyk10Tgp2jhsOHCAZ/dTB7lV9wiYUhUHYp0JhhBS2hQafsnkqQomkkkgQkoHZJLKKYEDzhMQT0RMpZTpgD2+ygRzj9kfKiWknkJ7Dsrhp/y/spx7Im0+yW0+yCk6AFpzjCbZ7KxtPslsQLRX2eyiWOGYVnZHAS2eyToeirtP8AlP4TGmSZg/hW9nsls9kBZS2DukKcd1b8sf7KfyvdGvYins90vLA6q35YHITGm1GvQFYM7f0TeWO37I5bBgJRhOgsB5Y9vwl5ft+yKRKicKKGmA2HuVFzSI6/ZHj6fhIiUbKsrlueD+FF4lojOVYI5CFBEIGuyu5hJmFEj2VkgygvxhBQCoIAgfgILhkzE+6suGECph5UyVoQB4zBCA4HMj9lYqfMUGtwPos32WVX/SUBwO/iQrRECehQYwkjRFepHQ59lxfxUrmj4NvvLne6mWxPQ4XavBHK4D4xz/4N1HaYIZ+xOYU+xdrR5H4OumaT4UddSP4hqzMc8BeV31Tzbknq50z9T1XU/r/J8Im1aSXPqF30C48HdVJjqurHHdnPllpRJP8AQyAeeECsNzmgco9YxDeqARuqSMALdfZzSLdQBtJoByByCg7S7jjupOgN25J7pbi2mI5d+yAGLhO7oMAIZ2EmeesKToaAgjJce6CWyT9m5u3pzKU7qnGOIhDRqIzBByeVXQuy3b0gYI9it+wpSGrFpPa0wTP0W5p1ZjQ2T7LmntnZgijtvDFmX1GktMH2Xp2i2zWDcIkce64Hw5cUWUaZLxu5iV6Bpb2nywxwgcieVxTWz2cP+TstPAFMYAMLRpkbsx91k274Aj2VxtaBBKi7KZpcjJUKhIIgoTK27j91LdPIlOyCTSSW5U5HcIMpSnoVhCRPKbcO6HKcn0wlaETLgOcpBwPse0oDqgbyqdxeU6RJnP2Ssvj/AAvuqEDP7qrWr7ZM8e65zUvEAG8UXekYJK5PUderVBJquAMjb1KYbR3d1rNKg5wdUA9pWHeeKKVNxFMSfrK4K6vK1T5SRPOefyqFWo8ZLyTHTKpRQzta/ix5kghoGMqufEleqCRVEcwFyNG3uLrDWPM9YWlbaFe4DKTpPCUqXQLZqO1+s4waro+6cazdcNqkg9ZMhNR8NXxA3saJ56q5R8KOcfW8tPaMKLRVEf8AGrpr2tNVxn3KtU9bvGuEQ4HiOitUPDTS1orTjkgrTtvDtswcuP1Uyaa0VaKlDWXP/wCczPWQtChqFGrxgozdGtw3DRHuVKlp1uwjaxZNOgtDth4kOH3Ui3HCM2g1ogBMaZn2U/8Aois+mHN6YXNeJdP8+2fjoV1/lg9Fm6nQ3UnTEQUXQ6s+e9ZsvIrPaR+0LlL+kWuwPwvVvF1gBWeQ2DPK8/1KgGzIXo4Mlnl+Tip2cwWmSQESm3GQPurD6Li70jHRKgwuiQfwuu/Z59UVKzntaY+3shWO6pdtLiZBB91pXFo+o4TMdIVm00004cGndGCm5qio423otVQH2xnJAlcpcems4ArsX0nMtn4zC5C4E1nx9EsRfk9KxM5aQT9eytVILekwqjPS0T+FaaQ7aTPZayqzni9UUS0g8GZWzoV26y1G0u6ZIfb1WVGkYI2mcdllvxcEcdkWweW1SCCSOUpdCjpn6O+GtSp6zoVhqFOCK9Bj5HHHT7rSMLy7/hx1J2o/DKyZUdudbPfR57EH+69PMrBHXGkh3QQmgJvUl6kx2hO+U/RAMjoiwZUXtMKQuyvUALswgujaeJVotBMoJpjmCkygCJTfBgnCdwjgJkqYWHnspNfAOFXaYMlEDwehVbC0E3Jt3v8AumBkJESmNAKplp6qo9m4HOVbc1zTEj7KGwIHyRU8n/cJeT/uFc8v2T+WPdKik7Kfk5xCnTpROFaZS9XCN5aZNoqClgY/ZT8r2KsimibECcil5R9/wpeX7H8K2Kc9FPyxIVCso+UeyY0fb9lfNMdAm8tAuX9M/wAo9kjSPb9le8v2S8pMfIz/ACf+n9kjTkcfstDy03lFIORneSf8v7JvJx8v7LR8n2CRYgrkjN8gD+X9kvJb2H4V/wAv2S8v2SHyMo0DuOMT2S8g/wCwtA0clLyT3CYcjSJTIkJtoRxZy2RBIU0k8HsqSoQ7SSVIchRZ1UgDI7IZa6CAmQpqIIhPMiRlQQx0kklouhN0JJTBACdKwsgBKltCdJFisbaEtoTpJWFjbQltCdJFhZAiCmREjwgdg0k+0pEQnSAZJJI+yTQJ2MRITQOxUklIwbm5QzmQiPIByoEwFSLRAiFEtypGMQhu5KbJGSJgpJnKEUiJyVB/RO44KhkCSpKQkCoZcfZFJ6qu8gEknHMplUJArYcpOqAdVXJJJQFCIkoFUozh6cc+yBUkESsmhgHHohDIKnVe3eZKA0+sFRdF+qIuySuI+KzA/wAL3oPApOz9uv5Xalri7AK4j4sjy/C144zmkQfpmUvZUT5cvnilpppzLt39ysSkJqLS1NkU2BxyXce2VnNG2sBPRdsF+pxZP9Ebg/xAEwHXupVTLo6pmAlogSrMvZN3zKAJeXPwB0ClUeOAh1iPS1v3TQPoZ5yc8qEwBHXlNMv9lI/MZ6J9GZE/NCtUhsYd0ZQaTfVJ46fVWaTPOfH2Skyoptg6LS90yY5WtYbg4A5jhWtO0jzAM9BC6LTvDZqEEOAnuuaeRHbhwy7H0ut5bhk8cLuNH1l7SC4wFR07we5rhveDI6rWtPD1Sg8w8O9iuec4np4oyidRZa3NNpDi445K17XVt7R/VcnR06tTghv2CvWlGpTIyef7rFtHR3s7m0u2vaDPGD9VoUnS2cLkrJ9Rkl3B6LoLWuS0SVKZLjSNFJQpvwexCSfZFEzH3UHPwQEiQDlVrhwiJhNjSK95WJa6CBHK5rUKryDDjMrbuXQO/KyK9PcSVPL7Nor0czcUzUe8ie8AQg0dPfUJcWxPGF0jaDWjMfhRe4AdAAk8mtDcF7MmjodI+qoecmVdttFsmZe1pH7odxeii0nBWaLvUbx7W2lE7SfmdgIUpMOCOstWWlFu2nTpiDHQFXadekIgslcC8ajTdW828t2uaJ2jJIT0q175NKqyvRcXdO8otgsZ6I6s1zRkZU2CnAyfqvOxrl3QuhSqsdIdnbkQtrT/ABJQrNHqIBdA6JP7E8f0dg3YWcQptft+WFlUr5ryIcCIn7I7bmVPIXFl01JmVDcDxyq5rjOR9E7KgcRsGUm7E40WG5KJtEIVIyc90cZIUvYmR2ggqjeMlh6g91pECOFTuWu28fZLsKaZ5v4xtsOcBgjK8u1KgAXDOF7N4po7rd68m1Sntc5rucro8d06MvIVqzlH0YqBskbuFoU7H05BP1UTTBqDdgA8+y1aNTy6MEZaOo5XZKVI4IQRQcGsLWhggcrToim5g2t4CzapLqhcOisUapaAUdmkVXRPUGNbb1SMHaYXndYEVnk8SV6JfnfYVCcYXn122HO/K3waOXzOwNQ5BHYKxSJ2gnogPE0x3ARqRmiIORytZbOOOmBuAPNnhWKBHmtq9zBHZDvGANa4c9VG3ftcQev7o7iPpn1b/wAJN8X2GsWBqelj2VWN7TMwvoQH7L5L/wCFW+Ft42r25d/9xauxPJbn+k/hfWhPH0/sue6dHQOkm3BLcExDpnNBS3BIEHhAAy3PKbaeyLAKW0IK5AXMPZN5fsj7QltCB8gHl+ybylY2pbQgXIAGEBLbHKPA/sn8v2KB8itskeyfy+JGFY8ueAl5Z7FAcgBpjom8v2Vjy/Ypwz3KQuVAWMJcMInljsptGQp+5anVgpr2QDIAwphsjKm0GAnA7qkqHysGA1SgEKYaBwE8Jk2wIaPdJoHQSjz7BMixFbb7qRZ/lRdg908eyHQL+gms7jKWwdkQ8pkF2Q2DsoupgSUVR9UooTYHbOAEhTHUZRh6eGppAHqCVMFKgJptgxKj5ZRYd2S2u7FK2Ox5KeUDzh7qTagMo5MzoJJ9lMOkxCEHAqYTTsLoInB4EIe4qQOPdOirsInDoERKg0z1Ukiewg7pJhwm3ZTTESUt3sopKuxE2mU6gCeiW4pUKiaShuKcOKKCiSSjuKW4ooKJJHhR3FLcTgooKFu9kxOZTJJjoROJTEy1MSUv5FDdgnskouMDCkoVMAR3SGRcJMqLmy05SBMlRc4yRKqir0MRCGeSpyTymLRKbViIJnJ1F/IUIfRGMyoPb6eVNDquLWZ7qS0AfUhpBH7qnXrgYnn9lG/uBSBJ7LmdQ1htMu3GIUSmrKRuuriYB4907KrTgnK87vPGNtb1HB1SSD1VvTfFAuSPLiOiIyv0FnfMeJ5CDXIOZmJWGzWC4TGEOvq7WsKtoLLtd4k5T0HBz+FzdfVwXHI+/K09Mu/MIMysH2aLo3G0tzgRzC4X4xMLfCF6AJL2x9AeV6HZ0w4h3dcZ8YKIfoFwyCYoud6fYSqcUgiz411DcX7nHA6KgM1QZV7VQRUdJxOFSojE9ZXVF/qcmTsjUEPk8J2O2tMjJUamXZyJRixuMK0tGQFwETPPRRiAScqTgC8iMBQeYPKaFJ0RbgOxlO3oT1KdwEJmc+yogm/kRjK09NpAvBJ/ZZnNSCtrS2AwesrLK6OnCrkdTpTA0ALtdC2BzXPiAOq4zTnNAGMwFtW1/tpuAI9J4OMLz57PZw0lZ6BT1CmypHtIjskzVKXmmXDnr1XAXGvU2PBpF1QubwMR7SrFlqdVlWq+pTpRsbguLsE8ys1jl2b/ACx6PRbfU6ZGCIP+5V2jdUqpxC5ehe0XVrag+i07gT/CJB2gTK07cUnEGzqFwHLTGFMotFxp9HSUnMjortGrt6rnaNwRElaNCvuAMqRyR0dvVkD3CshwPCx7WpMAnstCk4z7nCaMmkgzxJlVK7vSrdT5VUrpy10CRm3TvbuqFRy0boCJWTXcZMd1l/6bxS7BOmdxyEJ4LhAU5JgEqwxrGgvdADQSe4+iBoyri0oMp77sEk/I3jc7t+Vk3erb3ut9TrNs6lMTSaDAx+E3ifX7SztTVupdWcS2lRByOx9l55rOhaxqmhXWv6hUcKLB/CaBmJEfaJ5XRhxcts5s/kfH0al/4xsKd5GHOI2u8sT+6y6XjSi2nSpvpuZBnPT/ALrktIv3aYLqpUsLW9fWoOpNFcHbRJEbxmC7nmVhUGh24Hc5wkSScnuuxePGjzJedk5Hp2neJbepcV3iq6nUeNo3OkD7Lco6tQqbDW2tpUx87OZXlum6Rd3l7RpUxJqkDtC6PUPD+s6BBcC6iT6DE5WM8CfTOnF5c/Z6jp2oVKbqe2puDxuwcgQtylqZDfUTB/K8V0nWryxrH0Oe48z/ALwultdU1HU3NZQZ5E8v+aPoOFxTwSgeljzKao9KbqjHVBTBlxHAytS0qF0EFcnoVo2g2SS9xGXumSus0+gYCxTt0aSiq2atI4CtME5mFCjR9IJRwzaMfdWjll/BkGu2ZzCswOyDWSaobOU8RUQabhzPVeN+IWmneVBBjle5a1TmhUJE4leP+MLfa41OOJ91rheyM0bjaOMZNS52DM8LZr0CKTXe2THKx7d3l39I0xMO/Y//ACujuztsnexgfldk7OHGvsxXMDTKj2I4TufLZKjTmo/a3t+6aboa7otVWmpYVgOS3/RcNqVEsrubHGCvS7exebU4iW/9153rB/8AOOqHqYEexK2wy9GHlw1bM3y3bTjgJUWuDDBgzkIzXbg5uZnv0QANpcATC6H0cH9D127qE9v3VTIcCrrSTRIlU44RF6FK7s9O+BGpO0/4j6JV3bW1Knku9w4EL7iPJA4Bx9JX56+Dbo2WuabctJmjc03/AP8A0JX6C2lUVrSlVZ8r27v2lc8lUjqhtBUkkkgF1Um8hOAISJgFNDaodJIcJJkCSjMp/qmTAdoyBKlsPdM35gipMaBwB0E91PcExbnlLb7pBsW72S3eybaUtpQTbGTgSpbQkBCB8Rg0yphsypQkABwqpDEMABOkkmISSSSAEkkUkAJJJRdOIQBJRJxwlKZMdCSSSQMSiRJUkkA1ZA+kfRR3+ym4eknrCFCTdA2czaeI7WuRFSnP1WnRvaNUSHj7L5YtdffTI2ViB9V1nhzxVdOuWU21i4H34WHJm7hFn0G0bvlyrDXPDsjH0XHaDq1Woxu8kmO/K6mhd+Y2OvdWnZi47LTqm0SU3mxnpygVqsMJgFUql+2mfXAE5PshyS9jhjbZsU6oJycEYwjNIPGVkW15Sqt9Luq0bd4wmpX0yJwcXtFng5TyOyGXkk4Tbj2CqyQzTITobXHbwn3H2QFEgR0TqG4+yW49kWFE0lDeeykMtTsQ6SiTBTbz2RYEiQOqQcO6G53q4US6R2RaAOkeFWl3dKXd0rCiwHCUi4IO6eRASDh0yi0MNuCg8yIHdQ3HsEtx7BFoKGdLTyk4SEjkynlPkhxIbT2UHAycI0qJbOZQ2gqugUFMUWPv7Ji0n+VK0AMgkGFRuRAzhaW0joqF6w7XHvhSxx7OI8TX5oUqj5iBiCvCvGXi17a1SmxzvZeueP5bZVTxAMlfLHie7c/Uard3U9VmocpUPJPijQOr1q1cvc4uM9V1+g+InUS0OcAAvNLOp3MlaTK5EEf1XSoJIxtntFDxcwMjefuql/4tYWwHAnPJleSOv6jWH1GPqq51CoSYdnv2UuPopTdnqVr4gNaqfUYnovSPCdz+oDTPIC+bLHUKrapJdOe697+GdY16NJxkzC55Rpm8Ml9nsWnAljYC4v421hQ8MXQLg11Sg5g+67vTKZ8sfleY/wDELa1q3hx72fKxjnO6QBHH5Vvoq6Pj/VHl1QiZIJzHVUA+HsA7ZVi6dNUjoCqnLm/WF0wWjin3YVwO4D3R3/ISo1wGvbtyUquYHbJTDogxuHE8jhAnJJ5PCLVd6CBiVAieqpIzYNTGACE2z3Tuw1MQ7AfMB6LWsa2xwAWQzD4WhQYXDBzysslVs3wtp6OgpXo2wHQRjlBuNUG/aXENAyR1WLvqjc2CY6q1ptiK9xT88kAngrBY0jr+aT0jTtm3mqvbRsaT3NONxS1+hfaJc0rW4uKpe2mHQTEA5het/DXR6FJrazmgNwQ3uuR+MWnVG+KX3G07KzGhhAmY6LRcaHNNbZzFpf61Z6Y7XWXNNradTyGtfUbucSOjeSAB2haOieO7m3uQ+7G7fjc3C5M6fVqBzCIq4ADhH490m2ha1we4ODcCOEThCUTPHmywkfQXhzxBa61QBpPAq8EGMroqbiPoOq8X8E6Rd0rJl7Sc6m/dLYngL2DR6xurVjnYqRBHuF5+XE4M9rBl+SOzas6vGVtWr8AFYNu0h33W1YuyDCyRpM0DJQa7MEq80B0D2Qrhg2n6LSXRhZh3RBEdVkV2STK3LkQSAOVl16cmVgbx0tlUMBPp7rN8R6gyw0wudku4A6+30K22MLR+64vxBQvr/wAU2rLa0FW1t2Bzy8+kuPdNF19GdoHh865qTb7Vdx6sp9AOy9Nu9Gp32jVdPLWtt6lPZxwP/mFzVjVvKdy0/p9lNvRvC6e11V+yH0XkAZjK78eRKNHFnwNvkj5y8T+G6nh7UqlrWpv2AkioR6XCTC5wW9J1WoWFrGwQAzlw6L6l1Nljq1uWXVuKwONrmZXKt8K6Lb1xUpWNCk4EmXniFfyKjkXjORxHwv8ADlVlyNSvGltGnPlMPJ9122t29O62ur/I0y1h7rQqXNKzZtpNFUgY2iB9gqYZUvjucCGngHoubLl+j0MHjKK2cvV0OnVqjyaRDeuFraXo9WltDWYXR2emvcZcDA6Lds7ANcIB91yylKWrOhKOMy9OsKrTlhx3XS2VttYMK3bWzQ0Q0SrjWD/KAEo462Zyy3oAxsNiFLbgn2lFe0j5Qmc30mT0VMy2VwJQq3CLwg1zhRLoox9UzTLfZeXeLKU0qneML1DVDFNx9l5l4pf6amFWN/sOf+TzIgi6pkDIdldRqDh+kqVHel7obt7zBlc7cjy3NIA+aSr9499zVYACWhsLtnvaPPiu0VKNu+tUIbMcLf0zTtkF/wD8paXaOaW1A2QOVsVy1zWxiBwFMp3o3hBdlPUKpZS2Um9IK8u14mjdVKRxDl6XeVG06NV9QwGtJn3XlWqVnXd9WqEyC7C6PHW7OTzZWqKw4JHHCZpgpmgh7t2G9EnCHrqZ5lUWbaPMgKo9rqVQtifplWqJgjoQqtwS2o6MDhTHsqT/AFNzQtjHOqPnc0BzPcg/6Svv3w5VFXRrKoP/AFKDHj6FoX5/aGRvBqDcNrsfaf7L7z8EXH6rwho1bIP6Zg/DRCwlqR0Y/wDJ0aSi18iYS3eyY6CAjAUoQt2eFIPJQUn9jj5lKAoz2S3ItEMk2BMJ5KHuKW4p2GwoOcqW5qFv7gQnDmlIewu8e6YPBPChLe6QcAZlGhUySSbe32/CfzAOIQLgOCQcKW9Q8w9YCXmtQHFoIH5ypb290HzWpea1AUw29vdLe3ug+a1LzWosKDb290twiUHzWqO4TMoHQZzx0TbkM1AOyj530QKmGdwkSOoVfzT3SdUkcx90AoWWUlU8xSa+XcoKSLKSFuCbcElsfEMkhbgluCYcR0lDeE28eyY6PgNl+4Ef0JXT+C9TcdSY0k5I6rgPMIdGSTjhdz8PbJ9S9bULeI5+6uUYojHJt0j6b8KBlW2YeDtXZW1L08LhvCh2UmMPQAru7J8syVkmaz7JVqY2Hhc9q9PEd5XR3LgWFc5rFQeXzxIWeWNorFKpUzO0us9lbbu64yuysassAJXmNtqTWapsk4Psu90yu17GEOGQssLp0zXyI2jf3e6aQhteC2U+4LrOHiF3HufykHGef3Q5HdKR3QKgxJPX904OOf3QWkJ9wQDDbz/m/dO13v8AugSpsP1R0JILPuEpHdDBkqTTBStl0J3Ki3hTInhKD7IElQN/RNJ7osH2Sg+yNg0CM9UhPRFg+yi70oCiEnulJ7ogaYTwfZAUDbwpKW091MNMchFDugRxzhSBBE4TVOCOyqvqhnKAX7FwRKdY1TVGU/merVtqFOoAdyFJdFyxyWzRIwcET1VC6pnYVcbWDgM/RDrN3NOU9NaIiqezy7x7aeZYVsRLSvjvxXSdQ1qu1wOHkfuV9zeKbQVbZ4cOkL5H+K+jutNXq1QyGvPI+qmDSkTmTe0cNa1I4Vw1vefusfeWu7dE7rgg9V0oxTpGlXuMRPPugCoSRGJ5jqqYrF2HdUek6Y9oQ0CZqWHrqtaOf6r6Y+FVi5tjRLp4BK+fPB1ib/VKNMNkTk9F9ceBtMZQs6YgYAC5sito2x9nZ2NOKQER0+q8r/4iw5nhO6c2R6WsEdZkn+gXsVBgbTkxAGF5D/xG2763gepSpgF5LqzndYbn+6bWjS7Pi6uPXUJGQVSb8w+q1G0jUo13iIZkuPX2WawTUAHUrpg9HNPstlhLmnk4KhXaWucRnMKwDtbJ+iBWILx9zKi7YP7AuIyCeiROFH5nEjAUTwFotGb2KT3KmctEqLWypuadrRhMVWQZ84WzYAEjhY4btgyOVsaaZLYWOXo3w6ZoGkJkAK5pNoatzTfVBZSnnIRrGh5r2ACSYAXov/h9v+DtGz1tEmMCYlcbkz18OJS2dJ4Nbb06bGU3ksAELo/Evh+x1/TA2uGioz/l1erT/vC8w0Src2u4Aua1v+8LorTXbtzMl9MN5TWVI1n4/Lo4XWfAOuWly59JjLqkeHbgCPr/AL6JaH8Pbp9RlTVXNp0p/wCVTMuJ7Y6L0Olqr69RoeKpDsc4Jkf2latK4q05a2k1jMmDlxPc/aEPOuhQ8T7M5lhVbRo0LWgKdIYBcA0CPdXNOtbiyu2tqOY+k4SdvQ9lapMe8jfVeWNktHaVo2lqSdxMicLDJk5I6oQUEHDBAcOuVcs5DonCiWACPaESgAHALJdjltG7bwWglQuWekolqJDQpXYOQtmrRz9OjEq05c5U3UgXGeFqVGSTMZQfKHYflYOOzZMptoBwIAG7pKCyjVp72bYY4yYE5WpTp+sf6orqMtwfynGIObWjmLu22sPlS0wZHc+6qDzWUQ1zXNdnLT1ldTUtgYkSfZAdaMJ4V8foayL2cxVpGqW+t4aeQMfdQOnMDoG5wn0knJzwumGntHICk2yaCpafofyJGHS08ZhuPytC2030yGgfZarLUACIVmm0MAxhTx+weVsrUbMMECFcpW4HARadMNIlFbAmFekZSbIhm3AUs+6kUylsivY04k4QHuAnPdEc8OBEKtVInCl7Log90CcKrVqY6lFqOiRBVGvUiQJWbHWzO1Sr/DdPZeaeKTh046r0HU3yHe4XnHilxNSDkHCrF2E+jjLpgdT9y+IWhYlj6YH8w5/0VeuwA0p43p//ALckjoZXbejjit2b1lc/xAww0OGDP7It5LflHr5jvKzLUvrw+g0w523dGA72RdbvG24bQtG1KlcCCZ5KhRt2avJSOc8a6sKNu21ZBqv+f25XD0BuDgDB6LW1PSNYunXF3WtKhptMudEwsW3llYyIHUcL0ccUo6PGz5HOdsLUlgbuEjuh1AOWnJ/ZWSPMZt7GUEs2zH3V2RVkqXIQbtkVT+VYt2hxGRg5UL0fxT7kf1UJ/sJ9UWtPeGUsmAcfsvun4V1GHwFo4a6R+nbE9cBfCNq0upgDkuX2/wDCtxp+AtGBI9Nu0H8BY5HTs6cStUjvN8D+yfzR2KpCuIxKQuAfb6qOSLou+aOxTiqYVHzk3n9ijmgSsviqP9lP530Wea2eUxrD3RzCjR84eyXnD2Wb5/1T+cO6OYcTQNUHkpCo3us/zgTyn873COYcTQ8wd/3T+Y3uPyszzhMZhOawHUo5hRoeaO6fzQOqzhWnqn873RzDiaHmg8lN5o7qgKw7pecO6OYcTR8xvcflLzQs4VRzuS88DqjmHE0fNCXmhZ4rAhLzglzDgzQ80KPmCCqPnBMa4yPsjmHFl11YEc/uoecOp/dUTWAzlRNYe6HJopR2XzV7/wBVF1XiP6rONcwVA13DqjmU4o0/OPsisqyMRKx/1BVqjVP3TUiaNE1B1wl5mQqwcDyptMwqUiuAfeEt4Q0lVk8UE3jslvCgWkCUyLDij4J0vT33FdoAMSvYvBOk+QGEDPuFU8OeFW0f5Run/Llek6JpgosaC0D7KMmTkysWPirOi0WjsDY6BdVbP2MBJWHZNbRA445Vmpd7WGCB7SmmEkX7u8DW8hcd4k1ZlKjULnNxJRtV1Ly6bjP7ryLx14iLaVQB8HI+qHvQlUdss2uvU6usOG6CHEDK9M8P640bGkiY918rWGtVLe/80kcyZXrPg7XRqe0MJDxGJ5KyeJrZfP5EfRNhqDK7RB6dFfa7d9PZcJoFaqGDcCuttK5cwAk491pFnPJbL+0/5iltP+YoU7jg5T591oQEa2OSpB/QFBU6Y9MkZQCDp5KEp46FFg0EClvPshgqSCeg1P5QpITSAOU+4d1WhoIlKgIzlPLe4QJkhkxKlt+6jKUpUBLbnlLb7pmubAyJUgQUARLSpDoITqDnhoJnKBAq7tsg9Vj6hXFNricAcK9d1oaVyWv3u1jgTCwyT4qzq8fHyZzGv66GXQpeYfchH03WiC0sqA8YlcDr9UuvHuBI7FZlLU61u+WO4PC8353y0e5/x4yhR75puuNcBvdn3XQ2t4yu0H+i8W8J6s/U6oYWkRy4CAvV9KpbaTTyIXfgm5I8rycKgWNVpedSfgTC8C+LmgCvQqHy/UJg9eq+hbkHyiYK8z+INEPt6sgHB5z0Wku7OWrVHxfqtu62unMeIgqiXFvELqvG1AM1WptGJOAFzQpg+664u0jhkqbQNWLZ53QY9lHy/ZEt6R3gxCpknsHwdsg68a9wkzOV9V+HKDWUGCDEBfMPwZqNFxtlpMr6j0GpNBkLme2dePUbN4NB2tyAvMPjxYvvfCWoeXIcy3cAe0//AAvT6Zl7szAxlcn4vsWahZXNN7S5j2Frm8zgn+qbBdnwe2wc7TL+sxx20jtLe5BgrAt2gVDP0GF2erX36O11ewp0mt33LmTgQJOPzC5CtT/T1oeSSDiOOFrjejGXYcjIae6rV42k9SrAMu6oNUCDMJLsPQAwGgD5oyoDJAPCTjLiUqfv+61M2EaBMd1Nwgj6qLeVJ0mIVAgVQy0DpJWvpAlrQSeVjP8AmK1tEI3CTwcrLJtGuL/R6H4Msv1Go0udo5+y9lNlus4IMuEABedfDWzNSt5oBDeJXr1GnLA09AvOm6PewpxjZydXQ80ywQ0cjoi0tMIYAGjHXquq8nEbZT06A4IA/ZYbOnmjnGabFR21mCI4/daFvYEZJcTHUrap2oB4H4R20gMAJ0J5DOt7RoERI7rToUQ1uGwiU6QAyB+ETbA4RRFgTTnJ6KJAaWn3RnGAZMcoQyf6JPRSfZsWImJRb0CYQLCYb3R7vC3/AOph/wBjLqtM4yobe8yjVCN2VDCx9mt0MwyZUyVAH7Jy4dwiyXtjkSMpywH6ppCLTySekKhMG2mAMgSn8snoYKPE8hTAwhL6EiuKRUg0NweUZRIRQ9jDlPAEwoymOOVBVEpSLsIbnZOf3QnPJb2yhjpBDE4M/VVqvJ+6k5wDSCY+6r1nHoSspOhgqrvUVQuXckKzVOef3VOqRtIlRY0jH1QkNPC8+8QsBrNJ/wAwXoOpiWQOZXBeI2F1wRByJBHSOqvH2E+jk9UcGVhHR0oNyTcXLG0SPXAdPRaVXSLjUawcwholrAIy4447rXqaLbaXZCqwC4eKkFxOW7ecBdTdI4Wt6A6U19Em3FOm8W/8UtzDicCeMfRPcXFO0dcPNEMplgqAtEwScn1dZnvA7o109lLbdU3NY13zuLjBaeB9lyF5rNK/umu1K5bRtbOm59GjH/NcDIBH++6vDeTow8ifxqjbreP9Co2+omravq3hDKdJrDFMgck9z9IC8rurhl5e1qlNmxrnSByRKBfVm3F3VqtY1jXOJDWiAAoWxAeTwF6KxqJ5jm32WaFQbnMcPbCVf0mQfseqET/EBHPsi3mKYjBj8pey09BLZzDUhzMx0wp3tKnuEvcw/wDUJ/cKrTG0hwySAEe79QYYKlqpFJJhaFEspAte0iZHcr7F+F9y4+AtKn5hRA/C+NrX5TJ6r6/+FY3+DLEDIDIH4XNn7OjAlR2tK5O1Lz5fHdCFF0ieFN9GQCAeckLB2a1EP+oIxjHsl559lEUSQMcpfpyPdNhoRrSZmPol5+OifyT2TeQT0QGhec72S84z0S8gt6JeSf8AL+yVSGP5x9vsm849wnFvBlP+n/3CdsBvOMdEwqkdVP8ATZHKkbcdcpUxa7BecT1/CXmu5Rf0w90vJA6J9gCNV3sl5x7hE8gHpCXkAcgH7Ipjsh5j04qOHYogp+37J/K9kbERbUJA4T73eyfYB0UCwDKNlWPvPslvM4hNCSXJhQznE4MKKml9ijk2LiRDADMlSSAnBBAT7AlQ7ojt91ZpITW+yPTEAYWibFSLVPLcorRwh0iNvuitGQtPoLJwPf8ACdrQRkqTI3ZjjqiekdlZKBtEZATyeyckTyEpHdKieTPLbGzp0xBZ+AtNgbTIjA6rWqaTskgcLJvqVSgHGJ7KKa2b8rWg1S5DBO4Qsq91NtNjiX5A4CxNT1fyAQ4wQuL1rxG1hdtceIhEVewels1PEmvhtGqQ8RHeF4x4j1Wpe1iA/wBOeVo6vf1r5zmguLSeFQsdCuL2u0Bhgx07roUUuznm3LSMeytatxUDWNJMr134a6NVoXbKrwcxhT8KeC/K8t72Ce8L1LQtHFq4bW4ntCicr6Kxw4rZ1Ok0Jpjut23pbW4VPTKWxokchbFMANwpiqJl2QClGfdSLQU6shxE1szlTDT3Sb1U2jqkLocNg8p04Gcp4CZNihICE6SCbFKaUzuUkDSH3dkgVF8iFDfHKB8QwcQcmfZSFTsq+8Jbp4SHTLG/skKhHVV94HPKW8IugpsseYYQK1WEGtVLcAoBqFxzlLkCiyFzVlpGVy2usNRjgBJOF01YAt91lXtCZDsrDKrR2YZcTybXbAtJLZXPWOnPu7sU4hpOSvU9YsQQ4AAT3VDSNFDLjzOG8lcPxfseovI/XZqeD9Do2jGtY2DjK9CtKRpsHEQud0whgAHRbP6kNpcr0MUUkeVmk8krLd7cNFEyQIEryb4j6lTo29Q7xEEwD7Lq9f1ZtO3cQYxlfO3xN191xVfRp1J+h4Tk7dIwb4xPMvEFwbzUqj5O3cY/Kz225LpAwrYpF9QuPdXqFARwuyOkcdXszf05AT07d28YK2vJaMwFYoWzSctTuilE1vAV6/TtRachpX1H4R1Zle2p7X5K+W7Wk1gaWiCD0K9S8B6/+mhtZ4ERglc89OzWL9H0RRrjYXTkiAq29jrhryYayA4ngR/ZctaeIKb6XmucAxuA2eVkeN/FAsNG8i3JNzcgtYOxP/dTyVGnB0fJ3xRo06PxD1xlEh1I3TiC0Yif/hYvi/T32GpW7aghlSk2o095C1vHenXen67dU9SaTdVD5zveYP8AdUvGjxcP097dxJoAQfZbQZzyVMxWAl+OEC4fkgKwZp0THPBlZ9RxLitI92S3SoipgfwyVBFj07ehVmZJvyj6JEgCSkSGho7pVgWtE9eExgHGXrR0R4bclp4MLMPKPZ1PKrsd0BUyVovG/wBkz6L+GQAsQ4DExC9PtILQeSvLvhlVbV0lrm9SvT9PywSvInp7Po8e4Ki6Kc5wisog9U9MngI7AJA6FJbG9EGtjESpsYHHAyi02tzKIIaYCZHJEDTHTCGTDSEY8KvUMTCT2gu3QCqeAhsMbfqpPycoU/xAOxU+6KWmzaseR9VYvWzCDYCdqPecj2W16M7/AGMqpk/RDnEo9VoklVXemYWDezb+hA4SoE5UXOIBMqG/ac9UWCQbdjhWaBkKix4cYVugTCYmrLTeFMDCEwy1FB9IWkDN60JwUCYKk4yMKJCT7GiBUX9E7jAJUNwcCR0WbLRAjn2Q6jj91Jx5KCSTyoZVUMTJVes4TEeyJVcWtkKtUcZ55Ud7GDrDE91TrCQVbqOJCp1TyoDozL4TPsuJ1+GvLjwGldvd8n6LhPFrxStaruoBgLTG22E1UbKP6+hbUaNI1/XRb5xDMHdyBP0KDea7SrtaGnbSYw73P/mdPsOeiwdMsPKs3Vbqo81q4LnRktaeMfRE0jXfD+k0bune0n3F9P8AADQC0Ejqe47L0Y4+R42TLwdg36ox9rUrVHDbRcXbHDDoGMFec6ndPvLh9d387iYAgBa/iy/F3eO8sBjC0bgCDn7LnnH0gdOV1Ysagjiy5HN2xgp0+57oakWw365WzdqjEKXeuBzyFarfxKLe4HVUJ9TVeYJoyeggLORpFgqR9AHJJVqqJAHZVLfL/ocK5U/nI6cJMqPRGzADnAiYnj/fsvsH4KNdU8GWG4fyyvkCzGS7qV9o/A9rT4H00wJhw+ywyK2jpxdHa/px/sJG3EEYWj5QTtoiMj90uBVlBtCAAeyfyQtDyR2/dOKInhJwQuRneQEvIC0fKS8pHAdozhRHZOLechaH6cdv3UhSgJ8ELkjNFuf9hP5H1Wj5Xsl5SXAOSM7yPql5H1Wj5Xsl5XsjgHJGd5H1S8j6rR8pLykcA5IzvI+qXkfVaPlJeV7I4ByRneR9Uxpbei0TSUKlMxhDjoakjNdTBMobqYiOFffTwD3QHM9WVm4spSRRqN2nvhD29e6tVACq5HMKGt7LTTGGEkpSHumMmnCZOiKJCMGJ7ogGVBg9IRWhUl7KsnTMOCsDkKsOZ6ozT6eeipMkOCpbvZV2uzjspbz7K+yGHGQnQw7Aynk90qFRCswPHOSsbU7PfTOO622uEzyoVGtfyE2rVC5UeOeMNCqXLXBjSDHK8wreF7p9xFYuLehhfUN7p9OqP7LnL3QaTnkimY5mELXRqpcuzx3S/BrJaXj8rtNH8K07fadgAxC6+001lN4G2PqJWzStabAMDCtNjbSZh2OkBoGFu21mGkO7YRdrWxtbAUqtwxggGDypqhXZYpgUozCIbprf5ljXV+xjTkzCxb3WqdNriXn8wlYuN7O0FxJgFGp1ZeJK88sPFFvWeKfmevjldPpt8KkGZ9kKf2S4HSBwAynDhIz1QKLw5ozlTiDhUtmckWkpKrtcSYTucQeqZNB5KUlV/MPv+UvMPv8AlAqRYJlM4w2eqB5h9/youqHaZCBoIXg89FEuHQ5+iFu9ki7OAkMkmlNu9km5TsSVi3HthQdUHE5U3/Kfoq5bJmYSZfEYu3O90UAxwoMaA6UWZMQpaHWiMSq11TEcqy8hqpXVWXHp9/ZZvoqOmY17Q31C2JCZ1EUKQnBWg1ocSepVPUaNSpTcZ9IHblRx2b8vszausUbYGXtEBYmq+NramNhrAQOJXG+Pa5sQ8kuEjEFeSXWovqVXF7ic4BPCcYuXRlOSTs9I8VeNTXD20nktMzleW6ncuuqz3P4J7qNW6JaZghUalWXA9J4W2OFbOWc+QVrQDhWaZGFRNUYhSbctackfRdPog02ubIz+yPReGkx7LHN20Zn90m3rDOY+8oexKSvR0lKsO4V+21E0XggxGZXHjUGt/mlSGoGYMx9f+yhpFp+z0218X1KW01akUafqPuVXsfFP+OeJm31+8i2t/TTZPvgrzO61Bz2GmDg8qVG98uk0NwI5GJWfBFrIdF8WrqnqniCvfUY2Na2mB9p/suH1er5rbOeAI/C3bhjrqxrElxJj3zC54gG3DXAlzXQCVa0TJPsr3MCn9eFmv59loXj9z4AgAR3VB4iVrFaMJkAJ4RgROVBnMqbchWiV2JwJc2MwcqFy6ahhFGAVXqmXEhMYzuFFSOQmOEAj2v4K6mx9jUtiRvpkSPboV7fp9T+G2TC+Uvhrq40vxDS8wxSrEMOYzK+o9LqA0mObkGCvM8mHGVnveFl546+jepmYVqnlh7qnQM9IVyh1XOjrZYpcGfZEJA5Q2ujMcpz6wOia2ZPWiT8tI6qo/hWC6BwgP4Q3Q69lOsYz2yh037nA9JSuZmJ5xCemwtcAeTlSluy2vZu2JGJVm4B2qrZ+kAq3XPoOenC1XRj7M2vhUnZlXLnKqELCXZuloG7c50kY+qp3DiHSJjrhajKU9ftCjcUWeWQB90AlRmUa7RG4wtKk4FoIyFg3U0HzyJ+i1LCpuYCOCJ5Qn6BqlZr0nCInKLKrUnZ4RwVom0ZvY5UagkYUplRcekfdD2CBucIicoTyBCVbkIck8qSiD3HcYCHVIDcqVR22TEqs9xJk/hZSKsiTj7oT+v0KmeEIumRHKhADieECuCrBEAlV6x9JUsaMu+wD7rzrxs8Np7dwa4kAFwnb3K9Ev+PpC8Y+L12abKVOm4hziTgxhb+NHnNIx8ifDG2cx4h8TFlS4ttLefKeNj6hGXfTsuQkzI55UE4K92MVFUj52cnJ2yUkmXZSSSTIEjAgs75QgJMIzGS0xnKBMhEgq2HRQgdFWaZeRER1RmmaZUS0XEjbyXq4MNdOJVOidrnHmDKuPMMlRJlx0PR2g556ey+w/wDh9e5/gxlF5ktO8DtPRfHDCfNweF9Sf8L+uNrU6tg+d7WiJPI+ixl2jfG9M+gaPytkQ5G2Aj1KTaYmQftCO1od0hUlYnIrGmPf8JBgVryx2CXljsE3Ehsq+X2S8v2KtBg9vwn2fRPiCdFXyvqm8r3VvZ9EiwdYRQN2VPKT+V9Va2DsEtn0RQJ0VfLCRYByrWz6JFgI6fhFCKmwe/4S2D3/AArXljsEvKHYIoaZVhvdKG9yrXljsEvLHYJ0h82VXt9PCAaZP2VxzSWkcKGzaDlQVeylUYBiVWrMg8LQqtgyqlQEnPCllpmdUGDhVKokLQrN9lRq9fusZLZquisweog8og90Iuh8wpeYOym7KQQcqbcOkqAMFSDplAMsAiJ6KTHCeVXBxz9lJjocCqvQmyzgpwRCAahjAhTbUb1wU06EFBz2UwR3Qd47j8pbx3H5TUmKg+4d0pb3/ZA3juPylvHt+U+bFxLDSJEKY90+32UmtJGArM2yBaEN9KQcKwWuHRNUaQMhGxxezLq0Q0yAMcqo66FOQ76BXLqoG03c4XEeINT/AErnkuAEKHkSdM6IQ5m/c6o1jSZHZYl9rjGgzUaCF5trfjIUXPaag7QuA1fxrcVHEUnEj++Vcf26CVQ7PXtV8U06ZcDUmZ6rzzxN40DWvbTdLuF55fa9d153POZwsW4uHVHEvcSVccRzzzpdHY6V4nuaepMrufALsiei+ivBWuC+tqbmGDGV8kW9TI7L1z4Va+aVRtu8knoZSyQ1oePK5aZ9Q6fcFzWyVpgyJ7rkdEvDUptM5XT29TcOeizi9DfYdKE24J09iogcFKVKAkW9uU9kcSMqBKnCUI2IHyMpCe6JtTQJTHZFO1PAGQpMbuCTAiRg9kOJwFYIAwokADKV+2VQHbHKcAAp3cqNUwAT2TsFYGu+AZP0WdUO9xnKLeVZ46INu1zzxn3Wb30bJa2WaFLcQi3NuBbmQMq3ZW+1uQo6hAonvkJ8dWzO22eCfFil/DfwvErqn6zHHK95+KNI1LZ0CT7rxutbmTMLGGRRtGksfM52qHAcqlVJBIK3ryjAkDosS8pgA9zwumE0znnha2UnVSDzCQeXZBQH4eQeUa3ZuMdVu2c6tuidNj3CJKn5D45WlaWweRAMLVo6aSBjBWE81G8PHclZz9G2cQOYVptCG5yYwujo6WJgAwrY0pu4elZS8iujoXjNI45to5zs/fCtssiNpIwOfouspaSOYwpP00NDiW4Uf8lMpeL7ZV0KzbcUvKcDtcYOPquQ16zfp17UoVWGC7cCcGJheoeHrUU6sEhsCc/ULnfi9TLNXY9zNo8qBjkd1riycicmKkeZHBdOZ5VJ0l+VaqHaYPJQNsyey7InnSIxHRODAgJUwXPE8J3DBTv0RsjUMtx3UCBASn6/lPT+b6qgIuEAd1EmUWs3acd0JBSJUnmnUa9phzTIX1B8MdfGr6Fb1NwNRoDX/Ud18ujnK9E+Duvu03Wv0VV0UK/vEOWOfHzidnh5vjnX2fUds/EFaNFc7ZVw4AzM+8rco1JEEry/4e5Jl2R/mS3ADlCkbQkgkcZ5QqhO05Uw4DJQapxHVAegBaXPBJkAyPypPH8SVCrLRJ6BULrUG0R6yQBzjgJXRVWdPbPhoRatSVnWFdr6LHNcC0jkHBRqlbqFSf6kuO6I3DxKq7xGTCFd3TWgmeFx/iPXq9Ck5ti1r67uJ4b9Vm2rLUaO7bWZ0iPqgXFyBiYC8Wo3viV9c1H6nUgunaG4XVWusXfktbdv3vAgnEqJN9I0jBs3Ner+guBx0WjoVQvsmOE8Lj6txW1Os2hSaSJHA5XdaNZutLJjHAzE5RC7HNJKi+xxj3Vmm+RHVVSpBxlaHO0WVB74MESEMPIB4Q3OyZ5T2A5JdyVB5wUziA2egQarwYiUm1QxnEuJygvMGFLcPdCfkfdYt2CTGecxOIUYHdRjKYkIHYnxGFTqnCs1HYVC4qAAgSoopJmdqFQ7HAYML5++K92a2vtpA4ptGF7vqtbZSc84gdfZfM/iu7N7rt1VmRvIB+i7/Ahc7OD8jOocTHPKSR5TL1zxR5Kl090wPKeMT0SJZJolwAVqgIYN3JKr0fnV4fLAHuk2NIq1/wCHWqdicJ2mGlRufmZHVSYf5e6l9DQ9H5laDg5hB6KvScBg9ESmOVEi0JrvXM+y9W+A2tf4V4xttzjtqjbzg9l5PHq91teHb59hf0bii6HUnhw94WeRaNMb3R+jdofNoseOolWGthc54A1Rmr+HLG7pva7zKTSQOhgSF0sZKuO1oU9Ohk8HsnAMhSVUZ2Q2kpQeymkigshB7JQeymkjiFkIymgjlESRQWQg9k0ThESRQWDiOU0O5RUkUFgtp7lKIEHKKkih2AIwSoPzzwjkGFB/KmikynWAGIVWqFoVASFTqtklS0jSL2Z9YAcYWdcYJK07gZP0WVc4Lp91hk0bRZTPJSk90J7gHKPmN91ia0WWvIOchS8z2/dUxVJMJzVhFhTLgqwpeaOhWca2Sm88/dHIVGl5o7p/MHf91meeTyo+c4/RHKx0an6gJv1AWZ5hS833T2KjT/U+5TfqB/srL3Jbkr/oUd0GdxP2U2MicI20np+ydrCJXWct2CI9kz2bgVYjuB+FAgbTjKKtEt0c5q1LawxOZXlXjWi+oHRI5lex6rT3UjjhebeKraZEZM4heR50/ido9TxN9nzd4qt6tK5c6XQT1XNP4XqXjLSXvDnNYZEkw36rzC6pvY9zS0iDHC7/AAM3ywMPLxyjKynVMBVntkyr9O1q3BDWMcT7BbmmeE7m6IljgD7Ervuuzh+Ns5aluLg0SSfZdr4KoXLdRpOYx4BMn2wV1Wi/D8tDfMH5avQPD/hFlDaQyHA8wsck4nTiw8XbOu8KVnfp6e4/ldxaVJXLaVYGiGgDAj7LoqALABnHXusIjyLZqeYAnFQKo2qSURrtwlbJ6IsuA4numIlVw4iMmAi03l0zxCYnslBRNnuVFSBEATlAqoby/dSDQApJQeyQhgwE8JnQ0FTcYGIBVWrUjkqZTUVspRbY9R4AmVVfcA4EKtc3O3E4nuqzKsncSOeJXBLy1dWdKxOrNQVQq9at0EIDqwjDhP1QHVNzoBJ+ia8pS0gjifZCtue8AdeVo2FHPq4johW9AucCevcLUpU9ojC6cVyIm60WqIAYeyztVcBSMe60SYaRIAWRqh/hOjstZ9GcVbPJviBT3UHgiV5DcUhvcM8r2Lx4R+mqd4Xjdy8h7zk5PVedJ1I9DHHVGTftAaYXN3+H46LpL14LTIC5q/y8nounC7Mc6VGPVcfMV2wjcJVGr/zFbsnlrsz9V3Po8yC/Y6vSWNcW4XVWVu0tAgLktHrBrm5C67T627bErzMzZ7HjpNUa9vZNIB2qwywBghqJZkugTEfZa1Gn7rgc3Z2qCZmtsmtwWqFzaNFF0CStos2+ow7pkKjqBii78wiM22Eoqijp1Frq7WxMZP0C5b4yDzruk5vqb5W2O0T/AKrrdBcP1pe53pAM5mFynjwB1tcXdY4O8U5PWYA+sCYXp+O3qzzM/TPG7gjfDcwojjlRrDdUA6k8BTpt3VOm0L1KrZ5HYwbDCT0Q6h9BVmpDmxJA9+qqVJIgTyiPdhRFwgNHsp0WEnCd1I7mDPCsU2gAgASB0CbY1G2CrtlxyVVPKvVhhxjtlUyMThEWEtMgjWtd9tXZWpkh7CCCEFJULo+mfhv4mZrGl0SXDzmANe3svSbS4a8SDnovkfwB4gdoertJeRRqQ1+YX0roepMr0adRr5DxyOF5XkY+D/h73i5llhT7R2dOtMAwjA4wsq3rAtH9VdZVG0RE9lito6A6g4yVEPJMZCdIaIvAIMrM1PTzcUj5ZDXdJHPstMkHqE4IiJEdkNWNSp6OW06nqdiXNLmupZgTED6Kzca3+nZ/Ga4HjhbrmBxnH4VO802ldMIe0OnuOFDTqkVy+0cpd602ru8vdKyKrnVnkEAroLzw5Upud5WW9AEfS/DjnPbUr+lv+VZtMpSSMSw0utdiGt2g9c4XR2Phq3YGuqh1R/Uk4XRULOlRYBSa0N+gRuCtFCuyZZL6AWdjb2rQaVJrSBkwFZJBM/3UZPVNuAE4Koz39jPJBChMHlRq1R0jjogGplF0AXefZLzO4QvMKYvHUBFhsIXCENxk4UXHBGFAmO35UNjSokSoSkXCef3QnHiHfusykSe4tMCEMmTKYmeSh1H7e6YDVnbR0WdcPE+6sVKhzJMLPvag2EjpyUmWl9nHfEbU26fodd4dD3ANb91871Hbnl3cyvR/jBrBr37LCm+W0xLs9fdebc9F7PiY+ELfs8Dz8vPJSIpKcJsLrOGxhyiQdk9ih8EK3Sb/AA4iQUgeyDOQ4LRpguocD6qgW7BCvWpPlHJyIUvscVRUeziTmD+yHSdJE90e4ZtB/wByq1HqgQRvzFEpkw4IbeZHKK3Bd7qZRNF0RDjPfKsUHgQJxMlVgPV9CjM+acRCiSKTpn15/wAOWoVKeksBuDUt3gbWn+UjlfQDHbxPRfGX/DXrj2a0dMc6W/Mxs9Z4C+vtPrktAJlpEz2WeO02mVNKW0aSSYGRITrcxEkkkgBJJJIASSSSAEkkkgBJJJIASSSSAGIxCHUEEIqhUEkIY0AqfKqj/wCZXakeypXEQY59lnI1h2Z9wSPwsi84K17kHt0WRedVz5DeJkvw45JUTwFOoPXwVAiFyvs6PRE8KKmowOv7poSQySZw7DHskZA4R3odChPhR9SW09U+lQEpCfCYSmMymhMdJQJMpkhnqCSSS9A4SDuVH+X7IpAOVBzcenlRQmrM+9ANMzJK4bxDbbiSB1kr0C5pnyySuZ1W3D2mR0MLy/yOPlG0d/iSo8z1DR23LXS0ZwuWufAFvc1NxpQT2XrrbOXwWyrtDTmuAO0dlP424Ro6PImmeSaX4Eo2zp8oEe4yut03w1Tpgfw2iPZdw3Tmgj0hXKdq1rRheu3ZycknZzllpDGgAtGOsLTt9PawTgla7aTYgj8KXoAMLNxRDm2VqVoGjgI3lRwn8wTHREBBGEopWS2/ZXAMqbWnsjtpklEFM9lotIjoA1pxhHpAgQiNp8H6IrWZ4TFYMNJRG04zCmGlSA4QJyIBvZSa08CFMNAUXkg4QJPZVrOiSFk3tzGAeit39bYCDyuerVC6oZzleF+S8v49Ls78GNtWxn1STJnuEjUdE4UCJ+ycL5t+RJuzt9Eg4lX7OgXRuyUCypF7hHC27eiG9F7HgRlk/ZmGR0tErelAEq2GgCCMjsotACkSOpX0mKPFHBJtsi/hZOpOik7nhabnyOVkamf4bvoqn0ENM8u8ePAt385C8WuKk1Hg9yvZPHLt1CoO3ZeNXbdtV3uSvNn/AKPTxdGbeuBYcFYV31W3ecD6LCvTBK6sBzeR0YtU/wAUhWbf5VWqj+LKPQJyu6X+TzMfbNywqFpGcrqtMuflz2XJWokLZtHubEFcOZHp4HSO+065B7roLSrgdlw+l1jtBJyultbrAkry8kaPUg7RuPcCxZGrVdtJ3/tMQrD7lvlkhZGoVvNloyIKWOLuwm0Q06o3c4NOSR1Wb8aBT0vw5pFo8A3NUvquH/SR/qiaPVYdZtmOmA/e4ewXJfF68rarqjruo+KLD5NJs8NBnj6ler46Vo8fyHSZ5pu/iFxB5MKVN7gE9UbWlK2AmcSOF6faPLXYSq6Gw4Z6odNpfVaI5/ZSqHc8SZnlWtPph1V7zwOEloqt0Dc1zblrTBIyjU2hvzA+ox90iQ+uXjrhHrNHlMJIHX7wpbKiivWALH7RHAysxzYbPRan/wDrPWe4fwj9SqiwkvZXSSSWhmOCQRHIXr3wr8Ul9P8AQXTzvYIYT1HZeQKzp93UsbqnXomHsMiFnkxqapm2DK8ckz7A0+8DmDPZa1CrOZXlngXxLT1awpu3AVGgBwngrvrSuSBJH2XjTg4M+gg1kVo6CnU3cIgdJzkLMo1ii+cfZKx8S9junkdyqTa4Efuk66aI+qAq3VF1pEKbXYgLCvtZt7VkuduccBc7qPisyRTeR7DCqzpx+NOej0F7CWziBznhSpANEkgfdeZUvGNai4EPJ9iZn6qrqHjG5q1NoeGtiZBVf/h0L8bJ9nqzq1L+Wo36SmFVh/mC8cPiR5Df4jhP/UpHxVUbA853ClqRUvxskuz2B1VkH1T9EB1WJBK8lHjKpTn+MSemVVufiA+mc1M/lCUjGXhNez1mrXHUoRuBj1fheLXPxIrN+QOet3wn4ru9XqEPtarGtPzHhDi6MJYeOj05lWSYKn5gPOVn0KjjTbPUKywuAE8LOzKvRZ8z2Q6jgYwUMvKiXFTY0mSJEpicKMqFYwxSFE3OAyVXqvkkzhN/Kq1d4B5wEDI13kTlcz4m1OnpunXFzVIAY2R9egWzd1Q1hMjHuvE/ip4hde1m2Vu7+FTPrg4JW2DE8k1Zj5OX4oHB6tev1G+rXNUkvqOnPQKjOVI+6Yhe6lWj5tyt2xA4SIkpuE7kCF/Mr1D1UYHIEKi3LhKsUarqNQcQRGeyBMPU/wCWYEuiDPRTs3tALRJMSoxtns4Idm4NrGTiISfY02GuXAtmCICpUOSrtw0mi9/bhUaPzQkgLG2RKk0yY4TbiAESlByeVMmaRWgIePNLc+6O30sb7lVDis4q4wb2Y5blTMIuzW8J6xX8P+IrLUaDizyqoeQ3qByF+gXhHUqOt6HZ6jakOo3FJrxtjBI/+V8D2mmtu/Dta5pOHn0nAFnUhfQP/Cn4uqVBX0G6qE+UC6jJMAdv6qL3ZdNH0xT3AAFEBB4QxkKQMFWmQyaShuKcEkp2KiSSSSYhJJJIASSSSAEkkkgBJJJiQEAIlRcZKi6qwA5Cr1bum3lw/oocvspRYSqqdUATkINzqdJklz2/lYWoeJbS3BL6zB77lnKSSNox+zUuOJWTeAGcjqVy+qfEDTaLXH9TTxiJ/wC64/VPihp7dwbVEniDK5pyb6N48V2eg1XN3ETwgPrMA5XkNx8ULaTFUn7wsu4+KLJO1xj6rNwkzT5IV2e4mvTbgmUJ1zSP82R3XgVb4pdN2Pqqrvie8ydx9soWKfsTzQWj6EN2yYDxCgb6nMb/AN187O+J1WT6j+UJ3xKrHg5+pVfFIPngfRv6un0d+Ev1lOPmXziPiXcAfMfykPiVcbuUfFNh88D6NN7THLlL9U08OkL50/8A6lVhkOJ+pRx8TasZJ+xS4TWhrNA+hRdNOJT+e3uF4FR+KHG6Z7yrI+J7COT/APzJcJj+bH9n2TI7hKR3CFvb3/dIvHQT916Fo46Clw7pShh46wCnLoRaCqGrDcxYuoUucLYcdwKpXzNzeOnK5fJipRNMMqkc/tDakSFcty0DoBKzNSf5Ltx/0VYakGN+aB9V53j5VCXE754nKNo6PzQByFB9y0CJErmaussaCC4/lZt7r1NjT/EggL2E7RyqLWmddVvg0RuAH1VGtqoH8w/K87vvF9Ck07qrSQe65LVPH1JjjsqAj2OUnFsKSPZTrQFZo3iJ6lbtjd+c0ZEfVfLZ8f1HXtIMcYLwCvd/BupOvrWlUn5lNNPY2lJaPRLc7hhWgyYMKnZ/ItFgkFWcsuyDWncMcFEDJ9lIDhSCqhEQwDqVKD2U9oSdwU6FYN5G1U7iptbkqzUdhZd8/GMBc3kZfjib4oWzI1Gtk91mEkkkhHvam6qWjPugL4jz8/yTPUhHiqFwUSm3c7CFkhXLNhJk8LkwQ+SaSHLSL9k0N+nK0mPAaTKpU24HSEcHHC+x8LFwijiyv6C1KkQoebnKi7MShuEFer6Rz2O+r6clZGqVv4Jz3lXTwZWVqP8AynypmOJ5x4ycHNd7heV6jRAqPLhzwvUfFrQWu9XAXm2pEb3fRcbV9HdCX6nJ6gIDgCVg3RXQ6k0HdGBC5y8wSujEjnzMya5HmDPVWLfj7qrWEuHTKsW524hdr6POi/2Nu0MgRwti3I9Kw7IwAD16rZtyAG/ZcOXs9HFpG5a1NrRt5WrQu4IyuepVdoHVWBc56Bczx8jrWWlR0hvCQOoQ6lYFjnHoCsijdgjJ69091dAUTDsEGUo4qYpZLIWF15dSvcCN7SWj7/8AwuE8WXFS4vRvIIJkD3K6SrXLLDYz56js/nlcpc25q62yi1+4hw3GPfsvQwxo83O7Zn6lami2mC1wcWguJPJ5Vek3bBPHX6LqvHltSt7yjRocMpDcSOXYMwuXcdtKeYC6U7OWSpkDl7ndG8fVXbQCnbPJPOP3VNzdloCf5irrm7bSi2fU71fRDdIcex7akBLnYT1yJcD24U2NjYTzPCBcv31nED8FSaPRFzwLTbxmf2KpAel4I90e6IaxjAcnKrgnYfdaJURJorHlMnPKZaGQk6ZPHuEAbvhPXKmiak2q0nynYeF9DeG9Yp31rSq0ngscP3Xy6JBldh4H8U1dIuRRrPi2cevRcvkYeatdnf4Pk/HLi+j6Yt60t5lG81cto2qU7ig19OpuDhPK36NVr2gry2mnTPcUk+idzcmmxxHY/wBFxGveKbm1eabKb3Gf5ROO67O5aHiB+O6yzpzHvcXMBJEZAOELRpHtHmdx4orVnlry+ZmIOFTdrri/1Nd911uu6T5FbzGsAAOQQsb9LQe8F7BE9lsmmtHt+LHlHRi1NWDiCN34UH6kHsO7d7Lpha6b6j5I3SI7DunZa6OHUt1NuQSY6co5I7Pjkckb+q938Km5x6QiNtNVuQDTovDTgdF11rTsKdMuZS9RB2kj37fRFrahADaQDRAGMR7p8iJYpS7ZylPw9fOaHVqgZOVN2gUaRBq1H1CRMe633Xe70mSjWFlUu3klrtvSFLnRjljCCMnRdCpVbho8kET2lepaHpTLe3a2nTDREKGh6MyjsccHqD3XT0qQAIGOiylkbPH8jMm6RXps2CI+intPYo5p9Z49kOJWcjjsG4gBCe6Y6IrsoNTGUh+hFxnBUHHGMqLpmZwgmrjj90BRJ9Q9FTrVOgUqtSAc8rA1nURb0TtI3ZEShbDrsy/Fmsi1tnUaR/iOBE9l4brjnPqOc75i4k+677XKrqr3veSTwuC1ZpD8nqu/xI8WeX503JGOkncIMJl6Z45E8pk55TwmMTPnH1Ra4O5p6EITcORquRTPskwbLf8A6De/9oQKB/8AMsA4JgojnkU2gD25UWkNqNdHCmgvZZuYFKoycArOpiH5WjVG6jUf/mMrP/8AVQugYblxhWqQitEcqo4w2QPurNIkEEjIgT91nI0XRXuARXcEa2eA4/0Svm/xy72Ce1aC4TmU30NdntfwL0a31i11SlWpNedktnosjSrq68D/ABGdd0aRZTo1Q1zePSTH911//Cy6odX1Ojt/him10988K3/xHaM2wv6F7RaW07gw8xj/AGFz79G3cbPpzSL1moabb3VEgsqsa4R7gFaI915B/wAOniP/ABXwbSs6rwa9ofLE8uaMA+37r13dhaR6RkySUpueCnKoQ4SPdR3BMajWjJQKiUpE9UF1wwfzKtW1GjTaS94CVoriy/u90g/3XIap4x06xYTWuabYBzMrgvEHxm0WxDmi5bUcJw3P9EnNLaFSR7S64DeSFTr6rQpH1PaIPUr5Z174/Pc1zdPol7pwchee6z8W/EGouftqiiDOAcpcpMVo+0L/AMXWFqD5tdjfqVyer/FbRbKd9436AhfFmoeJtWvXF1e8rGfcrLqXFWoZqVHu7kmU1CTWyef8PrLV/j3plFp8ne8xwIzlcRrHx+uKhi0tzk9ei+fy49P3Tb/uhY/sHkken6r8YtdvQQ1wptMx7Llb7xnq95Jq3bjPQLmSZMpEiMBV8USbZdrapd1p313me5hVjc1Sc1HfmUFJVS+gthTWeep/KgXE9U0pkUvQDkk8pQmTjKaoBJ00pSqtCHSTSlKLQCTtPRMmUy2AlLce5/CikkM/SnzgnbVniVS833Um1DmFjZ2UX2uBHuph3cqi2r6c8ojHmUuQcbLW6eEKvkR9k7XzMpnuk/RKS5KmEVUtHLeJqUUXECSM/sV5pqerPtmO3EwCegXrGtU/MoVB0heNeKKQa+qO0hfLeXyxZ04nueLU4HH634xqUapAd9BK5m/8WXdzuDXEd88rO8VyyvkcYWC2qSIGSvqfFfKCZ5XlTcZtIuXmoXFUnzKjvysmtUe4iXElW6lKq/8AkcSq7rO4ccUnfhdRwy5MhbPIrMPBBmfovqj4VXPnaXb94AJXy7Ss64qAmk4RxK+kPgtv/wANpB7SIPVYZTqwpqOz3Swg02ytam0BowsfTfkatyj/AMsIijKbpkQ0JAAIm0JbQtDOyCZxhplTMAKtVfBwonKkVFWV7l4glYOp3ENxyr1/XDA4zzK5q8qmrUxkd183+T8ziuKPT8bHq2DLi4k9e6SXZOAThokr5STcpWvZ1kqLS94E4kLYoUgGtI+iDp9qIBPMLWo0SIkL6H8b4b02jmzZK0QpsJgI3lYCs06XBhTNOMnhfTYsfE4ZTbKLqZxCh5ZPIV8sGE3l9VtRHIxqrNskYC5zW65pUnE8QV2FxT9Jx0XHeI6ZFFxAPtCzmqRcXs8x8Vaiwh88wvN9SujvJBwcQtvxjcuo3jwQRK4a6uvMecjHZRCFlSzJaQ95VDgeywb6CT94V6vUlZtxncfqt4xSMJzbRmVwA4R3RKJyo1QQ7IUG4dPRbVaMFpmtb1PUBOFp0a8Dlc/RqZCs+fAiVi8fs6FlpG+26wM/spi7JMT+ywm3B2qbawkKXjKWds3qdziZKa4uZaGzhZAqgkZ6KfmOc6TwOqPjRSyt6D3V01r2l59IzHeEPwZY/wCJ6+6rVO0NPmTPb/4VC4cw0XEnkrW8C1XUq10KT9jnt2g/X/ZVpUqMpO5FTxyyozWqjnuc5lT1NJA46LlqriTtC6bxzci51sim4PZSptpg8THP7rnD8xKuC9mc1sJt82pRpt4H9VdvQPODW42CAhae0Cqx3Ukn7BTk1au73kKZSNIpUEP/ACyerW5+6ptaXDmC48q3dbQyDy4yf7KnXdsEznqnF2geincVN9Unjbge6cD0fuhOkj6ZRQQWGOFt6OdO2yqeUyc8plYhJ+U7eE6QrEkSYjomcooCjuvAviupY12W10/+GSA1x6L2nStUFam3Y6ZzK+XpjjkLvPAvit1rWZaXr4pEgNcTwuTyPH5fsj0/E8zj+kz6FY4vaCT0lWKTQW5yue0m/bVZTIeC0gRmcLet6m/rIhec01pnsJ3tA76wZdUnMLQBGTyuL1TQalu4uo+pi9EBDiO6d9FtXDmpJtHVh8meJ2jx+pZVA8y0tIPdVzavbAABxhes3OjUqhkAA+4VJ/h+o4y1ojphPmzvj+S1s81ZTqtYCBz0VihYXFZwhhP7r0EeGLhxG1ob7dFdt/DVRrg6pUAA/wAvVJzl6FL8lrRxmnaAGkOrmSYwcR7LrdK01tJohoAAW5baTTpQYJjkq35DW/KpakzzsvlOb7KtJoazhHaIEjqplgmCmiB7JJUc/uxc4QKnpOFOrwEOUNWJglXqHPOFYqkTCquMElSNOwdUlrcKq8wPVxKJXqckHhY+p6iKDC0EFx4CNjugerai2hScG88ALj7ys+s5znkknMHorNd76lUvfyVTrjJwFpGKRnJ2c/qg+acri9ZYN4jGV3Gosy6RC4/WmHzKe3uu7Bpnm+RtHOVPnhRMYR7sRWIjJQCCu9dHksaEkkkwFA+6k6doUYPZM7hAFis4hjBOeU5Et+6E8ja0dVJ5JEN7SgEXdxNINPBCon/mlHoncyesYQzBeSOVK6ZVD1OArVvl7ZyIVV+RCtW0B/sFEnZcERuM057GFK1E1BHB4Rds+YXYaevdK2G05U8qVDX+j6Q/4Wqbf1V+4Ab9oE/ddX/xG0vP8N0WlkmjV3T/AFWb/wAOWkOttBF6QQa78z2W38earRoLwG7i5wGO0rnujojHRyH/AA71a9pf1nNZto1PV9iZ/oV9L0aofSa45wvnj4TUG6XfWVX5aFe3k7uhAzHZew1NdoW42OqtgCRlOM6B49HUioAh1Lpg/mAXner+PLC0YS+4YCD3XnXif42afZtLbeqKzhIgf9lbn9IzfGO2e73esUqIJNQADquY1rx7pdgCa91TaRzLgvk7xP8AF7WNTc5ts40mHH2XAXurXt68uubqrUkkkbiEcJS7MXlX/U+qPE/xz02w3ttXtqPjAByvI/Evxp1rUHVG2gNGmcbvqvJg4ky457JF3ZarEkRzkzZ1HxHqupOc66u6rpkwHR/RZNSq959T3E/VQ3YTK1BIkeXQcn3TCcEdEklVDE4uPKaE6SXECKSSk0E8KBjQexSPsZSIgwnVJWIikpdOU8HHvwjiBBJTc0jkKIaf/lJ0hjJx7qe0ngT9AnFJ54afwhV9isHwnbzClBmIM/RMGz7odeh1ZBJE2eyW0dkh8QaSnA7FPGEWHEj6UvSit4HT6p0aFR+h8qVMxKeGHt+VJoAmAua3Z2pDhOHdikmAAMhNDDU3nvyiOdAIlV29SpIfQk6ZTvwX0nDvhea67pbri4f6Tn2Xp9zBBAySsavYb6m6F5Hk+P8AJOz0fHyqETyG/wDAlK8MuYM54Ubf4b24H/JEj2XtVDTm4G0fWFdZpwAGAfsvWwR4RRy5cilI8Yo+AqLceQwAeyuf+CLeCPJZ9YXrv+HiflH4TmwH+X9lsZ80jxup4JoD/wBJo6cSuu8I6Q3Th5bWACeRhdjU09pHyj8Jre2FN0kA+yxkzT5E1RoWLdrBlbNJ3oGFlWwggRCv037WwZKuL0cs0WST2SBH0QfM9v3UKtaG4/qq5UZ1ZOtUHdZd3dAAj+6jd3ezE/hYl3dcwV5nneWsa0d2HA3TBahcl74HuqDQnLt2SeqQXxPk53llZ6KVKhj3V7TbcvcHHuqtCn5lQN/OF0llbhjAIAXT+P8AFeWXJkZJ8VQa3pbQICu0qeEqTNoEq1TAA7Ffa4MPFI8zLksGWOOCEtpJzwju4UCuujGyGzvCY0weqIng9kUKilUpyMhY2pWAq03SJ56Lowz3/KFVoB3AClxstSPDvGfgZmoFzgyCeoC8p1/wDdWRe6jJHML63ubEOGWgyuV17Q2VWPOyR9OFm049AoqWz44vbKrb1HMrNLSD9lm1qZOF7R8QfDjWeY9jIAJK8lr0YcZBHRaxdmcouLMOrRg5QdgHK07hm0qk4CZWpm9MEBBwnEnqVIQeIKWBnp3SegHBMRKm2ZycJqdN1UkMBJ9grdPTbuoQWW9RwnlrSUrXbEk70Rt5qVA2cz+y0m2dx5Tn+U7a1pcTxA7rsfAXgypdanaG/a5lFzw5wI4aM5PRdH8U7KjprDStqYpsrgENbAAZOPysZZLlUTtw4dXI8Wu2kNZTeI3Gfsrmjl1CajPS1oc4+6WqNNS4LmtljGx9O5SuG/pdOYGky8bvorT+yXDdmRXf5tZ9QzLjKrcB0/8AyVe8sOpgbPUBJPRVXMaKzWPMAZPWVcTGS2WrVhZTc/8AyjA+oU6I3AED0nqjuAFiCGkb8gdeVFxFKiSTAAHB/ZZ9s3SVFa5eJL3cDAjqVlXFWTHfKsVKhf6jgA4/1VJ5DnkjklbQijnySrSEXYMBGYP4MjsgOaQrLBFJ84xhWzOBTKSZJWIcFSUFIc5KQmIiUoT/ANEkCInlIEggjkJkkyj0fwD4udbuZZ3rzEhrHHgL2vSL1lVgIcDIBkL5QY4tcC0wV6V8PfGLqL2Wd6/Mw15/ouHyfHv9onqeH5dfpM+g6Bkj36rRY0SI6LA0S8bc0Wua4EQIMrfouke4XntVpnrJ2tFhlJp5VmnbiIzAQaTpjEQrbXhoACpUZO7JbG+6Wxvb91HzAl5nsqDY7m4OUI0wBKkXyeE26AZUspA3tCrkSD/RWDUb1j8qu5wkkGBMqLGBc0cA5Qnkt6J6lQSYie6p1qx7lQwoeu8RKzq9baCeqe6qwwkmB/3WBqeoxLaR9R5PZJDdBNV1AMYQwy49Fz1Vznnc90noeyI6XEucZcoq0iW2A2E8KtXZz3yrz8NJVOvkZ91aIfRiX7J3H9lyOuUyC13QGF2102W9vdc3rNEGgSWzldON0zjyxtHFai3bW3d1UBha2o0y63DoMg9lkL0Yu0ePONOhJJJKiRJROEkvqgByZj6KbflJ9oQzhWKdM+WgEPbmAB3lDBioQrFBgAMnhAj+JKkq9By3JkYAVmwphznTkIbo/Tu79CrWmUztJzPP7KH0ax7BXIdua3gTCLYUjWumUwIBMFTqt/iguMgQr/h7yxqjH1BLA6Rjss29Div2Pr34cCho3hWlT3jcGAjPEheffGXxQ2r+msGVGvh+9/H9Vyt/49c2yNC3dBDdsNMcBefapqdS8uXPquJd3Jlc/Fs6W0dzeeNTZULNtq4zbNgx1lczrPxO1K8qRTqOY2I3TyuUuqrvJfBMwVgvA3nHVb48a9nNlm7pGpqWtX17Vca9zUcD/wBRWa5xd1QxJ6pxyteKRz1aJDHBShJKVaSQCTpJJ1QCII7JQT1Clk8ZKLRtLiu70U3GewKhuvYIBBCc4icrbsvDGpXbhtoPA9myul0/4balX2lzHZ6kJPLGJaxyZ58M8/ZT8t/Rs/Re2aX8I6lRrDVa6eshdno3weo4NamY7gLF+QjRYJHzPSsbip8lJ8/RaNt4c1Ku0FlvUM+y+wNM+Fmm0ID6TXHsQF1Nh4N0y1AFO1p49kc5PZXwpdnxbZeAdauYi0qQeu0robD4Oa9cwTbkfZfZNto9rSwykxsdmhW2W1NvDGj2AhFyY/jij5P034C6lVg1qnl56rorH/h7ph4N1czJBwvpNlJoBgAZRRTaIwJS39i+OJ4Pa/ATRKYb5odUPuStm1+Cnh2mIdaNMd17CGtmYypcZAQo2PhE8tp/CDw1TIIsaZ+yLU+FHh51MhunU/rC9Qj2SMQU+KDij5D+NPw+s9Bpfq7Gm2mycjiMLw2qG7pHPZfXf/Eoz/6UrEYM/wBl8gl05V4zOdJ6H25hSbTJyVBHokkCCVoQQ8kroPBPhmp4h1ujaMaXNJ9YHZZjWS3IyvU/+HwR4yBHbKibaRUV9noVv8BtIqWjJpVg+BJDuqA/4AabuMPuB9yvoy2AFFp4RcdIWXG/Zpwj9GMKXX+6fYrflpiyOUzW0VC3KYg8lW9p6KLmE4RsCryVLd2CI+mQRCGWEThIAZALpKmKQPKbYcFGY0rL47ZqnSDUKbcQFZFMA/8AdCoiB9FYaTK6YxpHPLsjs/3KYsgSieoj2UDxCbVkFeqwZPdB2CFZqjHVVZIWUjaHRNhLY7qw2qeAVSLiU4ftKzU10VKNouuqEDKoXV1tBglQuLqGkcEArGvLkkkSMlc/k+SscdF4cXJ2yV1eScTKz3FzjPRO71Jivj/N8p5HR6UY8VQwCXWAMpyMI9pT31Jj6Lz4Q+SaRT0XNNtyMujK3qLRhZ9t6cBaNEkFfZ/j/HUII4c07ZbaMtjpCMwgnH7qs2pB6ZxwiB4B/wCy9taOBhnCCmKi124SpKvQEmcqSi3lSVIljRPKUBOknQA6rA4LH1CkHMcD1C3HcLKvgM8rORcDx/4i2dP9LWd7FfOeosivVAjaHGPyvpj4jMH6Ktn+WV81agJuas8ErKHdDm0YdzTJ3GQst4JO1rSTMBdHZ2FzqV2y3s6L6jnGMBe6fDf4O0KLqd7rdMVX8imW8fVaSyqBmsUsnR4r4V+H2t+IXsNvbvZSPL3DovY/DfwAoDZU1a4c8kZaBj3XvWm6ZbWNFtK2pNpsGAAFfaA0QFhzlLbN1gjHs4HSPhX4c00ACyp1CBy4croKHhTR6DYZYW46YYAuhHugXTopuExIwk0axS6RyrtEsxUqVm0mta0FogdvovHfida0ry6t6Jkve/YInDB/uV7nrLjSs20qZBe4hrQepPdeV+KG0xc1azyxrKDoD4zI6fss+XFnVBWjwbWLB+mOqNLcPJa1x7LA1i5qGoGMeQ1oAXf+K2tOmU7y6zVrue4N/wAoJ7f0XmdyXVKpJEbjx7LpxytWc2fSpA2VKhaS4uyck8QhUKb61ZzjH17q3Vp+TRHmEkEYA5PZRsYJictIJA7rTpHIo7LVxuc5tKmDtaBz2VLUHSQ1vyq5VrEeZ3JWXXfueS7EJRs2m0kVrl8ANBVZglw45U3kOeUzR6oPC6FpHDKWwjhIHsZUnvOzHTCG1gBnKlXG2mIzJR2NfZXMnKUJwICdWTZGE4GVKEyKoVkniGN+igOCiVegHCHBmEhjRKccJ0xKA7HTtc5jwWkhwyCmkSkPnCbWgXZ7N8LfFFR4FrduJ2gAOJXtVjctewFp56r5p8DsgF7cGRlew+H9VNKm2lXJMQQf9V5PkR/a0fQ+LNuFM9EbW4zwjtq7uCsG3v6bmiXAhW6VdhM7sFc6OumbPmtjum83sCs39Q3/ADJfqAOrY69U26BQbNB1YglRdWEcrMfeU28EFAqagzjChyQ1DZo1ayDUuBtjPCyal7IwQq9W9aGy9wEe6hsrj9mjWuGjrCzru8awbtw2rGvdcps3NYdzvbgLHq3NS5qBz3GOgTohtXRo32oPrelriAeSs7O7J56p6bDtMklS8se6qMTNkVA8o20dlF7QGyOVVE0DfDjJVWqyZjpPKtAIT6Z9R6IE9mZcUyQZWLqVuHUHCJOV0dZgnEke6o3FGWmVvBmUlo8+u6BfbubHqBIXNubtcQZBzyu8r0NtxVaRg8SuV1yzdQrb4w4YXbhn6PJ8jH7RkziExCSmWcEcHhdPRyEBynJkABTDe/KZjQXQgQ7aZcWgK45u1scwmt6IDpM+6I5hIdGROFm3s1jGkCotJ46p6DZDiRInH1R6DNlJx6QnbShjG95cUmwSZCuC22EgDc6IWjYN2UzJEbeirXkfwacfKJP3yrNNrhbicFyzm9GsFsBVBIDxxxCuWs0mbgPVCalRL3MH8o5Vh42AtbBgTlRZtxIF4ogOe7Luir1nQQ8TDlVuKhrVXx04RwTUt27uQeipIzbRGs8bcysuuGzLTM9FarbiHE8SVTeFouzGewfdIHPVTDARypCmSMQrbshKkQBzCJTpPqP2saTP4WjpenG5rsZGT7SvZfA3w4/WtpvfS54JCzlNIqMHLo8bstAvLhwLWOA+i6rSfh9e3T2b6biD1gr6Z0X4c2lqxpewO65A/wBF1Vr4etbZoDKTfwsXkk+jdYEfO+i/CfeWec2AD1C7fSvhvaWwE0WuIHJC9fbYsbhrAB9EelbMacNjCzpvs2jGMdI4TTvB1tTAAoMbHsumsfDlvTY3c1uIW40NaIgfhEa8AYj8JpJdj5ekV6GnW9JoAptwrjGMbw0QoNcDypbgByquInfYaG9AZUmmOZQ93blOHd1VmbVhg7CUoYdjlSz3TsVNhafVT4CC120Yj7og9QnuEMEPuHukDKZ2QEwkGEE3ToLuEJuQUw4CRMNMID2jxL/iT9fhOvHtz9F8dj5V9j/8Rzf/AKUuBngf0Xx2WgCFeN+jKf8AoYAq1aAz91Xb1VqhgiOq0JLK9X/4e6YPip78SAF5ROV6t/w+PLfEzwAOAoydFR7Pr+i/+C0ZmB/RE3BV6B/hN9wEWfoszSQo9kononYQ44U2iSUWDTTpkNkieEEswrJBBiJS2g4ghNKyuioWzyl5c9VadSBKQoiU+I1Iq+T9PwkKZa72V3yvZIUh7ooHL6AsEA4/ZTbMlFFPjspBoEqrM7Y1MDYMJnBpGOCiAQmgAcJNUBXq01WNIZn9wtAZ5Q30wSSokrNFL7Mqs3ZlVKtbo3JWpc0S7juq36UNBlq5JpxN4tGBdVXbjk5VZwk5H5XQXFpTcOIKxbtppv2xC8Dz5ySdnZiorGAcJOKTshNyvm5Sts6F1QokiFoWo2tHAP8A3VJg9Ss05BgcL0fAw8pWZzZpUHyQFdY/afZZ9s0yFdbkdl9h40aR5+V7LYMkEdTKKCCqbXOEAIu4grtRhRYDiOCY9kUVMR17qsx/pyMqYcJTsVFhhycqc+6rh0FS3dYTsRakd0kBrnSJiFMOnhPmKgjiIP0WbfRB4V/dLc8rOvnSHeyTdlQR5b8Rv/sKv0K+d6el19T1V1vatLnOdBgTyvozx1Sfc0306Y3OdiPrKrfDfwXS0um68uaYNeoZMiQFyuVS0axxubpkfhr8PrXRbencXDA64I3ZHBXp1MN2NAx95QBAaGiBH4US4tcYKh72dKilpGi0qQIMwVQbcANMp/OES12eoVJ0S4sv7lTvXg+jmOUI3bWCHmPqVRq3lN4cd0E9U1JFRxuyrrNwymHVqp2iiwuB94XifjDUXVNLcwFsV6hqHHuQP6r0vxZf29LTyx1Rpc+WmT3XgvizUv1V1VFu7/y7C1hxjH/ws4/szf8AyjnvGN3WqVLazdUD3swQ3pj+0rm61Go2JaC4dIW263P+LU6l02AfVJVXU3l9R7w2D0+nuumL9HHkXLZzd9WJ2g/N0/1U7GKbaj3GTGPqgDbVuJJ9IySjVHAUHFoILjAH9Vs+qOZP2IPLqbnE9clUro+k/wCY9lsWekX13Sb+mtqtRrjtkMMSffhFvvDF5bPJvaZpEgOAPMH7qscHZGSejlY9QRIgdPZdLS0FtFwdUp1KrIBgGBnP/Zet+DvDGg3uki5p6S0VQdjhUJfkRPIVZp/GrYePged0jwahRqPhwpuPTDSUW5sbp+0Mtqxnswr6ns9Ds6FINpWtGmBmG0wtBtlTG0tY0QeghcX/ADa6R6a/G6ps+SqOh6rWE0tOunfSk7/RaFp4J8R3RBo6Rdlp6lm2PyvrahRIED09MYVylbtiTklD85+kT/8AzYp1Z8w6X8IvEN4AbhtG0YT/AOo8Ej7Bdfo/wZtKT92qXz6xBnZTG0H/AEXuRoMng/dQdTBI2tH4WcvLm+jeHg447aOAsvAPh23pmmzSadT/AKqo3O9lN/gXw7Up7X6PbAzyGld2WOMua0fYJNptOXD1dVl80vs6Phx+kcCfhl4aqO//ALYxo9nEKtcfCjw44emze0/9LyvTtoI4CiaYQs0/sXwY/o8fuvgzotRrjSfc0jE4cDH5WY/4J2rjFLUK7R/1NC9xNMCZ4UA1vaPqr+fJ9kPxcT9HlWj/AAxZpfpZePeP/bC26XhapRE7y4D7yu6IaDkpgN+GZJxhS5OW2axhGGkjh36Xf0p/TvDsfKcGVvaF4a1CuwVb+5NFpE+UzJ+54XU2Wnj5qomO61aYDRB6JpWEsrOWqeGW52Vq89PVhZt34b1Bgc6hdzjAc39pXe+YP8olReQ8QRhKUEtiWZ2eOasdZ00bq1m6tT6mnkx3josF3icucWmjUY4chwXu1xRY+YaB+6wNW8KaXqZJuLZofzvaIM+6z4o0+Rs8kd4jqP8ATTZ+T1VWtf3NxPmVI9hhdzqHw4azc6yrOHYEDC5y88JanZudNLzRPI5hLgkS5tmTb+o5zP3V+mM8IIta1B22rSe0jmQVYpzMQSB1CfsSDniEiITSnTVWAkkkgJTAi4ekoT+APbKOcFM4A9EEtWZ9Vo+mVXqU5BHIWhUYCchRFIfRFsTWjmtTswHh7Wz3hY+u6ULiwcWiXBstIC7qrbtexwjMRlZYtwwVKVQS0yAYW0JvTOXLitHjBplrnNIy09lOkAKJMgkcStnxPphtbh72NIYSZhYjJj2XpRnas8bJDhKiBMg9FYtaW4SRyo+V2HJ4WpaWzgwSOnMcJTkkPHjvYzKJFAuDT2mEB0kBgHSVr1g2nQA4EfkqhaW7qlRzgJH9lkpHRKH0SoN9DWOwOspid9zJwxvbqiUm77iqzgNGZQuGbjMSqszqhntNS5biS8wBC07ilBY0YPZA01gdXY9wwMhFuqv8YOPAUS2awWrZbphlKlLgJj7rJvrp3m7GDnspXVw+SJxCzwKlV0tEuRGP2LJK+iPmEPmMK20n9O4icmcK7pei17uP4Zz7Lq6HhKp+lzTjE5CrRlGMmjgA4+WWuHKF5bXYE/dbmt6cbNzmubEcLE3chWuiHd0x2UgRhWregCRwEGicK/R+ZJsFs3fCVNp1ai0jEr698D2tKnY0SwDgL5B8Lk/41bgdXL7E8Egt0+hPVoWGR20dOJKmdXsA46IVWAOcooMBV6jucJ7XRVshUcImcgZQXVQBz+E9TAJ7hV3nH1WVlBfO/wByk2rkf6qoXdkt2EuY6L3nN7j8pxVx0VDcVIVCBCXIfFF9tXIUxV5z+6zhVIUhVPZNSYuKNEVYPP7ozasmZ/dZrSSAVYpE9VakyWqLwdPKMwxCrNMgI7BEfZaIhhZBhJvKYCE45QZ+yY4TjgqI5apDk/RNKwu2eLf8SWPCVx0XxyTPC+xv+JP/APEK47L44ATxqmzOfZJvVWaES3PZVm9Uej8w+v8Adakls8r1b/h8IPid4GSQIXlPMFeqf8PX/wCXu/8AaFnkVlR7PsGg3+C2BmOylt9lKgJpNz0RDhSkaSMq3uZ6q9TqgrmqDnD5uVo29Uhc0Mj9no5sCXRuNeOU4cHFZ9OsZVjzJzK3jOziljaLJEJkFtQwitqDiQrT0S1QQGU6gH90t4VKrJpk4ShDL4GIlCqVoESEnJDUWwjqm0xKbze6qVLprWAFwCzq+oBpyQs5ZOJvDBKXo3BUATCqudbqjS4eoLStbveBBErOOZNUh5PFlHbNFoLyoVqeOEqbyXSOEfcHDPQQtJQ5IwtxZk1WHd9FjatTktdGeF01Zo5+6xb+jvMjovB/I+M3FpHbhnbOedIMFIDOVYuaR3GUBrXOPpBlfKPBLlxO5NCZ860LZm7nhDtbUkyRlbFtbDaJX1H43xXHbOXPkS6JUqQaN0KYai7A3ITR3C+ghFRRwt3tkNoT57qeEo9lY9DMcdqlTJ3jKZSByiyWgoKI04BQmESZKmMiQi7JaCieSU8wgKbSRKVAgu6Qs++MbvfCtuMNMlU7ggtzxym2VFbo546e2vcCpVAMd1pMaGN2tAgCOFEuBn64Um8j6rjbtnWouKQgCTjKXlnt+6M0g8JEwJPCCkVqlDcP9FTq2zoJa4j7rS3t7/sgVCCTCT6KMO9bVLIc6fphY9VrywzJjpK6O8aHSQckLny8U21A6S4KGbRXRwXiphqTUAhrag646yF5Rp1AXOpXVV7XOpUy5wYONwnleueJ3+Zp9UU2ncH59l594em3NyXNmo55+2SqhrYTjbM+/ay6v7es5pFMCA09xiFyfiO4DDXYPS4mPpC7TWarGW5qGBTogwPcleY6pU/UXRqTMH6fZdOPZy+RLiqKVETgna0lFrkl8j5W4A7qNIE1W7vlGZ7IzgGgluZAiey3vZwro9M8Pa7aVfC1a3dXq0r6kGi3p0yIc7iCOkd/dU7Rl75j6LrIvFUbS80S5wz3PH1Xnde4dbPpvouLKgO4OC9m8D/Fa0r29G11oNo3DQGitt9LvrytHmlGNpDx4YZJcZMr6T4N1u9rUmvmhROTVPbP45XrWkac3TNOZbNfu2mS48uJ7qvQ120rtDqFxRqtMfI6fzHCN/iLCZ3COv0Xn+R5Ep6aPZ8fxIYtxZcMAZTAtH5VCrqFPhhwq1bUWMHqeB7nAXEk/o79V2b1Ot+Fap1QWiFyFLW7Mug3FGW4+cdPurX+N2m0fx6U/wDvTUJL0TJxb7Oo81oPqdlRFZoOSCFyztbtifTXpR/7v+6DV1q32ki4p/8A8yOMif19M6qtdtBgcFRbXaQuObrdEgltRjgexlHpaoKjQ5rhHbqipFVF+zrv1NP3TOumASTgLlf8SaBO9sdif7codbUN8CRCSsFFHSVdSYCQ1V36k1oyZ7D3WTYWdxqDh5LdrJAL3dfddRpmiUbUte8GpVGS5xnPsrSbZM3GIKzo3N3Dtops7nqFt2tqyiOAT3lGawxiI9kVbxgkc8pt9DJQpdFAv9UNyjjRFic6BlR3k/KCUWjSz6hBR20hmUmFlAuJOQmL4yeFfdSmYEzwgm3cMkKaK5IqE7jhQdTa/kAn6K4aZHdLyz7ophf9MqrptvWDt9Njp5kArHvPCdhXksp+W48lswuuFOeAUhSJxCQckjzS88H16YPkVN3/ALsLAudKv7ZxD6Dto/mAle1m2e4AFvPuE36EOBD2iOiOH0J5EeFQRAIPv0Uw2Oq9jvfDljc/86g1xPJ4WLfeA7KqCaD6lE9MyPwhqSGpxZ5rjqnAC6u78BXtETb1adUf5T6Z/ssO90bULNzhXtKjQCcgbhjqIS37C0zPgdlEsaTMKZwYIIPEQmBlAxNYMYVO+tw4YAnmVfgRB4UaolsHAHCE9hSa0ed6/al1KqyrzyCVwVSlscWL13XrZr2PJEkiPqvM7ijuuXb8GV3YJ6PL8rFUgWm2hr1mgDrC7az0Qup5BwO3sj+AvD7btwaWhrpBDndgeF19xbCzqupPaS7I7yFhmzrlSNsHj1G2eX3+nVHXPltBdBxiFqDTW2WkvqFvr2TK7RljQLvMd85MQMwFzvjiLe0NMYLsQnjzW6LlgUYuRwFuXbatQHLplTq+qnRpgc4KegCKXlHh3P1V7SLV95rNGhTZvDSCQew5XQ5HnpW9m/4f8M3OoYtqbi0ACR3ITa34E1i1ftFB7jM4C9q+FVkG0qj6jA6mXuER8oxA/ZepDTberuL6bXdAY/1XLLNK9Hc8EeOj4uPhTVn1vVa1No/6V1HhzwTVc6a1Eg+4K+sKGi2rmQaLceyl/wCH7OZbTaD3T+WT0c68dJ2eReHvBjaVKmX04n2XU1PC7Rbua2nJ28wu+t9Jp03ADorzrRgp8T7rRLkh/wCT5D+LPh19q01GMAHdePGd2eeD9V9jfFPwTqXien+l0prWPJMvcehXmmm/8OWtmuHXl5Sazk7R1W0JUqZxZH+2keF0sLQogAY5Xrup/wDD74ht6znWL6NSn0kxK43Wvh/4g0En9bZP2gnLMhVyRMSn4SzrtqD1OfyF9ieDAP0FH/2hfHfhUupa5byCPVEHkfZfX3g+pNhROcgHhZTf7HTh6Ore+G+6rVHYwpFxiSFWq1DBSbLSZFziZn5QgVagCjVqZygu9TscLIuie6eOEpKZNuHdFFkpKUlQ3t7pb290aDROSjUmg8lVmu9OMkI9KBEp/wBEw9OSYPRWm8BCpnKsUuArijOQelwPqrNPIQqZE5RwMz0laoyl0SAkqW0JNgmAiNEDKCUkQAyFICA7vCkOQpP4MclNIX0eK/8AEcxp8I3OOn9l8anJX2t/xBU/M8H3gI4aF8Uu+b6KsZE+x29Uej8w+v8AdAb1Vi3+YR0Kv2QWm/svVf8Ah8B/8W4xheVjqvWf+HcE+KapAnAUz6Kj2fYNp/yQD2CMWAoVqP4IHVHhQjSXZzptXDopNolq2XUjBx+yh5E8tS+JHV/yb0zPYwgqw0GFZFEDopeXAwqWNEyyplZoI7pF5BRzTJKDUpxwm4/Qk0yBrDqVA3QAyUK4aQDyse9q1GAws5ScejpxYVM1q1+xoPqWVd6xTZPqXM6ld3Q3Bnv91zF47UKr3EyAuKfkP0j2PF/GRl2zrr/xHTZMuGPdcrqPitxJ2En7rFuba7cSHArMuLOu0EOa5cU8s5n0Xi/jvHh2aVTxVctqgiR/v6Lt/CfiMXLQXvMxnPK8lr03tBDmnHsrGiaibG6HqIYeiiE3B2aeZ+OxZMf6I+ldPvGvYDMyO60BUDhIK808Pa6H27DuBBC6u21VjmgDBOeV6eLOpdnwnk+JLFKjdqukc9FRuCIPWUL9YHDlBqVwTCeZKSObGnHsC+kHHOESjaAEYlR8xvUqxRrgwF58PFgpXJHQ5tKizQtm9PZWWMDUKjXAA4IKOKjHZI5916uKKj/k45tsUDsltCRe2FB1QdMLayKYnNCg4QmqV2gYH7qu+5A+iTaKSDptw7hUql5HCqVL0jl2OqhtFUzYc9o5ICb9Q1uNy56pqLGTLh9yq1TV6TR84nuUvkoOLOq/VN/zfukbpo6rjKviChTx5gVO48XWtMH+I3COYcTval23aBPOOVUuavoABklcVpniqlqN2KNEhx6wunpPNQyZ4WUsjqjXHD2HYcRCIOAhsGFOVnGzeWybXbSpOfIiEOU0lUTQi4AYOUGucD7qR4KHVMt57o6K4lC5cQCeVz104B7weoW/c5Z7rnL4OD5JP4Wcns6II5bxEzZbVhty4T9IXmmkXIFndeYCXlx2dOpXp3iOt6SI9JBBP2XkNm99LUKtIg+WSYn3lVj3oeR1sztfvm1KAtmkk5Lj+cLjr+kadWmxsgkSV2Or2tOjqBY0yBDj7k5XNXzhW1d7iIpj+gXXjPPzptmdVZsqBrTzEhSe6awZziZ7KNZ7XVy9v0Q6YPnbzj6rWjjutALkzUc2OeD2QXQJPREqmapKgcOWqSpGTu7LNG5r0RFGtVYOcORma5qdMei+uQI6PKpPdDABglBqS3HRHFN9FfNJKkzV/wDE+shu3/EbmPd5VavrOo1xFW9uHDsXlUnCFFNRj9EvPkfthP1FWSfNdJ6ypC4rDiq8fRxQYCkANswnS+ifkl9hP1Vx/wDr1P8A+YpG6uDzXqH/APiKEyCUwTUUP5J/ZYbe3IADa9QD2cVZt9b1K2M0L2u0/wDvP91TtbWtd1m0bam+pWcYDGiSSvXvAfwbur59O58SONtb8i3Zmq//AEWeRwgv2N8UcuT/ACznPCWq+Ltbu22ul+ZcyYL3tlrfcuX0L4S8IVrW1p1Ncqtu7z5iGthjT2jMkHqui0LQ7HR7Flrp9rSoUGiA1o6jkk8ytynTgYXn5Jxm9I9bCpwX7MBQtxTADRtEcAI7WgdFJzgBEIBrQ6IlQqRrthXODecBQdVH8skqDg949X4RGMDY/dDYEBucQJj90VrYOeVKAOE/J91P/wCg2mqHD5Mx+6m1xPdDBA904Mj0iEWQwzXQAOUSJxChSbIko7fZOhdEC09IHsl5Y6/0RdhP9Etkco4isH5beyYMAOBhG2jqpBuEUKweOycQTwpbBGQnAA4CqmKyG0EpeWOkfhEj2Sgp0FgXUpwc+6C+3mQQHAdCJCugdlIMMoqxcjltU8Ladf7nVbYNeR8zDEHvhcXqvga6twX2NQV2j+R2HR7dP6L14tTG33jLZ+yzlCxrI0fOt1QrWlU07ik+m/s4IBqgNl2F9Aap4ftL+lsuaTXg8dCPvyvOvEnw1rjfV0evP/8AhqDP2Klwa0jZZEzyrWKzS0ic9lwjLd9fUmtLAWuIBK7TxJpmqadeeRe2lai4mJLZB+6JoPha4raqKd1/Dphu89ymp8FRlKHyOzpfD9GjZ+XTpkFrG7sDPHCv3LhXqlwaMjk9FStbanYXtcNLnt4BPsrVSoY2gYOVyU27OuOlQ2yjSBq1shoJ6dl5T481EXVctYTl0j2HEfsu18QanSpUajXv2gDp19l5a55vL17yDEkhd3jwp2zh8vJa4oahT/hB9T7FdZ8OtLuLjUnXLJgktaYmf9hYAAqMZSptnjj917X4A0Ztj4ap1g0mtWxn3wVtmlS0YYMfLbO58B2n6Syaf/TqZdI6yu6pNdIMwJXP6PbeTQa2ZGB+Auhpk7onC5Ezrm10i7QdtJb7zyrIEgFVLf1Ek5OFeaBC2ijnloTcFFpNNSoB/KhcLUs6IDASMrbHHk7RhllSC0aLWNEASi7R2SwJykT/AEXYopHE3ZFzW/zAQsTWrC11Cg+lWpte10jK0Ly4LfSCqrXAgzmVhOSukbYoe2eXU/g/obNSN4GnzCZxgLt9N0ZlhSDKLiGgQJK2wQR0/CiQJWLj7OhUtGfUpPb1JVSq14/lMLbgFRdTa4cBOrGnRzj2yltK1qtkwyWmCqzqD6Z9TfT391FFFBwIxCEQROFomkHdEN1GJwgTaKB+kJAqy+hPCgaBBgqabFdgmGMRyrVPohMo4kqxTYTMKkmNvRYpq3R4VSi091aZgLSKqiGy2wYmeEdjpAHEKvTJwJwjjHC1MpBWjaZ5RAZEoTZ3KckCBhCdE/8AgVqThlQDtsEp9wccdExezyf4+j/6RvR/0L4ld8xX2x8fnR4Ru852r4nPKWPsia2O3qrFuIcM8lV29VZtuRK2ILQ5K9c/4c//AMpq/QLyResf8O7i3xW8TAIUZOio9n2Han+ED7Iyq2zooNJ7I3mA9FC6NJdlmExbKdJbUjOyOwJbApJwU6C2R8v2UXUpnCMkkHJmdWt2uCzbqwD+mFvvYChOpDspcLOjHncejj7nSmkgxwqNXSmT8srualu1zTjKo1LUb8hYvAjvxefJezjn6QwjDB+FQuNDpu+Zg/C9BbZtIyMqFSxYQYCh4Io6Yfk5R9nlV74cpuBhsLmtR8MgSWtOF7VW0/OW5CoXOlsfJ2/sueXjW9I9DD+akls8m0mncWMsefT0BXR22qOZGStjUNIaJhpC5y5tH0Xnbwsp4XDaM/I8mGfZ0FvqxxJiVcbqUmNwXHU9889Vp2zXQFyS8hx0zzpQS6Og/Wjq4SrVC73QJGe654sPKi64dQMTLunZKPk72RwVHZ0K4HzFX6dUFoghcZbXL3t57LToVn7R6l6WHOpLRzTxm++4DR6nAKrWvaYHzgfUqhUp1KzCCSD7KkdNrvcQTj6rdtvohQLtbU2DEjCo3GtBrSRzKc6JWdkv4yq1xolUCZnuEcZVYV6RQu9ffMNBMLEvder5gFbtbSCeW59lXOjMdILee6jg2XwfRxt54huROHfhYV7r988GGvXo9TQ6PBYD9kJ3h+i4yKYj7JrGxM8evNb1J3HmAe6wbvVNScXSX/gr3mp4YoEE+UD9QqzvCNqTJotz9E1CuzPi5PRifBLT6z21ry6JLiYbPRez0hGSsHw1p1KwtBTpsDROYC3QIGFz+zsiuMUiwzIkKSZogQnVJUMSafZOoFxBTGlZDdCrVX8xKO7hU65ImPdJ6LSRXrP6mVkXnqx+fotCs/vErNrkl0+yzaNo/RyviGkarQxmGkrznVrFlrfUblzsNdDj0g9V6nqbAaRJXBeJbY3FjWG2YkQOuFUGXNXGzidc21NXcafqbwD9uVxt28MvKgiOR911LKnrexxINNpA+y5K7BfcPc7kldcGeXnZRpNLnHpnqrVO2q1LV9wGHyWuDS/oCQSB+yT6WwgZnGEXfUFo5m4hhO4jpIEA/utU7Oal2zHriKpA4mENonIU6gIcTyO/dM1o2HlarZzsiM8JqjgI7qRw3b2QHncVS7JYtxS3FMpOZDZkJk0RHqOEVwinBQ6I9SnVdEDCAIUyZWv4a0K88QarSsbFhc93zHo1o6krLosNSqGMBLnYgCSZ4X0x8JvBo8P6SytXYP8AELkTWB5a3o3+6yz5VjWjr8Xx3mlvo2PAvgXTfDdo39NQp1LsiH3DmySfbsAu/taDG7SMnkk88IDWCmBA6cRCL5u0ScLynJy3I9uMFFVFF9r2Nb7/AEUTX3HiOghZ4uCTAwVYoNLzJSbYcaQYTUJk/ZFp0h16JMAb9OqcmTjhFUBJwycykASYCTRiRwrNBgAkp2S2BFJx6D8ojLd3JMKwNoTyFSRNsG2gwHhTFNo4EJi8KJqAdk6Jbdk+E7SQZQjUGTCj5ueiYyyHmEt+fZVjWH0SNcRjlAUi213dSkd1RZV5yp+bPdFk0W5HdKR3VXzPdO2pPunbFRbBwESkfUqO8RhEFUhITVGg3aR7ooaIELL8+OiO27aAJjATT9EU3sv+X9EgwKrTu2l0SFYZWa7qnr0J2TdTBHuo/ppHqhFYWn2RdwC0pGbbTMnUNGtb5hZc0adQDjc2YXF674PNCnWrWDdz4Po6/Zel7scIT2ghZ5MPJF48sonzXVbVpVajKrS2o0kOBxBVG/reXTMH1kGI4C9t8aeE6OqUX17Rrad6yXDs76rxTWrC431aD2GnUALXA4j3XG4cXT6O6E+aPNfEdya9wWUyTHzGf6LPt6GxsjJceF09fw5WBfVcxzgDAjr9fwr+jeG61zcscaJZSY2XOP8Av6LujlilSOOeCU5WYeh2Lq+q0ADDd4nHAkL6E0uk1htqDQBBDi3/ACgYC4bwp4afTrmvXaGgOnjkd/yu/wBNpOLv1Akb3QO4Ax/3XNmm5SOjHj4LZ1Fu4EtB5yeFqMcHEQCsuiCXiOg6rVpYEiJCSREnbD0HFpMdVZNQgA9CqrTPRNUcQ2R0Wr10ZPs0LE+fVxw0rebhoAWLoFM7C5wgnK2A7EFdeGNRs4c7uVImFXu64pNOcopdAJ6LntUut1YtB4WmXJxj/ScWPnIJUrGo6XcSpNqZ9lmCsYgQjipJEHJXDzd2d/x0jQ39VIOkKsxxxPVHbwtIyclszaQQOEp0MFPuKuyWhz1KG8Bwj3U3HlQk+yl9DRUrUdnqaSo02ipO75h3Vlwk5CrudsfIEqVLdDcbQ5pHoFB1uS4kALQpgPYHNyClsPv+FtS9GPKjM8kdgnbSiVoGjiYP4TeV7H8KVCh8mVqVLI7FWW0wCpBhBGEUMI5VpEtkGsMyEcOxEZ4UAI4TkqqIbZOcypAyFBOCRwkSnRLcISkbT9E38qQ+QoD6PJP+IRw/8I3X/tXxce6+0P8AiF//ABK6/wDb/ZfF57KsfZGT/Q7eqPQ5b9UBvVWKAy37LUgtjqvT/gI/Z4ubPUALzAdV6N8D3FvjCkPoon/kpdn2ZavPkBE3lV7R027fcIwKg2pPs0vunCZOFquzESccpklQiYMpJmp0CFCUJJIAi4Ywq5bJVk4BQ3NkyEmi4ugW0f7CW0Imw+yW0pMrkVa1KSI+6rVaMiI/ZaDmyAouYCMqaLU3Rz17a7hgLldVsoJ6fZd/csEHsuY1hnpJjosssbib4sjs4epR2PgDg9Arlu2COyhcNis73lEoYEL5ryk4yO2T0iw7j/RUa1MkyZV44EqvUghcvKiE6H0+sJLTAIhbNKpHtHVco6p5VXcCtOy1Fr2Q4r0fGz8dFOPJnT0L0NIaVo0a+6PT+y5SnXDSHFwhb+mV2VGiHD7r0seay5+PUbNYPJ6KvdVSGmR+QrQLYmVUvnN8srp5s54QTkczqerNt3QWwO/RUW69QqnaGgHhVfErg6A05CzNKtv1FUmF50vJnz4n0OPwcbxc2dCLplTLYH3TOug3GI7olDToZnKDqOmOdQdtJHY5XfDJJqzw/IxRhLRIXtLGRlTp12VXgNheS+K9Q1PRKziSXUR2kx9V0Hwt1evrL6lWrOxoiVc56OdRfo9RtmwwAfsrjBjIz7qvbiWgq0wEu4jC5zo7Jt4UunukBHKfjogiyJwokCeik8wOEIuycJjQGoYnJhUqp5k9+qt1TyFRqHBUy6NIf0pXHLln3R9OD1WhWMysy5d0jKzN0ZN9lp6rl9WADHQIwZXTXmAfdc1rAJDwOSiPZrWjyvWqJtL6rVHDh14yuWqN3VWHdJJmJmF6D4pty+3c+Pl5/C4SlhzgQNwOCu3G9Hl541Ia4pne0gE+8KhdOLMDqtevWiiI27wMrFuIc4mc9QtYnNlaRRrEkYmJ4Q8geyMTlBd2Hdbao46YzsCe/CgKRPf8LXsdNdXpio8egKxdWTabWBg6hS8lGqxNoxPJDWiRJlOQNkQJVy4YGnacoDw0O+yFLkKUFErADsFEiX/vwiFsFdL8P/Clx4r8RUrNjS22YQ+u/o1oz+eirlStkRg5Okjsvgf4LOpXn+NX9E/pKB/ggt+d2c/Qd19HUKAZEMEj2VTRtNt9Os6FpZ0xToUWhgAESB1PuVo1HbWleXmyOcrPfw4vjikhqjmtaSYCpy6o4iSB7lSl1UGflRW09wAOIWJuv6JgDcEjGJV2k/a2Md+VWDAG+pWKNMkguEDsjsUth2NNTJkdUTbHspAwAOgQ6lRs8rTVGV7JNftGIhIV9nLsKk+s1oIn3VOresYYJU2kWotmwbqBPMqIu+5/dYL9QB4PHsqtxqtOifXVCTnY1is6E3Z7kJv1c/zfkrhbvxTb0iIeXk8AKk7xUHOyHMHQxhLnRosSPRv1o3Rgn6p/1WOvHdcTY65Re3cKoLiOquM1PfGwgT7p8w+I6N904O5Md5KnTrw2SVz5upLdx91aZctgCSRhHOyXj1s3KdxulE83uf3WALxvmbWO4yrIucCXSqTM3B0bYqzwf3S83/crL/UtAyRPQJhde6pPZLjqzYFSQDITiqep/dZbbkxyk67gZKdomi++sZwSoOuNuf6lZpuZOChVK5cIlSxpGu29zg/urNvfdyfuVzbXZGVeoSGzOPdJMbijpKWocCcq9b3gd1H5XJtqEnEq5QrvHCtSaM3jTOrZUaTghTcMYWFQuXAc4WtRrg4ytVKznnDiTe0xwuD8feHxcD9fb0wKjBFQR8w7x1Xfl05CFdUW1aRa5ocCMgqZwTQ8c3FnidtprHN2vaPpELVstNYz0hmDziVu3mnNs7t7I9BMtP16KdOiBBbwuSmmd/K9oqU7QFpaQdsQcK7b0GtDWtAgcY4R2MACKxsHoitkMuW1IRiJV2nTEQY+6q0HBoz78K6z5SZW0ao5pJjlob1CBVy8NBMkwADyiuMAqNk0VL1mDgyVaVuiG6VnR2LBToNbgYCsIbIa2FJpmV3xVKjz3t2Av64o27iSJg8rj69fe/cTzPVa/ie5FNmwHJBXKGsXO5mVxeTk3R3+Jj9mjTqEu7hX6JwD1WXbgxJiVp24IK51/Dpk0i9SJjr91YbO0TKDSyAjBdEejml2TEdE6gpStCCMqDnDEKRyChEweFLKQ2+Of3Q3EE5j8p3uyMIFV8AiDlYt0aJWW9PrQ/aeq1Azrhc3SqllVpPErp7RwqUQesZXRhk5Kjl8iPF2B2n/ADBLy/orRpJeWtqZz3/SqABiJhOYPIRzSBMpvK7BFMLA7fZLb7IhYQeU+wopj0DaM5CYjOAihh7ykWkHlFMNA3AkpAGDjop7SkQWgyimL2eTfH5m7wjdemTs7L4o/mcvuH440nVfCV5tIHo6r4fcIe4e6eNbIy/6J0+qPSmcKvTMGO6s0cuH1WhPosjhei/BHPjGiRPI4XnXVekfAtpPi9kdIKnJ0OPZ9jWn/Ib9FYhCtAf0zfeEbaVmavZfSSSWhkJOmSTToCSUlRlJVyQUESUJ7JIWxUSPCiklKd0AySeT3TLMZF/RQPCKouCaZSZTrtwVzWsN/hujsunrNkFYmo0vQ4eyiatUa4nTPP70FtQnHMJqLiIlWtXpFtQ46n+qoUjDsr5nzNS2eitxtlxzxtVUuMGSjRPUqLKcTK4G9k+zNuRJnKzq9R1KS0nut+tSwcc8LH1CntZlXCWzr8dpumZz9bqMG0mCOMrZ0PxC+Wh7h+Vwmsv2PJbyAs/T9VNJ0EiQe69HCpXZ7SjCWPZ7rS14FnzcDugahrjfKPqA+8rzGhro2/MPyhX+vAMMVOkLtc5VRxYsONZLNjVdY824A3ey6Lwg7eJdGV5Vb3ouLxkunPVepeEazW02iQBCwx4v3tns+XPHHBxiei29u11MGESraMNPI55QdOrBzG+roFoVntLBBg9l6kUkj4vNbkzz/wAW6FQvbepTqtaQ4RwqfgLQGaNY1G0xlzj9l0+rFriRjoP3T2wDKTWys5S3Rtji6st0ekK1TA5Vah0VpSJsn/Mk7hM0gAyk4jbz0VWQ9shVAAH1QX4bIU3ndxmEKpkSOEehx07K9V2ZKpVyVZrGFQr1DmVlPs6IqwFY4Wbc8q5UM5VOv0Ulp7Rl3Yk/Zc5q7SA72nldLdA4KwtUaHGZnOUR0dCOQ123/wDJvLRLi37SvMHW9Q3NVxEGfsvYNSpg27x1/wCy8w1Ok8XjtnHC6sTOLyoWzAu/4ZyTKplkkuJ56LVr21Td/EGe3KG2g0PG5uByt4umeZLG2zErYJKixo8xoiZKvapSa1x2/wBFWpAsqsLmkCevWeDnotf6jLi0zudLt9un09zek/VUNW2tZ6QMcLpKVLzNJoPpD0lolc1q9OajWzkrmb3TOzdHO3J5ceuFRc1xdJ+y1q1v6jzxwqO0+ZEHHZa42ktnLNNukSsbSte3NG1tmb61UhrG+5K+rPhv4ToeGNApWYDXXRG6vUAyXHsewXmvwW8Kta4a1eMBe7FBpHAnle50iKdMd+y5fIz2+KPS8Xx+K5yLZc1rQAOkKlUeXvLGD6nsmq1gSYM5j7pvPt9PtzWvqzKNLq5x5+y5knJ0jsbUVbLVFkNwERktx1XGan8QrOg9tLT7O5uN5gVHAU2HjInn+2F1WlXIvLOlcNOHiT6pzwcjBVvDJbZnHLGb0zQZTgAkflT3huTz0hQLwGkzAVK4u2MBAgys3+vZok2i1VuQAVn3F3AJB/OFmahq9G1pufXqtpt9/wDTquP1PxfQc91O3L4P8+1RJt9FKKj2dTqGr06ALnvgjpySudraxVrViWANbyJzK5mvq9B1QucalR3uP7KNPXbRv/p1eeqKZSnFG7Vvbt5h1V4aegKqul5JeS76qNDU7Oq35iI6EcJ26np1SQyqQR0LUFKaYCrRBIMDCJ5ctgkwrdM0qzf4b2uSbR2PHOeEik16KFKiGVSBPQhXWVHs+VxB9kQ0ttUPESpmlJ3EQmAw1G5pkAOJ6ycpv8Ru907/AHj+yhVp+rEwobe4TToVmlZ6vsxUaQ7vMrVoaiyo0bagXKlgLpn9k7AWu9JMIsNHa0rwGNxH5lGbdZ9H7rlLe7c2N/HULQoXzXECnAjmVXMz4po6OnXccuOOkKLqxe4NZkDkrMo1DUje8R0yrtF7QYnjgo5C4FgNOOVIN9e0JvNH8pBVi0DZc4kFxBxzCLE0SoW5LpJ4V0emGNBJ5WdVuvLfsZl5wI/qr1pVa0BhMvPJ9lSaRDTZboMxBGDyjsYBxKjSIjBlFdEnsqMwlMkCQVco1S3Lf3VEOACIxwkQVSZDSNilcE8q5SeC3P7rFpVJOOiuUa4bEnKqMjGULBa9p/6ij5lMRUZke65qk7o7GV2LrgOaQSFx+pt8m/eAfS47h/v7LOaV2jbA30T8ycD6IlAneZVFr5IhXKBEDKizVxNKgZhXA/EBUaDhA+6sBxxKtdWYyVhHvwSVc0Nm5z6pzJwsu4eABByei39JZ5Ns0RmJW+FJyOfM6iaWVCs8U6ZceiiakjKpatcGlZVHey65VFWccf2dHI+Ib7z7t7Z+UkKhQy4EqtXealUuP8xJ/dWrVg3AziF5M25SPZhFQjo1rcCFo0hAVG3bDcK/S4hXBGWQtsMNEIoMgd0CjkEdkecELZKjFqwkpiQDyhpKrJomXENn2QCZ5U45Ki8yEXWgUbB1cCeqq1TCsVeZ6QqtTn7rCSN46QOSt7Qqxc1zTkgLnqkcSr2g19l9tJ5VYZ1LZl5EbidWRIKYN7qYCUFehZ5lkdoTp4KUFArI7QltClBSgosdg3CEyLBTFucoCwaZwkIqaMFPQ7PNPjMyfCV922L4Rqf81/8A7j/Vfe/xgH/0rff/ALZXwVV/51T/ANxUw7IntkW/MFbo/wAv2VVohwkK1R/l+y0EyyCeF6j8AWh/i4T0AXlzfmXq3/Ds2fGTv/b/AHUT6LWmmfYlo0fp2j2RDS91K1H8Bn0RCCTgJRVlciSShuKW4qqJomkmBlOkAkkkkCEkkkgBJJEwFDcUx0TSUNxS3FFBRNI8KG4pbiigoDVySsu+Z6StZ4xKz7segpSReN0zidco5JHuuebLahBHuut1pkgzwuUrmKvC+f8AyMN2enjdx2WqMOAkfdFFP/cIVtBHCu0gDMiV4zJZWqU8LE1hga3K6eo0BuAsHW6YcJhVB7KxNqWjy/xLVFPeey81v9Z/TVnCevdeh+LqDi2oB7rynU7ImqSRJnqvoPBgnG2b5vKnBUizU8UVRhk9uSgO8RV3ZcXEHuqjbKDjqitsRGQPuvTeOBwLyMrd2bWia+f1LN0tz0yvZPCGttcxh3YjuvBadsWPECOohdd4b1J9sdrieMLLJiS2jvweXOb4zPpjTNVp+UHb+fdaD9YYcbhj3XjOm684UWw4meMBX26xcVHlrAcdcLlnmcFR7GH8cs2z0C+1Ki6qAHgkkYlaNnceYRAwvPNOtri5rh7p2g913em0n0w0E9ljHJKT2Hl+NDBGkb1viFYVegIIlHkTyF1Lo8GfY5TEwIhInGCok4PdNCS9kSS0cTKr1TtRHOJjKBXJxlDuxxorV34JhZ9czKuVzhZ9UndE4MrN7OiP0Be5VLgiTCPXJgqnWMAwoLSKlyZELG1CmYx3la9Q7pJVC5G4EfYINUznLxktjmeQuYv7CkKuWgl3GF19xT/iOJ7LE1KnJDm8tyqUmuhSjyRyVzYtL5LYA9li17X+Pv4YOcLsLpkscSOhK555LqjqcYK6cTvs55Y17Oa1K3piau+ZOGgxhT8TatS1Y2Bo2zaItrdtCQAN0DBPutDWbJlRzPIb6iIwVgNoHe9jgJDuy6oSpUcGZOGkd54Sv/12iGg4AOpCBnlZN+zdcExG1UvDFw/TtQc1x/hOw4Fat49jqlWo2C368lc0uy8T5Ro56/fskNkvdgCFteB/CVXXtSYyHfpqZD6zh2xhZzbOpXquqO6n0/fovefhzpjNO0O3aAPMqHe8jkmI/soy5HFUi8WHlPZ0+m2VGyo06VuwMpMADWxAA7K1XqmRClVAa2faVWbNR8N+q5Gr2enx0kVNX1A6Vpde9DRUc0Q1pwJ7krzPxDqN9qNi3Ur26oW1m9wZ5ZrB1TAmRTGQOmV68+2p1rWpQuGB1J4IcCOQRlcPW+HOl07zzTXrvoc+VIgc/wCq7fGy48auRweVhnmdR6PPtLbqviC9ZaafXr1KLQWtJaDta45zxJ28L3nRrH/B9KoW7nhz2tAd0ysuyuNP0S1FGwt6dBjRGBk/U8rHvvElSs4+U2Y690vI8r5f8l+N4iw/6Oovb8U53Ohg5K5jVPETKe7yP4juPp7rn7yvc3dQ+ZUcGnIEwP2St7UgZyVxVfZ1tqqRl6rcXN7VdUr1HHmB2WcKZOC2T3XU1NP3HA5UTpRI4C09aMmmzln0yMxHsqN1Se700wc9l01/ZijyFmPp7ctweUXohooaLbVhVqedJJw2ei0rfTTWuCarSxjeSD+Ff0plNtPdU+Yn8q7qBH6Z9O2El+CueU3dDUKRgir5ddwoFwE4IK06OsXDG7X7XgcSqdppjiyQ71icITmFhIcIIK6FESl9GzS131RUpADrBn8LSoana1wJeGnscFckm3Q8ZkkwPqjgjTn9ncOax7ZY5p+6EWCIgdlztG9db+l5IPMIw1tzTAaHxyppF80a7qcAYTbBHYrPZr1F+HscCP8AL0RW6raPMB5HsRlTRSaLW1PtjPVVxd0XfLVb+YUjc0f/ANRv/wDMpHa9FqnWe04dge6O28uAZaTKzRdUWmTWpCf8zoVm0uG13/wCHx80dEm0hp2bdg28rw4vIHC12n9JTlz5qHp3KoWF4CzYeR2R2bKlXdXOBnPsmpJrQMtaZTc9z7msCOQBKtMeS8uBgTz2Cq3d+ynQDWGGnEgKrp9zV1Coadq3bRbh9Q9+2US+0SkmrOltb5hJE4aYmeVfZU3ZLcfVY1rbspO2j1Hly1Kbz/NnHQLSLbMJRRZlGp5yq7XSAeiKww4QVoQ0WWugqRqcmUHcRkITiST9U30Kl6LXnnv1WLr9Wa9JwES2Jn3V5xIGFm+I2kWlOqD8jxP0Kzb0EUlIq0KkHK0Ld44WDRrGQZnvhX7evPCyU/Rs0btN2cGAjCp0mfus+hU/hg9UVlQxlbKRjJB5NS4pM95lddb+mm0e0LkNJ/i34nIauuYYYIXXgVbODyNugkhc74ruSy2DG9T3W+cj91xviipvuSwfy9FedpRJwQbkY9P1AT7LUs6QMSePZZtAQcjste1AgfRedHbs9SWkXqQggBW6Q6KtTALlbZgiMLeCRzS2HpjbPWUUcIAyFMOIwDhXdGVhEkp90pPcpjGc7BCG8w0R3UieUOofSPqh9gqIVDuHZVag69ijvMN5yq1VxBWUjReivVf6uFPT6u2/oxyT3VeqeUK2qEXtI9QVkn+yKmlxPSqZloTjlDoEmmJReq9aO0meI+xkkkkhCSSSQAkxEp0kARhI4CkmPCB2cB8X6W7wpe5iaZC+Bqw23NTr6nL78+Ln/wCJ33tTML4Eq/8A3FUnnc5EOwfZBHomAChNAPKLQMuAPGArJouME5Xq/wDw7mPGm3mW/wB15SMARheqf8O+fGs9mqJ9Gn0fZlqP4DfoiwVC3/5IUwfZC6QmABCeULcm3d8IUka8Q6SDvEcqPm+6pSQKLZYccppCC2qCCpB4gQnaFxCSE6E1xJ4hSDoRaCiaSju9kxdKVoVEpCUhRlKU7Q6JSExKaVAvIMR+6LQUSqYaqF1wrj3EiCFTuflKmT0CuznNWYCCe2Vxd8QLg/VdtqbvQ4R0K4jUP+eV4/5BWj0cLtFi0fDcLSovECVk2oladFsL5+WmEkHqepshZOpN3NK1Z9JCo3Td0g4jqjH/AKHA828UWu/eB9V5brFqG1HYzK9q8R0AS4LybxHT2l+F9B4T1ReXaOYhodB6FS9I4dlZ7rjJBMGf3UfPP+ZepRx8kjSn3VihW8uoCD2WL+oPVyj+oMgyU6vQ4ZOLtHqXhv8A826mOcwvVtA0amWsJbJheXfC9hrbS4HnC+gNBtW7WY6LknhTke4vOlDGqHs9KZTbAatGjb7OVpsoADISq0wBIVLCkjz8nlyyPZWAAGAolwKk8xwOUOMhKqM1b2S5QnESfqp7tsBBPJQy0I4GVUqEbVZqOJGVUqO9lMqRa2VavKpXHRW6zuSqFd8ngDlZvs1j0VaxHCq1SArFV0xhU6xykaIBUOFQryd33Vuo4wqlY4OOQkaJUjOrn0/dY2oA7Ctq4GPusTUHANhJsd0Yl38hI6BYnlNdScZ+aYIWjqVbZTfnkYWdo2tUtJvLevc0m1mA5Y4SI911YdnPk7oxdVs2U30jb13Fx+YDoh0tIrCkys9r3N3gOdC3dWubS+183FjTbSo1Tmn7wvU6OhsPg+vFFoc/IkZlb3SMc2JWeT+I9JbbmhWoz6wJIV7RNPFemGvBdPCv07erXpUrSuS51NwAx07LqqOnUdN0x93WAayk2TPdc0p8nSCGNRXJnLahZUtNp0nlm5xeBt7RzK9Q0a4p1LW3qUo2vYDHY8FeL3Gr033TLupTbXe1xc6lUw3jHHPf7LutB1enoPhelXvYqXNWXUaTT95E5AmFvPxm4o5sHlXNt9HoVWsGyCRuM46q1aNAALeuSvPfCuvXusalUqXNCq2i5ph/NKW4MdzP7rvqDwylnnlceXG8emeliyrKriGuaoYN0+ywb++gFwOIMImo3WCN0BcpeXbq1UMZMd5WDOjjWyvrAudTYaLHuYzkweVT0xteg/yLpkjoe66LT6JcMtnjMrWpabTcwbmSe8LSHRjL/RiMtGkghadrYggTyrbrIUm4Cna7g4giEJCe0OzT24gcJq1m1jDHK2KEGMDhTqUQ4GR0WlkN0eeavaEknaSFz9xR2ghwIH0Xpl5ZB0y0EFc5qel7j6RAPYJNWFqjk3k/p2029TlW7SsKdEscQCoXtq+k44JjjCaz8PazqzyNJtalw4NJcG/y/VQsfLoyyTUVsvacaVShted9V5JkDhU9UtmSPIaYbzKnpdG6sLstuNpIEfRa120PtHta31OEn7rVZEnwfZgk65o5OnTqVKL3sMlpiJVzTKNRzww2wFRmdxRLfTjTrQHkFuSIPKsOfVipTpy54El4P7BauFK2ZrLzdWZmpEmud8Ax0VQcA9IT3L/4jyJyeTyq5f6SJjCwUTui9B5A6oVR7Q4yYQS8DqT7KrXrZKTQ20WatcEjIBPdb3h60t7q2qteJc7gxxys3QtNp3bH3N0YpggNbxK6ey8mzxRaAHTnquXPka1EuCswrPw7c1bypkspsMsccZUK17caPVFiyo2oaj5cQV0us3LnacGUAd/BcuWGkOr1Q9zi5/MqYyclTL410bH+MV7ZrGUyZPqK0rfWbiu1pLGt9+5Wfa2IYB5gkjurjKbWAgCMoSro0XRrWzXXpa2tUIZMldXprQxradBrQAInuuN0t0XbGOJ2uMH6LubQNaA1mB0IW0ejOelRfotAGDJ6lWqfGFXosgcyrVETK0j9mL6D0x6Ai9UNnyhFbyFoQxQeyW09kQJkn0TZBzTGQg6tT8/SrljeWtkfblWXHCk1ofTewjDmkKKHe7POqNaGjoSAtC3rGBB45VG6peTeVqZEbHkAfQotucjosOmdV6N6jXAHqOVL9SWsKyxVcMIdettY6ThUn9kOPs67wq7fVqVXdf8AuusFWQM9Oy4/wk4MsweST/VdJvxyvRwv9aPKzq5FqpVDabiCuH1SqKl449l01/W8u3qGekLjnEuc4zklLO01Rp40d2WLf5h9lqUQYnosqhyPstahlsLmidUuy4zorVM5VVvKss+YLZGMu2WG9VIHhCU25bKpmRKSpsdgyoQU4BBSDYqrsBBKJUGJ7IRdhL0OgdUiFUq4AVisYCqVXYWMjWPVFermVXoj/wA1RHdwCnWJJ7KFmN97QA/zhTH/AEXN/qz022/5TUQodt/ywiFesv8AJ4cuxJJJJEiSSSQAkkkkAJNOEkogIGcN8VwT4WvI/wD0yvgK6/8AuawP+c/1X6AfFQT4WvP/ANsr8/7rN3W/97h+6mP+gkCBlWKBnb9gq4EFWKHzD6rYSLgw2F6p/wAPBA8aN/8AavK+k+69R/4ej/8AWzB7KJ9Dpn2ha5oN+iOBhAtf+Q36BGBRAbMvzVE1FX8ySme/EBc6m+zvUArqwE5CC65AHP7qnXqFqyLi7fTd6sqJZkjpx+Py6OhbdS7nCt0qoMZ91y1te7yM8ras6oeG8+62hk5LRjm8fj2azHzx25RJVek7CmTHdaWctUEk9ylPum3Aj7KJwVSJeiZPulPuhqMjuUPQrDT7pi4dx+UKR1Lkx7qbHYR7xtwc+yp13SyUckBUbqpAKBrsxdVJz26rjr4A3BwCuq1aqNpnsVyd28GtGeV5Xm9Ud2FB7UeoYWpSAjjKzbT5lq02ACV89JOwnsYiGmB0VOuMmeVfdjI6Klcn1kogqkEOzl/EFKWnOYyvJ/EtuZd7z0XsGtN37j7LzPxHR3A+xOF7fiOjWStHklzRDblwiM/3UPK9lr6hQAuX4VYU+39V7SWjzuO6ZR8o9v2RWUS5wEHnsrgp/T8o1pS3VQAOqHSVs0xYuckj0/4ahtIMaAPde86DVHliSOBmV88+Eq5tniTGQV7JoOqMFswueAYC4/mSke7PwJPHo9CZVlsSI90N9TBHKw7fUWuiHqy2vvzK0+VNHmT8SWPbLNR0xnqmUGkEA906z5FRjSIuOef3TEiEnCThRKA6BVHciVUqGQROUephxVZwJcpm9Fx7K9Y4OVQr/wA33Vut8xVSt1+6z/prFlKofUZQasT0Raw9RPdV6hz1SbNoqynXxMfsqVc4MKzcOM/ZUa7wGmecqWzQpXTyAZKwtQqCDhaV3V5WDqVXZSe8nACXehOls5vW6731mUaILqjjtAHPKjrWg3+kUKZ1iyczzGgscSBM8QsgX1Wrq5qW4Ln0zLZE5HC0PEXi7VPEFK3stRa7fSw3GYXpYo1E5kuT5HRfDvwrV1m+ZVqBwp0zI7Be3azbttdJbRxtIPHRY/wgsnUdEY+ozbjkDP7rU8WXDWsLRJgQISytJGMnznRwmn6e2rrO9rfSB+6o/Fi9da6ZaadbSaldxfU/9o/tK7DQrUNpNqmN05VDxt4QHiJtGrRrto3FNha3eDtiZXPha53IrNGTx1E8V0qydWrtdUEtB3OHWIMf2C1NTqmuXNDj5xBaXE4ptGQ1v9cd112q+BLvTdFDrF/6u637qogiQO3++q5Sy8OalqN75Ytn0WF/qqvBAB9u+PZeossHs8dYMifH7Ol+GdK+1jUqZfVqGysaQpsbuhg68ccr0rUrhtEODHAbRlZmj0rTw/pjLGygBsbnwNzysjVL19aq9ocYK8rysvOWj3vDwfDjpgdRvX16m1hkeyfT7V1SoCR9fwmsbYv6Z910+m2YDRgSudLZvOTJWFntAx+y2KNEADjhKjRAA6I3GAtaMHKivVohzSqdSlsd2la20dsIdWiHifslVDUipaPIIB6QFoNIc3lUm0tp9Mc5VmmYMFOJLJ1KYcMws64tA4EEcrUcQD14Q3s3RkLREnH6lpoYdxYXf3UdI8VVvDVW4GksB85kVA8EgGF1VzSY5hD4XI6pZW9Ml0IUuD0Rkx/JpnNvuhWvHVawd8xd9T3TVdYaKsBn2QtQgPhoA6yqD7fzfWzcXdVCx/JLkY5ZPDHibH+K7jkENcImByoscWg1aTQAAc8fdYYJDyyXcgwVcFZ5DgSQCAIC2eTXFmMMP/ZArqiKjXOIAJOBwsqpb1S4hjS49uq064e8A7jA4UqFWlbOaWNNSqSJJ/osJ5FFXE7cUZLUgGj2VVlXfWpGBwHCUq+g+bXdVq1WspuMwOi16lyWuD4AEcdVlapXrV2tFEwCc5yuNSnJm9JFS/oC0e1lpXc5vYOkLboPFGwY9xLqmJ65WTbWxIyST3K2bRh8sU3gQFc4ckOA9tc1ag9dPcPcLQpiWggQospBvED6IrcCElFLRoPGQQOiY9e6dqbbn7p8SvQ9Mua4FpIcMru9Iqebb03TmM/VcMPmXV+Gau61LHHgprROSmjpqR6E/lHac4VWmQThWaZhbJHN6LbPlRWjIQqfKsggq0rIbsf6KQA2cZhJmJToZDYJEpSHJjyFNgMqOwbOI8TUfJ1qqSMVBuHuepVOkciFu+NaH8S3rDn5ZWDSxCwa/ZnVB/oWlQ1CrsYGnkkD3OQrr8c8LGvHGpe0WZy5KnZUuj0Pw43ZYMHXC2g+BlZekgMtmDsMq8agJPK9GDpI8vIrZV1evFEt3CVzgeS+M/RXtarzVAHRZtEkvlY5XbSOjAqRpWuXCVq0SAMFZVthwnqtKl8qmHZb7LzDIBnKst5Cq0TiFZC2T9GPthGkZUwYGCUJphTBieUyWicnuUpPcqO4Ji5O0IcuPf8AdQcQBkqM8/lDeQ5ohZvRSRCofTzP3VSqUepgKnVcOMrKTLiqA1DyVPRmeZqtAdJlBqOwVf8AC9PzNTBIw0GEsauaFldRPQKeGKYOVEEbUmmV6yVHjseUpSSQIUpwcpkkASlKVFJKhUTTHhNKZAJHF/FT/wDGLyP/ANMr8/rr/wC8rTPzu/qv0C+KP/4xe/8A7Z/ovz/viP1tx/8AuO/qkuwl6AwO4R7flv1VZWaHLfstRF4cFem/8Pz9vjdg7tXmIIIK9L+Af/51b/RRPod9H2rZmaDD3ARggWf/ACGfQI4TgVLs5jcnLwhjOFCoY4C4LaPYUbI3BBKwtRj1E8QtGvVhYepV2wfV+65srO/x8ewdnW2uicT1XTadV9I7rjbSpNVq6rTXYGV0YHSI8yNI6K2JOfZWRDueVTto289EUOPddsdrZ4skHDoS3cYCGKnAx9Utw7/snsmkE3nsFHKaR/mSn3QPih/woPqbRGE73ANOVQuau0mTx7paFVdBH1gMYhUbq4b1PRVbu9DGciVg3mpifm/dTKVdFxhYTVa4Id91ytesDdZKs3mo79wkdQsMVgbgmfblcHkLmjuwx0dPYmSAOy2Kbzt6LnbCrkZ/dbFKpPdeXLx7YsiVlmq/aOmVnXVWCeFO4qw3JI+6x7y6iRuUf8emhY42wWpVWua6SBheeeIHNkkZyQV1mpXQIcB1XF6sDUeGjvld3jx4vZu4OjgtYINxO04JGFmh5B4K6280d9atuMxzwq//AIfe5/UDheqsqo43gk5HNBz31GtY0mei6nQdCrVYeWuk8LpfDHgxlSsKlVswREjleoaV4doUKYim0H6BZ5MvJUjowYnilbPPbfQ61JjYBkDK03XFxaBjRuECML0I6Q0NmAR9FmX2jsc4yAftwuCabPpfF86FVIyND1itUIDw4Ceq7jTq7ntaT+65yx0htJ5AjnoulsKJY0DoIThyOb8hkxz3E2KTyR0wFNp+iHREDP5RJHddSPEpDklAcTJ+qlUy4R2QycFAEKhwVVe6MiEYnJzKBVUvZSj7K1XP1VOtifurT8clVqoJ3QO6g1qilVAMn6qjVJnor1bAM+6z6vKl9GsOilcuz9lmXLgN2e60LtwB5WPdvOYzypZrHZnXr5mO65XxNcMp2nludtLjE+y6K5cTMLgfFbzc1wd4NOnyJV4Y8pEZOujvK+jeDrfwBQvdPvWjWw0Od6/UT1BHEKp4A8Ps1q/ZXrua9wMEEDouF0XS36o/bbhwBIGJwvon4a+GGaHa+Y8TUcATuHsvRqtI5ZTUMfFHXUaVPTbBtKkNrWgYhcZqpdeXoYCYnK6XxDd+XTgHOOqwdOol7jWcDJ9ly5pW6M8MXdmhaUG06IbEQjlo4PRTaAGjjhRMSsTf2QcMoFw1vlbYG3sre0OHZCrM9BAyfond6Euzl9RY0FwDQOfssj9MX1QYwumu7bzCTHVQt7EdQPwoaN7KdjQ2kSAF0FhTxnhBpW7W9Afsr1Bm0/2QlsiTsOBCfomkd0pHdbGYkuiSSAIEdE7WxlSSU0A5MlNMc8JEjqqt5XFNhhw4znhVdCA6hcNbTcZXG6teeZIxyrmragSHtBP5XNVnl5JJPPdZSlbKUStctkckodqT5oDnim1oJyOfqrHKZ1FpjITjJoieJSKZpl905zY2k9AjmlPdHpUpJgSArtGzL8wpbt7LhCjLNExgKBty3I5GYW5Ut20xAjuqbhDjAwFLfoujLrUX1CN89uUWjaNaOD/orxb3bP2T7T2/ZSOgVKk1pAHVWmsAaI6oSkx3MlDGlXYcEgAJ2kk9EEPJwBKKwiTCkomkD9EklQCf0+q6Lwu8CpUBEgQVzg4W54Wd/wCZqAiTHVCCT0ztaMTIVqn0KqUCC3lXKWYwtjnfRZpchWmgByrUxmYwrMjuqiZMmCAEuVCQeqZKRNBFKniY7qPKkwGDzypRLM3xTQNfSi4CTTh2PdcbSB2jAmeF6FdU/NtatM8OaVwdEEFwI+Uxws5qnZ04XoepOz3WRS/iavRDumQtetG2Vk6bFTWScGBhSuy5PTPRbN48oThWHVAGiMqpRxSZHZNWqbWZxhdyejz2tmPqdTdcH2MIVEZCHdPDqpJ7qdA8ZXPN7Z1wVR0aVv8AyrSpEwOFQt2+kRzAV2l0TgTLsu0jmMK2zIk8hUaRhwVqm6GlbIylSDAwlJTA4ylI7hMVE5Ci4mRCaR3UXmBMpPQqIOkuOeqHVcRgRymcTJgn8oVRwjJUSZSRGoSQSVXqkQBOUV7ht5VOq6BM5WMmaJEapMFbvgxk1q1Q9oC52pU9JJHC67whTDLMuP8AMZn7q/HVysx8h/qdJJ7qTXoe5vdIOHdemeY6D7volu+iBvH+ZMagHX907FSLG76J9w7qsKgPX90tw7hFipBy/sn3FABHRLf7/uk2VxDbj7J93MrO1C+baUnPqOAAEyuPv/iBp9u5zRWDjkfMFlLNGPZcMMp9BvitcNp+Gr3c7/018DXhJvKxHG939V9L/FTx1U1axqWtlMPEEyvny50qo17jyXGThLHmhJhk8eSMbrCs0eR9kb/DqocfSfZaVloV1XgBhAMDjK2eWC3ZksU7qihuI4hem/AV23x1bzwQszSPBnmAGuS77ZXeeDNCo6FqtO7ot9Tesf3XNl8qL0jeHiSez6rsng0G9oVjcF55Y+MKTKLQ8xAA59ls0PFVm+mCKgTh5MEgn40kw5ZiYVas3lX6phU6wCzl2d2OWzHvgROVzF+YdB7rrbunuBXP31iahJHdcs4ts9bx5pLZSsh/FbkLqdN/l+y522tKlOoMrdsi9pEBdGFNGHlNT6Ojt3gNhGa4EGSsulUPUQrDauQPZdkZUjypxaLhdPVLcggnnuisG7lVyMuNDhyju7lKpgQEKPcqWx8Rq1YAH291kX1xDSdw4Vy7aduCQsHUWktiSpbRUcds5nxBrjbVpJcIz1XB1fEwr13AOGD0Vzx7Z3Bpv8uTzgLyZhuqNc7g4Ge6jvo6ODXSPURq3mTB5VmxrB7xlcFp106cnJOR7rqtKqncMrmymuJ12d3YmM8rYpvEGDC5yyq+hp6rXo1ZieCuRS2GT7Hu3kMMZ+i5rUXu5GF0lcgtgcHlY93bh5IA491UpR9hiaTOauXPcSqhtxUORlbVa3jJCjSoE1GhsSSs012ehCpLZRoacHken9lpW+ksOSPsQt6y030AkSRBRLi1exh2j8Km5JFwUXKh9MpUqIAAEhdFaPYRgjK4N1663qQ4nnqtXTtYY4gb2z2lTiys6M3gtrlE7WGFmcqtWt2Ekgcqlb6g14A6rSpVA9oldkZKSPJlGWOQCnbACYVyhTAjHKemAXAdFYaA3hVx+iZTb7HA6JPdA904zBIymc3cmlRAJxl0lDd8pHVEcIKG7kpdsCu8x3QqpkCEWrg/UoL8nPCn+GvorVeED+U/RHfkweFXcTBUjl0UrnMjss2s7PC06oBmVnV2hJmsOjJvHY45WHevIlbN6TB9lz96/Bk4WbZvAy7+uKNJ9RxwAeF5lq9f9RcONHcS5xB7Lt/E10KNo4SQSR16ZXJ6My2r3tTfIOAF2+NGo8mZZHSo9K+C2n1nl3mUwKQg8cr3c7KdLBiAvPvhhbC3sRtESV1WuXZo0XBphxEArSU6R50rk6MjU636288prvS05CvW7AxojoqNhR2gvd8zsytICBAXI25O2dP+YpIJPCXSO6ZJIsIBASIkKG4pbigVAX0wXH6qPlxwJ+isESZhO0CeE6FsE2mC3IyimOifZ9VGB3RVBaGSTkQmQUTAgp1CT3TgmCqtEsklPKQUHO2gnoktugA3VXa0ngrmtY1DbLQ7MK5q99saYOVx99WNV+6Tys5S3RUY1sHc1XPeTOOyrHkqXWUxAlQmVZEHPCsUaRe4QPsoUKTnuAHK6LT9Phoe4Smot7sAFpZ+mS3pKtVGNosmIkLQqFtJmQBAWJqV1uMNP/wkMpXFQueQEBOTJTJNgPKZJNDpkFHYEEg0ngZVmjaPqkADlX6GnbDLsJ0NszaTD1BARC1rSrtw1tMZz2KzK1VKhBDVE4EpCqDCqh/ZM12SSeCimWi43J4Wz4acRfkDq0hc8ytJ5W94adOptjjaSl2wZ3Vv8w+qv08CfdZ1ueCr9MnAWy+jAuUnbgpoLBtEIrVbIaJsMFEKGwcnsiJohkqfKJMIMkcIzQIykyboJtlp+i4fUKXkX9VkdZXcNJC5nxNTDb0VWjD2rKatF45U6OfuDFJxIVDQmzfud1JwVdvHjynTKB4fZ/Ea49+fssodms+jtQ7+E2MQFUvav8MZRXuApgFZmoVIZjldjejlinZQe6ahz7K5aj1Ays5pO6StO26HqsDpWlTNW39IyrtEFx7YVOj8g+it0jAEK49Gcuy00wZRmHMILcmDwjsGVqjOXoK04hIujEKP0TIaoKJFyhVdhOUzhPKl9BQF5gRHIQHHoi1J3HtKBUmcLJvRcSFaIEqlcH045RazyRlVKpMhRdFpeyI9Rjld9oNPy9OpgckArg7Ybrmmz/MV6RaUxTt6YHQBdHjJuVnH5TSQQFPnolCS7jzhfVJJJACSSSQBIGTwpjhDZ8wRDgoTplJ0jkvHlKrW0ysyiTuLSRC+W9UrVrO8q0a9Q7w4846r7B1ukx9m8vHAJ5XyR8TWtd4jq+SBGQYXB5MNnf4smjK/UMrCHulVnMpO+YArLJqNOAois8ZmfZc8YNdHZz1s2bW3oPrAEDPddNSNCjSGwCQMCOq4mxuorAumV0DLprg36j8qcnJFY1Fo6S1uH7fQArn6qoP5o+65yjePDYbj7KYr1XOhoMrnd32dFRRvOvHmQahytTSRe1bUvph+0uPU+y5Kmys5+MNC9y8GWNv/AIBblwBcZJV48fLtmGeo0dvVIVaqourA9VEvnkr0nKzkjBoBVpkoP6bcRKukyZRKbJgwlE1+RpFFlm2ZhWadrDhIwr9Gl6eEXyhAwuiKOSeRso+T2SFMAZVupTLWjHXsobCtBRly9j08ASit5QgEVvKOwaJQEJzRtKKDKWxNE/8ApRrU5Hv0WZdW24cZXQ+TPRI2odyEmrGppHnOr6ObhrvSIK4HU/B7XuedgJPsveq9i0z6Z+yybvSWEn0nKiUdWjsxeSumfP134ZfQIAbA6YT2ttUtXhpGF7FqOj5dLCQuZ1HSA1xIZ+y5M1rZ2RUMi0YFncbI/wBFt2t0DAn9lmPtHN4bHvCegC10LhlG3aMc2Jx0zbFYEc88YQ6rd+f5uyBRJJVoMJEqXbOdRozqrCcQj2dMeYD0CerRcM5P2QqdcUqgn+qiOuzuwJyVI6m0YNo+ysuo7qap6RXZUiSM4yVsenyzkHC74JSVCleOdnAeJ7D0OcAe4XCtr1aN3mYXrWs0GvpGV53qdoG3Bc0dT091zThxPe8Py04cZG94fvH1nsBGAu6s+n0XD+DLVzy95HpkAey9AtaW0DE9Fth6PH8+Sc9FsNIYQRlFnCieQpkYldKdnnCSTcwZTOO0TEpjBVZJjqgnhGJ3OlBeIKYIDUIAjqgOGIHKNVGZ7oJdDsrJlrboriOqDUzMIqA4xJ5SZcirWwYOCsy6wVo1juJMLNvMkqZM0g/RiX59Jz0XO3xABJP/AHW/qDhtM9lyuu1PLsq7xyGkj65UJW6Nuto4LxJcvub4tpZaAcDpCF4btXO1Gm54LZPVUrO7c11ao/LzP4n/ALLpPDDnahqNFrWFrWAAnuvQ/wAxo48jtpnuPgo+Rp+4mGgc/ZNXrOvr4vAljTAKzaV0LW0ZbMMPdjHRadjS2MbJ5yVzynfQRhs06LDEeysQg0XQQIRzwoRbSYhjhPuKZJMVDgHopgQEzeE6ErFdiSSSVgJJOBhKCgKIunEKMHsppJUFkNpU24GVLbhLb7oqgsg4wPdZt9XDWOzlXrlwaPyuY1S7gO7x3UTdLQ4q2ZOp1973d1kEkyCMI1eoaj5OJ90CcSVh2aUPA/2EqdM1HxBzxhToU3VagAbA/K6PTLBrSHPEx7cqo7BsjpumwxrniFqVC2gwmcRhEqPZTp8RCwdRvt0hvTHKt0kIHqF4cie6yX1NzvZJ9QvmeqGs7suiYIKdQacwj0KD6jhHAKQUDZTc9wDQSVrWGmeYQXAgcq1p9iGw5wkfRabqlOhSwQOi0jH2S/oCykygwyVQvb5jGlrcH7qGo3uSGnCwa9ZxcZyDxlJsAlxcFzjHCqPgukKJJJ5TpLYCSLnREY4TSJzMItKg6qYaDzAMIbGiLZIAjhbvhhpbqbSQY2kKNjpFR4EifsuisLBtq9rtonjhSrsG6Rv22GiVepEd1QomYCuU+AtkZ1RZp4dlWJhVg6UUOkgQrRNL0HpnlT3BCYcQpIujNrYUEAozOJVYGSVZYdzZjqldk1sJ1EDKyvElIvszUA+UytVvIQdQpedaVWDktPSUmtCWnZ5vfkCmSeuQi6FjaIicqvqJ2hzT0MK9pLdpYI4CxgrZ0z2jcqvhs+yxNRqbnENK0rx+xmVg3FUveYiJW83o58athaByta3yAse3MgFbFqJCyTs3NKhifoFcaRAhU6GPwFbZ0Vx0zKRcZlsBWqQ9JKq0+iuUm+kreOjOQkx5U4gqLhyUS7oCKhU4U0OoYCze9DKxw4+5QakEojjHKr1TmAsnotIq137Sqm4SUau4TkqtOJ6LOTLX9NPQaPn6pRgYaZK9IYwbM/RcX4HtvMvKlU/KBC7faZkDC9DxlUbPL8qa50hJJJLppHOKExGFIDEp9qfELBQlCLs9023KVIfIHBHRU77UKVpTJe4CMmUe/rC3oOeeAvD/ABx4qr17t9vbkgNMHMrmz5ljWjbDi+VnS+NvGlKnZ1KVB3rI+y+ftTe+7vKlV+S5xPK1b6vVruPmFzvus51uYkH3OF5ksrm7bPWhh4dGTXotDsmFTqUWkwtmtRcf5f3VCvTc0E7Y9lUZg4vZRoWzXVR3ldNZ2lNrGl39Fj6ZbmvesYDEnOF3rNDa2i0ufJEGAEssysUK2UrWxpuILADOMrXtNIdU+VhJnphaujWVCiGkgEro6L20x6WthcMpO9HRr6OetvDr3kEiF22jbrKwZQLvlVJlRzpggDkwFYa4xypjNpmcv2Oi/UjunFzkDusP9STiEahUJcCV6Py3o0lhSOgoVdx9lqURPRYlnJAJ5W1bGAF2YnZ52dV0XqY9kfYD0QqXIhWqYwu6KR5k2BNLcIKg6iAcAK24SMITmmcqqFGRUcwCU20dkV7N3CEWFvKKNk7HU2ZQwFIEyk0JlpsDsnIHZCpmZlFbwijJqhnAKtWpq2hviCgcZNMyLqhumQD2XO6lZA7sBdXcfKVjXgDgVzZkmel482cHe2ZyAFnOtCHSBhdZf0skYlZwotJXBCK5NM9DM7jZnW9se3ZXadDHCvU6AxhXKdsHAQFusSZ5jmYtW35nsue1WiabS9pyF3lWyJBIHRc9qenPqU3QzhZZcOrO7ws6UtnM6Rqb6Vby3EnrldbZ6mHsjdOFwl9Z17a5LmsIHVWrK+fTEOIH1XOpOB63k445I8onYXtfeznHZclqQBecdf7q0/UYZJKo27/1mo06ZyHOH9U5TU9HHj5Y9nbeFrLyLBsNguyujpjaMcoFjSFKkxojACu02y4kcFdeOKSRwZcjlLY4AnIUyMJ9pSVmVkExEiCpEJjhMdgKvpOMIFQkieqPX+b7IL88IfQ0VnkmEF3zZRah9SDVPqJWTLimmCfAKrVev3R3PG08qnWfAMIfRpTKtU85WZdOJJ7K7XqZPdZNzVIkErKTNYKqMi+dMg5XJeKKgbYVeghdLe1J3HsvN/H2oEBlvTeRPI7p4ItysvLNQjZz5pW80227i6o7kfdekeFLFun2hrVI3RzHsuX8FaF5lQXFVsjET3XYa9ctoWwoU8ThdmWd/qjhh+z5GtolT9ffPqSdreJXbUANrQOwXF+CaW23Ljx0PddpRd6Qubo6UqLbYHAVhVWn1yrDAWjKZMiR4SSPCSYibeAnSZkj6JJrokcCYjlSDCTCk3oiR6ShsPVgfL90toVhC2O7ITEyIaCeFF4hxRA0wChky6cwixroaSme7YJ7JOMN6qlcv9JPREgoqajdxuj3XJ6lWDifqtjU3mCe65y6Di8yMLCbNVVFckOOU7KW5w6ypMpF5wFu6Tp8gF7VMNg6JaZYDaHEdlrvIo0j0RQ1tKkR1CxNUvzkArZ6Etg9QvpBAPRYL3lxknlTfUc/lDesm7ZSIpRKm1rnA7QtDT7I1Xy5vRCTsorW1qaj2iD910Wn2Ips9QBMKxbWjKTQS0EqVWtsnaQFcVQrvoarWbRaRxHCwdSv90NB69EW+uC9zgViVyQTPPsk2KiNSo9zgCTCG/pmVJwO3ChB6CUkNoGTKNRZvweT1Rba2dUPpbM91u6fpGAXthCTYddmbaaa57wTldJp2mNZBLRiFftbJlMCOVdYyMBXVEOVdEKNNrBgAKVQGAYxIRg3uAo1RFMfVOiVK9BqXIV2mcKlSOfurtI4QNlgREBEp8T1QqeSjtaQecK0TeggGE6ZqdIzZJvP2Vijwgg7jhEadog90iH2HYZcZ6FEPEDryhs6Hoih2cJ2qJZ5f4ko+VqlSlGN8x9Vq29Hy9hHZP40tdus2lSMVB07gq2WbWtUwiaSl6M7V6hFIEnjhYQeTmYlafiCpFKAsWgZI7hOekViSTs1bQS0LZtuFkW2WhbFsMBZxXss0KOf2V2mBhVKBEQrtEcLSKMpFqkM8K3TGECkDyrRGFrEzkyJ5CZ3CkUoRIQF3CC/gyrFQwCCqtQw36qWHsA/jJ+ip1nGcFWKhwqlb5lhL+HRFFSrBd6jKE7DY6KT/mJUabPMqho6kBQlbSCTpWegeCbYUdN3kQ5xmV0Sq6PQFCxpMAghon8K6vWxx4xVHh5JXJsHtCW0Im4JbgtaZFsgBHClt90+4JbghWFsbb7pbU+4JbgnsWyhqtv59s5nfC+fPiD4Wu9Nuqt3RG6mcuE8L6RGRBWRrGj0b6i5lVjXMdyCFyZ8HM6fHzfGz5CF80iHD1cfRO27bkFew+MfhhbPpVK1iw0nwTA6rwrXKVbSL19C4kOH9F5ssDWj1oeTfZpmpTqHmFCtR81pMhYbLwj+bn3R2ag6IJU/FJGscsZGppNkWajTc3C9JpUQ63aXAcBeVWupup3DIOV2drq1V1uPUI4WOWEjbHKNHVUmMYBkAexV5tzbsaNzv3XCu1F05fI+qG/UgG/MT91j8UnsHOP2d3U1ihSENz2VY+IgDAlcJU1PIh37q5asuLml5lNhLSfdUvHkR8kYnqVJskZWnZtbu/CyqJOI5la9j9OypSVnVmdGxatErXt2ScLMtRELXtsFepgd0eP5DLtERyrjBj6qvTiRwrI+XC9BPo8qbGj3CWzPEqaSuzOwTqYjHKE6nIGFZ7youH0SKUmit5XsoluCrDuMY+iE+NvRBVtkGYBypB4AieFWc8g8lQNXKhyo1WNstmqAhVK0hU6lb3VapcETlKUzaGCw9zVEFY9zUyUWvcEj6jlZF1Xg8yubLNI9Hx8LAXbtx9kBjBE9U5fvKmwZEBcuP9naOjO1FUwtFskQta1oyAIVK1Z6uy2bRnGQu2C2eNkYRtuHD5codbTmVBlsrVo0xhWjS9sLbjaMVlcdI4jUtBo1WEGmOD2XGav4cFPcWNIA7Bey1bYOHA47LF1KxD2uEA4lc+Xx4yWjvw+fNfqzwK+ZVt3mnVBA6FbXge183UHVXCWt4Wr4v0oNpOcGD0/9KseArQ07M1HDLieQvPjiqdNHoyyqULR2VFoDR7q1SbEQUKmwgDqrLQu1L6PPb2KQou5SSPunxAieEzk5UXNgCD1SQ12Bqj1fZV6hIJCsVDJx2QKkSkyl2VX4JKrVHSJwrFUxOVVcec4WbN0Ae4tMYVK4M8HhWa0z1hUaxgn7qZa0UUbpxbwsO8q+oiVrX1SJzjuuevH7nnKyuzeJm39XYx7ugBJhecs0+trGuOrVGnyWuMdold1qNUNYWlwBcepVClWt7Gm7aWl0mYjlb4f1OXyFydGjupaZZQCG7W8e65w3BvLnzHkkEwAUG+vK17WyT5Y6ZEo9nSG5sYAhD+2EEl0ejeF2BlmIXR0Thc/oGLYD6cLdokjr+6VnQy80kH3VrcfZUKR6E5+qsUieXEx7oszosNMzKk0wUNp7H90QHgpp2S0TAhOl9FNnGe6CFsdnRG5QsEYTDnJT7DoMkkoPd2KSQNjVSRPugKTpnJKiqaAjU+QqjXBLSArxPdQdTB6Il9DRz1zbPe705VR+lue7LfsuqFBoMwPwp+U3nHfhQ4X2VzMGw0llPLmz9VoPay3aRhWa9QNHAB/dYt9VLjEGP6Jf5BPkUtVvTkMInK5urVc95k8rSvGuc4zP3VA0xPH7LKUtmqS6AxGZTtYXOAGZRW0d2AJ+y19P06SHEfeEldi6K9hZOeWz7dF0lpaeU2do7KxY2YpiTkQrZaGjMQuiMdEuRSqN2jhZVw07nQB1W1VbuEqhVokuwDCGhRZg3NIuJImVRfbPccjK6U2m5x5/CkzTtxkg/dZtNFWjlhZVS4gDAV+z0h7nAkHK6ShZta4EtCvMpNjAbj2CaiLn9GVZ6a2jAjI5WtTt2N+VSiPdOq66Iv7EGc5wE7ROeyYcFLlAie93/T+VGqd1OeoKfPZ/5TPH8Jw6zwgaJ0eJVyi7AGFSon0q9RAgSn/Af2WW4gq2qjOeRH7I7DJ5JCogIMKQ4UZjpKkOAglkmcorThCZ1RGdVDIkWGnA+imzkoLJxzCL1/0TYjH8U2orUbarndTqY+hCpVWgdTAXQXlLzrZzCJ6jE5XP3GN0j8rSK0R7OT8T1YLW9MlZ9mASMnKN4iJfcNGYBUbNsDAx9FjNnTDo17QCAtegIaFmWgwMLXtwMCBCI9FF2g0QDJ4CvUcqlRBxz0WhbxHC0X0YydFyiB3VmBHKBSEZ4R4WsXRlV7IkQUxElThRhF2HQKrmVTqmZ9lcqZJj9lTq9ZwspdFoq1eVVqkyVarR3VGqTu5WBuvRVflxV/w5bG41Wi2DAhx+yznkl2CSus8A2odXqVyJjAWmKPKRh5MuETuGNhoHHGFJOY6JgJXqrSPEsiW9k20qaUJ2OyG0pbSppIsLBpIiSLHZAT0T888qSiRlAirfU2mg+YGDkr5S+ONpSGvNNEDcQZj/AH9V9NeK751lplaoJlrZjuvknxVqtTV9UrV6sYcWgT2K4PInTO/xYcjinNc0ZBlB3va7E/dbTgCTIEIFWlTzj9lhHLfZ1SxNbRnU6z21mlxwF0TNScLYNZzHKwarG+Y0Aj7roNLtGPpy4dR0wjJJdseJSeiFO8rO6x+VaompVdB4PZXBZ02ZDR24RLZga+A0Bc8pr0bxxO9kaNmXECJyvefBWk27fD1vLQScmYXjtIAt4/Zd74f8S1LLTWUKnLSVEc1djy4fo6K2pSQei2LamBHHRVKNIBX6UNhcUezryts0rYYWlQcsyg8QrdKp7r1fHnR5eWNmtTeMK1TqSBCyKdXurVKrC9OErOGeM0w4JSFRFbsVLzj3V2zD42Wy7EZUZhV/1AjlDqXIHKfKgWNllxBEIFQwIwq77to6oFS8b1KlzSNoYZE6r9vKqVKoHVVry+Y0TKx7rVWgnKwnkivZ6GHxpS6RrVK46lVK1w0dVh1NXY7AKrvujVyDhc086/6noQ8Rr/RqV7wEkA8dln1KhcZ7oTT/ADdSOqk0FzhKySeTbNHKOLoIxp3d0ejlNSY4RhFaAMreEFE83Nl5vZZtyNwlbFm4QFghwGQVfs7iIB5W0Xs45q0dPbuEA9FeY4OGCsO2rzAC0KdXHK6FJNHLKLWy6QCOeQs28Y2HZHCP5wE5ys69uBtPZD6CMXZyHiai2rTLSOcJtDtW0LRjQIAH7p9UqitXYxvMrQt2BrQFxS3LR6kG4xpluiPT04RCMdFGlwiKo9EvsjB7BKD2CkmcYVAQPMKLj06pyclM4dUntDRWeNpzlVqp5KPVdIMqtUdkLN90aRKtYiVVq8yrNYxCrVzGAoNSvXfjgrOuHANMyrlc5+yy7t/IPuok9WXH+mVqFXoJWDcvmTwtTUH4MLGu3Qwk8ALJnQqSs5PxLUe+sGMdHfKzWU3bQXOcTE5Q7vUqdXU6jXOEh0BWmPB4grpiqVHA2pS0ydCmJ/1WhahoeB7qrTw0K1Rw9sd1EmzWB3+hEeQPstphiDyuc0J38AZwt2k+QAEkzdl5rgTMGCjNcYhVaWcHhWG8oJZbYQQEVpkQgMOAUVpTWiZIO0bQAcpxzCiCSclSGFRk9dBW8Jjynb8oTHlUgl0O121Q3BImFECUxJWO926IUU4E8JkxkS0pNMJ3cKCl6H2OckpOJAHRMkciCkDVlK4kkz2WbWYXT7rZfTBOBhQdbh2CFLTZSkkc5VtS4cBVxp5cflyup/Rj6x2U22wGS1QouxuZiWOkzkiFs0bSmxoAERyrIbtGE6040TysgABgfRJzJ56KW0TKdO66ArvbIgKPktgTEozmwcSVFV2SBNJvTCkGwICmUjys3pjGhICEkkAPBieiQBmBynyJaopAJO0w4FMnb8w+qADNcHTCHVENceikRs9TUn+phBTbGiNDhXqOAFn0CePdXaTjj3QnYPstg4VilhqrU/VyrFJxIytCaCjKmOENqIOEmR2iTcGEWmJBIQWnIlGpnB+qkiQVhwApobeQiIYkOcgjquc1D07gcYXRTiVgeIGlpeei0hIl9nCamQ+7PVFtG5AHCq1nb7pxHBK0LNnHZc8ts6kqRp2zTAEjhaduIICpWzPUJ6BaFEZnqmugLtHhX7cEAKlQAgfZX6IC1XaMJlumJZlGaZICFS+UdkYMGCtezNNjEEJd1KJTRhDQrsrKpXEkq44AHCqV+SspdFxb9lCsOIVOuYH3Vuu4h33VSoN2CsDoSK0EGOV6N4Nt/J0xjiIc/wBS8+oMNS4psHJdj7ler6ZQ8izpN/ytA/Zdfixt2cHnT1RZSSSXceYJJJJADwUySi6UDRJJJQJMoAmkoF8ZKbf7oboKMbxJa/q7KpSAJLgQvmjxZ8P9ToX1evQpl1Nzpx/8L6pftcDMZVS4sqNZpD2gyubLiU9nXiycD4j1GxvLF7hc0Hs94ws/zQW9RmI6r6p+I3h2zdpld/lU5DSePZfIF9dinf12M4a9wz7LCOHdHQ/I+zQIDqzB911WmDbTaDmYK4q1umOqNL3DldJQ1SjRYBuHt3WeTG3o2w5l2zowCRJBRqFKXA4XNu8Q0mM5lF0HWm6hrFC1aY8xwasH48mavyoJna21q57RAwY6rVtdLqvpSQZn3XoHhnwtZ0rJj6vre5s56FdE3TLJgjymJrw3Lsh+WkZDDtHuj06kBZ4rieVL9QB1XndHrPG2aYrx1hHpXQHJWG65A6qu+9Leq1hkUWmif+LyOrbetb1U26k0dQuIq37oMO/dAN/Uid37rvh5I1+N5dnobNRaf5sKf+If9QXnLNTqtGXSPqif4wYyc/WVr/ySJfij0F2pNHLhKp3GqtH8wx7rhK2tGOVj32tVDhr4+hSfk/Rpj/FK9noFxrbQSA4FZlxr/pJ3Y+q89GpPe87qhJVttdx5CxlmnI7V4OHErZ0NzrVR+AeVRN5UqTucqTJceqNSYd8e/ZRUn2J5ccFSRcoAuyr9EHEhCtqfp5WhRoyJWsMXs4M3lX0SosyJlXqVMHjJTUqYiZ/ZWKe1oPRbxikeZkyOfbEW7foqdzcCnyUe4rBoJH9VzOtXnlNMnp3V/wDhh7L1bUQ0/MES21ODlw/K8x1TxEylVLTUzPBU9O8Qte6fMEH3TppDVdHtmn6gCB6hK16d4ds4Xlel6wHNDg8zK6Gnq3obFQ8d4TjkozeG3aOzq3sDkSsPU9UDA6SB0WFcauYPq/dcvrWsPIO10mYifqs8mbRvi8bdnU6RXN7ePfyxvVdNQEx9FznhC28qwY4ghzxuJXUUhxnCyg72bTSWg9OY+ilkpMEcKS2swtWRhRf0UzwouyRKaGuwciSOqGTEgmFI4d90KoZMQjsYGqMFVaxER1VqocKnWKyl2aRWitUOIJyq1bj7o9V3q46qtWd7KDcqV3CDlY94+JytK5dg4WNdv5wok7NIox795PAWFqtXy7Oq+eGrYvHweP3XM+Jqwp6c8T82PooW6HN1Fs86stMvNW1Z7bSm573EnBhb9bTb7TmkXDHNLRmR2VnwZqVHStZo3DgS0HJ7hd14lvKXi+5pW+hUNz3SKj8gN7rTJmdqzzoRStnmlrqZc4h0AgxC2KFzL27iB2VTxL4JvPD+oU2134q5Lhx9Fcq6E6la061KsajiJICJTg/8suDleztNBqf+XbnnK6K1wJPGFx3hVznNDHyCOZXY24wPZFo7l0aDDhFZgZQGiGqwpYWWAcBFbwgtMtCKwyr9Etph6fzIiFS+b7IpMlNESWgjXDaMpnkEYUEytUTYkkklQhJyITJIARxymJ9knu3ZUFLY6HPfumSSSGPB7JdE7SQ6cEfRMeUhDJJJJjEovaTEKSSABbHdktjuyKknYqBbHdkix3ZFSSbsKBFrm/T2UEdzQ7lR8se6mgBJIvlj3S8se6KAEpNaXHCTmw6B1TsMOkoAbY7sn2meFLzB7pjUkRCNAMfmSfO0gJfzJSJQ/wCFR6I0TDoVynyFQon1j6q/S5CEJlmngyeFapwMdVWb2RqZ9YKtNEth2og4Q2og4QyPQkSlzPRDU2v29OvdSIMPmB6IoOYQWmQPdTaYcOqXoz3YTMQsLxeQ3T3VRy3n74/ut45JWZrtu26024pOEy0n8Z/qqXVjTXJHmNuwufJC27OnEDgqhZUHNcN2IxHutq3pwPqsq2dUmi1RZEEZV63bHIQKLd0Dj7K9SZxn9laRm2WqDBHvEq9RbgYVegzHKu0mwBmZ9lpFUzBvuwrGy7ARA09kqYjMojdzh2WpnaIbT2UTgkHlWNpQXj1HKTYaRXeIGeVTrRJVuqZPCpVTJKxkzVGfccn7qlWyJHRXqw3H64VR4jE5WJ0L+lzw7b+fqtGRhuV6gwQwD2hcN4It993UqH+UYXcjHUFej40ajaPH8uXKdCSSUd3sug5SSeFDd7JbimgokoPM8Jy6BwhPMZiVLGkO5wGJhQdVAkBM843dsIDjJKn2VQQ1D1hRdVQKriBz1QnVNoyk9FJMsebCg+r2hU6lUwY6+6rvuIBBUci0nZzvxOuAzQbggjLV8M6m8f4jcEGZqOX2L8WLzZoVczMN4XxpeGbuqSMlx+yrH+zMs+mhhWez5eUaldVnkBzuvCrQVOl8wnutuCMra9mgHvcMuK3PBTtviK0cOQ8FYDflnqtvwkSNbtjwd6iapaHC+Ss+x9CuZ01men9le/Ue4XKeH7l/+HUcn5QOVp+a5c1ndxZzjr7GCUI35g5OFgfrChPunkyF5HwfZ9zLgjefqB2/MfwgG+Lv5jKxfNe4nBlSHmHjKpYUmT80Ea4uZGSkLgHAWfSoumZ/ZXKNuT9wr4OzGfmQRM1cdVDeTxwrNO2gZCsttwRwqUG+zml5yMqs122QMRysi7pyYK6yrbjas2vahz8jCtY6M/8AnPuzAs7YmoSOFtNobWyeAi29sGH2RKzm5VqCMM3muXQ1ERwMK3TaCZ6qlTd6SWlTFbjOVaicsvItG9aAQFo0iGtwOqwLe7ApwTnlSdqW1pyFV+jnk2zohXAkcfRI3AXG19Za05eIUBrbCR/ECqvoVaOruquMYC5DxA5zmvAK0aGpiqI3T9FG8t23DSRnCEn7ElWzwnxZbXHnuc0kRJWHYalc2tQNcSQF7Nq+hNq7iWnMrhtW8JPNUvpAtH0W8JrpmE8cm7Rc0LxDAAe4ldla641zGw7HuvL6WlVbeAdwMrWtHVKZa0krDNro7PHi3pnfVtXaWkSeyzrDfqWrUqLCcuBP0/2Vg73EfNn6crufhlYmvePuXg+jr7yFxv8Abs7mlFHqGm0hRtqdIY2tg/haNMRx0VWgAGwFdpY/C6IL0cEnthRwnUUlpRnQ7vlMIbj2UjwoweSqQ0iBjPdV3E78lGe4Dnqq1aNykuiFU4VSocyrDyNpVWt/ZZv2bR0Va3J+qp1HE8lWqxAJPZZ1dxmBwsjWivdEiYKxLs85WpcvAkSsa7cJOe6hujRdGReHJyuR8S1W1T5PddPfDJXLeILVlZhqUnS9uFK7M8z/AFo5m1dVp1nikyYC7DwF40p6HVdSq0GgvdBIGVyWk1rht8KTRuY4hriRwF6BfeENPr6abxlVtOu1s4PWJ/qqzOLXGRwxT7RreISzXnUru4qH9K0bonv0WfUfZ6jc06GnsdSpUgAZ6rl6Go3tqw21QF1JkgEq7oFsNQuTcVa76VNpyB1WWOCgqN1O+jpbNrKWollEDaBBjuums2gtJ6rNo6xp9oKVPymuDRBcY5W7est32dvXsQN1QbiAQc4V/IrpnQmPTGc5CKeMKixz2loqAglWx6sBXor0WaZOBKO2AVWptIInorLDMq10JqgjSQccozMsJPMoLOco1PhCJe9DjlMnCZWuzMSSSSsBJnOIwDhOkSAkwIBJvKQSBAKT6GMkkkkMSeT3TJIAeT3TJJFwB5SASSAcFSaCeMJrYrCpJ+iQEpDGSTkQUoKYDJJyD1ShFMBkk8JQimBEgckICsFAAkwk0JjJJJ1IEmpEetOMJnOAKa6LWgFL/m9srQo5iVn08VSr1Hp9kIll0Y4wi0vnCrt5VimRHun7E0WGqUoTI6IwMBV/SKodJNKcZCRIdvA+ikyS4SgtOAiscJhS9kFgIVdoMwMnH5TgQE78nPCr+EpbZxdzZ+Te1mwImYR6LIAWnrNAecyqMh2Cq9KlJU1TN1LRKjSzwr9JgBGJQ6VMABW6dLgK0ieXosUGjaMdlba0ACEKiwj6BWWiQIVpGMmO3hHDQBgQnYwAyUQeyrsi6BoD/mKO6en3QKuDPRKh9ld4GZ4VGsAJhXqio1+DCwkvZtBWUqjQT7qpVHq++VZrcKrUyYCg1b0dp4MoCnZGpGXkrpM9DwsrQqfk6fRH80ZV81MnC9PD+kTxMu5MNz1S2+6Gx26U5cB1WqdmZKCfspQAoHAUS7smwqyRHQ8Ib+PukT1Ki4iEuykgb3HjohHr91YKC7Lj9VD7H07K1RxJInCrVDI5yrj2nPuq7qZSey0UaxycqjVccyVrVKJI4VWrbLNx+i0eX/FlhqaHXH/TnK+RrrFzUDuZX2p8R7KdCucH5V8X6izZqVy3s90/lXh7MM/aYBSpfMPqoqVL5x9V0GJdHyBbnhATrduDmHLCGWAe63vCRH+N2x/6gFE1+rKj/pH1D4eJ/QUR0hayy/DwJ0+lA/lWrtP+X91wtHpJ6PNG0zIRhb8YmVcFASPSEanTjkLDij1JeW7oqUbY9lYo0AeEcQ3IIRqQA7KuNGUvIdaIU6IhWKVIdsIrNoH8uUTgYT6Mvkb7GYwER2yjNYCFXL9uZSNxtbzn6o2K2w9RksP0VGtSjKN+pBAz+6HWfuZhKrZLdGfUqbT29lQuapLCArN9DRuxlUvNpzJMwrSQ7Go3BFM7hCrm7DqsB3JQ764a1sM64wqVnTJqbjM+4VkvRteeWtPqKx9R1NzGP9WRjlafkksmDHCwNQsKlV1QAGDlCSsT+zkdY8TOo1i0uOD+VStPFjn1BLxEqHiLQqtSo47XSOq4q8sq9o50B2OsLpjGMkceTJKMj2vQ/EAqwZk8RK77TNSp1WRP3lfMWj61VtqjfUQAvRvDnihstBeOcyVE8T9GmPPGXZ7YKbKtPIBlUL7TWGmDAWZo+uMrMYJHC133jajYEfYrmdpnRHvRx2p6YwPJDYXPVqPlvIAXe35Y9rpA45XH6nsa+GkEysZuj0MUPZQaHGIyen1XtXw+039Ho1IwQ543EnqV5L4es3X2sW1ITl4Me0r3qxpChQZTbHpaAogvsM0qVF1ghwVpmfsgsaAOEemPTxmFvjOJ6JJJASpQFrYJWRUXmGlSdxiEJ5kH6IFQF3qMcQq9Uyjn5TJygVOFMtdGi9AHmGqrVcT0R3kxlVKpACzl/DVFa4cY+6oVcwVarfNyqlXoszUzrtwBMZKxLt/7LWuj6jkLDujkyVmzRJUZ980mkXg8yB/Rcfe2N7al3mu2secfda/ibUn2dq3Z/m78rFGsXOp0mG4ILWcd01GlZx5siviAtrOrb1wWkEHldtYV7Oroda2rui+e4Bri/gBYFiaNevT85xIkS0HJCt+NKulUGU32W6jVa0Ag9cf3WcrlKiFUUUq3lUtXp2rXCo1/zHldfR8O1K1o+pQZspxiBC4Lwu2qNVp3tegXW5MgnhfR1GwP/haleHbTpVAOBwD1Wjj9E/Ils8Zq24ovbQrMkudE9gvR9I0XaLf9K9vlOEmTMIVx4esaz37a5O4jJ5CoXV9X0El9AVX29CASQcj6lZShzNXP9bRsajRPn+W8wBwT17LOtKhZWfSeWuLSQYVjXPFjPFul29rotg9tZhBc4iIjmI91j6VbXFpUeLxpFUnMlUqjKisM5NbOgDoI90ekPTKqUjMQrdPLRK3XRswoEFTa8jCaEgJOAmkTYUcpk7OWp9juya7IoindJyeqfY7sorQBJiJTpnSOqhvYIgknMdEyChFNMDP7KQEj+/ZHs7V9YgwdgMcIJbS2Dp29WrGxhMon+H3YyaRjuus0+gymGgNH4WpsBpO9PTsto47Vs5JeRUqR5s+Wu2uGRyoSCQequ64A2/eGiMrPlYPVpnVF2kx0UCM90EcotMktyU7spEkk+OhlSaO4SAYCRypJJIEM/hMG9ZUuU444VJAR2ptvupJIb0FkdqF5Y90dDUjQJ42uwm4DgjEAiCg1PnKTAYuxCgTKdygXRyjopWyAdFWIWjbmA1Zwguk91oUuBn8KbYPotzICKwwZQWH0QSisI3DP7qyew1N5PSUdploKrDnCLSf0P2VEPsKnHCYe6cY7JAxyUWnkhBJHsp0z6ucJENaLhEp4AyobgeqNAPZNEvStFa9pB9sTEkZAWeymAcDC29oc0tjEKgKW2oQeAnQkyNKngd1bY0NGU7WgIjBJyJ+qtdg+ibQI+qNTbOEMKxRGJhXGjNhmjHKdJMeEySJx9Cq9YQQArLlWrclSMrVeypVhCu1eqo1TkrGXRtEpVzGe0oFAF9xTbzLgEavG7PClpbN+oUgOAZUxWy5v9GdzbkMotb2A/oiCqqjSccqQJkZPK9BaSPJlG3bLYf7wpCpHuqs7cqQqT0VWZ1ssmpnGUwcXY4QpHRIEjglO2AcOIEcqBLgFEOOJOUie5Stg2S3FRjr3Swn/ACqpfYiJb91BzBKKRCZSVbQE0/ZRNHlWmkBJ2RykUm/s4vxzbtfpFyHDGwn8L4V19mzWbsdqrv6r768aNB0i4wPlXwV4o/8A7/fD/wDzOCcFbM8t+zLUqXzj6qKlS+cfVbGRdYPQtvwn/wD3q193hYzPlC2/B7N/iGzaP84Uy/yyo9pn1n4Yt92l0cfyj+i03UCDhG8MW4Gl0cCAFpuoZw1cvA7FI8oaQeE54KzqF01zSZCL5/T+yxOsJUqQYBiO6i259QyqN5WgYPHVZzrwA4JRTA6uhXDuqseaFzNjdgnbM56rXFb0j6IoT2yzWqxnogOrgiAqd1XO3GSMqqysYzifZFFJ6Lj7k035zlWad0HMyVi13F3y8+6TXuDSSTPbojiwbL1+8FvusStVLHmOCrL684Kyr64DXRjKEmxB2tdWdmIla1pbcQFl2dZgbuJ7LQo3oB5EJsEzZp0ZYAYU/wDD2wZByOgULKux4nH9lt0Ht2wCJUPsDjtT0RtQGW98QuJ13ww17XkMH+/svbX0GVAMD8LNvNLbUafRI+ycZOL0TJKXZ8ua14eqW7i5jSAD7rIoV69nUzLR2JhfRWt+HRVafSAe0LzXxD4Vc1ziKeQcQF2Y86emcc8G7iUvDviOq1zQSYkdV3tjrwexvqzHU+68toabUt6gkRHcLoLFrmgZKxzpdo7fFTqmdzV1XdIg/WVk1a3mVC4qrQaS2XEooBPGSSAPqvOk7dHqw0rZ33wu04179909sMpwGu916xTYAAVyvgOxFnolAbfU8bj3JXWt6ALWCqjnyPk9linyEcCJQaY45RQcgLaJzSJKQ4lRSlW0JOiL8BCfyER5JG48oTzBHCVjQGoQHHnKDUMtU63KrueZIhS2kXH0BrcFU6xVusSqdbGSob0axKtUiVQuHAMgyrdVxgnCo3J9J4WL/pquzKu3ggkArGufVuImBzK1Lo9Ase5qbGvJOFD6LukcN4svKbrjyOSBPCx7LbseCSGt5W5qVG3vbo1QIc3E91HRbS3L6za4IjIEJt0qPNk7k5GVoz2v1FoZctpmcFxWxf6PWv8AUqIq1hUog+t04IHRZviez05lu19oXMuZmBAV7Qry9qaayg8EuaZGO3urlqPJERtumdtSZSbQp21FgbSpt24b17rptDu3OtXWTruu9jRLae7H0+i5Xw7e1KF3Rp3dHfvIEcr1LTm6VcX7W1aTLemGjc4mCuZqTdI2k4pHK6hbaxfMFGxpuNZp3RTMmPf7LsKTNBuPDlO3Y+ncX8Dz6Tj6g4cgg8QhO1TRGa3WtNAuB+uc3ZuY7E+6z9Z8ON0q0qvL91/cmXFsDceqfzLF+pgv/wDSSZDR7ugzUqn+FWbKLW+mTH090HV7G5dWq3ZzSGXEcLkLW6r+H7p1a83miD1MLar+Nn+IrJ9HT6LreyYIqVXASZ7LFRd8kdd0WbG5ZVAgz2WpSy5VPDukUKtmBbvJeBu9XKl5gpV3UXH1Awu3HLRany0aM5hTZwg0am7p0VpjQRJWo+yIHqE90WAoBsdVKSqYrRJBgxPRGQfZSNjEwoEyVNwGMlLZOG8p0CIkxwnZTdUIFMSSrdtZPqepwLW9ZWlSpU7doLQNyRnLIlpFK3sA0zWIkZjv7LQphjdrWCBKFVeTzBnqU9CDVEoMG202btmJ2q9VxTcVStfSGwrVVxNN3C610cn/AGPPtYduvqnsqM5hWdYJN/UlUwY4XI+2enjX6hGkFwRGuDQQTwhU/nCMGCRyiiyTG+onoURRAEwOApIExKQ6A8KTGgtyov8AmKAoclnQSoJ2x1CRiccJp0AySaT3CW4JBQiQFBOTJQXv3ACEdDCkwhnqVFnJUKha3MmeUrspdEXmJPRBe8FpOVJ75biEBxIkYQ3oaWx6byT7LStjICyaR9cdytS34CUWrscuqLrHAABF/nQIwjsElVe7M+uwzTMBEazIM45UKbZPJRQTA4VWia9hAU6GCfZS3H2SsTdD9UVmEEHqptJypQnsO0TjqrLSIjqVUY4jOFaHAVJ0Q0WKfyhBqt/ifVEpunGFKpE8cK4u+jJAyCFJn7pzhMPnWhQUAghWqJ9KrsyQDxwrDfTwlaRm9hEzuEmmQol2E7CiLjAJQXyczgqbydpQHztBlQ9IqmAqn1QqdYjKt1SZ6KjWOSsXo0jZUrn0n7q74ao77tzjG1oJP7LPrPHH2XR+E7fbb1KhBlxx9FWJcpCzy4xo1w1OBCKaZnAUSwwV3nmNg93slu9k4kfMkTBiEByQhu5kJ5d3Cg5x6YTbz7IFaCz7qW+eZQd59lEkkIGWNwRA8QBnKpKQeceyYkXDyU0quKs91LzIQLiGDuyfJPuhB6lvH+wgONnP+NngaVcTwGlfBPiZwdr16Rwarj+6+6fiJXDNFuM/yHK+D9YdOq3RmSajv6oh2Tl9FNPS+b7pk9L5vutjFl6mcLpvh/T83xTZNH+cHK5lmGkrsfhUzf4wsR/1Ss59FRdNH2X4dobdOpD/AKR/Ranle37oGht22TMZDVpfhSlfR02l2fLunXXmCByVpl5AGCud0KZBPddIxu4DEyuRqjvXRTutzx6QVh1mvFQfMuubb7hlVLjTd1QED9kJ2My9NkVATPPVdPb09zAe4VG2sC14x+y37W3hg+nZMRm1aEzjHdUbiiQcSumfbgiAqN1bQCAEroDm6jnt+WEzHPefUCtCrand/wBkWlacYj7IsDPFAxxPusu8sHVXzB+4XXttgGgKJsmE9PwhMls4x1KpRb1j2Vd18KdQSeei6rUram2k7jgrgdat3McXMMATwhK3RLlx2dLR1plMN9QB4Mlbena0HR6x+V4fd6pWpXBAecFaWm+I3U3Dc/j3VvCyFmjez6CsNRDoODK2KNVtUZheMaL4na6Jq8x1XaaTrjam2an7zKz4tdmqafR2F3YsqNloE9lzOsaM1wJIBwuhtL9jwCXYhQvnsqMJEflJscVujyDW9Fa1ziGgQTGFhtoCm7bGOhjlei67SZ6jhcTeMDahxwVlObs78WNAcgY4Wn4ftP1uqW9Ecl2RyICzoJIA6ruPhdp4rXz7t7cUwQ2Qsa2azpHqthRbRoMYwANa0AQI4Cv0xgA8oNNu2CSO8BWWcyuiK0cjYZFHAUW/KpDMrSJixJ0kxMGFQAXGBlQeRiSpOyYQap9QUoqtAq/PtKrvIDjKPV6KtUd6uFEjSPor1HTOVUqu95R6rucKpUKk0rZXquEHgSqF24becK3cFZ1y7+HwsZOzWKtmVduiTMrm9eujQsqjwJJMLoLxwkrl9fIOxpyAcjupfQZP1ic1ReSwkgjdmFtaTa0KNFz9RIaag9Bnj6rP1Ah1vFEBr1hadqFWlq9KlqQfUoAxtGYVxjyVnnZHWgbt9XXnPAFSjTdInqP7r0Lw0y0uXtawDzJlwnjPCzX6bTudRDNNohnmnvJA7rqPDHh2y07XGC5unF5AdA4nt+VnPJydFpcI2XNT0k0rL/Eba5Auafy0Wt4hXdNos1DQ7i6v7xvnlpbAdkGOIXXM0K0uQ57KrXANOBxHuvMtN0ylX8Q3lN1wfLFTa1rXYOVpOajG6ObHcpFLw/p79NfcVKJLruo6W1N2QOeV6BomqVK9EPv6jqlYekPecAqhdafa2HnVKpkNEBs54XLa7qu7RP0dhRcKr3E7mgz+VwRj8stna4qKO31TQLbxB/8AcPd5B5e04J+y0/Dfw9t7yx/S0Km2yBjzBycyQvNPBeqXdvUs9Ov7qs22e+HN5iZ4+5XsWp+NrDwjo9O30yk2tTa3+GZMknrHJXpYceOLp9HLllNrRO60e28PO8mx3EbI3ckFeaa1qJoa0KZcPM/mA9+6p+IviRe1S5tCg4VqwJ9QOJ7Kp4W0rz6pv9auTS353GJnlYZpRg79G+FNVZ2ljdCpTaScwtSlWJxJ/K4yjfMoXD2F8s3ENPBhb9ndNqMBBkd1ePKpdHTRusII904VWi/AMz1VgOmMLpW0Q1QpMnlI9c8cqbAXn0q/a6bLt1bA7JuInNJbKVC3fVOG47wtOhZU6GXw4q2dlMBrG4CFUJIklHH0c8sjfQqj/TGAq+Z5UnOJwVAmFEnWhRS7IFw3Z6I1sZqiMhV3NmT3lW9Nb/EBPHH7oiraQSemb9s0FrcDhEr4pu+iVsBtEJ7v0scewXYlSOVd0ecas4fr6mVUByUbUnbr6offqq4yuGT/AGPWxr9SywAGendEmcgobRAUmmErKasM3IBUhE5lQYZapAwUyBJ3HJyluloUS6EwHSJAUS8BQc6SYCBpWOSAJKbc3uh1KnQhDLwOiB0SeRuMHCjvBwIQy4yob2jgqbHQVz/sg1iY9kiZPsq9ap0CGUokg6ChVnEnsobzmVXrVSTA/KkpKixSeTVg8d1sW5loyufou/iNzOeFvWplg+yfQpdl4fIEecQ059lXb8oRKUl3OFaMn2WaZII591OcqAwpDunVEsOOUxI7qIO7opQPokJqx4KcclNKQKQcaCtOOVZpOkST16lVaZzKNRMOjumiWrL7PlHdEjGVXpPH2VgGQri6MZKiDj06qVONwhQc31/VSpiHLUf9DNMflWOnuq8QR9UVrpMQkyOiaZx6JyhVHQ7jhJADflxgkgIFUwIlFqHaCR1Vaq/GVMnotRA1nnqqtY4mUSo4k5VWs8EccLGTNYorViHGO+F3+hW3labSEwSJXB2rPOvaLB/O8Aj7r063ZsosaOgXT4qs4/NnuiHkjqVHy4kE4ViEi0FpHVdnE4E6KbqcgbchQNMTkCVd8uOCAhvZkyPulQWUzTEKGwDgK6aYIOEM0iEUUtFMsMk/smIPUQrRZk4TPpgt46oaoLKqYz0JR/LHZQdTOYSsaRBJOWkdFEyDwgQ26OqcPGVAj3S2Hug0XRxfxUrlmg3JE/IviHUs6hWcTB3lfb/xNpl+hXDYmWL4g1Rm3UrkHkPKWN/sZZfRXUqXz/dRUqfzLoMGWxwu4+EY/wDrWx/9y4dvyrtvhI/b40sSf80LPItBHs+2tKA/St+n9lelqztKeP0rfYf2VzeFnB6Olqz5e0+2dT+ZdFa0ppgnlFo2IaQ4chaFCjtBmVyXfZ6aWio1hiQrDW7sEAlWfKURSIIISAlRtgTgK/SoiOMwh2cjPSVo8xCdsTKpogDj90GtREcYWjBQauXfZG6EYlW1k8JhRjkfRaTqY3FDqMxjI6qNhTKYAaIKBUuKYBEyoX1QNJXJ6xqD6W4jgBCjfQV9mhrFw003R0lcLrNxua4AxEqtf+JAC5tRxHaFj1NQ/UvG0z7LojifswnJdGPf0w+qXN56rIuQabxBg8rqqlB9UTtcs6/sHFhJaeOy3ics4vsx6eoVKG2HfZdPoHil9J7G1HEZA+q4y5pGnULXCBKPaUiTLeQrlBNWTinKMqPftI8RCrSZtdz3K2Xay2BJJjjK8V0m5qUg0ThdNS1AkRP7Lzsq4vR7GBKSto6rWdRDmug8zC5V1Vz3F0zJ7KNxdOqshDDmiMrB9neughkj057AdV7T8P7P9JolLc0NdUG533Xj+j2zrvUrak0TucJ/OV75ptIUbemwQA0ADCS7IyP0a4HpRqXyhAocSrTMmei1icjVB2DCfhRYQBnupLZfwzY6E8kOP2RUKq7oEMANQlpBCE4lxEqdVwdwUM8FIshVVSoclHcDnsqj5DjKhmsNAKnJVOqrFbIMKnUKzk9GtbKtw5Zt047eVfrECcrMu3AjBWTNEZV66AZwuf1CiK5qbTLmiVt3pEOz0lcTeXNYXVY0TyQAir0Z558VRYrCnW0l7qFN3nUz647LnrNxqXRe4DBiSJVuje3VrUdRrMczzT83f8J6dJtJ7mVDLiSQ6Oip/qjhht2bunX99p9QPs6bapcCNxHC6HwRfW19qdalrdZtC4bLmkCS8nt2RPh9aWNejVbdOna35R1QfFdPRdzamkS28D4OOv0juoxpRfKQ8t5P1Rt+IBqulhv+E37XU6rSHgEBwb2/osPRLetSufOaJqD1O3H903hu4q3l++jeXLabQ3LnY+y36NAWttWfXL2W7XemoQYI7yssj57RpjhHGtgqFB95Vp06z99Su75Sei6BljbW92Lalabi0AOMSP3XN6Xqmn6hqJbbVGB9AwHk5Pf+69GfeMNk19nRcXU2TUeBge5PChwcaaFPKm9HIeLPDVOtpdSvTpChXYJaQ3krk/h3f2lxqVelrVYOc0w0PPC9B1XxTZW+m1bvVIq0WtI2hwBPTheLaDZU9V1O81GiHU7U1CWAnAH+4XRCuPJkqXJ0eh+MmadXquq0KdN4otlr2tA+y5/SqwuqLqtWm4UWSRu4P0W7or6NenUoUm03gjk5z7Imq0GX1K3026reTbAz/DZMBcssik+LN1GlaMGzo1NUuTDQKIcBIWtdXDdLrU6W4AcRP++ywH63/g147T9OYyu75abuv1I/1W9pfg+/vqQv729Ya1Uz5beg7T+UscJRkNTS7OhsbkVaLXAxjnstmzpVLkgUx6e6q6P4ar27h5tQGlztGF1dGkyiwNYIgRK9WCZnkyx9Ara2ZRaMAu/OUdz3HkpnAzwhlW0czk32OeZ7odZxDcKTjgQoGUJABBMyn+b2RTIGVGO6Ukg30gRAE4WhpzRwe6qrRsB6OEo7ZM9I1qMAABD1BxFu73BRKEwSqurVYoETmCuuX+THHuSPNNRdN7UjuoUjhNdQbqoW/wCYp6QgZ4Xnvs9qP+C4yS0SnUaRBapJATHCdQHKlKd0S0S3FRLjJnhRc4A5PRRcRtkHohsEkLBhDc4h0A4UHPJ+ygXAHJRY6Hc4mSVCZ5SkchBdUI3SlZVBHvgZQt4VerUgShGtjH907HRc8yOECq4kjOCUA1scwjU7hnknc32mR+VLex9Aa7y3E4VR1XOErusKj4p5Ecqk6u1hhxgpJpldl62qTcM9z/ZdNZzsE+y422qg3DDIjcuttn7mwTkKlsiejWbwitABwqlIg9VaDgRM4VIxQRpJmVLccCVBieQmAaSDhI5KHuEj/RPuCLJa2TyeqnS5KC1wDgpOOUWP0WWkgyMIzSQ6RyqtNwIAHKJTEQldEtF+m7ieSrVMyqFJw2wSrdCABKce9mUkGcOqTe6ljpwmWtmdj5kIgMGUJSBVJolhd4QiSSnB5Q3n0FFopehqplqpVXEhHqO9OVUquEcrOX0WkwNQ5VOqTlWKjhPKp1DgrF1ZsujR8M0/M1ikT0kr0NvEd1xPgmnNzWqDgLtNy7/FrieV5T5TCbvZLd7KE+6cFdOjmolu9kxglIx3T/8A8SAIkd00BE3BKQgLB7J4UHsJCMZJOExnqih2VxTg8qDqfMK0o7RCniOyoaZ6/wBVB1Mz/wB1bdSHRR8se6mmOygaRg/6phTPt+VdNIQeU3lQCO6KY0cZ47oF+kVwB/KvhfxFT2a5dtj5ajgfyvvjxfSJ0ytAzsIj6r4L8WAt8RX7e1UylBVIjK9Iyk9PDk0eyVIerjr/AHW5iy62dq6v4av8rxXYuB/nXK0zAXQeCXFviSzI53j+qme40VGrR9uaNWJsmGeg/otDzT3WJ4bl2nUnD/KJ/C11h0dSR5jToiOEcUfSMR9kQCOEekJGVz0ehZQqscwTz+yBu9gterTD2DAWZWZsdjhJgmTpv2/fordCvu+37rNJJ5RrYncM/wC5RYzWBkSgO+YqbSdgyhoYooiWe6A9hMhWUm0ucpdg9GFe25dIAlcfrVg+ox8tgfRemVLXc1ZN1ppeCHZ+oTQrvs8OvfDrqzyS3v0RtL8NuZWG5mML1mpozd3yx9kW30prSIaPqr+R9GbgrujkLfw8DS+VVb3w4PLI2fsvT7e0axoBA55hNdWTDTdgHKhTfY3FPR85+JPDrqVRzm0+5wFhW1sWGCDIXu3iPTab6boAnIXmmq6aaNdxGM9FrHLapkx8dN2Z9nRIEwtWk2GzKFQp+kSjtgSuWcrZ6WKPGOiRTJSE8LN7NDtfhrYm41d1cj0U8gxyvYKDT16YXD/DKy/T6KKxEOqu5Ptwu8txIznKaVGc5bLdEQAPZWmCGjKBRaJVhvZapaOeTsI3hSMHCgJKYyMSqI46JkxyhPzI4TzHVRPzcoQ0iuou4UlB/RHQwboIicqpV5KsvMTCq1CSfqpezRL2V3McW8KhXYdxxnPRbzac0wUxsw4EkTiVDi2Cy0zkrolsg8lZV0fSVuawxtOuWjELAuzAMLJ6Z0x/ZWY+oVC1r4wYP9FiW2mCrYvunYe0khaerEik7PTn7qncah+j0hrwJgH7kqbVmGdWc9Wv2XddlF7QCyYWxpumNvbeq6o0knhokulZvhLTv/E2subHkvGZ4hdx4W1bS9F1G+06/qf+aon0HoVUnydHIpJI4ixdf6Bq7mPZUbSeRBcCPsujs7O2vQ653vdeF3yDjlVNe1ytrOpGnXY0UKT/AJgMkA/6LT1JlI07StoW8VGgbiCYCzzZFH9TTFG9sr32nNp0XbSRXA9QMglA13xZq+q6PQ8O0qOQI3NHA4zj+6t2OvWNK0u36huN+SAzcZB6LW8PWRbUq3lNjXPrNA3OGG+yWJN7FmnSpHKaX4S1DR6tO8bUD3NMvbOJ6yvVPB/jy01DSrjQ227aVyQ5j6jiAPqcK3YWtCvQDXAuqjmRgrxvxwKuj+P2nTmbHuAJaBMn6Lqi67OPjy0bXjvwU+adZupOdQnNPIEd1k+RV0+0/T0CWisIaBwB/wDC22a3dVmEXbBUdAA3YjHZVKwZp9MVruux76uGAOmPaFxZsnN1E9HDi4rYLTHP0h1qKNV9eu90OYBx911lKvSvtWNAg067mgkHkd1xtSjcWe2/3GZ9I91u+DNE1rWtYGr3QdQoNADSWxu+ynHi+R2VJ8EbGn+CqVHxKbiiw1C8SQ8yB7r0HTdNFkcgAZgAYVuzt22zCSS6oABPsiCYgznK744F2zkc2+hyc/f8pt3sopOmMFdJFCNQAwYUC7CjG7ndKkGdTkKWIZINkcqUJdEgUQZ902DxlORIhMABwENWaiA6d1rWTYYspuXD7LYsx6Qnji7Msr0X6Q9KzdfcG27s5hamAzCwvEjiLZxnMLeb/UzxbkcC47qjz3MqTflCECd2evKIeMLznpnsros0flUmmUGnU9QiY7IrSJwIRYn2TTE4youqAA91YpWNetR8zAB6SjkgbSKbnAlCfUgx/dTura5oUX1X03ikP5w2QsevfU6bhLx3CG0lbJTUui8Xw45UHOkkys1t8Kjj5eQOSBwFe0Z9vfXZp1KwaByDiVPNMr+hWNrVGb2037eZyqNa4a0OIPWCurZfOfQuLe1dSbQo8vIyR7LhryrbVKNUtfsY0k7j/MczCzllp0iY5fTJVbqOBI+qHTunVMMGV0WiaBZXmj2dxUe7dUOc9EvFdrb2VxTpWNIANbMzJlDyNFLL9HK3N29r2tewh3HK0tM0yve1J8wUaO3c57j7dFja/WLa1AFwDnDMouna5QpUP0926oNxAnaQD057Kl+ysmUrLGk6VVvtRu3Mv/Lt6btoMTnusvXLSpZalTptqmsKhgEZMrutK0oCzdUZc0KTHjcG9TK4TUL64d4hbSZSJ/TPkOIgEjrKxfKLIjLRafp97aVaRuaNSnu9TQ4RIlddYn0JW2oP1epSfWcH1Ws2gf5VGi4se4e+Vtjm3plxbfZq0DJCsAwI/dU6bpbhWN63sb/gbfMAKYOBnKCx0yf6qU9c/lFk2ES+ii124xCiHHMlFjsMHZEqYMkquSehRWTgj7pC7LDDAlGDgTgqruIwDhPTcd4JRQi9Tdtd3Vyk+en7rNY7OFapOPdCdESiaLHhwwphVqB+yPK2ir2YUThMpDgJQEw4iBlDe4lpTyQcFRdG0ynQ0wFQw1U6nEq2/rPCqV8Ehv8AvCyZouyrW6lU6mQZKs1ye6pVXSCJz0KyfZr0dh4NaGWb3E/M7+i6MVQTC5/QS2jp1JpGSJ5WmKk//K9LFSjo8jLuTL3mDuE4eMGVS8z6oragjP8AVa2jJploVAOSphwImQqW7ccYRGmBko5C4stB2ecqW5U2vIIJyp+aeiE7Jos7imJlBFSR1Umvk9VQUTSTbgnTsByITJJJaAYgQolse6mkigs53xawf4dWno0lfAXjAR4m1H/94r9AfFonTa//ALF+f/jL/wDKNRA484qUv2JyejGT0vmH1/umSpTv+/8AdaGbLzcQug8Dt3+JrJvd4XPN4C6r4asFTxhYNIn1pS/yEez7N8MUy3S6IP8AlHRa/ljsh6BQa3TqcQPT/ZaHk+ywSOyzywQOqcOjgqi64AMApv1EkcLkTs9DiaIqRiVXuMnCEHkgEcIjiXJja+iu5knKNQaQR2EKYBPRGos4CBMOz5AosBJnoiBhDQE7WQh7COyBGTPBR6LNxx+6gG8SrdJojGEuhT0DdTAdCHWpRzwrk54CG9oIMpozszK1IAjsqdV7ackSJWnXEtk9FiX5gGCk9lJjVNRbSkT+UKrqjX0sO/BXLazWqtk05JWRZXNw9xD92DiE0rK4o39WrB8kGeq4jWGSXE8SuleKr25aeFiapbPcw4ws5I6cK9HPEhpICW4dVF+4TuEHhM7hZ0brRPcEW2Ya1VlNglziAPuqwMyuj8CWZvfENBpA2U/U4Iodns2h2rbXTbaiBkMytu3bjCq02NDcDjCvUGgQB3TOZlui0gyYR2nKHS6ojMH7LVdGVi2nskQQn3eyRMiITFsg7lCc4B2ZkKVR0EAQgvMmSi6LSsjugKL3SCEnOkQhPlK7HSGcQ0ZVUxu+6NVJwhkZlIov0R6PuiOYNvuUO3MtjpKNWw0R0CtRT2cj7OJ1w/8AnH+y5u9cZMcLoNcef1TzAXN3pMFcku2enB1FHMeKPPNqGW/zE5j8rLovqXGkClUaS5hyEfxFqotNRpsJGyMj7KxaXDKVu64Y0FrhP+qmUJJWcWSSlNpMz9DqXthXq1rKi6m6OYhY9fTtTr3lbU6odvc7c4zldhSqfrdErV6Di14wYxGVzmlOuq962l+o30HEzlTBtHO4W6N60saNLSWXjqralap8zJkj7Lb0JtzUoXD7IUaNNrJO/JdjoudqVKGl1nPdTc+lwew94W1fNs6Hhald2t9Feu6CyYif9JWclzls7K4xOR1K2qaxqjbe1I37vWW912Wm1NU0hjaFZ5faQJEQQO63PB/hPTrJlK4oXLDWqiXGQ4k8x+Vt+MKVGx8OXdSpDntYW9sruhjqNHDOdyoza1TVriwnQXtL3MkY9u4Xn3gvR7jU/E11V8RXbWV6e71VDEnsJXUfCjUdVqaXdVrSkHU6UiXHgLK1bT7nUXXF3QINd7/+W0GZ9llLJS4o0xY3dsPXdbtp1fNaKbWGBUJ+b6LG0HT73Wtctq1O0fcWdJ8w4ekx1n6rt/D/AIJvNYsaFDWx5NkwglrcOPsTyvUbDTrXT7ZlKzotp02DaABwoxePu2dGTL6OYtvBtrUvxd3tIBmC23GWtXV0qbaNMU6TGspjAAxhEJ9kxPVdkYqPRzyk32yJ9024e6c8Ia0RCHPJTJJHogoRTJyoz9FMuxCdPRQIIlSn3CiTJSRSViwlu+v5STbRPKUnSKJ0cvH1H9Vs23yAdVjWwl+ehW3bgbQtMZjkdItO+QLmfFTy21cScQumeIauN8cVC20wck4TyOok4P8AZx9J+5wiSZjGVYdvaMsc0dJC0bSzFnpdGo1hfWrHGP7LQraRqJshVu2tawiQIXnttptHo/Ok6Oea+XYwR3Q6162kYc7PssnU7p9tVrUiKg2ZJAn+iyNVff0qdpUNu9rKxgOIiQVi89dI2tPZ1Nte0q9yxm8GV0en2xfqFBtQ1BTBBw7ELx6w1Cro/iOk++aX0Ad0TgjmF7TdeLdJraNRrWobbudHqfiAOxVpX+zOTPJt0jT8Wata2elVLSjsdTLS0kQYXz5rV0alZn6em8w7DhMkz2XeeKNd0rWXi0salWpduGzZQBP3J7ZXO2HhzUqFi5l35VINdLdxG8j3H9kpSc1yYsLUNIz7Y6qzUGWxpmmx7JBP9yqdvfusddNtebgHYlpyF1GsaVd29ix1xctdVj0lmCB7qpQ8H1dXFC6dXLC0ZdlZx42bubSOx8MUnvuWtpM/hlvqDsxjqFV8dXVHTrC4ZRtaNYlpDWtaJmOf3WLW8LeKNMp/q9N1Est3elxBk5/+Ve/8FV9FubPUtU1Z1w2u31h/qLTyMdlccTuznllRyHh7xJq/+HttH29WkaTi4FwIBHK0KuqajqDXXb5DmYa09Y5W9quv6XqOpMsLapRc+mC2GCJjqULWNCvtPrWVyy3f+irkDbHU5yPooyzp0aRapWYOn279Y1Ohe3LGllI+ppOMdVueJLjTf8C20rek2uKgLC2C6OyFolIB2o2BIaHNO0ExmFzHw/pU6Gp3o1Gm648ioWgF3AHUA/Ra45XGxN7o7/Sn06uiebc1qjaoaDBHyxwrlzpJv7CzoUqtubu6eNgEB5HU/QLHr1S7UnHT2VTTIlzOZj2Va7NR+o22q7/01e0dtGIhp5wo5q9hOLrRev8AwvceGdVYf1rnnaXNA4+isWtV1Qb3ES7OMfVX9Y1ez1rTGmnWpms2BvJz/VZFo9rmgU3BzW4kdUY5XOjXFdbNalURQ4mAqdE5hF3d8BddmtF0PIHKfd1VLeehKOx42AHmEh0WA8xKQcSEIGBlIPyBhGyWkWw7AhO17pQ2n0hPIHUKiCyx3pEqbTDlXa47ZwigklKxh2kEyJwrNN4lUg4t46ozH4BKTewas06L5gK5SMD6rLpOxhXqLzjgrSDOeWi2XAKDjJT9FFaoi7Eov+UqSi/5Smxlarw5VKqt1OHKpV4WDNF2UK7gHQqNQickc9VcrkcrE1y9Flpl1cPhrWUy6T3WdW9GkqS2dFa69RYG0w8EDE46LVoaux4BFQQvmu08W7qh/i8meSui03xW4fLVC7FyS2ea3GTPoGjqLHADdlWaV2wiNwyvHdP8TvdtMnPWVvWOtXVy4ijnsUvnUdHRD8flyLklo9LbV/6uVIVZOJXFi61Sm0P2kj6K1b649jR+oBHfoqWdXvREvBmlrZ13me6myp2KxLbVaVemHMII+qt07ph6rdNNWmcjg1pmj5meVIPcOqpCq3oVMVR1TtktUXWvJ6qe/pKoiqOhUw4QMotiouB5HVSDpzOVTDx3Ug4J8mTTRd3BIEFVd2BBypNeRyjkwpGP4xcW6ZW/9hXwF4wj/wATah71SQvvLxtVjSqxPGwr4K8VODtfviP/ANUoi9kZPRkp6XP3/umTt5K1M2XGfKF2fwjZ5njiwaepMLi6ZJaF3PwcE+PNO/8Acf7JT6CPZ9u6JSi0YPYLR2eyraU3baMjqArg4UKKaN7Z8/vcQepUqZkj7KZpuAzCJSpQZXCeuFpST7K3T9+U1CmAOFcFPI4QJtAmD1QRhW6VOTgJm05M4wjUxDoKCGxyyO5+yfyyc8IoCRBPZBPKugQpIzRAUgw9YT7EUDlZFKJ5EogEcwnRRNlSvTBAED8LIv7bc0w2fot6owR9FVrtHbBRQ0cZd6YKpMNAJ5wh22jMY6dgGf8AKuprUmz6cKpVOzHsg1MW6sWUxDW4iOFz2q2+1jyQIgrr7ohzZnjK5PXKxaKgzGYWMuzbFdnAagA2u4Y+yrDKJevFSuSEJqVHTLY69N+Emn+m4vXD/oBI/ovMhJIA5Xu3gayFloNuwthzvW4+54SIlo6dg9fGFcp8qvT4Cs0wS4K4mUi3T4H0U1FogBSVoyFHuUjwkokyqAHW4GUM8lTq9EIuycKdlLsC/JA/uoE9ypOxBQ6jsTCVmiQN2SZPCE53qwf3UnOEHnIQpkpCktF+jU2GJkfVSrVi8QHEfQ8qqDhTAxPRNs5uNs5zXqJlzwDnsuRvXOEyD+F6Vd0G1KRaQM91xuuaO9rjVoj0zJCxq5HZDJUaZ4r42pvq3bqoJ2zgZwt3wXtudLLKuTEQ4/Xuug8R6PQOlkvbFSevVc3pTDbBjaR2ndA6yenC6vIlUVFHmwxt5HJkmC7sa1a2a7ZQeZcFW07T6tDUXvoHc2JO3p9wtzxLp+outqb3WlWmD/MWwiaK/wAljGV6bBAy6eVxSi0jTF+07GbbP1Wo2hRpH0wanpJBWZqvh241fV221k8NpMHqaMAH6cSt5ms/o69yywe0FzIJI/on8D3VF11VZWrBldziS5xjcVGCD5WzXPkXSKvh6/1LwNqtG71Ombu1ZIDD6h7YyOi2vGt1qnjMU7mwcaNhWgOpN/m6479oXS+JPD7tSsHNZRp1HuaNgJkGRzK6H4c+F63h3SqTbqqK1wckEAho7Bd0oyqkciW7Zy3hzwfqFnp9OnY1XUKVQRUZ1I/Zeg2GiWVsaTmW1Km5jYkNyfcnuteAOmT2EJnCQox4FHbNnO+gcZIASkRlSJjGEJ7uQteuiRPI6Qhk5xwpNBPCcsgSeUxAw3vMqURgpJJhZB3KZ3AU3DCi7gJPoZFMQIOE6i44hQhoiklwmPdUWI46puT14Tyok9lDd9BTss2gzjuti2BiIMrLsAd0rYtuft/da4zDJ9BKuG8wFwvjdwfspl22eO8rubgxTK878eXFNvluLdxY7iOfqp8mSjHY8EbejuNDs7bTtHoXt1Wp1KrKe5u5wgGOB2+y5/xZ4nr1dP8ATVtaAGTvdkx2BXnuieHampXzqt3f3VOk4y2mx5gc+8KnceBLU1b2rqt9dVqWdv8AE4XO88ZRqPQ1gak3MyvD3jCte393bXFBrhVcQHBoJ7/hb2r6tWvNHfaXlNxq0yPJcOn0j+yfT/D2j6PpT7mgTvg7ahOQVIgVdKZXy5zTg91xZ58P8nZjXLTOQvrSrXoUnXE7qbhuce31XpFxp+k6r4Lo1Lm5tmUKUfwmkAkjGPcrlL2rQdTq06rmMFRsTOAtP4ceDdMqtu7y/v8A9RQbJawv9I9+VGHLKS2LPGi7SZofh2wbeFtOk1pA3UwC4jtPJwszxB4x0fxDq9ja+HLSqKg/5lV7dsxHTqum1HRrO9sW0LamKts1xMiDj/YXlmsUWaJ4iFa2JoUg6AQAuhSUo0Y1tSR6hq+mU7a1oGvWouqVWhu3cJE9FSoa/R8P6Zd29atQNyGEUwRIBjE+64+7ph9ahe1KlWqKmPUTg/ThUNe09lAtqt3PbV53Lj/WL0buLa2dz4P1C/1HSGV76+pigCT5IMA/ULsfDnkXV865rVaTqYpuHqzA4MSvOfANtaXulus/OFK5D4ku4H069V09xpVtpTDSp3bnlx2u2uyP/mV1Rm40zF47VI848Z17XQPFdTUrCgx9LcRJZEzzjhbTPiDdVf0tapUbdUabR/CLAA3EfSVy3xEqsq0hYWNOrXfumW5VDw/pl7RsjQvKD6RqfKXhVm3DlQQW+LNq81htXXf1TTU21eQz08+6joVehp3iV769Nvk3ILml5Bg8jP3VG60yrbgOqPpgAjg8otdti2naV7q4aytSdLQ4yM/2UYv20aT/AF2eveCNMta+p17m11Voq+XAbsBAJnv9p+y5TxNeWVPVhZeILulSoPuNlapR5Inn2XI6z4wvLfTP0lC4pUqZeNr6QAdBOZ6ptevdJZokVHuubqptIqkEkHkrWWDZzrK22bGs22iWuqhugGs+y5JcZkx3V7QawqUagYA0NdEdvsgWmoeHv/DNvTNdxvx6qjnCWtHbCWjvty6sbNxdSc4EEjuFnCPGWzrxV0dDTcZ5P5VjzA7H91SpPBEHlTFQEjkLY6mWg+OIKIwyJ/2FXaY95UqRJJTEWgZapU8OyYUG4CUpklkPn6KaA14wI7IzeqSAI2NuSjNIkQf3VfB9lKmfWDCALbT3KkHQOAUAOAHVSDxHBQJo0KLoIjKv0HmQseg4zhaFF/GZVRZjNGqwyEjgoNCocSp1zAkd1vejD2SJHcflMeOiE09cKYdMKUU0Aq8FVKvVW6vBVSr1UsuPZnXOCcwF5p8ZtQ/Q+CbsB0OrOawZgnvC9JvRMrwv/iEvotdNsmn5nl7gnhjcxeTJRxtnjlC9qU+HfTK1tL1au65p0w5xBPfC50A7QtHQ4/XU93AXpzilE8jxVzypP2ev6bqIo0G7jn3K7bwzrBoURVOZ4Xk797WMM4A6I9PVrqlbilSqkN915Tg+Vn6xh8SEsCgke5XHxCtmUS0ZcMEAhc1d+LBcPPrDQcwvJDXqEkue4k8qIuqvmANeUSi50RD8ZgwJtI9x0rXvKaCx8t5ieP8ARdBaeJ24l/7yvEr3VW2Ns0+aOIwecoOn+KWGAXwR7rpxwdHwX5NQhmaR9GWuv06vNSFp0NWY6P4mV4BZ+I2k4qj8rds/ERa0HzZ6RKptrR56pnt1O/a88j8q225BIyM+68htfExABc6futmx8UNeQHOA4Can6FxZ6UKk8EfYojKg4IlcZa69TdEVBnpK17fU6boO9UpWJr1Rvtqeof6qfmA9vysqleMqHDvyrDa3Y89lSdEOP8ML4g1Y0WuAf5SvhTXnbtXuzx/Ece05X2x8Saxbo1c7hOzuviLWDu1S5J53lVj/ANGWVdFQc8pmclKCcKLDtdB6lbGD7LtPhv0XcfCEkeOdOgn5j/ZcMww0fRdx8IXBvjnTyf8AMpm/1KXZ9zaU7/yzR7BX1kaTU/8ALtz/ALhXzUPQj8qYyVG7R415I46o1KiMKxTp5B5CMB2C4Uem3ZGnTa0BEhO2IgjKfHsglsTVNnLfqoykD2QSWWqQQwRs91NnyppbEvoMOAkmBAATgyqEJJOOVINgyQkHqwNThVqoESrFYjOVQrPIkdEnKi4rRXr1A0n2WLfahTZPdG1KvEgcrhdfvHMqBoMTj90lt0anRfqvOnaeVQvrGpXpHEghT8LUnVg3cZGPwu3oWDHUYLZCyyRfoanXR4ZrOkvoOc5oP0WLBGH8r23X9Ca+m4tHJXl+u6U+0rHGPoso3ezqhJS9lLQbV15q1tQaJ3PAP0X0LaMFO3pUxEMaAPsF478MrE19ddVIxSbH3Mr2eiMAGMKxTLTBEKzSVejHVWqPzfdXHRlMsg9EpKiwGTPHRSKtEDtUKh2jCkh1QYEJgQed3KA9xDiApVMdcoR5UtlRVDOVd7jkGIRKhgGShGC0wo7NKsG8kDHVNTEuyovIHJypUMoSJm/1olUkZHZQbWgienKe4/t/dVCeSEGJpNduglDuqDK7ILRnlVaVYsgHrhXqbw5gyp6dirs4fxRotWvbOYzBnC86t713hjxLZ3V7aivRov3FpGHQveq9FtVpDo44XJ+KvDFDUbcjYJA5HREpN7ZWNRema3iH4j6B4h8OihbUHGrUAwWAbPuvH6rA2/dscTQLpEqbdPqaJcGlUDjRJxjj7rovD3ha+1qsHUafl2zsmo/oDyi3Ps1WCGFaOD1Auqao2101jq1aqQA1gkycfheneEPhXcvbQra7VAI9RpU+c9Cey9B8L+BdK0J7a9Kgx91GajhJ+y6ktAEDpgBdMcaRwz3K0UbCwt7G3ZSoUxDAACcwrIwIUoI5CaQqaskgeVFxjqpHgwolu7mQlQyDhJycpAAKe0DCfaEUirIJJJIGBMplIiMnCiSENUQNCi4DqnMZTO4apaKInnCGndymQi0hASmPBTpIGRATHlLcEgQTHVZAmX7BuD9lrUBAws2xENGOgWpQ4+y6Ya2c09uyFy8Cm6eV5d40eat5sc4NByCTiV6bfu/hOgSIXivjy5e+8dTGDTdumVyef+0dm/jd2WdOdc1NQpfpbrY1sNqN2zP+8rf1C1Y6g+jTLneaduTmVn+DKto3SKr7h+25qCQdv9FveGbSm7b5lY3NyXEtLjAA+ntwvNh+sTomzyT4maLdaG6lRsrqubQjIc48q3ZU9RuLWxaapFEgCGiB9yofFW61KrqF7TqFr6NEYzz9Cug8O39Kr4R081djHhoAz1W+f/F0LFuRyepaVWpan5NTc4VgBLjIGV1Pgx9jZWlfTK1YGrukj6//ACsvxDqAdqtrUYQCDDYBMrK0mzutQ8Y1q73voUWASQ2Ce2PuscUXORWVqKtnr+g06lnTq0bKo0UyJdvAOOwXlHxZ1iwNqLbYG3za8gATuHdd7quh+LdPsX6hb1bQWxZEPMOA4/K5PSvCdOrq1S+1ry7yrWGBUI9JPUBdUcDT/Y5XlT/yKi+0r6FbtqOc2rtDoaQQ0xmUDW7yi/TmMp0atSmxwb5oEglVPEds3TNRrW9pXDfM5ptjAWHR1W+rWNax3htOm70gDnuuSWNKR2Rm2i9b227XLetp761sGtJc4kDc7jj7rcvqd85zK1a7cC7MLh9Qq3rtPbUouc2rTEiMI3hmjquv2zq15qDhRpfyAwTzj9l24YwcdnLkbUtHY0P/AA9p5NxdXL23J+Z8yZ/t0XP6p4otdQ1cU7Wq+pRaNo6Qtq18B6VrFs59W5qio0En1SvP7HRnWfiK5tPlFMmC+AYnn8LaVONEQb5WbTtRt7W5r06tHzw8Q3cSdqsNo0LnwxdD9NSLgC8VDMjnAKzrihbfrYqPIYYO4DBK1q91b2+lVLOm5wNSmQD3kLljpnS1a2W/gxpfhu8dcXHioDaBFEOyJlXvEdjaPu6zNNa2401hJDgz5R2XC+Dq3/mDptR72u3TgdJ9l2PiHUaOh2rLS3r1XPrDaWgckrom2+jhUadmNpVy2jQu6FK3BDzgloJAXSaA+dPpO6GREAcFF8KN0TTbV9PWba7qXdyz+ExsHJ7ngflQtbimCbVtJ9N1M5B6Z/quenys7sMt7NdrxAMonmQVQY7OThWWOLmrU7lsv03iOUWm4zIVKkYcAcK4w455QJlppkSVIFApGBkoshDdEkwcwiMdtJQGnIyjEtRdioKx28+ym0+oIDeJHCLKB0HLiAnYTtEGEFhGcqYjpykD2XKbuFdt3gALNZ82FapP2lUjOaNmg8QO6s4ewgrKo1Mg+6vMqy3C0TtHPKNDRkgEojMjKDWG0jOD1U2OBnKd7H6Gq8FUqvVXn5AhVq7ecKX9jXZkXnXsvnv4y6TqGreIPPt6RNGkwM+pjovoS9bII7qdTw9QuLdpqMDnOHZa4bu0Y+SuUaPie5067toFWi5p7woWTzRumEgiDlfW2q/D+0vdw8iScDGFzN58Df1jy6m1tJdvy2qZwY8bxzUkec2rm3OntLMn/sqLqFUE+k8novVP/wCml3oNqS6o2swcABc5eWNSm8t8h/P+Urgk2pH6R+O8+M8KTezh30qv+RylQtnVKgDtzR1wupNH1bTSfP8A7StPSdDrahU8ulQeHOwHRwEJ2dGfyoqLdnlHieo5jwzzDjjKwWV3UyNrjIX0BrHwI1TUZr0Ltgd0a4GD+F59r/wg8U6S17v0ra7ATmmc4XoYqrZ+b/kZPJlbRxlvq9ek75jC1rPxRVYdskfdYWo6deadVNK9t6tFw/zj+6qe604J9HBconotn4uIIa55/qt+y8UMeIL+w7LxvcRkco1K7rUyCHLN4l6Ljmkuz3uz8QsgBtQA/Vb9h4kqMiKg/K+c6GtXFIj1YW3ZeK6jCN0YWcsPtGi8hPs+jrHxYB/zHuW/ZeJ6L2j15x1XzjZeLAW+pwyt+y8SUnN3GoBHus+EkaKcWekfE3xJSbpVVm4epscr5Qv3+Zf1nf5nEheieNvEDbqiaYqEmMdV5oXlz3OK3xRrs588k3SHHKGPn+6k75SgyAZW9HOy/SMtXY/C5+zxlYHpvXD0qoXUeBq7qPiaxe3H8QBRNWiou3R91aPU/wDK084I/stHzFzPh67D7GlnJaJWuKoPC506OvicOxsYmVMYn3URypLBncxx3TJJnmPokFDyE4KEXgcpxUE/dANFxowEVnyn6qvSfOCj0yYKa2R0SU2GCY6KAKeY4VCCEyibpEQq+4qTiQ0kJCdga5gqjU9QICsXBJ5ygMEnKzZ0QVI5/WaDwCWSSfZcVqGnXFxWLi3AMr1ava+c1vHdV/8AC2/5B98pxtMHI5zwnZupMbuGTGF3NtSHlZ5VSys20eGgFajKcAQmZN6KlzatqMhwXB+K9JY5jztLivR6g2tAK5zX2jy3DHGFE42rLxNqRy3w7sBaWtepthz3/iJgfuu6ot4ErI0mg2lbUwAATkx3K26MQs6rR1t32HpiBKt0RgHsq1PhWqPAVozf9CpnGB90+QokyMrRISQt09FEuJGVF5jhQc47HZS9DVWDqRuwoFM7EKMlZsqiFY9IQS6A4IlXkILycQcJDXYKqfSp26DWceCj23yymnsjJ0K4+aOyqHk/VWKpJcUF4ABIGVT6MUyBEkHsi06jgYCr7ipsJ5nKivsfZpU6ofAI/dFdTY5uQCFmUnkZlXaVXdg5TpMV+yvT8O2l3eNqVwCwfyESCuytrahb0G0rdobTaIAHRc/SIDpaSI91uabWZVhpMStMVGWRutlicgITj6jI4K0HWjnCWO/ZUbmhWpTLcd10NN7MVJJU2Dc+R/3UQJUQHD5gn3BuCpRQ8JjhIuxMppPdF0BF/RMeAndyJTHgKRrsZI4SQ3OMmDhBQnukIT+ikou5CBDwAov6QnmCUN5McqLY0iLuUyRMlJM0GhRJjpKd5iFAmFLewSVDRHVIYePdJIGarG9VClbG4pG1aNhoErRYNre6pWohquD5BC64LRyTeyjqVTbSM4gLxjxFcWNbV6tSvWYGhwBDj79l67rhP6WoZ/lK+fNWrUv8Yv2NptLt2JE5XD5iUtHV42kdFqFZl6LSno1R7a24AemGQOsrYc/VbKtQgMLT/ODELh7PWLylb02Un7dglvp4+qpUfEHiDV67Nj2sZRcRPPB7cLmwxi1THlk4nqXjHwVY614fZeXur1WXUT5VKIdJiCFyN5oltpNCjaULt1cPZAa93y55ULuz8RV9MGpWl297GztaWYcR2XGaRq1/eawf8XqBtWnI2uEQM9l05eMsdRXRlg5RnbOku6tWnVdRwH0Rua4AZ/33VDVdW1Cz0+jqNuQ+qDDx0+v7Ile9bXuKzqDHPcGw4gYj6/3VSpSe7RKz3gS4EBpMx1/suHEpKVnbkqSosWWv+K/E9enUddPp2cAFm4xzzCvax4f8R6pWrXFjeVWttmyNrcEgThXvh/VoV9Fp0aYbSez5/Vk8fsvTNE1hthaP09rafl1s+c/kLvjLk9nly/TSPnXwmw3fid1PXbqu17nbHEDcZ6Afddz/AIFdXOrus7S0fFJu7zHAM3LhtYd5Hj+8/QvFRjap9bZx+F0trWuaorPuLiu55kYcQ4+0zK586ipWzt8eXJUEstKvNR/xKmxltQNsNr3PqDPs0dUP4fsospVLajXp/r2uMg8fdV6Gi3FOzN226b/Ef/yy47vrlbFGnbaRp7m07eiytUG91bdk/wC5Sxvl+qDNj1Z0uh79Mp1jXqUHVXEn09PsvOfE2l6p4n8T17ygGUqdPAM7ZA7rd07xNo9Kye2pXo+aJBLjkrDoePbCgK9paWhrV6rtjX5jJhdMMckcyp9sqX+r3lenQ0+4o0W0rYhvobkkHqYWjqtJrm2pDYJAz+ES7sbhlxaUP09OnVuCHEvdgdZQtUFyNXp2NV9ItpxurU/UBj+q5pPZ3JaOco1xo/iY1ahc3cMQOZRfEWqs1TUbRtJri8PndH9lu6HpOl6rqGo3Ws3NWKAigKbeY6rJuBa2uuUhZS9rgf8AmYXVdwOalyOj12q+0o2D6RO4M6K3phLrZtWtIq1Mkn/RZ/n1n3lHzxRcxrZ5ko7bmrcXrHOa1lEekN6GIXFDbOyJuMPDeyM1xaB1VWk4kzj7KwCZXQdMetFum/gwrTXHB6LPpEzCtMc7AQBoUnSI4RG8IFI+6MzhBPskDBCnPdDnhSPOEhh6LvTwioVP5VNpMx0RQibMIgOPZDAypB3YoveyX2GY/OMKbHO3iUCSFOm+HZymLvs1KD8DPZW6bvTysqlUCt0qgVJsxkrNGoZp4OQo0X8iEJlSD9kmEhwjunb7Eki3Kr1zk4/3CM3IBPKj5JrVA1uD1VNWtCujLfQfXrtYxpMnkdpXYWtg7a0HgDsi6Xp1O3pgloJ6nla7MjMLtw4qVs8/Pmt0inTs6dPIGU9WmQz0zyrm0dknAGAepW1I53NnPXtiK0h4kRELMq+HrV5JdRaf/wCFdm6gA2XAHqofpxtmPflS8aZtDy541UWcWzwrYud/yGY/6VrafoFpbEOp0mtcOzV0At9okAIjaQiYgpxxIqXnZJKmwdKkAwAdPblQrWVKs0h7QZ591aptG1MQZiVdUcnJnN6p4M0PU6bmX9hRrtcI9bQYXhnxJ/4erUsr3vhmr5BALv07zLT9DiP3X0uDKHXpteza7I90+VFKW9n5s6zpF7o1261v6L6dRji0z/aeVQPK+6/iP8OdO8TWdUOt2trwYqAZlfIHj3wVf+E9QdSuGONCSGu9pQpWKUPaOTTEY6pwJTJmRJhc0+lxCO29rsENeYVdL6oSAlXrVKzg6o77KEe6SeECrdkXfKUANk+xKsc4PCjEcJikWbSkJB5xwuh0GKeo0arQAWOB5XMMqOGJwtTTbzyXfVJ7HDs+pPCniqn+lotc4A7QDP8AquupeIaTmA+a0SvmfQ/ELWtY0vEfVdZQ15ppCKi5JJpndGSo9klPKhKYmBJWFo7aCEgcoFSsJIlDq1Tx0VC5q7ZAQ3RUI7DVK/qwUqVWX8rKfc7TlPRuhvjGcKG9nQsXs6Sk/cFbY4QI5WLa1sABaVN3CuL0cuSBbDoPsiAg8IQOPspNJTMkTCSiHYSlIqgVQEnCZjSSBHVEIRqDOqGU5Ug1ClnjoimhJU6Q9IUjITM3K2V/Lg89VI4yeFN+BjlV7mu1jDJH+ib6AFd3Apt5wuP1W9/UXrKAMlzsD6ZRfEOrso0nQTOSuU8MXLtS8Q1Khy2iySexOFlJnVix+2eg20ta1p6BaFMFzcFZ9s2ZknmVo0MYUpbNH/Q7eEalwUMCSAiN9IPVNdkhBynf0UQnJlMZF3KruBkolX5vsoEk9UNhaIOUDyilqG4QVL7GgNXOQq1QmICtPBDSqlTrKTLVWVqriQFesx/Bn2Wc/JAWnbgtoJLfRGZqkVnRuKgOEznHcUg7Cb2YehiIhMptMynLZMxhMdgqbYwVYbmI6IQBnKmCW8AGUg/hZp1S3HRXKbyDuYVnNJICIx5BGUJ70Jq0dVpepkODXuW6yqyvSIxJXAUqm14M5WzYXxbAJj7rpxZb0zmyYb2jbrUWGZaFVqWjSJAhGpXAqN5RHxtxwt3TWjFcoujKdQdnrB4QnhzcwtInnAQnMDgSFnRrytGcakc4H0SFQExOFYqUWxO0SqzqDg0kSk0Unod5B4QyRKhsqdlDeWmHAiEmNKwx4UQ0yo+a32Tb290rBJkiDJUXcJb8Qol0qKHTFEqDjhPJTHIUuVlRTXY0hQfJJI4TvxCjuxws2VoZ3JU7cHzhKEXTJRrP1VQOFUe0S/ZtWwIyeMK7I8tVqDcfYBFcYYR0XWujlfZy/ju5fb6JXrUjBAM/hePWNTSjcUNQY1927mtTifUcHnC9j8UusDZXA1B58nynGB1MFfO3he9qt1i9t9OpOqWoeQHFkwJXB5MeaOrBJJUepeMn2eqjTKGk6ULSrUEF7iG9BzCeytfDOj+Fbqz1a5YzX3bnbKJO6egb9o+64bXbe5uK1Cs2s5rqZgGSITW2mUKbK93XunVrkZ2gF2PquPHLh0GXC5R4o7/wtcULXR7Z1zVHqEmnumO68f8AEN1Qr+PrivZUN9AOG5vQ/VdQNT0y+NKnTvC17Ad7HGMAKWnO0C3u6tX9Rbs3A5f3+q7oRajsyj+rM29vqr75htaQoUqrdrmU/wCaFlMp1LPVSx8inUmASev912On6rbVbpzNHtKF1VoD5zEE90DW9Iqilbald3VtTNRxD2Nb6m9ePsuNycZUztUlJWi/4Y8OU9Mca10X0/1IJB3CIP8AsqzU/ROuqlm+6q1bVmNwgfhed+LhcBzKthf3FS3A2glxA/HZanhzw9c/o6NW6u6zqdYSS2YXZCpK0cU4tvZusfomhvuhp5oikck1DLiTysOx1u31MX+7UGWgo4otayS/9k3inwB5WmVr2wqVXlo3Oa49FyuhWTHUd5+YHoMNPulnjFxv2aYF+1ejoLu9bSsKIoXVY3VR38TADSFjeKBWv7uxo1KlRtN7mteQTMYlXLijTYxlQuPlgiHEHJnhG1Czr3NxasD3UXEhwMRIxwVz4dO/Zrm6Os0TQvDVPbbUrbe8gS6oJM9VzPxP8MWWhOs77TqtNnmO/wCWMZXV21jaWlo9orVHXjoM71594wuqdRop3NZ76tIkgOMx9Au5Ons5H9h6H6q5tGVLmo81BG2TwtLSmvpF1So6fryrNR2gv8DWlxRvKn+KmNzGjHMLOv227GWb7e5qVKjgDVZEQcdfyuLKqZ345aA0NQ1HSfEb32VmLukPU+k4S0zHP5Qqr7zV9fqV61rRtCWHaxphrROYU7jxFYW9/U89talLBJZnccLEpajU1bWqTaJdTotJ9Tuy6Um8ZzSf7neWGk1AxlVz2kBnTlVzXpNvLel5oLg+InMqFnSq+dWfSuv/AC1Omdxc6APouL0G3udT8SG5t3EUaVXduJ6CZhc+LDbbOh5aaR6pSIaf6q0xwJEKhSeCZVyi7iFZ1xd7LLcFWKRk4VQOzwj0XAHnA5PZNJ+im6L9PAgqwKgxCo0nw49RHMo9I7jKQ0r6LE9SiMIMEcIQzg/RGYI+yGJ9Fj+VLaR0UWOnCmHmc4CDOgg4A7JJmkEYTzhT/wCjJkp2uwZ5UJTtPRVaDosMefSOmFZY8AgjqqG/pCNRf05gShbIo06VWSibxvyeVQp1PVHBRQ6XjKOias0gSQGs+Y9FvaVatp0g52CcrC0yn51wDOGx911NEBoAbwAuzx43tnH5M60i6ww2VNtRkxuEnp1XC+PfGVt4Z051arl8Q1o5JXkXh34z313r9Kjd0GMoVH7RxOSuz/w4HjfbPpsGZhLjlZmmXrbi3ZUEZE89wr5qtPCq0Q1WmGLyR7pxUEZCrtqApGoD1RaJcfottdMqW4TCpioBOVIVQJJ6J2LgWSduTwhCq2TBXI+K/F9DTAATLzw0Lj6HxEBrgVKZaCe8rHJ5MISo9Px/xOfNDmkevO79FEkHqud0DxBS1OiDTeM85W2XyFSmntHHkwTxS4zJ1YIglcl418JWHiLT6lG8oMfuBAdGRI5XTbzPdRedwck9kx/p8h+Lvg1UtbioLEvDdxIHsvOtV8D6tYvdNF72jOAvurUrOnVBLhP2XOXnh61riHMaeeQpcnFjeOMj4Xr2lxQdFWi9p92oDiATOF9h6z8PLC7a7dbsP0C8z8V/CeiyjUq2zSwtk44VLLvZDwPtHhP3B+6Uq3q9m7Tb19u/LmmFTnutlsw/9ElCRIHJSnHsgYkpjCSSAJ0q9WmZa5XmavcNaBvKzkkDs+2C73/dQqO4k4+qHvHumc4EEZXm6R7TB1qgHVZd3XHWAr1YZysq6p75UN7NsRSfUL3kD8o1rTJcJnpCalbkOzC0LegZER0U2dbyJI0bJkMn2WlSMhUrdhDeivUmnnEK0cGR27LG7HOVKmSSUNEpHkKkYE/yl+VINjslCLGM3lWKUDjhCaDnhFpenlBL2XKXyp3KDDj3Q61UAHPHdNCUdkLiv5YMmRmFyuu6uKLHEuA5R9c1WnSY4B8EEryTxX4hc8uY1xMz2/1UuXo6sWP7A+KdeNVxZTdJg54XTfCu2c3Sa928ZrP56/VeS1K7q1Rpecknle8eD7YWnh6xpAQSwOcPqVnJnRf0dRbRHSSr1EGVQt5IC0qThwOyIsiStBuuEcRCA05BRRMK0RdCJiTmAmFSfZSJwR3CCRHKGJMm4g8kFDSSUjEhuB3IiSQ06APGFTqMJHB/C0HNx7INZvpwkWmjL8omq0e61Ht2W4H7oNJoNWeqsXp20wP2VxVIwyu3VmW/Dim5Se4FyaR7qQXRJh91Nh9XOEIKQIASBoO0Aob2EHEn6KTHADKmCDKYkC3HpIUgQXd/umLDEqBnpypoOywCe5RqVeAGkxGMqmx7hKIHCMobaD+G/Y3gaQCR9ytinWD2SI/K4ttYtgCYW1Y3ggBxMLfHlpUzDJB3o2ZEFKY6xKEyq17ZEync8YwVvzTMlrRLkZOE2xv+YIe+Bg4UpHb91LYqIOZlBfQDiSWg/UKxvaO6gXCUqstNoz6tpOW4E8KvVtqjMtJIjha8CFF7A4QiiuTMUmpTMuB+4UTWJxEQtipRpuBB+YKlXtBtmfwocdFxkmVBV7pebPsoVLctJg47ILvMYJiVm2aJWtFkmeTP3US7kc+6q/qCMPBHbCdtdruCobViqg+FYsf+cIVLzGkwCr2n/PKcXtCaN+jEKVU+g5Ex3UaSHcHaCZHZdKkzlq2ch4pp29etRo3tfyLV5irUPAGZC4SlqHhfRa+pm2vqRosdtBaz5vfjK7HxvT/UabVZGc8LN+GXhrTNe8N6nY3VGibgkxIl/HJ/ZZ8d7HkbStHnjdVs9TrsqW1Z9S2L4cO2eSF0Ou2LfDFuy802o11O4bD2v9XPbsuNq6Pe+FfE1xpdxaucwGQD+x+mV6BpfiKlc6bVstUsKFTYw7STwvPzQcHo6YT5RRy+p+DqTbFuq1KXlPrjdAPH2Qa2j0r/AMMvZQtg+t1LRkH7K5c1ze2jKP6pxqsO1rAfSfYJm6vX0CzZRpvoivVOQ8jE/wB10YMl6ZlNcdnn3hyrc6PrT6DC5rmH1cyM4ld3U/T6hXa6+qhlGJwfyg6frdlpFLUP8QtG39a7B9TGgls9PosLwxqzK+oV7YUCHPd/CY4EnnhPPiv9kVhyVpnSapWsrPRbig2i+tQn0VCwn8LT0S8os8PWVTU65p0wDtpsABMcLUsNB1PxFbv0v9O0VKdPfD4ZA6SfcrzbVafiB11V0epYCj+geWyZEwY54Mws/HVaYs01KVHe634jbeaRXsdGtSLqozYxpmTPU9Fwmm1HaDol1aahZkXlU5PUdeU1tqmtaIXXDRRqOY2c5j7o+l6hW1ei/VNVoGqQZLQMFa5Oh4lTM2tqd1X0+2tnsaKDXAiAFm+K7y93UDTqVCW+kGcDjC6HULOu+pTfbWT2Orn0U4OP+6q+NLG4tKVna17V1KuXAu3tIP1WOP8A1ZrNrjs3dD8OUG6Zb3N9qFd9arG/YT6ccBYnxC8M2FvRN1aPqMI6Pkk/ddFSYynYW9OpcbHAAlrmwePeFieLbht3pFekyv6hxGZEcyurdnJdmd4WtbO4sKTr2o4UmySW4+xW+G+HGUm/qalxucYY8EEYPBWL4IuLiro7rEUQ+k4ndU2z36rb1Gza+hSotYNrCILW/hcGVpzo7oajYi3SbatP6aiS8SypUHI9p6py7Trxn/l/IbdAZYzBA+3CxtW0Opf3QpVbl9GnTaNpj2VK2sWaPb3NVj3P2tI3nErqjH9KMZSqVi1e4deUK2n6dufXmHkGBCs+F9MuLOyh4LSDJhZeh0azGXF7UIO47iJzC6nTbqpcaX+pePJtAYJI55TtRVCi+UrZq0H4G7mMq5ScAJ/2Fl2tVlSm19OdhEifqr1Nw2qWj0sb1Z6D8O/C7dZJu73NsDDWx8x916lS8M6ZTpBjbSkGgRG0LmPhnWZT0Gg0YPWOq7dtcHg4Xo4ccVFHj+TkyObpnO3/AII06vLmU/Ld3YuY1HwNdWwLrWoKg5giCvUmVmlvVE9LxkBVLBGSMoeZkg9ngtzaV7OsWXFJ1N3EkGD9Co0+fde1ajpdteUy2rRY6Z5AXBa/4RqW2+tYkub/AJTyFyZPFlHaPSw+bHJqXZyu6OETHsgEFhLXD1AwQeUUFcz06Oz/AMCMdtmApgznhDpmCVKR3KTF0FlRJHfKh5gmIKQMlIUuggMolM7TKr7oPul5hS6F6ot7zM5+qs06gMTCzvO2CD/8J7S6Y+u1jJeecZhXFN9Gcnx7O20amadEO4n2W2DAkGFmWEtoU57BWbisKVMuJgDK9LHHjE8zJLlI+ev+Iarctu7fdPktmY4C8f0y8FG6o3AkljgR2K9C+O3im3vtSNhQglh9R7FeW2zahoyGnaMrS0ZSlurPbrD44t0y2ZSdb1HEAAmey9Z+HnxDsvFduHUHDzOCw8hfFt/JOQR1XtH/AA36Tetva128VG0DAAIPcZCdfQlPk6aPqtjwRiCkXCMCFXoNqPpDa0kARhO57mmCm/6QiyMjHKy9cvhaWj3FwAA5JVsVC084K8/+J2pmhptUMd6nCAsM0uMdHd+P8f5syXo8y8Raw7U9TqVS/wBE4ErObXjkhZnmEuESnNSMrxprk22z9Pw4Y44KMUejfDzXG2ly9lWoGtJkSYXsVlqNK5pg03gn2Xysy7dTfLXFpHUHK6/wN4ouKGo0qFWo5zHmBOV14Mjjpnz35j8R8t5Yn0P5vEdk27PKz7Ksatu18gyrO8Rlehfs+IlGnTHrgEGFmvlryJj6rQc4EQJWLrtxVtbY1adPeW84lJq9gt6DmD2ysPxHTYbJ52jjt7KvYeK7S5eKdb+E9uCDEqxrddlbT6jqZkQSpaNKaPjf4jCPEtxiI4/C5USeSV1vxHBPiW4+sLlHAgYyumDqOzz523og+eoKm0gAAGUMvk56KTMiZkK69kdEg4d08juoRIynpjO0DJToaZLBSldRo3gvUNWpB9FroInIwU9z4F1ujWcwW4IHXas3JJ7L4yfSPqZJJMfZece4CqAF2VWrUlc8suyEzqRhTQKVMosoyVfoUoj7Jm0oMx+yt0gAMIopybD0aYgTgIwgcKDeFIT0V0YvZJFpNEmeQggotIxIQTQdJQmU/XBRQUTbzHdWGsEDlBpxiYlWDhBJGo7awlYOr6m2hTdJHCv6hc+W10GQB3Xmni7VvLpPaHHqg1xw5Ozn/GHiGS8MfkrzutXfXqOe50g8KWo3X6q5cXEnP91X9gpaOr+F7R7c3eq2lGJ31QIX0VaUzSpta0ANa0N/C8S+Gtq258S0S4AtpAvyJAjj917fRJnqspsaRoWpMZWhQHU9Vn0CMZC0bfhCb9EsshgnkqZyUFPK1tEDlxTEyMpkkrASSdMkwHTIlMDMj8qYYDx+EUAEMMjCd9EuEQrbWguGI+yn5QTohspU6AZmJ7qlqRhw9ls1KYazJxCwtTMH7pvSMm9mcUhyFGRKTXDqVBp6QRJNM8FTbEZhIdkgZThxH0TSO6aR3RYgzZIBKT2yDHKg1wgKQcOp/KZQPI5SRSAcobgQeMKRUOBhTZUNMyCZ90EktMqQO7JASaB9G1p96CPVE9FqCq1zQZyuUpktMtlaNrekCD0WsZ+jJw9mzvzhNvz1QqdbewEASndgStf/AAzok9xnCW53shT7pt2THCLEv6ELiMlLzB/sIJf9/qoPqCREJXRVBy4ZP3UTWnBH7Ko6sATJEoNSvDcESpb0Uoh67w1xMfZVqlZoPdVK1cnO6FSrVwOSs5GiL76tJxPpB90AspO/mj7rMqXJB9KlRrlx7lTZSWi9Up1KUuBkBamjVBUAcJhU6bgKLt2fY5RPDrpqPjABx+U49kS6Z1jAAwSTlUr6ptaYVtolZ2ouAY4LpSOZfZg6hFSk7cJEwsXSry68NXta80+kysaogscMf2Wjc1JcYdj2QKjBUZtcAcRJV/0pq1TPNvGviytX1x+o6jTaa5geUwGIHTr/AFXP6U2rqVWvqdvWZRBMG3c6CR7LudRpWdpqk3dl+obBiRI/f7LgPE9A0r91fT6LqTSZ2tmBgdlyZY8yoNR0b+mU6Fpc0K9Qiq5j972DPCr+Oq1r4n1SjcMoi2pMMDpx7LP0O7uXuJNu9gfgmD2WtQ0ijeVKz69dtIU2yAXQSuWD+KRu0si6Os+EvhS01vVq1O4H8GiwOOInIGE3xX8J0fAet6fr2gB1WqCXGjVgiRjAHRZGg+Ka+g21R+lzv4c4Gcf3WZrnjy41x76eoCpUe1vLui9GGaEo/wBODLjkpHTWHirXiH6tWreRUuQGllGPSJxIMrkr4aifENf9Xeur07iHeY50ZPfphYGo6zqFrpbq1KoRQeR6Rzz1Wr4eczxNZuFfcHNbEzzhcU04y5ejWPFqvZ6Pb+Frf/8Ap/eXWwPcKbnPrCDge64bwDqOnUrd9rcXYcwOJFJzRxPdc9dah4h0anU0arqV3/hb3f8AKDyAWnkSOn1Wb4hp2FOnSbbBtGpt6YK3yKM0uBzwy5IT/bo9fu7+0q27qls8B1P5CDlsdsrlPF2o1TY2+oVarbt9Gq17mvMl0EGJHSFw+i3Lm6e8VLgtAPLjk/ldtT8OXbvDlS5L6TqL6e8dZWHxOLvs7fkWSI/xC8XV/EVewuv8Np2FJlICnTEOJ9yR0XF63Y3NbShdsuGgO+ZgMYU9O235azUKzopHbtngdkXxk3TqVk1mnVHlkiW7ufstlJ2cqfo6HwNrF1Y+Eq7ba0oemQXvIk9OOqDqOsagdNZXMUnTO2Ofp2TeD7W01AWVpcXTbGlUc0Pe7AA6nK0/iHqfh7Qrx+nWV+3UQ1glzACGuA7j3WTxNyujp+VQSic7d21fUn0KtzeulzZIpmI+qv6tdWj/AAc+wpCm2oHj1nLjHK5e18SOq0qlK0GzdhsicIFOkafqr1S8zJHAH3WijJaZhzbZs+HLWl5Rp1KhZbgfxHE8jmAtHU9ap6iGW9o00NNtxta08vPc/wDwucu75goMt7JwLiZcZU9Pbc3lQWtGkCZy4IcPZUZO6R1GhvNSzZOIJGO0lblhRq3Nw2hQaXPIjjH5VChptSxpUWEgF2F6l4WsLXSrMPO19w8D1GDCcY29npLJxhRueErWrpdhTpVHS7quqt7sk8hcq253PGY+/K0bauQB+5XXjdHn5bkzqaFxPCvUrg8QFz1tW9Ig5+q0beqCMlbxf2cs4G0HgtGR9kN7GvbHKr0qkYPRWGPgytLsyqujjfFfhgXLHVrZrWVueOV59XZUo1HUqrNrh0he6OIqcxHuuS8XeHGXtM17cRWHEDlcmfAntHo+L5VPjM82YdvHVEa/GUF4fQqvpVRD2mDIgpBwXBVM9Zb2g0ndPVEOMhV6ZgjqUcuBakJrREk54QbmuKFJz3wAApOK5/VnvvtSttPY7aKjwCZ6dU4x5SSJk+Ks6bw5pNzr7vOqh1Kybmf8wXSmvpenXdOxt20zXgTGcLTrNZovhoilAFOniMThfMl942r2PiatfPeXO3wR7ThelGCiqR5c8jk22fVFA+mMdwquutL9Nq7TDiMQuQ8EePNP1q0p/wAZoqEZyF2pqU7mifU0g4CpvVGKas+I/H1OtS8R3jrgEEvJ9QXUeBPDV1q2lOuHU3CmMDGCF7x4g+Gmla1fC4uaDC+eQDn6rp9H8O2mmac21t6NNtNoiG4QxcUpWeT+Dvh/pl5WLrugx7mkYcOo+y9n0DR7XSaLKdrTaxmMAQh6dpFGze57AAXHoFqgu2xn6Kougezc/WUaNEBkYHCya1UveXGMklBz1SHCqUmzOMEtonukGOYXnvjvw9c6w2KLy2DkRhd8cDE/ZRLccLGceao7PF8h+PLnE+fbzwZqlD5Wb47BZFxoWqUZBtnGOwK+lHW1Mt+XPdAdptu8HcwfjlckvFS9n0GP/wCimlUkfMD9M1HcR+lq/hdH4M8N6hX1ShUq0qjKbXAkxC92/wAGtZ/5bP2Vq2saNAja0NA69ER8dpjz/wD0LnBxSCadTNK0YwzI7qyMQZUBgY/ZSkCN0D6rrWtHy8nydseSqWs12UbGo6pt2gGQVDU9Ut7Gi51ao1oAmZAXjPxB+JlOoKtlYOLpwXAyAmxKJs2d1peuXteiDTFZpiR7HCsX9nf6TRdt31qBEAHJC8O8K6hdU/EtGrQcYe/1CTnK+o9ra+iB9SCdoIP24SUdl8kfMXxI8I3DLk6mXTSq5LQMj/Zlclb6KH05PP2XoHjvXat5eV7Pd/BpOIC49tyaTYWOSUkqQ8eOL7Oc1PSvKkBvC597XUqkOxC7e8rtq03By5LU2tD3QcyVt4+RvTOfycSW0V2OLjBhXbRgFVp5hwWfSPBlaNnmsz6j+oXS+jkifV3wus6D9Etv4bQduV2tTR7V7pcxk/Rcd8LqgOi220iNsSvQGn0hcTdnpQ1FUcl7pwZEhMnC5ov0dpNvCfb7lJvAUlotkkdvuUZggfVQaJKIMQEMaLFPDEpyoMJ2jKeT3Tolkk4McKEpSUUKgzHwACMo1L1EHhApDcYVimIGEiJbDMEGUqz8dk/QEdVRvaxptP7o6ElbMnW7sMp1ATGF454y1AEvAd3Xe+Jr/aypkwvGPEt35td4BPZJbZ241xRlF0unrMpw+DwghxDRlTYZAJ+6APTPg/bbql/dOb8oDG/der0XenjPZcF8KaIo+GnPIzVqTPeF3NMyQfwsZM0iX6JlzVrUjtaD7LItskfZadMk0xlKJMthqZIwcqZlDGHIjeFZmhxMZTjBTJ0wE7lTA4ymaBHupgSQFSQmO1uT7KbBtM8p9oAMJKkiJdhhyESYyhRBkcqQkgykJjXB/hjHdc9qRlxwt6uSWH6Fc7qJJKJdEpOzPJATbsjCiSUm/MPqsTRIsMOVJDHsn3FCZVE0kzeE/wBExDgwITg7jHCinBg4QAcOgKTiHDAgwgNcZ5RGnhIAbwQYTNwrBAPOVCo3EtTFQzHwIhSDjPp5QhiJ5UmkzgpC7L9vcuaIJP5WgaocDtIhYO4nqrVGsQIBVRk0S0jSLyDnPZRNXkIIqF4yePZCquI4Kd/QlH7DPedvKC6qQJJ4QTWJmSVVq1iAYUt/Y+NdFipWzj6qtVuB/sqvVrENk9pVN1WpUdtpN3OSbspILXryTHCrTVrOLaTC53Xb/qtvTfD9W6cHXL9jIyOF1NlptrYsGxjd3sOVaxt9kSyKLOJpaPcOaH1gWNPH91Zp29K2GMu911GpuaKJ+i5G5eS8qcn6McZOQO5rmHEGDC0fCbjUYXcZXPXTyWOk9F0XgymRQbPWVnjtyKmmonXzGVj6w+GOK1z8p9lg6w5xYZP+8rtujlgnZzrsuJnqkZ+yidwJDRiVHeeP2U80dPxNojWt2ViDVptf2kKhf2FKrRNMUWbekNWiHVAOEvUc9fopeSL0Cws4u9s73eLTRbfdVAkyEGy0kXDXUr6mKF8MOB6/Rdo2i5t5+op1HMqEbf8AYVO90ajeVvNuXPe//NOf2XLlhy6NViaOeu/D1rSsS61rCm5sFzQQZ7rhtb0kMY648w7WmSGjn2+i9TGgWbHOIpOJODLjn90maBZNaWi3bDuQZyohFxHLDyVM4rwj4do+JtMu2UrqhbU2UxJq42/WVzej0hoWvPsbvUWttmvINSk7dIBz9Z6L16hpNrb0KlCjb02Uqhl7AMO+o6of+B6cBAsrfH/QMLbk6pnLDw5Rlys4zxEylqdN/wCnJqUBhlQiDK4C30a6uNUd+qY+oxhw49uy93/Q0Gt2tpMA7AQh/oKAc6KLM84CUZOOjfJ4qmec+NWeFGeGbe0oU3frGubvqMOQARP7LqNQ8R+Hn+EdOs/DlCs1jGhrjUkEwACD3WlW8O6XXdNWwt3zB9TQU7fD+nU27GWlJrT0GITUmo8bM4eFwd2cHU8M0Dpr76jU2Pc4ktlUtd8G06Oi0b6hUBrRu2k/1Xpo0a1/SvoMYQwnI/7lUKvhelUbsNaoWRAa44CeOVPZ0PBGv6eSN0691Wyexz2saxsluAJVDQPDlHyK1S6cDUaYayeZ/qvSaXgS5t7upUbch1F0wyf9yhX3hqpaW7jRonfkyBP4WzyUqRl8Ee2jB8OeF2alqtOja09tIZe+MDutXxPo2l6be0adN4qtBJqEHtyFV0w6nptcsdUNGjUO0uGMdlonw0y8vRUp1nubySZ/KxuTkTGMU+jNtfC1jq1walsXW4iRHVdF4d8Pt0tr3DL5iTyV0+h6HTtmDa0nHPZatxaUaTTUruaxjRJMwtGm2EpQjtHL63b1Haa6s0EOpZmPZZ1jrd5S2htTAEZyq/jfx/ptjSOn2IFc1CA9wPCzab9zA5hwYIH1Q4uOy8OWOX9Tu9O8T1dzRWAzhdppWoMuGAsd2wvHKD+N2ey6vwvqJoVwwnBiFUJ+mPLhVWj1m0qekLY0+4bTcXOE9AuXsLgVKTXA9FsWlQcThdcHbOCUdUzfbX3udAiUenU4ByVQoUnOpkjgI1N5Bg8rQ52qNBpwncA4QevMoTKgJCepWYww78ql/SGn6OO8Y+Gm3NN1e1ZFXkwF53UBpvLHiHNML3cltZhAMheeeONCLD+rt2nB9QAXF5GFf6R6nh+S/wDEji2uO7CI1468quHEJnPyuFv7PTe+g73biOmVgatRrsu2XloYqUiHDHZa/mYwUAy+oWwTOIVQlTTREo2tif8AESnd6fUs75zW1du0juvAfG1i8Xr61qdzXZwux8f+Gr63c66o037fmkYXnz9Rrsb5d00kcT2Xq4XZ43k45Q0ZWl6lf6ZceZa1303tOYML1Pwr8Y9SsNlK+BqtHXqvM6/l1MiGquGNDpHPdb8V2cUZuJ9SaB8Y9Lu2AV6mwn/MF21h470i6a0U7qkZ94hfE2wn5cfQqza3dei4Blao2P8AK5ZPGuzWOa3s+56Gu2dZ3puKZE/5gr9O+tqhAbVaSfdfJOh31xTsPPN5UaQOpnMKFHx3rdneTQvC8NdgOBMqI/s6NXSPsZ1MmlvBaevKrmrTHLmjvlfOdz8bNbZpooPsaLKm35zuz9lzrPivrjjudsyc5VuL9EqSXZ9WmtTPyuB+6bzmckgL5et/ixqwefMayD2OUZ/xU1WrDKdMAdzOUuLGmn0z6bNen/nEqDruiJ9YP3XzFcfEnVjVpgua0OIBM8e69F8SavdDwN51K6oU7w097Gtlxgcu/bhDi0JyR6lU1S0piTVb91QuvE+n2wO+vTkdyIXyTceLtar/APMv6gHsYWZcale181Lms4HEEkpONi5o+qLz4j6VSqtpU7mk6o4wGgrivFHxSu6FarTsaG/Y4s35iRP7LwqzNzQqGqKbi44l3PTj8LuvEmqtvrW3pWYptc+lTGxreCB6if3R8f8AR/IZmt+KNY1h5/WXb2UzJLWmFz5ewGAZP1ySp6jb1bV4bc1Gyc4PRUhcUKIkmT3TUa6Bys7TwDbhupsr1o2NcDnC9S8Y/EG10/Sja2VVr67mwAOhP/yvn2nrFzWJpWTXDiYK7Lwr4Ov766o17rc5pMkOUTKhF2Y+o3dMMc+tHmPO4z3Jlc1XuQ4EDleq/E7wTc0NMZd2VCWsEENC8bFNwqEOBDm8hRxsbk4hK1VwZysTUHS6e616wikZ5WPc+p2e+FtijTs58s21QJgkj2WrYNBd/T7LOpMgZV6yqFr491tNnPHR7x8JvEIbQbaVXAOaIC9roXTXUmnJwvkXQdQfZXlOtTdEHPuF7/4d8QtuNLpVPMAnnK4px4uz0MMtUzYb8wRhJQAYKPTEj8Lkv6O4m0YCM4whBSLp54WqQrJ7h3S3DuhOIBxlMH5GEaGWQ6BA5TFxlQDs5SL84RsB984Eo9P5VVBgyrFN0gd06Ik6RZoj1KywwCq9L5vsrCkgVRxjCw9Xe4A44ytpxWbqdLczHZJlR0zx3xjfvYXgiD79l5hc1nVKpdC9o8Y6D+spPxBEkYXkOp6ZXsKrm1GkgdQOiaOq/opbpbHUqTCA0T3Qx0RqIDqjQepAH7Jgj3jwTQNv4V0+mR6vLk/ddJS4CydLZ5GmWtIfyUmhalB+4cQuZ9m1aNO16fZX6YkrNtDMrSpGCmujPstMOQiITD1RAZVomSHTjnKZOmiSTSAFNCCIDIV+iWTaQHZU0GZPBUw6BEJp0KSsKSIwSkCFCR0SLo5T0S9j1nAMInouevvnW5Xd6PysG/dDuFMtIF2UBlOAZ4TA5lOXeyxLCSEpCHuKdpwlQWychSpkGYQ04dt6ShCsMkobusBODJTsZJSaePqopDCYFneE4IIkKvuKkx8D7oAlUpzkcoREHKOHSOFBzJkzH2SsCDXkY6KbTBlCd6Upgz0TFVlyjUg88I7iKsQeeVmh09YRmVdsf1lFiqiN0DTJ25HdZ1Wp6fqVrvAqsIHXCvaN4dD6ra1zkDIEIUXJkuSj2Yun6Nc6g8GNlLBmF1NnolvZUhjc4cnuttjaVswNbAH0gFZ19eMYHQf3WqiodmLm5OkDqVgwQ0QFRub2WkDKo3F4CTHH1VCrccgH91lLLo0WO2T1G9cGgA5zKwK124uJPKNeVi4xPdZ1Qy4+y5pSvZ1QhQ1zeHjqu58IN/8AKscOuSvNrmoTUaIzIC9P8Jt22dIey2w92R5H+TcqmGlYmpfLA/3grZuXbcAThZN00PkFdcujlxd2YJpEkyDKXlR3Wn5Y6/0T+U2eq56tnep6Mry/qlsWr5TByAU3lN/yhDihOTRmeSTiMqf6cn+U/laW3sR+Ez8ETBTUVQ1Jmb+mJ5S/SDqcK8QmEdVLig5MoOtYMABCdQJlsHstGpzLVDESeRlHFFKTMl9F7cRwobStR5a45jKr12Aj0jKl6DmUjTAS2BSIIdnHspNaHcpFWyENA5S+iOKQIBlJ1Exj+iKE2C/lIVarRc4Eg9+VZ2mU5GO6aQjMdZ0q0i4phxHCsUbWnSIho2gcBWBSJdIx9kQCOQSntdEtIBc1rinQItacmDgdSvFfiRqviWpWdTqWdzTogESGmIx1C9xJ7n7KL27wA+COoI/rK1hl4u2cubx+apM+S9OtPM1Gky8a/wBRzMr1e28ttNgpuDmtaBj2XoeoeHdJunudcWNFz5+YNg/kLk9T8P22kUi+xfVdTe/c5rzO2exWmTMsipGXi+LLBK/RSYSHggYWpaVNhBHsskEy3oCrtvwCTxn6rGqPUW1s9N8J3oqUQ3cMD/RdnaPJ4C83+HtnXvNQcyk2aQyXRwvXHWQtrcnsO0LsxJvZ5fkUpUWLK+YKe12C3C4Dxr41Zoeq02TunkA5hdJUbUDHvBLTleI+KtMq6r4qc6s4uYwwOy05HK2oJyZ7d4U8XWus23mB5AA4MjP3VzxBeNqWR8pwBiQey8itaTdLt2tov2OPUFdFbXb61uGvrbh1Wqg2jzZfkFGdRR1GjeJ3UWNp3hGMbu616+s2V5QczzGQRGT1Xmt7t8swuH125urW4Bo1ajG9SlKGqYl+QfK6Op18fpb+r5YOwk56QssXod1ErAoX91c09tSu5wPIdlXbe3eQC131+q4peNydo7I/nFFUaorFxkLpPDFg2tXZVqmT/aVzNvQcA3jESt7T9RdaNECY7FJeNxZsvzCyaWjtdV0m0u7YtqNa4EYkBeSeNfhjaX2+pZtDH8rtKniGo4QQ4Jm6y14zz7rdPiX8/OOz5+v/AId31pO1heAD1Bj91yGoaLeWbnB9N7Y7BfWAr0LgHcAsrUNBs7yd9Npn2VrJo5ZuF6PktzqlJ+1zCEfTLqm29pvrENDeSei9/wBU+HVhck7GMb7ALlrz4WU9x8taqaejFzied6lq1N1RwoVhsjJnCseHNUs6F8ypcvaR3ImM8rpbn4U1zmm5wHTCzqnwv1FjgWOmD0VR4oPmX2eh2enaJ4qs7+salWsLeiS2PSAeQc5XkF8KFBz209sgxzxC6BnhPxLY0n07W7rU2PEPDCRuHYrGd4D1ck7tx+sp19DeSLRn2t2xlwx1Qw0HoV6f4R03RtXt6jrimXEDPqheeO8BavGGmfui0/CXiC2aRRqVWAiPSSP7pNFQkeoWPhHQbjw7qV7c7vOt9213mQGxxzzPH3WCfF51Gyo6dXqUqdt6aT9vzPaIwewXHs8K+I/0zrYV64oPMvZuMOUKfw+1ZxBlwj2z+U7VUyr+j13xLaeF7Hwm2rRpafTrkAwHAuH1zK8vs/EGmUTbOe5pNN5IbtmJ4PCPbfDbUK8ee+oRxBJK2LT4VvJG9hPXKycorsrhJnOeI/Fdtc3IFo01BtG58RJ6wCOIWW3X7nc19CiNzBAxzK9Qs/hdRYZfTGT1XQ2Pw9s6IG6iwjrhS8kTeGFt7PDbuvrGrvaDSIBwIC29J8BalfuDqssB917xYeFLSjtDKDcfddHa6UGNAp0wI7BYzzP0dmLFCP8Ao848G/D6hYBj67GveOfZetaRp1Ki1rWsAAOICPZabEQPrhb9naBgb6VlFSk7ZeScY9EX6dRu7U0qtMFrui+cPi78MLnTbmpqOlUnPok7ntbE/uV9UUaY6cIF/Z07mk6lVYHNcIgicLpSpHHJqR+eF4+qwuY+m5h6giDKoQC4mfdfWnjj4W2NzcVa1Cg0bzj2XlOr/Ca480/pAATj2/qqjlS7Od4JydRPIpBH/dW7LT7u7e0WtB7ieoC9P0/4YPs3NfqNWnUg8cLsNPtNM02k1lNlMuHWFhl8yMdI9zwf/n8uepT6POdF8GanWptNWKYIEjP9l2Fho+o2VuKLKogE/wApXUN1mm1uyltb2haFEtrUw8v5XBLy5yej6GH4LFgX7o2WfMFcpYjsq9EQrdJvpz7cLWJ8yye1vdDquHTARTDW44VOs7M9VdsRMnuUtwHVVnVMmTwkHE8JWMtGoCISFQDjKACWwfsjbZA28KrRL6DU/UAVbogRwq1BhiMKzTBBJKZDsM0wVaAnhVJHur1pDnADqkkS+gNUODesqq9wIIf9FsvoYOOFnXdAclXx9iUrMq8sqddmADI4XB+J/DYrMd6Bx0C9KDIM++UC/tG1qTnAZ90mjeEmnTPl7W9LqafcEObiSqlgzfeUGd6gH7heweM9Gp1WPJYJgmYXlNG3NvrdARDfNb/UKTX+o97omGtaejR/QK3Rdt64VAmY+g/orFF5XN7Oj0bdqRGPur9J4lZFpUkYWhReSqXRDTTNOm4OGFMkBVKT9pRS+eQrRNBt6W6UDc4dUmvzjhVaFRYD4GcqQqcKvvnnlPv7cosOJY3SZT7iqzXkHJ5U/MIESiyWg/mZgYKk1xMqqagHHKRrR3Tsig9Ykt57rDv/AJvutYvlhOcrJvRL8d0S60R7KREHCZSc0yorIsSeUyaPVKAJSe6kzMyo7SnaDlAgg4wpN5UW/KE6ksmI6J1Bpgpw4SAqTJJJJJJgOHHup7vdDSRQEiAegKC6d0ZhEkJncJMKByB1/dPuxz+6C4beUPcZ9khmjb3GwgniYXT2Gqt8sAGI91xcmUajcFjolXGXEzljUuzrNS1EbSW9lzV3qBe4+rPVG80V25+izrygQ4ubkFTkk5bY4QSBurknkoL6sCZMoZmc4UHn0rBezbikCqu3EzyqlQxwiVTLsdECo4TKhbKXRVeJuKY7u/uvWPDrA2zpznA/ovKaIm+pA/5gvXdEEWlPtAXTgTMfI6LN2fQsuuey0bp2FlVngOj3XVLo58fZBMT2OVB74MgwPqgPrhvVY6XZ1xT9Fkk/RRk+6qvuxGCUP9YQMSlaK4tl/fIjj3UHmYys91y4jBCH+odBmJStDpovmoByUN9UNAPKo+aT8/2UTUcQlY6LVSuCRGAoGrg5KrF5HKjvPvCV2GywKk8Sn3/VVt3ZMahnlLQUwtYAnESq++DCiK0cqvWrieeEil/S9TqkfRTddgN6LEqXuyYOOioXGouIgHMo2FG6+53HBzwj03SFz1jcOrPg910FCgXMkHpwqj2JqiRqBrgrDCDTkcygii4ZklQeHDMwFRLVoI9okYQ6jtoEIfmkEAykKoniUIQM1N3MrA1pgqUqrSOc/dbb3bSen0WPfQ6pEGEf+DVnIU8gA/ymMqzRcPljBUb2n5N04QYOQi29JzyMEEce60S0Lmo9nrnwsv7O1tHMLg2qSCZxK7u/1W1LJNRvHdeEaVTdQfuYSCY6rpG1y9sPc8nvK6MeRpUeP5WSPLkdVrWus8l1O3EuOJXEPpbalW4f6nn1FaVOI7hDuy0UXCBkK65HmZfJ0zi7u4r167nOBGfwuh0Bz3UvUTAQRQaf/TBWtoVNhBaWwQumCpaPLTudsnWplwgLC1/TXVbaY+y7Vtsw8IN/ZB9A7YScbNm+J5RQtnUXEbSMrTtazmCMjPZbztOy7g5SbprThwyj42tnOpxbtmULtwOJVj9aSyI/dXzpbYMAcYQjppH8uVjOEj0MLxMpm4LsyQj0KsxuP5RWaeQZgQrVvp/mfMIPdYO12enFwkqQ1GsGn39irjLj0g91NukhonPskLEsJkpqVES8bVoQqbsIlISchRFEtx19kUNcBgR9VcZr2cU8El0icNg9QcJgyl1A/Ch6gFB5cYgnmVvzic7xzXZU1B9Bj9paIPsqwq2wGWt9pAQ9Wt6j/VMhZdO3fUcQ5xgZWseLOZyyJ0jY822/ytP2UXVLUmCxp+yyf0lXcQ0uhT/Q1I+YhU4xZPz5YuzUYLcvAhpH0Vql+nB+Vv4XNXW61EeZJPGUaxumwA6pn6qo+JyVnO/zrxz4tHZWlG2qDgArTsHWVGuy2qtmtUfDTskflcjRugILXj88rTsdYay7tt21x8wRKyl4vHZ6WD8yp0mddettLGiKlem0MJ2+lk5U9JsKNekaxYCCcAjoqOpa5amjSFfbG/qtPRNSoVbYmk5pbuMAdli8e9nq4/MUtInZUKF7Se5lIM2vLOIOCrWm2rXCoC0Ha6EPRqrDQqREF7v6qxpFZm65EjFQpcEbf8jRasKTXVq7djQGPjj2Vqg1j6z2kD0lULO5a24vM58z+yrWmpsGpXTdwkRKqMf4J5o+2bVO5pm/qWgpkOpsDy6MZPClvZ+pFPBJE5K5HxBq14y4qv0t1PztoBkA4Wbout3361p1NzTVLTAEDH2SlKntHRix81aZ1+quoi4ZTeGgbC7heTfEHxPR064bSt2tBLTBAGFseKfE3mXTbWi4C4LTHfb1XjXj21ua91ReX7jsPP1XNkTn0j3PxmPBjmpZmR1PxL+oJc95JmYCwLnWzPpgKtS8P6lcH0MBB6AZXW+GvhxcXVQPvQ4DtC51gS7PqH+Z8fFGoHIf4zV5aeD0ErQpeK9RpsDWfKP+ldj4i8E/4dal9rRBcAchsnC8uutRq29d9KpSrbmnMNWkcEWefl/+kj7PpOnwZVumWhvJWax5BHZGbXDeoWUXR809F2o4QMrOuKmCiPrS2R+xWfVqEuz/AFVXYhw6eCi0XkGMKswS/HHWFaoMyD3QBZYNxyj02mI6KNJkCeVbpgYwmhNEqbYARQYUFGo6GlUnoVEn1g3srVlWIc3jlYNaqfMdCtWlYtcCSfpKAcdHXCq00wTEkKhfVWBsyJVWrd7afPTvwua1nWRRmXCB7rSMr0YqDTN39Q0uIkd8qFe6ptY7c4RHRcIzxA41DDy7tlVdT8SbKUF0YzlRdaOuML2WvFt7T2PIcI4H0K8hv6jXaxbEHHmtP7rR17xEa7i0OJzGFzHmPqVm1CTIcDg+4KVFN1o98p1HbGkZED+iLSfDgO6oW1QPtqLw4AOYCPwETzI6Z9lzVs6YvRt2tTafqtOjUwFz9vUJ2wR91qUapAzE/VNaE1ZqU6vq5RvM91ktrbTKKyvMkmPqVVk8WaPm+6Xmg/8Aws/zR3CRrAYJH5RdhxZoioD1T7ws3z/+r90/nnv+6Y+JomoTBnhP5onlZ4rf9X7pvOHdBLRo+aEm1RMLO833Ttqjdkx9UexcWbTTNOfZZl44+YZVyg4mjJJH9lRvIJP9VT0jl90Vt5MyopdSmP1Kz7LHSSHuptAhAPRINxynhNKbPcpCtEwMJKGe5Tgz1QOySdgnKZvIlTx0QkNjpJFKR3VCEoPcWxEZT7s8JnxiYSbAj5juwTbgeuVF2DggBQ4R2JE3Dc0ziFXqekYRp91F0EIoYGD3SBIySmeDHB5QpRQJF2lW2HnqrzKzKjYIBMLFn3TsqlhkY+6GtDov3NuHNlsys2q3aMh0+607a8a5m13Ke6oMq0wRA+nKzcXRSbOeqfMVWrcFXLug+i4iHEd85+6oVpiSSs4xZSB0c3dI9nAr17QR/wCSYZzErx+iR+qpQf5hwfdewaKYsmZA9P8AZdOAxzuxrxwBP1XPandGlU2mFvXhG4mRIXE67cgXBBjnutpuo6IwRVk6t8XHmAhuuXOwTj2WQbn2TG5xMmfquXs7IqjV84+yI2qNuVktuJHMH6orbgAcygo0vNHdN5jVn+eP8w/KXniPmE/VAjQ8xqXmNWa25gCUnXUdIToVmiaonCi6qREOwsupegHkDCE6/EHP5KOLHdmuawIOeEF9w0ZJysWpqADZBH2KpV9Szz+6aiwtG1VvQAQFQuL0QTP0WRV1AEwI/KqVb0GcoUWxckaFa7cZJIygCpLh6iqIuQRmPumZctDpwfqtFHQcl9nQWFwKZDieq2rbVQCBuxMLjG3kTwnF7t4x9FLQOSPQ6Oo03zBBMKRuGvbyAvPqWpuacOLfuVdo6z6RuP5KmmL/AMOwc4OMgiekoTwWSZ5WBT1gEcgordULp9JKpEuaXZrOeS0EjPss2+2hsiZKg++eR6ce3ZRDX1T6zhXGDZy5fKjDplG6txUDXwJGJU6LRTHqGVbqUHbZbn91SqtqzwYHSFsotLZxPyflei9SuQ1o2cq3Run91ifxIAAdKs0fNwc/ukpUQ8MZds3G3LwR6hBUK906p6S7qqLGVS0nPEo1rZvqEFxz7qot9mGTBCi5bFxOCFcouNrch5jYcGffCnZWWwA4RL2iXMOOnb3XRCZ5ObHxlo2aT5Y1zcjlWKf8UOa6IKzvDzvPpilUMOHE/wDdb9OzjMR9ltEpxtHOXNt5VQxOUHySSCYhdJfWc0y8iTGcLGfAGOQtezgyQ4SBU6TSACO3CMLdsQRwoUfm57K2SAMlS6NMbKNWm1s4CLa7A4YCHcOkmD+6g1xYZHTouLItnqePJo1zt2zAVWuQZjj2VYV6hwCR7cJfxHCeQuaS2etDJoIykD6jP0UjSG1M2QzrPZDq1TtxP5U8WgbVDFoEhIUw7EKs6ueMo9CqMZBKpWYtRfY1e2BbET9VQfbMY6QMrYfWaBGP2VF22o+QCr5y9GbhiTtkKVe0t6ZFSJ7kLB17VaDMUIJOcYWpe2rHuggD6BB/wugKe7Y1zvstozmtmM1gkqR5/Xv6lasXPa4tHQq3Sc17RFNwdC6KrpVEPJdSEfRXrPTaJYAKcTziUS8vLFaOXH+M8WcuTRwlzdXVMkMDgBxEwoaZqF/U1K2ad23zByF3VxpVAE7mn6Qs6vpVNzS2g7y39HDELNebN9noR/G+PDaRneLxqNSyp+S8gl4QvCNbWKNB1J1c+a1xcWk8grf0/QXMDTcXdSt12veXZ+6sX/h9taKjHupvHDmmCh+U29mn/GxLaLXh7X7zSbR41Ta5oe5xcDwJ6rofCfie0uqlVjnmm+od7Q7EhcAdAe54NzcVqzW5Ae4nKr3tg5xGTTLT6XDELSPkxOfJ48u4ntBc2iLmqazSyod0g8CF5q7WHDxFc7K5ayt8knmFzBZeFuypf3L6X+U1HQhXluK1MNc8kjgg5C9HBkxvs+b/ACnzxrh6OtNe9bqdW4dcA0XUw0N91i09drXXiMtp1Q+kymWyOJ6rmKtteEljruu6nxtLzHH1UXWVdlOLcvpOA+ZhIP7LolDGzzY+f5EKimzqbq4c/wAQ2xdE+S/+qpeJKk3VCduWHn6rlq1veMG911ceY3AcXmQPrKBTqVgR51arWd0L3F0flSoQNZ+fna1J2es+Fbai63pue1pPK7CiWUwQAO30XnPhK+2UGNcSD7ldjT1BnG5p/wD4l5+SMeR7vi+XleNWzRuKbKrHCo0EER3XI6n4Lsbu7dWdQZLgO3+i6D9eIiQR9U369nUt/IWTivR0vyJtbOe8+eSiB05Cp9VZptxH5XAj6l7C7yB1QKvMo5aZgqLmHd/qmSNQbn6q7RbmMdFXpj1CVbogThKxostENgqxTOIgoLBMSrVJmRHCsQ7AXZAUKtJ0OhX6NIk+6sOtXFmByqolySZytZha8yEWiRycdVp3VkQSQse6f5TD3UyddFxkpLQPVL/yqTjugcLyvxLrRddtph3PZdD4r1Xy6TmzABK8ivLt93qrQ0nDsrbDBy2ZZZJaR6dounValIVDJaYj7oXifQq9S0e6nIeAevZdf4HpNraRTDxLmgLY1KyYbd8NGRwlNKx45SPmOsH06zmVQdzTH7pNfBxytnxlai21mrAgOcSB+VhHkRypN39ntWhVvN0ayfz/AAwFd8w45WB4Lr+Z4ct2zlstH2WwXECSsGtnRB6RpW1XaRlaLbgNaSTAXO064a7JVuncTILjHbokx2zY8/6/lLz/ALfVY5ugMmExumnkoQJ2bQuvcpC5B64WE65ABIPCH+sngpjOj/UN7pv1PZwXO/qj3SbdOkZH5QB0P6ieY+5UxcxiR+Vz36onghI3UDlMVHQ/qjHzBN+oLnsAOZXPG8aByiW12DWYB/mCEl2D6PQrZ02wnkqpcvzEFWrTNmD3EqlXyZTfRxf9gIzlL+ZDcSCYS8zEdeFOi7CpwYCCHFLzYOUhFgOSEdwgmoCMHKjuKA0WC5O3CEwy1OSUUFB8d1DcRMH90NRn3RQ+w2/vkfVLeOyruOOVGT3RSC6LW/tyol/dVw+Dyk5wdGeEUgsM50qL3AN7oYdAiVF31T0FkvNHYpeaOxQ0ieYRY7JmoDiCg8FKSkSJyUxg3mJKgKh65RHAFp90AiEA2FbVg9VcoXZb6SZBWanbyEqEblRrbhgkAyM+yxNS051OXUhj3VmhVcwnJ6K+ysKjIfGUnFMfJo42gCL6k2M7h0916/pLSyzZJGB/ZcgzR217pj2gNggyF1rZoUGtPMK8UaMcs70gdy7du9l5n4nuduoOAPC9IqOmm5x7T+y8b8W3O3VqmcyrmrKwadsn+qPcqQueDKwRdgCMJzejqVnwXR08l6OgF0O/7p/1Z6O/dc5+tamN4J5ScA5HSG7IHzfuom+jly5x16A3lBfejElXwE5nS/riTgqFTUCBzlc5+tb3QKt9nlUsZDyG9U1Ax8yqVb4wcrBqX2OVVq3uIBVLG2yXlVG/U1EgH1Kq+/c7h0fdYD7ySZOFXfeNBOTCv46Mnmo3n30uA3KBvT0cucdeDmcqDr0d1agjP5UdN+snk5+qX6wj+Zcx+tHf9lOleg8lHBMfzI6UXhj5kZl050erC5oXbY5Vq3uC4ekjhS8YfMl2zfFxMwR+URhqvjaeeFk0nz1K6nR7UVNjiJSjhs5PJ/IxxL9SxpthUqEEkrqrDSwGeoScKWn02UmeqJHstihcMayCBH0XVjwRW2eFm/I5MnRQdpzAcASmZYtbwc+60/NaRjqi0qTX4Lgt/jj6OR+TP/sZRtHQYEz2Qv0Djktyeq6BlANmCjttpaDHKh4r0a4/JcVo5f8Aw53VkfZGp2EfyrpmW5B4n6hENANIkCYUf8dG68uTObdaECA3pBVq0tsiWrZfbgxjlFo2wES1CwpCedyK9CjjCKbdrzkY7K8ygBBSe0NOBgK1GiHJvRVt7QUKrajBlb9GoKjQeDCzrd7avpAz1Vug1zDnjoqouBaLWuBa4YI6rltZtjbVC5mWHP0XR1qsAnoMrF1G9pVWOpvge/ZVyojPj5Iw21w3k5Rf1IeYnosmsdtRwafTOFO3dkSsZSfo58UK0aTZd1ypmBiM90GjUGEVxk5WEj0cS49jhskEHhWGkBoAhAYx054KIWEGFk42eiskYoKSq9QTPGUQ0yMymLXI+NsUsyRUNL1dJlRNPacSru1wTFhJkp/GzF+QmUnB4OJIU6VN8SAZ6LQpUm8OCO1rWpLTJlHkjnrllVry57SB/VVX3LgRE56dQunruacEAj3VGrYU6mW4K3lkTWjkj47Tsyabt5AdlaNDy2MyoCwFMTPCBUbsMguXO7fo6ILgrsLc1Gu4CzarPXPH1U69R2fZZde89WHLWEE2c2byJLpmzRJZTkkSDjKhUviD6njaOZPRYNxfkNgOIwse7v3OMNeZ4V/HA5peXJLTO0/X0yY3Nz3Q6zaVdvIyuEdfvYJL8qI1WuctdhYzwK7R1YfOlWzs321Py8Eeyrvs2NaSS1c5T1atsaXDE91N+sOLTu+2VKhJPRpPNCa/ZGk63Dn46q0yxaGyckrFpas1r5PHRXaesUg3LxjutOc1owhDC3bFc2DDIPHsqg0qiTjdP0Vg6nSqElzmjsrFteUgRtcD2UPJkRosHjzdMtafpGymNpMdsq6dOrNBILuZEErc0YNqUWuIGQtjymQBtELLlN9ndHxsSWjz+4s7prvS90/VZFZmotqECoY+q9Pr2dNxzH4VJ+nUi4y1sq4ubMJ44JnPsYT0wrdNhBECQmAG1yLR4WB9GFa08xhM4HcjM+UIb/mKaGMj0hDQgK1T+Vv0CoRYpNO0BXKTCT2Val0Vyh1+yceiZF+zbBC0WtkfRUbfp9QtKjz9lpHo5pFK9ohzccrldZtvQ+BJhdrXAIyFz1+wOaZCU0VjbieK+LrOpVDgAesrjNE0CqNQ3PaRJnP1Xsus2tFzzLAVSsrSg2q2KYVQyOMdGzgpU2bXhS3/AE1qxvSFf1l+y3fnEGFO1aKdNu3GFieJLioyi4NdiCoTtg407PH/AB+BVu/MESP6Ljl0Xiqo59y7cZ5XOqmUek/D2qDoj2TllT8YXSlwg5XFfDtx8u7Z/LLV2T+v1WEuzqjpCLs4ymNctMdkN5gCFWe47zn/AHCEhMsm6IVepdknmFVrOIBVN73bjlacECZqfqTweCmNwG5BWT5ju6kx5Lso4oObNZt0Iyn/AFAGZKyHvIMBOajtvKVD5s1v1ZCd1yHNBJgrF813dLzXd0E8mazrqBB5VnSq3mX1JvdwWBVe4Tkq54de52r0QT1QHJ0e42YAsh22qhdfOr9r/wDZN+gVC5+ZTM5o7ZQq/MoqdVDHCgsfcTylu6ABJJAC3E46JJJJAODAUhUM54UEkwCbgTgptw7oZMJ0ColuHdM8iUyieUJF3Y8+6UqKSdColPumJlMkih0JJJJMBiR1TSCk5RUt0NIkSCIUHCYwnSSsdAnNdJgYURIOeEdAVJ2S1RKc4RaVUtyUEcpiTuhBPR0+kV3FoeeDxK0q9xvcBMrJsfTbt24wp0nONXJWkejKUd2adY7bZ5PHP7LwDxbdga3cCRIceq97uj/5Cqeuz+y+ZPE9Z51y7JPD/wC6tBHSssfqx3TG67FYRqP7pea/urUEw+Vmy667Jv1n0KxKlV+DuTeY/wDzFVwQnkaNd176TkIbr7bkwsQ1Xx8yFXqvDZlNRRnLK6Nt1/kncIKBV1AAyHT91ztatUDfmKo1LipuI3QqUUYSys6SrqX0VSrqYmA9s/X+yp+HrRmp6gyldOqbC6DtdGF7BongTw7Te11bTxcnH/PqvcPwCAtOKRDyyZ5BV1PPzR/VVq+pGeTnK+inaTo9pQe220XTqcNwRRk/uV4X42DW39bZTY0A8NbATikzHLlkkYrbqo8S3eR1wpCrWLZ8txH0KFZVHeUWT6SOFr7G/oZjMd1TiqMPnkZ1F1xXeGUaT3uPRoRWsu/N2eTUa7jLVoeGbipRruNN20r17wRpFnctNxXp+ZVLslxWUmo9G8JSn2eS2ulX1UtLqVTnnaugttIr02Dc1w3Ed19J2WlWXltH6enEdlYraBprxm2ZkKoNHH5cZv8Ay6PnKhZPa9oLTz1XYaePIY0NndHK9IvPC+luaT5EZ6FZV3oVlSqQxrwPqtZ0jzFgmncpWYVK6cO32Ks/rCA2cIlewo0fkLvyFl1RtqQCYUFLDs1qd4CZJ5WhRux1K5beUa3rVCR6ikpsqWFOjsaN2DEEGFcpXnvPRcpb1HHqrtKs+eVpHJaMnjp0dOy6ERIlTF03uFzgqvn5lH9RU7hDlsXRvuvJqGCDGEenfAYJXLis/wBWVKjWeXZKfLQQuzrhfAmAQhvuATkrn2vcX8laNuS5uTKweV2duPHy7JPvXW9UPZkTkLesrxl5Ra5hHvHK54021HEOEiVOxJtb2KJIaTwhZHZtjxKLOmc2Za5pyFyHiezr0AalPLCZXZzuaCeyVWjTr0qjKrQ5uVTbaNJQUkzyZlZ7nerAniFeoPkgFXtZs6NC7IptIB6SgW1NsjCzaZxxXFly3pkjIVyjSnoo2YBbnur9MCElGzdTrQ1KkIPsplkf/CIwRKJtG2VTjQcmyvtb15+iYNacApnH1hOwyQoc+JvHFzXYTyhCi5kDAReig9c+TO0deHwo9sGCYwMd04a5wMdUcMaOB1RKTR2WCyNs6pYIxWig+mBygPcGnBwr10AAs6tkr0sCUo7PB8yTxvQhVngodd7HMO6FSuqjmE7Ss6tWqbT6iulYInk5POklQLVi1k7Dhcdf3dZlU7Y+q1tWrPj5lztdxcTK6IYInh+R+QnJ0tAn31X/ANXhBfctPDghXHyOWRdHaDCjJgVmnj+TP2aVS4buy4cqRriQARx0XM3D3Y9RUKVzVbgOK5ZYtnsYs517qhDY9kmS1oJ5PE5WJbV6jnCXf7ha9Cq5zRMJQxbDL5BTvalZu6Tk8FV6FW5dAlWro7qp3Z4/orVrTbjC68eJbPKz+XJaI24rR6xK1rCrUpnLBAOc9FOhRZAwjhjWnC6F48GqPOl+QyxdpnX+H9aAY1rmn04XVUtRpPHIB+q8ut6rqRlhgytynXqGiHF2VlPwoHpeL+by1UjuhVY4SHBLePZcT+rrUmjY+OE3+JXX/wCoVzTwKLo9GH5Hkro//9k=";
//    String base64Img = "data:image/png;base64,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";
    
    public void getfaceid2() {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("webankAppId", appid);
        params.put("orderNo", orderNo);
//        params.put("name", "许根水");
//        params.put("idNo", "362331199102083018");
//        params.put("name", "邱宇骅");
//        params.put("idNo", "350721199012101337");
        params.put("userId", userid);
//        params.put("sourcePhotoStr", base64Img);
        params.put("sourcePhotoStr", "");
        params.put("sourcePhotoType", 2);
        params.put("version", version);
        params.put("sign", sign);
        
//        faceid = tengxunyunApi.serverGetfaceid(appid, generateBizId(), "邱宇骅", "350721199012101337", userid, 2, "1.0.0", sign );
        faceid = doPostByJson("https://idasc.webank.com/api/server/getfaceid?orderNo="+orderNo, null, JsonUtils.objectToString(params));
        
        System.out.println(faceid);
        
        Map<String, Object> map =
            JsonUtils.stringToObject(faceid, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> object = (Map<String, Object>)MapUtils.getObject(map, "result");
        faceid = (String)object.get("faceId");
        
        
        
        //        String accessToken = client.getAccessToken();//http 请求
//        String sign = SignUtils.sign(data, ticket);
        
    }
    
    public  void clientProfile() {
        try{
            Credential cred = new Credential("AKIDUlmW0D9oEMDn69vRgfNoytXu0pOghRv8", "yf6G9k48vRgfNoysVXIm0xc08zZsvhCQ");

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("iai.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            IaiClient client = new IaiClient(cred, "ap-guangzhou", clientProfile);

            DetectFaceAttributesRequest req = new DetectFaceAttributesRequest();
            req.setMaxFaceNum(1L);
            req.setUrl("HTTP");

            DetectFaceAttributesResponse resp = client.DetectFaceAttributes(req);

            System.out.println(DetectFaceAttributesResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
                System.out.println(e.toString());
        }

    }

    
    
    /**
     *  json类型参数 post请求
     * @param url
     * @param headers 请求头信息
     * @param json
     * @return  java.lang.String
     * <AUTHOR>
     * @date 2020/5/20 17:52
     **/       
    public static String doPostByJson(String url,Map<String,String> headers, String json) {
        // 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try{
            //创建httpPost请求
            HttpPost httpPost = new HttpPost(url);
            //给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(json,"utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            // 添加httpHeaders
            if (headers != null && headers.size() > 0) {
                for (String key : headers.keySet()) {
                    httpPost.addHeader(key, headers.get(key));
                }
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 获取响应消息
            resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
        }
        catch(Exception e)
        {
            e.printStackTrace();
        }
        finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return resultString;
    }

    public static String sign(List<String> values, String ticket) {
        if (values == null) {
            throw new NullPointerException("values is null");
        }
        values.removeAll(Collections.singleton(null));// remove null
        values.add(ticket);
        java.util.Collections.sort(values);
         StringBuilder sb = new StringBuilder();
        for (String s : values) {
            sb.append(s);
        }
        System.out.println(sb.toString());
        return Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
     }
    
    private static String generateBizId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    
    private void aaaa() {

        try{
            Credential cred = new Credential("AKIDAD2cy5yc2DAsKWTAblfKkvfEYdT3OTIr", "MSIrBG4cuwkJdiZAWdV8WfYCbmiOp1yi");

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("iai.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            IaiClient client = new IaiClient(cred, "ap-guangzhou", clientProfile);

            CompareFaceRequest req = new CompareFaceRequest();
            req.setUrlA("https://avatar01.jiaoliuqu.com/taqu_android_avatar_114_1623056201056_1_0_9359.JPEG");
            req.setUrlB("https://avatar01.jiaoliuqu.com/aliyunFaceDetect/1623056445169_3cbc0bac-c55a-40af-9e86-95d32e310cac.jpg");
            CompareFaceResponse resp = client.CompareFace(req);
            Float score = resp.getScore();

            System.out.println(CompareFaceResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            e.getErrorCode();
            
            System.out.println(e.toString());
        }
    }
}
