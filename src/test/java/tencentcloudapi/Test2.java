package tencentcloudapi;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import org.apache.commons.collections.MapUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.GetFaceIdTokenRequest;
import com.tencentcloudapi.faceid.v20180301.models.GetFaceIdTokenResponse;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceAttributesRequest;
import com.tencentcloudapi.iai.v20200303.models.DetectFaceAttributesResponse;

import cn.taqu.account.client.TengxunyunApiClient;
import cn.taqu.account.client.api.tengxunyun.TengxunyunApi;
import cn.taqu.account.client.api.tengxunyun.TengxunyunWBappid;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;

/**
 * 腾讯云测试
 * 
 * <AUTHOR>
 * @date 2021/05/27
 */
public class Test2 {

//    private final static TengxunyunWBappid PEIPEI_ONLINE_BAK = new TengxunyunWBappid("TIDATHgW","KxE3Z2xtMWIcin6t4FrpUAHhmumRmJ6mXmOleQuj7cRk7oauRsEVJC57svYJyiey");
// 配配
//    IDAeG64h","wFsKgEUbfVwGMq7g5jRzwPuPAZTyTGABpNBakFbXyC0rgmLffeIsHmEXsVKWkX8o"
    //测试
//    "IDAuMtBF","aDTjZkWFXfr013jyn0jicjoeXetRwxcRR9euYq1Hjy7dz0UQi0DRk6Sc2ZPQK5Dk"
    // 业务流程唯一标识，即 wbappid，可参考 获取 WBappid 指引在人脸核身控制台内申请
    private static String appid = "IDAeG64h";
    // wbappid 对应的密钥，申请 wbappid 时得到，可参考 获取 WBappid 指引在人脸核身控制台内申请
    private static String secret = "wFsKgEUbfVwGMq7g5jRzwPuPAZTyTGABpNBakFbXyC0rgmLffeIsHmEXsVKWkX8o";
    // 业务流程唯一标识，即 wbappid，可参考 获取 WBappid 指引在人脸核身控制台内申请
    private static String appid2 = "IDAeG64h";
    // wbappid 对应的密钥，申请 wbappid 时得到，可参考 获取 WBappid 指引在人脸核身控制台内申请
    private static String secret2 = "wFsKgEUbfVwGMq7g5jRzwPuPAZTyTGABpNBakFbXyC0rgmLffeIsHmEXsVKWkX8o";
    // 授权类型，默认值为：client_credential（必须小写）
    private static String grantType = "client_credential";
    // 版本号，默认值为：1.0.0
    private static String version = "1.0.0";

    private static TengxunyunApi tengxunyunApi;

//    private static String accessToken; 
    private static String accessToken = "";
    //
    private static String apiTicketNonce;
    // QOXn2AOKt16q7TubCxIBn52Mdo698MBIil0eIih3665vMO9HcX4uD6ZaRUVRTRCM
    private static String apiTicketSign = "";
    //
    private static String faceid;
    //
    private static String nonce;
    //
    private static String sign;
    //
    private static String nonce2;
    //
    private static String sign2;
    //
    private static String serverSync;
    //
    private static String serverGetLiveResult;
    //
    private static String userid = "bfqyp2l343rf";
    //
    private static String orderNo = "9e4f7c681a08431aae9ee415724e9c39";

    public static void main(String[] args) {
        Test2 test1 = new Test2();
        init();
        
        test1.getBase(test1);
        // userid,noce,sign,orderno
//        System.out.println("userid = " + userid);
//        System.out.println("nonce = " + nonce);
//        System.out.println("sign = " + sign);
//        System.out.println("orderno = " + orderNo);
//        System.out.println("faceid = " + faceid);
        
//        test1.getCheck1(test1);
        test1.getCheck2(test1);
//        
//        test1.clientProfile();
    }
    
    private void getBase(Test2 test1) {
        
        test1.getAccessToken();
        System.out.println("****************** accessToken=" + accessToken);
        test1.getApiTicketNonce();
        System.out.println("****************** apiTicketNonce=" + apiTicketNonce);
        test1.getApiTicketSign();
        System.out.println("****************** apiTicketNonce=" + apiTicketNonce);
        
        nonce = generateBizId();
//        orderNo = generateBizId();
        List<String> values = Lists.newArrayList();
        values.add("1.0.0");
        values.add(appid);
//        values.add(apiTicketNonce);
        values.add(nonce);
        values.add(userid);
        sign = sign(values, apiTicketSign);
        
        test1.getfaceid();
        System.out.println("****************** faceid=" + faceid);
        
    }
    
    private void getCheck1(Test2 test1) {
        test1.getAccessToken();
        System.out.println("****************** accessToken=" + accessToken);
        test1.getApiTicketSign();
        System.out.println("****************** apiTicketSign=" + apiTicketSign);
        test1.serverSync();
        System.out.println("****************** serverSync=" + serverSync);
        
    }
    
    private void getCheck2(Test2 test1) {
        test1.getAccessToken();
        System.out.println("****************** accessToken=" + accessToken);
        test1.getApiTicketSign();
        System.out.println("****************** apiTicketSign=" + apiTicketSign);
        test1.serverGetLiveResult();
        System.out.println("****************** serverGetLiveResult=" + serverGetLiveResult);
        
    }
    

    public static void init() {
        TengxunyunApiClient tengxunyunApiClient = new TengxunyunApiClient("https://idasc.webank.com");
        tengxunyunApi = tengxunyunApiClient.getTengxunyunApi();
    }

    public void m1() {
        try {

            // Credential cred = new Credential("SecretId", "SecretKey");
            Credential cred =
                new Credential("AKIDUlmW0D9oEMDn69vRgfNoytXu0pOghRv8", "yf6G9k48vRgfNoysVXIm0xc08zZsvhCQ");

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("faceid.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            FaceidClient client = new FaceidClient(cred, "", clientProfile);

            GetFaceIdTokenRequest req = new GetFaceIdTokenRequest();

            GetFaceIdTokenResponse resp = client.GetFaceIdToken(req);

            System.out.println(GetFaceIdTokenResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
    }

    public void getAccessToken() {
        String oauth2AccessToken = tengxunyunApi.oauth2AccessToken(appid, secret);
        System.out.println(oauth2AccessToken);
        Map<String, Object> map =
            JsonUtils.stringToObject(oauth2AccessToken, new TypeReference<Map<String, Object>>() {});
        accessToken = MapUtils.getString(map, "access_token");
    }

    public void getApiTicketNonce() {
        String oauth2ApiTicket = tengxunyunApi.oauth2ApiTicketNonce(appid, accessToken, userid);
        System.out.println(oauth2ApiTicket);
        Map<String, Object> map =
            JsonUtils.stringToObject(oauth2ApiTicket, new TypeReference<Map<String, Object>>() {});
        String code = MapUtils.getString(map, "code");
        if(Objects.equals(code, "0")) {
            List<Map<String, Object>> object = (List<Map<String, Object>>)MapUtils.getObject(map, "tickets");
            apiTicketNonce = (String)object.get(0).get("value");
        }else {
            throw new ServiceException("oauth2ApiTicket", "获取腾讯云oauth2ApiTicket失败");
        }
    }
    
    public void getApiTicketSign() {
        String oauth2ApiTicket = tengxunyunApi.oauth2ApiTicketSign(appid, accessToken);
        System.out.println(oauth2ApiTicket);
        Map<String, Object> map =
            JsonUtils.stringToObject(oauth2ApiTicket, new TypeReference<Map<String, Object>>() {});
        List<Map<String, Object>> object = (List<Map<String, Object>>)MapUtils.getObject(map, "tickets");
        apiTicketSign = (String)object.get(0).get("value");
    }
    
    public void serverSync() {
        nonce2 = generateBizId();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderNo);
        values.add("1.0.0");
//        values.add(apiTicketSign);
        values.add(nonce2);
        sign2 = sign(values, apiTicketSign );
        
        serverSync = tengxunyunApi.serverSync(appid, "1.0.0", nonce2, orderNo, sign2, "2");
        System.out.println(serverSync);
        Map<String, Object> map =
            JsonUtils.stringToObject(serverSync, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> object = (Map<String, Object>)MapUtils.getObject(map, "result");
        String bsae64 = (String)object.get("photo");
        System.out.println(bsae64);
    }
    
    public void serverGetLiveResult() {
        nonce2 = generateBizId();
        List<String> values = Lists.newArrayList();
        values.add(appid);
        values.add(orderNo);
        values.add("1.0.0");
//        values.add(apiTicketSign);
        values.add(nonce2);
        sign2 = sign(values, apiTicketSign);
        
        serverGetLiveResult = tengxunyunApi.serverGetLiveResult(appid, "1.0.0", nonce2, orderNo, sign2);
        Map<String, Object> map =
            JsonUtils.stringToObject(serverGetLiveResult, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> object = (Map<String, Object>)MapUtils.getObject(map, "result");
        String bsae64 = (String)object.get("photo");
        System.out.println(bsae64);
    }
    
    public void getfaceid() {
        
        
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("webankAppId", appid);
        params.put("orderNo", orderNo);
//        params.put("name", "许根水");
//        params.put("idNo", "362331199102083018");
        params.put("name", "陈伟昌");
        params.put("idNo", "35012519921005241X");
        params.put("userId", userid);
        params.put("sourcePhotoStr", "");
        params.put("sourcePhotoType", 2);
        params.put("version", version);
        params.put("sign", sign);
        params.put("nonce", nonce);
        
//        faceid = tengxunyunApi.serverGetfaceid(appid, generateBizId(), "邱宇骅", "350721199012101337", userid, 2, "1.0.0", sign );
        faceid = doPostByJson("https://idasc.webank.com/api/server/getfaceid?orderNo="+orderNo, null, JsonUtils.objectToString(params));
        System.out.println(faceid);
        Map<String, Object> map =
            JsonUtils.stringToObject(faceid, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> object = (Map<String, Object>)MapUtils.getObject(map, "result");
        faceid = (String)object.get("faceId");
        
        
        
        //        String accessToken = client.getAccessToken();//http 请求
//        String sign = SignUtils.sign(data, ticket);

    }
    
    public  void clientProfile() {
        try{
            Credential cred = new Credential("AKIDUlmW0D9oEMDn69vRgfNoytXu0pOghRv8", "yf6G9k48vRgfNoysVXIm0xc08zZsvhCQ");

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("iai.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            IaiClient client = new IaiClient(cred, "ap-guangzhou", clientProfile);

            DetectFaceAttributesRequest req = new DetectFaceAttributesRequest();
            req.setMaxFaceNum(1L);
            req.setUrl("HTTP");

            DetectFaceAttributesResponse resp = client.DetectFaceAttributes(req);

            System.out.println(DetectFaceAttributesResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
                System.out.println(e.toString());
        }

    }

    
    
    /**
     *  json类型参数 post请求
     * @param url
     * @param headers 请求头信息
     * @param json
     * @return  java.lang.String
     * <AUTHOR>
     * @date 2020/5/20 17:52
     **/       
    public static String doPostByJson(String url,Map<String,String> headers, String json) {
        // 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try{
            //创建httpPost请求
            HttpPost httpPost = new HttpPost(url);
            //给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(json,"utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            // 添加httpHeaders
            if (headers != null && headers.size() > 0) {
                for (String key : headers.keySet()) {
                    httpPost.addHeader(key, headers.get(key));
                }
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 获取响应消息
            resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
        }
        catch(Exception e)
        {
            e.printStackTrace();
        }
        finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return resultString;
    }

    public static String sign(List<String> values, String ticket) {
        if (values == null) {
            throw new NullPointerException("values is null");
        }
        values.removeAll(Collections.singleton(null));// remove null
        values.add(ticket);
        java.util.Collections.sort(values);
         StringBuilder sb = new StringBuilder();
        for (String s : values) {
            sb.append(s);
        }
        System.out.println(sb.toString());
        return Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
     }
    
    private static String generateBizId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
}
