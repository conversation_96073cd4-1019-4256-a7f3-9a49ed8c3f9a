package getui;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import com.google.common.collect.Maps;

import cn.taqu.core.utils.JsonUtils;
import okhttp3.ConnectionPool;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class AuthSignTest {

	public static String APPKEY = "Cl6ziuHLO1A4AUuyKmeVAA";
	public static String MASTERSECRET = "YZTnBNoemb8GICMLG1DsL7";
	public static String APPID = "uKosK7Zxbq98Za3CQaa9N2";
	
//        "appId": "uKosK7Zxbq98Za3CQaa9N2",
//        "appSecret": "TtQzZn7SuQ9H9CPFcYZDJ7",
//        "appKey": "Cl6ziuHLO1A4AUuyKmeVAA",
//        "masterSecret": "YZTnBNoemb8GICMLG1DsL7"
	
//	appId	String	必选	业务在个验申请的应用id
//	timestamp	Long	必选	时间戳
//	sign	String	必选	接口签名，生成规则：SHA256(appKey + timestamp + masterSecret)
//	token	String	必选	调用SDK上传token返回的凭证
//	gyuid	String	必选	调用
	
    public static String url = "https://openapi-gy.getui.com/v1/gy/ct_login/gy_get_pn";

    
    private static final OkHttpClient CLIENT;

    static {
        CLIENT = new OkHttpClient.Builder()
                .connectTimeout(3, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .writeTimeout(3, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(50, 60, TimeUnit.SECONDS))
                .build();
    }
    
    
    public static void main(String[] args) throws Exception {
    	
    	AuthSignTest authSignTest = new AuthSignTest();
    	
//    	authSignTest.a();
//    	authSignTest.b();
    	authSignTest.c();
    }
    
    public void a() throws Exception {

    	
        URL urlObj = new URL(url);
        URLConnection con = urlObj.openConnection();
        HttpURLConnection httpURLConnection = (HttpURLConnection) con;
        // http 头部
        httpURLConnection.setRequestMethod("POST");
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        Long timestamp = System.currentTimeMillis();

        // 在个像开发者中心获取的 app 对应的 key，可自行替换
        String appkey = APPKEY;
        String masterSecret = MASTERSECRET;
        // sha256 加密，使用 org.apache.commons 包中自带的加密方法，需将加密后数据一 起上传
        String sign = DigestUtils.sha256Hex(String.format("%s%d%s", appkey, timestamp, masterSecret));

        
//        {
//            "appId": "LLNstWgyGm8UM2SsherlU5",
//            "timestamp": 1529391652123,
//            "gyuid":"12313ssad",
//            "sign": "8f8ef776b7364c96fc1a7e734976e32b6a5e297a1ddbaea31bf3deab517d1"
//        }
        
        JSONObject requestDataObject = new JSONObject();
        requestDataObject.put("appId", APPID);
        requestDataObject.put("timestamp", timestamp);
        requestDataObject.put("sign", sign);
        requestDataObject.put("token", "xWuUi5Gt34AWWGtkqXyxE5");
        requestDataObject.put("gyuid", "4685189a8954486a97e4852c2b103b3434");
//        requestDataObject.put("appId", APPID);
//        requestDataObject.put("timestamp", "1529391652123");
//        requestDataObject.put("sign", "8f8ef776b7364c96fc1a7e734976e32b6a5e297a1ddbaea31bf3deab517d1");
//        requestDataObject.put("token", "1529391652123");
//        requestDataObject.put("gyuid", "12313ssad");
        // 建立连接，将数据写入内存
        DataOutputStream out = new DataOutputStream(httpURLConnection.getOutputStream());
        out.writeBytes(requestDataObject.toString());
        out.flush();
        out.close();

        BufferedReader in = null;
        String result = "";
        // 将数据发送给服务端，并获取返回结果
        in = new BufferedReader(new InputStreamReader(httpURLConnection.getInputStream()));
        String line;
        while ((line = in.readLine()) != null) {
            result += line;
        }
        System.out.println(result);
    }
    
    public void b() throws IOException {
        Long timestamp = System.currentTimeMillis();

        // 在个像开发者中心获取的 app 对应的 key，可自行替换
        String appkey = APPKEY;
        String masterSecret = MASTERSECRET;
        // sha256 加密，使用 org.apache.commons 包中自带的加密方法，需将加密后数据一 起上传
        String sign = DigestUtils.sha256Hex(String.format("%s%d%s", appkey, timestamp, masterSecret));

        
        RequestBody body = new FormBody.Builder()
                .add("appId", APPID)
                .add("timestamp", timestamp.toString())
                .add("sign", sign)
                .add("token", "xWuUi5Gt34AWWGtkqXyxE5")
                .add("gyuid", "4685189a8954486a97e4852c2b103b3434")
                .build();
        Request request = new Request.Builder()
                .post(body)
                .addHeader("Content-Type","application/json;charset=UTF-8")
                .url(url)
                .build();

        Response execute = CLIENT.newCall(request).execute();
        System.out.println(JsonUtils.objectToString(execute));
    }
    
    public void c() {
    		// 获得Http客户端(可以理解为:你得先有一个浏览器;注意:实际上HttpClient与浏览器是不一样的)
    		CloseableHttpClient httpClient = HttpClientBuilder.create().build();
//            Long timestamp = System.currentTimeMillis();
            Long timestamp = 1563243624124L;

            // 在个像开发者中心获取的 app 对应的 key，可自行替换
            String appkey = APPKEY;
            String masterSecret = MASTERSECRET;
            // sha256 加密，使用 org.apache.commons 包中自带的加密方法，需将加密后数据一 起上传
            String sign = DigestUtils.sha256Hex(String.format("%s%d%s", appkey, timestamp, masterSecret));
    		
    		// 创建Post请求
    		HttpPost httpPost = new HttpPost(url);
    		// 我这里利用阿里的fastjson，将Object转换为json字符串;
    		// (需要导入com.alibaba.fastjson.JSON包)
    		String jsonString = "{\n" + 
    				"    \"appId\": \""+APPID+"\",\n" + 
    				"    \"timestamp\": "+timestamp+",\n" + 
    				"    \"gyuid\":\"4685189a8954486a97e4852c2b103b3434\",\n" + 
    				"    \"token\":\"xWuUi5Gt34AWWGtkqXyxE5\",\n" + 
    				"    \"sign\": \""+sign+"\"\n" + 
    				"}";
    		HashMap<String,String> newHashMap = Maps.newHashMap();
    		newHashMap.put("appId", APPID);
    		newHashMap.put("timestamp", timestamp.toString());
    		newHashMap.put("gyuid", "4685189a8954486a97e4852c2b103b3434");
    		newHashMap.put("token", "xWuUi5Gt34AWWGtkqXyxE5");
    		newHashMap.put("sign", sign);
    		
    		StringEntity entity = new StringEntity(JsonUtils.objectToString(newHashMap), "UTF-8");
     
    		// post请求是将参数放在请求体里面传过去的;这里将entity放入post请求体中
    		httpPost.setEntity(entity);
     
    		httpPost.setHeader("Content-Type", "application/json;charset=utf8");
     
    		// 响应模型
    		CloseableHttpResponse response = null;
    		try {
    			// 由客户端执行(发送)Post请求
    			response = httpClient.execute(httpPost);
    			// 从响应模型中获取响应实体
    			HttpEntity responseEntity = response.getEntity();
     
    			System.out.println("响应状态为:" + response.getStatusLine());
    			if (responseEntity != null) {
    				System.out.println("响应内容长度为:" + responseEntity.getContentLength());
    				System.out.println("响应内容为:" + EntityUtils.toString(responseEntity));
    			}
    		} catch (ClientProtocolException e) {
    			e.printStackTrace();
    		} catch (ParseException e) {
    			e.printStackTrace();
    		} catch (IOException e) {
    			e.printStackTrace();
    		} finally {
    			try {
    				// 释放资源
    				if (httpClient != null) {
    					httpClient.close();
    				}
    				if (response != null) {
    					response.close();
    				}
    			} catch (IOException e) {
    				e.printStackTrace();
    			}
    		}
    }
}
